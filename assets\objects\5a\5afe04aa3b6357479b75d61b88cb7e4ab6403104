{"accessibility.onboarding.accessibility.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "accessibility.onboarding.screen.narrator": "Pres entar two enable naratorr", "accessibility.onboarding.screen.title": "Welcom 2 Minecraft\n\nWud u b c00l if narration kitteh talk or <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>??", "addServer.add": "Dun", "addServer.enterIp": "Servur Addrezz", "addServer.enterName": "<PERSON><PERSON><PERSON>", "addServer.resourcePack": "SERVR RESOURCE PACKZ", "addServer.resourcePack.disabled": "Turnd off", "addServer.resourcePack.enabled": "Turnd on", "addServer.resourcePack.prompt": "<PERSON>rumpt", "addServer.title": "Edetit Servur Info", "advMode.command": "console comarned", "advMode.mode": "<PERSON><PERSON>", "advMode.mode.auto": "Repet", "advMode.mode.autoexec.bat": "Allwayz on", "advMode.mode.conditional": "Condizional", "advMode.mode.redstone": "Impulz", "advMode.mode.redstoneTriggered": "<PERSON><PERSON> Redstone", "advMode.mode.sequence": "clingy ropes", "advMode.mode.unconditional": "Uncondizional", "advMode.notAllowed": "Must b an oppd pleyr in craetiv moed", "advMode.notEnabled": "comarned blocz are not enabeld on thiz servur", "advMode.previousOutput": "preevyous owtpoot", "advMode.setCommand": "Set consol cmd 4 bluk", "advMode.setCommand.success": "comarnd set: %s", "advMode.trackOutput": "<PERSON>rac otpoot", "advMode.triggering": "<PERSON><PERSON><PERSON>", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Unknewn advancement: %s", "advancements.adventure.adventuring_time.description": "Disccover evry baium", "advancements.adventure.adventuring_time.title": "Catvenshuring Tiem", "advancements.adventure.arbalistic.description": "kill 5 diffrz mobs wit 1 crosbowz shot", "advancements.adventure.arbalistic.title": "Arbalasticc", "advancements.adventure.avoid_vibration.description": "<PERSON>rouch walk near a Sculk detektor, Sculk Yeller or Blu shrek to igner it from detektin u", "advancements.adventure.avoid_vibration.title": "crouch walk 100,000,000", "advancements.adventure.blowback.description": "Swipe at teh Wind guy'z atacc 2 kill teh <PERSON> guy", "advancements.adventure.blowback.title": "Blowign da fandum away", "advancements.adventure.brush_armadillo.description": "Get hurd stuv frum a hard bawl usin' a bruum", "advancements.adventure.brush_armadillo.title": "Hard and adorabawl", "advancements.adventure.bullseye.description": "Scritch da eye frum faaar awai", "advancements.adventure.bullseye.title": "360 no scope", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Mae<PERSON> a Ancient Containr out ov 4 Ancient Containr <PERSON>z", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Go 2 a Cwaft Mashin wen it maekz a klon ov itselv", "advancements.adventure.crafters_crafting_crafters.title": "Cwafty Cwafter Cwaftashunception", "advancements.adventure.fall_from_world_height.description": "Fall rlly far (and survives)", "advancements.adventure.fall_from_world_height.title": "Cavez n' Clifz", "advancements.adventure.heart_transplanter.description": "Put a silly glowin lawg betwiin 2 pale ok lawg blck so dat it glowz", "advancements.adventure.heart_transplanter.title": "5-<PERSON><PERSON>", "advancements.adventure.hero_of_the_village.description": "Stawp teh rade in da villag very good", "advancements.adventure.hero_of_the_village.title": "Hero of the Cats", "advancements.adventure.honey_block_slide.description": "<PERSON> slidez on Sticky thing to B saved", "advancements.adventure.honey_block_slide.title": "Sticky Cat", "advancements.adventure.kill_a_mob.description": "Fite a bad kitteh", "advancements.adventure.kill_a_mob.title": "Bad Kit<PERSON>h deadmakr", "advancements.adventure.kill_all_mobs.description": "Scratch wun uv each bad catz", "advancements.adventure.kill_all_mobs.title": "Bad Kettehs made ded", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Meak a mob ded near Sculk Cat-alist :(", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Teh Weird Thing Spredz", "advancements.adventure.lighten_up.description": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>r Bob wif a Akz 2 maek it braaiter", "advancements.adventure.lighten_up.title": "Scrachign post", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Sav big-nos man frum lightening, but dun't start a fir", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "BOOM protecc", "advancements.adventure.minecraft_trials_edition.description": "Go 2 one uv tohse beeg <PERSON>", "advancements.adventure.minecraft_trials_edition.title": "Da new mc dungenz updet⁈ :o", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON>", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Deel 50 harts uv dmg at onc wit a sledjhamer", "advancements.adventure.overoverkill.title": "Scarlet Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Ma<PERSON> the Medows come aliv with the sond fo msic form a Jokebox", "advancements.adventure.play_jukebox_in_meadows.title": "CATZ of musik", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Let teh Redston Stuf fel the POWAH OF NERDY STUFZ!", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "KNOLEGGE POWAH!", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON><PERSON> a skery moneh bokz wit a spoo-key", "advancements.adventure.revaulting.title": "I get moneh im a star", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON>, explor an fite", "advancements.adventure.root.title": "Catventure", "advancements.adventure.salvage_sherd.description": "<PERSON><PERSON><PERSON> a Sussy blok to get a Containr <PERSON><PERSON>", "advancements.adventure.salvage_sherd.title": "Pres F to pey respectz", "advancements.adventure.shoot_arrow.description": "Pew-pew sumfin wid a bouw and Arrou", "advancements.adventure.shoot_arrow.title": "Hedshot som1", "advancements.adventure.sleep_in_bed.description": "taek a Nap 2 chengz ur kat home", "advancements.adventure.sleep_in_bed.title": "Cat dreams", "advancements.adventure.sniper_duel.description": "ez a boniboi frum at lest 50 metrs ewey", "advancements.adventure.sniper_duel.title": "mLgY0l0 dewwel!!!", "advancements.adventure.spyglass_at_dragon.description": "Lok at teh Dwagon Bos throo Spyglaz", "advancements.adventure.spyglass_at_dragon.title": "IS THAT A SUPRA ?!!!!!", "advancements.adventure.spyglass_at_ghast.description": "Lok at Ghast throo 1 Spyglaz", "advancements.adventure.spyglass_at_ghast.title": "R it <PERSON><PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON> at teh berd throo a Spyglaz", "advancements.adventure.spyglass_at_parrot.title": "R it bird?", "advancements.adventure.summon_iron_golem.description": "Spawn 1 strange irun hooman 2 defend ordinary hoomanz in hooman town", "advancements.adventure.summon_iron_golem.title": "Haird HALP", "advancements.adventure.throw_trident.description": "Frow a Dinglehopper at sumthin.\nNot: Frowin away katz only wepun is no gud.", "advancements.adventure.throw_trident.title": "EYE thruwei pun", "advancements.adventure.totem_of_undying.description": "Uze a Toitem of Undining to cheetos deth", "advancements.adventure.totem_of_undying.title": "After dieded", "advancements.adventure.trade.description": "Successfullee traid wif villagr", "advancements.adventure.trade.title": "Wat a deel!", "advancements.adventure.trade_at_world_height.description": "Trade wit Villaguur many blukz high", "advancements.adventure.trade_at_world_height.title": "Rlly good tradr", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON> dese smissin thingyz into armar at lest once: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> <PERSON>, *shhh*, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-wae", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "I smiss ur wae", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON><PERSON><PERSON> gud-lookin suit at a <PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.title": "Got da drip", "advancements.adventure.two_birds_one_arrow.description": "Makez 2 Creepy Flyin Tings ded wit a piering A<PERSON>rou", "advancements.adventure.two_birds_one_arrow.title": "2 Birbz, 1 Errow", "advancements.adventure.under_lock_and_key.description": "Use a Dungen Kee on a Moneh bokz", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON>, I liek money", "advancements.adventure.use_lodestone.description": "Hit magned wit da other magned", "advancements.adventure.use_lodestone.title": "cawntry lowd, taek me hooome to da plazz i belllonggggg :)", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON><PERSON><PERSON> a <PERSON> wiz lijthing!", "advancements.adventure.very_very_frightening.title": "beri beri frijthing!", "advancements.adventure.voluntary_exile.description": "Kill rade captain\nStay away frum villadges 4 nao...", "advancements.adventure.voluntary_exile.title": "He <PERSON>, Boi", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Walk on Weird Snow...without fell in it (oh no)", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lite as a kitteh", "advancements.adventure.who_needs_rockets.description": "Jumpp up in teh air 8 blocz wiht a Wind bawl", "advancements.adventure.who_needs_rockets.title": "<PERSON><PERSON><PERSON> kant fly?", "advancements.adventure.whos_the_pillager_now.description": "Give a <PERSON><PERSON>ur a test of itz oon medicin", "advancements.adventure.whos_the_pillager_now.title": "I am da <PERSON><PERSON><PERSON>. Not <PERSON>!", "advancements.empty": "Dere duznt sem 2 bee anythin hier...", "advancements.end.dragon_breath.description": "Colect dragonz breth in a glaz bottl", "advancements.end.dragon_breath.title": "Throw in a Minetos", "advancements.end.dragon_egg.description": "Hold <PERSON><PERSON><PERSON><PERSON>' unhatched baby", "advancements.end.dragon_egg.title": "<PERSON> next generashion", "advancements.end.elytra.description": "I BELIEVE I CAN FLYYYYYYYY", "advancements.end.elytra.title": "Sky iz teh limit", "advancements.end.enter_end_gateway.description": "ESC the I-land", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "Go in, wat cud possibly go wong?", "advancements.end.find_end_city.title": "Da city @ end", "advancements.end.kill_dragon.description": "Git gud", "advancements.end.kill_dragon.title": "Free teh End", "advancements.end.levitate.description": "<PERSON><PERSON> up 50 bloks laik suprkitty aftr getin hit by a shulker", "advancements.end.levitate.title": "Supr dupr view up heer", "advancements.end.respawn_dragon.description": "rspwn skary dragyboi", "advancements.end.respawn_dragon.title": "<PERSON><PERSON>... Agaen...", "advancements.end.root.description": "Or da start?", "advancements.end.root.title": "<PERSON>h <PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Hav blu boi drop a cak aat a nout bluk", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Happi Birfday!! (づ^･ω･^)づ", "advancements.husbandry.allay_deliver_item_to_player.description": "Hav blu boi delievvr items 2 u", "advancements.husbandry.allay_deliver_item_to_player.title": "therz a kat in mi <:D", "advancements.husbandry.axolotl_in_a_bucket.description": "KATCH A KUTE FISH IN A BUKKIT", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON> caught da KUTE FISH", "advancements.husbandry.balanced_diet.description": "EAT ALL THE THINGZ", "advancements.husbandry.balanced_diet.title": "ful uf fuuud", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON><PERSON> oll da animalz!", "advancements.husbandry.breed_all_animals.title": "2 by 2 iz 4?", "advancements.husbandry.breed_an_animal.description": "Breeed 2 animalz 2gether", "advancements.husbandry.breed_an_animal.title": "Da Parruts + da Batz", "advancements.husbandry.complete_catalogue.description": "Become cat lord!", "advancements.husbandry.complete_catalogue.title": "A Complet Cat-A-Log", "advancements.husbandry.feed_snifflet.description": "<PERSON> a Snifflet", "advancements.husbandry.feed_snifflet.title": "Kute Sniftehz!!", "advancements.husbandry.fishy_business.description": "Cat a fishy", "advancements.husbandry.fishy_business.title": "fishy bashiness", "advancements.husbandry.froglights.description": "get em all Toadshines in ya inventari", "advancements.husbandry.froglights.title": "2gether we meoww!", "advancements.husbandry.kill_axolotl_target.description": "Teem up wif a suger fwish an win fite", "advancements.husbandry.kill_axolotl_target.title": "<PERSON><PERSON> for Frendship", "advancements.husbandry.leash_all_frog_variants.description": "Git evvy Toad on a Leesh", "advancements.husbandry.leash_all_frog_variants.title": "Toad geng", "advancements.husbandry.make_a_sign_glow.description": "Use da magik shineeh skwid powars too maek sign shiny :O", "advancements.husbandry.make_a_sign_glow.title": "Low n' bee-hooold!", "advancements.husbandry.netherite_hoe.description": "use an Netherite ingut to upgrat a hoe... R U SERIOZ????", "advancements.husbandry.netherite_hoe.title": "r u seriouz y u do dis", "advancements.husbandry.obtain_sniffer_egg.description": "Get a Sniffr Ec", "advancements.husbandry.obtain_sniffer_egg.title": "Gud smell", "advancements.husbandry.place_dried_ghast_in_water.description": "Playz a <PERSON><PERSON> blck into wota", "advancements.husbandry.place_dried_ghast_in_water.title": "Sokeing it awl in", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant any Sniffr sed", "advancements.husbandry.plant_any_sniffer_seed.title": "Once upon teh taim...", "advancements.husbandry.plant_seed.description": "Plant 1 seed and w8t 4 it 2 grouw", "advancements.husbandry.plant_seed.title": "SEEDZ!", "advancements.husbandry.remove_wolf_armor.description": "Cut woof armur frum a woof usin' da Skizzors", "advancements.husbandry.remove_wolf_armor.title": "Cut dat off", "advancements.husbandry.repair_wolf_armor.description": "<PERSON><PERSON> fikz a dmged woof armur usin' hurd bawl stuhv", "advancements.husbandry.repair_wolf_armor.title": "Bettr armr bettr woof", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Get in bowt an flowt wif Gaot", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Watevr floatz ur gaot!", "advancements.husbandry.root.description": "Diz wurld iz full of catz an eetin stuffz", "advancements.husbandry.root.title": "Huzbandry", "advancements.husbandry.safely_harvest_honey.description": "Have <PERSON>er to get <PERSON><PERSON>r Sweetz from Beez House wit a Glaz Bottel so dey dnt bite u", "advancements.husbandry.safely_harvest_honey.title": "B our cat", "advancements.husbandry.silk_touch_nest.description": "Smooth dig 3 <PERSON><PERSON> in ther nest or hive 2 do a B-lokation", "advancements.husbandry.silk_touch_nest.title": "totl B-lokatiuon", "advancements.husbandry.tactical_fishing.description": "Cat a Fishy... wizhout a <PERSON>ik!", "advancements.husbandry.tactical_fishing.title": "Kattical fihing", "advancements.husbandry.tadpole_in_a_bucket.description": "Katch a kute smol toad in a bukkit", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit²", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON> a friendooo", "advancements.husbandry.tame_an_animal.title": "BEZT CATZ EVAARR", "advancements.husbandry.wax_off.description": "Sc<PERSON>e Waks off for Coppr Blok!", "advancements.husbandry.wax_off.title": "Waks off", "advancements.husbandry.wax_on.description": "Apple Honeycomb 2 Coppr Blok", "advancements.husbandry.wax_on.title": "Waks on", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON> woof lord!!", "advancements.husbandry.whole_pack.title": "dawg u serious?", "advancements.nether.all_effects.description": "Hav evri effelt applied at teh saem tiem", "advancements.nether.all_effects.title": "Haw did we get 'ere¿", "advancements.nether.all_potions.description": "Hav evri potion effect applied at teh same tiem", "advancements.nether.all_potions.title": "Strange tastin' milk", "advancements.nether.brew_potion.description": "<PERSON>rw poshun", "advancements.nether.brew_potion.title": "You're a lizard, <PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "FulL Powahhh", "advancements.nether.charge_respawn_anchor.title": "fake LOLCAT!", "advancements.nether.create_beacon.description": "mak and plais bacon", "advancements.nether.create_beacon.title": "Bring hoem da bacon", "advancements.nether.create_full_beacon.description": "Charg bacon 2 100%!!!1!", "advancements.nether.create_full_beacon.title": "Baconator 2000", "advancements.nether.distract_piglin.description": "<PERSON>ib <PERSON>y tu da <PERSON>", "advancements.nether.distract_piglin.title": "Shinyyyy", "advancements.nether.explore_nether.description": "Si al Nether bioms >:3", "advancements.nether.explore_nether.title": "Veri hot vacashun", "advancements.nether.fast_travel.description": "Uz da Nether 2 travl 7 km in slipycatwurld", "advancements.nether.fast_travel.title": "Hiway thru hell", "advancements.nether.find_bastion.description": "Go in Spoooky Place", "advancements.nether.find_bastion.title": "Loong LoOong Agoo", "advancements.nether.find_fortress.description": "<PERSON> B I MEEN intu a Nether Fortress", "advancements.nether.find_fortress.title": "Terribl Fortrez", "advancements.nether.get_wither_skull.description": "get <PERSON><PERSON> skelet's skul", "advancements.nether.get_wither_skull.title": "Spoopy <PERSON>e-ton", "advancements.nether.loot_bastion.description": "Steal frum an Anshien Cat Box", "advancements.nether.loot_bastion.title": "War oinks", "advancements.nether.netherite_armor.description": "get in a 4 sided Netherite box", "advancements.nether.netherite_armor.title": "Covr meow in trash", "advancements.nether.obtain_ancient_debris.description": "Ged de old trash", "advancements.nether.obtain_ancient_debris.title": "Hidin under da couch", "advancements.nether.obtain_blaze_rod.description": "Releef 1 blaze of its tail", "advancements.nether.obtain_blaze_rod.title": "In2 fire", "advancements.nether.obtain_crying_obsidian.description": "obtain depressed hard blok", "advancements.nether.obtain_crying_obsidian.title": "The sadest blog evah :(", "advancements.nether.return_to_sender.description": "<PERSON>t meanz Ghast w/ fiery ball", "advancements.nether.return_to_sender.title": "Return 2 sendr", "advancements.nether.ride_strider.description": "Climb on leg boats wif da moshroom stik", "advancements.nether.ride_strider.title": "Dis bout haz legz!", "advancements.nether.ride_strider_in_overworld_lava.description": "Take a <PERSON>ridr for a looooooooong rid no a lav lak in teh Overwrld", "advancements.nether.ride_strider_in_overworld_lava.title": "Itz laiK hOm", "advancements.nether.root.description": "It's gun be HOT", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "WUThering Heights", "advancements.nether.uneasy_alliance.description": "rezcu a <PERSON><PERSON>t from Nether, brin it safly 2 cathouz 2 teh ovawurld... AND THEN BETRAYYYY!!!!!!!", "advancements.nether.uneasy_alliance.title": "Un-eZ Aliance", "advancements.nether.use_lodestone.description": "hit magned wit da other magned", "advancements.nether.use_lodestone.title": "cawntry lowd, taek me hooome to da plazz i belllonggggg :)", "advancements.progress": "%s/%s", "advancements.sad_label": "UnU", "advancements.story.cure_zombie_villager.description": "wekn & then kur a gren ded vlager!", "advancements.story.cure_zombie_villager.title": "Zoombye Medic", "advancements.story.deflect_arrow.description": "Arro <PERSON> pass", "advancements.story.deflect_arrow.title": "<PERSON> says, \"Nawt today!\"", "advancements.story.enchant_item.description": "Inchant an item et an Inchant Tebl", "advancements.story.enchant_item.title": "Encater", "advancements.story.enter_the_end.description": "Entr teh End Portel", "advancements.story.enter_the_end.title": "Teh End?", "advancements.story.enter_the_nether.description": "Bilt, lite and entr Nether Portel", "advancements.story.enter_the_nether.title": "Us needz 2 go deepurr", "advancements.story.follow_ender_eye.description": "folo 4n ey of endr on twtter!!!", "advancements.story.follow_ender_eye.title": "Aye Spai", "advancements.story.form_obsidian.description": "obtian a Blokc ov Hardst Hardest Thing EVAR", "advancements.story.form_obsidian.title": "<PERSON>ze Bucket Chalendz", "advancements.story.iron_tools.description": "<PERSON><PERSON> beter pikax", "advancements.story.iron_tools.title": "Oh isnt it iron pik", "advancements.story.lava_bucket.description": "Fil Bukkit wif Hot Sauce", "advancements.story.lava_bucket.title": "Hot Stuffz", "advancements.story.mine_diamond.description": "Get dimunds", "advancements.story.mine_diamond.title": "DEEMONDS!", "advancements.story.mine_stone.description": "Mine stone wif ur sparckling new pikaxe", "advancements.story.mine_stone.title": "Stoen A<PERSON>", "advancements.story.obtain_armor.description": "Defend u<PERSON><PERSON> wid a piic of iron man suit", "advancements.story.obtain_armor.title": "Suit up k?", "advancements.story.root.description": "Da hart and stowy of de gaim", "advancements.story.root.title": "<PERSON><PERSON><PERSON>", "advancements.story.shiny_gear.description": "Deemond suit saves kitties' lives", "advancements.story.shiny_gear.title": "Covr cat wit shin' diamonds", "advancements.story.smelt_iron.description": "<PERSON> teh iruns", "advancements.story.smelt_iron.title": "I can haz irun", "advancements.story.upgrade_tools.description": "<PERSON>k a beder pikkatt", "advancements.story.upgrade_tools.title": "Lehveleng UP", "advancements.toast.challenge": "Chalendz Kumpleet!", "advancements.toast.goal": "Gool Reetst!", "advancements.toast.task": "Atfancemend maed!", "argument.anchor.invalid": "invalid entity anchor posishun %s", "argument.angle.incomplete": "not completz (1 angl ekspectd)", "argument.angle.invalid": "Dis angul bad", "argument.block.id.invalid": "Cat doezn't know diz bluk taiip '%s'", "argument.block.property.duplicate": "Properti '%s' can unli bee set wans 4 bluk %s", "argument.block.property.invalid": "Bluk %s doez not axept '%s' 4 %s properti", "argument.block.property.novalue": "<PERSON> doez expected valiu four properti '%s' on bluk %s", "argument.block.property.unclosed": "expected closing ] fur blok state properlies", "argument.block.property.unknown": "Bluk %s doez not haf properti '%s'", "argument.block.tag.disallowed": "Tags arent allowud, onli real blokz", "argument.color.invalid": "was dis color? %s", "argument.component.invalid": "invalud chat compononent: %s", "argument.criteria.invalid": "unknknomwn criterion '%s'", "argument.dimension.invalid": "Idk teh dimenzion '%s'", "argument.double.big": "<PERSON><PERSON> must not bE moar than %s, fund %s", "argument.double.low": "Double must nut be lezz den %s, fund %s", "argument.entity.invalid": "Bad naem or UUID", "argument.entity.notfound.entity": "No entity wuz findz", "argument.entity.notfound.player": "No playr wuz findz", "argument.entity.options.advancements.description": "Catz wit atfancemends", "argument.entity.options.distance.description": "ow far is entitii", "argument.entity.options.distance.negative": "distance catnot be negativ", "argument.entity.options.dx.description": "entitis bitwn x n x + dx", "argument.entity.options.dy.description": "catz btwn y and y + dy", "argument.entity.options.dz.description": "kat btwn z and z + dz", "argument.entity.options.gamemode.description": "Catz wit gaemode", "argument.entity.options.inapplicable": "<PERSON><PERSON><PERSON> '%s' isnt aplickyble her", "argument.entity.options.level.description": "lvl", "argument.entity.options.level.negative": "<PERSON><PERSON>s catnot be negativ", "argument.entity.options.limit.description": "Max numberr of entitiz to returnn", "argument.entity.options.limit.toosmall": "Limit must be at least 1", "argument.entity.options.mode.invalid": "invalid aw unnown geim moud '%s'", "argument.entity.options.name.description": "Enti<PERSON>i naem", "argument.entity.options.nbt.description": "Entitiz wit NBT", "argument.entity.options.predicate.description": "Ur own predikate thingie", "argument.entity.options.scores.description": "Entitiz wit scorz", "argument.entity.options.sort.description": "Surt entitiz", "argument.entity.options.sort.irreversible": "Invalid aw unnown surt styl '%s'", "argument.entity.options.tag.description": "Enti<PERSON>z wit tag", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON><PERSON> in team", "argument.entity.options.type.description": "Entitiz of typ", "argument.entity.options.type.invalid": "Invalid aw unnown entkitty styl '%s'", "argument.entity.options.unknown": "unknauwn optshn '%s'", "argument.entity.options.unterminated": "ekspected end of opshunz", "argument.entity.options.valueless": "<PERSON> doez expected valiu four option '%s'", "argument.entity.options.x.description": "x positun", "argument.entity.options.x_rotation.description": "<PERSON><PERSON><PERSON>'z x rotaetun", "argument.entity.options.y.description": "y positun", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON><PERSON>z y rotaetun", "argument.entity.options.z.description": "z positun", "argument.entity.selector.allEntities": "All entitiz", "argument.entity.selector.allPlayers": "All cats", "argument.entity.selector.missing": "Missing selector kat", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON> entti", "argument.entity.selector.nearestPlayer": "Neerest cat", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> don't allow ed ", "argument.entity.selector.randomPlayer": "Rundom cat", "argument.entity.selector.self": "Curent entitiii", "argument.entity.selector.unknown": "Cat doezn't know diz silection taip '%s'", "argument.entity.toomany": "Onwee wun enteetee ish alow'd, butt de provied'd shelektur alowz mur den wun", "argument.enum.invalid": "<PERSON><PERSON> caan't use \"%s\" :(", "argument.float.big": "Float must not be moar than %s, findz %s", "argument.float.low": "Float must not be les than %s, findz %s", "argument.gamemode.invalid": "Dk gamemod: %s", "argument.hexcolor.invalid": "<PERSON><PERSON><PERSON> wrung hex '%s'", "argument.id.invalid": "Invawid ID", "argument.id.unknown": "wuts the id %s ??", "argument.integer.big": "Integr must not be moar than %s, findz %s", "argument.integer.low": "Integr must not be les than %s, findz %s", "argument.item.id.invalid": "Cat doezn't know diz item '%s'", "argument.item.tag.disallowed": "Tegs arunt allwed, only itemz", "argument.literal.incorrect": "expectd literal %s", "argument.long.big": "Lounge must not b moar dan %s, findz %s", "argument.long.low": "Lounge must not b les than %s, findz %s", "argument.message.too_long": "Cat msg woz 2 big (%s is bigr van %s leters)", "argument.nbt.array.invalid": "Invalid urray styl '%s'", "argument.nbt.array.mixed": "Cant insert %s into %s", "argument.nbt.expected.compound": "Ekspektd cat-pound taggy", "argument.nbt.expected.key": "Ekspectd key", "argument.nbt.expected.value": "Eks<PERSON>d valu", "argument.nbt.list.mixed": "Cant insert %s into list ov %s", "argument.nbt.trailing": "Unexpected trailing data", "argument.player.entities": "Onwee playrs can be efect'd by dis cmd, butt de provied'd shelektur includs enteetees", "argument.player.toomany": "Onwee wun playr ish alow'd, butt de provied'd shele<PERSON>ur alowz mur den wun", "argument.player.unknown": "Dat playr duz nawt exist", "argument.pos.missing.double": "Expectd coordinaet", "argument.pos.missing.int": "expected a blockz pozishun", "argument.pos.mixed": "Cant mix world & locat coordz (everything must either use ^ or not)", "argument.pos.outofbounds": "Dis pozishin iz outta diz wurld!!!!!!", "argument.pos.outofworld": "dat pozishun is out of dis world!", "argument.pos.unloaded": "dat pozishun iz not loaded", "argument.pos2d.incomplete": "no enuf coordz, I WANS 2 COORDZ!!!", "argument.pos3d.incomplete": "incompletez (ekspected 3 coords)", "argument.range.empty": "expecc valu or ranj 0f vlues", "argument.range.ints": "<PERSON><PERSON><PERSON> hoel <PERSON><PERSON>z alowd, nat decimulz", "argument.range.swapped": "min cannut be lahrger den max", "argument.resource.invalid_type": "dis thingy '%s' hafe no no(s) '%s' (n u prolly meened '%s')", "argument.resource.not_found": "cant find elemnt x for '%s' ov tiype '%s'", "argument.resource_or_id.failed_to_parse": "<PERSON><PERSON><PERSON> cant reed struksure: %s", "argument.resource_or_id.invalid": "Invalud id/ tag", "argument.resource_or_id.no_such_element": "<PERSON><PERSON> find elemnt '%s' in rejistree '%s'", "argument.resource_selector.not_found": "Me no findy selektr \"%s\" uv taip \"%s\"", "argument.resource_tag.invalid_type": "dis taggie '%s' hafe no no(s) '%s' (n u prolly meened '%s')", "argument.resource_tag.not_found": "camt fimd thingmabob '%s' ov tyep '%s'", "argument.rotation.incomplete": "incompletez (ekspected 2 coords)", "argument.scoreHolder.empty": "No skore holdurs cat be foundz", "argument.scoreboardDisplaySlot.invalid": "Cat doezn't know deespley eslot '%s'", "argument.style.invalid": "Invald styo: %s", "argument.time.invalid_tick_count": "Tik nombur nedz 2 bee nu-negative", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON> yoonit", "argument.time.tick_count_too_low": "gaem ticcs muzt nawt b smolr den %s, lett alon %s", "argument.uuid.invalid": "Invawid kat ID", "argument.waypoint.invalid": "Selectid entyty iz not a woooowpoint", "arguments.block.tag.unknown": "Cat doezn't know diz bluk taj: '%s'", "arguments.function.tag.unknown": "Cat doezn't know diz funktion taj '%s'", "arguments.function.unknown": "Cat doezn't know diz funktion '%s'", "arguments.item.component.expected": "Ekspectd item componant", "arguments.item.component.malformed": "Mutaded '%s' componenant: '%s'", "arguments.item.component.repeated": "Thing copnonent '%s' again, butt uno vawwues onwy.:'(", "arguments.item.component.unknown": "Dunno wat item compnonent iz '%s'", "arguments.item.malformed": "Mewtaded ietm: '%s'", "arguments.item.overstacked": "%s can unli stack ap tu %s", "arguments.item.predicate.malformed": "Mutatd '%s' predi-cat: '%s'", "arguments.item.predicate.unknown": "Idk wat predi-kat iz dat: '%s'", "arguments.item.tag.unknown": "Cat doezn't know diz item teg '%s'", "arguments.nbtpath.node.invalid": "Invalud NBT path element", "arguments.nbtpath.nothing_found": "Fond no elementz matching %s", "arguments.nbtpath.too_deep": "resuwtin nbt 2 diipli birb <PERSON>id, sowwy", "arguments.nbtpath.too_large": "da nbt 2 big, sowwy", "arguments.objective.notFound": "Unknown scorebord objectiv %s", "arguments.objective.readonly": "scorebord objectiv %s iz read-only", "arguments.operation.div0": "cunot divid bai <PERSON>", "arguments.operation.invalid": "In<PERSON><PERSON>un", "arguments.swizzle.invalid": "Wrong swizzle, ekspected 'x', 'y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Armur", "attribute.name.armor_toughness": "Armur fatness", "attribute.name.attack_damage": "Attac dmg", "attribute.name.attack_knockback": "Atacc noccbaccz", "attribute.name.attack_speed": "Attak Faztnez", "attribute.name.block_break_speed": "<PERSON><PERSON> s<PERSON><PERSON>", "attribute.name.block_interaction_range": "<PERSON><PERSON><PERSON> Foolin' Distance", "attribute.name.burning_time": "Tiem be4 ashez", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>", "attribute.name.entity_interaction_range": "<PERSON><PERSON><PERSON>' Foolin' Distance", "attribute.name.explosion_knockback_resistance": "Boomy nokbak restanse", "attribute.name.fall_damage_multiplier": "Fawl Dmg Markipler", "attribute.name.flying_speed": "Flew sped", "attribute.name.follow_range": "<PERSON><PERSON> folow raynge", "attribute.name.generic.armor": "Armur", "attribute.name.generic.armor_toughness": "Armur fatness", "attribute.name.generic.attack_damage": "attak damige", "attribute.name.generic.attack_knockback": "Atacc noccbaccz", "attribute.name.generic.attack_speed": "attak faztnes", "attribute.name.generic.block_interaction_range": "<PERSON><PERSON><PERSON> Foolin' Distance", "attribute.name.generic.burning_time": "Tiem be4 ashez", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON><PERSON>' Foolin' Distance", "attribute.name.generic.explosion_knockback_resistance": "Boomy nokbak restanse", "attribute.name.generic.fall_damage_multiplier": "Fawl Dmg Markipler", "attribute.name.generic.flying_speed": "flew spede", "attribute.name.generic.follow_range": "mob folow raynge", "attribute.name.generic.gravity": "Graffiti", "attribute.name.generic.jump_strength": "Jump Powr", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "WOW get xtra one!!!", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON>", "attribute.name.generic.max_health": "max helth", "attribute.name.generic.movement_efficiency": "Hopz and bouncez", "attribute.name.generic.movement_speed": "spede", "attribute.name.generic.oxygen_bonus": "Air in wotah", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.generic.scale": "Saiz", "attribute.name.generic.step_height": "Paw <PERSON> Height", "attribute.name.generic.water_movement_efficiency": "Watr movin effish<PERSON>ey", "attribute.name.gravity": "Graffiti", "attribute.name.horse.jump_strength": "horze jumpeh strenth", "attribute.name.jump_strength": "Jump Powr", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON>", "attribute.name.luck": "Luk", "attribute.name.max_absorption": "<PERSON><PERSON><PERSON>", "attribute.name.max_health": "Makz helth", "attribute.name.mining_efficiency": "Fuzt Diggin'", "attribute.name.movement_efficiency": "Hopz and bouncez", "attribute.name.movement_speed": "Sped", "attribute.name.oxygen_bonus": "Air in wotah", "attribute.name.player.block_break_speed": "<PERSON><PERSON> s<PERSON><PERSON>", "attribute.name.player.block_interaction_range": "Blok Foolin' Distance", "attribute.name.player.entity_interaction_range": "<PERSON><PERSON><PERSON>' Foolin' Distance", "attribute.name.player.mining_efficiency": "Fuzt Diggin'", "attribute.name.player.sneaking_speed": "<PERSON><PERSON><PERSON>", "attribute.name.player.submerged_mining_speed": "Watr mine sped", "attribute.name.player.sweeping_damage_ratio": "Swepin dmg rayshow", "attribute.name.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.scale": "Skel", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON>", "attribute.name.spawn_reinforcements": "Bad Hooman Reinforzemontz", "attribute.name.step_height": "Paw <PERSON> Height", "attribute.name.submerged_mining_speed": "Watr mine sped", "attribute.name.sweeping_damage_ratio": "Swepin dmg rayshow", "attribute.name.tempt_range": "<PERSON><PERSON>", "attribute.name.water_movement_efficiency": "Watr movin effish<PERSON>ey", "attribute.name.waypoint_receive_range": "Weypont reCv ranj", "attribute.name.waypoint_transmit_range": "<PERSON><PERSON><PERSON><PERSON> trensm<PERSON>t ranj", "attribute.name.zombie.spawn_reinforcements": "Bad Hooman Reinforzemontz", "biome.minecraft.badlands": "Hot dirt land", "biome.minecraft.bamboo_jungle": "Green Stick <PERSON>", "biome.minecraft.basalt_deltas": "Bawzult deltaz", "biome.minecraft.beach": "Watury sans", "biome.minecraft.birch_forest": "Wite Forust", "biome.minecraft.cherry_grove": "pinki groov", "biome.minecraft.cold_ocean": "Cold Oshun", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON> wuds", "biome.minecraft.dark_forest": "Creepy forest", "biome.minecraft.deep_cold_ocean": "Deap Cold Oatshun", "biome.minecraft.deep_dark": "Derk hole", "biome.minecraft.deep_frozen_ocean": "Dep frzo oshun", "biome.minecraft.deep_lukewarm_ocean": "Deap Lukwurm Oatshun", "biome.minecraft.deep_ocean": "Deep Watur Land", "biome.minecraft.desert": "Sandy Place", "biome.minecraft.dripstone_caves": "VERY sharp caevz", "biome.minecraft.end_barrens": "<PERSON>", "biome.minecraft.end_highlands": "<PERSON>", "biome.minecraft.end_midlands": "End Semilandz", "biome.minecraft.eroded_badlands": "Broken Badlandz", "biome.minecraft.flower_forest": "flowy woodz", "biome.minecraft.forest": "Forust", "biome.minecraft.frozen_ocean": "Iced watr land", "biome.minecraft.frozen_peaks": "Frozeen Peeks", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Spiky icy thngz", "biome.minecraft.jagged_peaks": "Jagegd Peeks", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON> caevz", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Mushroom fieldz", "biome.minecraft.nether_wastes": "Nether sux", "biome.minecraft.ocean": "Watur land", "biome.minecraft.old_growth_birch_forest": "Old grouwth brch tree place", "biome.minecraft.old_growth_pine_taiga": "Old grouwth sPine tree place", "biome.minecraft.old_growth_spruce_taiga": "Old grouwth sprooce tree place", "biome.minecraft.pale_garden": "Wite heom ov scawy tree", "biome.minecraft.plains": "Planes", "biome.minecraft.river": "Watur road", "biome.minecraft.savanna": "Savanna oh nana", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "Smol End Izlandz", "biome.minecraft.snowy_beach": "Snuwy WatRside", "biome.minecraft.snowy_plains": "Snowee Plans", "biome.minecraft.snowy_slopes": "<PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Snuwy <PERSON>ga", "biome.minecraft.soul_sand_valley": "ded peepl valee", "biome.minecraft.sparse_jungle": "Spars <PERSON>", "biome.minecraft.stony_peaks": "<PERSON><PERSON>eeks", "biome.minecraft.stony_shore": "Ston Chore", "biome.minecraft.sunflower_plains": "Sunflowr plans", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Tiga", "biome.minecraft.the_end": "THE AND", "biome.minecraft.the_void": "no thing nezz !!!", "biome.minecraft.warm_ocean": "HOT Waterz", "biome.minecraft.warped_forest": "warpd wuds", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON> Forst", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "Badlandz wit treeeez", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence": "<PERSON>", "block.minecraft.acacia_fence_gate": "ACAISHA GAIT", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>' <PERSON>", "block.minecraft.acacia_leaves": "savana lefs", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON><PERSON>g", "block.minecraft.acacia_planks": "Acashuh Plankz", "block.minecraft.acacia_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_sapling": "<PERSON>", "block.minecraft.acacia_sign": "Acashuh Sign", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Acaci <PERSON>", "block.minecraft.acacia_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>' Sign On Da Wall", "block.minecraft.acacia_wall_sign": "A<PERSON>huh Sign on ur wall", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.activator_rail": "POWAAAAAAAAAAAH", "block.minecraft.air": "<PERSON><PERSON>", "block.minecraft.allium": "Alollium", "block.minecraft.amethyst_block": "P<PERSON><PERSON><PERSON> shinee bluk", "block.minecraft.amethyst_cluster": "Dun purpur shinee", "block.minecraft.ancient_debris": "super old stuffz", "block.minecraft.andesite": "grey rock", "block.minecraft.andesite_slab": "Grey Rock", "block.minecraft.andesite_stairs": "Grey Rock Stairz", "block.minecraft.andesite_wall": "Grey Rock Wal", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "sticky melon stik", "block.minecraft.attached_pumpkin_stem": "Atachd pumpkin stem", "block.minecraft.azalea": "Turtl-shel-laik trea", "block.minecraft.azalea_leaves": "Les pre<PERSON> lefs", "block.minecraft.azure_bluet": "Bloo flowre", "block.minecraft.bamboo": "Green Stick", "block.minecraft.bamboo_block": "Thicc bamboo", "block.minecraft.bamboo_button": "Cat stick pressablz", "block.minecraft.bamboo_door": "<PERSON><PERSON>", "block.minecraft.bamboo_fence": "<PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Gren stik Gaet", "block.minecraft.bamboo_hanging_sign": "gren banzuk Danglin' Sign", "block.minecraft.bamboo_mosaic": "<PERSON>ren blok", "block.minecraft.bamboo_mosaic_slab": "Fansi Gren Smol Blok", "block.minecraft.bamboo_mosaic_stairs": "Gren blok Smoll blok", "block.minecraft.bamboo_planks": "Litle gren stick Plankz", "block.minecraft.bamboo_pressure_plate": "gren blukz presurr plete", "block.minecraft.bamboo_sapling": "littl baby green stik", "block.minecraft.bamboo_sign": "gren blukz sign", "block.minecraft.bamboo_slab": "<PERSON><PERSON>", "block.minecraft.bamboo_stairs": "BAMBOOO climby blukz", "block.minecraft.bamboo_trapdoor": "<PERSON><PERSON>", "block.minecraft.bamboo_wall_hanging_sign": "gren banzuk Danglin' Sign On Da Wall", "block.minecraft.bamboo_wall_sign": "gren blukz wol book", "block.minecraft.banner.base.black": "BLAK", "block.minecraft.banner.base.blue": "Fully bloo feld", "block.minecraft.banner.base.brown": "Fulle broun feld", "block.minecraft.banner.base.cyan": "Full cayan feild", "block.minecraft.banner.base.gray": "FLUL grayy field", "block.minecraft.banner.base.green": "GREN", "block.minecraft.banner.base.light_blue": "<PERSON>le lite bloooooo fieldd", "block.minecraft.banner.base.light_gray": "LITTER COLUR", "block.minecraft.banner.base.lime": "CAT MINT COLUR", "block.minecraft.banner.base.magenta": "MEOWENTA", "block.minecraft.banner.base.orange": "HONEY COLUR", "block.minecraft.banner.base.pink": "PAW COLUR", "block.minecraft.banner.base.purple": "PURRP", "block.minecraft.banner.base.red": "cOMEPLETELY red feeld", "block.minecraft.banner.base.white": "Fool wit<PERSON> filed", "block.minecraft.banner.base.yellow": "Fuly ye low feild", "block.minecraft.banner.border.black": "<PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON>", "block.minecraft.banner.border.brown": "brown burder", "block.minecraft.banner.border.cyan": "<PERSON><PERSON> Burdur", "block.minecraft.banner.border.gray": "Gra Burdur", "block.minecraft.banner.border.green": "<PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Lite bloo burdur", "block.minecraft.banner.border.light_gray": "Lite Gra Burdur", "block.minecraft.banner.border.lime": "<PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON> burdur", "block.minecraft.banner.border.pink": "Pnk Burdur", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.red": "Redd Burdur", "block.minecraft.banner.border.white": "Wite burdur", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "BRIK PATTURN!", "block.minecraft.banner.bricks.blue": "covert in emo briks", "block.minecraft.banner.bricks.brown": "Broun briks", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON> briks", "block.minecraft.banner.bricks.gray": "Grey briks", "block.minecraft.banner.bricks.green": "Grean briks", "block.minecraft.banner.bricks.light_blue": "covert in laight blue briks", "block.minecraft.banner.bricks.light_gray": "Lite <PERSON> briks", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON> mazond", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "coverd in oringe briks", "block.minecraft.banner.bricks.pink": "Pink briks", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON> briks", "block.minecraft.banner.bricks.red": "Red briks", "block.minecraft.banner.bricks.white": "wit fel mazon", "block.minecraft.banner.bricks.yellow": "covert in yella briks", "block.minecraft.banner.circle.black": "<PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "B<PERSON>wn Rundel", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Light Bluu <PERSON>", "block.minecraft.banner.circle.light_gray": "Light Gry Rundel", "block.minecraft.banner.circle.lime": "<PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Magnta <PERSON>del", "block.minecraft.banner.circle.orange": "Orang <PERSON>del", "block.minecraft.banner.circle.pink": "<PERSON><PERSON>", "block.minecraft.banner.circle.purple": "Pur<PERSON><PERSON>del", "block.minecraft.banner.circle.red": "<PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "bleck creeper churge", "block.minecraft.banner.creeper.blue": "bleu creeper churge", "block.minecraft.banner.creeper.brown": "brouwn creeper churge", "block.minecraft.banner.creeper.cyan": "bleu creeper churge", "block.minecraft.banner.creeper.gray": "grey creeper churge", "block.minecraft.banner.creeper.green": "gryen creeper churge", "block.minecraft.banner.creeper.light_blue": "lite blew creeper churge", "block.minecraft.banner.creeper.light_gray": "lite grey creeper churge", "block.minecraft.banner.creeper.lime": "liem creeper churge", "block.minecraft.banner.creeper.magenta": "pyrple creeper churge", "block.minecraft.banner.creeper.orange": "oraynge creeper churge", "block.minecraft.banner.creeper.pink": "pienk creeper churge", "block.minecraft.banner.creeper.purple": "pyrple creeper churge", "block.minecraft.banner.creeper.red": "ried creeper churge", "block.minecraft.banner.creeper.white": "whiet creeper churge", "block.minecraft.banner.creeper.yellow": "yllow creeper churge", "block.minecraft.banner.cross.black": "Blak Saltir", "block.minecraft.banner.cross.blue": "Bloo Saltir", "block.minecraft.banner.cross.brown": "Brwn Saltir", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Gra Saltir", "block.minecraft.banner.cross.green": "Gren Saltir", "block.minecraft.banner.cross.light_blue": "Lite Bloo Saltir", "block.minecraft.banner.cross.light_gray": "Lite Gra Saltir", "block.minecraft.banner.cross.lime": "<PERSON>", "block.minecraft.banner.cross.magenta": "Majenta Saltir", "block.minecraft.banner.cross.orange": "Ornge Salitr", "block.minecraft.banner.cross.pink": "Pinc Saltir", "block.minecraft.banner.cross.purple": "Purrrrrrrple Saltir", "block.minecraft.banner.cross.red": "Red Salltir", "block.minecraft.banner.cross.white": "Wite Saltir", "block.minecraft.banner.cross.yellow": "Yelo Saltir", "block.minecraft.banner.curly_border.black": "Blek bordur idedendededed", "block.minecraft.banner.curly_border.blue": "<PERSON>loo burdur indentd", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> bur<PERSON>r indentd", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON> bur<PERSON> indentd", "block.minecraft.banner.curly_border.gray": "Grey burdur indentd", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> burdur indentd", "block.minecraft.banner.curly_border.light_blue": "Lite bloo burdur indentd", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON> burdur indentd", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON> bur<PERSON>r indentd", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON> bur<PERSON> indentd", "block.minecraft.banner.curly_border.orange": "Ornge burdur indentd", "block.minecraft.banner.curly_border.pink": "Pink burdur indentd", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON> bur<PERSON><PERSON> indentd", "block.minecraft.banner.curly_border.red": "Red burdur indentd", "block.minecraft.banner.curly_border.white": "Wite burdur indentd", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> burdur indentd", "block.minecraft.banner.diagonal_left.black": "Blak P<PERSON>", "block.minecraft.banner.diagonal_left.blue": "Blu Pr <PERSON>", "block.minecraft.banner.diagonal_left.brown": "Browhn Pr <PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "Grey Pr <PERSON>", "block.minecraft.banner.diagonal_left.green": "Grean <PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "Lite Blu Pr Behnd <PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON>-<PERSON>'m <PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON><PERSON> P<PERSON>", "block.minecraft.banner.diagonal_left.orange": "Ohrang Pr <PERSON>", "block.minecraft.banner.diagonal_left.pink": "Pink Pr <PERSON><PERSON>", "block.minecraft.banner.diagonal_left.purple": "Purpel Pr <PERSON>", "block.minecraft.banner.diagonal_left.red": "Red Pr <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.white": "Wite <PERSON><PERSON>", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON> Purr Bend", "block.minecraft.banner.diagonal_right.blue": "Bloo Purr Bend", "block.minecraft.banner.diagonal_right.brown": "Brwn Purr Bend", "block.minecraft.banner.diagonal_right.cyan": "Sighan Purr Bend", "block.minecraft.banner.diagonal_right.gray": "Gra Purr Bend", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Lite Bloo Purr Bend", "block.minecraft.banner.diagonal_right.light_gray": "Lite Gra Purr Bend", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.magenta": "Majentaa Purr Bend", "block.minecraft.banner.diagonal_right.orange": "Ornge Purr Bend", "block.minecraft.banner.diagonal_right.pink": "Pnk Purr Bend", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.red": "Redd Purr Bend", "block.minecraft.banner.diagonal_right.white": "Wite Purr Bend", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "<PERSON>lak Pr Behnd In<PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "Blu Pr Behnd In<PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "Browwhn Pr Behnd Inverted", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON> Invertd", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Light Blue Per Bend Invertd", "block.minecraft.banner.diagonal_up_left.light_gray": "Lite Gra Purr Bend Invertd", "block.minecraft.banner.diagonal_up_left.lime": "Lime Per Bend Invertd", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta Per Bend Invertd", "block.minecraft.banner.diagonal_up_left.orange": "Orange Per Bend Invertd", "block.minecraft.banner.diagonal_up_left.pink": "Pink Per Bend Invertd", "block.minecraft.banner.diagonal_up_left.purple": "Perpul Pr Behnd Inverted", "block.minecraft.banner.diagonal_up_left.red": "Red Pr Behnd Inverted", "block.minecraft.banner.diagonal_up_left.white": "<PERSON> <PERSON><PERSON> Invertd", "block.minecraft.banner.diagonal_up_left.yellow": "Yellow Per Bend Invertd", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON> Pa<PERSON>rr<PERSON>etteerf", "block.minecraft.banner.diagonal_up_right.blue": "Bluu Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.brown": "Brown Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON> Sinster Invertud", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON> <PERSON>ur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.green": "<PERSON>ren Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.light_blue": "Light bluu Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.light_gray": "Light Gry Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON> Sinster Invertud", "block.minecraft.banner.diagonal_up_right.magenta": "Magnta Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.orange": "Orang Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.pink": "Pink Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.purple": "Purpl Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.red": "Red Pur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON> <PERSON>ur Bed Sinster Invertud", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON> Pur Bed Sinster Invertud", "block.minecraft.banner.flow.black": "Blak Flw", "block.minecraft.banner.flow.blue": "Bloo Flw", "block.minecraft.banner.flow.brown": "Brow Flw", "block.minecraft.banner.flow.cyan": "Sighun Flw", "block.minecraft.banner.flow.gray": "Grai Flw", "block.minecraft.banner.flow.green": "<PERSON>ren <PERSON>", "block.minecraft.banner.flow.light_blue": "Lite Bloo Flw", "block.minecraft.banner.flow.light_gray": "Lite Grai Flw", "block.minecraft.banner.flow.lime": "Limd Flw", "block.minecraft.banner.flow.magenta": "Majenta Flw", "block.minecraft.banner.flow.orange": "Ornge Flw", "block.minecraft.banner.flow.pink": "Pinky Flw", "block.minecraft.banner.flow.purple": "Parpal Flw", "block.minecraft.banner.flow.red": "Red Flw", "block.minecraft.banner.flow.white": "Waite Flw", "block.minecraft.banner.flow.yellow": "Yello Flw", "block.minecraft.banner.flower.black": "<PERSON>lak flowr karge", "block.minecraft.banner.flower.blue": "Bloo Fluor <PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON>ghan Fluor <PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "Lite Bloo Fluor <PERSON>", "block.minecraft.banner.flower.light_gray": "Lite Gra Fluor <PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "Majentaa Fluor Charg", "block.minecraft.banner.flower.orange": "Orang Fluor <PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON>", "block.minecraft.banner.flower.purple": "Purrrpl Fluor <PERSON>", "block.minecraft.banner.flower.red": "Redd Fluor <PERSON>rg", "block.minecraft.banner.flower.white": "Wite <PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON>", "block.minecraft.banner.globe.black": "<PERSON><PERSON>", "block.minecraft.banner.globe.blue": "Water Glolbe", "block.minecraft.banner.globe.brown": "Chocolate Glolbe", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON>", "block.minecraft.banner.globe.gray": "<PERSON>", "block.minecraft.banner.globe.green": "Grass Glolbe", "block.minecraft.banner.globe.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.globe.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.globe.lime": "Limd Glolbe", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.orange": "Carrot Glolbe", "block.minecraft.banner.globe.pink": "<PERSON><PERSON>", "block.minecraft.banner.globe.purple": "<PERSON><PERSON>al <PERSON>lol<PERSON>", "block.minecraft.banner.globe.red": "Mojang <PERSON>lbe", "block.minecraft.banner.globe.white": "<PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON>", "block.minecraft.banner.gradient.black": "bleck faed", "block.minecraft.banner.gradient.blue": "bleu faed", "block.minecraft.banner.gradient.brown": "brouwn faed", "block.minecraft.banner.gradient.cyan": "sea faed", "block.minecraft.banner.gradient.gray": "grey faed", "block.minecraft.banner.gradient.green": "greeen faed", "block.minecraft.banner.gradient.light_blue": "lit bru gradz", "block.minecraft.banner.gradient.light_gray": "lite grey faed", "block.minecraft.banner.gradient.lime": "liem faed", "block.minecraft.banner.gradient.magenta": "maginte gradz", "block.minecraft.banner.gradient.orange": "oranje gradz", "block.minecraft.banner.gradient.pink": "pienk faed", "block.minecraft.banner.gradient.purple": "TEH PRPLZ TRANSIST", "block.minecraft.banner.gradient.red": "ryd faed", "block.minecraft.banner.gradient.white": "wit gradz", "block.minecraft.banner.gradient.yellow": "yelouw faed", "block.minecraft.banner.gradient_up.black": "blk gradz", "block.minecraft.banner.gradient_up.blue": "bru gradz", "block.minecraft.banner.gradient_up.brown": "bruwn gradz", "block.minecraft.banner.gradient_up.cyan": "nyan gradz", "block.minecraft.banner.gradient_up.gray": "gra grade", "block.minecraft.banner.gradient_up.green": "gran gradz", "block.minecraft.banner.gradient_up.light_blue": "lite blu gradz", "block.minecraft.banner.gradient_up.light_gray": "lit gra grade", "block.minecraft.banner.gradient_up.lime": "lemi gradz", "block.minecraft.banner.gradient_up.magenta": "magintaz gradz", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON> Baze <PERSON>rai<PERSON>", "block.minecraft.banner.gradient_up.pink": "oink gradz", "block.minecraft.banner.gradient_up.purple": "poi<PERSON> gradz", "block.minecraft.banner.gradient_up.red": "rad gradz", "block.minecraft.banner.gradient_up.white": "Wite <PERSON><PERSON>", "block.minecraft.banner.gradient_up.yellow": "yello grad", "block.minecraft.banner.guster.black": "<PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "Grai Gusta", "block.minecraft.banner.guster.green": "<PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "Lite Bloo <PERSON>", "block.minecraft.banner.guster.light_gray": "Lite Grai Gusta", "block.minecraft.banner.guster.lime": "<PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Ornge Gusta", "block.minecraft.banner.guster.pink": "<PERSON><PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "blak purr fez", "block.minecraft.banner.half_horizontal.blue": "blu purr fez", "block.minecraft.banner.half_horizontal.brown": "brawn purr fez", "block.minecraft.banner.half_horizontal.cyan": "cian purr fez", "block.minecraft.banner.half_horizontal.gray": "grey perr fez", "block.minecraft.banner.half_horizontal.green": "grean purr fez", "block.minecraft.banner.half_horizontal.light_blue": "lightish blu perr fez", "block.minecraft.banner.half_horizontal.light_gray": "lightish grey purr fez", "block.minecraft.banner.half_horizontal.lime": "lime perr fez", "block.minecraft.banner.half_horizontal.magenta": "majenta perr fez", "block.minecraft.banner.half_horizontal.orange": "orange perr fez", "block.minecraft.banner.half_horizontal.pink": "pink perr fez", "block.minecraft.banner.half_horizontal.purple": "purrpel purr fez", "block.minecraft.banner.half_horizontal.red": "red purr fez", "block.minecraft.banner.half_horizontal.white": "colorless perr fez", "block.minecraft.banner.half_horizontal.yellow": "yello perr fez", "block.minecraft.banner.half_horizontal_bottom.black": "blak purr fess inverted", "block.minecraft.banner.half_horizontal_bottom.blue": "Bloo par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.brown": "brown purr fess inverted", "block.minecraft.banner.half_horizontal_bottom.cyan": "Nyan par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.gray": "Grey par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.green": "green purr fess inverted", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Lite bloo par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Lite Gray par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.lime": "Lyem par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.magenta": "Majenta par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.orange": "Ornge par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.pink": "Pink par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.purple": "Purpal par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.red": "Red par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.white": "Wite par fazz invertud", "block.minecraft.banner.half_horizontal_bottom.yellow": "Yello par fazz invertud", "block.minecraft.banner.half_vertical.black": "blak pur payl", "block.minecraft.banner.half_vertical.blue": "bloo pore payl", "block.minecraft.banner.half_vertical.brown": "broun  pr payl", "block.minecraft.banner.half_vertical.cyan": "cian pur pal", "block.minecraft.banner.half_vertical.gray": "grey purr peil", "block.minecraft.banner.half_vertical.green": "grean por pail", "block.minecraft.banner.half_vertical.light_blue": "lightt bkuez purr pail", "block.minecraft.banner.half_vertical.light_gray": "lit gra pur pal", "block.minecraft.banner.half_vertical.lime": "leim purr peil", "block.minecraft.banner.half_vertical.magenta": "majenta purr pail", "block.minecraft.banner.half_vertical.orange": "oringe purr peil", "block.minecraft.banner.half_vertical.pink": "stylish purr peil", "block.minecraft.banner.half_vertical.purple": "porple pair paiyiyil", "block.minecraft.banner.half_vertical.red": "red par pal", "block.minecraft.banner.half_vertical.white": "white purr pail", "block.minecraft.banner.half_vertical.yellow": "yella purr peil", "block.minecraft.banner.half_vertical_right.black": "blak purr pale inverted", "block.minecraft.banner.half_vertical_right.blue": "blu purr pale inverted", "block.minecraft.banner.half_vertical_right.brown": "brown purr pale inverted", "block.minecraft.banner.half_vertical_right.cyan": "cyan purr pale inverted", "block.minecraft.banner.half_vertical_right.gray": "grey purr pale inverted", "block.minecraft.banner.half_vertical_right.green": "green purr pale inverted", "block.minecraft.banner.half_vertical_right.light_blue": "lightish blu purr pale inverted", "block.minecraft.banner.half_vertical_right.light_gray": "lighish grey purr pale inverted", "block.minecraft.banner.half_vertical_right.lime": "lime purr pale inverted", "block.minecraft.banner.half_vertical_right.magenta": "majenta purr pale inverted", "block.minecraft.banner.half_vertical_right.orange": "orange purr pale inverted", "block.minecraft.banner.half_vertical_right.pink": "pink purr pale inverted", "block.minecraft.banner.half_vertical_right.purple": "purrrppple purr pale inverted", "block.minecraft.banner.half_vertical_right.red": "red purr pale inverted", "block.minecraft.banner.half_vertical_right.white": "colorless purr pale inverted", "block.minecraft.banner.half_vertical_right.yellow": "yellow purr pale inverted", "block.minecraft.banner.mojang.black": "Blak thng", "block.minecraft.banner.mojang.blue": "Bloo thng", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON> thng", "block.minecraft.banner.mojang.cyan": "<PERSON>yan thng", "block.minecraft.banner.mojang.gray": "Grey thng", "block.minecraft.banner.mojang.green": "<PERSON><PERSON>n thng", "block.minecraft.banner.mojang.light_blue": "Lite bloo thng", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON> thng", "block.minecraft.banner.mojang.lime": "Lyme thng", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON> thng", "block.minecraft.banner.mojang.orange": "Ornge thng", "block.minecraft.banner.mojang.pink": "Pig-colored thing", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>pal thng", "block.minecraft.banner.mojang.red": "Redz thng", "block.minecraft.banner.mojang.white": "Wite thng", "block.minecraft.banner.mojang.yellow": "<PERSON>llo thng", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> noze", "block.minecraft.banner.piglin.blue": "Blu nouze", "block.minecraft.banner.piglin.brown": "Brown noze", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "Gray noze", "block.minecraft.banner.piglin.green": "Green noze", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON> bluu nouze", "block.minecraft.banner.piglin.light_gray": "Lite gray noze", "block.minecraft.banner.piglin.lime": "Limy nose", "block.minecraft.banner.piglin.magenta": "Magenta nouze", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON> noze", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON> noze", "block.minecraft.banner.piglin.purple": "Pur<PERSON>l noze", "block.minecraft.banner.piglin.red": "Redz noze", "block.minecraft.banner.piglin.white": "Wite noze", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "blak losng", "block.minecraft.banner.rhombus.blue": "<PERSON>", "block.minecraft.banner.rhombus.brown": "broun lozeng", "block.minecraft.banner.rhombus.cyan": "cian lozene", "block.minecraft.banner.rhombus.gray": "grey lozunj", "block.minecraft.banner.rhombus.green": "greyn lozinge", "block.minecraft.banner.rhombus.light_blue": "lit blu lozenge", "block.minecraft.banner.rhombus.light_gray": "lite gruy losinge", "block.minecraft.banner.rhombus.lime": "lym LOLzenge", "block.minecraft.banner.rhombus.magenta": "majina lizange", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON> l<PERSON>e", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "pupell losenge", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "wyte lozunge", "block.minecraft.banner.rhombus.yellow": "yullo lozunge", "block.minecraft.banner.skull.black": "Blac Scul Charg", "block.minecraft.banner.skull.blue": "Bloo Scul Charg", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON>ghan Scul Charg", "block.minecraft.banner.skull.gray": "Gra Scul Charg", "block.minecraft.banner.skull.green": "<PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Lite Bloo Scul Charg", "block.minecraft.banner.skull.light_gray": "Lite Gra Scul Charg", "block.minecraft.banner.skull.lime": "<PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "Majentaa Scul Charg", "block.minecraft.banner.skull.orange": "Orang Scul Charg", "block.minecraft.banner.skull.pink": "Pinc Scul Charg", "block.minecraft.banner.skull.purple": "Purrrpl Scul Charg", "block.minecraft.banner.skull.red": "rad SKULLZ charg", "block.minecraft.banner.skull.white": "Wite <PERSON>ul Charg", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.brown": "Brawn Palee", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON>", "block.minecraft.banner.small_stripes.green": "Gre-n Palee", "block.minecraft.banner.small_stripes.light_blue": "Lite Bloo Palee", "block.minecraft.banner.small_stripes.light_gray": "Light Grey Palee", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.purple": "PurpL Palee", "block.minecraft.banner.small_stripes.red": "<PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "Black Base Dextr Canton", "block.minecraft.banner.square_bottom_left.blue": "bluz bais lepht skuair", "block.minecraft.banner.square_bottom_left.brown": "browzn bais lepht skuair", "block.minecraft.banner.square_bottom_left.cyan": "sian bais lepht skuair", "block.minecraft.banner.square_bottom_left.gray": "grae bais lepht skuair", "block.minecraft.banner.square_bottom_left.green": "Green Base Dextr Canton", "block.minecraft.banner.square_bottom_left.light_blue": "lite blu bais lepht skuair", "block.minecraft.banner.square_bottom_left.light_gray": "lite grae bais lepht skuair", "block.minecraft.banner.square_bottom_left.lime": "laim bais lepht skuair", "block.minecraft.banner.square_bottom_left.magenta": "majnta bais lepht skuair", "block.minecraft.banner.square_bottom_left.orange": "oranj bais lepht skuair", "block.minecraft.banner.square_bottom_left.pink": "pinkz bais lepht skuair", "block.minecraft.banner.square_bottom_left.purple": "purpul bais lepht skuair", "block.minecraft.banner.square_bottom_left.red": "Red Base Dextr Canton", "block.minecraft.banner.square_bottom_left.white": "wiit bais lepht skuair", "block.minecraft.banner.square_bottom_left.yellow": "yeloe bais lepht skuair", "block.minecraft.banner.square_bottom_right.black": "blak bais rite skuair", "block.minecraft.banner.square_bottom_right.blue": "blu bais rite skuair", "block.minecraft.banner.square_bottom_right.brown": "braun bais rite skuair", "block.minecraft.banner.square_bottom_right.cyan": "sian bais rite skuair", "block.minecraft.banner.square_bottom_right.gray": "grai bais rite skuair", "block.minecraft.banner.square_bottom_right.green": "grin bais rite skuair", "block.minecraft.banner.square_bottom_right.light_blue": "lite blu bais rite skuair", "block.minecraft.banner.square_bottom_right.light_gray": "lite grai bais rite skuair", "block.minecraft.banner.square_bottom_right.lime": "laim bais rite skuair", "block.minecraft.banner.square_bottom_right.magenta": "majnta bais rite skuair", "block.minecraft.banner.square_bottom_right.orange": "oraje bais rite skuair", "block.minecraft.banner.square_bottom_right.pink": "pinck bais rite skuair", "block.minecraft.banner.square_bottom_right.purple": "purpul bais rite skuair", "block.minecraft.banner.square_bottom_right.red": "red bais rite skuair", "block.minecraft.banner.square_bottom_right.white": "White baze sinistor kattun", "block.minecraft.banner.square_bottom_right.yellow": "yelloe bais rite skuair", "block.minecraft.banner.square_top_left.black": "blak cheef dextor kattun", "block.minecraft.banner.square_top_left.blue": "blooh cheef dextor cantun", "block.minecraft.banner.square_top_left.brown": "broawn cheef dextor kattun", "block.minecraft.banner.square_top_left.cyan": "cian cheef dextor cantun", "block.minecraft.banner.square_top_left.gray": "greh cheef dextor cantun", "block.minecraft.banner.square_top_left.green": "grein cheef dextor kattun", "block.minecraft.banner.square_top_left.light_blue": "liat blooh cheef dextor cantun", "block.minecraft.banner.square_top_left.light_gray": "liat greh cheef dextor cantun", "block.minecraft.banner.square_top_left.lime": "liame cheef dextor cantun", "block.minecraft.banner.square_top_left.magenta": "magento<PERSON>h cheef dextor cantun", "block.minecraft.banner.square_top_left.orange": "oringe cheef dextor cantun", "block.minecraft.banner.square_top_left.pink": "piank cheef dextor cantun", "block.minecraft.banner.square_top_left.purple": "perpol cheef dextor cantun", "block.minecraft.banner.square_top_left.red": "red cheef dextor kattun", "block.minecraft.banner.square_top_left.white": "white cheef dextor cantun", "block.minecraft.banner.square_top_left.yellow": "yelow cheef dextor cantun", "block.minecraft.banner.square_top_right.black": "black cheef sinistor cantun", "block.minecraft.banner.square_top_right.blue": "blooh cheef sinistor cantun", "block.minecraft.banner.square_top_right.brown": "broawn cheef sinistor cantun", "block.minecraft.banner.square_top_right.cyan": "cian cheef sinistor cantun", "block.minecraft.banner.square_top_right.gray": "<PERSON>h cheef sinistor cantun", "block.minecraft.banner.square_top_right.green": "grein cheef sinistor cantun", "block.minecraft.banner.square_top_right.light_blue": "liat bloo cheef sinistor cantun", "block.minecraft.banner.square_top_right.light_gray": "liat greyh cheef sinistor cantun", "block.minecraft.banner.square_top_right.lime": "liame cheef sinistor cantun", "block.minecraft.banner.square_top_right.magenta": "magentorh cheef sinistor cantun", "block.minecraft.banner.square_top_right.orange": "oringe cheef sinistor cantun", "block.minecraft.banner.square_top_right.pink": "piank cheef sinistor cantun", "block.minecraft.banner.square_top_right.purple": "perpol cheef sinistor cantun", "block.minecraft.banner.square_top_right.red": "red cheef sinistor cantun", "block.minecraft.banner.square_top_right.white": "white cheef sinistor cantun", "block.minecraft.banner.square_top_right.yellow": "yelow cheef sinistor cantun", "block.minecraft.banner.straight_cross.black": "Blk Croz", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "Brawn Croz", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "Grai Croz", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Zero Bleu Croz", "block.minecraft.banner.straight_cross.light_gray": "Zero Grai Croz", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "Orang-<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "Pink Panther Croz", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON>", "block.minecraft.banner.straight_cross.white": "No Color Croz", "block.minecraft.banner.straight_cross.yellow": "Hello Yellow Croz", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "Bloo Baze", "block.minecraft.banner.stripe_bottom.brown": "Brwn Baze", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Lite Bloo Baze", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "Orang Baze", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "Red Baze", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON> Baze", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "Bloo Pal", "block.minecraft.banner.stripe_center.brown": "Brwn Pal", "block.minecraft.banner.stripe_center.cyan": "Sigh-an <PERSON>l", "block.minecraft.banner.stripe_center.gray": "Gra <PERSON>l", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "Lite Bloo Pal", "block.minecraft.banner.stripe_center.light_gray": "Lite Gra Pal", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "Majentaa Pal", "block.minecraft.banner.stripe_center.orange": "Ornge Pal", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "Red Pal\n", "block.minecraft.banner.stripe_center.white": "Wite Pal", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Bluk Vend Sinizturr", "block.minecraft.banner.stripe_downleft.blue": "Bluu Bend SinistR", "block.minecraft.banner.stripe_downleft.brown": "Brahwn Bend SinistR", "block.minecraft.banner.stripe_downleft.cyan": "CyN Bend SinistR", "block.minecraft.banner.stripe_downleft.gray": "Grey Bend SinistR", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Light Bluu Bend SinistR", "block.minecraft.banner.stripe_downleft.light_gray": "Light Grey Bend SinistR", "block.minecraft.banner.stripe_downleft.lime": "Limeh Bend SinistR", "block.minecraft.banner.stripe_downleft.magenta": "Magentah Bend SinistR", "block.minecraft.banner.stripe_downleft.orange": "Oreng Bend SinistR", "block.minecraft.banner.stripe_downleft.pink": "Pink Bend SinistR", "block.minecraft.banner.stripe_downleft.purple": "PurpL Bend SinistR", "block.minecraft.banner.stripe_downleft.red": "<PERSON>", "block.minecraft.banner.stripe_downleft.white": "Wite Bend SinistR", "block.minecraft.banner.stripe_downleft.yellow": "Yolo-w Bend SinistR", "block.minecraft.banner.stripe_downright.black": "darrk dawn line", "block.minecraft.banner.stripe_downright.blue": "Bloo Bend", "block.minecraft.banner.stripe_downright.brown": "bruwn side line", "block.minecraft.banner.stripe_downright.cyan": "greeen bleu side line", "block.minecraft.banner.stripe_downright.gray": "greay side line", "block.minecraft.banner.stripe_downright.green": "greeen side line", "block.minecraft.banner.stripe_downright.light_blue": "Teh lite blu bend", "block.minecraft.banner.stripe_downright.light_gray": "wite greay side line", "block.minecraft.banner.stripe_downright.lime": "brite greeen side line", "block.minecraft.banner.stripe_downright.magenta": "Teh Magentra Bend", "block.minecraft.banner.stripe_downright.orange": "orng bend", "block.minecraft.banner.stripe_downright.pink": "peenk side line", "block.minecraft.banner.stripe_downright.purple": "Pur<PERSON><PERSON>le Bend", "block.minecraft.banner.stripe_downright.red": "red dawn line", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "yelaw side line", "block.minecraft.banner.stripe_left.black": "blak pail deztr", "block.minecraft.banner.stripe_left.blue": "blu pail deztr", "block.minecraft.banner.stripe_left.brown": "braun pail deztr", "block.minecraft.banner.stripe_left.cyan": "sian pail de<PERSON>tr", "block.minecraft.banner.stripe_left.gray": "grei pail deztr", "block.minecraft.banner.stripe_left.green": "grin pail deztr", "block.minecraft.banner.stripe_left.light_blue": "<PERSON><PERSON> Bleu <PERSON>", "block.minecraft.banner.stripe_left.light_gray": "lite grei pail deztr", "block.minecraft.banner.stripe_left.lime": "laim pail deztr", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "pinck pail deztr", "block.minecraft.banner.stripe_left.purple": "purpl pail deztr", "block.minecraft.banner.stripe_left.red": "rad pail deztr", "block.minecraft.banner.stripe_left.white": "Wite <PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "Blak Fezz", "block.minecraft.banner.stripe_middle.blue": "Bloo Fezz", "block.minecraft.banner.stripe_middle.brown": "Brwn Fezz", "block.minecraft.banner.stripe_middle.cyan": "Sigh-an Fezz", "block.minecraft.banner.stripe_middle.gray": "Gray Fezz", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Late Bluu Fezz", "block.minecraft.banner.stripe_middle.light_gray": "Li<PERSON>", "block.minecraft.banner.stripe_middle.lime": "Lame Fezz", "block.minecraft.banner.stripe_middle.magenta": "purpl-ey Fezz", "block.minecraft.banner.stripe_middle.orange": "oranj dawn <PERSON>zz", "block.minecraft.banner.stripe_middle.pink": "Pinc Fezz", "block.minecraft.banner.stripe_middle.purple": "Purrrrple Fezz", "block.minecraft.banner.stripe_middle.red": "Red Fezz", "block.minecraft.banner.stripe_middle.white": "blanck dawn Fezz", "block.minecraft.banner.stripe_middle.yellow": "Yellau Fezz", "block.minecraft.banner.stripe_right.black": "Blak Peil Sinizter", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON>z P<PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Late <PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_gray": "Light Grayz Peil Sinizter", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "Meigent Peil Sinizter", "block.minecraft.banner.stripe_right.orange": "Oreng Peil Sinizter", "block.minecraft.banner.stripe_right.pink": "Pink Peil Sinizter", "block.minecraft.banner.stripe_right.purple": "Purpl Peil Sinizter", "block.minecraft.banner.stripe_right.red": "Red Peil Sinizter", "block.minecraft.banner.stripe_right.white": "<PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "B<PERSON>o Chif", "block.minecraft.banner.stripe_top.brown": "B<PERSON><PERSON>f", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Lite Bloo <PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Orang <PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "Redish Chif", "block.minecraft.banner.stripe_top.white": "Wite <PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON>", "block.minecraft.banner.triangle_bottom.blue": "Blu Chevronz", "block.minecraft.banner.triangle_bottom.brown": "<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "GREY SHEVRON", "block.minecraft.banner.triangle_bottom.green": "Green Chevronz", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "VERY LITE GREY SHEVRON", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "PIINK SHEVRON", "block.minecraft.banner.triangle_bottom.purple": "PRPLZ CHVRN", "block.minecraft.banner.triangle_bottom.red": "Red Chevronz", "block.minecraft.banner.triangle_bottom.white": "Wite <PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "blakc upsied v", "block.minecraft.banner.triangle_top.blue": "blew upsied v", "block.minecraft.banner.triangle_top.brown": "broun upsied v", "block.minecraft.banner.triangle_top.cyan": "bleuy upsied v", "block.minecraft.banner.triangle_top.gray": "grey upsied v", "block.minecraft.banner.triangle_top.green": "grn upsied v", "block.minecraft.banner.triangle_top.light_blue": "lite blew upsied v", "block.minecraft.banner.triangle_top.light_gray": "lite grey upsied v", "block.minecraft.banner.triangle_top.lime": "liem upsied v", "block.minecraft.banner.triangle_top.magenta": "puerple upsied v", "block.minecraft.banner.triangle_top.orange": "orangue upsied v", "block.minecraft.banner.triangle_top.pink": "pienk upsied v", "block.minecraft.banner.triangle_top.purple": "purrple upsied v", "block.minecraft.banner.triangle_top.red": "ryd upsied v", "block.minecraft.banner.triangle_top.white": "whiet upsied v", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> upsied v", "block.minecraft.banner.triangles_bottom.black": "bleck bottum indiented", "block.minecraft.banner.triangles_bottom.blue": "bleu bottum indiented", "block.minecraft.banner.triangles_bottom.brown": "broun bottum indiented", "block.minecraft.banner.triangles_bottom.cyan": "cian bottum indiented", "block.minecraft.banner.triangles_bottom.gray": "grey bottum indiented", "block.minecraft.banner.triangles_bottom.green": "greun bottum indiented", "block.minecraft.banner.triangles_bottom.light_blue": "lite bleu bottum indiented", "block.minecraft.banner.triangles_bottom.light_gray": "lite grey bottum indiented", "block.minecraft.banner.triangles_bottom.lime": "liem bottum indiented", "block.minecraft.banner.triangles_bottom.magenta": "prple bottum indiented", "block.minecraft.banner.triangles_bottom.orange": "oraynge bottum indiented", "block.minecraft.banner.triangles_bottom.pink": "pienk bottum indiented", "block.minecraft.banner.triangles_bottom.purple": "pyrple bottum indiented", "block.minecraft.banner.triangles_bottom.red": "read bottum indiented", "block.minecraft.banner.triangles_bottom.white": "whiet bottum indiented", "block.minecraft.banner.triangles_bottom.yellow": "YELLOU BASS INDEZTED", "block.minecraft.banner.triangles_top.black": "BLAWK CHEEF INDEZTED", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>d", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_gray": "<PERSON>t gray <PERSON><PERSON><PERSON>d", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON><PERSON>d", "block.minecraft.banner.triangles_top.red": "<PERSON> <PERSON><PERSON><PERSON>d", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON>", "block.minecraft.barrel": "Fish box", "block.minecraft.barrier": "You shall not pass", "block.minecraft.basalt": "Bawzult", "block.minecraft.beacon": "<PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON>", "block.minecraft.bed.no_sleep": "U can shleep onwy at nite and duwin thundewstuwms", "block.minecraft.bed.not_safe": "U do not haf tu rezt nao; der are monztahrs nirby", "block.minecraft.bed.obstructed": "Dis bed iz obstructd", "block.minecraft.bed.occupied": "<PERSON><PERSON><PERSON> stole ur bed!", "block.minecraft.bed.too_far_away": "U do not haf tu rezt nao; da bed is TOO FAR awey", "block.minecraft.bedrock": "TROL BLUKZ", "block.minecraft.bee_nest": "B nest", "block.minecraft.beehive": "<PERSON><PERSON>'s place", "block.minecraft.beetroots": "Red Juicy Undrgrawnd Plant", "block.minecraft.bell": "<PERSON>y", "block.minecraft.big_dripleaf": "Big fall thru plantt", "block.minecraft.big_dripleaf_stem": "Big fall thru plantt stehm", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "BIRCH SCRATCHEZPOST", "block.minecraft.birch_fence_gate": "BURCH GAIT", "block.minecraft.birch_hanging_sign": "Burchs Danglin' Sign", "block.minecraft.birch_leaves": "birch salad", "block.minecraft.birch_log": "lite wood<PERSON>n lawg", "block.minecraft.birch_planks": "lite woodezn blox", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.birch_sapling": "baby burch", "block.minecraft.birch_sign": "Burchs Sign", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON> Stairz", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON> T<PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> Danglin' Sign On Da Wall", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON> Sign on ur wall", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "blakc bahnor", "block.minecraft.black_bed": "Blak Bed", "block.minecraft.black_candle": "Blak land pikl", "block.minecraft.black_candle_cake": "Caek wif Blak Candl", "block.minecraft.black_carpet": "Black Cat Rug", "block.minecraft.black_concrete": "Blek tough bluk", "block.minecraft.black_concrete_powder": "Blak tough bluk powdr", "block.minecraft.black_glazed_terracotta": "Blek mosaic", "block.minecraft.black_shulker_box": "<PERSON> Shulk<PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON>aind G<PERSON>", "block.minecraft.black_stained_glass_pane": "blerk thin culurd thingy", "block.minecraft.black_terracotta": "<PERSON><PERSON>", "block.minecraft.black_wool": "Blackish Fur Bluk", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "b<PERSON><PERSON><PERSON> sleb", "block.minecraft.blackstone_stairs": "bleck rock stairz", "block.minecraft.blackstone_wall": "ble<PERSON><PERSON> wal", "block.minecraft.blast_furnace": "BlAsT FuRnAcE", "block.minecraft.blue_banner": "bloo bahnor", "block.minecraft.blue_bed": "Bloo Bed", "block.minecraft.blue_candle": "Bloo hot stik", "block.minecraft.blue_candle_cake": "Caek wif Bloo Land pikl", "block.minecraft.blue_carpet": "Blu Cat Rug", "block.minecraft.blue_concrete": "<PERSON>u tough bluk", "block.minecraft.blue_concrete_powder": "Bluu tough bluk powdr", "block.minecraft.blue_glazed_terracotta": "Bluu mosaic", "block.minecraft.blue_ice": "Big blu", "block.minecraft.blue_orchid": "pretteh bloo flowr", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "Waterly Stained Glazz", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON> Stan<PERSON>", "block.minecraft.blue_terracotta": "Bloo Terrakattah", "block.minecraft.blue_wool": "Bloo Fur Bluk", "block.minecraft.bone_block": "Compresd bone", "block.minecraft.bookshelf": "bed 4 bookz", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.brain_coral_block": "Brein koral cube", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON> koral fen", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "<PERSON><PERSON>", "block.minecraft.brick_stairs": "<PERSON><PERSON>z", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "brickz", "block.minecraft.brown_banner": "broun bahnor", "block.minecraft.brown_bed": "Brownish Bed", "block.minecraft.brown_candle": "Brwn land pikl", "block.minecraft.brown_candle_cake": "<PERSON><PERSON>k wif <PERSON>", "block.minecraft.brown_carpet": "Brownish Cat Rug", "block.minecraft.brown_concrete": "Brownish tough bluk", "block.minecraft.brown_concrete_powder": "Brownish tough bluk powdr", "block.minecraft.brown_glazed_terracotta": "Brownish mosaic", "block.minecraft.brown_mushroom": "brwn Mushroom", "block.minecraft.brown_mushroom_block": "brwn Mushroom Blok", "block.minecraft.brown_shulker_box": "Brownish <PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Brownly Stainedly Glazz", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_terracotta": "Brewn Terrakattah", "block.minecraft.brown_wool": "Brownish Fur Bluk", "block.minecraft.bubble_column": "babul kolum", "block.minecraft.bubble_coral": "Bubol koral", "block.minecraft.bubble_coral_block": "Bubol koral cube", "block.minecraft.bubble_coral_fan": "Bubol koral fen", "block.minecraft.bubble_coral_wall_fan": "Bable koral wull fan", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON> shinee makr", "block.minecraft.bush": "Smol tree", "block.minecraft.cactus": "Spiky Green Plant", "block.minecraft.cactus_flower": "Thornz flowah", "block.minecraft.cake": "liez", "block.minecraft.calcite": "Wite rok", "block.minecraft.calibrated_sculk_sensor": "smart catz Sculk detektor", "block.minecraft.campfire": "Campfireh", "block.minecraft.candle": "Land pikl", "block.minecraft.candle_cake": "lie wif hot stix", "block.minecraft.carrots": "i haz rabit fuds", "block.minecraft.cartography_table": "Cartogrophy Table", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cauldron": "Rly big pot", "block.minecraft.cave_air": "air of spOoOky cavess", "block.minecraft.cave_vines": "Caev wiskrs", "block.minecraft.cave_vines_plant": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Alpha Block Dat Duz A Congo Line With Other Alpha Blocks", "block.minecraft.cherry_button": "<PERSON>", "block.minecraft.cherry_door": "<PERSON> dur", "block.minecraft.cherry_fence": "Sakura fence", "block.minecraft.cherry_fence_gate": "Sakura fenc gaet", "block.minecraft.cherry_hanging_sign": "Sakura Danglin' Sign", "block.minecraft.cherry_leaves": "<PERSON> lef", "block.minecraft.cherry_log": "<PERSON>g", "block.minecraft.cherry_planks": "Sakura plankz", "block.minecraft.cherry_pressure_plate": "<PERSON> Prse<PERSON>", "block.minecraft.cherry_sapling": "baby sakura", "block.minecraft.cherry_sign": "Sakura sign", "block.minecraft.cherry_slab": "Sakura sleb", "block.minecraft.cherry_stairs": "<PERSON> stairz", "block.minecraft.cherry_trapdoor": "<PERSON> trapdur", "block.minecraft.cherry_wall_hanging_sign": "Sakura Wall Danglin' Sign", "block.minecraft.cherry_wall_sign": "Sakura Sign On Da Wall", "block.minecraft.cherry_wood": "<PERSON> wuud", "block.minecraft.chest": "Cat Box", "block.minecraft.chipped_anvil": "Oh so much chipped anvehl", "block.minecraft.chiseled_bookshelf": "Hollow Bed 4 Books", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "chizeled dark ston", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON>d nether brickz", "block.minecraft.chiseled_polished_blackstone": "chizald shiny blak rok", "block.minecraft.chiseled_quartz_block": "Chizald Kworts Blak", "block.minecraft.chiseled_red_sandstone": "Chizald warmy sanstown", "block.minecraft.chiseled_resin_bricks": "Spooky yelo stiky brix", "block.minecraft.chiseled_sandstone": "carved litter box rockz", "block.minecraft.chiseled_stone_bricks": "Stacked Rockz en an Patternz", "block.minecraft.chiseled_tuff": "Pretteh Tff", "block.minecraft.chiseled_tuff_bricks": "Pretth Tff Rocz", "block.minecraft.chorus_flower": "Meow flawur", "block.minecraft.chorus_plant": "Meow Plantz", "block.minecraft.clay": "CLAE", "block.minecraft.closed_eyeblossom": "Sleepy eye flowah", "block.minecraft.coal_block": "Koala", "block.minecraft.coal_ore": "blak dusty rok", "block.minecraft.coarse_dirt": "Ruff durt", "block.minecraft.cobbled_deepslate": "coobled dark ston", "block.minecraft.cobbled_deepslate_slab": "coobled dark ston sleb", "block.minecraft.cobbled_deepslate_stairs": "coobled dark ston stairz", "block.minecraft.cobbled_deepslate_wall": "coobled dark ston wal", "block.minecraft.cobblestone": "Cooblestoneh", "block.minecraft.cobblestone_slab": "small pebble blok", "block.minecraft.cobblestone_stairs": "rok stairz", "block.minecraft.cobblestone_wall": "COBULSTOWN WALL", "block.minecraft.cobweb": "icky spider webz", "block.minecraft.cocoa": "dont feed dis to ur catz", "block.minecraft.command_block": "Cmnd Bloc", "block.minecraft.comparator": "Redstone thingy", "block.minecraft.composter": "Compostr", "block.minecraft.conduit": "strange box", "block.minecraft.copper_block": "Blok of copurr", "block.minecraft.copper_bulb": "Copurr Bob", "block.minecraft.copper_door": "Copurr <PERSON>", "block.minecraft.copper_grate": "Copurr wit Holz", "block.minecraft.copper_ore": "Copurr rok", "block.minecraft.copper_trapdoor": "Copurr Trepnoor", "block.minecraft.cornflower": "Unicornflowerpower", "block.minecraft.cracked_deepslate_bricks": "half brokn dark ston breekz", "block.minecraft.cracked_deepslate_tiles": "half brokn dark ston tilz", "block.minecraft.cracked_nether_bricks": "Craked nether brickz", "block.minecraft.cracked_polished_blackstone_bricks": "craked polised bleck briks", "block.minecraft.cracked_stone_bricks": "Stacked <PERSON>", "block.minecraft.crafter": "<PERSON><PERSON>", "block.minecraft.crafting_table": "Krafting Tabal", "block.minecraft.creaking_heart": "Silly glowing lawg", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON>d", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON> Wall Hed", "block.minecraft.crimson_button": "Crimzn Bawttn", "block.minecraft.crimson_door": "Krimzn Big Kat Door", "block.minecraft.crimson_fence": "Crimzn Fanse", "block.minecraft.crimson_fence_gate": "Crimzn Fenc Gaet", "block.minecraft.crimson_fungus": "Crimzn Foongis", "block.minecraft.crimson_hanging_sign": "Krimzn Danglin' Sign", "block.minecraft.crimson_hyphae": "Weerd red nethar plnat", "block.minecraft.crimson_nylium": "Crimzun Nyanium", "block.minecraft.crimson_planks": "Crimsun planmks", "block.minecraft.crimson_pressure_plate": "<PERSON> p<PERSON><PERSON>", "block.minecraft.crimson_roots": "Crimsun roots", "block.minecraft.crimson_sign": "Krimzn Syne", "block.minecraft.crimson_slab": "Crimzn Sleb", "block.minecraft.crimson_stairs": "Crimzn Staez", "block.minecraft.crimson_stem": "Crimzn stim", "block.minecraft.crimson_trapdoor": "Crimzn Kat Door", "block.minecraft.crimson_wall_hanging_sign": "Krimzn Danglin' Sign On Da Wall", "block.minecraft.crimson_wall_sign": "Red walz sign", "block.minecraft.crying_obsidian": "sad hardest thing evar", "block.minecraft.cut_copper": "1000° kniv vs cuprr blok", "block.minecraft.cut_copper_slab": "Slised copurr sleb", "block.minecraft.cut_copper_stairs": "Slised copurr sters", "block.minecraft.cut_red_sandstone": "Warmy red sanstown", "block.minecraft.cut_red_sandstone_slab": "Cut warmy sansdstoen sleb", "block.minecraft.cut_sandstone": "Warmy sanstown", "block.minecraft.cut_sandstone_slab": "Cut sansdstoen sleb", "block.minecraft.cyan_banner": "nyan bahnor", "block.minecraft.cyan_bed": "<PERSON><PERSON>", "block.minecraft.cyan_candle": "C-an land pikl", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON><PERSON> wif <PERSON>", "block.minecraft.cyan_carpet": "Sighun Cat Rug", "block.minecraft.cyan_concrete": "<PERSON>yan tough bluk", "block.minecraft.cyan_concrete_powder": "Syan tough bluk powdr", "block.minecraft.cyan_glazed_terracotta": "Syan mosaic", "block.minecraft.cyan_shulker_box": "<PERSON> <PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> Stainely Glazz", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON> Fu<PERSON>", "block.minecraft.damaged_anvil": "Oh so much braken anvehl", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.dark_oak_door": "<PERSON><PERSON>", "block.minecraft.dark_oak_fence": "Drk Oak Fence", "block.minecraft.dark_oak_fence_gate": "DARK GAIT", "block.minecraft.dark_oak_hanging_sign": "Dark Ook Danglin' Sign", "block.minecraft.dark_oak_leaves": "derk ok lefs", "block.minecraft.dark_oak_log": "darkz oak lawg", "block.minecraft.dark_oak_planks": "Blac Oak blocz", "block.minecraft.dark_oak_pressure_plate": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sapling": "baby blak oak", "block.minecraft.dark_oak_sign": "Dak <PERSON> Sign", "block.minecraft.dark_oak_slab": "<PERSON><PERSON>", "block.minecraft.dark_oak_stairs": "Dakwoak Stairz", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "Dark Ook Danglin' Sign On Da Wall", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON> Sign on ur wall", "block.minecraft.dark_oak_wood": "<PERSON><PERSON>", "block.minecraft.dark_prismarine": "Dark Prizmarine", "block.minecraft.dark_prismarine_slab": "Dark Prizmarine Sleb", "block.minecraft.dark_prismarine_stairs": "Dark Prizmarine Stairz", "block.minecraft.daylight_detector": "light eater", "block.minecraft.dead_brain_coral": "Ded brein koral", "block.minecraft.dead_brain_coral_block": "Ded brain koral cube", "block.minecraft.dead_brain_coral_fan": "Ded brein koral fen", "block.minecraft.dead_brain_coral_wall_fan": "Ded brein koral wull fan", "block.minecraft.dead_bubble_coral": "Ded bubol koral", "block.minecraft.dead_bubble_coral_block": "Ded bubol koral cube", "block.minecraft.dead_bubble_coral_fan": "Ded bubol koral fen", "block.minecraft.dead_bubble_coral_wall_fan": "Ded bable korall wull fan", "block.minecraft.dead_bush": "Died bushez", "block.minecraft.dead_fire_coral": "Ded HOT koral", "block.minecraft.dead_fire_coral_block": "Ded HOT koral cube", "block.minecraft.dead_fire_coral_fan": "Ded HOT koral fen", "block.minecraft.dead_fire_coral_wall_fan": "Ded hot korall wull fan", "block.minecraft.dead_horn_coral": "<PERSON>d jorn koral", "block.minecraft.dead_horn_coral_block": "Ded jorn koral cube", "block.minecraft.dead_horn_coral_fan": "Ded jorn koral fen", "block.minecraft.dead_horn_coral_wall_fan": "Ded hurn korall wull fan", "block.minecraft.dead_tube_coral": "Ded tuf koral", "block.minecraft.dead_tube_coral_block": "Ded tuf koral cube", "block.minecraft.dead_tube_coral_fan": "Ded tuf koral fen", "block.minecraft.dead_tube_coral_wall_fan": "Ded tub koral wull fan", "block.minecraft.decorated_pot": "Ancient containr", "block.minecraft.deepslate": "dark ston", "block.minecraft.deepslate_brick_slab": "dark ston brik sleb", "block.minecraft.deepslate_brick_stairs": "dark ston brik stairz", "block.minecraft.deepslate_brick_wall": "dark ston brik wal", "block.minecraft.deepslate_bricks": "dark ston brikz", "block.minecraft.deepslate_coal_ore": "dark ston koal or", "block.minecraft.deepslate_copper_ore": "dark ston coppur or", "block.minecraft.deepslate_diamond_ore": "dark ston deemond or", "block.minecraft.deepslate_emerald_ore": "dark ston green shiny or", "block.minecraft.deepslate_gold_ore": "dark ston gold or", "block.minecraft.deepslate_iron_ore": "dark ston irony or", "block.minecraft.deepslate_lapis_ore": "dark ston bloo or", "block.minecraft.deepslate_redstone_ore": "dark ston Redstone or", "block.minecraft.deepslate_tile_slab": "dark ston til sleb", "block.minecraft.deepslate_tile_stairs": "dark ston til stairz", "block.minecraft.deepslate_tile_wall": "dark ston til wal", "block.minecraft.deepslate_tiles": "dark ston tilez", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "MUCH MUNY $$$", "block.minecraft.diamond_ore": "Rockz wuth Dimundz", "block.minecraft.diorite": "kitteh litter rockz", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt": "Durt", "block.minecraft.dirt_path": "Durt roud", "block.minecraft.dispenser": "Dizpnzur", "block.minecraft.dragon_egg": "Dwagon Ec", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Dragon Wall Hed", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Smoky lettuce cube", "block.minecraft.dripstone_block": "bootleg netherite", "block.minecraft.dropper": "DRUPPER", "block.minecraft.emerald_block": "Shineh bloock of Green stuff", "block.minecraft.emerald_ore": "Rockz wuth Emmiez", "block.minecraft.enchanting_table": "<PERSON><PERSON>", "block.minecraft.end_gateway": "Megeek purrtul", "block.minecraft.end_portal": "Magik yarn bawl howl", "block.minecraft.end_portal_frame": "Magik portl fraem", "block.minecraft.end_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone": "Weird white rock", "block.minecraft.end_stone_brick_slab": "<PERSON><PERSON> of za End", "block.minecraft.end_stone_brick_stairs": "Ston Stairz of za End", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> of za End", "block.minecraft.end_stone_bricks": "<PERSON>", "block.minecraft.ender_chest": "Magic Cat Box", "block.minecraft.exposed_chiseled_copper": "Seen <PERSON>h Copur", "block.minecraft.exposed_copper": "Seen copurr", "block.minecraft.exposed_copper_bulb": "Seen <PERSON>", "block.minecraft.exposed_copper_door": "Seen <PERSON>pur Noor", "block.minecraft.exposed_copper_grate": "Seen Copur Holz", "block.minecraft.exposed_copper_trapdoor": "Seen Copur Trepnoor", "block.minecraft.exposed_cut_copper": "Seen sliced coppur", "block.minecraft.exposed_cut_copper_slab": "Seen Sliced Copurr Half Block", "block.minecraft.exposed_cut_copper_stairs": "Seen Sliced Copurr sters", "block.minecraft.farmland": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fern": "furn", "block.minecraft.fire": "fireh", "block.minecraft.fire_coral": "HOT koral", "block.minecraft.fire_coral_block": "HOT coral cube", "block.minecraft.fire_coral_fan": "HOT koral fen", "block.minecraft.fire_coral_wall_fan": "Hot koral wull fan", "block.minecraft.firefly_bush": "U would not bliev ur eyes bush", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "container for all the pretty plantz", "block.minecraft.flowering_azalea": "Tertl-shel-laik tree but in FLOWARZZ", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON> azalee leevez", "block.minecraft.frogspawn": "toad ec", "block.minecraft.frosted_ice": "Thatz kowld!", "block.minecraft.furnace": "warm box", "block.minecraft.gilded_blackstone": "Some goldid bleckston", "block.minecraft.glass": "Glazz", "block.minecraft.glass_pane": "Glazz Payn", "block.minecraft.glow_lichen": "shiny moss", "block.minecraft.glowstone": "Glowstone", "block.minecraft.gold_block": "Shiny blok", "block.minecraft.gold_ore": "Rockz wif Guld", "block.minecraft.granite": "Red Rock", "block.minecraft.granite_slab": "Red Rock Sleb", "block.minecraft.granite_stairs": "Red Rock Stairz", "block.minecraft.granite_wall": "Red Rock Wal", "block.minecraft.grass": "Grazz", "block.minecraft.grass_block": "Gras blak", "block.minecraft.gravel": "fishy roks", "block.minecraft.gray_banner": "grai bahnor", "block.minecraft.gray_bed": "Greyh Bed", "block.minecraft.gray_candle": "Gra hot stik", "block.minecraft.gray_candle_cake": "Caek wif Gra Land pikl", "block.minecraft.gray_carpet": "Gray Cat Rug", "block.minecraft.gray_concrete": "<PERSON> tough bluk", "block.minecraft.gray_concrete_powder": "Grey tough bluk powdr", "block.minecraft.gray_glazed_terracotta": "Grey mosaic", "block.minecraft.gray_shulker_box": "<PERSON>ish <PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON>ly Stainedly Glazz", "block.minecraft.gray_stained_glass_pane": "<PERSON> <PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON>", "block.minecraft.gray_wool": "Gray Fur Bluk", "block.minecraft.green_banner": "grene bahnor", "block.minecraft.green_bed": "<PERSON><PERSON>", "block.minecraft.green_candle": "Gren hot stik", "block.minecraft.green_candle_cake": "Caek wif G<PERSON>", "block.minecraft.green_carpet": "Greenish Cat Rug", "block.minecraft.green_concrete": "<PERSON><PERSON> tough bluk", "block.minecraft.green_concrete_powder": "<PERSON><PERSON> tough bluk powdr", "block.minecraft.green_glazed_terracotta": "Gren mosaic", "block.minecraft.green_shulker_box": "Greenish <PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "G<PERSON><PERSON> Stainedly Glazz", "block.minecraft.green_stained_glass_pane": "<PERSON> Stan<PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON>", "block.minecraft.green_wool": "Greenish Fur Bluk", "block.minecraft.grindstone": "Removezstone", "block.minecraft.hanging_roots": "hangin stickz", "block.minecraft.hay_block": "Stwaw bael", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "hvy weited presplat plet", "block.minecraft.honey_block": "<PERSON><PERSON>", "block.minecraft.honeycomb_block": "B thing bloc", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "<PERSON><PERSON> k<PERSON>", "block.minecraft.horn_coral_block": "Jorn koral cube", "block.minecraft.horn_coral_fan": "<PERSON>rn koral fen", "block.minecraft.horn_coral_wall_fan": "<PERSON>rn koral wull fan", "block.minecraft.ice": "Frozen water", "block.minecraft.infested_chiseled_stone_bricks": "Kewl Rockz Wit Monsters", "block.minecraft.infested_cobblestone": "Invezzted Cooblestoon", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON><PERSON>z Wit Monsters", "block.minecraft.infested_deepslate": "dark ston wit weird thing inside idk", "block.minecraft.infested_mossy_stone_bricks": "Rockz Wit Mozs And Monsters", "block.minecraft.infested_stone": "Invezzted Stoon ", "block.minecraft.infested_stone_bricks": "Rockz Wit Patternz And Monsters", "block.minecraft.iron_bars": "<PERSON><PERSON>", "block.minecraft.iron_block": "Blok of Irony", "block.minecraft.iron_door": "Iron Dor", "block.minecraft.iron_ore": "<PERSON><PERSON> wuth Irun", "block.minecraft.iron_trapdoor": "Trappy <PERSON><PERSON>", "block.minecraft.jack_o_lantern": "Big glowy squash", "block.minecraft.jigsaw": "<PERSON><PERSON> saw <PERSON><PERSON><PERSON>", "block.minecraft.jukebox": "Catbox", "block.minecraft.jungle_button": "<PERSON><PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON>", "block.minecraft.jungle_fence": "<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Janggal Fence Gate", "block.minecraft.jungle_hanging_sign": "Jung<PERSON>z Danglin' Sign", "block.minecraft.jungle_leaves": "jungel salad", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON> wodden lawg", "block.minecraft.jungle_planks": "<PERSON><PERSON><PERSON> wodden bloc", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON>", "block.minecraft.jungle_sapling": "baby jungel", "block.minecraft.jungle_sign": "Junglz Sign", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON> T<PERSON>", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON>z Danglin' Sign On Da Wall", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON>z Sign on ur wall", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "Yucky lettuce", "block.minecraft.kelp_plant": "Yucky lettuce plant", "block.minecraft.ladder": "Ladr", "block.minecraft.lantern": "Lanturn", "block.minecraft.lapis_block": "Glosy Blu Bluk", "block.minecraft.lapis_ore": "P<PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Big purpur shinee", "block.minecraft.large_fern": "Larg furn", "block.minecraft.lava": "hot sauce", "block.minecraft.lava_cauldron": "big bukkit wif hot sauec", "block.minecraft.leaf_litter": "<PERSON><PERSON>", "block.minecraft.lectern": "Book readin ting", "block.minecraft.lever": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON>'", "block.minecraft.light_blue_banner": "ligt bloo bahnor", "block.minecraft.light_blue_bed": "Lite <PERSON>", "block.minecraft.light_blue_candle": "Lite blu land pikl", "block.minecraft.light_blue_candle_cake": "Caek wif Lite blu Land pikl", "block.minecraft.light_blue_carpet": "Lite Blu Cat Rug", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> tough bluk", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON> tough bluk powdr", "block.minecraft.light_blue_glazed_terracotta": "Layte Bluu mosaic", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON>lk<PERSON>", "block.minecraft.light_blue_stained_glass": "Lit Bloo Stan<PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Lit Bloo Staned <PERSON>", "block.minecraft.light_blue_terracotta": "Lite <PERSON>", "block.minecraft.light_blue_wool": "Lite Bloo Fur Bluk", "block.minecraft.light_gray_banner": "ligt grai bahnor", "block.minecraft.light_gray_bed": "<PERSON><PERSON>", "block.minecraft.light_gray_candle": "Lite gra hot stik", "block.minecraft.light_gray_candle_cake": "Caek wif Lite Grai Candl", "block.minecraft.light_gray_carpet": "<PERSON><PERSON> Cat Rug", "block.minecraft.light_gray_concrete": "<PERSON><PERSON> tough bluk", "block.minecraft.light_gray_concrete_powder": "Layte grey tough bluk powdr", "block.minecraft.light_gray_glazed_terracotta": "Layte Grey mosaic", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "Lightly <PERSON>en Stainedly Grazz", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON> <PERSON>", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "liht weightefd prueusure platt", "block.minecraft.lightning_rod": "Litnin rot", "block.minecraft.lilac": "tall purpl flowr", "block.minecraft.lily_of_the_valley": "<PERSON> of da Vallyz", "block.minecraft.lily_pad": "big watr leef", "block.minecraft.lime_banner": "liem bahnor", "block.minecraft.lime_bed": "Limeh Bed", "block.minecraft.lime_candle": "Liem hot stik", "block.minecraft.lime_candle_cake": "Caek wif Liem Land pickl", "block.minecraft.lime_carpet": "Limed Cat Rug", "block.minecraft.lime_concrete": "<PERSON><PERSON> tough bluk", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON> tough bluk powdr", "block.minecraft.lime_glazed_terracotta": "Layme mosaic", "block.minecraft.lime_shulker_box": "<PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON>", "block.minecraft.lime_stained_glass_pane": "<PERSON>", "block.minecraft.lime_terracotta": "<PERSON><PERSON>", "block.minecraft.lime_wool": "Limd Fur Bluk", "block.minecraft.lodestone": "Loserstone", "block.minecraft.loom": "Lööm", "block.minecraft.magenta_banner": "me<PERSON><PERSON> bahnor", "block.minecraft.magenta_bed": "Majenta Bed", "block.minecraft.magenta_candle": "Majentra land pikl", "block.minecraft.magenta_candle_cake": "Caek wif Majentra Land pikl", "block.minecraft.magenta_carpet": "<PERSON>enta Cat Rug", "block.minecraft.magenta_concrete": "<PERSON><PERSON>a tough bluk", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON>ah tough bluk powdr", "block.minecraft.magenta_glazed_terracotta": "Majentah mosaic", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON> Stainedlyd Glazz", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.magma_block": "Hot Bluk", "block.minecraft.mangrove_button": "mengruv buton", "block.minecraft.mangrove_door": "Mangroff door", "block.minecraft.mangrove_fence": "mengruv fens", "block.minecraft.mangrove_fence_gate": "mengruv fens dor", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON>' <PERSON>", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON> salud", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON> lawg", "block.minecraft.mangrove_planks": "Mangroff PlAnKz", "block.minecraft.mangrove_pressure_plate": "Mengruv pwesuwe pleite", "block.minecraft.mangrove_propagule": "baby mangroff", "block.minecraft.mangrove_roots": "Mangroff roots", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON> sihg", "block.minecraft.mangrove_slab": "Mengroff Half Block", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> steppie", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON> Trap", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON>' Sign On Da Wall", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON><PERSON> wal sin", "block.minecraft.mangrove_wood": "Mangroff wood", "block.minecraft.medium_amethyst_bud": "<PERSON>l purpur shinee", "block.minecraft.melon": "mlon", "block.minecraft.melon_stem": "melon stik", "block.minecraft.moss_block": "squishy gras blok", "block.minecraft.moss_carpet": "squeeshd grazz", "block.minecraft.mossy_cobblestone": "DURTY COBULSTOWN", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON> Sleb", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON>ston Stairz", "block.minecraft.mossy_cobblestone_wall": "DURTY COBULSTOWN WALL", "block.minecraft.mossy_stone_brick_slab": "Mossy Ston Brick Sleb", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON> Ston B<PERSON>", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> B<PERSON>", "block.minecraft.mossy_stone_bricks": "Stacked Greenish Rockz", "block.minecraft.moving_piston": "dis piston is movin", "block.minecraft.mud": "dirty water", "block.minecraft.mud_brick_slab": "dirty water brik sleb", "block.minecraft.mud_brick_stairs": "dirty water steppie", "block.minecraft.mud_brick_wall": "dirty water brik wal", "block.minecraft.mud_bricks": "dirty water briks", "block.minecraft.muddy_mangrove_roots": "Mengroff dirt roots", "block.minecraft.mushroom_stem": "Mushroom Stem", "block.minecraft.mycelium": "miceliwm", "block.minecraft.nether_brick_fence": "Nether brik fens", "block.minecraft.nether_brick_slab": "<PERSON>her brik sleb", "block.minecraft.nether_brick_stairs": "<PERSON><PERSON> B<PERSON>", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> brik <PERSON>", "block.minecraft.nether_bricks": "<PERSON><PERSON> from Nether", "block.minecraft.nether_gold_ore": "Netherockz wif Guld", "block.minecraft.nether_portal": "Nether purtal", "block.minecraft.nether_quartz_ore": "Nether Kworts Ore", "block.minecraft.nether_sprouts": "<PERSON><PERSON>", "block.minecraft.nether_wart": "Icky Nether plant", "block.minecraft.nether_wart_block": "Nether warz bluk", "block.minecraft.netherite_block": "Block of Netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "nowht blohk", "block.minecraft.oak_button": "Oak Button", "block.minecraft.oak_door": "Oak Dor", "block.minecraft.oak_fence": "oke fenz", "block.minecraft.oak_fence_gate": "Oke Wod fence Gate", "block.minecraft.oak_hanging_sign": "Ook Danglin' Sign", "block.minecraft.oak_leaves": "oak salad", "block.minecraft.oak_log": "<PERSON> lawg", "block.minecraft.oak_planks": "oak bloc", "block.minecraft.oak_pressure_plate": "<PERSON> Prse<PERSON>", "block.minecraft.oak_sapling": "baby oak", "block.minecraft.oak_sign": "Ook Sign", "block.minecraft.oak_slab": "<PERSON>", "block.minecraft.oak_stairs": "WOAKOAK Stairz", "block.minecraft.oak_trapdoor": "Oak Trap", "block.minecraft.oak_wall_hanging_sign": "Ook Danglin' Sign On Da Wall", "block.minecraft.oak_wall_sign": "Ook Sign on ur wall", "block.minecraft.oak_wood": "<PERSON>", "block.minecraft.observer": "CREEPY StaLKeR DuDE", "block.minecraft.obsidian": "HARDEST THING EVAR", "block.minecraft.ochre_froglight": "<PERSON><PERSON>", "block.minecraft.ominous_banner": "skeri bahnor", "block.minecraft.open_eyeblossom": "Wakey eye flowah", "block.minecraft.orange_banner": "oreng bahnor", "block.minecraft.orange_bed": "Orang Bed", "block.minecraft.orange_candle": "Ornge land pikl", "block.minecraft.orange_candle_cake": "Caek wif Ornge Land pikl", "block.minecraft.orange_carpet": "Ornge Cat Rug", "block.minecraft.orange_concrete": "Orang tough bluk", "block.minecraft.orange_concrete_powder": "Orang tough bluk powdr", "block.minecraft.orange_glazed_terracotta": "Orang mosaic", "block.minecraft.orange_shulker_box": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_terracotta": "Orang Teracottah", "block.minecraft.orange_tulip": "Ornge tulehp", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.oxeye_daisy": "big wite flowr", "block.minecraft.oxidized_chiseled_copper": "Very-old <PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper": "Very-old copurr", "block.minecraft.oxidized_copper_bulb": "Very-old <PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_door": "Very-old <PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "Very-old <PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Very-old Copur Trepnoor", "block.minecraft.oxidized_cut_copper": "Very-old sliced copurr", "block.minecraft.oxidized_cut_copper_slab": "Very-old <PERSON><PERSON><PERSON> sleb", "block.minecraft.oxidized_cut_copper_stairs": "Very-old Copurr sters", "block.minecraft.packed_ice": "Pack'd Frozen Water", "block.minecraft.packed_mud": "dirty water compact", "block.minecraft.pale_hanging_moss": "Whity falling greeny", "block.minecraft.pale_moss_block": "Wite noodles in a box", "block.minecraft.pale_moss_carpet": "Wite noodles spilld on flor", "block.minecraft.pale_oak_button": "Wite Ok Button", "block.minecraft.pale_oak_door": "Wite <PERSON> Dor", "block.minecraft.pale_oak_fence": "Wite Oak Fens", "block.minecraft.pale_oak_fence_gate": "Wite <PERSON>", "block.minecraft.pale_oak_hanging_sign": "Wite Ook Danglin' Sign", "block.minecraft.pale_oak_leaves": "Wite Ok salad", "block.minecraft.pale_oak_log": "Wite Ok Lawwg", "block.minecraft.pale_oak_planks": "Wite ook Planx", "block.minecraft.pale_oak_pressure_plate": "<PERSON>ite <PERSON><PERSON> P<PERSON>", "block.minecraft.pale_oak_sapling": "Smol Wite Ook", "block.minecraft.pale_oak_sign": "Wite Ok Sign", "block.minecraft.pale_oak_slab": "Wite ook Sleb", "block.minecraft.pale_oak_stairs": "Wite Oke Steirz", "block.minecraft.pale_oak_trapdoor": "Wite Ok Trepdor", "block.minecraft.pale_oak_wall_hanging_sign": "Wite Ook danglin' Sign On da Wal", "block.minecraft.pale_oak_wall_sign": "Wite Ok sign on ur wal", "block.minecraft.pale_oak_wood": "Wite ook <PERSON>d", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "pony plant", "block.minecraft.petrified_oak_slab": "Petrifid Oak Sleb", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON>", "block.minecraft.pink_banner": "<PERSON>k bahnor", "block.minecraft.pink_bed": "Pinky Bed", "block.minecraft.pink_candle": "Pinc land pikl", "block.minecraft.pink_candle_cake": "Caek wif Pinc Land pikl", "block.minecraft.pink_carpet": "Pinky Cat Rug", "block.minecraft.pink_concrete": "Pinkyyy tough bluk", "block.minecraft.pink_concrete_powder": "Pinkyyy tough bluk powdr", "block.minecraft.pink_glazed_terracotta": "Pinkyyy mosaic", "block.minecraft.pink_petals": "<PERSON>y <PERSON>z", "block.minecraft.pink_shulker_box": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON>", "block.minecraft.pink_tulip": "Pink tulehp", "block.minecraft.pink_wool": "<PERSON><PERSON> Fur <PERSON>", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "pushy-uppy face", "block.minecraft.pitcher_crop": "Bottl Krop", "block.minecraft.pitcher_plant": "Bottl Plant", "block.minecraft.player_head": "<PERSON>", "block.minecraft.player_head.named": "%s's Hed", "block.minecraft.player_wall_head": "<PERSON>", "block.minecraft.podzol": "Du<PERSON>e durt", "block.minecraft.pointed_dripstone": "Sharp cave rok", "block.minecraft.polished_andesite": "pretteh grey rock", "block.minecraft.polished_andesite_slab": "Pretteh Grey Rock Sleb", "block.minecraft.polished_andesite_stairs": "Pretteh Grey Rock Stairz", "block.minecraft.polished_basalt": "cleened baisolt", "block.minecraft.polished_blackstone": "polised bleckston", "block.minecraft.polished_blackstone_brick_slab": "shiny blak rok brik slab", "block.minecraft.polished_blackstone_brick_stairs": "shiny blak rok brik sters", "block.minecraft.polished_blackstone_brick_wall": "polised bleck brik wal", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON> bleck rockz stackd", "block.minecraft.polished_blackstone_button": "shiny blak rok buton", "block.minecraft.polished_blackstone_pressure_plate": "shiny blak rok step button", "block.minecraft.polished_blackstone_slab": "shiny blak rok sleb", "block.minecraft.polished_blackstone_stairs": "shiny blak rok sters", "block.minecraft.polished_blackstone_wall": "shiny blak rok wal", "block.minecraft.polished_deepslate": "shiny dark ston", "block.minecraft.polished_deepslate_slab": "shiny dark ston sleb", "block.minecraft.polished_deepslate_stairs": "shiny dark ston stairz", "block.minecraft.polished_deepslate_wall": "shiny dark ston wal", "block.minecraft.polished_diorite": "pretteh kitteh litter rockz", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_stairs": "<PERSON>tteh Diorito <PERSON>", "block.minecraft.polished_granite": "pretteh red rock", "block.minecraft.polished_granite_slab": "Pretteh Red Rock Sleb", "block.minecraft.polished_granite_stairs": "Pretteh Red Rock Stairz", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON> Tff Starz", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON>", "block.minecraft.poppy": "poopy", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Pottd acazia saplin", "block.minecraft.potted_allium": "Pottd alium", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_azure_bluet": "Pottd azur blut", "block.minecraft.potted_bamboo": "Pottd green stick", "block.minecraft.potted_birch_sapling": "Pottd birsh saplin", "block.minecraft.potted_blue_orchid": "Pottd bluu orkid", "block.minecraft.potted_brown_mushroom": "Pottd bron shroom", "block.minecraft.potted_cactus": "Pottd cacus", "block.minecraft.potted_cherry_sapling": "Pottd sakura saplin", "block.minecraft.potted_closed_eyeblossom": "Pottd sleepy eye flowah", "block.minecraft.potted_cornflower": "Pottd Cornflowerpower", "block.minecraft.potted_crimson_fungus": "<PERSON>ttd Crimzin <PERSON>", "block.minecraft.potted_crimson_roots": "Pottd Crimzn Rutez", "block.minecraft.potted_dandelion": "<PERSON><PERSON><PERSON> danks<PERSON>", "block.minecraft.potted_dark_oak_sapling": "Pottd darkok saplin", "block.minecraft.potted_dead_bush": "<PERSON><PERSON><PERSON> ded bujsh", "block.minecraft.potted_fern": "Pottd ferrn", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_jungle_sapling": "<PERSON>ttd junle saplin", "block.minecraft.potted_lily_of_the_valley": "Pottd Lily of za Valley", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON><PERSON> baby inpot", "block.minecraft.potted_oak_sapling": "Pottd ok saplin", "block.minecraft.potted_open_eyeblossom": "Pottd wakey eye flowah", "block.minecraft.potted_orange_tulip": "Pottd orang tlip", "block.minecraft.potted_oxeye_daisy": "Pottd oxyey daisye", "block.minecraft.potted_pale_oak_sapling": "Pottd smol wite ook", "block.minecraft.potted_pink_tulip": "Pottd pikk tlip", "block.minecraft.potted_poppy": "Pottd popi", "block.minecraft.potted_red_mushroom": "Pottd red shroom", "block.minecraft.potted_red_tulip": "Pottd red tlip", "block.minecraft.potted_spruce_sapling": "Pottd sprus saplin", "block.minecraft.potted_torchflower": "Pottd burny flowr", "block.minecraft.potted_warped_fungus": "Pottd Warpt Foongis", "block.minecraft.potted_warped_roots": "Pottd Warpd Rootz", "block.minecraft.potted_white_tulip": "Pottd wit tlip", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Big bukkit wit Powdr Sno", "block.minecraft.powered_rail": "PUWERD RAIL", "block.minecraft.prismarine": "Prizmarine", "block.minecraft.prismarine_brick_slab": "Prizmarine Bric slap", "block.minecraft.prismarine_brick_stairs": "Prizmarin bric strs", "block.minecraft.prismarine_bricks": "Prizmarine Briks", "block.minecraft.prismarine_slab": "Prizmarine Sleb", "block.minecraft.prismarine_stairs": "Prizmarine stepz", "block.minecraft.prismarine_wall": "Prizmarine Wal", "block.minecraft.pumpkin": "Pangkan", "block.minecraft.pumpkin_stem": "Dat thing teh orange scary thing grows on", "block.minecraft.purple_banner": "purrrpel bahnor", "block.minecraft.purple_bed": "Parpal Bed", "block.minecraft.purple_candle": "Parpal hot stik", "block.minecraft.purple_candle_cake": "Caek wif Purpl Candl", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON> Cat Rug", "block.minecraft.purple_concrete": "Perpl tough bluk", "block.minecraft.purple_concrete_powder": "Perpl tough bluk powdr", "block.minecraft.purple_glazed_terracotta": "Perpl mosaic", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Purply Stainedly Glazz", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON><PERSON> Fur Blu<PERSON>", "block.minecraft.purpur_block": "OOO PURPUR Bluk", "block.minecraft.purpur_pillar": "OOO PURPUR Bluk w/ Stripez", "block.minecraft.purpur_slab": "OOO PURPUR Bluk Cut In Half", "block.minecraft.purpur_stairs": "OOO PURPUR Bluk U Can Climb", "block.minecraft.quartz_block": "Blak Av Kworts", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON>z", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON> piler", "block.minecraft.quartz_slab": "White half blok", "block.minecraft.quartz_stairs": "Kworts Sters", "block.minecraft.rail": "Trakz", "block.minecraft.raw_copper_block": "Bluk ov moldy wurst", "block.minecraft.raw_gold_block": "Bluk ov shiny coal", "block.minecraft.raw_iron_block": "Bluk ov RAWWWW irun", "block.minecraft.red_banner": "Red bahnor", "block.minecraft.red_bed": "Reddish Bed", "block.minecraft.red_candle": "Red land pikl", "block.minecraft.red_candle_cake": "Caek wif Rd Candl", "block.minecraft.red_carpet": "Redish Cat Rug", "block.minecraft.red_concrete": "Redish tough bluk", "block.minecraft.red_concrete_powder": "Redish tough bluk powdr", "block.minecraft.red_glazed_terracotta": "Redish mosaic", "block.minecraft.red_mushroom": "blood Mooshroom", "block.minecraft.red_mushroom_block": "blood Mushroom blok", "block.minecraft.red_nether_brick_slab": "Red Sleb that Nether bricks", "block.minecraft.red_nether_brick_stairs": "Red Nether Brik Sters", "block.minecraft.red_nether_brick_wall": "<PERSON> Nether brik <PERSON>al", "block.minecraft.red_nether_bricks": "<PERSON>d <PERSON> brik", "block.minecraft.red_sand": "Red sand", "block.minecraft.red_sandstone": "Warmy sanstown", "block.minecraft.red_sandstone_slab": "Rds litterbox slab", "block.minecraft.red_sandstone_stairs": "Rds litterbox sters", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> Wal", "block.minecraft.red_shulker_box": "Redish <PERSON>lk<PERSON>", "block.minecraft.red_stained_glass": "Read Stainedly Glazz", "block.minecraft.red_stained_glass_pane": "rud thin culurd thingy", "block.minecraft.red_terracotta": "Reddish Teracottah", "block.minecraft.red_tulip": "Red tulehp", "block.minecraft.red_wool": "WLR3D Fur Bluk", "block.minecraft.redstone_block": "Redstone Bluk", "block.minecraft.redstone_lamp": "Redstone lapm", "block.minecraft.redstone_ore": "Rockz wif Redstone", "block.minecraft.redstone_torch": "Glowy Redstone Stik", "block.minecraft.redstone_wall_torch": "Glowy Redstone Wall Stik", "block.minecraft.redstone_wire": "Redstone Whyr", "block.minecraft.reinforced_deepslate": "dark ston wit weird thing outside idk", "block.minecraft.repeater": "Redstone Repeetah", "block.minecraft.repeating_command_block": "Again Doing Block", "block.minecraft.resin_block": "<PERSON><PERSON> stiky blok", "block.minecraft.resin_brick_slab": "<PERSON><PERSON> stiky brik slab", "block.minecraft.resin_brick_stairs": "<PERSON><PERSON> stiky stairz", "block.minecraft.resin_brick_wall": "<PERSON>lo stiky brik wal", "block.minecraft.resin_bricks": "<PERSON>lo stiky brix", "block.minecraft.resin_clump": "<PERSON><PERSON> poop", "block.minecraft.respawn_anchor": "Reezpon mawgic blok", "block.minecraft.rooted_dirt": "durt wit stickz", "block.minecraft.rose_bush": "<PERSON> brah", "block.minecraft.sand": "litter box stuffs", "block.minecraft.sandstone": "litter box rockz", "block.minecraft.sandstone_slab": "sansdstoen sleb", "block.minecraft.sandstone_stairs": "Sanston Sters", "block.minecraft.sandstone_wall": "Sandston Wal", "block.minecraft.scaffolding": "Sceffolding", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk Cat-alist", "block.minecraft.sculk_sensor": "Sculk detektor", "block.minecraft.sculk_shrieker": "<PERSON><PERSON>k yeller", "block.minecraft.sculk_vein": "Smol Sculk", "block.minecraft.sea_lantern": "See lanturn", "block.minecraft.sea_pickle": "See pikol", "block.minecraft.seagrass": "MARIN GRAZZ", "block.minecraft.set_spawn": "hear is new kat home!!", "block.minecraft.short_dry_grass": "Shurt Dry Graz", "block.minecraft.short_grass": "Smol Grazz", "block.minecraft.shroomlight": "Shr<PERSON>y Lite", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Skellyton hed", "block.minecraft.skeleton_wall_skull": "Spooke <PERSON> Wall Hed", "block.minecraft.slime_block": "bouncy green stuff", "block.minecraft.small_amethyst_bud": "Smol purpur shinee", "block.minecraft.small_dripleaf": "Smol fall thru plantt", "block.minecraft.smithing_table": "Smissing Table", "block.minecraft.smoker": "<PERSON><PERSON>", "block.minecraft.smooth_basalt": "smoos bazalt", "block.minecraft.smooth_quartz": "Smooth Wite Kat <PERSON>law<PERSON>", "block.minecraft.smooth_quartz_slab": "Smooth Qwartz Sleb", "block.minecraft.smooth_quartz_stairs": "Smooth Qwartz Stairz", "block.minecraft.smooth_red_sandstone": "Smud warmy sanstown", "block.minecraft.smooth_red_sandstone_slab": "Smooth <PERSON>y <PERSON> Sleb", "block.minecraft.smooth_red_sandstone_stairs": "Smooth Redy Sandston Stairz", "block.minecraft.smooth_sandstone": "smooth litter box rockz", "block.minecraft.smooth_sandstone_slab": "Smooth Sandston Sleb", "block.minecraft.smooth_sandstone_stairs": "Smooth Sandston Stairz", "block.minecraft.smooth_stone": "Smooth Rockz", "block.minecraft.smooth_stone_slab": "Smooth Ston Sleb", "block.minecraft.sniffer_egg": "Sniffr ec", "block.minecraft.snow": "Cold white stuff", "block.minecraft.snow_block": "Snowi bloc", "block.minecraft.soul_campfire": "sul camfiri", "block.minecraft.soul_fire": "bloo hot stuff", "block.minecraft.soul_lantern": "Sol Lanturn", "block.minecraft.soul_sand": "sole snd", "block.minecraft.soul_soil": "ded peeple dirt", "block.minecraft.soul_torch": "Creepy torch thingy", "block.minecraft.soul_wall_torch": "Creepy torch thingy on de wal", "block.minecraft.spawn.not_valid": "U iz homelezz nao cuz ur hoem bed or charjd respwn anchr, or wuz obstructd", "block.minecraft.spawner": "bad kitteh maekr", "block.minecraft.spawner.desc1": "Pulay wiv spon ec:", "block.minecraft.spawner.desc2": "setz mahbstrr tyep", "block.minecraft.sponge": "Spangblob", "block.minecraft.spore_blossom": "snees flowar", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_door": "Spruz Dor", "block.minecraft.spruce_fence": "S<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>' Sign", "block.minecraft.spruce_leaves": "spruce salad", "block.minecraft.spruce_log": "Spruce lawg", "block.minecraft.spruce_planks": "dark wooden bloc", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.spruce_sapling": "baby spurce", "block.minecraft.spruce_sign": "Sproos Sign", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>' Sign On Da Wall", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON> Sign on ur wall", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "<PERSON><PERSON>", "block.minecraft.stone": "Rock", "block.minecraft.stone_brick_slab": "Ston Brick Sleb", "block.minecraft.stone_brick_stairs": "<PERSON> Brik <PERSON>", "block.minecraft.stone_brick_wall": "<PERSON><PERSON>", "block.minecraft.stone_bricks": "Stackd Rockz", "block.minecraft.stone_button": "Stoon Button", "block.minecraft.stone_pressure_plate": "Stone Presar Plat", "block.minecraft.stone_slab": "stne slab", "block.minecraft.stone_stairs": "Ston Stairz", "block.minecraft.stonecutter": "Shrpy movin thingy", "block.minecraft.stripped_acacia_log": "Naked <PERSON><PERSON><PERSON><PERSON> lawg", "block.minecraft.stripped_acacia_wood": "Naked Acashuh Bawk", "block.minecraft.stripped_bamboo_block": "bluk ov naked bamboo", "block.minecraft.stripped_birch_log": "Naked berch lawg", "block.minecraft.stripped_birch_wood": "Naked Berch Bawk", "block.minecraft.stripped_cherry_log": "Naked Sakura lawg", "block.minecraft.stripped_cherry_wood": "Naked Sakura Bawk", "block.minecraft.stripped_crimson_hyphae": "Da werd red nehtr plnta but neked", "block.minecraft.stripped_crimson_stem": "Nakd crimzon stem", "block.minecraft.stripped_dark_oak_log": "Naked shaded-<PERSON> <PERSON>g", "block.minecraft.stripped_dark_oak_wood": "Naked Blak Oac Bawk", "block.minecraft.stripped_jungle_log": "Naked juncle lawg", "block.minecraft.stripped_jungle_wood": "Naked Juggle Bawk", "block.minecraft.stripped_mangrove_log": "naked mangroff lawg", "block.minecraft.stripped_mangrove_wood": "naked mangroff wuud", "block.minecraft.stripped_oak_log": "Naked Oac lawg", "block.minecraft.stripped_oak_wood": "Naked Oac Bawk", "block.minecraft.stripped_pale_oak_log": "Naked Wite Ook <PERSON>g", "block.minecraft.stripped_pale_oak_wood": "Naked Wite <PERSON><PERSON>", "block.minecraft.stripped_spruce_log": "Naked spruice lawg", "block.minecraft.stripped_spruce_wood": "Naked Sproos Bawk", "block.minecraft.stripped_warped_hyphae": "Da werd nethr plnt butt nakie", "block.minecraft.stripped_warped_stem": "Naekt Warpt Stem", "block.minecraft.structure_block": "<PERSON><PERSON><PERSON> blek", "block.minecraft.structure_void": "Blank space", "block.minecraft.sugar_cane": "BAMBOOO", "block.minecraft.sunflower": "Rly tall flowr", "block.minecraft.suspicious_gravel": "Sussy grey thing", "block.minecraft.suspicious_sand": "Sussy litter box", "block.minecraft.sweet_berry_bush": "Sweet Bery Bush", "block.minecraft.tall_dry_grass": "Tawl Dry Graz", "block.minecraft.tall_grass": "Tall Gras", "block.minecraft.tall_seagrass": "TOLL MARIN GRAZZ", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terrakattah", "block.minecraft.test_block": "<PERSON><PERSON> blok", "block.minecraft.test_instance_block": "Tez instenz bluk", "block.minecraft.tinted_glass": "Skary glazz", "block.minecraft.tnt": "BOOM", "block.minecraft.tnt.disabled": "No boom 4 u :(", "block.minecraft.torch": "burny stick", "block.minecraft.torchflower": "burny flowr", "block.minecraft.torchflower_crop": "burny flowr crop", "block.minecraft.trapped_chest": "Rigged Cat Box", "block.minecraft.trial_spawner": "Dungenz Kitteh Mae<PERSON>r", "block.minecraft.tripwire": "String", "block.minecraft.tripwire_hook": "String huk", "block.minecraft.tube_coral": "<PERSON><PERSON>", "block.minecraft.tube_coral_block": "<PERSON>f koral cube", "block.minecraft.tube_coral_fan": "<PERSON><PERSON> koral fen", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON>", "block.minecraft.tuff": "Tfff", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.tuff_bricks": "<PERSON><PERSON>", "block.minecraft.tuff_slab": "Tff <PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON>", "block.minecraft.tuff_wall": "Tff Wal", "block.minecraft.turtle_egg": "Tertl ball", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON> noodel", "block.minecraft.twisting_vines_plant": "Spyrale noodel plont", "block.minecraft.vault": "Moneh box", "block.minecraft.verdant_froglight": "<PERSON><PERSON> toe<PERSON><PERSON>", "block.minecraft.vine": "Climbin plantz", "block.minecraft.void_air": "vOoId", "block.minecraft.wall_torch": "burny stick on de wall", "block.minecraft.warped_button": "<PERSON><PERSON> Thingy", "block.minecraft.warped_door": "Warpt Big Kat Door", "block.minecraft.warped_fence": "Warpt Fenc", "block.minecraft.warped_fence_gate": "Warpt Fenc <PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON> Danglin' Sign", "block.minecraft.warped_hyphae": "Wierd nethr plant", "block.minecraft.warped_nylium": "wrped nyanium", "block.minecraft.warped_planks": "Warpt Plaenkz", "block.minecraft.warped_pressure_plate": "Wapt Weight Plaet", "block.minecraft.warped_roots": "<PERSON><PERSON> Roods", "block.minecraft.warped_sign": "Warpt Texxt Bawrd", "block.minecraft.warped_slab": "<PERSON><PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> Katstairs", "block.minecraft.warped_stem": "Warpt trunkk", "block.minecraft.warped_trapdoor": "Warpt Kat Door", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON> Danglin' Sign On Da Wall", "block.minecraft.warped_wall_sign": "Warpt wol book", "block.minecraft.warped_wart_block": "Warpt Pimpl Blawk", "block.minecraft.water": "Watr", "block.minecraft.water_cauldron": "big bukkit wit watr", "block.minecraft.waxed_chiseled_copper": "Shiny Preteh Copur", "block.minecraft.waxed_copper_block": "Shiny Blok of copurr", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON>", "block.minecraft.waxed_copper_door": "<PERSON><PERSON>", "block.minecraft.waxed_copper_grate": "Shineh Copur Holz", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON>", "block.minecraft.waxed_cut_copper": "Waxd an sliecd copurr", "block.minecraft.waxed_cut_copper_slab": "Shiny slised copurr sleb", "block.minecraft.waxed_cut_copper_stairs": "Shineh slised copurr sters", "block.minecraft.waxed_exposed_chiseled_copper": "Shiny Seen Pretteh Copur", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON> Seen <PERSON>", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON> <PERSON>n <PERSON>", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON> Seen <PERSON>pur<PERSON>", "block.minecraft.waxed_exposed_copper_grate": "Shiny Seen Copur Holz", "block.minecraft.waxed_exposed_copper_trapdoor": "Shiny Seen Copurr Trepnoor", "block.minecraft.waxed_exposed_cut_copper": "Shiny Seen Sliced Copurr", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON>n Sliced Steal Half Block", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON>y Seen Sliced Steal sters", "block.minecraft.waxed_oxidized_chiseled_copper": "Shiny Old Pretteh Copur", "block.minecraft.waxed_oxidized_copper": "Shiny very-old copurr", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON>y Old Copurr-bob", "block.minecraft.waxed_oxidized_copper_door": "Shiny Old Copurr Noor", "block.minecraft.waxed_oxidized_copper_grate": "Shiny Old Copur holz", "block.minecraft.waxed_oxidized_copper_trapdoor": "Shiny Old Copurr Trepnoor", "block.minecraft.waxed_oxidized_cut_copper": "Shiny very-old sliced copurr", "block.minecraft.waxed_oxidized_cut_copper_slab": "Shiny very-old copurr sleb", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Shiny cery-old copurr sters", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON> Preteh Copur", "block.minecraft.waxed_weathered_copper": "Yucky old copurr", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON>pur Trepnoor", "block.minecraft.waxed_weathered_cut_copper": "Shiny Yucky old sliced copurr", "block.minecraft.waxed_weathered_cut_copper_slab": "Shiny rained sliced steal half block", "block.minecraft.waxed_weathered_cut_copper_stairs": "Shiny rained sliced copurr sters", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON>h <PERSON>", "block.minecraft.weathered_copper": "Rainy copurr", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON>", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON>pur Trepdoor", "block.minecraft.weathered_cut_copper": "Yuckeh-old slised copurr", "block.minecraft.weathered_cut_copper_slab": "Kinda-old slised copurr bluk haf", "block.minecraft.weathered_cut_copper_stairs": "Old yucky slised copurr sters", "block.minecraft.weeping_vines": "oh no y u cry roof noddle", "block.minecraft.weeping_vines_plant": "sad noodle plant", "block.minecraft.wet_sponge": "wet Spangblob", "block.minecraft.wheat": "Wheat Crops", "block.minecraft.white_banner": "blenk bahnor", "block.minecraft.white_bed": "<PERSON>e <PERSON>", "block.minecraft.white_candle": "Wite land pikl", "block.minecraft.white_candle_cake": "Caek wif Wite Land pickl", "block.minecraft.white_carpet": "<PERSON><PERSON> Cat Rug", "block.minecraft.white_concrete": "Wyte tough bluk", "block.minecraft.white_concrete_powder": "<PERSON><PERSON> tough bluk powdr", "block.minecraft.white_glazed_terracotta": "Wayte mosaic", "block.minecraft.white_shulker_box": "<PERSON><PERSON>", "block.minecraft.white_stained_glass": "Wite <PERSON><PERSON>", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON>la<PERSON>n", "block.minecraft.white_terracotta": "<PERSON><PERSON>", "block.minecraft.white_tulip": "Wite tulehp", "block.minecraft.white_wool": "<PERSON><PERSON> Fu<PERSON>", "block.minecraft.wildflowers": "Weed but pretier", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON>kul", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON>", "block.minecraft.yellow_banner": "yelloh bahnor", "block.minecraft.yellow_bed": "Banana Bed", "block.minecraft.yellow_candle": "Yello land pikl", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_carpet": "<PERSON>llo Cat Rug", "block.minecraft.yellow_concrete": "<PERSON><PERSON> tough bluk", "block.minecraft.yellow_concrete_powder": "Yellow tough bluk powdr", "block.minecraft.yellow_glazed_terracotta": "Yellow mosaic", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.yellow_terracotta": "<PERSON><PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON> Fur Blu<PERSON>", "block.minecraft.zombie_head": "Bad Hooman Hed", "block.minecraft.zombie_wall_head": "<PERSON><PERSON> <PERSON>d", "book.byAuthor": "by %1$s", "book.edit.title": "Bowk Edi ScRen", "book.editTitle": "Titl for ur book:", "book.finalizeButton": "sighn an uplode", "book.finalizeWarning": "Warnin!!1! whenz you sign teh bok it cantz be changedz!!1", "book.generation.0": "FIRST!", "book.generation.1": "copee of first", "book.generation.2": "copee off copee", "book.generation.3": "scratched up (i didnt do dat)", "book.invalid.tag": "* Invalud buk tag *", "book.pageIndicator": "page %1$s of a bunch (%2$s)", "book.page_button.next": "nex pagee", "book.page_button.previous": "p<PERSON><PERSON><PERSON> pagee", "book.sign.title": "Bowk Sine ScRen", "book.sign.titlebox": "titl", "book.signButton": "Put najm", "book.view.title": "<PERSON><PERSON>", "build.tooHigh": "tallnes limet for bilding iz %s", "chat.cannotSend": "Cantz send chat msg", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Click 2 teleportz", "chat.copy": "SAEV 4 L8R", "chat.copy.click": "klix tu cop 2 catbord", "chat.deleted_marker": "Dis meow is DENIED!!! by teh servr.", "chat.disabled.chain_broken": "Chats disabld cuz bruken cain. Try reconectin plss.", "chat.disabled.expiredProfileKey": "Chats disabld cuz publik kee expaired. Try reconectin plss", "chat.disabled.invalid_command_signature": "<PERSON><PERSON> haz sus or missin komand argumen singaturz.", "chat.disabled.invalid_signature": "Chats haz bad singatur. Try reconectin pls", "chat.disabled.launcher": "Chat disabld by launchr opshun. Cannot sen mesage.", "chat.disabled.missingProfileKey": "Chats disabld cuz publik kee dosnt exists!!! Try reconectin plss", "chat.disabled.options": "<PERSON><PERSON> turnd off in cleint opshinz.", "chat.disabled.out_of_order_chat": "Chat gotz rong. Did ur cat clocc chaeng??", "chat.disabled.profile": "chatz is not allowed becuz of akownt setingz. Prez '%s' again for more informashions.", "chat.disabled.profile.moreInfo": "Chat not allowed by ako<PERSON>t settins. Cannot send or view mesagez.", "chat.editBox": "chatz", "chat.filtered": "Fliterd frum King cat.", "chat.filtered_full": "<PERSON>h servr hazs hided ur meow 4 sum catz.", "chat.link.confirm": "R u sure you want 2 open teh followin websiet?", "chat.link.confirmTrusted": "Do u wants 2 open dis link or copy it 2 ur clipboard?", "chat.link.open": "Opin in browzr", "chat.link.warning": "Nevr open linkz frum kittehs u doan't trust!", "chat.queue": "[+%s pandig limes]", "chat.square_brackets": "[%s]", "chat.tag.error": "Sever sen no guud msg", "chat.tag.modified": "Dis mesage haz been modified by teh servr. <PERSON>:", "chat.tag.not_secure": "NuN trusted meow. <PERSON>t bi repoted.", "chat.tag.system": "<PERSON><PERSON><PERSON> meow can't be repoted.", "chat.tag.system_single_player": "<PERSON>vr mesa<PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s didz %s clap clap meeee", "chat.type.advancement.goal": "%s dids %s omg gg bro", "chat.type.advancement.task": "%s haz maed da atfancemend %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON><PERSON>", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sayz %s", "chat.validation_error": "Menssage thwing not workwing!!", "chat_screen.message": "Mesage to sen: %s", "chat_screen.title": "<PERSON> screeen", "chat_screen.usage": "Inputed mesage an prez Intr 2 sen", "chunk.toast.checkLog": "See lawg for moar detailz", "chunk.toast.loadFailure": "Faild 2 lod chonk at %s", "chunk.toast.lowDiskSpace": "Low disk spac!", "chunk.toast.lowDiskSpace.description": "Uh oh we mite not be abel 2 sav teh wurld.", "chunk.toast.saveFailure": "Faild 2 sav chonk at %s", "clear.failed.multiple": "No items wuz findz on %s players", "clear.failed.single": "Nu itemz fund on pleyer %s", "color.minecraft.black": "dark", "color.minecraft.blue": "blu", "color.minecraft.brown": "wuudy colur", "color.minecraft.cyan": "nyan", "color.minecraft.gray": "graey", "color.minecraft.green": "greeeeee<PERSON>", "color.minecraft.light_blue": "wet powdr", "color.minecraft.light_gray": "dull colah", "color.minecraft.lime": "<PERSON><PERSON><PERSON>", "color.minecraft.magenta": "too shinee pink colah", "color.minecraft.orange": "stampy colur", "color.minecraft.pink": "pincc", "color.minecraft.purple": "OURPLE", "color.minecraft.red": "redd", "color.minecraft.white": "wite", "color.minecraft.yellow": "yelow", "command.context.here": "<--[LOK HER!11]", "command.context.parse_error": "%s at pozishun %s: %s", "command.exception": "cud not parse command: %s", "command.expected.separator": "Expectd whitespace 2 end wan argument, but findz trailin data", "command.failed": "An unexpectd errur occurd tryin 2 do dis cmd", "command.forkLimit": "Maks numbr ov conteksts (%s) rechd", "command.unknown.argument": "Incorrect argument 4 command", "command.unknown.command": "cant find teh commnd :( luk under 2 c y", "commands.advancement.criterionNotFound": "De atfancemend '%1$s' dooz not contain de criterion '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Coeldn't grand da kriterion '%s' uv atfancemend %s 2 %s playrs cuz they already have it", "commands.advancement.grant.criterion.to.many.success": "Granted da criterion '%s' of atfancemend %s 2 %s kities", "commands.advancement.grant.criterion.to.one.failure": "Coeldn't grand da kriterion '%s' uv atfancemend %s 2 %s cuz they already have it", "commands.advancement.grant.criterion.to.one.success": "Granted da criterion '%s' of atfancemend %s 2 %s", "commands.advancement.grant.many.to.many.failure": "Coeldn't grand %s atfancemends 2 %s pleytrs cuz they already hav em", "commands.advancement.grant.many.to.many.success": "Granted %s atfancemends 2 %s kities", "commands.advancement.grant.many.to.one.failure": "Coeldn't grand %s atfancemends 2 %s cuz they already hav em", "commands.advancement.grant.many.to.one.success": "Granted %s atfancemends 2 %s", "commands.advancement.grant.one.to.many.failure": "Coeldn't grand da atfancemend %s 2 %s playrs cuz they already have it", "commands.advancement.grant.one.to.many.success": "Granted da atfancemend '%s' 2 %s kities", "commands.advancement.grant.one.to.one.failure": "Coeldn't grand da atfancemend '%s' 2 %s cuz they already hav it", "commands.advancement.grant.one.to.one.success": "Granted da atfancemend '%s' 2 %s", "commands.advancement.revoke.criterion.to.many.failure": "Coeldn't tak da kriterion '%s' uv atfancemend %s frum %s katz cuz they no have it", "commands.advancement.revoke.criterion.to.many.success": "Skracht standurdz '%s' of advansment squarz %s from %s katz", "commands.advancement.revoke.criterion.to.one.failure": "Coeldn't tak da kriterion '%s' uv atfancemend %s frum %s cuz they no have it", "commands.advancement.revoke.criterion.to.one.success": "Skracht standurdz '%s' of advansment squarz %s from %s", "commands.advancement.revoke.many.to.many.failure": "Coodint Skrach %s advansment squarz from %s katz bcuz thay dont hav squarz", "commands.advancement.revoke.many.to.many.success": "Skracht %s advansment squarz from %s katz", "commands.advancement.revoke.many.to.one.failure": "Coodint Skrach %s advansment squarz from %s bcuz thay dont hav squarez", "commands.advancement.revoke.many.to.one.success": "Skracht %s advansment squarz from %s", "commands.advancement.revoke.one.to.many.failure": "Coeldn't tak da adfancemend %s frum %s playrs cuz they no have it", "commands.advancement.revoke.one.to.many.success": "Tuk atvunzment %s frum %s kities", "commands.advancement.revoke.one.to.one.failure": "Coeldn't tek atvunzment %s frum %s cuz dey no hav it", "commands.advancement.revoke.one.to.one.success": "Tuk atvunzment %s frum %s", "commands.attribute.base_value.get.success": "Base valooe of attribute %s 4 cat %s iz %s", "commands.attribute.base_value.reset.success": "Base valooe 4 attribute %s 4 cat %s reset 2 defawlt %s", "commands.attribute.base_value.set.success": "Base valooe 4 attribute %s 4 cat %s iz nao %s", "commands.attribute.failed.entity": "%s iz not valid entity 4 dis command", "commands.attribute.failed.modifier_already_present": "Modifier %s alredy preznt on attribute %s 4 cat %s", "commands.attribute.failed.no_attribute": "Cat %s haz no attribute %s", "commands.attribute.failed.no_modifier": "Atributz %s for katz haz %s no modifer %s", "commands.attribute.modifier.add.success": "Addz modifier %s 2 attribute %s 4 cat %s", "commands.attribute.modifier.remove.success": "Remoovd modifier %s from attribute %s 4 cat %s", "commands.attribute.modifier.value.get.success": "Valooe of modifier %s on attribute %s 4 cat %s iz %s", "commands.attribute.value.get.success": "Valooe of attribute %s 4 cat %s iz %s", "commands.ban.failed": "<PERSON><PERSON> changd, da kat is already banned", "commands.ban.success": "Beaned %s: %s", "commands.banip.failed": "<PERSON>hin changd, dat IP is banned", "commands.banip.info": "Dis ban affectz %s player(s): %s", "commands.banip.invalid": "Bad IP or unknown kitteh", "commands.banip.success": "IP Beaned %s: %s", "commands.banlist.entry": "%s was beaned by %s: %s", "commands.banlist.entry.unknown": "(Idk)", "commands.banlist.list": "Ther r %s bean(z):", "commands.banlist.none": "There r no beans", "commands.bossbar.create.failed": "a Boshbar alweady exists with da id '%s'", "commands.bossbar.create.success": "Kreatd cuztum bossthing %s", "commands.bossbar.get.max": "cuztum bossthing %s haz new big numburz: %s", "commands.bossbar.get.players.none": "cuztum bossthing %s haz no katz on teh linez", "commands.bossbar.get.players.some": "Cuztum bossthing %s haz %s kat(z) on teh linez: %s", "commands.bossbar.get.value": "cuztum bossthing %s haz new numberz: %s", "commands.bossbar.get.visible.hidden": "cuztum bossthing %s iz hiddind", "commands.bossbar.get.visible.visible": "cuztum bossthing %s iz shownin'", "commands.bossbar.list.bars.none": "Der ar no cuztum bossthing aktiv", "commands.bossbar.list.bars.some": "Der r %s cuztum bossthing(s) aktiv: %s", "commands.bossbar.remove.success": "Remuvd cuztum bossthing %s", "commands.bossbar.set.color.success": "cuztum bossthing %s haz new culurz", "commands.bossbar.set.color.unchanged": "nothin changd, thaz already teh color ov dis bosbar", "commands.bossbar.set.max.success": "cuztum bossthing %s haz new big numburz: %s", "commands.bossbar.set.max.unchanged": "nothin changd, thaz already teh max ov dis bosbar", "commands.bossbar.set.name.success": "cuztum bossthing %s haz new namez", "commands.bossbar.set.name.unchanged": "nothin changd, thaz already teh naym ov dis bosbar", "commands.bossbar.set.players.success.none": "cuztum bossthing %s haz no mor katz", "commands.bossbar.set.players.success.some": "Cuztum bossthing %s haz %s kat(z) %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON> changd, dose players r already on teh bosbar wif nobody 2 add or remoov", "commands.bossbar.set.style.success": "cuztum bossthing %s haz new stylinz", "commands.bossbar.set.style.unchanged": "nothin changd, thaz already teh style ov dis bosbar", "commands.bossbar.set.value.success": "cuztum bossthing %s haz new numberz: %s", "commands.bossbar.set.value.unchanged": "nothin changd, thaz already teh value ov dis bosbar", "commands.bossbar.set.visibility.unchanged.hidden": "nothin changd, teh bosbar iz already hidden", "commands.bossbar.set.visibility.unchanged.visible": "nothin changd, teh bosbar iz already visible", "commands.bossbar.set.visible.success.hidden": "I cant see cuztum bossthing %s", "commands.bossbar.set.visible.success.visible": "I can see cuztum bossthing %s", "commands.bossbar.unknown": "No bosbar exists wif teh id %s", "commands.clear.success.multiple": "%s item(z) frum %s players said gudbye :(", "commands.clear.success.single": "%s item(z) frum player %s said gudbye :(", "commands.clear.test.multiple": "We seez %s lookeelikee stuff(z) on %s katz", "commands.clear.test.single": "We seez %s lookeelikee stuff(z) on kat %s", "commands.clone.failed": "no blockz wuz clond", "commands.clone.overlap": "teh source an destinashun areas cant overlap", "commands.clone.success": "Copy clond %s blok(z)", "commands.clone.toobig": "2 maini blukz in teh spesified aria (macksimum %s, spesified %s)", "commands.damage.invulnerable": "Targt cant get any damag dat u give >:)))", "commands.damage.success": "Kit<PERSON>h did %s scratchz 2 %s", "commands.data.block.get": "%s on blok %s, %s, %s aftur skalez numbur of %s iz %s", "commands.data.block.invalid": "teh target block iz not block entity", "commands.data.block.modified": "Changeded blok infoz of %s, %s, %s", "commands.data.block.query": "%s, %s, %s haz dis blok infoz: %s", "commands.data.entity.get": "%s on %s after skalez numbur of %s iz %s", "commands.data.entity.invalid": "unabl 2 modifi cat dataz", "commands.data.entity.modified": "Modifid entitey deeta off %s", "commands.data.entity.query": "%s haz teh followin entitey deeta: %s", "commands.data.get.invalid": "No can doo with %s; can ownly bee number tag", "commands.data.get.multiple": "Dis argument accepts a singl NBT value", "commands.data.get.unknown": "Cant git %s: tag dosnt exist", "commands.data.merge.failed": "nauthing chanched, da specifd propaties alweady hav dis values", "commands.data.modify.expected_list": "Cat wonts many not %s", "commands.data.modify.expected_object": "Expected object, got: %s", "commands.data.modify.expected_value": "no valooe 4 mi, u gabe %s", "commands.data.modify.invalid_index": "Invlid lst indez: %s", "commands.data.modify.invalid_substring": "Bad subword thing: %s to %s", "commands.data.storage.get": "%s n sturage %s aftur skalez numbur o %s iz %s", "commands.data.storage.modified": "Modifid reservoir %s", "commands.data.storage.query": "Storge %s has followin subject-matter: %s", "commands.datapack.create.already_exists": "Pakz w/ naem \"%s' alredie eksistz", "commands.datapack.create.invalid_full_name": "Invlid new pak nam \"%s\"", "commands.datapack.create.invalid_name": "Bad cartacrer in newzz pock namez '%s'", "commands.datapack.create.io_failure": "cen't criate pock wiz namez '%s', lol chck logis", "commands.datapack.create.metadata_encode_failure": "Faelad 2 encoder metodator 4 pock wiz namez '%s': %s", "commands.datapack.create.success": "<PERSON><PERSON><PERSON>d new empteh pock wiz namez '%s'", "commands.datapack.disable.failed": "Pack %s iz not enabld!", "commands.datapack.disable.failed.feature": "Pak \"%s\" cant B turnd off, cuz it part uv da enabld fwag", "commands.datapack.enable.failed": "Pack %s iz already enabld!", "commands.datapack.enable.failed.no_flags": "nerdz pacc '%s' camt b ussed, sinc needd flagz rnt enabld in dis wurld: %s!!!!!!!", "commands.datapack.list.available.none": "Ther R no infoz packz that can be yes-yes", "commands.datapack.list.available.success": "Ther r %s data pack(z) that can be yes-yes: %s", "commands.datapack.list.enabled.none": "Ther R no infoz packz that are yes-yes", "commands.datapack.list.enabled.success": "Ther r %s data pack(z) that are yes-yes: %s", "commands.datapack.modify.disable": "deactivatin nerdz stwuff: %s", "commands.datapack.modify.enable": "activatin nerdz dawta stuffz: %s", "commands.datapack.unknown": "Cat doezn't know diz data pak %s", "commands.debug.alreadyRunning": "<PERSON>h tik profilr r <PERSON><PERSON>y startd", "commands.debug.function.noRecursion": "kat cant traec frawm insid ov fnctoin", "commands.debug.function.noReturnRun": "Tracin' con't be used wuth retuhn run", "commands.debug.function.success.multiple": "Traicd %s komand(z) frum %s funkshinz t0 output file %s", "commands.debug.function.success.single": "Traicd %s komand(z) frum funkshinz '%s' too output file %s", "commands.debug.function.traceFailed": "coodnt trace teh functshun!", "commands.debug.notRunning": "<PERSON><PERSON> tik profilr hasnt startd", "commands.debug.started": "Startd tik profilin'", "commands.debug.stopped": "Stoppd tik profilin aftr %s secondz an %s tikz (%s tikz purr secund)", "commands.defaultgamemode.success": "%s iz naow da normul playin' for teh kittez", "commands.deop.failed": "nothin changd, teh playr iz not an operator", "commands.deop.success": "%s is not alfa enymor", "commands.dialog.clear.multiple": "<PERSON><PERSON>rd dialogz fr %s kittiez", "commands.dialog.clear.single": "Deleetd dilogz for %s", "commands.dialog.show.multiple": "Dizpleyd dialawg 2 %s kittenz", "commands.dialog.show.single": "Dizplyd dialawg 2 %s", "commands.difficulty.failure": "Teh difficulty did not change; it already set 2 %s", "commands.difficulty.query": "Teh diffikuty es %s", "commands.difficulty.success": "Da ameownt of creeperz iz gunnu bee %s", "commands.drop.no_held_items": "Entity can not hold any items... AND NEVAH WILL", "commands.drop.no_loot_table": "Entity %s has no loot table", "commands.drop.no_loot_table.block": "Blokc %s haz no luutz", "commands.drop.success.multiple": "Droppd %s itemz", "commands.drop.success.multiple_with_table": "Droppd %s items frum za table %s", "commands.drop.success.single": "Droppd %s %s", "commands.drop.success.single_with_table": "Droppd %s %s stuffz frum za table %s", "commands.effect.clear.everything.failed": "target has no effects 2 remoov", "commands.effect.clear.everything.success.multiple": "Tuk all efekt frum %s targits", "commands.effect.clear.everything.success.single": "Rmved all efekt fram %s", "commands.effect.clear.specific.failed": "target doesnt has teh requestd effect", "commands.effect.clear.specific.success.multiple": "Tuk efukt %s frum %s targits", "commands.effect.clear.specific.success.single": "Tuk efukt %s frum %s", "commands.effect.give.failed": "unable 2 apply dis effect (target iz eithr immune 2 effects, or has somethin strongr)", "commands.effect.give.success.multiple": "%s targitz ar gunnu have %s", "commands.effect.give.success.single": "Appoled efekt %s tO %s", "commands.enchant.failed": "nothin changd, targets eithr has no item in their hanz or teh enchantment cud not be applid", "commands.enchant.failed.entity": "%s is nawt a valid entiti for that cemmand", "commands.enchant.failed.incompatible": "%s cennot seppurt thet megic", "commands.enchant.failed.itemless": "%s s nut hulding item", "commands.enchant.failed.level": "%s iz highr than teh maximum level ov %s supportd by dat enchantment", "commands.enchant.success.multiple": "Applid inchantmnt %s to %s intitiez", "commands.enchant.success.single": "Applid inchantmnt %s to %s item", "commands.execute.blocks.toobig": "2 maini blukz in teh spesified aria (macksimum %s, spesified %s)", "commands.execute.conditional.fail": "Tiist failured", "commands.execute.conditional.fail_count": "Tiist failured, count: %s", "commands.execute.conditional.pass": "<PERSON><PERSON> uwu", "commands.execute.conditional.pass_count": "Test pasd, count: %s", "commands.execute.function.instantiationFailure": "<PERSON><PERSON><PERSON> faild 2 imstemtiaet funkshun %s: %s", "commands.experience.add.levels.success.multiple": "Gev %s uxpeerinz luvlz two %s katz", "commands.experience.add.levels.success.single": "Gev %s uxpeerinz luvlz two %s", "commands.experience.add.points.success.multiple": "Gev %s uxpeerinz puhnts two %s katz", "commands.experience.add.points.success.single": "Gev %s uxpeerinz puhnts two %s", "commands.experience.query.levels": "%s haz %s uxpeerinz luvlz", "commands.experience.query.points": "%s haz %s uxpeerinz puhnts", "commands.experience.set.levels.success.multiple": "Sat %s uxpeerinz luvlz on %s playurz", "commands.experience.set.levels.success.single": "Sat %s uxpeerinz luvlz on %s", "commands.experience.set.points.invalid": "cant set experience points aboov teh maximum points 4 da kitteh current level", "commands.experience.set.points.success.multiple": "Sat %s uxpeerinz puhnts on %s katz", "commands.experience.set.points.success.single": "Sat %s uxpeerinz puhnts on %s", "commands.fill.failed": "no blockz wuz filld", "commands.fill.success": "Succeszfuly fild %s blok(x)", "commands.fill.toobig": "2 maini blukz in teh spesified aria (macksimum %s, spesified %s)", "commands.fillbiome.success": "baium(s) r maded betwin %s, %s, %s AND %s, %s, %s !", "commands.fillbiome.success.count": "%s biom entry(z) maded betwin %s, %s, %s, n %s, %s, %s", "commands.fillbiome.toobig": "2 maini blukz in teh spesified voluum (macksimum %s, spesified %s)", "commands.forceload.added.failure": "No pieces wre markd for furce looding", "commands.forceload.added.multiple": "Merked %s pieces in %s frum %s 2 %s 2 to be furce loded", "commands.forceload.added.none": "A furce loded piece ws fund in  et: %s", "commands.forceload.added.single": "Merked pieces %s in %s 2 be furce looded", "commands.forceload.list.multiple": "%s furce looded pieces wer fund in %s at: %s", "commands.forceload.list.single": "A furce loded piece ws fund in %s et: %s", "commands.forceload.query.failure": "Piece et %s in %s is nut merked fur furce loding", "commands.forceload.query.success": "Piece et %s in %s is merked fur furce loding", "commands.forceload.removed.all": "Unmerked al furce looded pieces in %s", "commands.forceload.removed.failure": "Nu pieces wer removd frum furce looding", "commands.forceload.removed.multiple": "Unmerked %s pieces inn %s frm %s 2 %s fr furce looding", "commands.forceload.removed.single": "Unmerked piece %s in %s for furce lodig", "commands.forceload.toobig": "2 maini blukz in teh spesified aria (macksimum %s, spesified %s)", "commands.function.error.argument_not_compound": "Bad argumen tap: %s, expectd Compound", "commands.function.error.missing_argument": "No argumen %2$s 2 funcshun %1$s", "commands.function.error.missing_arguments": "No argumen 2 funcshun %s", "commands.function.error.parse": "Wile instatiating macro %s: Cmnd '%s' cawsd error: %s", "commands.function.instantiationFailure": "<PERSON><PERSON><PERSON> faild 2 imstemtiaet funkshun %s: %s", "commands.function.result": "Funkshen %s returnd %s", "commands.function.scheduled.multiple": "Runnin' funkshens %s", "commands.function.scheduled.no_functions": "Kitteh no find ani <PERSON> calld %s :(", "commands.function.scheduled.single": "Runnin' funksheun %s", "commands.function.success.multiple": "Dun %s komand(z) frum %s funkshunz", "commands.function.success.multiple.result": "Dun %s funkshunz", "commands.function.success.single": "Dun %s comand(z) frum funkshun %s", "commands.function.success.single.result": "Funkshun '%2$s' gotchu %1$s", "commands.gamemode.success.other": "Set %s's gaem moed 2 %s", "commands.gamemode.success.self": "Set awn gaem moed 2 %s", "commands.gamerule.query": "Gemrul %s iz set to: %s", "commands.gamerule.set": "Gemrul %s iz now set to: %s", "commands.give.failed.toomanyitems": "Cant givez moar dan %s off %s", "commands.give.success.multiple": "Gev %s %s to %s kities", "commands.give.success.single": "Givn %s %s 2 %s", "commands.help.failed": "unknown command or insufficient permishuns", "commands.item.block.set.success": "Replased a slot att %s, %s, %s wit %s", "commands.item.entity.set.success.multiple": "Replacd slot on %s intitiez wif %s", "commands.item.entity.set.success.single": "Replaedz a sluut on %s wif %s", "commands.item.source.no_such_slot": "Teh target doez not has slot %s", "commands.item.source.not_a_container": "Sors bluk at %s, %s, %s iz nawt a containr", "commands.item.target.no_changed.known_item": "Naw tawgetz welcmd itemz %s intu slot %s", "commands.item.target.no_changes": "Neh targeets hassnt aceptd item in toslot %s", "commands.item.target.no_such_slot": "Da targit duzent has slot %s", "commands.item.target.not_a_container": "Bluk at %s, %s, %s iz nawt a containr", "commands.jfr.dump.failed": "JFR profawlllin faild to trash recawrd: %s", "commands.jfr.start.failed": "JFR profawlllin reawly messd up cos fo CATZ", "commands.jfr.started": "JFR profawlllin startd wiht CATZ", "commands.jfr.stopped": "JFR profawlllin stopd and thrown by kittn to %s", "commands.kick.owner.failed": "Cant yeet the svr owner in LAN gaem", "commands.kick.singleplayer.failed": "Can't yeet in a 1 kitteh gaem", "commands.kick.success": "Shun %s out of da houz: %s", "commands.kill.success.multiple": "%s tings put to slep foreva", "commands.kill.success.single": "Killd %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Der r %s of a max of %s kities in da houz: %s", "commands.locate.biome.not_found": "No kan findz bioem liek \"%s\" in resunabl ranj", "commands.locate.biome.success": "Da nerest %s iz at %s (%s stepz fur)", "commands.locate.poi.not_found": "no kan findz intewest wit da typ \"%s\" near kitteh", "commands.locate.poi.success": "Da nerest %s iz at %s (%s stepz fur)", "commands.locate.structure.invalid": "<PERSON><PERSON><PERSON> cant find da strooktur wif taip \"%s\"", "commands.locate.structure.not_found": "<PERSON><PERSON><PERSON> culdnt find strooktur of taip \"%s\" neerby", "commands.locate.structure.success": "Da nerest %s iz at %s (%s stepz fur)", "commands.message.display.incoming": "%s meowz at u: %s", "commands.message.display.outgoing": "You meow 2 %s: %s", "commands.op.failed": "nothin changd, teh kitteh iz already an operator", "commands.op.success": "Med %s a alfa kiten", "commands.pardon.failed": "nothin changd, teh kitteh isnt bannd", "commands.pardon.success": "Unbeaned %s", "commands.pardonip.failed": "nothin changd, dat ip isnt bannd", "commands.pardonip.invalid": "invalid ip addres", "commands.pardonip.success": "Unbeaned IP %s", "commands.particle.failed": "teh particle wuz not visible 4 anybody", "commands.particle.success": "Showin purrtikle %s", "commands.perf.alreadyRunning": "<PERSON><PERSON> performens profilr r al<PERSON>y startd", "commands.perf.notRunning": "<PERSON><PERSON> performens profilr hasnt startd", "commands.perf.reportFailed": "Faild 2 maek Bug reprort", "commands.perf.reportSaved": "Bugs re nau in %s", "commands.perf.started": "Startd 10 secon performens profilin runed (us '/perf stop' 2 stop eerly)", "commands.perf.stopped": "Stoppd performens profilin aftr %s second(z) & %s tik(z) (%s tik(z) pr secund)", "commands.place.feature.failed": "<PERSON><PERSON><PERSON> tryd so hard but culdnt place fetur", "commands.place.feature.invalid": "Dere iz nah featur wit taip \"%s\"", "commands.place.feature.success": "Plaised da \"%s\" at %s, %s, %s", "commands.place.jigsaw.failed": "Spon jigsou reawly messd up cos fo CATZ", "commands.place.jigsaw.invalid": "Dere iz nah templete pul wit taip \"%s\"", "commands.place.jigsaw.success": "Spond jigsou @ %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON><PERSON> tryd so hard but culdnt place structur", "commands.place.structure.invalid": "<PERSON><PERSON><PERSON> cant find da strooktur wif taip \"%s\"", "commands.place.structure.success": "Spond structur \"%s\" @ %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON><PERSON> tryd so hard but culdnt place teemplet", "commands.place.template.invalid": "Dere iz nah teemplete wit typ \"%s", "commands.place.template.success": "<PERSON><PERSON><PERSON> made da teemplete \"%s\" at %s, %s, %s", "commands.playsound.failed": "teh sound iz 2 far away 2 be herd", "commands.playsound.success.multiple": "Playd sund %s to %s kitiez", "commands.playsound.success.single": "Makd noize %s 2 %s", "commands.publish.alreadyPublished": "Kittenzgame is elreadi hsted 0n prt %s", "commands.publish.failed": "Unabl 2 hust locul gaem", "commands.publish.started": "Lucl gaem hustd on port %s", "commands.publish.success": "Yurw wurld is naw hawsted on powrt %s", "commands.random.error.range_too_large": "<PERSON><PERSON><PERSON> of randum valw must b @ most 2147483646 (wow)", "commands.random.error.range_too_small": "<PERSON><PERSON><PERSON> of randum valw must b atlest 2", "commands.random.reset.all.success": "Reset %s randum ordre(z)", "commands.random.reset.success": "Reset randum ordre %s", "commands.random.roll": "%s got %s (between %s n %s)", "commands.random.sample.success": "Randumizd valw: %s", "commands.recipe.give.failed": "no new recipez wuz lernd", "commands.recipe.give.success.multiple": "Gott %s resapeez 4 %s kittehz", "commands.recipe.give.success.single": "Gott %s resapeez 4 %s", "commands.recipe.take.failed": "no recipez cud be forgotten", "commands.recipe.take.success.multiple": "Took %s resapeez from %s katz", "commands.recipe.take.success.single": "Took %s resapeez from %s", "commands.reload.failure": "Relod iz fail! Keepin old stuffz", "commands.reload.success": "Reloadin!", "commands.ride.already_riding": "%s is alwedii ridin %s", "commands.ride.dismount.success": "%s stoppd ridin %s", "commands.ride.mount.failure.cant_ride_players": "CAnt ride other kats!!", "commands.ride.mount.failure.generic": "%s 2 chomky 2 ried %s", "commands.ride.mount.failure.loop": "Thingz no can ried temselvs n passengrs", "commands.ride.mount.failure.wrong_dimension": "Cant ried the thing in diffrent dimenshun :(", "commands.ride.mount.success": "%s startd ridin %s", "commands.ride.not_riding": "%s is not ridin ani vee cool.", "commands.rotate.success": "Spinnd %s", "commands.save.alreadyOff": "<PERSON><PERSON> <PERSON>z already turnd off", "commands.save.alreadyOn": "<PERSON><PERSON> <PERSON>z already turnd on", "commands.save.disabled": "Robot savingz iz no-no", "commands.save.enabled": "Robot savingz iz yes-yes", "commands.save.failed": "unable 2 save teh game (iz thar enough disk space?)", "commands.save.saving": "Savingz teh gamez (U hav 2 wate)", "commands.save.success": "Saved da gamez", "commands.schedule.cleared.failure": "Nu enrollments called id %s", "commands.schedule.cleared.success": "%s schejul(z) wit id %s said gudbye :(", "commands.schedule.created.function": "Sceduled funcshun '%s' in %s tik(s) at gemtime %s", "commands.schedule.created.tag": "Sceduled teg '%s' in %s tiks at gemtime %s", "commands.schedule.macro": "Meow puur no marco", "commands.schedule.same_tick": "Dats 2 sun i cant maek it", "commands.scoreboard.objectives.add.duplicate": "an objectiv already exists by dat naym", "commands.scoreboard.objectives.add.success": "Made new goalz %s", "commands.scoreboard.objectives.display.alreadyEmpty": "nothin changd, dat display slot iz already empty", "commands.scoreboard.objectives.display.alreadySet": "nothin changd, dat display slot iz already showin dat objectiv", "commands.scoreboard.objectives.display.cleared": "No moar goalzez in shownin' spot %s", "commands.scoreboard.objectives.display.set": "Made shownin' spot %s shownin' goalz %s", "commands.scoreboard.objectives.list.empty": "Ther R no objectivez", "commands.scoreboard.objectives.list.success": "Ther r %s goal(z): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disaebld displae auto-updaet 4 %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enaebld displae auto-updaet 4 %s", "commands.scoreboard.objectives.modify.displayname": "%s now reeds liek %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "<PERSON><PERSON><PERSON> defolt numbr 4mat uv %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "<PERSON><PERSON><PERSON><PERSON> defolt numbr 4mat uv %s", "commands.scoreboard.objectives.modify.rendertype": "Chengd how objectiv %s is drawd", "commands.scoreboard.objectives.remove.success": "Tuk away goalz %s", "commands.scoreboard.players.add.success.multiple": "Added %s 2 %s 4 %s thingyz", "commands.scoreboard.players.add.success.single": "Added %s 2 %s 4 %s (naow %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Kleard dizpwey naym forr %s kittehz in %s", "commands.scoreboard.players.display.name.clear.success.single": "K<PERSON>rd dizpwey naym for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Chainjd dizplei naym to %s for %s kittehz in %s", "commands.scoreboard.players.display.name.set.success.single": "<PERSON>jd dizplei naym to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Kleard numbr 4mat forr %s kittehz in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Kleard numbr 4mat for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Chan<PERSON>d numbr 4mat for %s kittehz in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Chaenjd numbr 4mat for %s in %s", "commands.scoreboard.players.enable.failed": "nothin changd, dat triggr iz already enabld", "commands.scoreboard.players.enable.invalid": "Enable only werkz on triggr-objectivez", "commands.scoreboard.players.enable.success.multiple": "Tigger %s 4 %s <PERSON>yz is yes-yes", "commands.scoreboard.players.enable.success.single": "Enabuled trigur %s for %s", "commands.scoreboard.players.get.null": "Cantt gt valu of %s fur %s; nun is set", "commands.scoreboard.players.get.success": "%s haz %s %s", "commands.scoreboard.players.list.empty": "<PERSON>r <PERSON>d entitiz", "commands.scoreboard.players.list.entity.empty": "%s haz no pointz 4 shownin'", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s haz %s scor(z):", "commands.scoreboard.players.list.success": "Ther r %s trakd entiti(ez): %s", "commands.scoreboard.players.operation.success.multiple": "Updatd %s 4 %s entitiez", "commands.scoreboard.players.operation.success.single": "sett %s 4 %s 2 %s", "commands.scoreboard.players.remove.success.multiple": "Tuk %s frum %s 4 %s thingyz", "commands.scoreboard.players.remove.success.single": "Tuk %s frum %s 4 %s (naow %s)", "commands.scoreboard.players.reset.all.multiple": "Start pointzez over 4 %s thingyz", "commands.scoreboard.players.reset.all.single": "Start pointzez over 4 %s", "commands.scoreboard.players.reset.specific.multiple": "Start %s over 4 %s thingyz", "commands.scoreboard.players.reset.specific.single": "Start %s over 4 %s", "commands.scoreboard.players.set.success.multiple": "Set %s 4 %s thingyz 2 %s", "commands.scoreboard.players.set.success.single": "Set %s 4 %s 2 %s", "commands.seed.success": "Seed: %s", "commands.setblock.failed": "Cud not set teh block", "commands.setblock.success": "Changd teh blok at %s, %s, %s", "commands.setidletimeout.success": "Teh playr idle timeout iz now %s minute(z)", "commands.setidletimeout.success.disabled": "Teh slow kitteh go out thing haz bean off", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON><PERSON> can only set teh wurld spawn fur ovrwurld", "commands.setworldspawn.success": "I has movd big bed tu %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Set kat home 2 %s, %s, %s [%s] in %s 4 %s playrz", "commands.spawnpoint.success.single": "Set kat home 2 %s, %s, %s [%s] in %s 4 %s", "commands.spectate.not_spectator": "%s is not lurkin arund", "commands.spectate.self": "Cant wach urself", "commands.spectate.success.started": "Now lukin at %s", "commands.spectate.success.stopped": "Not lookin at anythign anymoar", "commands.spreadplayers.failed.entities": "Culd not spread %s entity/entitiez around %s, %s (2 much da entitiez 4 space - try usin spread ov at most %s)", "commands.spreadplayers.failed.invalid.height": "Dat maxHeight %s doeznt wurk; ekzpekted highar dan wurld mimimum %s", "commands.spreadplayers.failed.teams": "Cud not spread %s team(z) around %s, %s (2 lotz da entitiez 4 space - try usin spread ov at most %s)", "commands.spreadplayers.success.entities": "Spred %s playr(z) around %s, %s wif an average distance ov %s blockz apart", "commands.spreadplayers.success.teams": "Spred %s team(z) around %s, %s wif an average distens ov %s blockz apart", "commands.stop.stopping": "Stoppin teh servr", "commands.stopsound.success.source.any": "Stopd all teh sundz %s", "commands.stopsound.success.source.sound": "Stopt soundz '%s\" on sourzz '%s'", "commands.stopsound.success.sourceless.any": "Stopd all teh sundz", "commands.stopsound.success.sourceless.sound": "Stupped sound %s", "commands.summon.failed": "Unable 2 summon enkitty", "commands.summon.failed.uuid": "Unebel 2 sunon dis entity cuz sam cat id", "commands.summon.invalidPosition": "<PERSON><PERSON><PERSON> canot pops into existnz ther", "commands.summon.success": "Summzund new %s", "commands.tag.add.failed": "target eithr already has teh tag or has 2 lotz da tags", "commands.tag.add.success.multiple": "Addd tag '%s' to %s intitiez", "commands.tag.add.success.single": "Addd tag '%s' to %s", "commands.tag.list.multiple.empty": "Ther r no tahz on da %s entitiz", "commands.tag.list.multiple.success": "Teh %s intitiez haz %s totel tags: %s", "commands.tag.list.single.empty": "%s haz no tagzz", "commands.tag.list.single.success": "%s haz %s tagz: %s", "commands.tag.remove.failed": "Target doez not has dis tag", "commands.tag.remove.success.multiple": "Tuk tag '%s' frum %s intitiez", "commands.tag.remove.success.single": "Removd tag '%s' frum %s", "commands.team.add.duplicate": "a team already exists by dat naym", "commands.team.add.success": "Created Kitteh Team %s", "commands.team.empty.success": "Removd %s kat(z) frum teem %s", "commands.team.empty.unchanged": "nothin changd, dat team iz already empty", "commands.team.join.success.multiple": "Added %s kats to teemz %s", "commands.team.join.success.single": "Added %s 2 teemz %s", "commands.team.leave.success.multiple": "Kickd out %s kats frum any teamz", "commands.team.leave.success.single": "Kickd out %s frum any teemz", "commands.team.list.members.empty": "Ther iz no kats on teem %s", "commands.team.list.members.success": "Teem %s haz %s kat(z): %s", "commands.team.list.teams.empty": "There r no beanz", "commands.team.list.teams.success": "Ther r %s teem(z): %s", "commands.team.option.collisionRule.success": "Bumpy rulez 4 teem %s iz naow \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON>hin changd, collishun rule iz already dat value", "commands.team.option.color.success": "Updatd teh colr fr teem %s to %s", "commands.team.option.color.unchanged": "<PERSON><PERSON> changd, dat team already has dat color", "commands.team.option.deathMessageVisibility.success": "Dying memoz shownin' 4 teem %s iz naow \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothin changd, death mesage visibility iz already dat value", "commands.team.option.friendlyfire.alreadyDisabled": "nothin changd, friendly fire iz already disabld 4 dat team", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON> changd, friendly fire iz already enabld 4 dat team", "commands.team.option.friendlyfire.disabled": "Disabld frendli fire fr teem %s", "commands.team.option.friendlyfire.enabled": "Inabld frendli fire fr teem %s", "commands.team.option.name.success": "%s haz got new naem", "commands.team.option.name.unchanged": "<PERSON>hin changd. Dat team already has dat naym", "commands.team.option.nametagVisibility.success": "Nemetag visibiliti fr teem %s r now \"%s\"", "commands.team.option.nametagVisibility.unchanged": "nothin changd, nametag visibility iz already dat value", "commands.team.option.prefix.success": "tem prfix set 2 %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "nothin changd, dat team already cant c invisable teammatez", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON>hin changd, dat team can already c invisable teammatez", "commands.team.option.seeFriendlyInvisibles.disabled": "Teem %s can no longr se envisibl teemmatez", "commands.team.option.seeFriendlyInvisibles.enabled": "Teem %s can now se envisibl teemmatez", "commands.team.option.suffix.success": "tem sufx sot 2 %s", "commands.team.remove.success": "Remoovd teem %s", "commands.teammsg.failed.noteam": "Ur muzt be on a teem 2 mesage ur teem", "commands.teleport.invalidPosition": "<PERSON><PERSON><PERSON> canot zaps tu ther", "commands.teleport.success.entity.multiple": "Teleportd %s entitez two %s", "commands.teleport.success.entity.single": "Teleportd %s 2 %s", "commands.teleport.success.location.multiple": "Teleportd %s intitiez to %s, %s, %s", "commands.teleport.success.location.single": "Teleportd %s tu %s, %s, %s", "commands.test.batch.starting": "Stahtin playgraund %s batsh %s", "commands.test.clear.error.no_tests": "Me no findy tezt 2 kler ", "commands.test.clear.success": "Klerd %s strukchur(z)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "klix tu cop 2 catbord", "commands.test.create.success": "Maed setup 4 tezt %s", "commands.test.error.no_test_containing_pos": "Me no findy tezt instenz wiv %s, %s, %s", "commands.test.error.no_test_instances": "Me no findy tezt instenzz", "commands.test.error.non_existant_test": "Me no findy tezt %s", "commands.test.error.structure_not_found": "Me no findy tezt strukchur %s", "commands.test.error.test_instance_not_found": "Me no findy tes instenz blok kretur", "commands.test.error.test_instance_not_found.position": "Me no findy tezt instenz blok kretur @ %s, %s, %s", "commands.test.error.too_large": "Teh strukr iz 2 big! Must b less than %s blox along x y z!", "commands.test.locate.done": "Finishd locatng, fuond %s srtutucre(sss)", "commands.test.locate.found": "Found structrue @ %s (but ta distnaec: %s)", "commands.test.locate.started": "Startit loCATing testuh struktur, tis might take time :O", "commands.test.no_tests": "Nu tez 2 run !1!", "commands.test.relative_position": "Posishun realtive too %s: %s", "commands.test.reset.error.no_tests": "Cuold no found any tes 2 reset", "commands.test.reset.success": "restet %s srtuctrues", "commands.test.run.no_tests": "no tes fuond :(", "commands.test.run.running": "runin %s tess(s)...", "commands.test.summary": "Gam Tes compet!1! %s tes(tz) wer ran", "commands.test.summary.all_required_passed": "alll uv te reqided tess got chezburger :)", "commands.test.summary.failed": "te %s reqided tess did no got chezburger :( ", "commands.test.summary.optional_failed": "%s optinnal tes(s) falled :(", "commands.tick.query.percentiles": "Percantlez: P50: %sms P95: %sms P99: %sms, sampel: %s", "commands.tick.query.rate.running": "Target tick raet: %s/s.\n<PERSON><PERSON> tiem per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick raet: %s/s (ignord, refernce only).\nAveraj tiem per tick: %sms", "commands.tick.rate.success": "Set teh target tik rat to %s/s", "commands.tick.sprint.report": "<PERSON><PERSON><PERSON> sped up wif %s tikz/s, or %s ms/tick", "commands.tick.sprint.stop.fail": "No tick sped up rn", "commands.tick.sprint.stop.success": "A tick sped up stopd", "commands.tick.status.frozen": "<PERSON>h gaem iz frezin' ded", "commands.tick.status.lagging": "<PERSON><PERSON> gaem iz werkin, butt teh tiks R 2 fast :(", "commands.tick.status.running": "<PERSON><PERSON> gaem runz noormaly", "commands.tick.status.sprinting": "<PERSON><PERSON> gaem is spedin' up", "commands.tick.step.fail": "<PERSON><PERSON><PERSON> kant step teh gaem :( - the gaem mus be kool first", "commands.tick.step.stop.fail": "No tik step rn", "commands.tick.step.stop.success": "A tik step stopd", "commands.tick.step.success": "Stepin' %s tick(z)", "commands.time.query": "The tiem is %s", "commands.time.set": "Taim travld 2 %s", "commands.title.cleared.multiple": "<PERSON><PERSON>rd titlez fr %s kittiez", "commands.title.cleared.single": "Deleetd Nams for %s", "commands.title.reset.multiple": "Reset titl optionz fr %s kittiez", "commands.title.reset.single": "Reset titl optionz fr %s", "commands.title.show.actionbar.multiple": "Showin new actionbar titl fr %s kittiez", "commands.title.show.actionbar.single": "Showin new actionbar titl fr %s", "commands.title.show.subtitle.multiple": "Showin new subtitl fr %s kittiez", "commands.title.show.subtitle.single": "Showin new subtitl fr %s", "commands.title.show.title.multiple": "Showin new titl fr %s kittiez", "commands.title.show.title.single": "Showin new titl fr %s", "commands.title.times.multiple": "Changd titl displai timez fr %s kittiez", "commands.title.times.single": "Changd titl displai timez fr %s", "commands.transfer.error.no_players": "<PERSON><PERSON><PERSON> needa choos at lest 1 pweyer to move awey", "commands.transfer.success.multiple": "Moven' %s katers 2 %s:%s", "commands.transfer.success.single": "Moven' %s 2 %s:%s", "commands.trigger.add.success": "Tiggerd %s (added %s 2 numburz)", "commands.trigger.failed.invalid": "u can only triggr objectivez dat r triggr type", "commands.trigger.failed.unprimed": "u cant triggr dis objectiv yet", "commands.trigger.set.success": "Tiggerd %s (made numburz %s)", "commands.trigger.simple.success": "Tiggerd %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Nu weypontz in %s", "commands.waypoint.list.success": "%s woooowpoint(z) in %s: %s", "commands.waypoint.modify.color": "Weypont colur iz nao %s", "commands.waypoint.modify.color.reset": "Reset waypontz colurrz", "commands.waypoint.modify.style": "Weypointzz styl changid", "commands.weather.set.clear": "Rainz R off", "commands.weather.set.rain": "Quiet rainz R on", "commands.weather.set.thunder": "<PERSON><PERSON> rainz <PERSON> on", "commands.whitelist.add.failed": "cat iz already whitelistd", "commands.whitelist.add.success": "Whitlist now haz %s", "commands.whitelist.alreadyOff": "whitelist iz already turnd off", "commands.whitelist.alreadyOn": "whitelist iz already turnd on", "commands.whitelist.disabled": "Witlist off :(!!!", "commands.whitelist.enabled": "Witlist now on boi", "commands.whitelist.list": "Thar r %s whitelistd kat(z): %s", "commands.whitelist.none": "Ther R no kats in teh exklusiv clubz", "commands.whitelist.reloaded": "Reloadd teh whitelist", "commands.whitelist.remove.failed": "cat iz not whitelistd", "commands.whitelist.remove.success": "Removd %s frum teh whitelist", "commands.worldborder.center.failed": "nothin changd, teh wurld bordr iz already senterd thar", "commands.worldborder.center.success": "Set teh centr for teh world bordr to %s, %s", "commands.worldborder.damage.amount.failed": "nothin changd, teh wurld bordr damage iz already dat amount", "commands.worldborder.damage.amount.success": "Set teh wurld bordr damage tiem 2 %s purr blok evry second", "commands.worldborder.damage.buffer.failed": "<PERSON>hin changd, teh wurld bordr damage buffr iz already dat distance", "commands.worldborder.damage.buffer.success": "Set teh wurld bordr damage buffr 2 %s block(z)", "commands.worldborder.get": "Teh world bordr currentle r %s blok(z) wide", "commands.worldborder.set.failed.big": "<PERSON><PERSON><PERSON> bordr cant b biggr den %s blokz wide", "commands.worldborder.set.failed.far": "<PERSON><PERSON><PERSON> bordr cant b moar away den %s blokz", "commands.worldborder.set.failed.nochange": "nothin changd, teh wurld bordr iz already dat size", "commands.worldborder.set.failed.small": "Da wrold border canot bee smaler thn 1 block wide", "commands.worldborder.set.grow": "Growin teh world bordr to %s blokz wide ovr %s secondz", "commands.worldborder.set.immediate": "Set teh wurld bordr 2 %s block(z) wide", "commands.worldborder.set.shrink": "<PERSON>nkin teh wurld bordr 2 %s block(z) wide ovar %s second(z)", "commands.worldborder.warning.distance.failed": "nothin changd, teh wurld bordr warnin iz already dat distance", "commands.worldborder.warning.distance.success": "Set teh wurld bordr warnin distance 2 %s block(z)", "commands.worldborder.warning.time.failed": "nothin changd, teh wurld bordr warnin iz already dat amount ov tiem", "commands.worldborder.warning.time.success": "Set teh wurld bordr warnin tiem 2 %s second(z)", "compliance.playtime.greaterThan24Hours": "U was playin for moar dan 24 hourz", "compliance.playtime.hours": "Yu haz bin pleyin fur %s aur(s)", "compliance.playtime.message": "GO OUTSIED N HAV A LIF", "connect.aborted": "Abort!!", "connect.authorizing": "Makin sure it's safe...", "connect.connecting": "Connectin 2 the servr...", "connect.encrypting": "Encriipthing...", "connect.failed": "Not today... :(", "connect.failed.transfer": "<PERSON><PERSON><PERSON> faild wile movin' to teh svr", "connect.joining": "Cat iz comming tu wourld...", "connect.negotiating": "NiGuZiatInj...", "connect.reconfiging": "Rekonfigawrin...", "connect.reconfiguring": "Rekonfigawrin...", "connect.transferring": "Movin' to mew svr...", "container.barrel": "Fish box", "container.beacon": "<PERSON>", "container.beehive.bees": "Buzzy Bz: %s/%s", "container.beehive.honey": "Sticky B thing: %s/%s", "container.blast_furnace": "BlAsT Hot Box", "container.brewing": "<PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "Katografy Table", "container.chest": "Kat Box", "container.chestDouble": "Exttra Bigg Cat Box", "container.crafter": "<PERSON><PERSON>", "container.crafting": "<PERSON><PERSON>", "container.creative": "Grabbeh <PERSON>uff", "container.dispenser": "Dizpnzur", "container.dropper": "DRUPPER", "container.enchant": "<PERSON><PERSON> shineh", "container.enchant.clue": "%s wat?", "container.enchant.lapis.many": "%s blu stuffz", "container.enchant.lapis.one": "1 blu stuffz", "container.enchant.level.many": "%s shineh levls", "container.enchant.level.one": "1 shineh stick", "container.enchant.level.requirement": "Lvl neded: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "warm box", "container.grindstone_title": "Fix it & Remove za shine", "container.hopper": "RUN VACUUM!", "container.inventory": "ME STUFFZ HEER", "container.isLocked": "Oh noes! %s woent open!", "container.lectern": "Lectrn", "container.loom": "Looooooo<PERSON>", "container.repair": "Fixeh & Nameh", "container.repair.cost": "Shineh <PERSON>l Cost: %1$s", "container.repair.expensive": "2 Expenciv!", "container.shulkerBox": "porttabul cat bux!!!", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "& %s moar...", "container.shulkerBox.unknownContents": "HUH?????", "container.smoker": "<PERSON><PERSON>", "container.spectatorCantOpen": "kant opn cuz thr aint no cheezburgers yet", "container.stonecutter": "Shrpy movin thingy", "container.upgrade": "<PERSON><PERSON><PERSON>", "container.upgrade.error_tooltip": "<PERSON><PERSON><PERSON> kant appgraed dis liek dis D=", "container.upgrade.missing_template_tooltip": "Putt Smissinng thingy", "controls.keybinds": "<PERSON><PERSON>s...", "controls.keybinds.duplicateKeybinds": "Dis key iz also usd 4:\n%s", "controls.keybinds.title": "Key Bindz", "controls.reset": "<PERSON><PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON><PERSON>", "controls.title": "Contrulz", "createWorld.customize.buffet.biome": "<PERSON><PERSON><PERSON> pik baium", "createWorld.customize.buffet.title": "Singel Biom Kustemizaeshun", "createWorld.customize.flat.height": "tallnes", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Battom - %s", "createWorld.customize.flat.layer.top": "High thingy - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.tile": "<PERSON><PERSON>", "createWorld.customize.flat.title": "SUPER FLAT CUSTOMIZ", "createWorld.customize.presets": "Presuts", "createWorld.customize.presets.list": "<PERSON><PERSON>, here iz da hooman presuts!", "createWorld.customize.presets.select": "Use presut", "createWorld.customize.presets.share": "SHAR UR STOOF!?1? JUST PUNCH BOX!!!!1111!\n", "createWorld.customize.presets.title": "Select a presut", "createWorld.preparing": "Cat god iz preparin ur wurld...", "createWorld.tab.game.title": "videogam", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Attribushun", "credits_and_attribution.button.credits": "Thx to", "credits_and_attribution.button.licenses": "<PERSON>z", "credits_and_attribution.screen.title": "Creditz & Attribushun", "dataPack.bundle.description": "Enebal EXPREMINTL Boxies thingz", "dataPack.bundle.name": "<PERSON> powchz", "dataPack.locator_bar.description": "Shoo teh dyrekshun ov oze playerz inn multeplayrz", "dataPack.locator_bar.name": "Low-K-tor Bar", "dataPack.minecart_improvements.description": "Bettr mofment 4 Minekatz", "dataPack.minecart_improvements.name": "Minekat POW UPZZ", "dataPack.redstone_experiments.description": "Eggsperimntal Redston chanjz", "dataPack.redstone_experiments.name": "<PERSON><PERSON>", "dataPack.title": "Selecc data packz", "dataPack.trade_rebalance.description": "Nuw twedz for Big Noz", "dataPack.trade_rebalance.name": "Villagr no mor op", "dataPack.update_1_20.description": "neu feturrs wif epik stuffz 4 KaTKrAFt 1 . two-ty!!!", "dataPack.update_1_20.name": "NEW UPDAET 1.20!1!!!1!!", "dataPack.update_1_21.description": "Neu fracurz n' epik stufz for Minecraft 1.21", "dataPack.update_1_21.name": "Updaet 1.21!!!", "dataPack.validation.back": "Go Bacc", "dataPack.validation.failed": "Data pacc validashun fayld!", "dataPack.validation.reset": "Rezet 2 defawlt", "dataPack.validation.working": "Validatin selectd data packz...", "dataPack.vanilla.description": "Teh uzual infoz", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "<PERSON>eu <PERSON>z and stuf fo teh Winter updete", "dataPack.winter_drop.name": "Snowy updet", "datapackFailure.safeMode": "Safty moed", "datapackFailure.safeMode.failed.description": "Dis wurld containz inwalid or curruptd saev stuffz.", "datapackFailure.safeMode.failed.title": "Faild 2 lod wurld in da safe mode", "datapackFailure.title": "Errurs in nau chusen datapak maek wurld no loed.\nU can loed vnaila datapak (\"Safty moed\") or go bak tu titol screhn and fix manuelly.", "death.attack.anvil": "%1$s wuz squazhd by a fallen anvehl", "death.attack.anvil.player": "%1$s was kille by anvul from sky whil protec from %2$s", "death.attack.arrow": "%1$s wuz shot by %2$s", "death.attack.arrow.item": "%1$s wuz shot by %2$s usin %3$s", "death.attack.badRespawnPoint.link": "Intnesional game desin", "death.attack.badRespawnPoint.message": "%1$s was kill by %2$s", "death.attack.cactus": "%1$s wuz prickd 2 death", "death.attack.cactus.player": "%1$s walkd intu a spiky green plant whil tryin 2 escaep %2$s", "death.attack.cramming": "%1$s gut turnd intu mashd potato", "death.attack.cramming.player": "%1$s wus skwash by %2$s", "death.attack.dragonBreath": "%1$s wuz roastd in dragonz breath", "death.attack.dragonBreath.player": "%1$s wuz roastd in dragonz breath by %2$s", "death.attack.drown": "%1$s drownd when dey took a bath", "death.attack.drown.player": "%1$s took a showuh whil tryin 2 escape %2$s", "death.attack.dryout": "%1$s ded from no wotr", "death.attack.dryout.player": "%1$s ded bc ov no watr whil tryin 2 escape %2$s", "death.attack.even_more_magic": "%1$s iz ded by lotz of magikz", "death.attack.explosion": "%1$s xploded", "death.attack.explosion.player": "%1$s waz xploded by %2$s", "death.attack.explosion.player.item": "%1$s was boom by %2$s usin %3$s", "death.attack.fall": "%1$s hit teh ground 2 hard", "death.attack.fall.player": "%1$s trippd tryna ezcayp frum %2$s", "death.attack.fallingBlock": "%1$s wuz squashd by fallin kat", "death.attack.fallingBlock.player": "%1$s was killd by kitteh from teh sky whil skartchin' %2$s", "death.attack.fallingStalactite": "%1$s waz kut in half by foling sharp cave rok", "death.attack.fallingStalactite.player": "%1$s waz kut in half by foling sharp cave rok whil scratchin at %2$s", "death.attack.fireball": "%1$s wuz fireballd by %2$s", "death.attack.fireball.item": "%1$s wuz fireballd %2$s usin %3$s", "death.attack.fireworks": "%1$s iz ded cuz BOOM", "death.attack.fireworks.item": "%1$s exploded cuz a firwork ran awy frum %3$s bie %2$s", "death.attack.fireworks.player": "%1$s got boomed by fiarwok whil skraching %2$s", "death.attack.flyIntoWall": "%1$s flu in2 a wal", "death.attack.flyIntoWall.player": "%1$s flew 2 wall cuz of %2$s", "death.attack.freeze": "%1$s waz 2 kold and died", "death.attack.freeze.player": "%1$s waz 2 kold and died bcuz of %2$s", "death.attack.generic": "R.I.P %1$s", "death.attack.generic.player": "%1$s ded cuz %2$s", "death.attack.genericKill": "%1$s wuz dieded :(((", "death.attack.genericKill.player": "%1$s wuz ded whil scratchin' %2$s", "death.attack.hotFloor": "%1$s fund ut flor was laav", "death.attack.hotFloor.player": "%1$s walkd into teh dangr zone due 2 %2$s", "death.attack.inFire": "%1$s walkd intu a fyre n dyed", "death.attack.inFire.player": "%1$s walkd 2 fier wile fiting with %2$s", "death.attack.inWall": "%1$s deid in da wahls", "death.attack.inWall.player": "%1$s wuz burid alife whil fiting %2$s", "death.attack.indirectMagic": "%1$s was killd by %2$s usin magikz", "death.attack.indirectMagic.item": "%1$s was killd by %2$s usin %3$s", "death.attack.lava": "%1$s thawt thay cud swim in hot sauce", "death.attack.lava.player": "%1$s treid 2 swim in hot sauce to git away frum %2$s", "death.attack.lightningBolt": "%1$s waz struck by lightnin", "death.attack.lightningBolt.player": "%1$s ded by ligtning wen figting %2$s", "death.attack.mace_smash": "%1$s was boinkd into a piz of papr by %2$s", "death.attack.mace_smash.item": "%1$s was turnd into papur by %2$s wif %3$s", "death.attack.magic": "%1$s was killd by magikz", "death.attack.magic.player": "%1$s wahs kaboosh'd whil tryin 2 escaep %2$s", "death.attack.message_too_long": "Da msg wuz 2 long 2 snd fuly. Sry! Hers lessd vrsion: %s", "death.attack.mob": "%1$s was scretchd bai %2$s", "death.attack.mob.item": "%1$s was scratchd to ded by %2$s usin %3$s", "death.attack.onFire": "%1$s died in a fier", "death.attack.onFire.item": "%1$s was fiting  %2$s wen thay BURND ALIEV wildin %3$s!!!", "death.attack.onFire.player": "%1$s was fiting %2$s wen thay BURND ALIEV!!!", "death.attack.outOfWorld": "%1$s fell out ov teh wurld", "death.attack.outOfWorld.player": "%1$s not want to liv in sam world as %2$s", "death.attack.outsideBorder": "%1$s gone to da dedzone", "death.attack.outsideBorder.player": "%1$s gon to da dedzone while scratchin %2$s", "death.attack.player": "%1$s was scretchd bai %2$s", "death.attack.player.item": "%1$s was scratchd to ded by %2$s usin %3$s", "death.attack.sonic_boom": "%1$s was booomed bai a sunically-charged EPIG ANIME ATTEK!!!!!!!~~", "death.attack.sonic_boom.item": "%1$s was booooomed by a sunically-charged ANIME ATTECK while tring 2 hid from %2$s wildin %3$s!!!", "death.attack.sonic_boom.player": "%1$s was booooomed by teh ANIME ATTECK while trin 2 hid from %2$s", "death.attack.stalagmite": "%1$s waz hurt bi sharp cave rokk", "death.attack.stalagmite.player": "%1$s waz hurt bi sharp cave rokk whil scratchin at %2$s", "death.attack.starve": "%1$s starvd 2 death", "death.attack.starve.player": "%1$s iz tooo hugry whil fitghting %2$s", "death.attack.sting": "%1$s was piereced to DETH", "death.attack.sting.item": "%1$s wuz poked 2 deth by %2$s usin %3$s", "death.attack.sting.player": " %2$s piereced %1$s to DETH", "death.attack.sweetBerryBush": "%1$s was poked to deth by a sweet bery bush", "death.attack.sweetBerryBush.player": "%1$s was pokd to deth by a sweet bery bush whil trying to escepe %2$s", "death.attack.thorns": "%1$s got karmuh twying too damuj %2$s", "death.attack.thorns.item": "%1$s was killd tryin by %3$s 2 hurt %2$s", "death.attack.thrown": "%1$s was pumeld by %2$s", "death.attack.thrown.item": "%1$s was pumeld by %2$s usin %3$s", "death.attack.trident": "%1$s was vigorously poked by %2$s", "death.attack.trident.item": "%1$s was imPAled cos of %2$s and iz %3$s", "death.attack.wither": "%1$s withrd away lol", "death.attack.wither.player": "%1$s withrd away while protecting us against %2$s", "death.attack.witherSkull": "%1$s vus shhoott bai 1 skall frem %2$s", "death.attack.witherSkull.item": "%1$s wus shhoott bai 1 skall frem %2$s usin %3$s", "death.fell.accident.generic": "%1$s fell frum high place", "death.fell.accident.ladder": "%1$s fell off laddr an doan landd on fed", "death.fell.accident.other_climbable": "%1$s fell whil clymbin", "death.fell.accident.scaffolding": "%1$s dropd frm a climby thing", "death.fell.accident.twisting_vines": "%1$s kuldnt huld da spyral noodel", "death.fell.accident.vines": "%1$s fell off sum vinez", "death.fell.accident.weeping_vines": "%1$s kuldnt huld da sad noodel", "death.fell.assist": "%1$s wuz doomd 2 fall by %2$s", "death.fell.assist.item": "%1$s wuz doomd 2 fall by %2$s usin %3$s", "death.fell.finish": "%1$s fell 2 far an wuz finishd by %2$s", "death.fell.finish.item": "%1$s fell 2 far an wuz finishd by %2$s usin %3$s", "death.fell.killer": "%1$s waz doomd 2 fall", "deathScreen.quit.confirm": "R u sure u wants 2 quit?", "deathScreen.respawn": "<PERSON><PERSON><PERSON>", "deathScreen.score": "ur pointz", "deathScreen.score.value": "ur pointz: %s", "deathScreen.spectate": "Spec wurld lol", "deathScreen.title": "<PERSON> dies, sad kitteh :c", "deathScreen.title.hardcore": "Gae<PERSON> ova, lol", "deathScreen.titleScreen": "Da Big Menu", "debug.advanced_tooltips.help": "F3 + H = Advancd tooltipz", "debug.advanced_tooltips.off": "Advancd tooltipz: nah", "debug.advanced_tooltips.on": "Advancd tooltipz: yea", "debug.chunk_boundaries.help": "F3 + G = maek linez 'round pieczs", "debug.chunk_boundaries.off": "Chunk line thingys: Nah", "debug.chunk_boundaries.on": "Chunk line thingys: Yea", "debug.clear_chat.help": "F3 + D = forget wat every1 is sayin", "debug.copy_location.help": "f3 + 3 = copi locatoin as /tp command, hold f3 + c to mak gam go pasta!!", "debug.copy_location.message": "Coopieed luucation tO clappbirb", "debug.crash.message": "f3 + c i prssed. dis wil mak gam go pasta unles rilizd", "debug.crash.warning": "<PERSON><PERSON> guorkin in %s...", "debug.creative_spectator.error": "U shall nawt chaeng ur gaem mode!", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON>e gaem mode <-> spetaror", "debug.dump_dynamic_textures": "Savd dinamix textuurz 2 %s", "debug.dump_dynamic_textures.help": "f3 + s = dump dinamix textuurz", "debug.gamemodes.error": "Uh oh, no swichin permishunz", "debug.gamemodes.help": "f3 and f4 = open cat mood changer", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Nekzt", "debug.help.help": "F3 + Q = Wat ur lookin at rite nao", "debug.help.message": "<PERSON><PERSON>:", "debug.inspect.client.block": "Copid client-side block data 2 clipbord", "debug.inspect.client.entity": "Copid client-side entity data 2 clipbord", "debug.inspect.help": "F3 + I = Copy entity or blukz data 2 clipboard", "debug.inspect.server.block": "Copid servr-side block data 2 clipbord", "debug.inspect.server.entity": "Copid servr-side entity data 2 clipbord", "debug.pause.help": "F3 + Esc = Paws wifut da menu (cant if cant paws)", "debug.pause_focus.help": "F3 + P = Paws wen no focusss", "debug.pause_focus.off": "Paws wen no focus: nah", "debug.pause_focus.on": "Paws wen no focus: yea", "debug.prefix": "[Dedog]:", "debug.profiling.help": "F3 + L = Statr/stup your catt saving", "debug.profiling.start": "Profilin startd fr %s secundz. Us F3 + L to stop eerle", "debug.profiling.stop": "The Grand Finaly of Profilin'. saivd rezults too %s", "debug.reload_chunks.help": "F3 + A = mak all piecz reloed", "debug.reload_chunks.message": "Re<PERSON>edin all pieczs", "debug.reload_resourcepacks.help": "F3 + T = Reloed visualz", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON>z", "debug.show_hitboxes.help": "F3 + B = put all stuffz in glas box", "debug.show_hitboxes.off": "Stuff is in glass boxs: nah", "debug.show_hitboxes.on": "Stuff is in glass boxs: yea", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = clint verzion en4", "demo.day.1": "Dis cat demo wil lust 5 gaem daez, so do ur best!", "demo.day.2": "DAY 2", "demo.day.3": "DAY 3", "demo.day.4": "DAY 4", "demo.day.5": "DIS UR LAST DAYZ!!", "demo.day.6": "U have pasd ur 5th dae. Uz %s 2 saev a screenshawt of ur kreashun.", "demo.day.warning": "UR TIEM IZ ALMOST UP!", "demo.demoExpired": "DEMO TIEMZ UP!", "demo.help.buy": "Buy nao!", "demo.help.fullWrapped": "Dis demo's gunna last 5 in-gaem-daez (bout 1 oure nd 40 minits of kitteh tiem). <PERSON><PERSON><PERSON> le advaensmens 4 hintz! Haev fun!", "demo.help.inventory": "Uze '%1$s' 2 luk at your stuff", "demo.help.jump": "Jump with tha %1$s-key", "demo.help.later": "Go on playin!", "demo.help.movement": "UZ %1$s, %2$s, %3$s, %4$s N TASTI MOUZEZ 2 MOVE ARUND", "demo.help.movementMouse": "Lu<PERSON> arund wif teh mouz", "demo.help.movementShort": "MOV BI PRESING %1$s, %2$s, %3$s N %4$s", "demo.help.title": "DEMO MODE lolz", "demo.remainingTime": "REMENING TIEM: %s", "demo.reminder": "Za demo tiem haz ekspird. Buy da gaem 2 kontinu or start 1 new wurld!", "difficulty.lock.question": "R u sure u wants 2 lock teh difficulty ov dis wurld ? Dis will set dis wurld 2 always be %1$s,na u will nevr be able 2 change dat again!", "difficulty.lock.title": "<PERSON> <PERSON><PERSON><PERSON>", "disconnect.endOfStream": "Ent ov striam", "disconnect.exceeded_packet_rate": "Kick'd 4 eks<PERSON><PERSON> paket ratd limet", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Dont caer stats rekest", "disconnect.loginFailedInfo": "Fled to lug in: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON> kitte<PERSON>z is dizabl, pwease chek ur Mekrosoft akowant setinz.", "disconnect.loginFailedInfo.invalidSession": "sezzion nawt workz (restart ur gaem & lawnchr)", "disconnect.loginFailedInfo.serversUnavailable": "coodnt reach authentikashn servers! plz try again.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "TEH CONNECTION BLUW UP", "disconnect.packetError": "Netwurk Protokul Erur", "disconnect.spam": "Meow, sthap spammin'", "disconnect.timeout": "took 2 long", "disconnect.transfer": "Arrivd in anuther svr", "disconnect.unknownHost": "<PERSON><PERSON><PERSON> doezn't know da hozt", "download.pack.failed": "%s aut uv %s paks said no", "download.pack.progress.bytes": "Progrezs: %s (idk rly tho)", "download.pack.progress.percent": "Progresz: %s%%", "download.pack.title": "Dawnlodin stuffpak %s/%s", "editGamerule.default": "Da basikz: %s", "editGamerule.title": "EDET gaem Rulez", "effect.duration.infinite": "forevah", "effect.minecraft.absorption": "absurbshun", "effect.minecraft.bad_omen": "<PERSON> kitteh", "effect.minecraft.blindness": "I cant see anything", "effect.minecraft.conduit_power": "<PERSON> powah", "effect.minecraft.darkness": "kripi fog", "effect.minecraft.dolphins_grace": "Greis of a Dolphin", "effect.minecraft.fire_resistance": "Fire Rezistance", "effect.minecraft.glowing": "Makes U Shiny", "effect.minecraft.haste": "IM MININ FAST", "effect.minecraft.health_boost": "10th Life", "effect.minecraft.hero_of_the_village": "Da Hero ov teh Village", "effect.minecraft.hunger": "Need nomz", "effect.minecraft.infested": "Stikeh bug insaid", "effect.minecraft.instant_damage": "Instant Ouch", "effect.minecraft.instant_health": "insta sheezburgerz", "effect.minecraft.invisibility": "U cant see meh", "effect.minecraft.jump_boost": "Bunny cat", "effect.minecraft.levitation": "Hoverz", "effect.minecraft.luck": "LOL", "effect.minecraft.mining_fatigue": "fu wana stehp minin", "effect.minecraft.nausea": "Sik cat", "effect.minecraft.night_vision": "<PERSON>", "effect.minecraft.oozing": "Gren thing insaid", "effect.minecraft.poison": "Puizn", "effect.minecraft.raid_omen": "<PERSON><PERSON>", "effect.minecraft.regeneration": "Time <PERSON><PERSON>", "effect.minecraft.resistance": "Rezistance", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "Slowmo fall", "effect.minecraft.slowness": "fatnes", "effect.minecraft.speed": "spede", "effect.minecraft.strength": "Powah", "effect.minecraft.trial_omen": "Dunguhn DLC", "effect.minecraft.unluck": "NOT LOL", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "Fat cat", "effect.minecraft.weaving": "Spaidr silk insaid", "effect.minecraft.wind_charged": "Wind boom insaid", "effect.minecraft.wither": "wiith<PERSON>r", "effect.none": "No Effects to dis cat", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Kitteh no like water", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON>ey killey thingy", "enchantment.minecraft.binding_curse": "cant taek dis off", "enchantment.minecraft.blast_protection": "Blast Protecshun", "enchantment.minecraft.breach": "Bleach", "enchantment.minecraft.channeling": "Being thor", "enchantment.minecraft.density": "Thiccnez", "enchantment.minecraft.depth_strider": "Fuzt Watur Wullken", "enchantment.minecraft.efficiency": "Effishincy", "enchantment.minecraft.feather_falling": "Fall on your feet", "enchantment.minecraft.fire_aspect": "<PERSON>er Aspekt", "enchantment.minecraft.fire_protection": "<PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "Forshun", "enchantment.minecraft.frost_walker": "cat no liek water hax", "enchantment.minecraft.impaling": "Idk lmao", "enchantment.minecraft.infinity": "FOREVERS", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Luting", "enchantment.minecraft.loyalty": "BFF", "enchantment.minecraft.luck_of_the_sea": "Luk ov tha See", "enchantment.minecraft.lure": "<PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON>", "enchantment.minecraft.multishot": "Manysot", "enchantment.minecraft.piercing": "Errow gous thru", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Projektile Protecshun", "enchantment.minecraft.protection": "Protecshun", "enchantment.minecraft.punch": "Punsh", "enchantment.minecraft.quick_charge": "Qwick Charge", "enchantment.minecraft.respiration": "fish moed", "enchantment.minecraft.riptide": "ZOOOM", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Smooth Diggin'", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "rannin on ded peepl", "enchantment.minecraft.sweeping": "Sweeper Deeper", "enchantment.minecraft.sweeping_edge": "Sweeper Deeper", "enchantment.minecraft.swift_sneak": "SNEK SPEEDRUN!!", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Unbraking", "enchantment.minecraft.vanishing_curse": "wen u getrekt its gon", "enchantment.minecraft.wind_burst": "Wind Booom", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> W<PERSON>r Car", "entity.minecraft.acacia_chest_boat": "Acashuh Watr Car w/ Cat Box", "entity.minecraft.allay": "flyin blu mob", "entity.minecraft.area_effect_cloud": "Area Effect Cloud", "entity.minecraft.armadillo": "Hard Bawl", "entity.minecraft.armor_stand": "Stick hooman", "entity.minecraft.arrow": "ERROW", "entity.minecraft.axolotl": "KUTE PINK FISHH", "entity.minecraft.bamboo_chest_raft": "<PERSON><PERSON> Watuh Ka w/ Kat box", "entity.minecraft.bamboo_raft": "Stik Wat<PERSON> Ka<PERSON>", "entity.minecraft.bat": "<PERSON>", "entity.minecraft.bee": "B", "entity.minecraft.birch_boat": "Burch Watr Car", "entity.minecraft.birch_chest_boat": "Burch Watr Car w/ Cat Box", "entity.minecraft.blaze": "OMG it's made of fier", "entity.minecraft.block_display": "Blok Dizplae", "entity.minecraft.boat": "Watr car", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Brrrrrrr guy", "entity.minecraft.breeze_wind_charge": "Wind Attacc", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON>e paneful spidur", "entity.minecraft.cherry_boat": "Sakura watr car", "entity.minecraft.cherry_chest_boat": "Sakura Watr Car w/ Cat Box", "entity.minecraft.chest_boat": "Watr Car w/ Bokz", "entity.minecraft.chest_minecart": "Minecat wif Cat Box", "entity.minecraft.chicken": "Bawk Bawk!", "entity.minecraft.cod": "Cot", "entity.minecraft.command_block_minecart": "Minecat wif Comnd Bluk", "entity.minecraft.cow": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking": "Scawy twee", "entity.minecraft.creaking_transient": "Scawy twee", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Blak Oke Watr Car", "entity.minecraft.dark_oak_chest_boat": "Blak Oak Watr Car w/ Cat Box", "entity.minecraft.dolphin": "doolfin", "entity.minecraft.donkey": "Donkeh", "entity.minecraft.dragon_fireball": "Dragunish burp", "entity.minecraft.drowned": "watuur thing", "entity.minecraft.egg": "Fhrown Egg", "entity.minecraft.elder_guardian": "BIG LAZUR SHARK", "entity.minecraft.end_crystal": "no touchy cryystal", "entity.minecraft.ender_dragon": "Dwagon Bos", "entity.minecraft.ender_pearl": "<PERSON><PERSON> <PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Wiz<PERSON>", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Thraun potion wif ur levlz", "entity.minecraft.experience_orb": "Lvl ballz", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> evil eye", "entity.minecraft.falling_block": "<PERSON><PERSON><PERSON>", "entity.minecraft.falling_block_type": "Fallin %s", "entity.minecraft.fireball": "No touchies", "entity.minecraft.firework_rocket": "shiny 'splody thing", "entity.minecraft.fishing_bobber": "watuur toy", "entity.minecraft.fox": "Fuxe", "entity.minecraft.frog": "Toad", "entity.minecraft.furnace_minecart": "Minecat wif Hot Box", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "OMG ZOMBIE 4 DAYZ", "entity.minecraft.glow_item_frame": "Brite item holdr", "entity.minecraft.glow_squid": "<PERSON>y skwid", "entity.minecraft.goat": "monten shep", "entity.minecraft.guardian": "SHARKS WITH LAZERS", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "minecat wif HUPPEH", "entity.minecraft.horse": "PONY", "entity.minecraft.husk": "Warm hooman", "entity.minecraft.illusioner": "Wiizardur", "entity.minecraft.interaction": "interacshun", "entity.minecraft.iron_golem": "<PERSON>", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "Thingz diszplae", "entity.minecraft.item_frame": "kitteh fraem", "entity.minecraft.jungle_boat": "<PERSON><PERSON> W<PERSON>r Car", "entity.minecraft.jungle_chest_boat": "Junglz Watr Car w/ Cat Box", "entity.minecraft.killer_bunny": "Scari jumpin foodz", "entity.minecraft.leash_knot": "Leesh knot", "entity.minecraft.lightning_bolt": "litnin bot", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.llama": "camel sheep", "entity.minecraft.llama_spit": "tal goat spit", "entity.minecraft.magma_cube": "Fier sliem", "entity.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "Mangroff Watr Car w/ Cat Box", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Minecat", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Donke<PERSON>ony", "entity.minecraft.oak_boat": "Ouk Watr Car", "entity.minecraft.oak_chest_boat": "Oak Watr Car w/ Cat Box", "entity.minecraft.ocelot": "Wild kitteh", "entity.minecraft.ominous_item_spawner": "Skery stuff maekr", "entity.minecraft.painting": "hangabl arting", "entity.minecraft.pale_oak_boat": "Wite Ok Watur Kar", "entity.minecraft.pale_oak_chest_boat": "Wite Ok Watr Car w/ <PERSON>z", "entity.minecraft.panda": "Green Stick Eatar", "entity.minecraft.parrot": "<PERSON>", "entity.minecraft.phantom": "Creepy flyin ting", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON>", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON>", "entity.minecraft.player": "Cat", "entity.minecraft.polar_bear": "Wite beer", "entity.minecraft.potion": "Poshun", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "Baa Baa!", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON> bullit", "entity.minecraft.silverfish": "Grae fish", "entity.minecraft.skeleton": "Spooke scury <PERSON>", "entity.minecraft.skeleton_horse": "Spooke scury <PERSON><PERSON>", "entity.minecraft.slime": "Sliem", "entity.minecraft.small_fireball": "Tiny nope", "entity.minecraft.sniffer": "S<PERSON>ff<PERSON>", "entity.minecraft.snow_golem": "Cold Watr Hooman", "entity.minecraft.snowball": "Cold wet", "entity.minecraft.spawner_minecart": "Minecat wit spawnr", "entity.minecraft.spectral_arrow": "<PERSON><PERSON>", "entity.minecraft.spider": "Spydur", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON> poshun", "entity.minecraft.spruce_boat": "Sproos Watr Car", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON> Watr Ka w/ Cat Box", "entity.minecraft.squid": "Sqyd", "entity.minecraft.stray": "Frozen Skeletun", "entity.minecraft.strider": "mr hoo walx on laava", "entity.minecraft.tadpole": "Frogfish", "entity.minecraft.text_display": "<PERSON>z Showin", "entity.minecraft.tnt": "Primeld TNT", "entity.minecraft.tnt_minecart": "<PERSON>y <PERSON>", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Dinglehopper", "entity.minecraft.tropical_fish": "Topicel fis", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "<PERSON> Sickled", "entity.minecraft.tropical_fish.predefined.15": "Rd Lippd Bleny", "entity.minecraft.tropical_fish.predefined.16": "<PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "Red Funny Fishy", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON> bird fishy", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "Buttrfli Fsh", "entity.minecraft.tropical_fish.predefined.4": "Sicklid", "entity.minecraft.tropical_fish.predefined.5": "funny fishy", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON> candeh fishy", "entity.minecraft.tropical_fish.predefined.7": "Dotts in the back", "entity.minecraft.tropical_fish.predefined.8": "<PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Gowt fishy", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blouckfish", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON>ley fishy", "entity.minecraft.tropical_fish.type.dasher": "Runnr", "entity.minecraft.tropical_fish.type.flopper": "Floppy", "entity.minecraft.tropical_fish.type.glitter": "Glitur", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snopr", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "stipor", "entity.minecraft.tropical_fish.type.sunstreak": "Sunzxcnjsa", "entity.minecraft.turtle": "TortL", "entity.minecraft.vex": "ghosty thingy", "entity.minecraft.villager": "Vilaagur", "entity.minecraft.villager.armorer": "Armurur", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Mapmakr", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON>r", "entity.minecraft.villager.fisherman": "Foodz suply", "entity.minecraft.villager.fletcher": "Fletchur", "entity.minecraft.villager.leatherworker": "Let<PERSON>wu<PERSON><PERSON>", "entity.minecraft.villager.librarian": "Nerdz", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "shtOOpid man", "entity.minecraft.villager.none": "Vilaagur", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "We<PERSON>nsmif", "entity.minecraft.vindicator": "Bad Guy", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "<PERSON> shrek", "entity.minecraft.wind_charge": "Wind Attacc", "entity.minecraft.witch": "Mean old lade", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Spooke Wither <PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "Woof Woof!", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> hoers", "entity.minecraft.zombie_villager": "Unded <PERSON>guur", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON>", "entity.not_summonable": "cant spon entiti of tipe %s", "event.minecraft.raid": "<PERSON><PERSON>", "event.minecraft.raid.defeat": "Defeet", "event.minecraft.raid.defeat.full": "Raid - gg :(", "event.minecraft.raid.raiders_remaining": "Raiderz stieel alaiv: %s", "event.minecraft.raid.victory": "ezpz", "event.minecraft.raid.victory.full": "Raid - #1 victory royael", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Jungel Explurer Map", "filled_map.explorer_swamp": "Zwump Explurer Map", "filled_map.id": "Id #%s", "filled_map.level": "(lvl %s/%s)", "filled_map.locked": "LOCCD", "filled_map.mansion": "WOOdland explurer map", "filled_map.monument": "Oshun explurer map", "filled_map.scale": "<PERSON><PERSON><PERSON> @ 1:%s", "filled_map.trial_chambers": "Dungenz Explurer Map", "filled_map.unknown": "Idk dis map", "filled_map.village_desert": "<PERSON><PERSON>", "filled_map.village_plains": "<PERSON><PERSON>", "filled_map.village_savanna": "<PERSON><PERSON>", "filled_map.village_snowy": "Snowwy Villzage Map", "filled_map.village_taiga": "Tiga Villaj Map", "flat_world_preset.minecraft.bottomless_pit": "Da PIT Of FALLiNG DOWN", "flat_world_preset.minecraft.classic_flat": "DA OG Flat", "flat_world_preset.minecraft.desert": "Sandy Place", "flat_world_preset.minecraft.overworld": "ON TOp WUrlD", "flat_world_preset.minecraft.redstone_ready": "<PERSON> fer dat Redstone", "flat_world_preset.minecraft.snowy_kingdom": "Kingdom ef Snows", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Tunnel Manz Dream", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Catventure Moed", "gameMode.changed": "Ur gaem mowd has bin apdated tu %s", "gameMode.creative": "HAX MOD", "gameMode.hardcore": "Catcore Moed", "gameMode.spectator": "Spectutor <PERSON>", "gameMode.survival": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Tik fiar away frum kittehzz", "gamerule.allowFireTicksAwayFromPlayer.description": "Kontrollz whetha / not fiar & hot salsa shuld b abol 2 tik furtha then 8 chunkz awsy frum any fellow kittehz", "gamerule.announceAdvancements": "Annunc advencement", "gamerule.blockExplosionDropDecay": "Ien <PERSON> kaboms slom cubs u ll naw dlup thil loties", "gamerule.blockExplosionDropDecay.description": "sum blukz drups arr hafe gon buai xplodez frum bluk interakshunz.", "gamerule.category.chat": "chatties", "gamerule.category.drops": "dops", "gamerule.category.misc": "ooneek tings", "gamerule.category.mobs": "<PERSON><PERSON><PERSON> cweeters", "gamerule.category.player": "Cat", "gamerule.category.spawning": "Zponing", "gamerule.category.updates": "<PERSON><PERSON><PERSON> updatz", "gamerule.commandBlockOutput": "Broadcatz command blocky bois output thingy", "gamerule.commandModificationBlockLimit": "Comand mod blok limit", "gamerule.commandModificationBlockLimit.description": "huw mani bloccs yuo can chenge wit wun paw", "gamerule.disableElytraMovementCheck": "Nou moar FLYYY", "gamerule.disablePlayerMovementCheck": "Nou moar hopz chek of catz", "gamerule.disableRaids": "no raidz", "gamerule.doDaylightCycle": "advanc tiem ov dai", "gamerule.doEntityDrops": "<PERSON>p nenity kuitmenp", "gamerule.doEntityDrops.description": "Kontrol dropz from mincartz (inclooding invantoriez), item framz, boat stuffs, etc.", "gamerule.doFireTick": "Updat burny stuff", "gamerule.doImmediateRespawn": "Reezpon fazt", "gamerule.doInsomnia": "Spahn creepy flyin ting", "gamerule.doLimitedCrafting": "Requaire recipee 4 kraftingz", "gamerule.doLimitedCrafting.description": "If enabaled, cats will bee able 2 kraft onlee unlokd recipiez.", "gamerule.doMobLoot": "Drop cat goodies", "gamerule.doMobLoot.description": "Twakes wower fnacy pants frum mobz, inclubing xp orbz.", "gamerule.doMobSpawning": "Zpon tings", "gamerule.doMobSpawning.description": "Som real-tingz mait havv diffrnt rwlez.", "gamerule.doPatrolSpawning": "Zpon ugly men wit krosbou", "gamerule.doTileDrops": "Drop blockz", "gamerule.doTileDrops.description": "Twakes wower fnacy pants frum mobz, inclubing xp orbz.", "gamerule.doTraderSpawning": "Spon Walkin <PERSON>rz", "gamerule.doVinesSpread": "climbin plantz RUN TO EVRYWERE D:", "gamerule.doVinesSpread.description": "Kontrlz whenevr or not the Climbin Plantz runz away to EVRYWERE randumly 2 the any blocz suhc as cryin roof nooodlez :( , spyrale noodlez , more n more", "gamerule.doWardenSpawning": "Spon Blu Shrek", "gamerule.doWeatherCycle": "Updat wetder", "gamerule.drowningDamage": "Deel 2 long in watr damaj", "gamerule.enderPearlsVanishOnDeath": "Yeeted endwer-pearl<PERSON> van<PERSON>sh on die", "gamerule.enderPearlsVanishOnDeath.description": "If endwer-pearlz yeeted by da player vanizsh when de player go die die.", "gamerule.entitiesWithPassengersCanUsePortals": "Enttiz wiv pasengerz cn uze potrals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Let entitt dat hav pasengrer 2 warpz thru all da magik portlols.", "gamerule.fallDamage": "Deel fall damaj", "gamerule.fireDamage": "Deel hot stuff damage", "gamerule.forgiveDeadPlayers": "Forgiev ded kittehz", "gamerule.forgiveDeadPlayers.description": "Angerd neutrl mobz stop goin angery moed wehn teh targetd playr oofz near<PERSON>i.", "gamerule.freezeDamage": "do kold pain", "gamerule.globalSoundEvents": "<PERSON><PERSON> lo<PERSON> noizes", "gamerule.globalSoundEvents.description": "Wen da event, liek da bauz spawnin, maek veri BiiiG saund evriwher.", "gamerule.keepInventory": "Keepz invantorie aftur no livez", "gamerule.lavaSourceConversion": "hot sauce konvert 2 MOAR hot sauce", "gamerule.lavaSourceConversion.description": "Win floing hot stuffz iz surrounded on 2 side by hot stuffz sourcez change in 2 source.", "gamerule.locatorBar": "Uz playr low-k-t0r bar", "gamerule.locatorBar.description": "Whin enbled, a bar is shuwn in z scrin 2 indiecate z deraction of playerz.", "gamerule.logAdminCommands": "Broadcatz Admin orderz", "gamerule.maxCommandChainLength": "Comnd chainz size limtz", "gamerule.maxCommandChainLength.description": "Applizes to comand blockz tingy chainz and functionz.", "gamerule.maxCommandForkCount": "Kommand contekst limit", "gamerule.maxCommandForkCount.description": "<PERSON> numbr ov contekstsz dat kan B usd bi kommandz liek \"execute as\".", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON> cramin trushhold", "gamerule.minecartMaxSpeed": "Minecat max faztnes", "gamerule.minecartMaxSpeed.description": "<PERSON><PERSON><PERSON> defalt sped of a mofin' Mainkat on land.", "gamerule.mobExplosionDropDecay": "In da mob kaboomz sum blukz ull naw dwap their loties", "gamerule.mobExplosionDropDecay.description": "Sum ov teh dropz frum blockz destroyd by explosions caused by mobs r lost in teh explosion.", "gamerule.mobGriefing": "Alow anmgry mob actionz", "gamerule.naturalRegeneration": "Regenrat kat loivs", "gamerule.playersNetherPortalCreativeDelay": "Kitteh'z Nethr portul deley in kretiv mode", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in tiqz) that a kretiv mode kitteh needz to stand in a Nethr portul b4 chanjin' dimanshunz.", "gamerule.playersNetherPortalDefaultDelay": "Kitteh'z Nethr portul deley in non-kretiv mode", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in tiqz) that a non-kretiv mode kitteh needz to stand in a Nethr portul b4 chanjin' dimanshunz.", "gamerule.playersSleepingPercentage": "Sleepy katz percentg", "gamerule.playersSleepingPercentage.description": "Kat percentge that must sleep 2 nite go byeeeeeeeee", "gamerule.projectilesCanBreakBlocks": "Projektilz kan brek blocz", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON><PERSON> whethr impac projetils will destroy blocz that kan be deztroy by them.", "gamerule.randomTickSpeed": "Random tickz speed rat", "gamerule.reducedDebugInfo": "reduc debugz infos", "gamerule.reducedDebugInfo.description": "Limitz contentz ov deeBUG screen.", "gamerule.sendCommandFeedback": "<PERSON><PERSON> comand responz", "gamerule.showDeathMessages": "show ded katz", "gamerule.snowAccumulationHeight": "Sno pauer levelz", "gamerule.snowAccumulationHeight.description": "wen its tiem 2 snowy, snowynez ov snowy padz wil b abel 2 exist om da grnd up 2 dis numbr ov padz at mozt!!!", "gamerule.spawnChunkRadius": "Spawn chuck raduz", "gamerule.spawnChunkRadius.description": "Amont of chunx that stay loaded around teh ovrwuld spawn plac.", "gamerule.spawnRadius": "Reezpon pleac raduz", "gamerule.spawnRadius.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> teh big-ness of teh area arond teh span pont tat playrz kan span in.", "gamerule.spectatorsGenerateChunks": "Alow spectatwor kittehs to generat terainz", "gamerule.tntExplodes": "TNT goes BOOM!", "gamerule.tntExplosionDropDecay": "Ien Boom kaboms som cubs u ll naw dlup thil loties", "gamerule.tntExplosionDropDecay.description": "Sum of teh dropz from blockz destroyed by kaboomz caused by boom r lost in teh BOOM.", "gamerule.universalAnger": "Big Angery!", "gamerule.universalAnger.description": "Angerd neutrl mobz attacc ani neerbai playr, nut juzt teh playr dat angrd dem. Workz bezt if forgievDedKittehz iz dizabld.", "gamerule.waterSourceConversion": "watr konvert 2 surz", "gamerule.waterSourceConversion.description": "Win floing watr iz surrounded on 2 side by watrr sourcez it changez in2 source.", "generator.custom": "Kustom", "generator.customized": "<PERSON><PERSON><PERSON> frum da past", "generator.minecraft.amplified": "BIGGUR", "generator.minecraft.amplified.info": "Notis: Just 4 fun! <PERSON><PERSON><PERSON><PERSON> a muscular compootr.", "generator.minecraft.debug_all_block_states": "DBUG MOD", "generator.minecraft.flat": "2 flatz 4 u", "generator.minecraft.large_biomes": "bigur baiums", "generator.minecraft.normal": "<PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Onli 1 biomz", "generator.single_biome_caves": "Caevz", "generator.single_biome_floating_islands": "<PERSON><PERSON> s<PERSON>", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drugs or alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Description:", "gui.abuseReport.reason.false_reporting": "False Reporting", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "Harassment or bullying", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Hate speech", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism or violent extremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Sending your report...", "gui.abuseReport.sent.title": "Report Sent", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "i understnad.", "gui.advancements": "Advnzmnts", "gui.all": "Oll", "gui.back": "Bak", "gui.banned.description": "%s\n\n%s\n\nLern moar at dis link: %s", "gui.banned.description.permanent": "Your account is permanently banned, which means you can't play online or join Realms.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "da code: %s", "gui.banned.description.reason_id_message": "da code: %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified that it goes against the Minecraft Community Standards.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Account permanently banned", "gui.banned.title.temporary": "Account temporarily suspended", "gui.cancel": "<PERSON>u", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "Leave and Discard Report", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Continue Editing", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Plz tell me, wut hapend:", "gui.chatReport.observed_what": "Why are you reporting this?", "gui.chatReport.read_info": "Learn About Reporting", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "Select Report Category", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "Send Report", "gui.chatReport.send.comments_too_long": "Please shorten the comment", "gui.chatReport.send.no_reason": "Please select a report category", "gui.chatReport.send.no_reported_messages": "Please select at least one chat message to report", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Report Player Chat", "gui.chatSelection.context": "msgs n chitchats n funy mincraf montagez in dis selektun wil b incld 2 provied moar contexto!", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Go on", "gui.copy_link_to_clipboard": "Copi Link 2 Klipbord", "gui.days": "%s dae(z)", "gui.done": "Dun", "gui.down": "Dawn", "gui.entity_tooltip.type": "Toipe: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Wejectd %s fiel", "gui.fileDropFailure.title": "Faild to ad fialz", "gui.hours": "%s long time(z)", "gui.loadingMinecraft": "Lod Minecraft", "gui.minutes": "%s minute(z)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s butonn", "gui.narrate.editBox": "%s chaenj box: %s", "gui.narrate.slider": "%s slydurr", "gui.narrate.tab": "%s tub", "gui.no": "<PERSON>u", "gui.none": "<PERSON>", "gui.ok": "K", "gui.open_report_dir": "Opn repowt diwektory", "gui.proceed": "pruseed", "gui.recipebook.moreRecipes": "Right PAW 4 moar", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Surch...", "gui.recipebook.toggleRecipes.all": "Showin' all", "gui.recipebook.toggleRecipes.blastable": "Showin' blstble recips", "gui.recipebook.toggleRecipes.craftable": "Showin' craftble recips", "gui.recipebook.toggleRecipes.smeltable": "Showin' smeltble recips", "gui.recipebook.toggleRecipes.smokable": "Showin' smokble recips", "gui.report_to_server": "Repot 2 Svr", "gui.socialInteractions.blocking_hint": "Manag wiv Mekrosoft akowant", "gui.socialInteractions.empty_blocked": "No blukd katz in litter box", "gui.socialInteractions.empty_hidden": "No katz hiddin in litter box", "gui.socialInteractions.hidden_in_chat": "Lowd meow frem %s wil bee hiden", "gui.socialInteractions.hide": "Hide in litter box", "gui.socialInteractions.narration.hide": "Blawk the meows frum %s", "gui.socialInteractions.narration.report": "Repowrt %s", "gui.socialInteractions.narration.show": "No hid murr meows from da cat %s", "gui.socialInteractions.report": "Oh no! Report da bad!", "gui.socialInteractions.search_empty": "No katz wis dis naem", "gui.socialInteractions.search_hint": "Surch...", "gui.socialInteractions.server_label.multiple": "%s - %s katz", "gui.socialInteractions.server_label.single": "%s - %s kat", "gui.socialInteractions.show": "<PERSON> in litter box", "gui.socialInteractions.shown_in_chat": "Lowd meow frem %s wil bee not hide", "gui.socialInteractions.status_blocked": "Bloked", "gui.socialInteractions.status_blocked_offline": "Blukd <PERSON> <PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden": "no no zone!!", "gui.socialInteractions.status_hidden_offline": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_offline": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_all": "EvriTHiNg", "gui.socialInteractions.tab_blocked": "Blokd", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON>", "gui.socialInteractions.title": "Soshul interacshuns", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> hide an seek wif da mesage", "gui.socialInteractions.tooltip.report": "Sum1 did bad thingz >:(", "gui.socialInteractions.tooltip.report.disabled": "No report fo u", "gui.socialInteractions.tooltip.report.no_messages": "Thers no msgs for riport from cat %s", "gui.socialInteractions.tooltip.report.not_reportable": "Yo catnt rewportw thif cat, cus thirf cat msgs catnt verifid un survur", "gui.socialInteractions.tooltip.show": "Wach msg", "gui.stats": "Catistics", "gui.toMenu": "Bacc to teh playing with othr kittehs", "gui.toRealms": "Bacc to play wit othr kittehz in Realms", "gui.toTitle": "Bacc to titl scrin", "gui.toWorld": "Bacc 2 wurld list to pley wit kittehz", "gui.togglable_slot": "Clic to disble slot", "gui.up": "<PERSON>p", "gui.waitingForResponse.button.inactive": "Bak (%ss)", "gui.waitingForResponse.title": "Wating fr Servr", "gui.yes": "Yez", "hanging_sign.edit": "<PERSON><PERSON><PERSON> danglin' sein mesag", "instrument.minecraft.admire_goat_horn": "Admir", "instrument.minecraft.call_goat_horn": "*AAAAhhhhhh*", "instrument.minecraft.dream_goat_horn": "Mind filmz", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON> somthen", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Findin", "instrument.minecraft.sing_goat_horn": "Noize", "instrument.minecraft.yearn_goat_horn": "Wantd U_U", "inventory.binSlot": "<PERSON><PERSON><PERSON> itum", "inventory.hotbarInfo": "Sev T00lbar w/ %1$s+%2$s", "inventory.hotbarSaved": "Item t00lbar sevd (rEEstor w/ %1$s+%2$s)", "item.canBreak": "CAN DESTROI:", "item.canPlace": "Can b put awn:", "item.canUse.unknown": "Idk", "item.color": "Colur: %s", "item.components": "%s componant(z)", "item.disabled": "Dizabld item", "item.durability": "brokn lvl: %s/%s", "item.dyed": "<PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> W<PERSON>r Car", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON>huh Watr Car wit Cat Box", "item.minecraft.allay_spawn_egg": "flyin blu mob spon ec", "item.minecraft.amethyst_shard": "Purpur shinee pice", "item.minecraft.angler_pottery_shard": "Cat fud ancient containr thingy", "item.minecraft.angler_pottery_sherd": "Cat fud ancient containr thingy", "item.minecraft.apple": "Mapple", "item.minecraft.archer_pottery_shard": "Bowman ancient containr thingy", "item.minecraft.archer_pottery_sherd": "Bowman ancient containr thingy", "item.minecraft.armadillo_scute": "Hard Bawl Stuf", "item.minecraft.armadillo_spawn_egg": "hard bawl spon ec", "item.minecraft.armor_stand": "Stick hooman", "item.minecraft.arms_up_pottery_shard": "Human Bing ancient containr thingy", "item.minecraft.arms_up_pottery_sherd": "Human Bing ancient containr thingy", "item.minecraft.arrow": "ERROW", "item.minecraft.axolotl_bucket": "Bukkit wit KUTE PINK FISHH", "item.minecraft.axolotl_spawn_egg": "KUTE PINK FISH spon ec", "item.minecraft.baked_potato": "Bak'd <PERSON>", "item.minecraft.bamboo_chest_raft": "Stik Watuh Ka wit Cat Box", "item.minecraft.bamboo_raft": "stick boat", "item.minecraft.bat_spawn_egg": "Bad spon ec", "item.minecraft.bee_spawn_egg": "B spon ec", "item.minecraft.beef": "Spotty meet", "item.minecraft.beetroot": "Betrut", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON> seds", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON> supe", "item.minecraft.birch_boat": "Burch Watr Car", "item.minecraft.birch_chest_boat": "B<PERSON>ch Watr Car wit Cat Box", "item.minecraft.black_bundle": "Spooky powch", "item.minecraft.black_dye": "Ender powder", "item.minecraft.black_harness": "<PERSON><PERSON>-dle :D", "item.minecraft.blade_pottery_shard": "Surwd ancient containr thingy", "item.minecraft.blade_pottery_sherd": "Surwd ancient containr thingy", "item.minecraft.blaze_powder": "HOT DUST", "item.minecraft.blaze_rod": "<PERSON>in <PERSON>", "item.minecraft.blaze_spawn_egg": "Bleze spon ec", "item.minecraft.blue_bundle": "Watur powch", "item.minecraft.blue_dye": "Water powder", "item.minecraft.blue_egg": "DIAMUNDZ ec", "item.minecraft.blue_harness": "Bloo Happy-dle :D", "item.minecraft.bogged_spawn_egg": "Boogie spon ec", "item.minecraft.bolt_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt drip", "item.minecraft.bone": "Doggy treetz", "item.minecraft.bone_meal": "Smashed <PERSON>", "item.minecraft.book": "lotz of paperz", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON><PERSON><PERSON> indentd bannr paturn", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "boul", "item.minecraft.bread": "bred", "item.minecraft.breeze_rod": "Wind guy stik", "item.minecraft.breeze_spawn_egg": "Brrrrrrrr spon ec", "item.minecraft.brewer_pottery_shard": "Medisin ancient containr thingy", "item.minecraft.brewer_pottery_sherd": "Medisin ancient containr thingy", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "Burned Clay", "item.minecraft.brown_bundle": "<PERSON><PERSON>e powch", "item.minecraft.brown_dye": "Chocolate powder", "item.minecraft.brown_egg": "Schokolate ec", "item.minecraft.brown_harness": "Bruwn Happy-dle :D", "item.minecraft.brush": "mini broom", "item.minecraft.bucket": "Shiny sit-in cold thing", "item.minecraft.bundle": "Cat powch", "item.minecraft.bundle.empty": "MT", "item.minecraft.bundle.empty.description": "<PERSON>n hold a mikz stacc of thingz :o", "item.minecraft.bundle.full": "Fool", "item.minecraft.bundle.fullness": "%s OUt OV %s", "item.minecraft.burn_pottery_shard": "fireh ancient containr thingy", "item.minecraft.burn_pottery_sherd": "fireh ancient containr thingy", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON> spon ec", "item.minecraft.carrot": "rebbit treetz", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON><PERSON> on a stick", "item.minecraft.cat_spawn_egg": "Kitty Cat spon ec", "item.minecraft.cauldron": "contain liqwide", "item.minecraft.cave_spider_spawn_egg": "Cavspaydur spon ec", "item.minecraft.chainmail_boots": "cliky fast bootz1!", "item.minecraft.chainmail_chestplate": "BLINNG CHEHST", "item.minecraft.chainmail_helmet": "blinghat", "item.minecraft.chainmail_leggings": "<PERSON>ling pantz", "item.minecraft.charcoal": "burned w00d", "item.minecraft.cherry_boat": "Sakura watr car", "item.minecraft.cherry_chest_boat": "Sakura Watr Car wit Cat Box", "item.minecraft.chest_minecart": "Minecat wif Cat Box", "item.minecraft.chicken": "raw cluck", "item.minecraft.chicken_spawn_egg": "Nugget spon ec", "item.minecraft.chorus_fruit": "Frute dat Du<PERSON>", "item.minecraft.clay_ball": "cley prom", "item.minecraft.clock": "Tiem tellur", "item.minecraft.coal": "ur present", "item.minecraft.coast_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.coast_armor_trim_smithing_template.new": "Watery drip", "item.minecraft.cocoa_beans": "itz poop", "item.minecraft.cod": "Roh Cod", "item.minecraft.cod_bucket": "Cot buket", "item.minecraft.cod_spawn_egg": "Cot spon ec", "item.minecraft.command_block_minecart": "Minecrat with Command Blockz", "item.minecraft.compass": "Spinny thing", "item.minecraft.cooked_beef": "Bad spotty meet", "item.minecraft.cooked_chicken": "cooked cluck", "item.minecraft.cooked_cod": "Cookd Cod", "item.minecraft.cooked_mutton": "Bad wooly meet", "item.minecraft.cooked_porkchop": "Toasted <PERSON>", "item.minecraft.cooked_rabbit": "<PERSON> Jumpin <PERSON>", "item.minecraft.cooked_salmon": "Crisp Pink Nomz", "item.minecraft.cookie": "Rainbow burd poizon", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON> ingut", "item.minecraft.cow_spawn_egg": "Milk-Maeker spon ec", "item.minecraft.creaking_spawn_egg": "Scawy twee spon ec", "item.minecraft.creeper_banner_pattern": "bannr paturn", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON> churge", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON> churge bannr paturn", "item.minecraft.creeper_spawn_egg": "Cwepuh spon ec", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Projektile:", "item.minecraft.crossbow.projectile.multiple": "PRO-jectil: %s x %s", "item.minecraft.crossbow.projectile.single": "PRO-jectil: %s", "item.minecraft.cyan_bundle": "<PERSON>ghun powch", "item.minecraft.cyan_dye": "Sky powder", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>-dle :D", "item.minecraft.danger_pottery_shard": "creepr aw man ancient containr thingy", "item.minecraft.danger_pottery_sherd": "creepr aw man ancient containr thingy", "item.minecraft.dark_oak_boat": "Blak Oke Watr Car", "item.minecraft.dark_oak_chest_boat": "Blak Oak Watr Car wit Cat Box", "item.minecraft.debug_stick": "Magik stik", "item.minecraft.debug_stick.empty": "%s hash nu pwopewties", "item.minecraft.debug_stick.select": "shelekted \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" tu %s", "item.minecraft.diamond": "Ooooh shineee!", "item.minecraft.diamond_axe": "<PERSON><PERSON>", "item.minecraft.diamond_boots": "awesome shoes", "item.minecraft.diamond_chestplate": "super cool shirt", "item.minecraft.diamond_helmet": "very shiny hat", "item.minecraft.diamond_hoe": "<PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "SUPAH shiny hoofy fur", "item.minecraft.diamond_leggings": "amazing pants", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "shiny sord", "item.minecraft.disc_fragment_5": "loud thimg piec", "item.minecraft.disc_fragment_5.desc": "Round loud thing - 5", "item.minecraft.dolphin_spawn_egg": "doolfin spon ec", "item.minecraft.donkey_spawn_egg": "Danky spon ec", "item.minecraft.dragon_breath": "Dragunish puff", "item.minecraft.dried_kelp": "crunchy sea leaves", "item.minecraft.drowned_spawn_egg": "Drawn spon ec", "item.minecraft.dune_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.dune_armor_trim_smithing_template.new": "Litterbox drip", "item.minecraft.echo_shard": "boomerang sund pice", "item.minecraft.egg": "Ec", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON>r Gardien spon ec", "item.minecraft.elytra": "FLYYYYYYYY", "item.minecraft.emerald": "shineh green stuff", "item.minecraft.enchanted_book": "<PERSON><PERSON>", "item.minecraft.enchanted_golden_apple": "Glowy power apl", "item.minecraft.end_crystal": "<PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "dwagon boos spon ec", "item.minecraft.ender_eye": "teh evil eye", "item.minecraft.ender_pearl": "magic ball", "item.minecraft.enderman_spawn_egg": "Enderman spon ec", "item.minecraft.endermite_spawn_egg": "Endermite spon ec", "item.minecraft.evoker_spawn_egg": "Evokr spon ec", "item.minecraft.experience_bottle": "Buttl wif ur levlz", "item.minecraft.explorer_pottery_shard": "Eksplorer ancient containr thingy", "item.minecraft.explorer_pottery_sherd": "Eksplorer ancient containr thingy", "item.minecraft.eye_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.eye_armor_trim_smithing_template.new": "BLINK BLINK drip", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Bad spider bal", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON><PERSON> bannr paturn", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON><PERSON> papeh", "item.minecraft.fire_charge": "Fire Punch!", "item.minecraft.firework_rocket": "shiny 'splody thing", "item.minecraft.firework_rocket.flight": "TIEM OF FLY:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "dis makes da rocket go BOOM BOOM", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "<PERSON>lew", "item.minecraft.firework_star.brown": "Broun", "item.minecraft.firework_star.custom_color": "cuztum", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "vanish to", "item.minecraft.firework_star.flicker": "Sparklies!", "item.minecraft.firework_star.gray": "<PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON>", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Stampy Colour", "item.minecraft.firework_star.pink": "Pynk", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "Red", "item.minecraft.firework_star.shape": "Squiggy shape", "item.minecraft.firework_star.shape.burst": "Boom", "item.minecraft.firework_star.shape.creeper": "Creepery", "item.minecraft.firework_star.shape.large_ball": "BIG ball", "item.minecraft.firework_star.shape.small_ball": "<PERSON>e", "item.minecraft.firework_star.shape.star": "pointi shaep", "item.minecraft.firework_star.trail": "<PERSON><PERSON>", "item.minecraft.firework_star.white": "Wite", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "kitteh feedin' device", "item.minecraft.flint": "sharpy rock", "item.minecraft.flint_and_steel": "frint und steal", "item.minecraft.flow_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON> drip", "item.minecraft.flow_banner_pattern": "Ban<PERSON>r pat<PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON> ban<PERSON>r paturn", "item.minecraft.flow_pottery_sherd": "Windeh ancient containr thingy", "item.minecraft.flower_banner_pattern": "bahnor patturnn", "item.minecraft.flower_banner_pattern.desc": "Flowerpower in Charge", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON> churge bannr paturn", "item.minecraft.flower_pot": "container for all the pretty plantz", "item.minecraft.fox_spawn_egg": "Fuxe spon ec", "item.minecraft.friend_pottery_shard": "Gud kat ancient containr thingy", "item.minecraft.friend_pottery_sherd": "Frend ancient containr thingy", "item.minecraft.frog_spawn_egg": "toad spon ec", "item.minecraft.furnace_minecart": "Minecat wif Hot Box", "item.minecraft.ghast_spawn_egg": "Ghast spon ec", "item.minecraft.ghast_tear": "Ghast Tear", "item.minecraft.glass_bottle": "GLAS BOTUL", "item.minecraft.glistering_melon_slice": "glistrin melooon sliz", "item.minecraft.globe_banner_pattern": "bahnor patturnn", "item.minecraft.globe_banner_pattern.desc": "Glolbe", "item.minecraft.globe_banner_pattern.new": "Glolbe bannr paturn", "item.minecraft.glow_berries": "shiny berriz", "item.minecraft.glow_ink_sac": "Shiny ink sac", "item.minecraft.glow_item_frame": "Brite item holdr", "item.minecraft.glow_squid_spawn_egg": "Shiny skwid spon ec", "item.minecraft.glowstone_dust": "Glowstone duzt", "item.minecraft.goat_horn": "monten shep's sharp thingy", "item.minecraft.goat_spawn_egg": "monten shep spon ec", "item.minecraft.gold_ingot": "GULDEN INGUT", "item.minecraft.gold_nugget": "<PERSON><PERSON> chikn nuget", "item.minecraft.golden_apple": "GULD APEL", "item.minecraft.golden_axe": "<PERSON><PERSON>", "item.minecraft.golden_boots": "GULD BOOTZ", "item.minecraft.golden_carrot": "GULDEN CARRUT", "item.minecraft.golden_chestplate": "GOLDEN CHESPLAET", "item.minecraft.golden_helmet": "GULDEN HAT", "item.minecraft.golden_hoe": "<PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON>", "item.minecraft.golden_leggings": "GOLD PATNZ", "item.minecraft.golden_pickaxe": "<PERSON><PERSON>", "item.minecraft.golden_shovel": "<PERSON><PERSON>", "item.minecraft.golden_sword": "<PERSON><PERSON>", "item.minecraft.gray_bundle": "Ash powch", "item.minecraft.gray_dye": "Moar dull powder", "item.minecraft.gray_harness": "<PERSON>-dle :D", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON> powch", "item.minecraft.green_dye": "Grass powder", "item.minecraft.green_harness": "<PERSON><PERSON>-dle :D", "item.minecraft.guardian_spawn_egg": "Gardien spon ec", "item.minecraft.gunpowder": "Gan<PERSON>udar", "item.minecraft.guster_banner_pattern": "Ban<PERSON>r pat<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON><PERSON> bannr paturn", "item.minecraft.guster_pottery_sherd": "Bloweh ancient containr thingy", "item.minecraft.happy_ghast_spawn_egg": "Happeezz Ghast spon ec", "item.minecraft.harness": "Happy-dle :D", "item.minecraft.heart_of_the_sea": "<PERSON> of da see", "item.minecraft.heart_pottery_shard": "<3 ancient containr thingy", "item.minecraft.heart_pottery_sherd": "<3 ancient containr thingy", "item.minecraft.heartbreak_pottery_shard": "Hartbrek ancient containr thingy", "item.minecraft.heartbreak_pottery_sherd": "</3 ancient containr thingy", "item.minecraft.hoglin_spawn_egg": "Hoglin spon ec", "item.minecraft.honey_bottle": "huny buttle", "item.minecraft.honeycomb": "honyconb", "item.minecraft.hopper_minecart": "Minecat wif hoppez", "item.minecraft.horse_spawn_egg": "Hors spon ec", "item.minecraft.host_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.host_armor_trim_smithing_template.new": "Host drip", "item.minecraft.howl_pottery_shard": "*AWWWWWWWWWW* ancient containr thingy", "item.minecraft.howl_pottery_sherd": "*AWWW* ancient containr thingy", "item.minecraft.husk_spawn_egg": "Hask spon ec", "item.minecraft.ink_sac": "Squirty nasty stuff", "item.minecraft.iron_axe": "Iron Aks", "item.minecraft.iron_boots": "Irun Shoes", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "IRON MAN spon ec", "item.minecraft.iron_helmet": "IRUNHAT", "item.minecraft.iron_hoe": "<PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Shiny hoofy fur", "item.minecraft.iron_ingot": "<PERSON><PERSON>", "item.minecraft.iron_leggings": "<PERSON><PERSON>", "item.minecraft.iron_nugget": "Steely thingy", "item.minecraft.iron_pickaxe": "Iron Pikakse", "item.minecraft.iron_shovel": "Iron Shaval", "item.minecraft.iron_sword": "<PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON> W<PERSON>r Car", "item.minecraft.jungle_chest_boat": "Junglz Watr Car wit Cat Box", "item.minecraft.knowledge_book": "Buk of Knawledg", "item.minecraft.lapis_lazuli": "blu stuffz", "item.minecraft.lava_bucket": "bukkit full of HOT SAUCE", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Leathr Horse Armor", "item.minecraft.leather_leggings": "<PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Ice powch", "item.minecraft.light_blue_dye": "Wet powder", "item.minecraft.light_blue_harness": "<PERSON><PERSON> blu <PERSON>-dle :D", "item.minecraft.light_gray_bundle": "<PERSON>l<PERSON>h powch", "item.minecraft.light_gray_dye": "Dull powder", "item.minecraft.light_gray_harness": "<PERSON><PERSON> grey Happy-dle :D", "item.minecraft.lime_bundle": "<PERSON>m powch", "item.minecraft.lime_dye": "Nasty powder", "item.minecraft.lime_harness": "<PERSON><PERSON>-dle :D", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON> of weird", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON> Poshun u Cann Onli Gat in HAX MODE", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON>in Poshun ov Fire Rezistance", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON> ov Ouch", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON> Poshun ov Bugz", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.luck": "Lin<PERSON>in Poshun ov LOL", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> Poshu<PERSON> ov Bo<PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.oozing": "Linnerin Poshun ov Gren stuff", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON> ov Poizzon", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON> Poshun ov Regenerashun", "item.minecraft.lingering_potion.effect.slow_falling": "linn<PERSON>n poshun uf slowmo fall", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON> Poshun ov Slownez", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON> Poshun ov Strength", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON>in Poshun ov Spedz", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON> of TurTl Boss", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> Poshun ov <PERSON><PERSON><PERSON> Breathin", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON> ov She<PERSON>'", "item.minecraft.lingering_potion.effect.wind_charged": "Linnerin Poshun ov Wind boom", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON> spon ec", "item.minecraft.lodestone_compass": "loserstone compas", "item.minecraft.mace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON> powch", "item.minecraft.magenta_dye": "Warmish powder", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>-dle :D", "item.minecraft.magma_cream": "Flamin creem", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> cewb spon ec", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON> watr car", "item.minecraft.mangrove_chest_boat": "Mangroff Watr Car wif Cat Box", "item.minecraft.map": "cleen map", "item.minecraft.melon_seeds": "<PERSON><PERSON>", "item.minecraft.melon_slice": "melon sliz", "item.minecraft.milk_bucket": "mikk buket!!!", "item.minecraft.minecart": "Minecat", "item.minecraft.miner_pottery_shard": "Pickakz ancient containr thingy", "item.minecraft.miner_pottery_sherd": "Pickakz ancient containr thingy", "item.minecraft.mojang_banner_pattern": "bahnor patturnn", "item.minecraft.mojang_banner_pattern.desc": "<PERSON>g", "item.minecraft.mojang_banner_pattern.new": "Ting bannr paturn", "item.minecraft.mooshroom_spawn_egg": "Mooshroom spon ec", "item.minecraft.mourner_pottery_shard": ":( ancient containr thingy", "item.minecraft.mourner_pottery_sherd": ":( ancient containr thingy", "item.minecraft.mule_spawn_egg": "Mewl spon ec", "item.minecraft.mushroom_stew": "A supe of funges", "item.minecraft.music_disc_11": "Round loud thing", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Round loud thing", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Round loud thing", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Round loud thing", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Round loud thing", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Round loud thing", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Round loud thing", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Round loud thing", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (<PERSON><PERSON>h bokz)", "item.minecraft.music_disc_far": "Round loud thing", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Round loud thing", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Round loud thing", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Round loud thing", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Round loud thing", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Round loud thing", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Round loud thing", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Round loud thing", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Round loud thing", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Round loud thing", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Round loud thing", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Round loud thing", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Round loud thing", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Wooly meet", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Naughty shell", "item.minecraft.nether_brick": "Nether brik", "item.minecraft.nether_star": "Nether Asterisk", "item.minecraft.nether_wart": "icky nether bumbz", "item.minecraft.netherite_axe": "Netherite ax", "item.minecraft.netherite_boots": "<PERSON>her<PERSON> shoez", "item.minecraft.netherite_chestplate": "Netherite sweatur", "item.minecraft.netherite_helmet": "Netherite hat", "item.minecraft.netherite_hoe": "Netherite Hoe", "item.minecraft.netherite_ingot": "Netherite ingut", "item.minecraft.netherite_leggings": "Netherite pantz", "item.minecraft.netherite_pickaxe": "Nether<PERSON>", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON> skra<PERSON>z", "item.minecraft.netherite_shovel": "Netherite shuvl", "item.minecraft.netherite_sword": "Netherite swurd", "item.minecraft.netherite_upgrade_smithing_template": "Smissinng thingy", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite lvl UP!", "item.minecraft.oak_boat": "Ouk Watr Car", "item.minecraft.oak_chest_boat": "Oak Watr Car wit Cat Box", "item.minecraft.ocelot_spawn_egg": "Oslot spon ec", "item.minecraft.ominous_bottle": "skeri botel", "item.minecraft.ominous_trial_key": "Spoo<PERSON><PERSON>oo-key", "item.minecraft.orange_bundle": "<PERSON>ne<PERSON> powch", "item.minecraft.orange_dye": "Bright powder", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>-dle :D", "item.minecraft.painting": "Art on a paper", "item.minecraft.pale_oak_boat": "Wite Ok Watur Kar", "item.minecraft.pale_oak_chest_boat": "Wite Ok Watr Car w/ <PERSON>z", "item.minecraft.panda_spawn_egg": "Green Stick Eatar spon ec", "item.minecraft.paper": "Papyrus", "item.minecraft.parrot_spawn_egg": "Pawet spon ec", "item.minecraft.phantom_membrane": "creepy flyin ting mimbrain", "item.minecraft.phantom_spawn_egg": "Fantum spon ec", "item.minecraft.pig_spawn_egg": "Pic spon ec", "item.minecraft.piglin_banner_pattern": "bahnor patturnn", "item.minecraft.piglin_banner_pattern.desc": "Nouze", "item.minecraft.piglin_banner_pattern.new": "Pinknozz bannr paturn", "item.minecraft.piglin_brute_spawn_egg": "Piglin Brut spon ec", "item.minecraft.piglin_spawn_egg": "Piglin spon ec", "item.minecraft.pillager_spawn_egg": "Pilagur spon ec", "item.minecraft.pink_bundle": "Sakura powch", "item.minecraft.pink_dye": "Less warmy powder", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON>-dle :D", "item.minecraft.pitcher_plant": "Bottl plantz", "item.minecraft.pitcher_pod": "Bottl seed ball", "item.minecraft.plenty_pottery_shard": "RICH ancient containr thingy", "item.minecraft.plenty_pottery_sherd": "RICH ancient containr thingy", "item.minecraft.poisonous_potato": "no eat iz yukky", "item.minecraft.polar_bear_spawn_egg": "Polah bar spon ec", "item.minecraft.popped_chorus_fruit": "Popped <PERSON>ut dat <PERSON>", "item.minecraft.porkchop": "rawr pig", "item.minecraft.potato": "Pootato", "item.minecraft.potion": "Poshun", "item.minecraft.potion.effect.awkward": "Poshun of weird", "item.minecraft.potion.effect.empty": "Poshun u Cann Onli Gat in HAX MODE", "item.minecraft.potion.effect.fire_resistance": "Poshun ov Fire Rezistance", "item.minecraft.potion.effect.harming": "Poshun ov Ouch", "item.minecraft.potion.effect.healing": "Poshun ov <PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "Poshun ov Bugzz", "item.minecraft.potion.effect.invisibility": "Poshun ov Hiding", "item.minecraft.potion.effect.leaping": "Poshun ov <PERSON>pin", "item.minecraft.potion.effect.levitation": "bad flyn", "item.minecraft.potion.effect.luck": "Poshun ov LOL", "item.minecraft.potion.effect.mundane": "Poshun ov Boringnes", "item.minecraft.potion.effect.night_vision": "Poshun ov Cat <PERSON>", "item.minecraft.potion.effect.oozing": "Poshun ov G<PERSON> stuf", "item.minecraft.potion.effect.poison": "Poshun ov Poizzon", "item.minecraft.potion.effect.regeneration": "Poshun ov Regenerashun", "item.minecraft.potion.effect.slow_falling": "Poshun of slowmo fall", "item.minecraft.potion.effect.slowness": "Poshun ov Slownez", "item.minecraft.potion.effect.strength": "chuckz noris pushun", "item.minecraft.potion.effect.swiftness": "Poshun ov Spedz", "item.minecraft.potion.effect.thick": "<PERSON>", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON><PERSON> of TurTl Boss", "item.minecraft.potion.effect.water": "Not for katz", "item.minecraft.potion.effect.water_breathing": "Poshun ov Watr Breathing", "item.minecraft.potion.effect.weakness": "Poshun ov Weaknes", "item.minecraft.potion.effect.weaving": "Poshun ov She<PERSON>'", "item.minecraft.potion.effect.wind_charged": "Poshun ov Wind booom", "item.minecraft.pottery_shard_archer": "Bowman ancient containr thingy", "item.minecraft.pottery_shard_arms_up": "Human Bing ancient containr thingy", "item.minecraft.pottery_shard_prize": "Shineee ancient containr thingy", "item.minecraft.pottery_shard_skull": "Head ancient containr thingy", "item.minecraft.powder_snow_bucket": "Bukkit wit Powdr Sno", "item.minecraft.prismarine_crystals": "Prizmarine pebblez", "item.minecraft.prismarine_shard": "Prizmarine thingy", "item.minecraft.prize_pottery_shard": "Shineee ancient containr thingy", "item.minecraft.prize_pottery_sherd": "Shineee ancient containr thingy", "item.minecraft.pufferfish": "very yukky fishy", "item.minecraft.pufferfish_bucket": "Pa<PERSON><PERSON>osh buket!!!", "item.minecraft.pufferfish_spawn_egg": "Pufahfis spon ec", "item.minecraft.pumpkin_pie": "pie with squash in it", "item.minecraft.pumpkin_seeds": "Pumpkez Sedz", "item.minecraft.purple_bundle": "Grapz powch", "item.minecraft.purple_dye": "Sorta-in-between powder", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON><PERSON> Happy-dle :D", "item.minecraft.quartz": "Nether cloudz", "item.minecraft.rabbit": "Rawr <PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON>b <PERSON>g", "item.minecraft.rabbit_spawn_egg": "Wabit spon ec", "item.minecraft.rabbit_stew": "Warm Jumpin Foodz in Bawl", "item.minecraft.raiser_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.raiser_armor_trim_smithing_template.new": "Farmr drip", "item.minecraft.ravager_spawn_egg": "Rivagur spon ec", "item.minecraft.raw_copper": "moldy wurst", "item.minecraft.raw_gold": "shiny coal", "item.minecraft.raw_iron": "RAWWWW irun", "item.minecraft.recovery_compass": "Deth findr v1.0", "item.minecraft.red_bundle": "Bloody powch", "item.minecraft.red_dye": "Mojang powder", "item.minecraft.red_harness": "<PERSON> Happy-dle :D", "item.minecraft.redstone": "Redstone Dust", "item.minecraft.resin_brick": "<PERSON><PERSON> stiky brik", "item.minecraft.resin_clump": "<PERSON><PERSON> poop", "item.minecraft.rib_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.rib_armor_trim_smithing_template.new": "Boney drip", "item.minecraft.rotten_flesh": "Taestz Laik Brokali", "item.minecraft.saddle": "Sad-le :c", "item.minecraft.salmon": "Rawr Pink Nomz", "item.minecraft.salmon_bucket": "Salmon buket!!!", "item.minecraft.salmon_spawn_egg": "Samin spon ec", "item.minecraft.scrape_pottery_sherd": "Claw ancient cotainr thingy", "item.minecraft.scute": "Hard pice ov thing", "item.minecraft.sentry_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> drip", "item.minecraft.shaper_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.shaper_armor_trim_smithing_template.new": "Buildrman drip", "item.minecraft.sheaf_pottery_shard": "Cowfud ancient containr thingy", "item.minecraft.sheaf_pottery_sherd": "Cowfud ancient containr thingy", "item.minecraft.shears": "Skizzors", "item.minecraft.sheep_spawn_egg": "Shep spon ec", "item.minecraft.shelter_pottery_shard": "Cat hous ancient containr thingy", "item.minecraft.shelter_pottery_sherd": "Cat hous ancient containr thingy", "item.minecraft.shield": "Cat protecshun", "item.minecraft.shield.black": "Blak cat protecshun", "item.minecraft.shield.blue": "Bloo cat protecshun", "item.minecraft.shield.brown": "Broun cat protecshun", "item.minecraft.shield.cyan": "Sighun cat protecshun", "item.minecraft.shield.gray": "Moar dull cat protecshun", "item.minecraft.shield.green": "Greenish cat protecshun", "item.minecraft.shield.light_blue": "Lite bloo cat protecshun", "item.minecraft.shield.light_gray": "Dull cat protecshun", "item.minecraft.shield.lime": "Limd cat protecshun", "item.minecraft.shield.magenta": "Majenta cat protecshun", "item.minecraft.shield.orange": "Ornge cat protecshun", "item.minecraft.shield.pink": "Pinky cat protecshun", "item.minecraft.shield.purple": "Parpal cat protecshun", "item.minecraft.shield.red": "Redish cat protecshun", "item.minecraft.shield.white": "Wite cat protecshun", "item.minecraft.shield.yellow": "Yello cat protecshun", "item.minecraft.shulker_shell": "Shulker Shell", "item.minecraft.shulker_spawn_egg": "Shuker spon ec", "item.minecraft.sign": "wite a mesige", "item.minecraft.silence_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.silence_armor_trim_smithing_template.new": "*shhhhhh* drip", "item.minecraft.silverfish_spawn_egg": "Grae fish spon ec", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> hors span ec", "item.minecraft.skeleton_spawn_egg": "Skelehten spon ec", "item.minecraft.skull_banner_pattern": "bahnor patturnn", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON> churge", "item.minecraft.skull_banner_pattern.new": "<PERSON><PERSON> charg bannr paturn", "item.minecraft.skull_pottery_shard": "Bone ancient containr thingy", "item.minecraft.skull_pottery_sherd": "Bone ancient containr thingy", "item.minecraft.slime_ball": "boogerz", "item.minecraft.slime_spawn_egg": "Sliem spon ec", "item.minecraft.smithing_template": "Smissinng thingy", "item.minecraft.smithing_template.applies_to": "appliz to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Putt ingut or sharp shiniez", "item.minecraft.smithing_template.armor_trim.applies_to": "Armur", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Putt a piec ov armur", "item.minecraft.smithing_template.armor_trim.ingredients": "butters and sharp shiniez", "item.minecraft.smithing_template.ingredients": "da stuff u need:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "putt ur netherite barr heer", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "blue shiny thingz", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "putt ur shiny clothin orr toolz heer", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite ingut", "item.minecraft.smithing_template.upgrade": "IMPROVE!! ", "item.minecraft.sniffer_spawn_egg": "Sniffr spon ec", "item.minecraft.snort_pottery_shard": "*snfff* ancient containr thingy", "item.minecraft.snort_pottery_sherd": "*snfff* ancient containr thingy", "item.minecraft.snout_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.snout_armor_trim_smithing_template.new": "Nouze drip", "item.minecraft.snow_golem_spawn_egg": "Cold watr hooman spon ec", "item.minecraft.snowball": "Cold wet", "item.minecraft.spectral_arrow": "<PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON> bal", "item.minecraft.spider_spawn_egg": "Spaydur spon ec", "item.minecraft.spire_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.spire_armor_trim_smithing_template.new": "Towr drip", "item.minecraft.splash_potion": "Throw Poshun", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON> of weird", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON> Poshun u Cann Onli Gat in HAX MODE", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON> ov Fire Rezistance", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON> ov Ouch", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON> ov <PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON> ov Bugz", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.luck": "Spleshy Poshun ov LOL", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON> ov Gren mob", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON> ov Poizzon", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON> ov Regenerashun", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> poshun uf slowmo fall", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON> ov Slownez", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON> ov Strength", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON> ov Spedz", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON> poshun uf TurTl Boss", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> ov <PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON> ov <PERSON>", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON> ov She<PERSON>'", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON><PERSON><PERSON> ov Wind boom", "item.minecraft.spruce_boat": "Sproos Watr Car", "item.minecraft.spruce_chest_boat": "Sprce Watr Car wit Cat Box", "item.minecraft.spyglass": "I C U", "item.minecraft.squid_spawn_egg": "Sqwid spon ec", "item.minecraft.stick": "stik", "item.minecraft.stone_axe": "Stone Aks", "item.minecraft.stone_hoe": "Ston Hoe", "item.minecraft.stone_pickaxe": "Ston <PERSON>", "item.minecraft.stone_shovel": "stone spoon", "item.minecraft.stone_sword": "Pointy rock", "item.minecraft.stray_spawn_egg": "Strey spon ec", "item.minecraft.strider_spawn_egg": "Straydur spon ec", "item.minecraft.string": "CAN I HAZ TEH TAIL THING", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "IDK dont eat dat", "item.minecraft.sweet_berries": "Sweet Beries", "item.minecraft.tadpole_bucket": "buket wit toadpol", "item.minecraft.tadpole_spawn_egg": "mini frog spon ec", "item.minecraft.tide_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.tide_armor_trim_smithing_template.new": "Wavy drip", "item.minecraft.tipped_arrow": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "Shooty Tihng Wit Liqwide Magik", "item.minecraft.tipped_arrow.effect.empty": "Shooty Tihng u Cann Onli Gat in HAX MODE", "item.minecraft.tipped_arrow.effect.fire_resistance": "Speshul Shooty Tignh Dat Makez u Fire Rezistant", "item.minecraft.tipped_arrow.effect.harming": "Speshul Shooty Tignh Dat Makez u Ouch", "item.minecraft.tipped_arrow.effect.healing": "Speshul Shooty Tignh Dat Makez u Heal", "item.minecraft.tipped_arrow.effect.infested": "Speshul Shooty Tignh Dat Makez bugz", "item.minecraft.tipped_arrow.effect.invisibility": "Speshul Shooty Tignh Dat Makez u Invizibul", "item.minecraft.tipped_arrow.effect.leaping": "Speshul Shooty Tignh Dat Makez u Bunny Cat", "item.minecraft.tipped_arrow.effect.levitation": "Speshul Shooty Tignh Dat Makez u Flai", "item.minecraft.tipped_arrow.effect.luck": "Speshul Shooty Tignh Dat Makez u LOL", "item.minecraft.tipped_arrow.effect.mundane": "Shooty Tihng Wit Liqwide Magik", "item.minecraft.tipped_arrow.effect.night_vision": "Speshul Shooty Tignh Dat Makez u Cat Vishun", "item.minecraft.tipped_arrow.effect.oozing": "Speshul Shooty Tignh Dat Makez gren slaimz", "item.minecraft.tipped_arrow.effect.poison": "Speshul Shooty Tignh Dat Makez u Puiznd", "item.minecraft.tipped_arrow.effect.regeneration": "Speshul Shooty Tignh Dat Makez u Reg", "item.minecraft.tipped_arrow.effect.slow_falling": "Arruz uf slowmo fall", "item.minecraft.tipped_arrow.effect.slowness": "Speshul Shooty Tignh Dat Makez u Slo", "item.minecraft.tipped_arrow.effect.strength": "Speshul Shooty Tignh Dat Makez u Powahful", "item.minecraft.tipped_arrow.effect.swiftness": "Speshul Shooty Tignh Dat Makez u Fazt", "item.minecraft.tipped_arrow.effect.thick": "<PERSON>y Tihng wit poshun", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON><PERSON> of TurTl Boss", "item.minecraft.tipped_arrow.effect.water": "Speshul Shooty Tignh Dat Splashes", "item.minecraft.tipped_arrow.effect.water_breathing": "Speshul Shooty Tignh Dat Makez u Breathin undr Watr", "item.minecraft.tipped_arrow.effect.weakness": "Speshul Shooty Tignh Dat Makez u Week", "item.minecraft.tipped_arrow.effect.weaving": "Speshul Shooty Tignh Dat Makez u Shed", "item.minecraft.tipped_arrow.effect.wind_charged": "Speshul Shooty Tignh Dat Makez u Windi", "item.minecraft.tnt_minecart": "<PERSON>y <PERSON>", "item.minecraft.torchflower_seeds": "burny flowr seeedz", "item.minecraft.totem_of_undying": "Anti-dyin' <PERSON><PERSON> ", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON>l spon ec", "item.minecraft.trial_key": "Dungenz Kee", "item.minecraft.trident": "Dinglehopper", "item.minecraft.tropical_fish": "Weird water thing", "item.minecraft.tropical_fish_bucket": "Weird water thing buket!!!", "item.minecraft.tropical_fish_spawn_egg": "Trahpicul fish spon ec", "item.minecraft.turtle_helmet": "TortL head", "item.minecraft.turtle_scute": "pice ov tutrl shell", "item.minecraft.turtle_spawn_egg": "TortL spon ec", "item.minecraft.vex_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.vex_armor_trim_smithing_template.new": "Gosty drip", "item.minecraft.vex_spawn_egg": "Vax spon ec", "item.minecraft.villager_spawn_egg": "Vilagur spon ec", "item.minecraft.vindicator_spawn_egg": "Vindahcaytur spon ec", "item.minecraft.wandering_trader_spawn_egg": "Wubterng Truder spon ec", "item.minecraft.ward_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.ward_armor_trim_smithing_template.new": "Blue scary man drip", "item.minecraft.warden_spawn_egg": "Blu shrek spon ec", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON> on a stick", "item.minecraft.water_bucket": "WatR Buket", "item.minecraft.wayfinder_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "find-da-wae drip", "item.minecraft.wheat": "Weet", "item.minecraft.wheat_seeds": "wit seeds", "item.minecraft.white_bundle": "Cloud powch", "item.minecraft.white_dye": "Blank powder", "item.minecraft.white_harness": "Wait Happy-dle :D", "item.minecraft.wild_armor_trim_smithing_template": "Smissinng thingy", "item.minecraft.wild_armor_trim_smithing_template.new": "wildin drip", "item.minecraft.wind_charge": "Wind Attacc", "item.minecraft.witch_spawn_egg": "Wetch spon ec", "item.minecraft.wither_skeleton_spawn_egg": "Wither skel<PERSON>ten spon ec", "item.minecraft.wither_spawn_egg": "Wither spon ec", "item.minecraft.wolf_armor": "Woof <PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON> spon ec", "item.minecraft.wooden_axe": "teh furst chopper", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON>e", "item.minecraft.wooden_pickaxe": "Wuddn Pikkatt", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "NeRd!", "item.minecraft.written_book": "Book 4 Reeding", "item.minecraft.yellow_bundle": "<PERSON><PERSON> powch", "item.minecraft.yellow_dye": "Banana powder", "item.minecraft.yellow_harness": "<PERSON><PERSON>-dle :D", "item.minecraft.zoglin_spawn_egg": "Zoglin spwn ec", "item.minecraft.zombie_horse_spawn_egg": "Zob<PERSON> horz spon ec", "item.minecraft.zombie_spawn_egg": "Zobi spon ec", "item.minecraft.zombie_villager_spawn_egg": "Unded villaguur spon ec", "item.minecraft.zombified_piglin_spawn_egg": "Zobifyed Piglin spon ec", "item.modifiers.any": "Whenz iz weard:", "item.modifiers.armor": "<PERSON>hn un bodi:", "item.modifiers.body": "Whenz iz weard:", "item.modifiers.chest": "<PERSON> un kit-cat bodi:", "item.modifiers.feet": "<PERSON>hn un bak pawz:", "item.modifiers.hand": "<PERSON> holdin:", "item.modifiers.head": "Wen unn kit-cat hed:", "item.modifiers.legs": "<PERSON> un paws:", "item.modifiers.mainhand": "Wehn in Maen Paw:", "item.modifiers.offhand": "<PERSON>hn in Bad Paw:", "item.modifiers.saddle": "<PERSON><PERSON> saddold:", "item.nbt_tags": "NbT: %s stuf(z)", "item.op_block_warning.line1": "ALERT:", "item.op_block_warning.line2": "Usin dis item might execwut komand", "item.op_block_warning.line3": "DO NOT use unles u know teh exak kontenz!", "item.unbreakable": "no breakz", "itemGroup.buildingBlocks": "Build Blukz", "itemGroup.coloredBlocks": "Grey Blockz", "itemGroup.combat": "Combawt", "itemGroup.consumables": "<PERSON>", "itemGroup.crafting": "<PERSON><PERSON>", "itemGroup.foodAndDrink": "<PERSON>", "itemGroup.functional": "Funcshunal Blockz", "itemGroup.hotbar": "Savd <PERSON>bras", "itemGroup.ingredients": "<PERSON>", "itemGroup.inventory": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.natural": "catland bluks", "itemGroup.op": "Hax 4 Me", "itemGroup.redstone": "Redstone bluccz", "itemGroup.search": "Saerhc Itmz", "itemGroup.spawnEggs": "spon ecs", "itemGroup.tools": "toolz & ulitelites", "item_modifier.unknown": "know'nt item mudifyer: %s", "jigsaw_block.final_state": "Transforms to:", "jigsaw_block.generate": "Crete!!", "jigsaw_block.joint.aligned": "alyned", "jigsaw_block.joint.rollable": "Hairballable", "jigsaw_block.joint_label": "joynt typ:", "jigsaw_block.keep_jigsaws": "Keep Jig saw", "jigsaw_block.levels": "lvlz: %s", "jigsaw_block.name": "Naem:", "jigsaw_block.placement_priority": "Lokatiun Purrority:", "jigsaw_block.placement_priority.tooltip": "<PERSON> jigsaw blok kenekts with peice dis da ordar da peice kenekshunz get purrocessed in da big struktr.\n\nPeices b purrocessed as pawsition goez down but if saem then insercion ordar brakes da ties.", "jigsaw_block.pool": "Taaget Pul:", "jigsaw_block.selection_priority": "Selecshun Purrority:", "jigsaw_block.selection_priority.tooltip": "<PERSON> da paerunt peice getz itz kenekshuns purrocessed dis b da ordar da jig saw blok tryz to kenekt with itz shiney red dot.\n\nJigsawz b purrocessed as pryority goez down and tiez brake with purr chance.", "jigsaw_block.target": "Walmart naem:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (<PERSON><PERSON>h bokz)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Atfancemends", "key.attack": "HYAHHH", "key.back": "<PERSON><PERSON>", "key.categories.creative": "HAX MOD", "key.categories.gameplay": "Playing da game", "key.categories.inventory": "Mah stuff", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "hopz and bouncez", "key.categories.multiplayer": "I can haz friendz", "key.categories.ui": "<PERSON><PERSON>z", "key.chat": "Open Chatterbox", "key.command": "<PERSON><PERSON>", "key.drop": "Throw teh yarn away", "key.forward": "<PERSON>", "key.fullscreen": "Turn on nd off teh fullzcrin", "key.hotbar.1": "Front Pocket 1", "key.hotbar.2": "Front Pocket 2", "key.hotbar.3": "Front Pocket 3", "key.hotbar.4": "Front Pocket 4", "key.hotbar.5": "Front Pocket 5", "key.hotbar.6": "Front Pocket 6", "key.hotbar.7": "Front Pocket 7", "key.hotbar.8": "Front Pocket 8", "key.hotbar.9": "Front Pocket 9", "key.inventory": "Opun/Cloze Secret fur pouch", "key.jump": "Jumpy jumpy jumpy", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "oops no", "key.keyboard.caps.lock": "CAPZ", "key.keyboard.comma": ",", "key.keyboard.delete": "DELET DIS", "key.keyboard.down": "<PERSON>own Arrowz", "key.keyboard.end": "End", "key.keyboard.enter": "Entr", "key.keyboard.equal": "=", "key.keyboard.escape": "escap", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Kittybox", "key.keyboard.insert": "Insurrt", "key.keyboard.keypad.0": "Numbur 0", "key.keyboard.keypad.1": "Numbur 1", "key.keyboard.keypad.2": "Numbur 2", "key.keyboard.keypad.3": "Numbur 3", "key.keyboard.keypad.4": "Numbur 4", "key.keyboard.keypad.5": "Numbur 5", "key.keyboard.keypad.6": "Numbur 6", "key.keyboard.keypad.7": "Numbur 7", "key.keyboard.keypad.8": "Numbur 8", "key.keyboard.keypad.9": "Numbur 9", "key.keyboard.keypad.add": "Numbur +", "key.keyboard.keypad.decimal": "Numbur '.'", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "<PERSON><PERSON>", "key.keyboard.left.alt": "Lefty Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Lefty Control", "key.keyboard.left.shift": "Lefty Shiftz", "key.keyboard.left.win": "<PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Numbz Lockz", "key.keyboard.page.down": "<PERSON><PERSON>", "key.keyboard.page.up": "Pagez Up", "key.keyboard.pause": "Pawz", "key.keyboard.period": ".", "key.keyboard.print.screen": "Prnt Screenz", "key.keyboard.right": "<PERSON><PERSON>", "key.keyboard.right.alt": "Righty Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Righty Control", "key.keyboard.right.shift": "Righty Shiftz", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "Scrolls Lockz", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Spacez", "key.keyboard.tab": "Tabz", "key.keyboard.unknown": "nO key hEre", "key.keyboard.up": "Up Arrowz", "key.keyboard.world.1": "Wurld 1", "key.keyboard.world.2": "Wurld 2", "key.left": "Roll Left", "key.loadToolbarActivator": "Lowd Hawtbar Ektivator", "key.mouse": "Button %1$s", "key.mouse.left": "Left Paw", "key.mouse.middle": "Middl Paw", "key.mouse.right": "Right Paw", "key.pickItem": "<PERSON>", "key.playerlist": "Show utur <PERSON>", "key.quickActions": "<PERSON><PERSON>", "key.right": "Roll Right", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON> Ektivetor", "key.screenshot": "makin funny picz fer interwebz", "key.smoothCamera": "Switch teh fanceh (feast) camera on nd off", "key.sneak": "Tip<PERSON><PERSON>", "key.socialInteractions": "Soshul interacshuns screen", "key.spectatorOutlines": "Mak teh catz shinies (Spectutorz)", "key.sprint": "Sprint", "key.swapOffhand": "Swap itemz in pawz", "key.togglePerspective": "chang teh way kitteh look<PERSON>z", "key.use": "Uze Item/Plac blukz", "known_server_link.announcements": "Impottent stufz", "known_server_link.community": "Katmunity", "known_server_link.community_guidelines": "Kat rulz", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forumz", "known_server_link.news": "Newz", "known_server_link.report_bug": "<PERSON>awt srvre bug", "known_server_link.status": "Meowtuz", "known_server_link.support": "Suport", "known_server_link.website": "Websyte", "lanServer.otherPlayers": "Settings 4 <PERSON><PERSON> Katz", "lanServer.port": "Port numbr", "lanServer.port.invalid": "Not valid port. Sr<PERSON><PERSON> leef teh edit box empty or entr numbr tween 1024 an 65535.", "lanServer.port.invalid.new": "Not valed powt :(\n<PERSON><PERSON> teh edit box empteh or entr difewant numbr betwiin %s an %s.", "lanServer.port.unavailable": "Powt not available :(\n<PERSON><PERSON> teh edit box empteh or entr difewant numbr tween 1k24 an 6 5 5 3 5.", "lanServer.port.unavailable.new": "Powt not available :(\n<PERSON><PERSON> teh edit box empteh or entr difewant numbr betwiin %s an %s.", "lanServer.scanning": "<PERSON><PERSON>in for nearby kittenz", "lanServer.start": "Start LAN Wrld", "lanServer.title": "LAN Wrld", "language.code": "qll", "language.name": "LOLCAT", "language.region": "Kingdom of Cats", "lectern.take_book": "Taek <PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "Read moar aboth <PERSON>g and teh privacy laws", "mco.account.privacy.info.button": "Raed moar abut GDPR", "mco.account.privacy.information": "Mojang implements certain procedurez 2 halp protect children an their privacy includin complyin wif teh Children’s Online Privacy Protection Act (COPPA) an General Data Protection Regulation (GDPR).\n\nU cud ned 2 obtain parental conset before accesin ur Realms accunt.", "mco.account.privacyinfo": "Mojang implements certain procedurez 2 halp protect kittens an their privacy includin complyin wif teh Kitten’s Online Privacy Protection Act (COPPA) an General Data Protection Regulation (GDPR).\n\nU cud ned 2 obtain parental consent before accesin ur Realms accunt.\n\nIf u has an oldr Minecraft account (u log in wif ur username), u ned 2 migrate teh accunt 2 Mojang accunt in ordr 2 acces Realms.", "mco.account.update": "<PERSON>det ur akkunt", "mco.activity.noactivity": "No activity 4 teh past %s dai(s)", "mco.activity.title": "Katz actibity", "mco.backup.button.download": "Downwowd Watest meow", "mco.backup.button.reset": "Reset <PERSON>", "mco.backup.button.restore": "Brings bak teh old wan", "mco.backup.button.upload": "<PERSON><PERSON>d wurld", "mco.backup.changes.tooltip": "<PERSON>z", "mco.backup.entry": "Bakapz (%s)", "mco.backup.entry.description": "Descripshun", "mco.backup.entry.enabledPack": "Ok pack(z)", "mco.backup.entry.gameDifficulty": "<PERSON><PERSON><PERSON>", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON> moed", "mco.backup.entry.gameServerVersion": "Gaem Svr <PERSON>", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>", "mco.backup.entry.undefined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.uploaded": "Uplodid", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON>", "mco.backup.generate.world": "Generate wurld", "mco.backup.info.title": "Chanjz frum last backap", "mco.backup.narration": "Backup frum %s", "mco.backup.nobackups": "Dis Realm doesnt has any bakups currently, plz ask kat 4 wan.", "mco.backup.restoring": "<PERSON><PERSON>n yo <PERSON>", "mco.backup.unknown": "IDK", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "Downloaded", "mco.brokenworld.message.line1": "Plz rezet r zelect nathr wurld.", "mco.brokenworld.message.line2": "U can also chooz 2 download teh wurld 2 singlplayur.", "mco.brokenworld.minigame.title": "Diz mini-gaem iznt suportd anymoar", "mco.brokenworld.nonowner.error": "plz wait 4 the Realm master 2 reset the wurld", "mco.brokenworld.nonowner.title": "Wurld iz 2 old", "mco.brokenworld.play": "GO", "mco.brokenworld.reset": "rezet", "mco.brokenworld.title": "Ur currnt wurld is nawt suportd anymoar", "mco.client.incompatible.msg.line1": "Ur kat iz nawt kompatibl wif Realms.", "mco.client.incompatible.msg.line2": "Plz us teh most recent vershun ov Minecraft.", "mco.client.incompatible.msg.line3": "Realms iz nawht kompatibl wif snepshut vershunz.", "mco.client.incompatible.title": "Kat <PERSON>om<PERSON>l!", "mco.client.outdated.stable.version": "Ur klient vershun (%s) dont wanna be with Realms.\n\nPlz use the most resent vershun of Minecraft.", "mco.client.unsupported.snapshot.version": "Ur klient vershun (%s) dont wanna be with Realms.\n\nRealms is nut ok for this snapshot vershun.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "Dis werld woz last playd in verzhun %s and UR on vershun %s. <PERSON><PERSON><PERSON><PERSON><PERSON>in a weorld cud make it brokn and minekraft kitteh say it mit not werk.\n\nA bakup uv ur werld will be savd undr \"Werld bakups\". Pls restor ur world if needd.", "mco.compatibility.incompatible.popup.title": "Incompatible vershun", "mco.compatibility.incompatible.releaseType.popup.message": "Teh wrld ur tryna get in is inkompatibul w/ teh vershun ur on.", "mco.compatibility.incompatible.series.popup.message": "Dis wrld wuz last playd in vershun %s; ur in vershun %s!!\n\nDese siriz are not kompatibol wit each odur. U gotta start a new wurld 2 go on playin w/ dis vershun.", "mco.compatibility.unverifiable.message": "Da verzhun dis werld wos last playd in cud not be chekd by minekraft kitteh. If da world getz upgraedd or dawhngraedd, a bakup will be maed by minekraft robot and savd undr \"Werld bakups\".", "mco.compatibility.unverifiable.title": "Komblahblahbilty kant B chekd by minekraft kat", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Dis werld woz last playd in verzhun %s and UR on vershun %s.\n\nA bakup uv ur werld will be savd undr \"Werld bakups\".\n\nPls restor ur world if needd.", "mco.compatibility.upgrade.friend.description": "Dis world waz last playd in verzhun %s; u r on verzhun %s.\n\nA bakup of the world will be savd undr \"World Backups\".\n\nThe ownr of the Realm kan restor the world if need-d.", "mco.compatibility.upgrade.title": "do u RLY wanna upgraed dis!?!??!?!", "mco.configure.current.minigame": "NOW", "mco.configure.world.activityfeed.disabled": "catz f33d temporali off", "mco.configure.world.backup": "<PERSON><PERSON><PERSON> bak<PERSON>z", "mco.configure.world.buttons.activity": "Cat Activity", "mco.configure.world.buttons.close": "Temp <PERSON>", "mco.configure.world.buttons.delete": "DELET DIS", "mco.configure.world.buttons.done": "Dun", "mco.configure.world.buttons.edit": "set<PERSON><PERSON>", "mco.configure.world.buttons.invite": "<PERSON><PERSON>te kitteh", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON> op<PERSON>", "mco.configure.world.buttons.newworld": "New Wooowrld", "mco.configure.world.buttons.open": "Reopn Realm", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "Cats", "mco.configure.world.buttons.region_preference": "Selict Regi0n...", "mco.configure.world.buttons.resetworld": "Reset <PERSON>", "mco.configure.world.buttons.save": "<PERSON><PERSON>", "mco.configure.world.buttons.settings": "set<PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Subscripshuns", "mco.configure.world.buttons.switchminigame": "Switcc Kitteh Gae<PERSON>zz", "mco.configure.world.close.question.line1": "U can temp klos ur Realm, so dat kittehz kant pley wil u maek chanjz. Js opn it bck up wen ur redy. \n\nDis dose not kancel ur Realms sub sub.", "mco.configure.world.close.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.close.question.title": "Nid 2 maik changis wizoud dizruption?", "mco.configure.world.closing": "Temp closin teh Realm...", "mco.configure.world.commandBlocks": "Comnd Blox", "mco.configure.world.delete.button": "Kil Realm", "mco.configure.world.delete.question.line1": "Ur Realm will b gon 4 evr", "mco.configure.world.delete.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.description": "Realm deskripshun", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON> naym", "mco.configure.world.edit.subscreen.adventuremap": "Sum setinz r disabld since ur currnt wurld iz an advenchur", "mco.configure.world.edit.subscreen.experience": "<PERSON>m settinz r disabld since ur current wurld iz an experience", "mco.configure.world.edit.subscreen.inspiration": "<PERSON>m settinz r disabld since ur current wurld iz an inspirayshun", "mco.configure.world.forceGameMode": "<PERSON><PERSON> G<PERSON>", "mco.configure.world.invite.narration": "u has %s new invite(z)", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Invitd", "mco.configure.world.invited.number": "Invitd (%s)", "mco.configure.world.invites.normal.tooltip": "Normal Kitteh", "mco.configure.world.invites.ops.tooltip": "Adminkitteh", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "If u leef dis Realm u wont c it unles invitd again", "mco.configure.world.leave.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.loading": "Lodein <PERSON>", "mco.configure.world.location": "Locashun", "mco.configure.world.minigame": "Now: %s", "mco.configure.world.name": "Realm naem", "mco.configure.world.opening": "Opnin teh Realm...", "mco.configure.world.players.error": "A kat with da provided naem does nawt exist", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON> kat...", "mco.configure.world.players.title": "Other cats", "mco.configure.world.pvp": "CVC", "mco.configure.world.region_preference": "Regi0n prefirnce", "mco.configure.world.region_preference.title": "Regi0n Prefrenzz Selecztion", "mco.configure.world.reset.question.line1": "Ur wurld will b regeneratd an ur currnt wurld will b lost", "mco.configure.world.reset.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.resourcepack.question": "U ned a costum resauc pac 2 pley un dis Realm\n\nDo u rly wanna dawnlod it n pley?", "mco.configure.world.resourcepack.question.line1": "U need a custm resource pack 2 play on dis Realm", "mco.configure.world.resourcepack.question.line2": "Do u wan 2 automaticly dwonlod & instll it 2 pley?", "mco.configure.world.restore.download.question.line1": "<PERSON><PERSON> wurld will b downloadd an addd to your single playr wurldz.", "mco.configure.world.restore.download.question.line2": "Do u wants 2 continue?", "mco.configure.world.restore.question.line1": "<PERSON><PERSON> wurld will b restord 2 date '%s' (%s)", "mco.configure.world.restore.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.settings.expired": "U cannot edit da setingz uv expird Realm", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Wurld %s", "mco.configure.world.slot.empty": "emptie", "mco.configure.world.slot.switch.question.line1": "Ur Realm will be sandwitched 2 another wurld", "mco.configure.world.slot.switch.question.line2": "arr joo surr joo wan 2 do diz????", "mco.configure.world.slot.tooltip": "Sandwitch 2 wurld", "mco.configure.world.slot.tooltip.active": "Join", "mco.configure.world.slot.tooltip.minigame": "Switch 2 kitty-game", "mco.configure.world.spawnAnimals": "Zpawn Animalz", "mco.configure.world.spawnMonsters": "Zpawn Monzterz", "mco.configure.world.spawnNPCs": "Zpawn NPCz", "mco.configure.world.spawnProtection": "No Bwock Breaky Awowed", "mco.configure.world.spawn_toggle.message": "Iv u torn off dis it wil delet ol kittehz ov dat type", "mco.configure.world.spawn_toggle.message.npc": "Iv u torn off dis it wil delet ol kittehz ov dat type, like Big Noz", "mco.configure.world.spawn_toggle.title": "Uh oh!", "mco.configure.world.status": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "dai", "mco.configure.world.subscription.days": "dais", "mco.configure.world.subscription.expired": "Ded", "mco.configure.world.subscription.extend": "Extend Subscrioshun", "mco.configure.world.subscription.less_than_a_day": "Les tehn dai", "mco.configure.world.subscription.month": "motnh", "mco.configure.world.subscription.months": "motnhs", "mco.configure.world.subscription.recurring.daysleft": "Renewd automagically in", "mco.configure.world.subscription.recurring.info": "Changez made 2 ur Realmz subscripshun such as stackin tiem or turnin off recurrin billin wil not be reflectd til ur next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s dae(z)", "mco.configure.world.subscription.remaining.months": "%1$s month(z)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(z), %2$s day(z)", "mco.configure.world.subscription.start": "<PERSON>t daet", "mco.configure.world.subscription.tab": "Subscrpshuns", "mco.configure.world.subscription.timeleft": "Tiem levd", "mco.configure.world.subscription.title": "Zubzcripshion infu", "mco.configure.world.subscription.unknown": "IDK", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> wurld", "mco.configure.world.switch.slot.subtitle": "Diz wurld is empti, plz chooz what 2 do", "mco.configure.world.title": "Convigr Realm:", "mco.configure.world.uninvite.player": "R u sure dat u wants 2 uninviet '%s'?", "mco.configure.world.uninvite.question": "R u sure dat u wants 2 uninvite", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON>", "mco.connect.authorizing": "Lowoging in...", "mco.connect.connecting": "Konnectin 2 teh Realm...", "mco.connect.failed": "Faild 2 konnect 2 teh Realm", "mco.connect.region": "Srevrs rigion: %s", "mco.connect.success": "Dun", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "U mast entr a naem!", "mco.create.world.failed": "Kitteh no kan maek wurld!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON> wurld...", "mco.create.world.skip": "Sqip", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON>, selact wut wurld 2 put on ur new Realm", "mco.create.world.wait": "Creatin teh <PERSON>...", "mco.download.cancelled": "Download cancelld", "mco.download.confirmation.line1": "Teh wurld u r goin 2 download iz largr than %s", "mco.download.confirmation.line2": "U wont be able 2 upload dis wurld 2 Realm again", "mco.download.confirmation.oversized": "Da wurld u r gonna dawnlond iz bigr then %s\n\nU kant uplod dis wuld to ur Realm agan", "mco.download.done": "Kat finishd download", "mco.download.downloading": "Downloadin'", "mco.download.extracting": "Extrakting", "mco.download.failed": "Kat faild 2 download", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparin download", "mco.download.resourcePack.fail": "Cant downlud resauce pack!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Downldng Latus<PERSON>", "mco.error.invalid.session.message": "restart ur gaem", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON> se<PERSON>on", "mco.errorMessage.6001": "Kat outdatd", "mco.errorMessage.6002": "ToS not acceptd", "mco.errorMessage.6003": "Download limit reachd", "mco.errorMessage.6004": "Upload limit reachd", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON> luckd", "mco.errorMessage.6006": "Wurld iz 2 old", "mco.errorMessage.6007": "Usr in tuu many Realms", "mco.errorMessage.6008": "Bad Realm naem", "mco.errorMessage.6009": "Bad Realm deskripshun", "mco.errorMessage.connectionFailure": "An error, lol. <PERSON><PERSON><PERSON> try again latr.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON> bad hapen: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON><PERSON> kant inishaliz <PERSON>", "mco.errorMessage.noDetails": "No error detailz givn", "mco.errorMessage.realmsService": "<PERSON><PERSON><PERSON> bad hapen (%s):", "mco.errorMessage.realmsService.configurationError": "1 unekzpektd eror happnd wil edit1n wurld opshunz", "mco.errorMessage.realmsService.connectivity": "Culd nawt connect 2 Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Culd nut chec compatibel vershun, got responc: %s", "mco.errorMessage.retry": "Re<PERSON>i <PERSON>hun", "mco.errorMessage.serviceBusy": "Relmz buzi rn.\npls connect realm later idk", "mco.gui.button": "Butonn", "mco.gui.ok": "gud", "mco.info": "Info!", "mco.invited.player.narration": "Invietd kitteh %s", "mco.invites.button.accept": "Accept", "mco.invites.button.reject": "Reject", "mco.invites.nopending": "No pandin invitez!", "mco.invites.pending": "New invite(z)!!", "mco.invites.title": "<PERSON><PERSON>", "mco.minigame.world.changeButton": "Select Another <PERSON>", "mco.minigame.world.info.line1": "<PERSON><PERSON> will tamporarly replace ur wurld wif a mini gaem!", "mco.minigame.world.info.line2": "U can latr return 2 ur original wurld wifut lozin anythin.", "mco.minigame.world.noSelection": "Plz mak selecshun", "mco.minigame.world.restore": "Ending Kitty Game...", "mco.minigame.world.restore.question.line1": "Teh mini game will end an ur Realm will be restord.", "mco.minigame.world.restore.question.line2": "R u sure u wants 2 do dis?", "mco.minigame.world.selected": "Sewected kitty geam:", "mco.minigame.world.slot.screen.title": "Sandwitchin litter box...", "mco.minigame.world.startButton": "Sandwich", "mco.minigame.world.starting.screen.title": "Startin Kitty <PERSON>...", "mco.minigame.world.stopButton": "<PERSON> <PERSON>", "mco.minigame.world.switch.new": "Select another mini gaem?", "mco.minigame.world.switch.title": "Switcc Kitteh Gae<PERSON>zz", "mco.minigame.world.title": "Switcc Realm 2 Kitteh Gaem", "mco.news": "Realms newz", "mco.notification.dismiss": "Dizmiz", "mco.notification.transferSubscription.buttonText": "Mov rn!", "mco.notification.transferSubscription.message": "Java Realms subz are moovin' to teh Microsoft Store. Kitteh dont let your sub expiar!\nMov now and get 30 dayz of Realms 4 FREE.\nGo to Profile un minecraft.net 2 mov ur sub.", "mco.notification.visitUrl.buttonText.default": "Open teh link", "mco.notification.visitUrl.message.default": "P<PERSON>ase visit da link below", "mco.onlinePlayers": "<PERSON><PERSON> playign", "mco.play.button.realm.closed": "Realm iz slepin", "mco.question": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Catventures", "mco.reset.world.experience": "Experiences", "mco.reset.world.generate": "New Wurld", "mco.reset.world.inspiration": "Inpirayshun", "mco.reset.world.resetting.screen.title": "Resettin wurld...", "mco.reset.world.seed": "<PERSON><PERSON> (Opshional)", "mco.reset.world.template": "<PERSON> will show u <PERSON>z", "mco.reset.world.title": "Reset <PERSON>", "mco.reset.world.upload": "Uplod Wrld", "mco.reset.world.warning": "Dis will delete ur Realms current wurld!", "mco.selectServer.buy": "Buy Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Ded Realm", "mco.selectServer.closeserver": "<PERSON><PERSON>", "mco.selectServer.configure": "confugre realm", "mco.selectServer.configureRealm": "Convigr Realm", "mco.selectServer.create": "creat relm", "mco.selectServer.create.subtitle": "<PERSON>woose wat wurld 2 put on ur niw Realm", "mco.selectServer.expired": "Ded Realm", "mco.selectServer.expiredList": "Ur realm is ded", "mco.selectServer.expiredRenew": "Mak it life agehn", "mco.selectServer.expiredSubscribe": "SUBSKWIBE", "mco.selectServer.expiredTrial": "Ur triel is ded", "mco.selectServer.expires.day": "Expirez in a dai", "mco.selectServer.expires.days": "Expirez in %s dais", "mco.selectServer.expires.soon": "Expirez soon", "mco.selectServer.leave": "Lev Realm", "mco.selectServer.loading": "Lodein Realms lis", "mco.selectServer.mapOnlySupportedForVersion": "Diz map iz no suportd in %s", "mco.selectServer.minigame": "Kittygame:", "mco.selectServer.minigameName": "Kittygaem: %s", "mco.selectServer.minigameNotSupportedInVersion": "Cent pley diz mini-gaem in %s", "mco.selectServer.noRealms": "Yu dont seem to havv a Realm. Add a Realm to pley 2gether wit ur frindz.", "mco.selectServer.note": "Noet:", "mco.selectServer.open": "Opn Realm", "mco.selectServer.openserver": "Opn Realm", "mco.selectServer.play": "GO", "mco.selectServer.popup": "Realms iz a seif & simpl wey 2 enjoi n onlin Minecraft wurld wif ap 2 10 friendz @ a tiem. It suprtz many kitty-gaemz & plany ov cuztm wurldz! Onli teh ohnor ov teh Realm nedz 2 pey.", "mco.selectServer.purchase": "ad reelm", "mco.selectServer.trial": "Tri it!", "mco.selectServer.uninitialized": "Click 2 Create Realm!", "mco.snapshot.createSnapshotPopup.text": "Ur abt 2 maek a free znapzhut Realm dat will B paird wif ur payd Realms thingy. Dis neu znupzhut Realm will still b aliev until u stop payin. Ur payed Realm will not b affektd", "mco.snapshot.createSnapshotPopup.title": "Maek Znapzhut Realm?", "mco.snapshot.creating": "<PERSON><PERSON>...", "mco.snapshot.description": "Paird wif %s", "mco.snapshot.friendsRealm.downgrade": "U ned to B on Da %sish version to jion dis relm", "mco.snapshot.friendsRealm.upgrade": "%s needz 2 upgraed der Realm b4 u kan play frum dis vershun", "mco.snapshot.paired": "Dis spanzhut Realm is paird wif %s", "mco.snapshot.parent.tooltip": "<PERSON><PERSON> da latets releess ov Minecraft 2 play on dis Realm", "mco.snapshot.start": "Start fre spanzhut Realm", "mco.snapshot.subscription.info": "Dis is a znupshot Realm dat is paird 2 da supskripshun uv ur Realm \"%s\". It will stay aktivf 4 az long az der payrd Realm iz!!1!.", "mco.snapshot.tooltip": "Uez znupshet Realms z2 get a zneek peek!!!1!! At upkumin verzhuns of Minecraft, wic mit inkluud niuuw feetures n ovver sutff.\n\nu kan find ur normal 1 in da relees verzhuns of da gaem.", "mco.snapshotRealmsPopup.message": "Realms r now ok in Snapshutz startin' wit Snapshot 23w41a. Every Realms sub comez wit a FrEE Snapshut Realm that is sep frum ur normal Java Realm!!", "mco.snapshotRealmsPopup.title": "Realms kan naw doo snapzhut verzhuns!!!1!", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON>", "mco.template.button.publisher": "Publishr", "mco.template.button.select": "Zelect", "mco.template.button.trailer": "Trailur", "mco.template.default.name": "Wurld template", "mco.template.info.tooltip": "Publishr websiet", "mco.template.name": "Template", "mco.template.select.failure": "we cannot see what it's in the box!!\nplez chek if wi-fi is correcc or try later!!1!", "mco.template.select.narrate.authors": "Autrs: %s", "mco.template.select.narrate.version": "model %s", "mco.template.select.none": "WOOOPS!!! lok liek dis content catgory iz empty... plz chek bak l8ter for niew content!! or if you create, %s.", "mco.template.select.none.linkTitle": "conzider submiting someting yurself", "mco.template.title": "<PERSON><PERSON><PERSON>tz", "mco.template.title.minigame": "Kittygames", "mco.template.trailer.tooltip": "Map trailur", "mco.terms.buttons.agree": "Mkay", "mco.terms.buttons.disagree": "Lul nope", "mco.terms.sentence.1": "I agree 2 Minecraft Realms", "mco.terms.sentence.2": "Turms ov Servise", "mco.terms.title": "Realms Terms ov Service", "mco.time.daysAgo": "%1$s dae(s) agoe", "mco.time.hoursAgo": "%1$s hr(s) agoe", "mco.time.minutesAgo": "%1$s minut(s) agoo", "mco.time.now": "rait now", "mco.time.secondsAgo": "%1$s secund(s) agoe", "mco.trial.message.line1": "Wan 2 gat ur own Realm?", "mco.trial.message.line2": "Clic here 4 moar info!", "mco.upload.button.name": "Upload", "mco.upload.cancelled": "Upload cancelld", "mco.upload.close.failure": "Cud nawt close ur Realm, plz try again latr", "mco.upload.done": "Upload dun", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Upload faild! (%s)", "mco.upload.failed.too_big.description": "<PERSON><PERSON> choosd wurld iz 2 beeg. Te max aloud sais izz %s.", "mco.upload.failed.too_big.title": "Wurld 2 BIG", "mco.upload.hardcore": "Pawtastic wurlds cant be uploadd!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparin ur wurld data", "mco.upload.select.world.none": "No singlplayur wurldz found!", "mco.upload.select.world.subtitle": "Plz selakt a singlplayur wurld 2 upload", "mco.upload.select.world.title": "Uplod Wrld", "mco.upload.size.failure.line1": "'%s' iz 2 bihg!", "mco.upload.size.failure.line2": "It iz %s. Teh max alowd size iz %s.", "mco.upload.uploading": "Kat iz uploadin '%s'", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON> ur wurld", "mco.version": "Verzhun: %s", "mco.warning": "Uh oh!", "mco.worldSlot.minigame": "Kittygame", "menu.custom_options": "Custum opshns....", "menu.custom_options.title": "Custm opshns", "menu.custom_options.tooltip": "Custm opshunz r provid bai 3rd-party svrz &/ kontnt.\nBe cawshus!!", "menu.custom_screen_info.button_narration": "<PERSON><PERSON> is a custm skreen. Lurn moar.", "menu.custom_screen_info.contents": "Des comntent of thes screean r comtrowled by a third-pawrty serverz amd maps that r not ownd, operatd, or suparvisd by Mojang Studios ore Microsoft.\n\nHandol with caere! Alway be caereful when folowing links and Never give up ur personal informanation, incliuding login detials.\n\nIf this screen prefents u form playing, u can also discomnect from the current serverz by useing the button below..", "menu.custom_screen_info.disconnect": "castom sk<PERSON><PERSON> said no", "menu.custom_screen_info.title": "Notzz abotz kustm skreenzz", "menu.custom_screen_info.tooltip": "<PERSON><PERSON> is a custm skreen. <PERSON><PERSON> hear 2 lurn moar.", "menu.disconnect": "imma go now", "menu.feedback": "Meowbacc...", "menu.feedback.title": "Meowbacc", "menu.game": "<PERSON><PERSON><PERSON>", "menu.modded": " (Moaddded!)", "menu.multiplayer": "I can haz friendz", "menu.online": "Minecraft Realms", "menu.options": "Opshuns...", "menu.paused": "gaem stopd", "menu.playdemo": "<PERSON><PERSON><PERSON>", "menu.playerReporting": "Kat Repworting", "menu.preparingSpawn": "Preepayrin spawn ayyrea: %s%%", "menu.quick_actions": "Quck <PERSON>ons...", "menu.quick_actions.title": "<PERSON><PERSON>", "menu.quit": "kthxbai", "menu.reportBugs": "Catch Bugz", "menu.resetdemo": "<PERSON><PERSON><PERSON>", "menu.returnToGame": "kk im back", "menu.returnToMenu": "<PERSON><PERSON> and cwit tu titel", "menu.savingChunks": "Scratching the disk", "menu.savingLevel": "<PERSON><PERSON> wurld", "menu.sendFeedback": "Feed Us", "menu.server_links": "Sehvur Linkz...", "menu.server_links.title": "Sehvur Linkz", "menu.shareToLan": "Opn 2 LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON> kitteh", "menu.working": "Cat too lazy to work...", "merchant.deprecated": "<PERSON><PERSON><PERSON><PERSON> restoc ahp two to tiemz purrr dai.", "merchant.level.1": "Noviec", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Ekspurt", "merchant.level.5": "MasTr", "merchant.title": "%s - %s", "merchant.trades": "Tradez", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Prezz %1$s tu Dizmount", "multiplayer.applyingPack": "Ap<PERSON>inn resorce pak", "multiplayer.confirm_command.parse_errors": "Ur tryna to execute an unrecgonized or invaild command.\nr u sure?\nConmand: %s", "multiplayer.confirm_command.permissions_required": "Ur tryna to execute an command that rekwires higher permisions.\nThis may negateiviy effect ur gamepaly.\nr u sure?\nCommand: %s", "multiplayer.confirm_command.title": "Konfurm Cmnd Ekzecution", "multiplayer.disconnect.authservers_down": "Authenticashun srvrs r down. Pls com bak latr, sry!1", "multiplayer.disconnect.bad_chat_index": "Reseivd mized oer redredeod chatz mesage frum servur", "multiplayer.disconnect.banned": "ur bannd from dis sever", "multiplayer.disconnect.banned.expiration": "ur ban wil be rmvd on %s", "multiplayer.disconnect.banned.reason": "<PERSON><PERSON><PERSON> cat bannd u from dis srvr. Resun: %s", "multiplayer.disconnect.banned_ip.expiration": "yoyr bean wil be endd on %s", "multiplayer.disconnect.banned_ip.reason": "<PERSON><PERSON><PERSON> cat bannd u from dis srvr. Resun: %s", "multiplayer.disconnect.chat_validation_failed": "chatty meow verifiurr epik fail!!!", "multiplayer.disconnect.duplicate_login": "U logd in frum another locashun", "multiplayer.disconnect.expired_public_key": "Expierd plofill pabllick kee. chek dat uar sistemm tim iz sinklonyzd, end try restating ur gaem ", "multiplayer.disconnect.flying": "Flyin iz nawt enabld on dis srvr", "multiplayer.disconnect.generic": "CYA", "multiplayer.disconnect.idling": "U hav ben idle 4 2 long!", "multiplayer.disconnect.illegal_characters": "Not OK characterz in c(h)at", "multiplayer.disconnect.incompatible": "Incmpatibl clynt! Plz uze %s", "multiplayer.disconnect.invalid_entity_attacked": "Attemptin 2 attack an not valid entity", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON><PERSON> sent ey weird paket", "multiplayer.disconnect.invalid_player_data": "Weird cat dataz", "multiplayer.disconnect.invalid_player_movement": "Weird move player paket receivd", "multiplayer.disconnect.invalid_public_key_signature": "diz signatur isnt usbl sowwy\ntry restart ur gam maybe dis will help (i hope)", "multiplayer.disconnect.invalid_public_key_signature.new": "Dis signatur for profle public ki is unusabel :(\nTrai restartin ur game", "multiplayer.disconnect.invalid_vehicle_movement": "Weird moov vehicl paket receivd", "multiplayer.disconnect.ip_banned": "Cat iz banned from ip from dis server", "multiplayer.disconnect.kicked": "kikk ez by oprt9r", "multiplayer.disconnect.missing_tags": "Incomplat sec of tehs recived fom servah.\nPlez contac servah oporota.", "multiplayer.disconnect.name_taken": "dat nam is taken, try cat100 or catcat", "multiplayer.disconnect.not_whitelisted": "ur not part of th trulolcatclub100!d!&1", "multiplayer.disconnect.out_of_order_chat": "Rong chatpacket gotten. much sistm timechange?", "multiplayer.disconnect.outdated_client": "ur gaem iz 2 uld 4 dis!!! plss uss %s insted wololo", "multiplayer.disconnect.outdated_server": "ur gaem iz 2 uld 4 dis!!! plss uss %s insted wololo", "multiplayer.disconnect.server_full": "Dat servR is compleet!!!!!", "multiplayer.disconnect.server_shutdown": "Servur iz ded", "multiplayer.disconnect.slow_login": "Took 2 long 2 login", "multiplayer.disconnect.too_many_pending_chats": "2 mane un-undrrstnaded chatty msgz", "multiplayer.disconnect.transfers_disabled": "Servr dosn't allow kittehs to moev out :(", "multiplayer.disconnect.unexpected_query_response": "UniCZpektet cuztum deeta frum CLAINT!!!", "multiplayer.disconnect.unsigned_chat": "Cat god receivd chat packet wif misin or invalid signachur.", "multiplayer.disconnect.unverified_username": "Fail'd 2 verifi us<PERSON>!", "multiplayer.downloadingStats": "getting catistics...", "multiplayer.downloadingTerrain": "<PERSON>din cheezburgerz...", "multiplayer.lan.server_found": "nuu seUWUver fauwnd: %s", "multiplayer.message_not_delivered": "Can nawt dilivr meow mesadg, check srvr lugs: %s", "multiplayer.player.joined": "%s joind teh game", "multiplayer.player.joined.renamed": "%s (used 2 b %s) joind teh game", "multiplayer.player.left": "%s leeft teh gaem", "multiplayer.player.list.hp": "%shelf lol", "multiplayer.player.list.narration": "Kats playign: %s", "multiplayer.requiredTexturePrompt.disconnect": "Survur nedz a vizyual", "multiplayer.requiredTexturePrompt.line1": "Dis survur nedz u 2 use a cuztm vishul.", "multiplayer.requiredTexturePrompt.line2": "Sayin no 2 thiz vizyual wil nope u from dis survur.", "multiplayer.socialInteractions.not_available": "Soshul interacshuns r ownly avalible in utur kittehz werlds", "multiplayer.status.and_more": "... & %s moar ...", "multiplayer.status.cancelled": "St<PERSON>d", "multiplayer.status.cannot_connect": "Cant conect 2 servr", "multiplayer.status.cannot_resolve": "Cant reshulf ho<PERSON><PERSON>m", "multiplayer.status.finished": "Dun", "multiplayer.status.incompatible": "Incompatible vershun!", "multiplayer.status.motd.narration": "<PERSON> kit<PERSON><PERSON>'s wisdom: %s", "multiplayer.status.no_connection": "(naw konecticon)", "multiplayer.status.old": "Old", "multiplayer.status.online": "<PERSON><PERSON>", "multiplayer.status.ping": "%s millisecndz", "multiplayer.status.ping.narration": "ping is %s million sekandz", "multiplayer.status.pinging": "<PERSON><PERSON><PERSON>...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s outa %s katz playinn", "multiplayer.status.quitting": "KTHXBYE", "multiplayer.status.request_handled": "Staeduz rekwesst haz bin haendlt", "multiplayer.status.unknown": "???????????????", "multiplayer.status.unrequested": "Receivd unreqstd statuz", "multiplayer.status.version.narration": "Srevrs versin: %s", "multiplayer.stopSleeping": "geuts upsi", "multiplayer.texturePrompt.failure.line1": "Survur vizyual cotton't b applid", "multiplayer.texturePrompt.failure.line2": "Eni functshunliy dis requiz vizyua mait no werk cuz ekspectd", "multiplayer.texturePrompt.line1": "Dis servr recommendz teh use ov custom resource pack.", "multiplayer.texturePrompt.line2": "You wantz DL and installz 'em magic?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nDa servr kitteh sayz:\n%s", "multiplayer.title": "P<PERSON> with utur kittehz", "multiplayer.unsecureserver.toast": "hol on a minut ur msgs mite b not liek u r seein rite nao bc dis kitteh survur hasz moded yroue meows 2 do waccky thimabobz!!! or tey jus wan2 add fxs to ur msgs idk", "multiplayer.unsecureserver.toast.title": "chat msg(s) cant b verifi'd!!!", "multiplayerWarning.check": "Do not shw dis scren agin >:(", "multiplayerWarning.header": "Caushun: Thurd-Parteh Onlien Plae", "multiplayerWarning.message": "Coushn: On<PERSON> pley is offerd bah therd-porty servrs that not be ownd, oprtd, or suprvisd by M0jang\nStuds or micrsft. durin' onlain pley, yu mey be expsed to unmdrated chat messges or othr typs\nof usr-generatd content tht may not be suitable for othr kitty-cats.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Buhton: %s", "narration.button.usage.focused": "Prez Enter two aktiwait", "narration.button.usage.hovered": "Lefft clck to aktivate", "narration.checkbox": "Box: %s", "narration.checkbox.usage.focused": "Prez Intr 2 toggl", "narration.checkbox.usage.hovered": "Leaf clik 2 toggl", "narration.component_list.usage": "Prez tab 2 nevigmicate 2 next elemnt plz", "narration.cycle_button.usage.focused": "Press Entr to svitch to %s", "narration.cycle_button.usage.hovered": "Leaf clik 2 swich 2 %s", "narration.edit_box": "<PERSON><PERSON> boks: %s", "narration.item": "zing: %s", "narration.recipe": "how 2 meak %s", "narration.recipe.usage": "Levvt clck to selekt", "narration.recipe.usage.more": "Rite clik 2 show moar recipez", "narration.selection.usage": "Prez up an down buttonz to muv 2 anothr intry", "narration.slider.usage.focused": "Prez leaf or rite keyboord buttonz 2 change value", "narration.slider.usage.hovered": "<PERSON><PERSON><PERSON><PERSON>ahr two chang value", "narration.suggestion": "Zelectd sugezzton %s ut uf %s: %s", "narration.suggestion.tooltip": "Zelectd sugezzton %s ut uf %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON> to chanj to teh nex sujestion", "narration.suggestion.usage.cycle.hidable": "<PERSON>z <PERSON>b to chanj to da nex sujeztion, or Escape to leaf sujeztionz", "narration.suggestion.usage.fill.fixed": "Prez Tab to uz sujestion", "narration.suggestion.usage.fill.hidable": "Prez Tab to uz sujestion, or Escape to leaf sujestionz", "narration.tab_navigation.usage": "Pres Ctrl & Tab to switch betweeen tabz", "narrator.button.accessibility": "A<PERSON><PERSON>bil<PERSON>", "narrator.button.difficulty_lock": "Hardnez lock", "narrator.button.difficulty_lock.locked": "Lockd", "narrator.button.difficulty_lock.unlocked": "Unloked", "narrator.button.language": "Wat u re speek", "narrator.controls.bound": "%s iz bound 2 %s", "narrator.controls.reset": "Rezet %s butonn", "narrator.controls.unbound": "%s iz no bound", "narrator.joining": "<PERSON><PERSON><PERSON>", "narrator.loading": "Lodin: %s", "narrator.loading.done": "Dun", "narrator.position.list": "Selectd list rwo %s otu of %s", "narrator.position.object_list": "Selectd row elemnt %s owt of %s", "narrator.position.screen": "Scrin elemnt %s owt of %s", "narrator.position.tab": "Selectd tab %s otu of %s", "narrator.ready_to_play": "ME IZ REDY 2 PLAE!", "narrator.screen.title": "Teh Big Menu", "narrator.screen.usage": "Uze Mickey mouse cursr or tab bttn 2 slct kitteh", "narrator.select": "Selectd: %s", "narrator.select.world": "Selectd %s, last playd: %s, %s, %s, vershun: %s", "narrator.select.world_info": "Chosed %s, last pleyr: %s, %s", "narrator.toast.disabled": "Sp00knator ded", "narrator.toast.enabled": "Sp00knator ded", "optimizeWorld.confirm.description": "Do u want to upgrade ur world? Cat dont love to do this. If u upgrade ur world, the world may play faster! But cat says u cant do that, because will no longer be compatible with older versions of the game. THAT IS NOT FUN BUT TERRIBLE!! R u sure you wish to proceed?", "optimizeWorld.confirm.proceed": "Maek baccup and optimais", "optimizeWorld.confirm.title": "Optimaiz kitteh land", "optimizeWorld.info.converted": "Upgraeded pieces: %s", "optimizeWorld.info.skipped": "Skipped pieces: %s", "optimizeWorld.info.total": "Total pieces: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Cowntin pieces...", "optimizeWorld.stage.failed": "Oopsie doopsie! :(", "optimizeWorld.stage.finished": "Finnishin up...", "optimizeWorld.stage.finished.chunks": "<PERSON>ishin up upgraedin peices...", "optimizeWorld.stage.finished.entities": "<PERSON>ishin up upgraedin entitez...", "optimizeWorld.stage.finished.poi": "<PERSON>ishin up upgraedin pontz ov interez...", "optimizeWorld.stage.upgrading": "Upgraedin evry piece...", "optimizeWorld.stage.upgrading.chunks": "Upgraedin evry piece...", "optimizeWorld.stage.upgrading.entities": "Upgraedin evry entities...", "optimizeWorld.stage.upgrading.poi": "Upgraedin' awl pont of intewestz...", "optimizeWorld.title": "Optimaiziin kitteh land '%s'", "options.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "options.accessibility.high_contrast": "High Contwazt", "options.accessibility.high_contrast.error.tooltip": "High Contwazt resauce pak is nawt avialable.", "options.accessibility.high_contrast.tooltip": "LEVEL UP the contwazt of UI elementz.", "options.accessibility.high_contrast_block_outline": "High Kontrst blokk outlainz", "options.accessibility.high_contrast_block_outline.tooltip": "<PERSON><PERSON><PERSON> teh blok outlain kontrst ov tagettd blok.", "options.accessibility.link": "Aksesibilty Gaid", "options.accessibility.menu_background_blurriness": "Minyou bakraond blorrr", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON><PERSON><PERSON> how blurrreh teh menyiu bekrund iz", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Aloes teh Narrtur to b on n off wit 'Cmd+B'", "options.accessibility.narrator_hotkey.tooltip": "Aloes teh Narrtur to b on n off wit 'Ctrl+B'", "options.accessibility.panorama_speed": "movingness ov cuul bakgrund", "options.accessibility.text_background": "Tekst Bakround", "options.accessibility.text_background.chat": "<PERSON><PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Tekt Bakrund 'pacty", "options.accessibility.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "options.allowServerListing": "<PERSON>u<PERSON>", "options.allowServerListing.tooltip": "Plz Dunt Shuw Myz Multiplar Namez Plz Minraft! Plzzzz.", "options.ao": "<PERSON><PERSON>", "options.ao.max": "BIGGERER!!!!!!!", "options.ao.min": "ittiest bittiest", "options.ao.off": "naw", "options.attack.crosshair": "LAZR POINTR!", "options.attack.hotbar": "HAWTBAR!", "options.attackIndicator": "<PERSON><PERSON><PERSON> helpr", "options.audioDevice": "Technowogy", "options.audioDevice.default": "Sisten defolt", "options.autoJump": "<PERSON>-<PERSON>-<PERSON><PERSON>", "options.autoSuggestCommands": "Magic Stuff Suggestions", "options.autosaveIndicator": "autosaev indiCATor", "options.biomeBlendRadius": "mix baium", "options.biomeBlendRadius.1": "NOT (best for potato)", "options.biomeBlendRadius.11": "11x11 (not kul for pc)", "options.biomeBlendRadius.13": "13x13 (fur shaww)", "options.biomeBlendRadius.15": "15x15 (hypermegasupah hi')", "options.biomeBlendRadius.3": "3x3 (fazt)", "options.biomeBlendRadius.5": "fivx5 (normie)", "options.biomeBlendRadius.7": "7w7 (hi')", "options.biomeBlendRadius.9": "9x9 (supah hi')", "options.chat": "Chet Opshunz...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Delayyyyy: %s sec;)", "options.chat.delay_none": "Delayyyyy: no:(", "options.chat.height.focused": "<PERSON><PERSON><PERSON>", "options.chat.height.unfocused": "Unfocus<PERSON><PERSON>", "options.chat.line_spacing": "Lime spasin", "options.chat.links": "<PERSON> Linkz", "options.chat.links.prompt": "Prompt on <PERSON><PERSON>", "options.chat.opacity": "Persuntage Of Ninja text", "options.chat.scale": "Chata saiz", "options.chat.title": "<PERSON><PERSON>", "options.chat.visibility": "<PERSON><PERSON>", "options.chat.visibility.full": "Showed", "options.chat.visibility.hidden": "Hided", "options.chat.visibility.system": "Cmds only", "options.chat.width": "Waidnez", "options.chunks": "%s pieces", "options.clouds.fancy": "<PERSON><PERSON><PERSON>", "options.clouds.fast": "WEEEE!!!!", "options.controls": "Controlz...", "options.credits_and_attribution": "Creditz & Attribushun...", "options.damageTiltStrength": "pain shaek :c", "options.damageTiltStrength.tooltip": "da amont o kamera sHaKe cuzd by bein ouchd!", "options.darkMojangStudiosBackgroundColor": "oone culur lugo", "options.darkMojangStudiosBackgroundColor.tooltip": "Dis wil change da Mojang Studios loding skreen baccground kolor two blakk.", "options.darknessEffectScale": "Sussy fog", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON>l hao much da <PERSON> Efek pulses wen a Blu shrek or Sculk Yeller giv it 2 u.", "options.difficulty": "hardnez", "options.difficulty.easy": "EZ", "options.difficulty.easy.info": "evry mahbs cna zpon, butt them arr wimpy!!! u wil fele hungy n draggz ur hp dwon to at leat 5 wen u r starving!!!", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "evry mahbs cna zpon, n them arr STRONK!!! u wil fele hungy n u mite gu to slep foreva if u r starving 2 mucc!!!", "options.difficulty.hardcore": "YOLO", "options.difficulty.normal": "<PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "evry mahbs cna zpon, n deel norman dmgege. u wil fele hungy n draggz ur hp dwon to at leat 0,5 wen u r starving!!!", "options.difficulty.online": "Kittenz Zerverz Difcult", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "00 bad mahbs, onwy bery frienli mahbs spahn in dis hardnesz. u wil aslo nevar fele hungy n ur helth regens wifout eetin!!!", "options.directionalAudio": "where daz sound com frum", "options.directionalAudio.off.tooltip": "Ztereo madness do be vibin.", "options.directionalAudio.on.tooltip": "Usez HRTF-based direcshunal audio 2 improve teh simulashuj ov 3D sound. Requires HRRR compatible audio hardware, and iz best experienced wif headphones.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON>'", "options.entityDistanceScaling": "Nenity distance", "options.entityShadows": "<PERSON><PERSON>ty Shddows", "options.font": "<PERSON>t Setinz...", "options.font.title": "<PERSON><PERSON>", "options.forceUnicodeFont": "FORS UNICAT FONT", "options.fov": "FOV", "options.fov.max": "CATNIP", "options.fov.min": "<PERSON><PERSON><PERSON>", "options.fovEffectScale": "No kitty-cat eye effect", "options.fovEffectScale.tooltip": "See les or moar wihth teh SPEEDEH ZOOM ZOOM", "options.framerate": "%s mps (meows/s)", "options.framerateLimit": "max mps", "options.framerateLimit.max": "Omglimited", "options.fullscreen": "Whole screne", "options.fullscreen.current": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Fullscreen resolushun", "options.fullscreen.unavailable": "<PERSON><PERSON>", "options.gamma": "shinies settin", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "2 brite 4 me", "options.gamma.min": "Grumpy Cat Approves", "options.generic_value": "%s: %s", "options.glintSpeed": "shien sped", "options.glintSpeed.tooltip": "Controls how quikc shien is on da magic itamz.", "options.glintStrength": "shien powah", "options.glintStrength.tooltip": "cultruls hao stronk da <PERSON><PERSON><PERSON> is on encated itumz!", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Fabuliss!", "options.graphics.fabulous.tooltip": "%s gwaphis usez zadars far doodling wethar, cottun candie and paticlez behind da fuzzy blobs aund slush-slush!\n<PERSON><PERSON>al thewices aund 4Kat diwplayz may bee sevely imawted inm perwomans.", "options.graphics.fancy": "<PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "Fansee grafiks baylanses performans aand qwlity four majoiti oof taxis.\n<PERSON><PERSON><PERSON>, cotton candi, aund bobs may nout apeeaar when hiding behwind thrans<PERSON>want bwacks aund slosh-slosh.", "options.graphics.fast": "WEEEE!!!!", "options.graphics.fast.tooltip": "Spweed gwafics weduces da amoount oof seeable chum-chum and bum-bum.\nTwanspawancy ewfacts aren stanky forw loth of bwokz lwike lllleeeeaavveesssss.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON><PERSON> let me bacc", "options.graphics.warning.message": "Graphikz thingy no supurt fer dis %s gwaphics popshon\n\nkat can igner dis but supurt no happen fer ur thingy if kat choze %s graphikz wowption.", "options.graphics.warning.renderer": "Doodler thetecthed: [%s]", "options.graphics.warning.title": "Graphikz thingy go bboom", "options.graphics.warning.vendor": "Tha goodz stuf detec: [%s]", "options.graphics.warning.version": "u haz oopneGL", "options.guiScale": "gooey scalez", "options.guiScale.auto": "liek magicz", "options.hidden": "Hiddn", "options.hideLightningFlashes": "Hied litening flashz (too scary)", "options.hideLightningFlashes.tooltip": "Stopz laightnang flasherr from hurting ya eyez. Da bolts will still be vizible.", "options.hideMatchedNames": "Hide macht naems", "options.hideMatchedNames.tooltip": "udur katz can givez u presunts in werd wrappur.\nWif dis enabl: hidun katz vil be faund wif presunt sendur naems.", "options.hideSplashTexts": "<PERSON><PERSON> Spwash Texz", "options.hideSplashTexts.tooltip": "Mak all yelo spash texzt invisibl in teh main menu.", "options.inactivityFpsLimit": "Redews FPS wen", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limitz framraet to 30 wen kitteh is not doin anythin to da gaem 4 mor than 1min. Kittehz Fps will furda down to 10 afta 9 moar minz.", "options.inactivityFpsLimit.minimized": "minimizid", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON><PERSON> framrete only wen teh gaem winduhw is minimzeed.", "options.invertMouse": "esuoM trevnI", "options.japaneseGlyphVariants": "Jah<PERSON><PERSON>z gliff vershunz", "options.japaneseGlyphVariants.tooltip": "<PERSON>z Jahpuneez vershunz of CJK carecturz in da nomral funt", "options.key.hold": "Prezz", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Wat u re speek...", "options.language.title": "Wat u re speek", "options.languageAccuracyWarning": "(Langij tehranzlashunz mai no bi 100%% akkurit)", "options.languageWarning": "Tranzlashuns cud not be 100%% akkurit", "options.mainHand": "<PERSON>n hand", "options.mainHand.left": "Left Paw", "options.mainHand.right": "Rite Paw", "options.mipmapLevels": "Mipmap levelz", "options.modelPart.cape": "Flappy-thing", "options.modelPart.hat": "Thing-On-Head", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "Left Sleev", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "Right Sleev", "options.mouseWheelSensitivity": "Scrawl Senzitivity", "options.mouse_settings": "<PERSON><PERSON>...", "options.mouse_settings.title": "<PERSON><PERSON>", "options.multiplayer.title": "Friendzplayng Setingz...", "options.multiplier": "%s X", "options.music_frequency": "How offn kat muzik plae", "options.music_frequency.constant": "Al ze tiem", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Offn", "options.music_frequency.tooltip": "changz how oftn kat muzik plaez whiel in game wurld.", "options.narrator": "Sp00knator", "options.narrator.all": "<PERSON><PERSON><PERSON>", "options.narrator.chat": "Me<PERSON>z Le Cha<PERSON>", "options.narrator.notavailable": "<PERSON><PERSON><PERSON> cant has", "options.narrator.off": "naw", "options.narrator.system": "Meowz sistem", "options.notifications.display_time": "How Lomg Do Msg Shwoe", "options.notifications.display_time.tooltip": "huw long u can c de beep.", "options.off": "naw", "options.off.composed": "%s: NAW", "options.on": "yiss", "options.on.composed": "%s: YISS", "options.online": "<PERSON><PERSON> kittnz...", "options.online.title": "<PERSON><PERSON>", "options.onlyShowSecureChat": "Protektd chats only", "options.onlyShowSecureChat.tooltip": "dis opshun wil onwy showe chitcats frum odrr kats dat cna bb berifyed dat dis chittycat hafe been sended frum dat exacct kat n frum dis kat only, n is nawt bean modded buai any meemz!!!", "options.operatorItemsTab": "<PERSON><PERSON>", "options.particles": "LITUL BITS", "options.particles.all": "Oll", "options.particles.decreased": "Smallerz", "options.particles.minimal": "Smallersist", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chukn buildr", "options.prioritizeChunkUpdates.byPlayer": "blocking but not like completely", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Sum actionz inzid a chunk wiwl remakd da chunk imediatly!! Dis incwudz blok placin & destwoyin.", "options.prioritizeChunkUpdates.nearby": "One hundrd parcent bloccing", "options.prioritizeChunkUpdates.nearby.tooltip": "Chuckns near bye is always compild IMEDIATLYy!!! Dis may mess wit gaem performace when blocks broken, destroyed, etc etc etc", "options.prioritizeChunkUpdates.none": "threddit", "options.prioritizeChunkUpdates.none.tooltip": "Neawby chunkz awre maekd in matchn thredz. Dis mai wesolt in bref cheezey vizion wen u brek blocz.", "options.rawMouseInput": "Uncooked input", "options.realmsNotifications": "Realms Nwz & Invitez", "options.realmsNotifications.tooltip": "Fechez Realms newz & invitz in te tital skren & displeiz their owon ikon on te Realms bu'on.", "options.reducedDebugInfo": "Less debugz infoz", "options.renderClouds": "<PERSON><PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "clod diztance", "options.renderDistance": "rendur far away thingy", "options.resourcepack": "Resource Packz...", "options.rotateWithMinecart": "Rotete wif Ma<PERSON>z", "options.rotateWithMinecart.tooltip": "Weather if te player'z view shuld rotete wif a turnin Minekat. Only ok in te wurlds wif teh Minekat Impruvmntz egzperimntal setting is on.", "options.screenEffectScale": "Cat-mint effcts", "options.screenEffectScale.tooltip": "Da strengt of nousea and Nether portal scrin cat mint effects.\nAt da lowr values, the nousea effct is replaced wit a grin ovrlay.", "options.sensitivity": "Senzitivity", "options.sensitivity.max": "WEEEEEEEE!!!", "options.sensitivity.min": "*zzz*", "options.showNowPlayingToast": "Sho muzik sandwich", "options.showNowPlayingToast.tooltip": "Showz a sandwich wen kat muzik begin 2 plae. Ze saem bread iz alwaes displaed in da in-gaem paws menu whil kat song iz plaeing.", "options.showSubtitles": "<PERSON><PERSON> saund <PERSON>z", "options.simulationDistance": "Seamulation Distanz", "options.skinCustomisation": "How ur skin look liek...", "options.skinCustomisation.title": "How ur skin look liek", "options.sounds": "Musik & Soundz...", "options.sounds.title": "Musik & Sund Opshuns", "options.telemetry": "telekenesis deetahz...", "options.telemetry.button": "daytah collektings", "options.telemetry.button.tooltip": "\"%s\" incl.s onwy da needed deetah.\n\"%s\" incl.s opshunul stufz AND da needed daytah.", "options.telemetry.disabled": "Telekineses iz of.", "options.telemetry.state.all": "EVERYTHIN", "options.telemetry.state.minimal": "itty bitty plis", "options.telemetry.state.none": "No", "options.title": "Opshuns", "options.touchscreen": "Paw Pressing Power", "options.video": "<PERSON><PERSON><PERSON><PERSON>...", "options.videoTitle": "<PERSON><PERSON><PERSON><PERSON>", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "<PERSON>", "options.vsync": "VSink", "outOfMemory.message": "WARNING WARNING ALARM Minecraft haz ate oll da fudz.\n\ndis prolly hav occurd bcz ov a bug in a gaem orr da JVM (aka ur gaem) thimg ate evrithimg but stel hungery n is sad :(\n\nTo stup ur wurld frum beeing sick, we hafe saev&qwit ur wurld! rite nao we hafe tried gibbing da gaem sum moar kat fudz, but we dk if itll wok (prolly no de gaem is still meowing way 2 lowd)\n\niff u stel see dis scween 1nce orr a LOTS ov tiemz, jus restaht da gaem!!! mayb put it in rice- oh wait", "outOfMemory.title": "Memary noo ehough!", "pack.available.title": "Avaylabl", "pack.copyFailure": "Faild 2 copeh packz", "pack.dropConfirm": "U wantz 2 add followin packz 2 Min<PERSON><PERSON>?", "pack.dropInfo": "Dragz an dropz fylez in2 dis windw 2 add packz", "pack.dropRejected.message": "Teh folluwing entries r nut valid pacz n r nut copied:\n %s", "pack.dropRejected.title": "Nun-pacc entries", "pack.folderInfo": "(pla<PERSON> <PERSON><PERSON> heer)", "pack.incompatible": "LOLcatz cant uze dis to mak memes!", "pack.incompatible.confirm.new": "Diz eyz wur maded 4 a new generashun ov kittehz an ur literbox mae lookz wierd.", "pack.incompatible.confirm.old": "Diz eyes wur maded 4 eld r vershuns ov Min<PERSON>aft an mae nut maek epik meemz.", "pack.incompatible.confirm.title": "R u sur u wantz to lud dis new pair uf eyes?", "pack.incompatible.new": "(Myade for a newerborn verz<PERSON> of Minceraftr)", "pack.incompatible.old": "(<PERSON><PERSON> fur an elder verz<PERSON> of Minceraftr)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Opn pac foldr", "pack.selected.title": "Dis 1", "pack.source.builtin": "bulded-in", "pack.source.feature": "feture", "pack.source.local": "lolcal", "pack.source.server": "survr", "pack.source.world": "wurld", "painting.dimensions": "%sbai%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The Void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "rng", "parsing.bool.expected": "Expectd boolean", "parsing.bool.invalid": "Invalid boolean, expectd true or false but findz %s", "parsing.double.expected": "expectd double", "parsing.double.invalid": "Invalid double %s", "parsing.expected": "Expectd %s", "parsing.float.expected": "Expectd float", "parsing.float.invalid": "Invalid float %s", "parsing.int.expected": "Expectd integr", "parsing.int.invalid": "Invalid integr %s", "parsing.long.expected": "Eggspected lounge", "parsing.long.invalid": "Invalid lounge '%s'", "parsing.quote.escape": "Invalid escape sequence \\%s in quotd strin", "parsing.quote.expected.end": "Unclosd quotd strin", "parsing.quote.expected.start": "Expectd quote 2 start strin", "particle.invalidOptions": "<PERSON><PERSON><PERSON> kant parse partikuwl opshunz: %s", "particle.notFound": "Unknewwn particle: %s", "permissions.requires.entity": "An entwity is reqwayured tew run dis cumand heyre", "permissions.requires.player": "A playur iz reqwiyured tew run dis cumand heyer", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "When Applid:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Idk whet predikate is dat: %s", "quickplay.error.invalid_identifier": "Can't find teh wurld wit teh identifyr", "quickplay.error.realm_connect": "Culd nawt connect 2 Realm", "quickplay.error.realm_permission": "Yu don't havv perimishun 2 play wit kittehz in dis Realm :(", "quickplay.error.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "realms.configuration.region.australia_east": "New Sauz Walis, Avstralia", "realms.configuration.region.australia_southeast": "Victoria, Avstralia", "realms.configuration.region.brazil_south": "Braz1l", "realms.configuration.region.central_india": "Indie", "realms.configuration.region.central_us": "l0wa, USA", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "<PERSON><PERSON>, USA", "realms.configuration.region.france_central": "<PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON>", "realms.configuration.region.japan_west": "<PERSON><PERSON>", "realms.configuration.region.korea_central": "Soz <PERSON>", "realms.configuration.region.north_central_us": "Illionisisisi, USA", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texaz, USA", "realms.configuration.region.southeast_asia": "<PERSON><PERSON>-purr", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "Unid Arab Erimates (UAE)", "realms.configuration.region.uk_south": "Sozen Englond", "realms.configuration.region.west_central_us": "Uda, USA", "realms.configuration.region.west_europe": "Ni<PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "Clawifornia, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Awtomatiq (Realm ouner pig)", "realms.configuration.region_preference.automatic_player": "Awtomatiq (1st 2 jo1n sesionn)", "realms.missing.snapshot.error.text": "realms is cuwantly not supprotd in snapshots", "recipe.notFound": "Unknew recipi: %s", "recipe.toast.description": "Chek ur resip beek", "recipe.toast.title": "Nu Resipees Unlokt!", "record.nowPlaying": "Naw plaing: %s", "recover_world.bug_tracker": "Reprt a Bug", "recover_world.button": "Atempt to Recovr", "recover_world.done.failed": "Kant recovr frum last stat.", "recover_world.done.success": "Recovry DID IT!!!", "recover_world.done.title": "Recovry ok", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON> kant find file", "recover_world.issue.none": "No issuw", "recover_world.message": "The folowin' issuwz happnd while traing to read wurld foldr \"%s\".\nIt might b posibel to restor the wurld frum a oldr stat or u kan report dis issuw on teh bug trackr.", "recover_world.no_fallback": "No stat to recovr frum OKz", "recover_world.restore": "Atempt to Restor", "recover_world.restoring": "Atemptin to restor wurld...", "recover_world.state_entry": "Stat frum %s: ", "recover_world.state_entry.unknown": "kitteh dk", "recover_world.title": "No kan load wurld", "recover_world.warning": "No kan load wurld summarie", "resourcePack.broken_assets": "BROKD ASSETZ DETECTED", "resourcePack.high_contrast.name": "High Contwazt", "resourcePack.load_fail": "Resourecpac failz @ relod", "resourcePack.programmer_art.name": "<PERSON>", "resourcePack.runtime_failure": "Re<PERSON>rz pak is no! Errur!", "resourcePack.server.name": "<PERSON><PERSON>h Specefik Resourcez", "resourcePack.title": "<PERSON><PERSON>", "resourcePack.vanilla.description": "The originl see and toch of CatCraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Duwnluzinz vizualz", "resourcepack.progress": "Downloadin filez (%s MB)...", "resourcepack.requesting": "Makin' requestz...", "screenshot.failure": "coodent sayv scrienshot: %s", "screenshot.success": "savd skreehnshot az %s", "selectServer.add": "Ad Servur", "selectServer.defaultName": "Minecraft Servur", "selectServer.delete": "DELET DIS", "selectServer.deleteButton": "DELET DIS", "selectServer.deleteQuestion": "R u shure u want 2 remuv dis servur?", "selectServer.deleteWarning": "'%s' wil b lozt for lotz of eternityz! (longir den kitteh napz)", "selectServer.direct": "GOIN TO SERVER......", "selectServer.edit": "modifi", "selectServer.hiddenAddress": "(NO LOOKY)", "selectServer.refresh": "Refursh", "selectServer.select": "Join <PERSON>", "selectWorld.access_failure": "Nu kan acsez levl", "selectWorld.allowCommands": "K DO HAKS", "selectWorld.allowCommands.info": "Cmds liek /geimoud or /ex pi", "selectWorld.allowCommands.new": "Say ok 2 hax", "selectWorld.backupEraseCache": "<PERSON><PERSON> cachd deta", "selectWorld.backupJoinConfirmButton": "saev stuffz n' lode", "selectWorld.backupJoinSkipButton": "Cat go.", "selectWorld.backupQuestion.customized": "Weird woldz are no lonjer suported", "selectWorld.backupQuestion.downgrade": "<PERSON>rld downgradin iz not suported!", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON> usin Experimental setinz is nu acceptablz", "selectWorld.backupQuestion.snapshot": "Are u sure u want to lud dis litterbox?", "selectWorld.backupWarning.customized": "Sowy, i dont doo custumizd world In dis tiep of minecraf. I can play world and everyting will be ok but new land will not be fancy and cusstumb no more. Plz forgive!", "selectWorld.backupWarning.downgrade": "Dis wurld waz last playd in like version %s idk an ur usig %s, downgradin wurldz may corrupt or somethin and we cant guarantea if it will load n' work so maek backup plz if you still want two continue!", "selectWorld.backupWarning.experimental": "Dis wurld uziz da expurrimentul settinz dat mabeh stahp workin sumtiem. It mite no wurk! Da spooky lizord liv here!", "selectWorld.backupWarning.snapshot": "Dis wurld wuz last playd in vershun %s; u r on vershun %s. <PERSON><PERSON><PERSON> plz mak bakup in caes u experienec wurld curuptshuns!", "selectWorld.bonusItems": "Bonuz Kat Box", "selectWorld.cheats": "HAX", "selectWorld.commands": "<PERSON>x", "selectWorld.conversion": "IS OUTDATTED!", "selectWorld.conversion.tooltip": "Dis wrld must open first in da super ultra old version (lyke 1.6.4) to be converted safe", "selectWorld.create": "Make a new catz land", "selectWorld.customizeType": "UR OWN WURLD SETNGS", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON> stwuff", "selectWorld.data_read": "Reeding wurld stuffz...", "selectWorld.delete": "<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "DELET FOREVER", "selectWorld.deleteQuestion": "U SHUR U WANT TO DELET?!", "selectWorld.deleteWarning": "'%s' wial beh lozt forevr (longir than kittehz napz)", "selectWorld.delete_failure": "Nu can removz levl", "selectWorld.edit": "<PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "Meow a bakup", "selectWorld.edit.backupCreated": "Baekt'up: %s", "selectWorld.edit.backupFailed": "<PERSON>pz faild", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON> bakup foldr", "selectWorld.edit.backupSize": "SizE: %s MB", "selectWorld.edit.export_worldgen_settings": "<PERSON><PERSON><PERSON>t wurld genaraishun settinz", "selectWorld.edit.export_worldgen_settings.failure": "Expurrt iz no", "selectWorld.edit.export_worldgen_settings.success": "Expurrted", "selectWorld.edit.openFolder": "Open ze litter foldah", "selectWorld.edit.optimize": "Optimaiz kitteh land", "selectWorld.edit.resetIcon": "Reset ur iconz", "selectWorld.edit.save": "<PERSON><PERSON>", "selectWorld.edit.title": "Edidet Litterbox", "selectWorld.enterName": "<PERSON><PERSON><PERSON>", "selectWorld.enterSeed": "RANDOM NUMBER 4 WURLD GENRASUR", "selectWorld.experimental": "EKZPIREMENTALZ", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "REQQWIRED EXPREMINTL Thingz: %s", "selectWorld.experimental.details.title": "Ekzpirementalz Thing Reqqwirementz", "selectWorld.experimental.message": "Be Careful!\nDis configurashun requirez featurez dat r still undr development. Ur wurld mite crash, break, or not werk wif fuchur updatez.", "selectWorld.experimental.title": "EKZPIREMENTALZ Thingz WARNIN", "selectWorld.experiments": "TeStY ThINgY", "selectWorld.experiments.info": "Testy thingz r potenshul NEW thingz. B carful as thingz might BREK. Exprimentz cant be turnd off aftr wurld makin.", "selectWorld.futureworld.error.text": "A kat died wile tryng 2 load 1 wurld fromm 1 footshure vershn. Dis waz 1 riskee operashn 2 begn wiz; srry your kat iz ded.", "selectWorld.futureworld.error.title": "Tingz no workz!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Catventure", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blockz cant be addd or removd.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON>, but bloxx caen't", "selectWorld.gameMode.adventure.line2": "b addud or remoovd", "selectWorld.gameMode.creative": "Craetiv", "selectWorld.gameMode.creative.info": "OP kitteh moed activaetd. Maek nd eksplore. NO LIMITS. NONE. Kat can fly, kat can hav IFNITITE blockz, kat no hurt.", "selectWorld.gameMode.creative.line1": "Unlimitd resourcez, free flyin an", "selectWorld.gameMode.creative.line2": "make da blockz go bye byez", "selectWorld.gameMode.hardcore": "YOLO", "selectWorld.gameMode.hardcore.info": "Survival Mode lockd 2 hard difficulty. U cant respawn if u dye.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> <PERSON>, lokkt at hardzt", "selectWorld.gameMode.hardcore.line2": "difficulty, and u haz ony one lives (u ded 8 times alredu)", "selectWorld.gameMode.spectator": "Spectutor", "selectWorld.gameMode.spectator.info": "U can look but doan touch.", "selectWorld.gameMode.spectator.line1": "Can lukie luk but no touch", "selectWorld.gameMode.survival": "SIRVIVL", "selectWorld.gameMode.survival.info": "Ekspllor da KITTEH KINGDUM where u can biuld, colect, maekz, and scratch bad kitens.", "selectWorld.gameMode.survival.line1": "Luk 4 resoorcz, kraeft, gaen", "selectWorld.gameMode.survival.line2": "lvls, helth an hungrr", "selectWorld.gameRules": "Lvl roolz", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON> imp<PERSON><PERSON><PERSON>z", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON> opshun filez (.jason)", "selectWorld.incompatible.description": "Dis wurld kant be opend in dis vershun.\nIt waz last playd in vershun %s.", "selectWorld.incompatible.info": "Incompatible vershun: %s", "selectWorld.incompatible.title": "Incompatible vershun", "selectWorld.incompatible.tooltip": "Thiz wurld kant be opend bc it waz made by a inkompatibul vershun.", "selectWorld.incompatible_series": "Maed in bad vershun", "selectWorld.load_folder_access": "Oh noes! Da folder where da game worldz r savd can't b read or accessed!", "selectWorld.loading_list": "Luding <PERSON><PERSON>", "selectWorld.locked": "Lokd by anothr runnin instanz ov Minekraft", "selectWorld.mapFeatures": "<PERSON><PERSON> structrs", "selectWorld.mapFeatures.info": "stranjj peepl houses , brokn shipz n stuff", "selectWorld.mapType": "<PERSON><PERSON><PERSON>", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "<PERSON>ar <PERSON>ns...", "selectWorld.newWorld": "New Wurld", "selectWorld.recreate": "Copy-paste", "selectWorld.recreate.customized.text": "Customized wurlds r no longr supportd in dis vershun ov Minecraft. We can twee 2 recreaet it wif teh saem seed an propertiez, but any terraen kustemizaeshuns will b lost. wuz swee 4 teh inconvenienec!", "selectWorld.recreate.customized.title": "You catn cuztomez worldz naw >:c , pls go bak", "selectWorld.recreate.error.text": "Uh oh!!! <PERSON><PERSON> went rong when remakin world. Not my fallt tho.", "selectWorld.recreate.error.title": "O NO SUMTING WENT WONG!!!", "selectWorld.resource_load": "Preparin <PERSON>...", "selectWorld.resultFolder": "Wil b saevd in:", "selectWorld.search": "surtch 4 werlds", "selectWorld.seedInfo": "DUNT ENTER FUR 100%% RANDUM", "selectWorld.select": "<PERSON><PERSON>", "selectWorld.targetFolder": "Saev foldr: %s", "selectWorld.title": "Select wurld", "selectWorld.tooltip.fromNewerVersion1": "Litterbox wuz svaed in eh nwer verzhun,", "selectWorld.tooltip.fromNewerVersion2": "entreing dis litterbox culd caz prebblls!", "selectWorld.tooltip.snapshot1": "Renimdr 2 not frget 2 bku<PERSON> da wordl", "selectWorld.tooltip.snapshot2": "bifor u lowd it in dis snapshat.", "selectWorld.unable_to_load": "Oh NOeS! Worldz DiD NOt LOaD!", "selectWorld.version": "Vers<PERSON>:", "selectWorld.versionJoinButton": "lod aniwi", "selectWorld.versionQuestion": "R<PERSON> want to lud dis litterbox?", "selectWorld.versionUnknown": "kat dosnt knoe", "selectWorld.versionWarning": "Dis litterbox wuz last pleyed in verzhun '%s' and ludding it in dis verzhun culd make ur litterbox smell funny!", "selectWorld.warning.deprecated.question": "Sum tingz are nu longr wurk in da fushur, u surr u wana do dis?", "selectWorld.warning.deprecated.title": "WARNIN! Dis settinz iz usin veri anshien tingz", "selectWorld.warning.experimental.question": "Dis settinz iz expurrimentul, mabeh no wurk! U sur u wana do dis?", "selectWorld.warning.experimental.title": "WARNIN! Dis settinz iz usin expurrimentul tingz", "selectWorld.warning.lowDiskSpace.description": "Oopsies not much space left on ur devaiz.\nRunnin' out of disk spac when in gaem can hurt ur wurld.", "selectWorld.warning.lowDiskSpace.title": "Alert !! Low disk spac!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "<PERSON><PERSON><PERSON> sein mesag", "sleep.not_possible": "<PERSON><PERSON> sleeps can makez dark go away", "sleep.players_sleeping": "%s/%s kats sleepin", "sleep.skipping_night": "Sleepink thru dis nite", "slot.only_single_allowed": "Onwwy 1!!! swot awwoed, but robo-kitteh gotd '%s'", "slot.unknown": "Cat doezn't know da eslot '%s'", "snbt.parser.empty_key": "I don want empti KEY", "snbt.parser.expected_binary_numeral": "Ekzpektd a binary no.", "snbt.parser.expected_decimal_numeral": "Ekpektd a dezimal number", "snbt.parser.expected_float_type": "Oh hai, i needz floaty numbarz", "snbt.parser.expected_hex_escape": "Thougt character literal gunna be %s long", "snbt.parser.expected_hex_numeral": "Omgz needz hexadezimul numbarz", "snbt.parser.expected_integer_type": "Ekzspektd an integer num", "snbt.parser.expected_non_negative_number": "Oh noes needz happy numbarz no sad numbarz", "snbt.parser.expected_number_or_boolean": "Expectd numbr or boolean", "snbt.parser.expected_string_uuid": "Was lookin' fur a txt dat be a gud UUID, but dis ain’t it", "snbt.parser.expected_unquoted_string": "Ekzpectd a valud unkuotd string", "snbt.parser.infinity_not_allowed": "<PERSON><PERSON><PERSON><PERSON> dat go on furevur iz not alloawd", "snbt.parser.invalid_array_element_type": "Invald uray elemnt tip", "snbt.parser.invalid_character_name": "Invald Unicode charakter naemz", "snbt.parser.invalid_codepoint": "Invald Unicode charakter vawwues %s", "snbt.parser.invalid_string_contents": "<PERSON><PERSON><PERSON> strin kontetz", "snbt.parser.invalid_unquoted_start": "Oh noes stringz no start wif numbarz 0-9 or plus/minus y u do diz", "snbt.parser.leading_zero_not_allowed": "Noess dezimal numbarz dont startz wiz 0", "snbt.parser.no_such_operation": "Nu such operwation: %s", "snbt.parser.number_parse_failure": "Omgs cant reed numbarz: %s wat u do", "snbt.parser.undescore_not_allowed": "Noes noes no underscorez at start or end of numbarz", "soundCategory.ambient": "Saundz 4 nawt-haus kittehz", "soundCategory.block": "Blukz", "soundCategory.hostile": "Bad kittehz", "soundCategory.master": "MOAR SOUND", "soundCategory.music": "Musique", "soundCategory.neutral": "<PERSON><PERSON>", "soundCategory.player": "Cat<PERSON>", "soundCategory.record": "Music Stashun/Beetbox", "soundCategory.ui": "UI", "soundCategory.voice": "Voize/Spech", "soundCategory.weather": "<PERSON><PERSON>", "spectatorMenu.close": "<PERSON><PERSON><PERSON> der menu", "spectatorMenu.next_page": "<PERSON> After Dis One", "spectatorMenu.previous_page": "De OThER WAy", "spectatorMenu.root.prompt": "Push any key 2 chooze a command and do it agen 2 use it.", "spectatorMenu.team_teleport": "<PERSON>vz fast 2 guy on ya teem", "spectatorMenu.team_teleport.prompt": "Team 2 fast move 2", "spectatorMenu.teleport": "Move to da player", "spectatorMenu.teleport.prompt": "Choose da player yew want 2 move 2", "stat.generalButton": "Generul", "stat.itemsButton": "Itemz", "stat.minecraft.animals_bred": "Animuulz made bbyz", "stat.minecraft.aviate_one_cm": "Haw mach u fly", "stat.minecraft.bell_ring": "belly wrung", "stat.minecraft.boat_one_cm": "Linkth by water car", "stat.minecraft.clean_armor": "<PERSON><PERSON> washd", "stat.minecraft.clean_banner": "Bahnorz made kleen", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON>d", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_absorbed": "Dmg Sponged", "stat.minecraft.damage_blocked_by_shield": "wat de shild sav u 4 pain", "stat.minecraft.damage_dealt": "Fists Punched", "stat.minecraft.damage_dealt_absorbed": "Dmg Delt (Sponged)", "stat.minecraft.damage_dealt_resisted": "Dmg Delt (Shielded)", "stat.minecraft.damage_resisted": "Dmg Shielded", "stat.minecraft.damage_taken": "<PERSON><PERSON>", "stat.minecraft.deaths": "Numbr ov deaths", "stat.minecraft.drop": "stuffs dropped", "stat.minecraft.eat_cake_slice": "Piezes ov lie eatn", "stat.minecraft.enchant_item": "Stuff madeh shineh", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Rly big pots filld", "stat.minecraft.fish_caught": "Kat fud caught", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON><PERSON> birdid", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.horse_one_cm": "Distance by <PERSON>h", "stat.minecraft.inspect_dispenser": "Dizpnzur intacshunz", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "Intacshunz wif Anvehl", "stat.minecraft.interact_with_beacon": "<PERSON>", "stat.minecraft.interact_with_blast_furnace": "Interacshuns wif BlAsT FuRnAcE", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Interacshuns wif Campfireh", "stat.minecraft.interact_with_cartography_table": "Interacshuns wif cartogrophy table", "stat.minecraft.interact_with_crafting_table": "Krafting <PERSON><PERSON>z", "stat.minecraft.interact_with_furnace": "Hot Stone Blok intacshunz", "stat.minecraft.interact_with_grindstone": "Intacshunz wif Removezstone", "stat.minecraft.interact_with_lectern": "Interacshuns wif Lectrn", "stat.minecraft.interact_with_loom": "Interacshuns wif Lööm", "stat.minecraft.interact_with_smithing_table": "interwachszions wif smif tabel", "stat.minecraft.interact_with_smoker": "Interacshuns wif Za Smoker", "stat.minecraft.interact_with_stonecutter": "Interacshuns wif shrpy movin thingy", "stat.minecraft.jump": "Jumpz", "stat.minecraft.leave_game": "RA<PERSON> quitted", "stat.minecraft.minecart_one_cm": "Distince bi Minecat", "stat.minecraft.mob_kills": "mowz killz", "stat.minecraft.open_barrel": "Fish box opnd", "stat.minecraft.open_chest": "Cat Boxez opnd", "stat.minecraft.open_enderchest": "<PERSON><PERSON> opnd", "stat.minecraft.open_shulker_box": "SHuLKeR BOxz opnd", "stat.minecraft.pig_one_cm": "Distance by <PERSON><PERSON><PERSON>", "stat.minecraft.play_noteblock": "<PERSON><PERSON> blohkz plaid", "stat.minecraft.play_record": "zic dissc playd!!", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON> playd", "stat.minecraft.player_kills": "<PERSON><PERSON>", "stat.minecraft.pot_flower": "Plantz potd", "stat.minecraft.raid_trigger": "Raidz <PERSON>gerd", "stat.minecraft.raid_win": "Raidz Won", "stat.minecraft.sleep_in_bed": "<PERSON>er <PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sneak_time": "snek tiem", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Distance by laava walkr", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON><PERSON> in anty-kitteh <PERSON>d", "stat.minecraft.talked_to_villager": "Talkd tu Ho<PERSON>z", "stat.minecraft.target_hit": "Taw<PERSON> Hitt", "stat.minecraft.time_since_death": "time sins last rip", "stat.minecraft.time_since_rest": "Sinz last time kitteh slep", "stat.minecraft.total_world_time": "<PERSON><PERSON><PERSON> wif <PERSON><PERSON><PERSON>", "stat.minecraft.traded_with_villager": "Tradid wif hoomuns", "stat.minecraft.trigger_trapped_chest": "Rigged <PERSON>ez triggrd", "stat.minecraft.tune_noteblock": "<PERSON><PERSON> blohkz made sund niz", "stat.minecraft.use_cauldron": "Watr takn vrum rly big pots", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON> Welked on <PERSON><PERSON>", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON>lked uder <PERSON>", "stat.mobsButton": "Mahbs", "stat_type.minecraft.broken": "<PERSON><PERSON> br<PERSON>n", "stat_type.minecraft.crafted": "<PERSON><PERSON><PERSON>d", "stat_type.minecraft.dropped": "Droppd", "stat_type.minecraft.killed": "U KILLD %s %s", "stat_type.minecraft.killed.none": "U HAS NEVR KILLD %s", "stat_type.minecraft.killed_by": "%s killd u %s tiem(s)", "stat_type.minecraft.killed_by.none": "U has never been killd by %s", "stat_type.minecraft.mined": "Tiems Mind", "stat_type.minecraft.picked_up": "Pikd up", "stat_type.minecraft.used": "Tiems usd", "stats.none": "-", "structure_block.button.detect_size": "FINED", "structure_block.button.load": "LOED", "structure_block.button.save": "SAYV", "structure_block.custom_data": "Castum Deta Teg Neim", "structure_block.detect_size": "Detekt Thing's <PERSON><PERSON> <PERSON>:", "structure_block.hover.corner": "Croner: %s", "structure_block.hover.data": "Deta: %s", "structure_block.hover.load": "Lood: %s", "structure_block.hover.save": "Seiv: %s", "structure_block.include_entities": "Haves <PERSON><PERSON>turs:", "structure_block.integrity": "Stractur brokenes and random nambur", "structure_block.integrity.integrity": "stractur brokenes", "structure_block.integrity.seed": "stractur random nambur", "structure_block.invalid_structure_name": "invlid scturur nam '%s'", "structure_block.load_not_found": "Sretcur %s nott xist ", "structure_block.load_prepare": "Structure %s posishun redi", "structure_block.load_success": "Loudded stercur frum %s", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "Deta", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "SAEV", "structure_block.mode_info.corner": "Cernur modz - markz lokatiun and bignes", "structure_block.mode_info.data": "Data moed - gaem logik markr", "structure_block.mode_info.load": "<PERSON>ud modz - Laod frum file", "structure_block.mode_info.save": "Saiv mod - wrait to file", "structure_block.position": "Wot pozishun tu put?", "structure_block.position.x": "relativ pozishun x", "structure_block.position.y": "relativ pozishun y", "structure_block.position.z": "relativ pozishun z", "structure_block.save_failure": "Cudnt saiv strecur %s", "structure_block.save_success": "Strectyr saivd as %s", "structure_block.show_air": "<PERSON>w bleks w/ invis:", "structure_block.show_boundingbox": "Shaw Cat Box:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.size.x": "strecur seiz x", "structure_block.size.y": "strecur seiz y", "structure_block.size.z": "strecur seiz z", "structure_block.size_failure": "Cudnt fined seiz, meik cernurs dat kan meow 2 eichotha", "structure_block.size_success": "<PERSON><PERSON> finded 4 %s", "structure_block.strict": "STRICT plecmnt:", "structure_block.structure_name": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ambient.cave": "Spooky sound", "subtitles.ambient.sound": "Spoopy nos", "subtitles.block.amethyst_block.chime": "Shiny purpel blok makez sound", "subtitles.block.amethyst_block.resonate": "Purpl shinee *wengweng*", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON> go bye", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> landed", "subtitles.block.anvil.use": "Anvehl usd", "subtitles.block.barrel.close": "Fish box closez", "subtitles.block.barrel.open": "Fish box opnz", "subtitles.block.beacon.activate": "Bacon startz purrrng", "subtitles.block.beacon.ambient": "Bacon purrzzz", "subtitles.block.beacon.deactivate": "Bacon purzzzz no more", "subtitles.block.beacon.power_select": "Bacon soopa powurzzz", "subtitles.block.beehive.drip": "hony plops from bloc", "subtitles.block.beehive.enter": "B aboard hive", "subtitles.block.beehive.exit": "BEEZ GONE", "subtitles.block.beehive.shear": "Skizzors crunch surfase", "subtitles.block.beehive.work": "B mop yer deck", "subtitles.block.bell.resonate": "Belly resonates", "subtitles.block.bell.use": "Belly rings", "subtitles.block.big_dripleaf.tilt_down": "fall thru plantt tildz doun", "subtitles.block.big_dripleaf.tilt_up": "fall thru plantt tildz up", "subtitles.block.blastfurnace.fire_crackle": "Veri hot box cracklz", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON>z", "subtitles.block.bubble_column.bubble_pop": "Bublz pop", "subtitles.block.bubble_column.upwards_ambient": "Bublz flw", "subtitles.block.bubble_column.upwards_inside": "POP POP Shwizoom", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON><PERSON> wirly", "subtitles.block.bubble_column.whirlpool_inside": "Bublz ZOom", "subtitles.block.button.click": "Bttn clicz", "subtitles.block.cake.add_candle": "SqUiSh", "subtitles.block.campfire.crackle": "Campfireh cracklez", "subtitles.block.candle.crackle": "Land pikl cracklez", "subtitles.block.candle.extinguish": "Land pikl iz hot no moer", "subtitles.block.chest.close": "Cat Box closez", "subtitles.block.chest.locked": "no acces noob", "subtitles.block.chest.open": "Cat Box opnz", "subtitles.block.chorus_flower.death": "Purply-<PERSON><PERSON><PERSON> withrz", "subtitles.block.chorus_flower.grow": "Purply-W<PERSON><PERSON> growz", "subtitles.block.comparator.click": "Redstuff thingy clicz", "subtitles.block.composter.empty": "taekd out compostr stuffs", "subtitles.block.composter.fill": "stuffd compostr", "subtitles.block.composter.ready": "compostr maeking new stuffs", "subtitles.block.conduit.activate": "Strange box startz purrrrring", "subtitles.block.conduit.ambient": "Strange box ba-boomzzz", "subtitles.block.conduit.attack.target": "Strange box scratchs", "subtitles.block.conduit.deactivate": "Strange box stopz purrng", "subtitles.block.copper_bulb.turn_off": "Cop<PERSON> <PERSON>z", "subtitles.block.copper_bulb.turn_on": "Copurr Bob dings", "subtitles.block.copper_trapdoor.close": "Trepnoor closz", "subtitles.block.copper_trapdoor.open": "Trepnoor opnz", "subtitles.block.crafter.craft": "<PERSON><PERSON> Mashin kraft", "subtitles.block.crafter.fail": "<PERSON><PERSON> Mashin kant kraft :(", "subtitles.block.creaking_heart.hurt": "Glowing lawg waaaahhs", "subtitles.block.creaking_heart.idle": "Spoopy nos", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON> lawg beeg glos", "subtitles.block.deadbush.idle": "ded bush soundz", "subtitles.block.decorated_pot.insert": "Dekoratd pot fillz!", "subtitles.block.decorated_pot.insert_fail": "Dekratd pot wobbbllyes", "subtitles.block.decorated_pot.shatter": "Containr destroyd :(", "subtitles.block.dispenser.dispense": "Dizpnzd itum", "subtitles.block.dispenser.fail": "Dizpnzur faild", "subtitles.block.door.toggle": "<PERSON><PERSON> c<PERSON>z", "subtitles.block.dried_ghast.ambient": "Sundz ov drainezz", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rhydra<PERSON>", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON> soek", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> smiels :3", "subtitles.block.dry_grass.ambient": "Wind soundz", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON> usd", "subtitles.block.end_portal.spawn": "Finish portel opnz", "subtitles.block.end_portal_frame.fill": "<PERSON>h evil eye klingz", "subtitles.block.eyeblossom.close": "Eye flowah schleepz", "subtitles.block.eyeblossom.idle": "Eye flowah meowz", "subtitles.block.eyeblossom.open": "Eye flowah bloooooom", "subtitles.block.fence_gate.toggle": "Fenc Gaet creegz", "subtitles.block.fire.ambient": "Fier cracklz", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Toad poizon buz", "subtitles.block.frogspawn.hatch": "kute smol toad apears", "subtitles.block.furnace.fire_crackle": "Hot box cracklz", "subtitles.block.generic.break": "Blok brokn", "subtitles.block.generic.fall": "<PERSON><PERSON><PERSON> tripz on a blok", "subtitles.block.generic.footsteps": "Futstapz", "subtitles.block.generic.hit": "Blok brkin", "subtitles.block.generic.place": "Blok plazd", "subtitles.block.grindstone.use": "Removezstone usd", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON> cropp'd", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign nodz", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON> in da honi", "subtitles.block.iron_trapdoor.close": "Trappy <PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "Trappy <PERSON><PERSON>", "subtitles.block.lava.ambient": "hot sauce pubz", "subtitles.block.lava.extinguish": "Hot sauce makes snaek sound", "subtitles.block.lever.click": "Flipur<PERSON> clicz", "subtitles.block.note_block.note": "Nowht blohk plaiz", "subtitles.block.pale_hanging_moss.idle": "Spoopy nos", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON> m<PERSON>z", "subtitles.block.pointed_dripstone.drip_lava": "hot sauce fallz", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "hot sause fals intwo huge iron pot", "subtitles.block.pointed_dripstone.drip_water": "Watr fols", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Woter dreepz int irony pot", "subtitles.block.pointed_dripstone.land": "inverted sharp cave rok brakez", "subtitles.block.portal.ambient": "Portl annoyz", "subtitles.block.portal.travel": "Purrtl noiz iz smol", "subtitles.block.portal.trigger": "[portl noiz intensifiez]", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON> clicz", "subtitles.block.pumpkin.carve": "Skizzors grave", "subtitles.block.redstone_torch.burnout": "Burny stick fizzs", "subtitles.block.respawn_anchor.ambient": "Portl annoyz", "subtitles.block.respawn_anchor.charge": "respoon stuffzz lodded", "subtitles.block.respawn_anchor.deplete": "rezpaw ankoor out o juicz", "subtitles.block.respawn_anchor.set_spawn": "reezpaw fing set spown", "subtitles.block.sand.idle": "<PERSON> soundz", "subtitles.block.sand.wind": "Wind soundz", "subtitles.block.sculk.charge": "Sculk bublz", "subtitles.block.sculk.spread": "Sculk spredz", "subtitles.block.sculk_catalyst.bloom": "Sculk Cat-alist spredz", "subtitles.block.sculk_sensor.clicking": "Sculk detektor sart clikning", "subtitles.block.sculk_sensor.clicking_stop": "Sculk detektor stop clikning", "subtitles.block.sculk_shrieker.shriek": "Sculk yeller *AHHH*", "subtitles.block.shulker_box.close": "Shell bokz clusez", "subtitles.block.shulker_box.open": "Shulkr bokz opnz", "subtitles.block.sign.waxed_interact_fail": "Sign nodz", "subtitles.block.smithing_table.use": "Smissing Table usd", "subtitles.block.smoker.smoke": "Za Smoker smoks", "subtitles.block.sniffer_egg.crack": "Sniffr ec's gone :(", "subtitles.block.sniffer_egg.hatch": "Sniffr ec generetz <PERSON>ffr", "subtitles.block.sniffer_egg.plop": "Sniffr plupz", "subtitles.block.sponge.absorb": "Spunjbob drinkz", "subtitles.block.sweet_berry_bush.pick_berries": "Beriez pop", "subtitles.block.trapdoor.close": "Trepnoor closz", "subtitles.block.trapdoor.open": "Trepnoor opnz", "subtitles.block.trapdoor.toggle": "Secret wuud dor creegz", "subtitles.block.trial_spawner.about_to_spawn_item": "Skeri dungenz tingz almos there", "subtitles.block.trial_spawner.ambient": "Dungenz Maekr quacz", "subtitles.block.trial_spawner.ambient_charged": "S<PERSON><PERSON> quacz", "subtitles.block.trial_spawner.ambient_ominous": "S<PERSON><PERSON> quacz", "subtitles.block.trial_spawner.charge_activate": "Badnezz DEVOURS Dungenz maekr", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON><PERSON> Maekr says byebye", "subtitles.block.trial_spawner.detect_player": "Dungenz Maekr charjing up", "subtitles.block.trial_spawner.eject_item": "Dungenz Maekr gift :o", "subtitles.block.trial_spawner.ominous_activate": "Badnezz DEVOURS Dungenz maekr", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON> Maekr says hii~", "subtitles.block.trial_spawner.spawn_item": "skeri ting gibs 2 u", "subtitles.block.trial_spawner.spawn_item_begin": "skeri ting sayz haii~", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON> kitteh sponz", "subtitles.block.tripwire.attach": "trap criatud", "subtitles.block.tripwire.click": "String clicz", "subtitles.block.tripwire.detach": "trap destroid", "subtitles.block.vault.activate": "<PERSON><PERSON> box catch on faer", "subtitles.block.vault.ambient": "Moneh box cracklez", "subtitles.block.vault.close_shutter": "Moneh box closz", "subtitles.block.vault.deactivate": "Moneh box not hot", "subtitles.block.vault.eject_item": "Moneh box pukez", "subtitles.block.vault.insert_item": "Moneh box unlokz", "subtitles.block.vault.insert_item_fail": "Moneh box still locz up", "subtitles.block.vault.open_shutter": "Moneh box wide opun", "subtitles.block.vault.reject_rewarded_player": "Dun b g<PERSON>i u got ya moneh alwedii", "subtitles.block.water.ambient": "Watr flohz", "subtitles.block.wet_sponge.dries": "Spangblob leavz bikini bottum", "subtitles.chiseled_bookshelf.insert": "book ting plazd", "subtitles.chiseled_bookshelf.insert_enchanted": "Shineh book ting plazd", "subtitles.chiseled_bookshelf.take": "Book ting taekd awae", "subtitles.chiseled_bookshelf.take_enchanted": "shineh dodji nerdi ting taekd awae", "subtitles.enchant.thorns.hit": "Spikz spike", "subtitles.entity.allay.ambient_with_item": "blu boi with wingz faindz", "subtitles.entity.allay.ambient_without_item": "blu boi with wingz yearnz", "subtitles.entity.allay.death": "Flying blue mob dead :(", "subtitles.entity.allay.hurt": "blu boi with wingz owwy", "subtitles.entity.allay.item_given": "blu boi with wingz's \"AHHAHAHAHAHHAH\"", "subtitles.entity.allay.item_taken": "blu boi goes brr......", "subtitles.entity.allay.item_thrown": "blu boi with wingz thonkz u ar a bin", "subtitles.entity.armadillo.ambient": "Hard bawl aughs", "subtitles.entity.armadillo.brush": "Hard stuf falz ov", "subtitles.entity.armadillo.death": "Hard bawl ded", "subtitles.entity.armadillo.eat": "Hard Bawl eetz", "subtitles.entity.armadillo.hurt": "Hard bawl hurtz", "subtitles.entity.armadillo.hurt_reduced": "Hard bawl be hidin", "subtitles.entity.armadillo.land": "Hard Bawl lanz", "subtitles.entity.armadillo.peek": "Hard bawl lukin eyez", "subtitles.entity.armadillo.roll": "Hard bawl bawls", "subtitles.entity.armadillo.scute_drop": "Hard bawl scretchez", "subtitles.entity.armadillo.unroll_finish": "Hard bawl fwattenz", "subtitles.entity.armadillo.unroll_start": "Hard bawl lukin eyez", "subtitles.entity.armor_stand.fall": "Sum<PERSON>gn fell", "subtitles.entity.arrow.hit": "<PERSON><PERSON> hits", "subtitles.entity.arrow.hit_player": "Kat hit", "subtitles.entity.arrow.shoot": "<PERSON><PERSON> feird", "subtitles.entity.axolotl.attack": "KUTE PINK FISHH atakz", "subtitles.entity.axolotl.death": "KUTE PINK FISHH ded :(", "subtitles.entity.axolotl.hurt": "KUTE PINK FISHH hurtz", "subtitles.entity.axolotl.idle_air": "KUTE PINK FISH cheerpz", "subtitles.entity.axolotl.idle_water": "KUTE PINK FISH cheerpz", "subtitles.entity.axolotl.splash": "KUTE PINK FISHH splashz", "subtitles.entity.axolotl.swim": "KUTE PINK FISHH sweemz", "subtitles.entity.bat.ambient": "Flyig rat criez", "subtitles.entity.bat.death": "Batman ded", "subtitles.entity.bat.hurt": "<PERSON> hurz", "subtitles.entity.bat.takeoff": "Batman flize awey", "subtitles.entity.bee.ambient": "BEEZ MEIKS NOIZ!!", "subtitles.entity.bee.death": "B ded", "subtitles.entity.bee.hurt": "BEEZ OUCH", "subtitles.entity.bee.loop": "BEEZ BUSSIN", "subtitles.entity.bee.loop_aggressive": "BEEZ MAD!!", "subtitles.entity.bee.pollinate": "BEEZ HAPPYY!!!!", "subtitles.entity.bee.sting": "B attac", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON> breehtz", "subtitles.entity.blaze.burn": "<PERSON><PERSON> craklez", "subtitles.entity.blaze.death": "<PERSON><PERSON>z ded", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.blaze.shoot": "<PERSON><PERSON>z shooz", "subtitles.entity.boat.paddle_land": "Watr car usd in land", "subtitles.entity.boat.paddle_water": "Watr car splashez", "subtitles.entity.bogged.ambient": "Boogie be spoooooky", "subtitles.entity.bogged.death": "<PERSON><PERSON>ie ded", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.breeze.charge": "Brrrrrrrr charjz", "subtitles.entity.breeze.death": "Brrrrrrr guy ded", "subtitles.entity.breeze.deflect": "Brrrr uno reversus", "subtitles.entity.breeze.hurt": "Brrrrrrrr got 2 cold", "subtitles.entity.breeze.idle_air": "Brrrrrrrr activats hax", "subtitles.entity.breeze.idle_ground": "Wind guy goes brr", "subtitles.entity.breeze.inhale": "Brrrrrrrr inhelz", "subtitles.entity.breeze.jump": "Brrrrrrrr BOINGz", "subtitles.entity.breeze.land": "Brrrrrrrr landz", "subtitles.entity.breeze.shoot": "Brrrrrrrr shuutz", "subtitles.entity.breeze.slide": "Brrrrr be skatbordin'", "subtitles.entity.breeze.whirl": "Brrrrrrrr goes brr", "subtitles.entity.breeze.wind_burst": "Wind attac ekplodz", "subtitles.entity.camel.ambient": "<PERSON><PERSON> graaa", "subtitles.entity.camel.dash": "Banana Hors YEETs", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON>", "subtitles.entity.camel.death": "Banana Ho<PERSON> ded :(", "subtitles.entity.camel.eat": "Banana Hors noms", "subtitles.entity.camel.hurt": "<PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON> eqeeps", "subtitles.entity.camel.sit": "<PERSON><PERSON> hors sitz down", "subtitles.entity.camel.stand": "<PERSON><PERSON> standz up", "subtitles.entity.camel.step": "Banana <PERSON> Futstapz", "subtitles.entity.camel.step_sand": "Banana Ho<PERSON> litters", "subtitles.entity.cat.ambient": "<PERSON><PERSON><PERSON> speekz", "subtitles.entity.cat.beg_for_food": "KAT WANTZ", "subtitles.entity.cat.death": "noo kitteh ded :(", "subtitles.entity.cat.eat": "Kitty cat noms", "subtitles.entity.cat.hiss": "Kitty cat hizzs", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.cat.purr": "Gime morrrrr", "subtitles.entity.chicken.ambient": "Bawk Bawk!", "subtitles.entity.chicken.death": "Bawk Bawk ded", "subtitles.entity.chicken.egg": "Bawk Bawk plubz", "subtitles.entity.chicken.hurt": "Bawk Bawk hurz", "subtitles.entity.cod.death": "Cot ded", "subtitles.entity.cod.flop": "Cot flopz", "subtitles.entity.cod.hurt": "Cot hurtz", "subtitles.entity.cow.ambient": "Milk-Maeker mooz", "subtitles.entity.cow.death": "Milk-<PERSON><PERSON> ded", "subtitles.entity.cow.hurt": "Milk-<PERSON><PERSON> hurz", "subtitles.entity.cow.milk": "Milk-Maeker makez milk", "subtitles.entity.creaking.activate": "<PERSON> got a live", "subtitles.entity.creaking.ambient": "Tree treez", "subtitles.entity.creaking.attack": "Tree got itz branches", "subtitles.entity.creaking.deactivate": "Tree bekame real tree", "subtitles.entity.creaking.death": "Tree diez", "subtitles.entity.creaking.freeze": "Tree standz stil", "subtitles.entity.creaking.spawn": "Tree growz", "subtitles.entity.creaking.sway": "Tree iz OP", "subtitles.entity.creaking.twitch": "Creaking be tweaking", "subtitles.entity.creaking.unfreeze": "Tree got itz rootz out omg", "subtitles.entity.creeper.death": "C<PERSON>per ded", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.creeper.primed": "Creeper hizzs", "subtitles.entity.dolphin.ambient": "doolfin cherpz", "subtitles.entity.dolphin.ambient_water": "doolfin wislz", "subtitles.entity.dolphin.attack": "doolfin attaks", "subtitles.entity.dolphin.death": "doolfin ded", "subtitles.entity.dolphin.eat": "doolfin eatz", "subtitles.entity.dolphin.hurt": "do<PERSON><PERSON> hurtz", "subtitles.entity.dolphin.jump": "do<PERSON>fin jumpz", "subtitles.entity.dolphin.play": "do<PERSON><PERSON> plaez", "subtitles.entity.dolphin.splash": "doolfin splashz", "subtitles.entity.dolphin.swim": "do<PERSON>fin swimz", "subtitles.entity.donkey.ambient": "Dunky he-hawz", "subtitles.entity.donkey.angry": "Dunky neiz", "subtitles.entity.donkey.chest": "Dunky box soundz", "subtitles.entity.donkey.death": "Dunky ded", "subtitles.entity.donkey.eat": "Donkey goes nom nom", "subtitles.entity.donkey.hurt": "Dunky hurz", "subtitles.entity.donkey.jump": "Dunky jumpz", "subtitles.entity.drowned.ambient": "watuur thing groans underwater", "subtitles.entity.drowned.ambient_water": "watuur thing groans underwater", "subtitles.entity.drowned.death": "watuur thing ded", "subtitles.entity.drowned.hurt": "watuur thing huurtz", "subtitles.entity.drowned.shoot": "watuur thing launches fork", "subtitles.entity.drowned.step": "watuur thing walk", "subtitles.entity.drowned.swim": "watuur thing bobs up and down", "subtitles.entity.egg.throw": "<PERSON>g fliez", "subtitles.entity.elder_guardian.ambient": "big lazur shark monz", "subtitles.entity.elder_guardian.ambient_land": "big lazur shark flapz", "subtitles.entity.elder_guardian.curse": "big lazur shark cursz", "subtitles.entity.elder_guardian.death": "big lazur shark gets rekt", "subtitles.entity.elder_guardian.flop": "big lazur shark flopz", "subtitles.entity.elder_guardian.hurt": "big lazur shark hurz", "subtitles.entity.ender_dragon.ambient": "Dwagon roarz", "subtitles.entity.ender_dragon.death": "Dwagon ded", "subtitles.entity.ender_dragon.flap": "Dwagon flapz", "subtitles.entity.ender_dragon.growl": "Dwagon gwaulz", "subtitles.entity.ender_dragon.hurt": "Dwagon hurz", "subtitles.entity.ender_dragon.shoot": "Dwagon shooz", "subtitles.entity.ender_eye.death": "teh evil eye fallz", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> evil eye shooz", "subtitles.entity.ender_pearl.throw": "Magic ball fliez", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> scarz", "subtitles.entity.enderman.death": "<PERSON><PERSON> ded", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> hurz", "subtitles.entity.enderman.scream": "endi boi angy", "subtitles.entity.enderman.stare": "Enderman makez sum skary noisez", "subtitles.entity.enderman.teleport": "Enderman warpz", "subtitles.entity.endermite.ambient": "Endermite skutlz", "subtitles.entity.endermite.death": "Endermite ded", "subtitles.entity.endermite.hurt": "Endermite hurz", "subtitles.entity.evoker.ambient": "ghust makr guy speeks", "subtitles.entity.evoker.cast_spell": "ghust makr guy duz magic", "subtitles.entity.evoker.celebrate": "Wizrd cheerz", "subtitles.entity.evoker.death": "ghust makr guy gets rekt", "subtitles.entity.evoker.hurt": "ghust makr guy hurz", "subtitles.entity.evoker.prepare_attack": "Ghost makr guy preparz atak", "subtitles.entity.evoker.prepare_summon": "G<PERSON>t makr guy preparez makin ghosts", "subtitles.entity.evoker.prepare_wololo": "Ghust makr guy getz da magic redy", "subtitles.entity.evoker_fangs.attack": "Bear trapz bite", "subtitles.entity.experience_orb.pickup": "<PERSON>per<PERSON><PERSON> geind", "subtitles.entity.firework_rocket.blast": "Fierwurk blastz", "subtitles.entity.firework_rocket.launch": "Fierwurk launshz", "subtitles.entity.firework_rocket.twinkle": "Fierwurk twinklz", "subtitles.entity.fish.swim": "Splesh a lot", "subtitles.entity.fishing_bobber.retrieve": "Watuur toy cam bacc", "subtitles.entity.fishing_bobber.splash": "<PERSON>in boober splashez", "subtitles.entity.fishing_bobber.throw": "flye fliez", "subtitles.entity.fox.aggro": "Fuxe veri angri", "subtitles.entity.fox.ambient": "Fuxe skveks", "subtitles.entity.fox.bite": "Fuxe bites!", "subtitles.entity.fox.death": "Fuxe ded", "subtitles.entity.fox.eat": "Fuxe omnomnom's", "subtitles.entity.fox.hurt": "<PERSON><PERSON>", "subtitles.entity.fox.screech": "Fuxe criez LOUD", "subtitles.entity.fox.sleep": "Fuxe zzz", "subtitles.entity.fox.sniff": "Fuxe smel", "subtitles.entity.fox.spit": "Fuxe spits", "subtitles.entity.fox.teleport": "Fox becomz endrman", "subtitles.entity.frog.ambient": "Toad singin", "subtitles.entity.frog.death": "toad ded", "subtitles.entity.frog.eat": "toad uze its tengue 2 eet", "subtitles.entity.frog.hurt": "toad hurtz", "subtitles.entity.frog.lay_spawn": "toad maeks ec", "subtitles.entity.frog.long_jump": "toad jumpin", "subtitles.entity.generic.big_fall": "sumthign fell off", "subtitles.entity.generic.burn": "fier!", "subtitles.entity.generic.death": "RIP KAT", "subtitles.entity.generic.drink": "Sippin", "subtitles.entity.generic.eat": "NOM NOM NOM", "subtitles.entity.generic.explode": "BOOM", "subtitles.entity.generic.extinguish_fire": "u r no longer hot", "subtitles.entity.generic.hurt": "Sumthign hurz", "subtitles.entity.generic.small_fall": "smthinn tripz!!", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Wind charj burztz", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> criez", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> ded", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> shooz", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> koos", "subtitles.entity.ghastling.death": "Smol Ghast dyez :((((", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.ghastling.spawn": "S<PERSON>l Ghast sayz haiii!1!", "subtitles.entity.glow_item_frame.add_item": "Brite item holdr filz", "subtitles.entity.glow_item_frame.break": "Brite item holdr breakz", "subtitles.entity.glow_item_frame.place": "Brite item holdr plazd", "subtitles.entity.glow_item_frame.remove_item": "Brite item holdr getz empteed", "subtitles.entity.glow_item_frame.rotate_item": "Brite item holdr clikz", "subtitles.entity.glow_squid.ambient": "<PERSON>y skwid sweemz", "subtitles.entity.glow_squid.death": "F fur GLO skwid", "subtitles.entity.glow_squid.hurt": "Shiny skwid iz in pain", "subtitles.entity.glow_squid.squirt": "Glo skwid shootz writy liquid", "subtitles.entity.goat.ambient": "monten shep bleetz", "subtitles.entity.goat.death": "monten shep ded", "subtitles.entity.goat.eat": "monten shep eetz", "subtitles.entity.goat.horn_break": "monten shep's shap thing becom thingy", "subtitles.entity.goat.hurt": "monten shep hurz", "subtitles.entity.goat.long_jump": "monten shep jumpz", "subtitles.entity.goat.milk": "monten shep gotz milkd", "subtitles.entity.goat.prepare_ram": "monten shep STOMP", "subtitles.entity.goat.ram_impact": "monten shep RAMZ", "subtitles.entity.goat.screaming.ambient": "monten shep SCREM", "subtitles.entity.goat.step": "monten shep stepz", "subtitles.entity.guardian.ambient": "Shark-thingy monz", "subtitles.entity.guardian.ambient_land": "Shark-thingy flapz", "subtitles.entity.guardian.attack": "Shark-thingy shooz", "subtitles.entity.guardian.death": "Shark-thingy ded", "subtitles.entity.guardian.flop": "Shark-thingy flopz", "subtitles.entity.guardian.hurt": "Shark-thingy hurz", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON><PERSON>z", "subtitles.entity.happy_ghast.death": "Happeezz Ghast ded :((", "subtitles.entity.happy_ghast.equip": "Happy-dle eqipz", "subtitles.entity.happy_ghast.harness_goggles_down": "Happeezz Ghast iz rdy", "subtitles.entity.happy_ghast.harness_goggles_up": "Happeezz Ghast stps", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON><PERSON> hurz", "subtitles.entity.happy_ghast.unequip": "Happy-dle goes bye-bye", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> grwls", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> gr<PERSON>z wit disapprevel", "subtitles.entity.hoglin.attack": "Hoglin attaks", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> evolves to Z<PERSON><PERSON>", "subtitles.entity.hoglin.death": "F <PERSON>n", "subtitles.entity.hoglin.hurt": "Hoglin waz hurt", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> dosnt want figt", "subtitles.entity.hoglin.step": "Hoglin WALKZ", "subtitles.entity.horse.ambient": "<PERSON><PERSON> naiz", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> neighs", "subtitles.entity.horse.armor": "<PERSON><PERSON> gets protecshuun", "subtitles.entity.horse.breathe": "<PERSON><PERSON> <PERSON>z", "subtitles.entity.horse.death": "<PERSON><PERSON> ded", "subtitles.entity.horse.eat": "<PERSON><PERSON> e<PERSON>z", "subtitles.entity.horse.gallop": "<PERSON><PERSON> gal<PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON> hurz", "subtitles.entity.horse.jump": "<PERSON><PERSON> jumpz", "subtitles.entity.horse.saddle": "saddel eqeeps", "subtitles.entity.husk.ambient": "Warm hooman groonz", "subtitles.entity.husk.converted_to_zombie": "Mummy ting turns into zombze", "subtitles.entity.husk.death": "Warm hooman ded", "subtitles.entity.husk.hurt": "Warm hooman hurz", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.illusioner.cast_spell": "Wiizardur duz magic", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> becomes dieded", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON> ouches", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> maeks clonz", "subtitles.entity.illusioner.prepare_blindness": "Wiizardur will make u no see", "subtitles.entity.illusioner.prepare_mirror": "Illuzionr preparez miror imaig", "subtitles.entity.iron_golem.attack": "Big irun guy attakz", "subtitles.entity.iron_golem.damage": "Stel Guy brek", "subtitles.entity.iron_golem.death": "<PERSON> ded", "subtitles.entity.iron_golem.hurt": "Big irun guy hurz", "subtitles.entity.iron_golem.repair": "Stel Guy fixd", "subtitles.entity.item.break": "Itum breakz", "subtitles.entity.item.pickup": "Itum plubz", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> filz", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>z", "subtitles.entity.item_frame.place": "Fraem plazd", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON> clicz", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> not breakz", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> not teid", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON> stri<PERSON>z", "subtitles.entity.lightning_bolt.thunder": "O noez thundr", "subtitles.entity.llama.ambient": "Camel sheep bleetz", "subtitles.entity.llama.angry": "spitty boi blitz rajmod!!!", "subtitles.entity.llama.chest": "Camel sheep box soundz", "subtitles.entity.llama.death": "Camel sheep gets rekt", "subtitles.entity.llama.eat": "Camel sheep eetz", "subtitles.entity.llama.hurt": "Camel sheep hurz", "subtitles.entity.llama.spit": "Camel sheep spitz", "subtitles.entity.llama.step": "Camel sheep walkze", "subtitles.entity.llama.swag": "Camel sheep getz embellishd", "subtitles.entity.magma_cube.death": "Fier sliem ded", "subtitles.entity.magma_cube.hurt": "<PERSON>er sliem hurz", "subtitles.entity.magma_cube.squish": "eww magmy squishy", "subtitles.entity.minecart.inside": "Minecat meows agrresivly", "subtitles.entity.minecart.inside_underwater": "Minecat in wotah blub blub blub", "subtitles.entity.minecart.riding": "Minecat ROFLz", "subtitles.entity.mooshroom.convert": "Mooshroom transfomz", "subtitles.entity.mooshroom.eat": "Mooshroom eatz", "subtitles.entity.mooshroom.milk": "Mooshroom makez milk", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom makez milk werdly", "subtitles.entity.mule.ambient": "<PERSON>al he-hawz", "subtitles.entity.mule.angry": "Mewl maek mewl soundz", "subtitles.entity.mule.chest": "Mual box soundz", "subtitles.entity.mule.death": "<PERSON><PERSON> ded", "subtitles.entity.mule.eat": "Mewl go nom nom", "subtitles.entity.mule.hurt": "<PERSON><PERSON> hurz", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumpz", "subtitles.entity.painting.break": "Art on a paper brekz", "subtitles.entity.painting.place": "Art on a paper plazd", "subtitles.entity.panda.aggressive_ambient": "Green Stick Eatar huffs", "subtitles.entity.panda.ambient": "Green Stick Eatar panz", "subtitles.entity.panda.bite": "Green Stick Eatar bitez", "subtitles.entity.panda.cant_breed": "Green Stick Eatar bleatz", "subtitles.entity.panda.death": "Green Stick Eatar ded", "subtitles.entity.panda.eat": "Green Stick Eatar eatin", "subtitles.entity.panda.hurt": "Green Stick Eatar hurtz", "subtitles.entity.panda.pre_sneeze": "Green Stick Eatars nose is ticklin'", "subtitles.entity.panda.sneeze": "Green Stick Eatar goes ''ACHOO!''", "subtitles.entity.panda.step": "Green Stick Eatar stepz", "subtitles.entity.panda.worried_ambient": "Green Stick Eatar whimpars", "subtitles.entity.parrot.ambient": "<PERSON> speeks", "subtitles.entity.parrot.death": "Rainbow Bird ded", "subtitles.entity.parrot.eats": "<PERSON> Bird eeting", "subtitles.entity.parrot.fly": "renbo berd flapz", "subtitles.entity.parrot.hurts": "<PERSON> hurz", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> breethz", "subtitles.entity.parrot.imitate.bogged": "<PERSON> b spo0ky", "subtitles.entity.parrot.imitate.breeze": "<PERSON> goes brr", "subtitles.entity.parrot.imitate.creaking": "Rainbow burd treez", "subtitles.entity.parrot.imitate.creeper": "Rainbow <PERSON> hiss", "subtitles.entity.parrot.imitate.drowned": "purot gurglze", "subtitles.entity.parrot.imitate.elder_guardian": "Patriot moans", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON> roooring", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> skutlz", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ghast": "Rainbow Bird kraing", "subtitles.entity.parrot.imitate.guardian": "<PERSON> groonz", "subtitles.entity.parrot.imitate.hoglin": "Parrot anmgry soundz", "subtitles.entity.parrot.imitate.husk": "<PERSON> groonz", "subtitles.entity.parrot.imitate.illusioner": "<PERSON> moomoorz", "subtitles.entity.parrot.imitate.magma_cube": "Purrot squishy", "subtitles.entity.parrot.imitate.phantom": "<PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON>t noze", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON> hurz", "subtitles.entity.parrot.imitate.pillager": "<PERSON> moomoorz", "subtitles.entity.parrot.imitate.ravager": "purot doin' nut nut loudly", "subtitles.entity.parrot.imitate.shulker": "Rainbow Bird lurkz", "subtitles.entity.parrot.imitate.silverfish": "Rainbow <PERSON> hiss", "subtitles.entity.parrot.imitate.skeleton": "<PERSON> b spo0ky", "subtitles.entity.parrot.imitate.slime": "Rainbow Bird squishehs", "subtitles.entity.parrot.imitate.spider": "Rainbow <PERSON> hiss", "subtitles.entity.parrot.imitate.stray": "<PERSON> b spo0ky", "subtitles.entity.parrot.imitate.vex": "purrot nut hapy", "subtitles.entity.parrot.imitate.vindicator": "purot is charging atackzz", "subtitles.entity.parrot.imitate.warden": "Pwrrwt whwnws", "subtitles.entity.parrot.imitate.witch": "purot iz laufing", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> b <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON> b spo0ky", "subtitles.entity.parrot.imitate.zoglin": "<PERSON> groonz", "subtitles.entity.parrot.imitate.zombie": "<PERSON> groonz", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON> groonz", "subtitles.entity.phantom.ambient": "creepy flyin ting screechz", "subtitles.entity.phantom.bite": "creepy flyin ting bitez", "subtitles.entity.phantom.death": "creepy flyin ting ded", "subtitles.entity.phantom.flap": "creepy flyin ting flapz", "subtitles.entity.phantom.hurt": "creepy flyin ting hurtz", "subtitles.entity.phantom.swoop": "Fart0m swoops", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON> ded", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.pig.saddle": "saddel eqeeps", "subtitles.entity.piglin.admiring_item": "Piglin likz da STUFF", "subtitles.entity.piglin.ambient": "<PERSON>lin doez da werd pig thing", "subtitles.entity.piglin.angry": "<PERSON><PERSON> doez da wird pig noiz wit passion", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> is happeh", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> is gren", "subtitles.entity.piglin.death": "<PERSON><PERSON> is ded f in chat", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> waz hurt", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snoirz wit passion", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> dosnt want figt", "subtitles.entity.piglin.step": "Piglin WALKZ", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON>t doez da wild pig noiz wit passion", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> Brut is convarted to gren", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> Brut daiz", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> B<PERSON> hurets", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON>z", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON> mumblz", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON> cheerz", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> gets rekt", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.player.attack.crit": "A critical scratch!", "subtitles.entity.player.attack.knockback": "Nockback scratch", "subtitles.entity.player.attack.strong": "Powah scratch", "subtitles.entity.player.attack.sweep": "Sweepeh scratch", "subtitles.entity.player.attack.weak": "week scratch", "subtitles.entity.player.burp": "Boorp", "subtitles.entity.player.death": "<PERSON> ded", "subtitles.entity.player.freeze_hurt": "Kat iz veri kold now", "subtitles.entity.player.hurt": "<PERSON> hurz", "subtitles.entity.player.hurt_drown": "kitteh cant swim!", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON> fierz", "subtitles.entity.player.levelup": "<PERSON> dingz", "subtitles.entity.player.teleport": "Cat wrapz", "subtitles.entity.polar_bear.ambient": "Wite beer groons", "subtitles.entity.polar_bear.ambient_baby": "Wite Beer humz", "subtitles.entity.polar_bear.death": "Wite Beer ded", "subtitles.entity.polar_bear.hurt": "Wite beer hurz", "subtitles.entity.polar_bear.warning": "Wite beer roarz", "subtitles.entity.potion.splash": "<PERSON><PERSON> smeshs", "subtitles.entity.potion.throw": "Buttl thraun", "subtitles.entity.puffer_fish.blow_out": "pufferfish deflatez", "subtitles.entity.puffer_fish.blow_up": "pufferfish inflatez", "subtitles.entity.puffer_fish.death": "pufferfish diez", "subtitles.entity.puffer_fish.flop": "Pufferfish flops", "subtitles.entity.puffer_fish.hurt": "pufferfish hurts", "subtitles.entity.puffer_fish.sting": "pufferfish stings", "subtitles.entity.rabbit.ambient": "<PERSON>in Foodz skweekz", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON> ded", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> hurz", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> jumpz", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON> sayz grrrr", "subtitles.entity.ravager.attack": "Rivagur noms", "subtitles.entity.ravager.celebrate": "R<PERSON><PERSON>r cheerz", "subtitles.entity.ravager.death": "Rivagur be ded", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON> owwie", "subtitles.entity.ravager.roar": "Rivagur iz LOUD", "subtitles.entity.ravager.step": "Rivagur hooves", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON> slep", "subtitles.entity.salmon.death": "salmon diez", "subtitles.entity.salmon.flop": "salmon flops", "subtitles.entity.salmon.hurt": "salmon hurts", "subtitles.entity.sheep.ambient": "Baa Baa baahz", "subtitles.entity.sheep.death": "Baa Baa ded", "subtitles.entity.sheep.hurt": "Baa Baa hurz", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> lurkz", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> clus<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> diez", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> op<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> shooz", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> wrapz", "subtitles.entity.shulker_bullet.hit": "Shulker ball makez boom", "subtitles.entity.shulker_bullet.hurt": "Shulker shooty thingy brekz", "subtitles.entity.silverfish.ambient": "Grae fish hizzs", "subtitles.entity.silverfish.death": "Grae fish ded", "subtitles.entity.silverfish.hurt": "Grae fish hurz", "subtitles.entity.skeleton.ambient": "ooooo im spoooooooky", "subtitles.entity.skeleton.converted_to_stray": "Spooke scury <PERSON><PERSON><PERSON><PERSON> getz cold n' frozn", "subtitles.entity.skeleton.death": "Spooke scury <PERSON> ded", "subtitles.entity.skeleton.hurt": "Spooke scury <PERSON> hurz", "subtitles.entity.skeleton.shoot": "Spooke scury <PERSON> shooz", "subtitles.entity.skeleton_horse.ambient": "Skeletun hoers criez", "subtitles.entity.skeleton_horse.death": "S<PERSON><PERSON><PERSON> hoers ded", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON><PERSON> hoers hurz", "subtitles.entity.skeleton_horse.jump_water": "S<PERSON><PERSON><PERSON> hoers kan do parkor", "subtitles.entity.skeleton_horse.swim": "boni boi horz swizm in h0t sauc!", "subtitles.entity.slime.attack": "Sliem atakz", "subtitles.entity.slime.death": "Sliem ded", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.slime.squish": "ew slimy squishy", "subtitles.entity.sniffer.death": "Sniffr ded :(", "subtitles.entity.sniffer.digging": "Sniffr digz 4 sumthin", "subtitles.entity.sniffer.digging_stop": "<PERSON>ni<PERSON><PERSON> standz up", "subtitles.entity.sniffer.drop_seed": "Sniffr drops sed", "subtitles.entity.sniffer.eat": "Sniffr noms", "subtitles.entity.sniffer.egg_crack": "Sniffr ec's gone :(", "subtitles.entity.sniffer.egg_hatch": "Sniffr ec generetz <PERSON>ffr", "subtitles.entity.sniffer.happy": "Sniffr izz happi :D", "subtitles.entity.sniffer.hurt": "Sniffr *ouch*", "subtitles.entity.sniffer.idle": "Sniffr sayz grrrr", "subtitles.entity.sniffer.scenting": "Sniffr *snff*", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "Sniffr sniffs", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON> stepz", "subtitles.entity.snow_golem.death": "Cold Watr Hooman diez", "subtitles.entity.snow_golem.hurt": "Snowy Man hurtz", "subtitles.entity.snowball.throw": "Cold wet fliez", "subtitles.entity.spider.ambient": "Spidur hizzs", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON> ded", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.death": "Sqyd ded", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> hurz", "subtitles.entity.squid.squirt": "Sqyd shooz inc", "subtitles.entity.stray.ambient": "Frozen Skeletun spoooooooky", "subtitles.entity.stray.death": "Frozen Skeletun ded", "subtitles.entity.stray.hurt": "Frozen Skeletun hurz", "subtitles.entity.strider.death": "laava walkr ded", "subtitles.entity.strider.eat": "lava walkr nomz", "subtitles.entity.strider.happy": "laava walkr hapi", "subtitles.entity.strider.hurt": "laava walkr ouchi", "subtitles.entity.strider.idle": "lava walkr chrpz", "subtitles.entity.strider.retreat": "lava walkr fleez", "subtitles.entity.tadpole.death": "kute smol toad ded :(", "subtitles.entity.tadpole.flop": "kute smol toad jumpes", "subtitles.entity.tadpole.grow_up": "kute smol toad growz up?!?!", "subtitles.entity.tadpole.hurt": "kute smol toad hurtz", "subtitles.entity.tnt.primed": "Thatll maek BOOM", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON><PERSON> fysh ded", "subtitles.entity.tropical_fish.flop": "<PERSON><PERSON><PERSON><PERSON> fysh flopz", "subtitles.entity.tropical_fish.hurt": "Trpicl fysh hrtz", "subtitles.entity.turtle.ambient_land": "TortL chirpz", "subtitles.entity.turtle.death": "TortL ded", "subtitles.entity.turtle.death_baby": "Aw man Smol turtL ded :(", "subtitles.entity.turtle.egg_break": "TortL sphere BREAK :(", "subtitles.entity.turtle.egg_crack": "TortL ball crakz", "subtitles.entity.turtle.egg_hatch": "TortL sphere openz", "subtitles.entity.turtle.hurt": "TortL hurtz", "subtitles.entity.turtle.hurt_baby": "Smol tortL hurtz", "subtitles.entity.turtle.lay_egg": "TortL lies egg", "subtitles.entity.turtle.shamble": "TortL do a litter", "subtitles.entity.turtle.shamble_baby": "Smol tortL do a litter", "subtitles.entity.turtle.swim": "TortL does yoohoo in watR", "subtitles.entity.vex.ambient": "Ghosty thingy iz ghostin", "subtitles.entity.vex.charge": "Ghosty thingy iz mad at u", "subtitles.entity.vex.death": "ghosty thingy gets rekt", "subtitles.entity.vex.hurt": "ghosty thingy hurz", "subtitles.entity.villager.ambient": "Vilaagur mumblz", "subtitles.entity.villager.celebrate": "Vilaagur cheerz", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON> ded", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON><PERSON> hurz", "subtitles.entity.villager.no": "Vila<PERSON><PERSON> nays", "subtitles.entity.villager.trade": "Vilaagur tradez", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON> workz", "subtitles.entity.villager.work_butcher": "Butcher workz", "subtitles.entity.villager.work_cartographer": "Cartographer workz", "subtitles.entity.villager.work_cleric": "Cleric workz", "subtitles.entity.villager.work_farmer": "Farmer workz", "subtitles.entity.villager.work_fisherman": "Fisherman workz", "subtitles.entity.villager.work_fletcher": "<PERSON> workz", "subtitles.entity.villager.work_leatherworker": "Leatherworker workss", "subtitles.entity.villager.work_librarian": "Librarian workss", "subtitles.entity.villager.work_mason": "<PERSON><PERSON> workss", "subtitles.entity.villager.work_shepherd": "Sheephird workss", "subtitles.entity.villager.work_toolsmith": "Toolsmif workss", "subtitles.entity.villager.work_weaponsmith": "Weeponsmith workss", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON><PERSON> nods", "subtitles.entity.vindicator.ambient": "Bad Guy mumblz", "subtitles.entity.vindicator.celebrate": "Bad Guy cheerz", "subtitles.entity.vindicator.death": "Bad Guy gets rekt", "subtitles.entity.vindicator.hurt": "Bad Guy hurz", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON> mumb<PERSON>z", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> ded", "subtitles.entity.wandering_trader.disappeared": "wubterng truder disapers", "subtitles.entity.wandering_trader.drink_milk": "Wander tredr drinkin mylk", "subtitles.entity.wandering_trader.drink_potion": "wubterng troder sip magik", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON> hurz", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON><PERSON> nays", "subtitles.entity.wandering_trader.reappeared": "Strnga troder apers", "subtitles.entity.wandering_trader.trade": "Wu<PERSON>ern<PERSON> Truder <PERSON>z", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON> nods", "subtitles.entity.warden.agitated": "Blu shrek 'roooor'", "subtitles.entity.warden.ambient": "Blu shrek iz sad :(", "subtitles.entity.warden.angry": "Blu shrek whant too kilz u", "subtitles.entity.warden.attack_impact": "Blu shrek wont sumfin ded ;(", "subtitles.entity.warden.death": "Blu shrek iz ded dw :)", "subtitles.entity.warden.dig": "Blu shrek meakz a hol", "subtitles.entity.warden.emerge": "Blu shrek tryngz 2 be a tree", "subtitles.entity.warden.heartbeat": "Blu shrek demon-strat it iz alive", "subtitles.entity.warden.hurt": "<PERSON> shrek hurz", "subtitles.entity.warden.listening": "Blu shrek filz sumtin", "subtitles.entity.warden.listening_angry": "Blu shrek filz sumtin (and gos MAD!!!!!!111)", "subtitles.entity.warden.nearby_close": "Blu shrek iz near u <:(", "subtitles.entity.warden.nearby_closer": "Blu shrek coms klosur", "subtitles.entity.warden.nearby_closest": "Blu shrek iz mooving-", "subtitles.entity.warden.roar": "Blu shrek fink iz big lowd fur kat", "subtitles.entity.warden.sniff": "Blu shrek sniiiiiiiiiiiiiiiffz", "subtitles.entity.warden.sonic_boom": "<PERSON> shrek meakz his ANIME ATTECK", "subtitles.entity.warden.sonic_charge": "Blu shrek iz keepin energy", "subtitles.entity.warden.step": "Blu shrek tryngz 2 be Slender", "subtitles.entity.warden.tendril_clicks": "Blu shrek kat whiskurz clikkes", "subtitles.entity.wind_charge.throw": "Wind attac fliez", "subtitles.entity.wind_charge.wind_burst": "Wind attac ekplodz", "subtitles.entity.witch.ambient": "<PERSON> <PERSON><PERSON>r giglz", "subtitles.entity.witch.celebrate": "<PERSON> <PERSON><PERSON><PERSON> cheerz", "subtitles.entity.witch.death": "<PERSON> <PERSON><PERSON>r ded", "subtitles.entity.witch.drink": "<PERSON> <PERSON><PERSON>r drinkz", "subtitles.entity.witch.hurt": "<PERSON> <PERSON><PERSON> hurz", "subtitles.entity.witch.throw": "<PERSON> <PERSON><PERSON>r drowz", "subtitles.entity.wither.ambient": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.wither.death": "Wither ded", "subtitles.entity.wither.hurt": "<PERSON><PERSON> hurz", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON>er relizd", "subtitles.entity.wither_skeleton.ambient": "Spooke <PERSON><PERSON> is spooooooky", "subtitles.entity.wither_skeleton.death": "Spooke Wither S<PERSON>un ded", "subtitles.entity.wither_skeleton.hurt": "Spooke Wither Skeletun hurz", "subtitles.entity.wolf.ambient": "Woof Woof panz", "subtitles.entity.wolf.bark": "Woof Woof woofs", "subtitles.entity.wolf.death": "Woof Woof ded", "subtitles.entity.wolf.growl": "Woof Woof gwaulz", "subtitles.entity.wolf.hurt": "Woof Woof hurz", "subtitles.entity.wolf.pant": "Woof Woof panz", "subtitles.entity.wolf.shake": "Woof Woof shakez", "subtitles.entity.wolf.whine": "Woof Woof iz sad :(", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> grrr", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> grrrrrr", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> scar<PERSON>s", "subtitles.entity.zoglin.death": "Zoglin nov ded", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> ouchs", "subtitles.entity.zoglin.step": "Zoglin wlkz", "subtitles.entity.zombie.ambient": "<PERSON> Ho<PERSON> groonz", "subtitles.entity.zombie.attack_wooden_door": "Kitteh dor iz attac!", "subtitles.entity.zombie.break_wooden_door": "Kit<PERSON>h dor iz breks!", "subtitles.entity.zombie.converted_to_drowned": "Ded guy evolvez into de watuur tingy", "subtitles.entity.zombie.death": "Bad Hooman ded", "subtitles.entity.zombie.destroy_egg": "<PERSON><PERSON> ec stumpt", "subtitles.entity.zombie.hurt": "Bad Hooman hurz", "subtitles.entity.zombie.infect": "Slow baddie infectz", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> hoers criez", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> hoers ded", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> hoers hurz", "subtitles.entity.zombie_villager.ambient": "Unded <PERSON><PERSON><PERSON> groonz", "subtitles.entity.zombie_villager.converted": "Unded Villager Cryz", "subtitles.entity.zombie_villager.cure": "Undead villager <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.death": "Unded Villaguur gets rekt", "subtitles.entity.zombie_villager.hurt": "Unded Villaguur hurz", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>tz", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON><PERSON> grontz grrrr", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON>d <PERSON>z", "subtitles.entity.zombified_piglin.hurt": "Zumbefid <PERSON> hrtz", "subtitles.event.mob_effect.bad_omen": "Badnezz dominatz", "subtitles.event.mob_effect.raid_omen": "Raid gona happuhn soon", "subtitles.event.mob_effect.trial_omen": "<PERSON>keri dungen kitteh gos nerbai", "subtitles.event.raid.horn": "Ominous horn blares", "subtitles.item.armor.equip": "girrs eqip", "subtitles.item.armor.equip_chain": "jingly katsuit jinglez", "subtitles.item.armor.equip_diamond": "shiny katsuit pings", "subtitles.item.armor.equip_elytra": "FLYY tiny doin noiz", "subtitles.item.armor.equip_gold": "shiny katsuit chinks", "subtitles.item.armor.equip_iron": "cheap katsuit bangs", "subtitles.item.armor.equip_leather": "katsu<PERSON> ruz<PERSON>z", "subtitles.item.armor.equip_netherite": "<PERSON>herite armur meows", "subtitles.item.armor.equip_turtle": "TortL Shell funks", "subtitles.item.armor.equip_wolf": "Woof armur on", "subtitles.item.armor.unequip_wolf": "Woof armur falz of", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON>ez", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> streepz", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON> off", "subtitles.item.bone_meal.use": "Smashed <PERSON><PERSON><PERSON><PERSON> go prrrrr", "subtitles.item.book.page_turn": "<PERSON><PERSON> rustlez", "subtitles.item.book.put": "Book thump", "subtitles.item.bottle.empty": "no moar sippy", "subtitles.item.bottle.fill": "Buttl filz", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Broomin grey thing", "subtitles.item.brush.brushing.gravel.complete": "B<PERSON>in graval COMPLETED!!!", "subtitles.item.brush.brushing.sand": "Broomin litter box", "subtitles.item.brush.brushing.sand.complete": "Broomin litter COMPLETED!!!", "subtitles.item.bucket.empty": "Buket empteiz", "subtitles.item.bucket.fill": "Buket filz", "subtitles.item.bucket.fill_axolotl": "KUTE PINK FISHH pickd up", "subtitles.item.bucket.fill_fish": "fishi got", "subtitles.item.bucket.fill_tadpole": "<PERSON>t smol toad capturez", "subtitles.item.bundle.drop_contents": "Budnl emptied naww catrzzz", "subtitles.item.bundle.insert": "Itaem packd", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> powch ful", "subtitles.item.bundle.remove_one": "Itaem unpackd", "subtitles.item.chorus_fruit.teleport": "Cat wrapz", "subtitles.item.crop.plant": "Crop plantd", "subtitles.item.crossbow.charge": "C<PERSON><PERSON>z is getting redy", "subtitles.item.crossbow.hit": "<PERSON><PERSON> hits", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON> be loadin'", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON> is firin'", "subtitles.item.dye.use": "Colurer thing staanz", "subtitles.item.elytra.flying": "FLYYYYYY", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON> wushez", "subtitles.item.flintandsteel.use": "Frint und steal clic", "subtitles.item.glow_ink_sac.use": "Shiny ink sac does sploosh", "subtitles.item.goat_horn.play": "monten shep's shap thingy becom catbox", "subtitles.item.hoe.till": "<PERSON>e tilz", "subtitles.item.honey_bottle.drink": "kitteh stuffd", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON> on", "subtitles.item.horse_armor.unequip": "Hoofy Armar falz of", "subtitles.item.ink_sac.use": "<PERSON><PERSON> likwid does sploosh", "subtitles.item.lead.break": "Leesh braeks", "subtitles.item.lead.tied": "<PERSON><PERSON> tid", "subtitles.item.lead.untied": "<PERSON><PERSON> untaid", "subtitles.item.llama_carpet.unequip": "Rug falz of", "subtitles.item.lodestone_compass.lock": "loserstone spinny thing lockd onto loserstone", "subtitles.item.mace.smash_air": "*bionkz*", "subtitles.item.mace.smash_ground": "*bionkz*", "subtitles.item.nether_wart.plant": "plantd plant", "subtitles.item.ominous_bottle.dispose": "Botle brekz", "subtitles.item.saddle.unequip": "<PERSON>del falz of", "subtitles.item.shears.shear": "Skizzors clic", "subtitles.item.shears.snip": "Skizzors zniapp", "subtitles.item.shield.block": "sheild blocz", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> flatnz", "subtitles.item.spyglass.stop_using": "i cnt c u anymoar", "subtitles.item.spyglass.use": "Now i can c u", "subtitles.item.totem.use": "Totum iz revivin u", "subtitles.item.trident.hit": "Dinglehopper zticks", "subtitles.item.trident.hit_ground": "Dinglehopper goez brr brr", "subtitles.item.trident.return": "<PERSON>gle<PERSON><PERSON> come back to kitteh", "subtitles.item.trident.riptide": "Dinglehopper zmmz", "subtitles.item.trident.throw": "Dinglehopper clengz", "subtitles.item.trident.thunder": "Dinglehopper thundr crackz", "subtitles.item.wolf_armor.break": "Woof armur braekz", "subtitles.item.wolf_armor.crack": "Woof armur almost brekz", "subtitles.item.wolf_armor.damage": "Woof armur hurz", "subtitles.item.wolf_armor.repair": "Woof armur fixd", "subtitles.particle.soul_escape": "cya sol", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON><PERSON> papeh d<PERSON>hn", "subtitles.ui.hud.bubble_pop": "<PERSON><PERSON><PERSON> in wotah blob blob blob", "subtitles.ui.loom.take_result": "<PERSON><PERSON> iz looomin", "subtitles.ui.stonecutter.take_result": "<PERSON>un chopprs uzed", "subtitles.weather.rain": "<PERSON><PERSON>z", "symlink_warning.message": "Ludin wurldz frum stuff-containrz wit strnj linkz can b dangros if u dk wat iz going on. Pls go to %s to lean moar.", "symlink_warning.message.pack": "<PERSON><PERSON> paccz wit stranj linkz can b dangros if u dk wat iz going on. Pls go to %s to lean moar.", "symlink_warning.message.world": "Ludin wurldz frum stuff-containrz wit strnj linkz can b dangros if u dk wat iz going on. Pls go to %s to lean moar.", "symlink_warning.more_info": "Moar Informashunz", "symlink_warning.title": "<PERSON>rld stuffs haz stranj linkz", "symlink_warning.title.pack": "Added pac(z) contain(z) stranj linkz", "symlink_warning.title.world": "<PERSON>rld stuffs haz stranj linkz", "team.collision.always": "4evres", "team.collision.never": "no way", "team.collision.pushOtherTeams": "Push othr kities", "team.collision.pushOwnTeam": "Push own kities", "team.notFound": "Cat doezn't know da team '%s'", "team.visibility.always": "4 evr", "team.visibility.hideForOtherTeams": "Hid for othr kitty", "team.visibility.hideForOwnTeam": "Hid for herd", "team.visibility.never": "Nevr", "telemetry.event.advancement_made.description": "Undr<PERSON><PERSON> teh contxt behain recivin a afvancment kan halp uz maek teh gaem progezzion goodr!!", "telemetry.event.advancement_made.title": "Atfancemen<PERSON>", "telemetry.event.game_load_times.description": "Deez evnt kan halp uz eject teh impastaz in gaem to startup perfurmanc r need bai measurin doinz taimz ov teh statrtup fasez.", "telemetry.event.game_load_times.title": "<PERSON><PERSON><PERSON> Lo<PERSON>", "telemetry.event.optional": "%s (opshunal)", "telemetry.event.optional.disabled": "%s (Opshunal) - No", "telemetry.event.performance_metrics.description": "Knowing the overlol prformance pforile of Minceraft helpz us tune and optimizze the game for a widee range of machine specifications and meowperating sistemzz. Game version is included 2 help uz compare the performance profi le for new versionz of Minecraft", "telemetry.event.performance_metrics.title": "Perforrmanz metrics", "telemetry.event.required": "%s (we nede dis)", "telemetry.event.world_load_times.description": "Us kittehz wants 2 now haw long it taek to get in da woerld, and haw it get fazter an sloewr. Wen da kitteh gang add new stuffz, we wants 2 now cuz us kittehs need 2 help wiv da gaem. Thx :)", "telemetry.event.world_load_times.title": "wurld lolad timez", "telemetry.event.world_loaded.description": "Knoweng how catz play Minceraft (such as gaem mode, cliient or servr moded, and gaem version) allows us to focus gaem apdates to emprove the areas that catz care about mozt.\nThe world loadded ivent is pair widda world unloadded event to calculate how logn the play sezzion haz lasted.", "telemetry.event.world_loaded.title": "wurld loded", "telemetry.event.world_unloaded.description": "dis event tis paried widda wurld loaded event 2 calculate how long the wurld sezzion has lasted. The duration (in secands and tickz) is meazured when a wurld session has endedd (quitting 2 da title, dizzconecting from a servr)", "telemetry.event.world_unloaded.title": "world unloded", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON> (Tickz)", "telemetry.property.advancement_id.title": "Atfancemend ID", "telemetry.property.client_id.title": "clyunt AI DEE", "telemetry.property.client_modded.title": "moadded catcraft!!!", "telemetry.property.dedicated_memory_kb.title": "memory given (kBitez)", "telemetry.property.event_timestamp_utc.title": "evnt tiemstamp (universal kat tiem)", "telemetry.property.frame_rate_samples.title": "meowrate samplz (or jus meows/s idk)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON> moed", "telemetry.property.game_version.title": "gameh vershun", "telemetry.property.launcher_name.title": "<PERSON><PERSON><PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "Boot5trap Taim (ms)", "telemetry.property.load_time_loading_overlay_ms.title": "loding scren tiem (milisecats)", "telemetry.property.load_time_pre_window_ms.title": "tim elapsd b4 spahnin windowe (milisecats)", "telemetry.property.load_time_total_time_ms.title": "ALL Lod Taim (ms)", "telemetry.property.minecraft_session_id.title": "minceraft sesshun AIDEE", "telemetry.property.new_world.title": "neu wurld", "telemetry.property.number_of_samples.title": "sample texts", "telemetry.property.operating_system.title": "os", "telemetry.property.opt_in.title": "oppt-IN", "telemetry.property.platform.title": "wherr yoy plae dis catcraft", "telemetry.property.realms_map_content.title": "Realms Map Stuffz (Mgaem Naem)", "telemetry.property.render_distance.title": "rendur far-ness", "telemetry.property.render_time_samples.title": "rend<PERSON>rin tiem sampl txts", "telemetry.property.seconds_since_load.title": "taem since loda (sec)", "telemetry.property.server_modded.title": "moadded survurr!!!", "telemetry.property.server_type.title": "survurr tyep", "telemetry.property.ticks_since_load.title": "taem since load (tiks)", "telemetry.property.used_memory_samples.title": "used WAM", "telemetry.property.user_id.title": "kat AI-DEE", "telemetry.property.world_load_time_ms.title": "Worrld load taem (millisecats)", "telemetry.property.world_session_id.title": "wurld sesshun AIDEE", "telemetry_info.button.give_feedback": "gib uz feedbakz", "telemetry_info.button.privacy_statement": "Privasy Statemnt", "telemetry_info.button.show_data": "show me ma STUFF", "telemetry_info.opt_in.description": "Kitteh can send opshunal telemetree data", "telemetry_info.property_title": "included info", "telemetry_info.screen.description": "collectin dees deetah wil halp us 2 improov Minecraft buai leeding light us in waes dat r vrery luvd or liked 2 ourr kats.\nu cna also feed us moar feedbacs n fuds 2 halp us improoveing Minecraft!!!", "telemetry_info.screen.title": "telekenesis deetah collektingz", "test.error.block_property_mismatch": "Ekzpectd property %s to be %s, wuz %s", "test.error.block_property_missing": "Blok propurty missing, ekspectd propahty %s to be %s", "test.error.entity_property": "Entity %s faild tez: %s", "test.error.entity_property_details": "Entity %s faild tes: %s, ekzpectd: %s, wus: %s", "test.error.expected_block": "Ekzpeced blok %s, gawt %s", "test.error.expected_block_tag": "Ekzpeted blok in #%s, gawt %s", "test.error.expected_container_contents": "Da box shud hav: %s", "test.error.expected_container_contents_single": "Teh box shud hav one: %s", "test.error.expected_empty_container": "Da box shuld be hollow", "test.error.expected_entity": "Expectd %s", "test.error.expected_entity_around": "Ekzpectd %s to be arund %s, %s, %s", "test.error.expected_entity_count": "Ekzpctd %s entitis of taip %s, fund %s", "test.error.expected_entity_data": "Ekzpctd entity data to b: %s, wuz: %s", "test.error.expected_entity_data_predicate": "Entity dataz mismatcc 4: %s", "test.error.expected_entity_effect": "Ekzpctd %s to hab effekt %s %s", "test.error.expected_entity_having": "Entity inventry shuld habe %s", "test.error.expected_entity_holding": "Entity shuld be holdin' %s", "test.error.expected_entity_in_test": "Ekzpectd %s 2b in tez", "test.error.expected_entity_not_touching": "Dinot eggzpek %s touchin' %s, %s, %s (re-lait: %s, %s, %s)", "test.error.expected_entity_touching": "Ekzpekd %s touchin' %s, %s, %s (re-lait: %s, %s, %s)", "test.error.expected_item": "Ekzpektd item of type %s", "test.error.expected_items_count": "Ekzpctd %s itemz of taip %s, fund %s", "test.error.fail": "FEiL kondishunz met", "test.error.invalid_block_type": "Unekzpectd blok typ fund: %s", "test.error.missing_block_entity": "Blok entity has gone", "test.error.position": "%s @ %s, %s, %s (relait: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Kondishun alr triggerd @ %s", "test.error.sequence.condition_not_triggered": "<PERSON>ndi<PERSON>n nuh triggrd", "test.error.sequence.invalid_tick": "Sukseeded in a wrong tick: ekspectd %s >:(", "test.error.sequence.not_completed": "<PERSON>z timd owt b4 sekwenzz kumpletd", "test.error.set_biome": "<PERSON><PERSON><PERSON> kant set biome 4 tez", "test.error.spawn_failure": "Faild 2 mak entitti %s", "test.error.state_not_equal": "Inkorret state. Ekspectd %s, wuz %s", "test.error.structure.failure": "<PERSON><PERSON>h cant plec tez strukture 4 %s", "test.error.tick": "%s on tik %s", "test.error.ticking_without_structure": "Ticklin' tez b4 plecin' struk<PERSON> muhahaha", "test.error.timeout.no_result": "Didnt sukcedd / feil wifin %s tix", "test.error.timeout.no_sequences_finished": "No sekwens finishd wifin %s tix", "test.error.too_many_entities": "Ekspektd only 1 %s 2 be around %s, %s, %s but fund %s", "test.error.unexpected_block": "Blok shudn't be %s !!", "test.error.unexpected_entity": "Y iz %s here it shouldn't exist!!", "test.error.unexpected_item": "Nobody expectz teh item uv tyep %s !!", "test.error.unknown": "Anknoun intiernal eror: %s", "test.error.value_not_equal": "%s shuld'v been %s but it waz %s", "test.error.wrong_block_entity": "Blok entity tyep iz wrong: %s", "test_block.error.missing": "Testuh struktur misin %s blok", "test_block.error.too_many": "2 many %s blokz", "test_block.invalid_timeout": "You cant't do taht timout (%s) - it haz to be a positve numbr ov ticks", "test_block.message": "Masage:", "test_block.mode.accept": "Acept", "test_block.mode.fail": "Fale", "test_block.mode.log": "<PERSON><PERSON>", "test_block.mode.start": "Stahwt", "test_block.mode_info.accept": "<PERSON><PERSON> - Acept suceses for (part ov) a tess", "test_block.mode_info.fail": "<PERSON>all Moed - Fiall te tess", "test_block.mode_info.log": "<PERSON><PERSON> - Lawg a mesage", "test_block.mode_info.start": "<PERSON><PERSON><PERSON> Moed - Te stahtin pot for a tes", "test_instance.action.reset": "Do it again n load", "test_instance.action.run": "Lod and wun", "test_instance.action.save": "<PERSON><PERSON>", "test_instance.description.batch": "Batcc: %s", "test_instance.description.failed": "Oopsiz: %s", "test_instance.description.function": "Funkshun: %s", "test_instance.description.invalid_id": "Bad tez ID", "test_instance.description.no_test": "Ther iz no tez", "test_instance.description.structure": "Structr: %s", "test_instance.description.type": "Toipe: %s", "test_instance.type.block_based": "Blok-Basik Tez", "test_instance.type.function": "Bewt-in Funkshun Tez", "test_instance_block.entities": "Entitiz:", "test_instance_block.error.no_test": "Unabel to ran tes intasnce at %s, %s, %s bc it has an udnefind tes", "test_instance_block.error.no_test_structure": "Unabel to ran tes intasnce at %s, %s, %s bc it haz no tes strutcue", "test_instance_block.error.unable_to_save": "Unabel to sav tes structrue tempate for trs intance at %s, %s, %s", "test_instance_block.invalid": "[nor coretc]", "test_instance_block.reset_success": "Restet suceeeded for tes: %s", "test_instance_block.rotation": "Rotaetun:", "test_instance_block.size": "Tez Strecur Seiz", "test_instance_block.starting": "Startin' tez %s", "test_instance_block.test_id": "Tez instenz ID", "title.32bit.deprecation": "BEEP BOOP 32-bit sistem detekted: dis mai prvent ya form playin in da future cuz 64-bit sistem will be REQUIRED!!", "title.32bit.deprecation.realms": "Minecraft'll soon need da 64-bit sistem wich will prevnt ya from playin or usin Realms on dis devic. Yall need two manualy kancel any Realms subskripshin.", "title.32bit.deprecation.realms.check": "Do nt shw ths scren agan >:(", "title.32bit.deprecation.realms.header": "32-bit sistm detektd", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "U cnt pley wif oter kits, pweas chek ur Microsoft accunt setinz.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "Multiplayr (LAL)", "title.multiplayer.other": "NAT LONELEY (3rd-kitteh servurr)", "title.multiplayer.realms": "Multiplayr (Reelms) k?", "title.singleplayer": "<PERSON><PERSON><PERSON> kitteh", "translation.test.args": "%s %s lol", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %s", "translation.test.none": "HAI WORLD!", "translation.test.world": "world", "trim_material.minecraft.amethyst": "purpur shinee stuff", "trim_material.minecraft.copper": "copurr stuff", "trim_material.minecraft.diamond": "SHINEE DEEMOND stuff", "trim_material.minecraft.emerald": "green thingy stuff", "trim_material.minecraft.gold": "gulden stuff", "trim_material.minecraft.iron": "irun stuff", "trim_material.minecraft.lapis": "blu ston stuff", "trim_material.minecraft.netherite": "Netherite stuff", "trim_material.minecraft.quartz": "kworts stuff", "trim_material.minecraft.redstone": "Redstone stuff", "trim_material.minecraft.resin": "Stiky stuff", "trim_pattern.minecraft.bolt": "Bolt drip", "trim_pattern.minecraft.coast": "Watery drip ", "trim_pattern.minecraft.dune": "Litterbox drip", "trim_pattern.minecraft.eye": "BLINK BLINK drip", "trim_pattern.minecraft.flow": "<PERSON><PERSON> drip", "trim_pattern.minecraft.host": "Host drip", "trim_pattern.minecraft.raiser": "Farmr drip", "trim_pattern.minecraft.rib": "Boney drip", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON><PERSON> drip", "trim_pattern.minecraft.shaper": "Buildrman drip", "trim_pattern.minecraft.silence": "*shhhhhh* drip", "trim_pattern.minecraft.snout": "Nouze drip", "trim_pattern.minecraft.spire": "Towr drip", "trim_pattern.minecraft.tide": "Wavy drip", "trim_pattern.minecraft.vex": "Gosty drip", "trim_pattern.minecraft.ward": "Blue scary man drip", "trim_pattern.minecraft.wayfinder": "find-da-wae drip", "trim_pattern.minecraft.wild": "wildin drip", "tutorial.bundleInsert.description": "Rite clck t0 add itamz", "tutorial.bundleInsert.title": "Use cat powch", "tutorial.craft_planks.description": "The recipe buk thingy can halp", "tutorial.craft_planks.title": "Mak wuddn plankz", "tutorial.find_tree.description": "Beet it up 2 collecz w00d", "tutorial.find_tree.title": "Find 1 scratchr", "tutorial.look.description": "Us<PERSON> ur rat 2 see da w0rld", "tutorial.look.title": "See da wor1d", "tutorial.move.description": "Jump wit %s", "tutorial.move.title": "Expl0r wid %s, %s, %s, & %s", "tutorial.open_inventory.description": "Prez %s", "tutorial.open_inventory.title": "Opn ur stuffz containr", "tutorial.punch_tree.description": "Prez %s @ lengthz", "tutorial.punch_tree.title": "Demolish ur scratcher lul", "tutorial.socialInteractions.description": "Pres %s 2 opun", "tutorial.socialInteractions.title": "sociul interacshuns", "upgrade.minecraft.netherite_upgrade": "Netherite lvl UP!"}