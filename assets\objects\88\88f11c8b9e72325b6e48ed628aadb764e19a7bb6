{"accessibility.onboarding.accessibility.button": "Handy Settings...", "accessibility.onboarding.screen.narrator": "Thring streakbreak to lay on the mielder", "accessibility.onboarding.screen.title": "Welcome to Minecraft!\n\nWould you like to lay on the Mielder or go to the Handy Settings?", "addServer.add": "Done", "addServer.enterIp": "Outreckoner Link", "addServer.enterName": "Outreckoner Name", "addServer.resourcePack": "Outreckoner Lode Packs", "addServer.resourcePack.disabled": "Off", "addServer.resourcePack.enabled": "On", "addServer.resourcePack.prompt": "Ask", "addServer.title": "Bework Outreckoner Ab<PERSON>", "advMode.command": "Wieldboard Hest", "advMode.mode": "Wayset", "advMode.mode.auto": "Edledging", "advMode.mode.autoexec.bat": "Always Astirred", "advMode.mode.conditional": "Tharfly", "advMode.mode.redstone": "Onebeat", "advMode.mode.redstoneTriggered": "Needs <PERSON><PERSON>", "advMode.mode.sequence": "Rackent", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "Must be an overseer in makerly wayset", "advMode.notEnabled": "Hest clots are not on with this outreckoner", "advMode.previousOutput": "Former Output", "advMode.setCommand": "Set Wieldboard Hest for Clot", "advMode.setCommand.success": "Hest set: %s", "advMode.trackOutput": "Follow output", "advMode.triggering": "Triggering", "advMode.type": "Kind", "advancement.advancementNotFound": "Unknown forthstep: %s", "advancements.adventure.adventuring_time.description": "Find every lifeheap", "advancements.adventure.adventuring_time.title": "Wayspelling Time", "advancements.adventure.arbalistic.description": "Kill five sunder wights with one steel bow shot", "advancements.adventure.arbalistic.title": "Shootlore", "advancements.adventure.avoid_vibration.description": "Sneak near a Sculk Feeler or Holdend to forestall it from acknowing you", "advancements.adventure.avoid_vibration.title": "Sneak 100", "advancements.adventure.blowback.description": "Kill a Whith with a bounced Whith-shot Wind Blast", "advancements.adventure.blowback.title": "Blowback", "advancements.adventure.brush_armadillo.description": "Reap Girdledeer Shales from a Girdledeer using a Bristler", "advancements.adventure.brush_armadillo.title": "Unhirst the Girdledeer!", "advancements.adventure.bullseye.description": "Strike the bullseye of a Mark<PERSON> from at least 30 m away", "advancements.adventure.bullseye.title": "Bullseye", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Make a Bedecked Pot out of 4 Pot Sherds", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Careful Edstowing", "advancements.adventure.crafters_crafting_crafters.description": "Be near a Crafter when it crafts a Crafter", "advancements.adventure.crafters_crafting_crafters.title": "Crafters Crafting Crafters", "advancements.adventure.fall_from_world_height.description": "Free fall from the top of the world (build threshold) to the bottom of the world and live", "advancements.adventure.fall_from_world_height.title": "Hollows & Cliffs", "advancements.adventure.heart_transplanter.description": "Lay a Creaking Heart in the right way between two Bloak Oak Stock clots", "advancements.adventure.heart_transplanter.title": "Fulfledged <PERSON>", "advancements.adventure.hero_of_the_village.description": "Speedfully forstand a thorp from a reaving", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> of the Thorp", "advancements.adventure.honey_block_slide.description": "Leap into a Honey Clot to break your fall", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "Kill any foe fiend", "advancements.adventure.kill_a_mob.title": "Fiend <PERSON>", "advancements.adventure.kill_all_mobs.description": "Kill one of every foe fiend", "advancements.adventure.kill_all_mobs.title": "Fiends Hunted", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Kill a wight near a Sculk Sunderer", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "It Spreads", "advancements.adventure.lighten_up.description": "Shrape an Are Lightvat with an Axe to make it brighter", "advancements.adventure.lighten_up.title": "Lighten Up", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Shield a <PERSON><PERSON><PERSON> from an unwished strike without starting a fire", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Walm Shield", "advancements.adventure.minecraft_trials_edition.description": "Step foot in a Fand Room", "advancements.adventure.minecraft_trials_edition.title": "...Ready For It?", "advancements.adventure.ol_betsy.description": "Shoot a Steel Bow", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON>' <PERSON>", "advancements.adventure.overoverkill.description": "Deal 50 hearts of harm in only one strike with the Beetle", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Make the Meadows come alive with songs from a Gleebox", "advancements.adventure.play_jukebox_in_meadows.title": "Loud of Song", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Read the strength of a Graven Bookshelf using a Bemeter", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "The Might of Books", "advancements.adventure.revaulting.description": "Unlock a Threatening Lockvat with a Threatening Fand Key", "advancements.adventure.revaulting.title": "After the Halsend", "advancements.adventure.root.description": "Wayspelling, seeking and fighting", "advancements.adventure.root.title": "Wayspelling", "advancements.adventure.salvage_sherd.description": "<PERSON>ristle a Weird clot to reap a Pot Sherd", "advancements.adventure.salvage_sherd.title": "Worthying the Laves", "advancements.adventure.shoot_arrow.description": "Shoot something with a Arrow", "advancements.adventure.shoot_arrow.title": "Shoot Your Bow", "advancements.adventure.sleep_in_bed.description": "Sleep in a Bed to wend your edstarting ord", "advancements.adventure.sleep_in_bed.title": "Sweet Dreams", "advancements.adventure.sniper_duel.description": "Kill a Boneframe from at least 50 meters away", "advancements.adventure.sniper_duel.title": "Sniter Onewye", "advancements.adventure.spyglass_at_dragon.description": "Look at the <PERSON><PERSON> Worm through a Zooming Glass", "advancements.adventure.spyglass_at_dragon.title": "Is It a Flightcraft?", "advancements.adventure.spyglass_at_ghast.description": "Look at a Ghast through a Zooming Glass", "advancements.adventure.spyglass_at_ghast.title": "Is It a Windball?", "advancements.adventure.spyglass_at_parrot.description": "Look at a Bleefowl through a Zooming Glass", "advancements.adventure.spyglass_at_parrot.title": "Is It a Fowl?", "advancements.adventure.summon_iron_golem.description": "<PERSON>on an Iron Livenedman to help forstand a thorp", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Throw a Leister at something.\nHeed: Throwing away your only weapon is not a good thank.", "advancements.adventure.throw_trident.title": "A Throwaway Rib", "advancements.adventure.totem_of_undying.description": "<PERSON> a Token of Undying to swike death", "advancements.adventure.totem_of_undying.title": "Undeathly", "advancements.adventure.trade.description": "Speedfully wrixle with a <PERSON><PERSON><PERSON>", "advancements.adventure.trade.title": "What a Deal!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON><PERSON> with a <PERSON><PERSON><PERSON> at the build height threshold", "advancements.adventure.trade_at_world_height.title": "<PERSON> Chapman", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Beseech these smithing forelays at least once: St<PERSON>le, Swinenose, Rib, Ward, Stillness, Nettle, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smithing Your Way", "advancements.adventure.trim_with_any_armor_pattern.description": "Craft trimmed hirsting at a Smithing Bench", "advancements.adventure.trim_with_any_armor_pattern.title": "Crafting a New Look", "advancements.adventure.two_birds_one_arrow.description": "Kill two Nightgosts with a throughshot Arrow", "advancements.adventure.two_birds_one_arrow.title": "Two Fowls, One Arrow", "advancements.adventure.under_lock_and_key.description": "Unlock a <PERSON><PERSON> with a Fand Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Brook a Northfinder on a Lodestone", "advancements.adventure.use_lodestone.title": "Homeland Lode, Make Me Home", "advancements.adventure.very_very_frightening.description": "Strike a <PERSON><PERSON><PERSON> with lightning", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "Kill a reaving leader.\nMaybe think about keeping away from thorps for the time being...", "advancements.adventure.voluntary_exile.title": "Willing Outcast", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Walk on Dust Snow... without sinking in it", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Light as a Hare", "advancements.adventure.who_needs_rockets.description": "Brook a Wind Blast to fly yourself upward 8 clots", "advancements.adventure.who_needs_rockets.title": "Way to the Welkin!", "advancements.adventure.whos_the_pillager_now.description": "Yeave a Reaver what hie have sown", "advancements.adventure.whos_the_pillager_now.title": "Who's the Reaver Now?", "advancements.empty": "There doesn't seem to be anything here...", "advancements.end.dragon_breath.description": "<PERSON><PERSON> W<PERSON>'s Breath in a Glass Handvat", "advancements.end.dragon_breath.title": "Worst Breath", "advancements.end.dragon_egg.description": "Hold the Worm Ey", "advancements.end.dragon_egg.title": "The Next Offsprings", "advancements.end.elytra.description": "Find an Enderglider", "advancements.end.elytra.title": "Welk<PERSON>'s the Threshold", "advancements.end.enter_end_gateway.description": "Atwind the iland", "advancements.end.enter_end_gateway.title": "Farflung Holiday", "advancements.end.find_end_city.description": "Go on in, what could befall?", "advancements.end.find_end_city.title": "End Game", "advancements.end.kill_dragon.description": "Good luck", "advancements.end.kill_dragon.title": "Free the End", "advancements.end.levitate.description": "Hover up 50 m from the strikes of a Shulker", "advancements.end.levitate.title": "Great Sight From Up Here", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON> the <PERSON>er <PERSON>orm", "advancements.end.respawn_dragon.title": "The End... Once More...", "advancements.end.root.description": "Or the beginning?", "advancements.end.root.title": "The End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Have an Allay drop a <PERSON><PERSON> at a Ringer", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON> Song", "advancements.husbandry.allay_deliver_item_to_player.description": "Have an Allay bear things to you", "advancements.husbandry.allay_deliver_item_to_player.title": "You Have a Friend in Me", "advancements.husbandry.axolotl_in_a_bucket.description": "Hole a Waterhelper in a Stop", "advancements.husbandry.axolotl_in_a_bucket.title": "The Sweetest Hunter", "advancements.husbandry.balanced_diet.description": "Eat everything that is eatbere, even if it's not good for you", "advancements.husbandry.balanced_diet.title": "An <PERSON>", "advancements.husbandry.breed_all_animals.description": "Breed all the deer!", "advancements.husbandry.breed_all_animals.title": "Two by Two", "advancements.husbandry.breed_an_animal.description": "Breed two deer together", "advancements.husbandry.breed_an_animal.title": "Where Did I Come From", "advancements.husbandry.complete_catalogue.description": "Tame all Catlikes!", "advancements.husbandry.complete_catalogue.title": "Don't Stop Me Meow", "advancements.husbandry.feed_snifflet.description": "Feed a Sniffling", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": "Hole a fish brooking a Fishing Rod", "advancements.husbandry.fishy_business.title": "Fishy Business", "advancements.husbandry.froglights.description": "Have all Froshlights in your inholding", "advancements.husbandry.froglights.title": "With Our Mights Fayed!", "advancements.husbandry.kill_axolotl_target.description": "Team up with a Waterhel<PERSON> and win a fight", "advancements.husbandry.kill_axolotl_target.title": "The Healing Might of Friendship!", "advancements.husbandry.leash_all_frog_variants.description": "Have each <PERSON><PERSON><PERSON> on a Lead", "advancements.husbandry.leash_all_frog_variants.title": "Gathering of the Swordsmen", "advancements.husbandry.make_a_sign_glow.description": "Make the writ of any kind of token glow", "advancements.husbandry.make_a_sign_glow.title": "Glow and Behold!", "advancements.husbandry.netherite_hoe.description": "<PERSON> a Netherite Ingot to better a <PERSON><PERSON>, and then edthink over your life choosings", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON> a Sniffer E<PERSON>", "advancements.husbandry.obtain_sniffer_egg.title": "Smells Gripping", "advancements.husbandry.place_dried_ghast_in_water.description": "Lay a Dried Ghast clot into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Wet as a Bone", "advancements.husbandry.plant_any_sniffer_seed.description": "Sow any Sniffer seed", "advancements.husbandry.plant_any_sniffer_seed.title": "Sowing the Bygone", "advancements.husbandry.plant_seed.description": "Sow a seed and watch it grow", "advancements.husbandry.plant_seed.title": "A <PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Fornim <PERSON>ing from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Snithe It Off", "advancements.husbandry.repair_wolf_armor.description": "Fully fettle worn-out <PERSON>ing brooking Girdledeer <PERSON>hales", "advancements.husbandry.repair_wolf_armor.title": "Good as New", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Float in a Boat with a Goat", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Whatever Floats Your Goat!", "advancements.husbandry.root.description": "The world is full of friends and food", "advancements.husbandry.root.title": "Earthtilth", "advancements.husbandry.safely_harvest_honey.description": "Brook a Haltfire to gather Honey from a Beehive brooking a Glass Handvat without maddening the <PERSON>s", "advancements.husbandry.safely_harvest_honey.title": "Bee Our Yest", "advancements.husbandry.silk_touch_nest.description": "Shift a Bee Nest or Beehive, with 3 Bees inside, brooking Soft Rine", "advancements.husbandry.silk_touch_nest.title": "Full Beesetting", "advancements.husbandry.tactical_fishing.description": "Hole a Fish... without a Fishing Rod!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "Hole a Toadhead in a Stop", "advancements.husbandry.tadpole_in_a_bucket.title": "Ahead of the Stop", "advancements.husbandry.tame_an_animal.description": "Tame a deer", "advancements.husbandry.tame_an_animal.title": "Best Friends Forever", "advancements.husbandry.wax_off.description": "Shrape Wax off of an Are clot!", "advancements.husbandry.wax_off.title": "Wax Off", "advancements.husbandry.wax_on.description": "Beseech Honeycomb to an Are clot!", "advancements.husbandry.wax_on.title": "Wax On", "advancements.husbandry.whole_pack.description": "Tame one of each <PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Have every rine besought at the same time", "advancements.nether.all_effects.title": "How Did We End Up Here?", "advancements.nether.all_potions.description": "Have every lib rine besought at the same time", "advancements.nether.all_potions.title": "A Wood Cocktail", "advancements.nether.brew_potion.description": "Brew a Lib", "advancements.nether.brew_potion.title": "Neighwist Brewhouse", "advancements.nether.charge_respawn_anchor.description": "Load an Edstart Holder to the utmost", "advancements.nether.charge_respawn_anchor.title": "Not Alsuch \"Nine\" Lives", "advancements.nether.create_beacon.description": "Build and lay down a Beacon", "advancements.nether.create_beacon.title": "Bring Home the Beacon", "advancements.nether.create_full_beacon.description": "Bring a Beacon to full might", "advancements.nether.create_full_beacon.title": "Beacon Builder", "advancements.nether.distract_piglin.description": "Draw away Piglins with gold", "advancements.nether.distract_piglin.title": "Oh Shiny", "advancements.nether.explore_nether.description": "Seek all Nether lifeheaps", "advancements.nether.explore_nether.title": "Hot Sightseer Headings", "advancements.nether.fast_travel.description": "Brook the Nether to fare 7,000 m in the Overworld", "advancements.nether.fast_travel.title": "Underroomth Bubble", "advancements.nether.find_bastion.description": "Go into a Piglin Stronghold Lave", "advancements.nether.find_bastion.title": "Those Were the Days", "advancements.nether.find_fortress.description": "Break your way into a Nether Stronghold", "advancements.nether.find_fortress.title": "A Dreadful Stronghold", "advancements.nether.get_wither_skull.description": "Fetch a <PERSON><PERSON>'s headbone", "advancements.nether.get_wither_skull.title": "Frightful Fearsome Boneframe", "advancements.nether.loot_bastion.description": "Reave a Chest in a Piglin Stronghold Lave", "advancements.nether.loot_bastion.title": "Fight Swines", "advancements.nether.netherite_armor.description": "Get a full set of Netherite hirsting", "advancements.nether.netherite_armor.title": "Wry Me in Lave", "advancements.nether.obtain_ancient_debris.description": "Reap Fe<PERSON>", "advancements.nether.obtain_ancient_debris.title": "Hidden in the Depths", "advancements.nether.obtain_blaze_rod.description": "Liss a Blaze of its rod", "advancements.nether.obtain_blaze_rod.title": "Into Fire", "advancements.nether.obtain_crying_obsidian.description": "<PERSON>ap Weeping <PERSON>lint", "advancements.nether.obtain_crying_obsidian.title": "Who is <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>?", "advancements.nether.return_to_sender.description": "Slay a <PERSON><PERSON><PERSON> with a fireball", "advancements.nether.return_to_sender.title": "Eftcome to Sender", "advancements.nether.ride_strider.description": "Ride a Strider with a Warped Swamb on a Stick", "advancements.nether.ride_strider.title": "This Boat Has Legs", "advancements.nether.ride_strider_in_overworld_lava.description": "Go for a loooong ride on a Strider over a moltenstone mere in the Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "Feels Like Home", "advancements.nether.root.description": "Bring summer clothes", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON> the Wither", "advancements.nether.summon_wither.title": "Withering Heights", "advancements.nether.uneasy_alliance.description": "Neer a Ghast from the Nether, bring it soundly home to the Overworld... and then kill it", "advancements.nether.uneasy_alliance.title": "Ill Thoftship", "advancements.nether.use_lodestone.description": "Brook a Northfinder on a Lodestone", "advancements.nether.use_lodestone.title": "Homeland Lode, Make Me Home", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Woaken and then heal an Undead <PERSON><PERSON><PERSON>", "advancements.story.cure_zombie_villager.title": "Leech of the Liches", "advancements.story.deflect_arrow.description": "Bounce a shot with a Shield", "advancements.story.deflect_arrow.title": "Not Today, Thank You", "advancements.story.enchant_item.description": "Gale a thing at a Galdercraft Bench", "advancements.story.enchant_item.title": "Galer", "advancements.story.enter_the_end.description": "Go through the End Ingang", "advancements.story.enter_the_end.title": "The End?", "advancements.story.enter_the_nether.description": "Build, light and go through a Nether Ingang", "advancements.story.enter_the_nether.title": "We Need to Go Deeper", "advancements.story.follow_ender_eye.description": "Follow an Eye of Ender", "advancements.story.follow_ender_eye.title": "Eye Toot", "advancements.story.form_obsidian.description": "Reap a Ravenflint clot", "advancements.story.form_obsidian.title": "Ice Stop Dare", "advancements.story.iron_tools.description": "Better your Pike", "advancements.story.iron_tools.title": "Isn't It Iron Pike", "advancements.story.lava_bucket.description": "Fill a Stop with moltenstone", "advancements.story.lava_bucket.title": "Hot to Go!", "advancements.story.mine_diamond.description": "Fetch hardhirsts", "advancements.story.mine_diamond.title": "Hardhirsts!", "advancements.story.mine_stone.description": "Dig <PERSON> with your new <PERSON>", "advancements.story.mine_stone.title": "<PERSON>", "advancements.story.obtain_armor.description": "Shield yourself with a stetch of iron hirsting", "advancements.story.obtain_armor.title": "<PERSON><PERSON>", "advancements.story.root.description": "The heart and tale of the game", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Hardhirst hirsting neers lives", "advancements.story.shiny_gear.title": "Wry Me with Hardhirsts", "advancements.story.smelt_iron.description": "Smelt an Iron Ingot", "advancements.story.smelt_iron.title": "Fetch Hardware", "advancements.story.upgrade_tools.description": "Build a better Pike", "advancements.story.upgrade_tools.title": "Making a Bettering", "advancements.toast.challenge": "<PERSON>!", "advancements.toast.goal": "Goal Reached!", "advancements.toast.task": "Forthstep Made!", "argument.anchor.invalid": "Unright ansen holder stow %s", "argument.angle.incomplete": "Not done (abode 1 hirn)", "argument.angle.invalid": "Unright hirn", "argument.block.id.invalid": "Unknown clot kind '%s'", "argument.block.property.duplicate": "Holding '%s' can be set once only for clot %s", "argument.block.property.invalid": "Clot %s does not bear '%s' for %s holding", "argument.block.property.novalue": "Abode worth for holding '%s' on clot %s", "argument.block.property.unclosed": "Abode shutting ] for clot hoad holdings", "argument.block.property.unknown": "Clot %s does not have holding '%s'", "argument.block.tag.disallowed": "Tokens aren't aleaved here, only true clots", "argument.color.invalid": "Unknown hue '%s'", "argument.component.invalid": "Unright chat underdeal: %s", "argument.criteria.invalid": "Unknown yardstick '%s'", "argument.dimension.invalid": "Unknown farstead '%s'", "argument.double.big": "Twofold must not be more than %s, found %s", "argument.double.low": "Twofold must not be less than %s, found %s", "argument.entity.invalid": "Unright name or UUID", "argument.entity.notfound.entity": "No ansen was found", "argument.entity.notfound.player": "No player was found", "argument.entity.options.advancements.description": "Players with forthsteps", "argument.entity.options.distance.description": "Length to ansen", "argument.entity.options.distance.negative": "Length cannot be undernaught", "argument.entity.options.dx.description": "Ansens between x and x + dx", "argument.entity.options.dy.description": "Ansens between y and y + dy", "argument.entity.options.dz.description": "Ansens between z and z + dz", "argument.entity.options.gamemode.description": "Players with game wayset", "argument.entity.options.inapplicable": "Kire '%s' can't work here", "argument.entity.options.level.description": "Cunning layer", "argument.entity.options.level.negative": "Layer shouldn't be undernaught", "argument.entity.options.limit.description": "Highest rime of ansens to bring back", "argument.entity.options.limit.toosmall": "Threshold must be at least 1", "argument.entity.options.mode.invalid": "Unright or unknown game wayset '%s'", "argument.entity.options.name.description": "Ansen name", "argument.entity.options.nbt.description": "<PERSON><PERSON><PERSON> with NTT", "argument.entity.options.predicate.description": "Bespoke foreworth", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON> with stands", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON> the ansens", "argument.entity.options.sort.irreversible": "Unright or unknown temse kind '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON><PERSON> with token", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON> on team", "argument.entity.options.type.description": "Ansens of kind", "argument.entity.options.type.invalid": "Unright or unknown ansen kind '%s'", "argument.entity.options.unknown": "Unknown kire '%s'", "argument.entity.options.unterminated": "Abode end of kires", "argument.entity.options.valueless": "Abode worth for kire '%s'", "argument.entity.options.x.description": "x stow", "argument.entity.options.x_rotation.description": "<PERSON><PERSON>'s x wharving", "argument.entity.options.y.description": "y stow", "argument.entity.options.y_rotation.description": "<PERSON><PERSON>'s y wharving", "argument.entity.options.z.description": "z stow", "argument.entity.selector.allEntities": "All ansens", "argument.entity.selector.allPlayers": "All players", "argument.entity.selector.missing": "Missing chooser kind", "argument.entity.selector.nearestEntity": "Nearest ansen", "argument.entity.selector.nearestPlayer": "Nearest player", "argument.entity.selector.not_allowed": "Chooser not aleaved", "argument.entity.selector.randomPlayer": "Hapsome player", "argument.entity.selector.self": "<PERSON><PERSON> ansen", "argument.entity.selector.unknown": "Unknown chooser kind '%s'", "argument.entity.toomany": "Only one ansen is aleaved, but the yeaven chooser aleaves more than one", "argument.enum.invalid": "Unright worth \"%s\"", "argument.float.big": "Float must not be more than %s, found %s", "argument.float.low": "Float must not be less than %s, found %s", "argument.gamemode.invalid": "Unknown game wayset: %s", "argument.hexcolor.invalid": "Unright sixteenish hue dern '%s'", "argument.id.invalid": "Unright IHOOD", "argument.id.unknown": "Unknown IHOOD: %s", "argument.integer.big": "Wholerime must not be more than %s, found %s", "argument.integer.low": "Wholerime must not be less than %s, found %s", "argument.item.id.invalid": "Unknown thing %s'", "argument.item.tag.disallowed": "Tokens aren't aleaved here, only true things", "argument.literal.incorrect": "Abode staffly %s", "argument.long.big": "Long must not be more than %s, found %s", "argument.long.low": "Long must not less than %s, found %s", "argument.message.too_long": "Chat writ was too long (%s > greatest %s staves)", "argument.nbt.array.invalid": "Unright list kind '%s'", "argument.nbt.array.mixed": "Can't input %s into %s", "argument.nbt.expected.compound": "Abode binding token", "argument.nbt.expected.key": "Abode key", "argument.nbt.expected.value": "Abode worth", "argument.nbt.list.mixed": "Can't input %s into list of %s", "argument.nbt.trailing": "Unweened loasting lore", "argument.player.entities": "Only players may be onworked by this hest, but the yeaven chooser ins ansens inside", "argument.player.toomany": "Only one player is aleaved, but the yeaven chooser aleaves more than one", "argument.player.unknown": "That player does not bestand", "argument.pos.missing.double": "Abode a stowrime", "argument.pos.missing.int": "Abode a clot stow", "argument.pos.mixed": "Cannot mix world and neigh stowrimes (everything must either use ^ or not)", "argument.pos.outofbounds": "That stow is outside the aleaved rims.", "argument.pos.outofworld": "That stow is out of this world!", "argument.pos.unloaded": "That stow is not loaded", "argument.pos2d.incomplete": "Underdone (abode 2 stowrimes)", "argument.pos3d.incomplete": "Underdone (abode 3 stowrimes)", "argument.range.empty": "Abode worth or breadth of worths", "argument.range.ints": "Only whole rimes aleaved, not dots", "argument.range.swapped": "Least cannot be greater than highest", "argument.resource.invalid_type": "Thing '%s' has unright kind '%s' (abode '%s')", "argument.resource.not_found": "Can't find thing '%s' of kind '%s'", "argument.resource_or_id.failed_to_parse": "Could not understand framework: %s", "argument.resource_or_id.invalid": "Unright ihood or token", "argument.resource_or_id.no_such_element": "Can't find thing '%s' in tell '%s'", "argument.resource_selector.not_found": "No matches for chooser '%s' of kind '%s'", "argument.resource_tag.invalid_type": "Token '%s' has unright kind '%s' (abode '%s')", "argument.resource_tag.not_found": "Can't find token '%s' of kind '%s'", "argument.rotation.incomplete": "Underdone (abode 2 stowrimes)", "argument.scoreHolder.empty": "No fitting stand holders could be found", "argument.scoreboardDisplaySlot.invalid": "Unknown show groovestead '%s'", "argument.style.invalid": "Unright kind: %s", "argument.time.invalid_tick_count": "The tick rime must not be undernaught", "argument.time.invalid_unit": "Unright onedom", "argument.time.tick_count_too_low": "The tick rime must not be smaller than %s, found %s", "argument.uuid.invalid": "Unright UUID", "argument.waypoint.invalid": "<PERSON>sen ansen is not a wayord", "arguments.block.tag.unknown": "Unknown clot token '%s'", "arguments.function.tag.unknown": "Unknown working token '%s'", "arguments.function.unknown": "Unknown working %s", "arguments.item.component.expected": "Abode thing underdeal", "arguments.item.component.malformed": "Crooked '%s' underdeal: '%s'", "arguments.item.component.repeated": "Thing underdeal '%s' was edledged, but only one worth can be narrowed", "arguments.item.component.unknown": "Unknown thing underdeal '%s'", "arguments.item.malformed": "Crooked thing: '%s'", "arguments.item.overstacked": "%s can only heap up to %s", "arguments.item.predicate.malformed": "Crooked '%s' foreworth: '%s'", "arguments.item.predicate.unknown": "Unknown thing foreworth: '%s''", "arguments.item.tag.unknown": "Unknown thing token %s'", "arguments.nbtpath.node.invalid": "Unright NTT path thing", "arguments.nbtpath.nothing_found": "Found no things matching %s", "arguments.nbtpath.too_deep": "Outcoming NTT too deeply nested", "arguments.nbtpath.too_large": "Outcoming NTT too great", "arguments.objective.notFound": "Unknown standboard goal '%s'", "arguments.objective.readonly": "Standboard goal '%s' is read-only", "arguments.operation.div0": "Cannot sunder by naught", "arguments.operation.invalid": "Unright freeming", "arguments.swizzle.invalid": "Unright swizzle, abode mix of 'x', 'y', and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "Strike Harm", "attribute.name.attack_knockback": "Strike Knockback", "attribute.name.attack_speed": "Strike Speed", "attribute.name.block_break_speed": "Clot Break Speed", "attribute.name.block_interaction_range": "<PERSON><PERSON> Breadth", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Byldmaker Length", "attribute.name.entity_interaction_range": "<PERSON><PERSON>th", "attribute.name.explosion_knockback_resistance": "Blast Knockback Withstanding", "attribute.name.fall_damage_multiplier": "Fall Harm Manifolder", "attribute.name.flying_speed": "Flying Speed", "attribute.name.follow_range": "<PERSON>", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "Strike Harm", "attribute.name.generic.attack_knockback": "Strike Knockback", "attribute.name.generic.attack_speed": "Strike Speed", "attribute.name.generic.block_interaction_range": "<PERSON><PERSON> Breadth", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON>th", "attribute.name.generic.explosion_knockback_resistance": "Blast Knockback Withstanding", "attribute.name.generic.fall_damage_multiplier": "Fall Harm Manifolder", "attribute.name.generic.flying_speed": "Flying Speed", "attribute.name.generic.follow_range": "<PERSON>", "attribute.name.generic.gravity": "Heaviness", "attribute.name.generic.jump_strength": "Leap Strength", "attribute.name.generic.knockback_resistance": "Knockback Withstanding", "attribute.name.generic.luck": "Luck", "attribute.name.generic.max_absorption": "Greatest Upsoaking", "attribute.name.generic.max_health": "Greatest Health", "attribute.name.generic.movement_efficiency": "Shrithing Frimfulness", "attribute.name.generic.movement_speed": "Speed", "attribute.name.generic.oxygen_bonus": "Breath Overfrim", "attribute.name.generic.safe_fall_distance": "Shielded Fall Height", "attribute.name.generic.scale": "<PERSON>er", "attribute.name.generic.step_height": "Step Height", "attribute.name.generic.water_movement_efficiency": "Water Shrithing Frimfulness", "attribute.name.gravity": "Heaviness", "attribute.name.horse.jump_strength": "Horse Leap Strength", "attribute.name.jump_strength": "Leap Strength", "attribute.name.knockback_resistance": "Knockback Withstanding", "attribute.name.luck": "Luck", "attribute.name.max_absorption": "Greatest Upsoaking", "attribute.name.max_health": "Greatest Health", "attribute.name.mining_efficiency": "Delving Frimfulness", "attribute.name.movement_efficiency": "Shrithing Frimfulness", "attribute.name.movement_speed": "Speed", "attribute.name.oxygen_bonus": "Breath Overfrim", "attribute.name.player.block_break_speed": "Clot Break Speed", "attribute.name.player.block_interaction_range": "<PERSON><PERSON> Breadth", "attribute.name.player.entity_interaction_range": "<PERSON><PERSON>th", "attribute.name.player.mining_efficiency": "Delving Frimfulness", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Sunken Delving Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Harm Evenness", "attribute.name.safe_fall_distance": "Shielded Fall Height", "attribute.name.scale": "<PERSON>er", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Undead Lich Edstrengthenings", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Sunken Delving Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Harm Evenness", "attribute.name.tempt_range": "<PERSON>", "attribute.name.water_movement_efficiency": "Water Shrithing Frimfulness", "attribute.name.waypoint_receive_range": "<PERSON><PERSON>ap Breadth", "attribute.name.waypoint_transmit_range": "Wayord Oversend Breadth", "attribute.name.zombie.spawn_reinforcements": "Undead Lich Edstrengthenings", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Rinestone Mouths", "biome.minecraft.beach": "Beach", "biome.minecraft.birch_forest": "Birch Wold", "biome.minecraft.cherry_grove": "Stoneberry Grove", "biome.minecraft.cold_ocean": "Cold Sea", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON> Wold", "biome.minecraft.deep_cold_ocean": "Deep Cold Sea", "biome.minecraft.deep_dark": "Deep Dark", "biome.minecraft.deep_frozen_ocean": "Deep Icy Sea", "biome.minecraft.deep_lukewarm_ocean": "Deep Lukewarm Sea", "biome.minecraft.deep_ocean": "Deep Sea", "biome.minecraft.desert": "<PERSON>asten", "biome.minecraft.dripstone_caves": "Dripstone Hollows", "biome.minecraft.end_barrens": "End Barelands", "biome.minecraft.end_highlands": "End Highlands", "biome.minecraft.end_midlands": "End Midlands", "biome.minecraft.eroded_badlands": "Gnawed Badlands", "biome.minecraft.flower_forest": "Blossom Wold", "biome.minecraft.forest": "Wold", "biome.minecraft.frozen_ocean": "Icy <PERSON>", "biome.minecraft.frozen_peaks": "Icy Peaks", "biome.minecraft.frozen_river": "Icy Stream", "biome.minecraft.grove": "Grove", "biome.minecraft.ice_spikes": "Ice Thorns", "biome.minecraft.jagged_peaks": "Sawtooth Peaks", "biome.minecraft.jungle": "Rainwold", "biome.minecraft.lukewarm_ocean": "Lukewarm Sea", "biome.minecraft.lush_caves": "Green Hollows", "biome.minecraft.mangrove_swamp": "Liftmore Swamp", "biome.minecraft.meadow": "Meadow", "biome.minecraft.mushroom_fields": "Toadstool Fields", "biome.minecraft.nether_wastes": "Nether Barelands", "biome.minecraft.ocean": "Sea", "biome.minecraft.old_growth_birch_forest": "Old Growth Birch Wold", "biome.minecraft.old_growth_pine_taiga": "Old Growth Furrow Wold", "biome.minecraft.old_growth_spruce_taiga": "Old Growth Harttartree Wold", "biome.minecraft.pale_garden": "Bloak Yard", "biome.minecraft.plains": "Grasslands", "biome.minecraft.river": "Stream", "biome.minecraft.savanna": "Bareland", "biome.minecraft.savanna_plateau": "Bareland Beedland", "biome.minecraft.small_end_islands": "Small End Ilands", "biome.minecraft.snowy_beach": "Snowy Beach", "biome.minecraft.snowy_plains": "Snowy Grasslands", "biome.minecraft.snowy_slopes": "Snowy Hillsides", "biome.minecraft.snowy_taiga": "Snowy Northwold", "biome.minecraft.soul_sand_valley": "<PERSON>", "biome.minecraft.sparse_jungle": "Thin <PERSON>", "biome.minecraft.stony_peaks": "Stony Peaks", "biome.minecraft.stony_shore": "Stony Shore", "biome.minecraft.sunflower_plains": "Sunblossom Grasslands", "biome.minecraft.swamp": "Swamp", "biome.minecraft.taiga": "Northwold", "biome.minecraft.the_end": "The End", "biome.minecraft.the_void": "The Emptiness", "biome.minecraft.warm_ocean": "Warm Sea", "biome.minecraft.warped_forest": "Warped Wold", "biome.minecraft.windswept_forest": "Windswept Wold", "biome.minecraft.windswept_gravelly_hills": "Windswept Pebbly Hills", "biome.minecraft.windswept_hills": "Windswept Hills", "biome.minecraft.windswept_savanna": "Windswept Bareland", "biome.minecraft.wooded_badlands": "Wooded Badlands", "block.minecraft.acacia_button": "Wattletree Knap", "block.minecraft.acacia_door": "Wattletree Door", "block.minecraft.acacia_fence": "Watt<PERSON>ree Edder", "block.minecraft.acacia_fence_gate": "Wattletree Edder Gate", "block.minecraft.acacia_hanging_sign": "Wattletree Hanging Token", "block.minecraft.acacia_leaves": "Wattletree Leaves", "block.minecraft.acacia_log": "Wattletree Stock", "block.minecraft.acacia_planks": "Wattletree Boards", "block.minecraft.acacia_pressure_plate": "Wattletree Thrutch Tile", "block.minecraft.acacia_sapling": "Wattletree Sprout", "block.minecraft.acacia_sign": "Wattletree Token", "block.minecraft.acacia_slab": "Wattletree Halfclot", "block.minecraft.acacia_stairs": "Wattletree Stairs", "block.minecraft.acacia_trapdoor": "Wattletree Trapdoor", "block.minecraft.acacia_wall_hanging_sign": "Wattletree Side Hanging Token", "block.minecraft.acacia_wall_sign": "Wattletree Side Token", "block.minecraft.acacia_wood": "Wattletree Wood", "block.minecraft.activator_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.air": "Lift", "block.minecraft.allium": "<PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Drunklack Clot", "block.minecraft.amethyst_cluster": "Drunklack Cluster", "block.minecraft.ancient_debris": "<PERSON><PERSON>", "block.minecraft.andesite": "Andestone", "block.minecraft.andesite_slab": "Andestone Halfclot", "block.minecraft.andesite_stairs": "Andestone Stairs", "block.minecraft.andesite_wall": "Andestone Wough", "block.minecraft.anvil": "An<PERSON>", "block.minecraft.attached_melon_stem": "Linked Entapple Stem", "block.minecraft.attached_pumpkin_stem": "Linked Harvestovet Stem", "block.minecraft.azalea": "Drywort", "block.minecraft.azalea_leaves": "Drywort Leaves", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo": "Treereed", "block.minecraft.bamboo_block": "Treereed Clot", "block.minecraft.bamboo_button": "<PERSON><PERSON>", "block.minecraft.bamboo_door": "Treereed Door", "block.minecraft.bamboo_fence": "<PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Treereed Edder Gate", "block.minecraft.bamboo_hanging_sign": "Treereed Hanging Token", "block.minecraft.bamboo_mosaic": "Woven Treereed", "block.minecraft.bamboo_mosaic_slab": "Woven Treereed Halfclot", "block.minecraft.bamboo_mosaic_stairs": "Woven Treereed Stairs", "block.minecraft.bamboo_planks": "Treereed Boards", "block.minecraft.bamboo_pressure_plate": "<PERSON><PERSON> Thrutch Tile", "block.minecraft.bamboo_sapling": "Treereed Shoot", "block.minecraft.bamboo_sign": "Treereed Token", "block.minecraft.bamboo_slab": "<PERSON><PERSON> Half<PERSON>lot", "block.minecraft.bamboo_stairs": "Treereed Stairs", "block.minecraft.bamboo_trapdoor": "<PERSON>reed Trapdoor", "block.minecraft.bamboo_wall_hanging_sign": "Treereed Side Hanging Token", "block.minecraft.bamboo_wall_sign": "Treereed Side Token", "block.minecraft.banner.base.black": "<PERSON>y Black <PERSON>", "block.minecraft.banner.base.blue": "Fully Hewn Field", "block.minecraft.banner.base.brown": "<PERSON><PERSON>", "block.minecraft.banner.base.cyan": "Fully Hewngreen Field", "block.minecraft.banner.base.gray": "<PERSON><PERSON>", "block.minecraft.banner.base.green": "Fully Green Field", "block.minecraft.banner.base.light_blue": "Fully Light Hewn Field", "block.minecraft.banner.base.light_gray": "Fully Light Gray Field", "block.minecraft.banner.base.lime": "Fully Light Green Field", "block.minecraft.banner.base.magenta": "<PERSON>y <PERSON>ed <PERSON>", "block.minecraft.banner.base.orange": "<PERSON>y <PERSON>", "block.minecraft.banner.base.pink": "Fully Light Red Field", "block.minecraft.banner.base.purple": "Fully Baze Field", "block.minecraft.banner.base.red": "Fully Red Field", "block.minecraft.banner.base.white": "<PERSON>y White <PERSON>", "block.minecraft.banner.base.yellow": "Fully Yellow Field", "block.minecraft.banner.border.black": "Black Frame", "block.minecraft.banner.border.blue": "<PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON>", "block.minecraft.banner.border.cyan": "He<PERSON>green Frame", "block.minecraft.banner.border.gray": "<PERSON>", "block.minecraft.banner.border.green": "Green Frame", "block.minecraft.banner.border.light_blue": "Light Hewn Frame", "block.minecraft.banner.border.light_gray": "<PERSON> Gray Frame", "block.minecraft.banner.border.lime": "Light Green Frame", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "Light Red Frame", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "Red Frame", "block.minecraft.banner.border.white": "White Frame", "block.minecraft.banner.border.yellow": "Yellow Frame", "block.minecraft.banner.bricks.black": "Black Field Stonewrighted", "block.minecraft.banner.bricks.blue": "Hewn Field Stonewrighted", "block.minecraft.banner.bricks.brown": "Brown Field Stonewrighted", "block.minecraft.banner.bricks.cyan": "Hewngreen Field Stonewrighted", "block.minecraft.banner.bricks.gray": "<PERSON> Stonewrighted", "block.minecraft.banner.bricks.green": "Green Field Stonewrighted", "block.minecraft.banner.bricks.light_blue": "Light Hewn Field Stonewrighted", "block.minecraft.banner.bricks.light_gray": "<PERSON> Gray Field Stonewrighted", "block.minecraft.banner.bricks.lime": "Light Green Field Stonewrighted", "block.minecraft.banner.bricks.magenta": "Bazered Field Stonewrighted", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON> Stonewrighted", "block.minecraft.banner.bricks.pink": "Light Red Field Stonewrighted", "block.minecraft.banner.bricks.purple": "Baze Field Stonewrighted", "block.minecraft.banner.bricks.red": "Red Field Stonewrighted", "block.minecraft.banner.bricks.white": "White Field Stonewrighted", "block.minecraft.banner.bricks.yellow": "Yellow Field Stonewrighted", "block.minecraft.banner.circle.black": "Black Trendle", "block.minecraft.banner.circle.blue": "He<PERSON> Trendle", "block.minecraft.banner.circle.brown": "<PERSON> Trendle", "block.minecraft.banner.circle.cyan": "Hewngreen Trendle", "block.minecraft.banner.circle.gray": "<PERSON>", "block.minecraft.banner.circle.green": "Green Trendle", "block.minecraft.banner.circle.light_blue": "Light Hewn Trendle", "block.minecraft.banner.circle.light_gray": "Light Gray Trendle", "block.minecraft.banner.circle.lime": "Light Green Trendle", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON> Trendle", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "Light Red Trendle", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>dle", "block.minecraft.banner.circle.red": "<PERSON> Trendle", "block.minecraft.banner.circle.white": "White Trendle", "block.minecraft.banner.circle.yellow": "Yellow Trendle", "block.minecraft.banner.creeper.black": "Black Creeper Shape", "block.minecraft.banner.creeper.blue": "Hewn Creeper <PERSON>pe", "block.minecraft.banner.creeper.brown": "Brown Creeper Shape", "block.minecraft.banner.creeper.cyan": "Hewngreen Creeper Shape", "block.minecraft.banner.creeper.gray": "<PERSON>", "block.minecraft.banner.creeper.green": "Green Creeper Shape", "block.minecraft.banner.creeper.light_blue": "Light Hewn Creeper Shape", "block.minecraft.banner.creeper.light_gray": "Light Gray Creeper Shape", "block.minecraft.banner.creeper.lime": "Light Green Creeper Shape", "block.minecraft.banner.creeper.magenta": "Bazered Creeper Shape", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "Light Red Creeper Shape", "block.minecraft.banner.creeper.purple": "<PERSON>ze Creeper Sha<PERSON>", "block.minecraft.banner.creeper.red": "Red Creeper Shape", "block.minecraft.banner.creeper.white": "White Creeper Shape", "block.minecraft.banner.creeper.yellow": "Yellow Creeper Shape", "block.minecraft.banner.cross.black": "Black Leaning Rood", "block.minecraft.banner.cross.blue": "Hewn Leaning Rood", "block.minecraft.banner.cross.brown": "<PERSON> Leaning Rood", "block.minecraft.banner.cross.cyan": "Hewngreen Leaning Rood", "block.minecraft.banner.cross.gray": "<PERSON> Rood", "block.minecraft.banner.cross.green": "Green Leaning Rood", "block.minecraft.banner.cross.light_blue": "Light Hewn Leaning Rood", "block.minecraft.banner.cross.light_gray": "<PERSON> Gray Leaning Rood", "block.minecraft.banner.cross.lime": "Light Green Leaning Rood", "block.minecraft.banner.cross.magenta": "Bazered Leaning Rood", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> Rood", "block.minecraft.banner.cross.pink": "Light Red Leaning Rood", "block.minecraft.banner.cross.purple": "Baze Leaning Rood", "block.minecraft.banner.cross.red": "Red Leaning Rood", "block.minecraft.banner.cross.white": "White Leaning Rood", "block.minecraft.banner.cross.yellow": "Yellow Leaning Rood", "block.minecraft.banner.curly_border.black": "Black Sawtoothed Frame", "block.minecraft.banner.curly_border.blue": "Hewn Sawtoothed Frame", "block.minecraft.banner.curly_border.brown": "<PERSON> Sawtoothed Frame", "block.minecraft.banner.curly_border.cyan": "Hewngreen Sawtoothed Frame", "block.minecraft.banner.curly_border.gray": "<PERSON> Sawtoothed Frame", "block.minecraft.banner.curly_border.green": "Green Sawtoothed Frame", "block.minecraft.banner.curly_border.light_blue": "Light Hewn Sawtoothed Frame", "block.minecraft.banner.curly_border.light_gray": "<PERSON> <PERSON> Sawtoothed Frame", "block.minecraft.banner.curly_border.lime": "Light Green Sawtoothed Frame", "block.minecraft.banner.curly_border.magenta": "Bazered Sawtoothed Frame", "block.minecraft.banner.curly_border.orange": "Yellow Sawtoothed Frame", "block.minecraft.banner.curly_border.pink": "Light Red Sawtoothed Frame", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> Sawtoothed Frame", "block.minecraft.banner.curly_border.red": "Red Sawtoothed Frame", "block.minecraft.banner.curly_border.white": "White Sawtoothed Frame", "block.minecraft.banner.curly_border.yellow": "Yellow Sawtoothed Frame", "block.minecraft.banner.diagonal_left.black": "Black Left Leaning Up", "block.minecraft.banner.diagonal_left.blue": "He<PERSON> Left Leaning Up", "block.minecraft.banner.diagonal_left.brown": "<PERSON> Left Leaning Up", "block.minecraft.banner.diagonal_left.cyan": "Hewngreen Left Leaning Up", "block.minecraft.banner.diagonal_left.gray": "<PERSON> Left Leaning Up", "block.minecraft.banner.diagonal_left.green": "Green Left Leaning Up", "block.minecraft.banner.diagonal_left.light_blue": "Light Hewn Left Leaning Up", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON> Left Leaning Up", "block.minecraft.banner.diagonal_left.lime": "Light Green Left Leaning Up", "block.minecraft.banner.diagonal_left.magenta": "Bazered Left Leaning Up", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON> Left Leaning Up", "block.minecraft.banner.diagonal_left.pink": "Light Red Left Leaning Up", "block.minecraft.banner.diagonal_left.purple": "Baze Left Leaning Up", "block.minecraft.banner.diagonal_left.red": "Red Left Leaning Up", "block.minecraft.banner.diagonal_left.white": "White Left Leaning Up", "block.minecraft.banner.diagonal_left.yellow": "Yellow Left Leaning Up", "block.minecraft.banner.diagonal_right.black": "Black Right Leaning Up", "block.minecraft.banner.diagonal_right.blue": "Hewn Right Leaning Up", "block.minecraft.banner.diagonal_right.brown": "<PERSON> Right Leaning Up", "block.minecraft.banner.diagonal_right.cyan": "Hewngreen Right Leaning Up", "block.minecraft.banner.diagonal_right.gray": "<PERSON> Right Leaning Up", "block.minecraft.banner.diagonal_right.green": "Green Right Leaning Up", "block.minecraft.banner.diagonal_right.light_blue": "Light Hewn Right Leaning Up", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON> Gray Right Leaning Up", "block.minecraft.banner.diagonal_right.lime": "Light Green Right Leaning Up", "block.minecraft.banner.diagonal_right.magenta": "Bazered Right Leaning Up", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON> Right Leaning Up", "block.minecraft.banner.diagonal_right.pink": "Light Red Right Leaning Up", "block.minecraft.banner.diagonal_right.purple": "Baze Right Leaning Up", "block.minecraft.banner.diagonal_right.red": "Red Right Leaning Up", "block.minecraft.banner.diagonal_right.white": "White Right Leaning Up", "block.minecraft.banner.diagonal_right.yellow": "Yellow Right Leaning Up", "block.minecraft.banner.diagonal_up_left.black": "Black Left Leaning Down", "block.minecraft.banner.diagonal_up_left.blue": "Hewn Left Leaning Down", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON> Left Leaning Down", "block.minecraft.banner.diagonal_up_left.cyan": "Hewngreen Left Leaning Down", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON> Left Leaning Down", "block.minecraft.banner.diagonal_up_left.green": "Green Left Leaning Down", "block.minecraft.banner.diagonal_up_left.light_blue": "Light Hewn Left Leaning Down", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON> Left Leaning Down", "block.minecraft.banner.diagonal_up_left.lime": "Light Green Left Leaning Down", "block.minecraft.banner.diagonal_up_left.magenta": "Bazered Left Leaning Down", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON> Left Leaning Down", "block.minecraft.banner.diagonal_up_left.pink": "Light Red Left Leaning Down", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON>ze Left Leaning Down", "block.minecraft.banner.diagonal_up_left.red": "Red Left Leaning Down", "block.minecraft.banner.diagonal_up_left.white": "White Left Leaning Down", "block.minecraft.banner.diagonal_up_left.yellow": "Yellow Left Leaning Down", "block.minecraft.banner.diagonal_up_right.black": "Black Right Leaning Down", "block.minecraft.banner.diagonal_up_right.blue": "Hewn Right Leaning Down", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON> Right Leaning Down", "block.minecraft.banner.diagonal_up_right.cyan": "Hewngreen Right Leaning Down", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON> Right Leaning Down", "block.minecraft.banner.diagonal_up_right.green": "Green Right Leaning Down", "block.minecraft.banner.diagonal_up_right.light_blue": "Light Hewn Right Leaning Down", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON> Gray Right Leaning Down", "block.minecraft.banner.diagonal_up_right.lime": "Light Green Right Leaning Down", "block.minecraft.banner.diagonal_up_right.magenta": "Bazered Right Leaning Down", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON> Right Leaning Down", "block.minecraft.banner.diagonal_up_right.pink": "Light Red Right Leaning Down", "block.minecraft.banner.diagonal_up_right.purple": "Baze Right Leaning Down", "block.minecraft.banner.diagonal_up_right.red": "Red Right Slanted Down", "block.minecraft.banner.diagonal_up_right.white": "White Right Leaning Down", "block.minecraft.banner.diagonal_up_right.yellow": "Yellow Right Leaning Down", "block.minecraft.banner.flow.black": "Black Flow", "block.minecraft.banner.flow.blue": "Hewn Flow", "block.minecraft.banner.flow.brown": "Brown Flow", "block.minecraft.banner.flow.cyan": "Hewngreen Flow", "block.minecraft.banner.flow.gray": "Gray Flow", "block.minecraft.banner.flow.green": "Green Flow", "block.minecraft.banner.flow.light_blue": "Light Hewn Flow", "block.minecraft.banner.flow.light_gray": "Light Gray Flow", "block.minecraft.banner.flow.lime": "Light Green Flow", "block.minecraft.banner.flow.magenta": "Bazered Flow", "block.minecraft.banner.flow.orange": "<PERSON><PERSON>", "block.minecraft.banner.flow.pink": "Light Red Flow", "block.minecraft.banner.flow.purple": "Baze Flow", "block.minecraft.banner.flow.red": "Red Flow", "block.minecraft.banner.flow.white": "White Flow", "block.minecraft.banner.flow.yellow": "Yellow Flow", "block.minecraft.banner.flower.black": "Black Blossom Shape", "block.minecraft.banner.flower.blue": "Hewn Blossom Shape", "block.minecraft.banner.flower.brown": "Brown Blossom Shape", "block.minecraft.banner.flower.cyan": "Hewngreen Blossom Shape", "block.minecraft.banner.flower.gray": "Gray Blossom Shape", "block.minecraft.banner.flower.green": "Green Blossom Shape", "block.minecraft.banner.flower.light_blue": "Light Hewn Blossom Shape", "block.minecraft.banner.flower.light_gray": "Light Gray Blossom Shape", "block.minecraft.banner.flower.lime": "Light Green Blossom Shape", "block.minecraft.banner.flower.magenta": "Bazered Blossom Shape", "block.minecraft.banner.flower.orange": "Yellowred Blossom <PERSON>pe", "block.minecraft.banner.flower.pink": "Light Red Blossom Shape", "block.minecraft.banner.flower.purple": "Baze Blossom Shape", "block.minecraft.banner.flower.red": "Red Blossom Shape", "block.minecraft.banner.flower.white": "White Blossom Shape", "block.minecraft.banner.flower.yellow": "Yellow Blossom Shape", "block.minecraft.banner.globe.black": "Black World", "block.minecraft.banner.globe.blue": "Hewn World", "block.minecraft.banner.globe.brown": "Brown World", "block.minecraft.banner.globe.cyan": "Hewngreen World", "block.minecraft.banner.globe.gray": "Gray World", "block.minecraft.banner.globe.green": "Green World", "block.minecraft.banner.globe.light_blue": "Light Hewn World", "block.minecraft.banner.globe.light_gray": "Light Gray World", "block.minecraft.banner.globe.lime": "Light Green World", "block.minecraft.banner.globe.magenta": "Bazered World", "block.minecraft.banner.globe.orange": "<PERSON><PERSON>", "block.minecraft.banner.globe.pink": "Light Red World", "block.minecraft.banner.globe.purple": "Baze World", "block.minecraft.banner.globe.red": "Red World", "block.minecraft.banner.globe.white": "White World", "block.minecraft.banner.globe.yellow": "Yellow World", "block.minecraft.banner.gradient.black": "Black Dimming", "block.minecraft.banner.gradient.blue": "He<PERSON> Dimming", "block.minecraft.banner.gradient.brown": "<PERSON>", "block.minecraft.banner.gradient.cyan": "Hewngreen Dimming", "block.minecraft.banner.gradient.gray": "<PERSON>", "block.minecraft.banner.gradient.green": "Green Dimming", "block.minecraft.banner.gradient.light_blue": "Light Hewn Dimming", "block.minecraft.banner.gradient.light_gray": "Light Gray Dimming", "block.minecraft.banner.gradient.lime": "Light Green Dimming", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON> Dimming", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "Light Red Dimming", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient.red": "Red Dimming", "block.minecraft.banner.gradient.white": "White Dimming", "block.minecraft.banner.gradient.yellow": "Yellow Dimming", "block.minecraft.banner.gradient_up.black": "Black Bottom Dimming", "block.minecraft.banner.gradient_up.blue": "Hewn Bottom Dimming", "block.minecraft.banner.gradient_up.brown": "<PERSON> Bottom Dimming", "block.minecraft.banner.gradient_up.cyan": "Hewngreen Bottom Dimming", "block.minecraft.banner.gradient_up.gray": "<PERSON> Bottom Dimming", "block.minecraft.banner.gradient_up.green": "Green Bottom Dimming", "block.minecraft.banner.gradient_up.light_blue": "Light Hewn Bottom Dimming", "block.minecraft.banner.gradient_up.light_gray": "Light Gray Bottom Dimming", "block.minecraft.banner.gradient_up.lime": "Light Green Bottom Dimming", "block.minecraft.banner.gradient_up.magenta": "Bazered Bottom Dimming", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.pink": "Light Red Bottom Dimming", "block.minecraft.banner.gradient_up.purple": "Baze Bottom Dimming", "block.minecraft.banner.gradient_up.red": "Red Bottom Dimming", "block.minecraft.banner.gradient_up.white": "White Bottom Dimming", "block.minecraft.banner.gradient_up.yellow": "Yellow Bottom Dimming", "block.minecraft.banner.guster.black": "Black Thoder", "block.minecraft.banner.guster.blue": "He<PERSON> Thoder", "block.minecraft.banner.guster.brown": "<PERSON> Thoder", "block.minecraft.banner.guster.cyan": "Hewngreen Thoder", "block.minecraft.banner.guster.gray": "<PERSON>", "block.minecraft.banner.guster.green": "Green Thoder", "block.minecraft.banner.guster.light_blue": "Light Hewn Thoder", "block.minecraft.banner.guster.light_gray": "Light Gray Thoder", "block.minecraft.banner.guster.lime": "Light Green Thoder", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON> Thoder", "block.minecraft.banner.guster.orange": "<PERSON><PERSON>", "block.minecraft.banner.guster.pink": "Light Red Thoder", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "Red Thoder", "block.minecraft.banner.guster.white": "White Thoder", "block.minecraft.banner.guster.yellow": "Yellow Thoder", "block.minecraft.banner.half_horizontal.black": "Black Top Half", "block.minecraft.banner.half_horizontal.blue": "Hewn Top Half", "block.minecraft.banner.half_horizontal.brown": "Brown Top Half", "block.minecraft.banner.half_horizontal.cyan": "Hewngreen Top Half", "block.minecraft.banner.half_horizontal.gray": "Gray Top Half", "block.minecraft.banner.half_horizontal.green": "Green Top Half", "block.minecraft.banner.half_horizontal.light_blue": "Light Hewn Top Half", "block.minecraft.banner.half_horizontal.light_gray": "Light Gray Top Half", "block.minecraft.banner.half_horizontal.lime": "Light Green Top Half", "block.minecraft.banner.half_horizontal.magenta": "Bazered Top Half", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON> Top Half", "block.minecraft.banner.half_horizontal.pink": "Light Red Top Half", "block.minecraft.banner.half_horizontal.purple": "Baze Top Half", "block.minecraft.banner.half_horizontal.red": "Red Top Half", "block.minecraft.banner.half_horizontal.white": "White Top Half", "block.minecraft.banner.half_horizontal.yellow": "Yellow Top Half", "block.minecraft.banner.half_horizontal_bottom.black": "Black Bottom Half", "block.minecraft.banner.half_horizontal_bottom.blue": "Hewn Bottom Half", "block.minecraft.banner.half_horizontal_bottom.brown": "Brown Bottom Half", "block.minecraft.banner.half_horizontal_bottom.cyan": "Hewngreen Bottom Half", "block.minecraft.banner.half_horizontal_bottom.gray": "Gray Bottom Half", "block.minecraft.banner.half_horizontal_bottom.green": "Green Bottom Half", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Light Hewn Bottom Half", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Light Gray Bottom Half", "block.minecraft.banner.half_horizontal_bottom.lime": "Light Green Bottom Half", "block.minecraft.banner.half_horizontal_bottom.magenta": "Bazered Bottom Half", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Light Red Bottom Half", "block.minecraft.banner.half_horizontal_bottom.purple": "Baze Bottom Half", "block.minecraft.banner.half_horizontal_bottom.red": "Red Bottom Half", "block.minecraft.banner.half_horizontal_bottom.white": "White Bottom Half", "block.minecraft.banner.half_horizontal_bottom.yellow": "Yellow Bottom Half", "block.minecraft.banner.half_vertical.black": "<PERSON> Upright Half", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON> Upright Half", "block.minecraft.banner.half_vertical.brown": "<PERSON>right Half", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON> Upright Half", "block.minecraft.banner.half_vertical.gray": "<PERSON>", "block.minecraft.banner.half_vertical.green": "<PERSON> Upright Half", "block.minecraft.banner.half_vertical.light_blue": "Light Hewn Upright Half", "block.minecraft.banner.half_vertical.light_gray": "<PERSON> <PERSON> Upright Half", "block.minecraft.banner.half_vertical.lime": "Light Green Upright Half", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON> Upright Half", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "Light Red Upright Half", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON>right Half", "block.minecraft.banner.half_vertical.white": "<PERSON>right Half", "block.minecraft.banner.half_vertical.yellow": "Yellow Upright Half", "block.minecraft.banner.half_vertical_right.black": "<PERSON>right Half Right Side", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> Upright Half Right Side", "block.minecraft.banner.half_vertical_right.brown": "<PERSON>right Half Right Side", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON> Half Right Side", "block.minecraft.banner.half_vertical_right.gray": "<PERSON> Half Right Side", "block.minecraft.banner.half_vertical_right.green": "<PERSON>right Half Right Side", "block.minecraft.banner.half_vertical_right.light_blue": "Light Hewn Upright Half Right Side", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON> <PERSON> Upright Half Right Side", "block.minecraft.banner.half_vertical_right.lime": "Light Green Upright Half Right Side", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON> Upright Half Right Side", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON> Half Right Side", "block.minecraft.banner.half_vertical_right.pink": "Light Red Upright Half Right Side", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> Half Right Side", "block.minecraft.banner.half_vertical_right.red": "<PERSON> Half Right Side", "block.minecraft.banner.half_vertical_right.white": "<PERSON>right Half Right Side", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON> Upright Half Right Side", "block.minecraft.banner.mojang.black": "Black Thing", "block.minecraft.banner.mojang.blue": "Hewn Thing", "block.minecraft.banner.mojang.brown": "<PERSON>", "block.minecraft.banner.mojang.cyan": "Hewngreen Thing", "block.minecraft.banner.mojang.gray": "<PERSON>", "block.minecraft.banner.mojang.green": "Green Thing", "block.minecraft.banner.mojang.light_blue": "Light Hewn Thing", "block.minecraft.banner.mojang.light_gray": "Light Gray Thing", "block.minecraft.banner.mojang.lime": "Light Green Thing", "block.minecraft.banner.mojang.magenta": "Bazered Thing", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "Light Red Thing", "block.minecraft.banner.mojang.purple": "Baze Thing", "block.minecraft.banner.mojang.red": "Red Thing", "block.minecraft.banner.mojang.white": "White Thing", "block.minecraft.banner.mojang.yellow": "Yellow Thing", "block.minecraft.banner.piglin.black": "Black Swinenose", "block.minecraft.banner.piglin.blue": "Hewn Swinenose", "block.minecraft.banner.piglin.brown": "Brown Swinenose", "block.minecraft.banner.piglin.cyan": "Hewngreen Swinenose", "block.minecraft.banner.piglin.gray": "Gray <PERSON>nose", "block.minecraft.banner.piglin.green": "Green Swinenose", "block.minecraft.banner.piglin.light_blue": "Light Hewn Swinenose", "block.minecraft.banner.piglin.light_gray": "Light Gray Swinenose", "block.minecraft.banner.piglin.lime": "Light Green Swinenose", "block.minecraft.banner.piglin.magenta": "Bazered Swinenose", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "Light Red Swinenose", "block.minecraft.banner.piglin.purple": "Baze Swinenose", "block.minecraft.banner.piglin.red": "Red Swinenose", "block.minecraft.banner.piglin.white": "White Swinenose", "block.minecraft.banner.piglin.yellow": "Yellow Swinenose", "block.minecraft.banner.rhombus.black": "Black Kite", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON>e", "block.minecraft.banner.rhombus.brown": "<PERSON>", "block.minecraft.banner.rhombus.cyan": "Hewngreen Kite", "block.minecraft.banner.rhombus.gray": "<PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON> Kite", "block.minecraft.banner.rhombus.light_blue": "Light Hewn Kite", "block.minecraft.banner.rhombus.light_gray": "Light Gray Kite", "block.minecraft.banner.rhombus.lime": "Light Green Kite", "block.minecraft.banner.rhombus.magenta": "Bazered Kite", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "Light Red Kite", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON>e", "block.minecraft.banner.rhombus.white": "White Kite", "block.minecraft.banner.rhombus.yellow": "Yellow Kite", "block.minecraft.banner.skull.black": "Black Headbone Shape", "block.minecraft.banner.skull.blue": "Hewn Headbone Shape", "block.minecraft.banner.skull.brown": "<PERSON>bone Shape", "block.minecraft.banner.skull.cyan": "Hewngreen Headbone Shape", "block.minecraft.banner.skull.gray": "<PERSON> S<PERSON>pe", "block.minecraft.banner.skull.green": "Green Headbone Shape", "block.minecraft.banner.skull.light_blue": "Light Hewn Headbone Shape", "block.minecraft.banner.skull.light_gray": "<PERSON> <PERSON> Headbone Shape", "block.minecraft.banner.skull.lime": "Light Green Headbone Shape", "block.minecraft.banner.skull.magenta": "Bazered Headbone Shape", "block.minecraft.banner.skull.orange": "<PERSON><PERSON>pe", "block.minecraft.banner.skull.pink": "Light Red Headbone Shape", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>bone Shape", "block.minecraft.banner.skull.red": "Red Headbone Shape", "block.minecraft.banner.skull.white": "White Headbone Shape", "block.minecraft.banner.skull.yellow": "Yellow Headbone Shape", "block.minecraft.banner.small_stripes.black": "Black Standing Streaks", "block.minecraft.banner.small_stripes.blue": "Hewn Standing Streaks", "block.minecraft.banner.small_stripes.brown": "Brown Standing Streaks", "block.minecraft.banner.small_stripes.cyan": "Hewngreen Standing Streaks", "block.minecraft.banner.small_stripes.gray": "Gray Standing Streaks", "block.minecraft.banner.small_stripes.green": "Green Standing Streaks", "block.minecraft.banner.small_stripes.light_blue": "Light Hewn Standing Streaks", "block.minecraft.banner.small_stripes.light_gray": "Light Gray Standing Streaks", "block.minecraft.banner.small_stripes.lime": "Light Green Standing Streaks", "block.minecraft.banner.small_stripes.magenta": "Bazered Standing Streaks", "block.minecraft.banner.small_stripes.orange": "<PERSON>red Standing Streaks", "block.minecraft.banner.small_stripes.pink": "Light Red Standing Streaks", "block.minecraft.banner.small_stripes.purple": "Baze Standing Streaks", "block.minecraft.banner.small_stripes.red": "Red Standing Streaks", "block.minecraft.banner.small_stripes.white": "White Standing Streaks", "block.minecraft.banner.small_stripes.yellow": "Yellow Standing Streaks", "block.minecraft.banner.square_bottom_left.black": "Black Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.blue": "Hewn Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.brown": "<PERSON> Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.cyan": "Hewngreen Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.gray": "<PERSON> Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.green": "Green Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.light_blue": "Light Hewn Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON> Gray Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.lime": "Light Green Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.magenta": "Bazered Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON> Right Hirn", "block.minecraft.banner.square_bottom_left.pink": "Light Red Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.purple": "<PERSON>ze Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.red": "Red Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.white": "White Bottom Right Hirn", "block.minecraft.banner.square_bottom_left.yellow": "Yellow Bottom Right Hirn", "block.minecraft.banner.square_bottom_right.black": "<PERSON> Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.blue": "He<PERSON> Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.brown": "<PERSON> Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.cyan": "Hewngreen Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.gray": "<PERSON> Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.green": "Green Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.light_blue": "Light Hewn Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON> Gray Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.lime": "Light Green Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.magenta": "Bazered Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON> Left Hirn", "block.minecraft.banner.square_bottom_right.pink": "Light Red Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.purple": "<PERSON>ze <PERSON> Left Hirn", "block.minecraft.banner.square_bottom_right.red": "Red Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.white": "White Bottom Left Hirn", "block.minecraft.banner.square_bottom_right.yellow": "Yellow Bottom Left Hirn", "block.minecraft.banner.square_top_left.black": "Black Top Right Hirn", "block.minecraft.banner.square_top_left.blue": "Hewn Top Right Hirn", "block.minecraft.banner.square_top_left.brown": "<PERSON> Top Right Hirn", "block.minecraft.banner.square_top_left.cyan": "Hewngreen Top Right Hirn", "block.minecraft.banner.square_top_left.gray": "<PERSON> Top Right Hirn", "block.minecraft.banner.square_top_left.green": "Green Top Right Hirn", "block.minecraft.banner.square_top_left.light_blue": "Light Hewn Top Right Hirn", "block.minecraft.banner.square_top_left.light_gray": "<PERSON> Gray Top Right Hirn", "block.minecraft.banner.square_top_left.lime": "Light Green Top Right Hirn", "block.minecraft.banner.square_top_left.magenta": "Bazered Top Right Hirn", "block.minecraft.banner.square_top_left.orange": "<PERSON><PERSON> Top Right Hirn", "block.minecraft.banner.square_top_left.pink": "Light Red Top Right Hirn", "block.minecraft.banner.square_top_left.purple": "Baze Top Right Hirn", "block.minecraft.banner.square_top_left.red": "Red Top Right Hirn", "block.minecraft.banner.square_top_left.white": "White Top Right Hirn", "block.minecraft.banner.square_top_left.yellow": "Yellow Top Right Hirn", "block.minecraft.banner.square_top_right.black": "Black Top Left Hirn", "block.minecraft.banner.square_top_right.blue": "Hewn Top Left Hirn", "block.minecraft.banner.square_top_right.brown": "<PERSON> Top Left Hirn", "block.minecraft.banner.square_top_right.cyan": "Hewngreen Top Left Hirn", "block.minecraft.banner.square_top_right.gray": "<PERSON> Left Hirn", "block.minecraft.banner.square_top_right.green": "Green Top Left Hirn", "block.minecraft.banner.square_top_right.light_blue": "Light Hewn Top Left Hirn", "block.minecraft.banner.square_top_right.light_gray": "<PERSON> Gray Top Left Hirn", "block.minecraft.banner.square_top_right.lime": "Light Green Top Left Hirn", "block.minecraft.banner.square_top_right.magenta": "Ba<PERSON>ed Top Left Hirn", "block.minecraft.banner.square_top_right.orange": "<PERSON><PERSON> Hirn", "block.minecraft.banner.square_top_right.pink": "Light Red Top Left Hirn", "block.minecraft.banner.square_top_right.purple": "Baze Top Left Hirn", "block.minecraft.banner.square_top_right.red": "Red Top Left Hirn", "block.minecraft.banner.square_top_right.white": "White Top Left Hirn", "block.minecraft.banner.square_top_right.yellow": "Yellow Top Left Hirn", "block.minecraft.banner.straight_cross.black": "Black Rood", "block.minecraft.banner.straight_cross.blue": "Hewn Rood", "block.minecraft.banner.straight_cross.brown": "<PERSON> Rood", "block.minecraft.banner.straight_cross.cyan": "Hewngreen Rood", "block.minecraft.banner.straight_cross.gray": "<PERSON>", "block.minecraft.banner.straight_cross.green": "Green Rood", "block.minecraft.banner.straight_cross.light_blue": "Light Hewn Rood", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> Gray Rood", "block.minecraft.banner.straight_cross.lime": "Light Green Rood", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON> Rood", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "Light Red Rood", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "Red Rood", "block.minecraft.banner.straight_cross.white": "White Rood", "block.minecraft.banner.straight_cross.yellow": "Yellow Rood", "block.minecraft.banner.stripe_bottom.black": "Black Bottom", "block.minecraft.banner.stripe_bottom.blue": "Hewn Bottom", "block.minecraft.banner.stripe_bottom.brown": "<PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Hewngreen Bottom", "block.minecraft.banner.stripe_bottom.gray": "<PERSON>", "block.minecraft.banner.stripe_bottom.green": "Green Bottom", "block.minecraft.banner.stripe_bottom.light_blue": "Light Hewn Bottom", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON> Gray Bottom", "block.minecraft.banner.stripe_bottom.lime": "Light Green Bottom", "block.minecraft.banner.stripe_bottom.magenta": "Bazered Bottom", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "Light Red Bottom", "block.minecraft.banner.stripe_bottom.purple": "Baze Bottom", "block.minecraft.banner.stripe_bottom.red": "Red Bottom", "block.minecraft.banner.stripe_bottom.white": "White Bottom", "block.minecraft.banner.stripe_bottom.yellow": "Yellow Bottom", "block.minecraft.banner.stripe_center.black": "Black Thin Streak", "block.minecraft.banner.stripe_center.blue": "Hewn Thin Streak", "block.minecraft.banner.stripe_center.brown": "Brown Thin Streak", "block.minecraft.banner.stripe_center.cyan": "Hewngreen Thin Streak", "block.minecraft.banner.stripe_center.gray": "Gray Thin Streak", "block.minecraft.banner.stripe_center.green": "Green Thin Streak", "block.minecraft.banner.stripe_center.light_blue": "Light Hewn Thin Streak", "block.minecraft.banner.stripe_center.light_gray": "Light Gray Thin Streak", "block.minecraft.banner.stripe_center.lime": "Light Green Thin Streak", "block.minecraft.banner.stripe_center.magenta": "Bazered Thin Streak", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "Light Red Thin Streak", "block.minecraft.banner.stripe_center.purple": "Baze Thin Streak", "block.minecraft.banner.stripe_center.red": "Red Thin Streak", "block.minecraft.banner.stripe_center.white": "White Thin Streak", "block.minecraft.banner.stripe_center.yellow": "Yellow Thin Streak", "block.minecraft.banner.stripe_downleft.black": "Black Left Bend", "block.minecraft.banner.stripe_downleft.blue": "Hewn Left Bend", "block.minecraft.banner.stripe_downleft.brown": "<PERSON> Left Bend", "block.minecraft.banner.stripe_downleft.cyan": "Hewngreen Left Bend", "block.minecraft.banner.stripe_downleft.gray": "<PERSON> Left Bend", "block.minecraft.banner.stripe_downleft.green": "Green Left Bend", "block.minecraft.banner.stripe_downleft.light_blue": "Light Hewn Left Bend", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON> Left Bend", "block.minecraft.banner.stripe_downleft.lime": "Light Green Left Bend", "block.minecraft.banner.stripe_downleft.magenta": "Bazered Left Bend", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.pink": "Light Red Left Bend", "block.minecraft.banner.stripe_downleft.purple": "Baze Left Bend", "block.minecraft.banner.stripe_downleft.red": "Red Left Bend", "block.minecraft.banner.stripe_downleft.white": "White Left Bend", "block.minecraft.banner.stripe_downleft.yellow": "Yellow Left Bend", "block.minecraft.banner.stripe_downright.black": "Black Bend", "block.minecraft.banner.stripe_downright.blue": "Hewn Bend", "block.minecraft.banner.stripe_downright.brown": "Brown Bend", "block.minecraft.banner.stripe_downright.cyan": "Hewngreen Bend", "block.minecraft.banner.stripe_downright.gray": "Gray Bend", "block.minecraft.banner.stripe_downright.green": "Green Bend", "block.minecraft.banner.stripe_downright.light_blue": "Light Hewn Bend", "block.minecraft.banner.stripe_downright.light_gray": "Light Gray Bend", "block.minecraft.banner.stripe_downright.lime": "Light Green Bend", "block.minecraft.banner.stripe_downright.magenta": "Bazered Bend", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.pink": "Light Red Bend", "block.minecraft.banner.stripe_downright.purple": "Baze Bend", "block.minecraft.banner.stripe_downright.red": "Red Bend", "block.minecraft.banner.stripe_downright.white": "White Bend", "block.minecraft.banner.stripe_downright.yellow": "Yellow Bend", "block.minecraft.banner.stripe_left.black": "Black Left Thin Streak", "block.minecraft.banner.stripe_left.blue": "Hewn Left Thin Streak", "block.minecraft.banner.stripe_left.brown": "<PERSON> Left Thin Streak", "block.minecraft.banner.stripe_left.cyan": "Hewngreen Left Thin Streak", "block.minecraft.banner.stripe_left.gray": "<PERSON> Left Thin Streak", "block.minecraft.banner.stripe_left.green": "Green Left Thin Streak", "block.minecraft.banner.stripe_left.light_blue": "Light Hewn Left Thin Streak", "block.minecraft.banner.stripe_left.light_gray": "<PERSON> Left Thin Streak", "block.minecraft.banner.stripe_left.lime": "Light Green Left Thin Streak", "block.minecraft.banner.stripe_left.magenta": "Bazered Left Thin Streak", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON> Left Thin Streak", "block.minecraft.banner.stripe_left.pink": "Light Red Left Thin Streak", "block.minecraft.banner.stripe_left.purple": "<PERSON>ze Left Thin Streak", "block.minecraft.banner.stripe_left.red": "Red Left Thin Streak", "block.minecraft.banner.stripe_left.white": "White Left Thin Streak", "block.minecraft.banner.stripe_left.yellow": "Yellow Left Thin Streak", "block.minecraft.banner.stripe_middle.black": "Black Middle Streak", "block.minecraft.banner.stripe_middle.blue": "Hewn Middle Streak", "block.minecraft.banner.stripe_middle.brown": "Brown Middle Streak", "block.minecraft.banner.stripe_middle.cyan": "Hewngreen Middle Streak", "block.minecraft.banner.stripe_middle.gray": "Gray Middle Streak", "block.minecraft.banner.stripe_middle.green": "Green Middle Streak", "block.minecraft.banner.stripe_middle.light_blue": "Light Hewn Middle Streak", "block.minecraft.banner.stripe_middle.light_gray": "Light Gray Middle Streak", "block.minecraft.banner.stripe_middle.lime": "Light Green Middle Streak", "block.minecraft.banner.stripe_middle.magenta": "Bazered Middle Streak", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON> Middle Streak", "block.minecraft.banner.stripe_middle.pink": "Light Red Middle Streak", "block.minecraft.banner.stripe_middle.purple": "Baze Middle Streak", "block.minecraft.banner.stripe_middle.red": "Red Middle Streak", "block.minecraft.banner.stripe_middle.white": "White Middle Streak", "block.minecraft.banner.stripe_middle.yellow": "Yellow Middle Streak", "block.minecraft.banner.stripe_right.black": "Black Right Thin Streak", "block.minecraft.banner.stripe_right.blue": "Hewn Right Thin Streak", "block.minecraft.banner.stripe_right.brown": "<PERSON> Right Thin Streak", "block.minecraft.banner.stripe_right.cyan": "Hewngreen Right Thin Streak", "block.minecraft.banner.stripe_right.gray": "<PERSON> Right Thin Streak", "block.minecraft.banner.stripe_right.green": "Green Right Thin Streak", "block.minecraft.banner.stripe_right.light_blue": "Light Hewn Right Thin Streak", "block.minecraft.banner.stripe_right.light_gray": "<PERSON> Gray Right Thin Streak", "block.minecraft.banner.stripe_right.lime": "Light Green Right Thin Streak", "block.minecraft.banner.stripe_right.magenta": "Bazered Right Thin Streak", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON> Right Thin Streak", "block.minecraft.banner.stripe_right.pink": "Light Red Right Thin Streak", "block.minecraft.banner.stripe_right.purple": "Baze Right Thin Streak", "block.minecraft.banner.stripe_right.red": "Red Right Thin Streak", "block.minecraft.banner.stripe_right.white": "White Right Thin Streak", "block.minecraft.banner.stripe_right.yellow": "Yellow Right Thin Streak", "block.minecraft.banner.stripe_top.black": "Black Top", "block.minecraft.banner.stripe_top.blue": "Hewn Top", "block.minecraft.banner.stripe_top.brown": "<PERSON>", "block.minecraft.banner.stripe_top.cyan": "Hewngreen Top", "block.minecraft.banner.stripe_top.gray": "<PERSON>", "block.minecraft.banner.stripe_top.green": "Green Top", "block.minecraft.banner.stripe_top.light_blue": "Light Hewn Top", "block.minecraft.banner.stripe_top.light_gray": "Light Gray Top", "block.minecraft.banner.stripe_top.lime": "Light Green Top", "block.minecraft.banner.stripe_top.magenta": "Bazered Top", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "Light Red Top", "block.minecraft.banner.stripe_top.purple": "Baze Top", "block.minecraft.banner.stripe_top.red": "Red Top", "block.minecraft.banner.stripe_top.white": "White Top", "block.minecraft.banner.stripe_top.yellow": "Yellow Top", "block.minecraft.banner.triangle_bottom.black": "<PERSON> Threehirn", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON>n", "block.minecraft.banner.triangle_bottom.brown": "<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Hewngreen Threehirn", "block.minecraft.banner.triangle_bottom.gray": "<PERSON>", "block.minecraft.banner.triangle_bottom.green": "Green Threehirn", "block.minecraft.banner.triangle_bottom.light_blue": "Light Hewn Threehirn", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON> Gray <PERSON>hirn", "block.minecraft.banner.triangle_bottom.lime": "Light Green Threehirn", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>hirn", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "Light Red Threehirn", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON> Threehirn", "block.minecraft.banner.triangle_bottom.white": "White Threehirn", "block.minecraft.banner.triangle_bottom.yellow": "Yellow Threehirn", "block.minecraft.banner.triangle_top.black": "Black Upsodown Threehirn", "block.minecraft.banner.triangle_top.blue": "Hewn Upsodown Threehirn", "block.minecraft.banner.triangle_top.brown": "<PERSON>down Threehirn", "block.minecraft.banner.triangle_top.cyan": "Hewngreen Upsodown Threehirn", "block.minecraft.banner.triangle_top.gray": "<PERSON>down Threehirn", "block.minecraft.banner.triangle_top.green": "Green Upsodown Threehirn", "block.minecraft.banner.triangle_top.light_blue": "Light Hewn Upsodown Threehirn", "block.minecraft.banner.triangle_top.light_gray": "Light Gray Upsodown Threehirn", "block.minecraft.banner.triangle_top.lime": "Light Green Upsodown Threehirn", "block.minecraft.banner.triangle_top.magenta": "Bazered Upsodown Threehirn", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON> Threehirn", "block.minecraft.banner.triangle_top.pink": "Light Red Upsodown Threehirn", "block.minecraft.banner.triangle_top.purple": "Baze Upsodown Threehirn", "block.minecraft.banner.triangle_top.red": "Red Upsodown Threehirn", "block.minecraft.banner.triangle_top.white": "White Upsodown Threehirn", "block.minecraft.banner.triangle_top.yellow": "Yellow Upsodown Threehirn", "block.minecraft.banner.triangles_bottom.black": "Black Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.blue": "Hewn Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.brown": "<PERSON> Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.cyan": "Hewngreen Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.gray": "<PERSON> Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.green": "Green Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.light_blue": "Light Hewn Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON> Gray Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.lime": "Light Green Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.magenta": "Bazered Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.pink": "Light Red Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON> Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.red": "Red Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.white": "White Sawtoothed Bottom", "block.minecraft.banner.triangles_bottom.yellow": "Yellow Sawtoothed Bottom", "block.minecraft.banner.triangles_top.black": "Black Sawtoothed Top", "block.minecraft.banner.triangles_top.blue": "Hewn Sawtoothed Top", "block.minecraft.banner.triangles_top.brown": "<PERSON> Sawtoothed Top", "block.minecraft.banner.triangles_top.cyan": "Hewngreen Sawtoothed Top", "block.minecraft.banner.triangles_top.gray": "<PERSON> Sawtoothed Top", "block.minecraft.banner.triangles_top.green": "Green Sawtoothed Top", "block.minecraft.banner.triangles_top.light_blue": "Light Hewn Sawtoothed Top", "block.minecraft.banner.triangles_top.light_gray": "<PERSON> Gray Sawtoothed Top", "block.minecraft.banner.triangles_top.lime": "Light Green Sawtoothed Top", "block.minecraft.banner.triangles_top.magenta": "Bazered Sawtoothed Top", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.pink": "Light Red Sawtoothed Top", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> Sawtoothed Top", "block.minecraft.banner.triangles_top.red": "Red Sawtoothed Top", "block.minecraft.banner.triangles_top.white": "White Sawtoothed Top", "block.minecraft.banner.triangles_top.yellow": "Yellow Sawtoothed Top", "block.minecraft.barrel": "Vat", "block.minecraft.barrier": "<PERSON>nderer", "block.minecraft.basalt": "Rinestone", "block.minecraft.beacon": "Beacon", "block.minecraft.beacon.primary": "Main Might", "block.minecraft.beacon.secondary": "Other Might", "block.minecraft.bed.no_sleep": "You can sleep only at night and in thunderstorms", "block.minecraft.bed.not_safe": "You may not rest now; there are fiends nearby", "block.minecraft.bed.obstructed": "This bed is hindered", "block.minecraft.bed.occupied": "This bed is nimmed", "block.minecraft.bed.too_far_away": "You may not rest now; the bed is too far away", "block.minecraft.bedrock": "Bedstone", "block.minecraft.bee_nest": "Bee Nest", "block.minecraft.beehive": "Beehive", "block.minecraft.beetroots": "Red Mores", "block.minecraft.bell": "Bell", "block.minecraft.big_dripleaf": "Great Dripleaf", "block.minecraft.big_dripleaf_stem": "Great Dripleaf Stem", "block.minecraft.birch_button": "<PERSON>", "block.minecraft.birch_door": "<PERSON>", "block.minecraft.birch_fence": "<PERSON>", "block.minecraft.birch_fence_gate": "Birch Edder Gate", "block.minecraft.birch_hanging_sign": "<PERSON> Hanging Token", "block.minecraft.birch_leaves": "Birch Leaves", "block.minecraft.birch_log": "Birch Stock", "block.minecraft.birch_planks": "Birch Boards", "block.minecraft.birch_pressure_plate": "<PERSON> T<PERSON>ch Tile", "block.minecraft.birch_sapling": "Birch Sprout", "block.minecraft.birch_sign": "<PERSON>", "block.minecraft.birch_slab": "<PERSON>", "block.minecraft.birch_stairs": "<PERSON> Stairs", "block.minecraft.birch_trapdoor": "<PERSON>", "block.minecraft.birch_wall_hanging_sign": "Birch Side Hanging Token", "block.minecraft.birch_wall_sign": "Birch Side Token", "block.minecraft.birch_wood": "Birch Wood", "block.minecraft.black_banner": "Black Streamer", "block.minecraft.black_bed": "Black Bed", "block.minecraft.black_candle": "Black Waxlight", "block.minecraft.black_candle_cake": "Kitch with <PERSON> Waxlight", "block.minecraft.black_carpet": "Black Stepcloth", "block.minecraft.black_concrete": "Black Stonelime", "block.minecraft.black_concrete_powder": "Black Stonelime Dust", "block.minecraft.black_glazed_terracotta": "Black Glazed Bakedclay", "block.minecraft.black_shulker_box": "Black Shulker Box", "block.minecraft.black_stained_glass": "Black Hued Glass", "block.minecraft.black_stained_glass_pane": "Black Hued Glass Sheet", "block.minecraft.black_terracotta": "<PERSON> Bakedclay", "block.minecraft.black_wool": "Black Wool", "block.minecraft.blackstone": "Blackstone", "block.minecraft.blackstone_slab": "Blackstone Halfclot", "block.minecraft.blackstone_stairs": "Blackstone Stairs", "block.minecraft.blackstone_wall": "Blackstone Wough", "block.minecraft.blast_furnace": "Blast Oven", "block.minecraft.blue_banner": "Hewn Streamer", "block.minecraft.blue_bed": "Hewn Bed", "block.minecraft.blue_candle": "Hewn Waxlight", "block.minecraft.blue_candle_cake": "Kitch with <PERSON><PERSON> Waxlight", "block.minecraft.blue_carpet": "Hewn Stepcloth", "block.minecraft.blue_concrete": "Hewn Stonelime", "block.minecraft.blue_concrete_powder": "Hewn Stonelime Dust", "block.minecraft.blue_glazed_terracotta": "Hewn Glazed <PERSON>", "block.minecraft.blue_ice": "Hewn Ice", "block.minecraft.blue_orchid": "Hewn Ballockwort", "block.minecraft.blue_shulker_box": "Hewn Shulker Box", "block.minecraft.blue_stained_glass": "Hewn <PERSON>ed <PERSON>", "block.minecraft.blue_stained_glass_pane": "Hewn Hued Glass Sheet", "block.minecraft.blue_terracotta": "<PERSON><PERSON>", "block.minecraft.blue_wool": "Hewn Wool", "block.minecraft.bone_block": "Bone Clot", "block.minecraft.bookshelf": "Bookshelf", "block.minecraft.brain_coral": "<PERSON>", "block.minecraft.brain_coral_block": "<PERSON> B<PERSON>", "block.minecraft.brain_coral_fan": "<PERSON> B<PERSON>er Blower", "block.minecraft.brain_coral_wall_fan": "<PERSON> Blossomdeer Side Blower", "block.minecraft.brewing_stand": "Brewing Stand", "block.minecraft.brick_slab": "Tile <PERSON>", "block.minecraft.brick_stairs": "Tile Stairs", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "Tiles", "block.minecraft.brown_banner": "<PERSON>er", "block.minecraft.brown_bed": "Brown Bed", "block.minecraft.brown_candle": "<PERSON>", "block.minecraft.brown_candle_cake": "<PERSON>ch with <PERSON>", "block.minecraft.brown_carpet": "<PERSON>", "block.minecraft.brown_concrete": "<PERSON> Stone<PERSON>", "block.minecraft.brown_concrete_powder": "Brown Stonelime Dust", "block.minecraft.brown_glazed_terracotta": "<PERSON> Glazed <PERSON>", "block.minecraft.brown_mushroom": "Brown Toadstool", "block.minecraft.brown_mushroom_block": "Brown Toadstool Clot", "block.minecraft.brown_shulker_box": "<PERSON> Shulker Box", "block.minecraft.brown_stained_glass": "<PERSON> Hued <PERSON>", "block.minecraft.brown_stained_glass_pane": "Brown Hued Glass Sheet", "block.minecraft.brown_terracotta": "<PERSON>", "block.minecraft.brown_wool": "Brown Wool", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON> Blossom<PERSON><PERSON>", "block.minecraft.bubble_coral_fan": "B<PERSON><PERSON> Blossom<PERSON><PERSON> Blower", "block.minecraft.bubble_coral_wall_fan": "B<PERSON>ble Blossomdeer Side Blower", "block.minecraft.budding_amethyst": "Sprouting Drunklack", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON>", "block.minecraft.cactus_flower": "Thorntree Blossom", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Chalkshaft", "block.minecraft.calibrated_sculk_sensor": "<PERSON>y <PERSON>", "block.minecraft.campfire": "Haltfire", "block.minecraft.candle": "Waxlight", "block.minecraft.candle_cake": "Kitch with <PERSON><PERSON><PERSON>", "block.minecraft.carrots": "Walmores", "block.minecraft.cartography_table": "Drafting Bench", "block.minecraft.carved_pumpkin": "<PERSON><PERSON>", "block.minecraft.cauldron": "Ironcrock", "block.minecraft.cave_air": "Hollow Lift", "block.minecraft.cave_vines": "Hollow Hanggrass", "block.minecraft.cave_vines_plant": "Hollow Hanggrass Wort", "block.minecraft.chain": "Rackent", "block.minecraft.chain_command_block": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_button": "Stoneberry Knap", "block.minecraft.cherry_door": "Stoneberry Door", "block.minecraft.cherry_fence": "Stoneberry Edder", "block.minecraft.cherry_fence_gate": "Stoneberry Edder Gate", "block.minecraft.cherry_hanging_sign": "Stoneberry Hanging Token", "block.minecraft.cherry_leaves": "Stoneberry Leaves", "block.minecraft.cherry_log": "Stoneberry Stock", "block.minecraft.cherry_planks": "Stoneberry Boards", "block.minecraft.cherry_pressure_plate": "Stoneberry Thrutch Tile", "block.minecraft.cherry_sapling": "Stoneberry Sprout", "block.minecraft.cherry_sign": "Stoneberry Token", "block.minecraft.cherry_slab": "Stoneberry Halfclot", "block.minecraft.cherry_stairs": "Stoneberry Stairs", "block.minecraft.cherry_trapdoor": "Stoneberry Trapdoor", "block.minecraft.cherry_wall_hanging_sign": "Stoneberry Side Hanging Token", "block.minecraft.cherry_wall_sign": "Stoneberry Side Token", "block.minecraft.cherry_wood": "Stoneberry Wood", "block.minecraft.chest": "Chest", "block.minecraft.chipped_anvil": "Chipped Anvil", "block.minecraft.chiseled_bookshelf": "<PERSON>n <PERSON>helf", "block.minecraft.chiseled_copper": "Graven Are", "block.minecraft.chiseled_deepslate": "Graven Deepstone", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON>", "block.minecraft.chiseled_polished_blackstone": "Graven Sliked <PERSON>", "block.minecraft.chiseled_quartz_block": "Graven Hardstone Clot", "block.minecraft.chiseled_red_sandstone": "Graven <PERSON>", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON>", "block.minecraft.chiseled_sandstone": "Graven <PERSON>", "block.minecraft.chiseled_stone_bricks": "<PERSON>n <PERSON>", "block.minecraft.chiseled_tuff": "Graven <PERSON>stone", "block.minecraft.chiseled_tuff_bricks": "Graven Ashstone Woughstones", "block.minecraft.chorus_flower": "Glee Blossom", "block.minecraft.chorus_plant": "<PERSON><PERSON>", "block.minecraft.clay": "<PERSON>", "block.minecraft.closed_eyeblossom": "Shut Eyeblossom", "block.minecraft.coal_block": "Coal Clot", "block.minecraft.coal_ore": "Coal Ore", "block.minecraft.coarse_dirt": "Rough Earth", "block.minecraft.cobbled_deepslate": "Cobbled Deepstone", "block.minecraft.cobbled_deepslate_slab": "Cobbled Deepstone Halfclot", "block.minecraft.cobbled_deepslate_stairs": "Cobbled Deepstone Stairs", "block.minecraft.cobbled_deepslate_wall": "Cobbled Deepstone Wough", "block.minecraft.cobblestone": "Cobblestone", "block.minecraft.cobblestone_slab": "Cobblestone Halfclot", "block.minecraft.cobblestone_stairs": "Cobblestone Stairs", "block.minecraft.cobblestone_wall": "Cobblestone Wough", "block.minecraft.cobweb": "Cobweb", "block.minecraft.cocoa": "Muckovet", "block.minecraft.command_block": "<PERSON><PERSON>", "block.minecraft.comparator": "Redstone Bemeter", "block.minecraft.composter": "Bonemealer", "block.minecraft.conduit": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Are Clot", "block.minecraft.copper_bulb": "Are Lightvat", "block.minecraft.copper_door": "Are Door", "block.minecraft.copper_grate": "<PERSON>rdle", "block.minecraft.copper_ore": "Are Ore", "block.minecraft.copper_trapdoor": "Are Trapdoor", "block.minecraft.cornflower": "Corn<PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Cracked Deepstone Woughstones", "block.minecraft.cracked_deepslate_tiles": "Cracked Deepstone Tiles", "block.minecraft.cracked_nether_bricks": "Cracked <PERSON><PERSON> Tiles", "block.minecraft.cracked_polished_blackstone_bricks": "Cracked Sliked Blackstone Woughstones", "block.minecraft.cracked_stone_bricks": "Cracked Woughstones", "block.minecraft.crafter": "Crafter", "block.minecraft.crafting_table": "Workbench", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "Creeper Head", "block.minecraft.creeper_wall_head": "Creeper Side Head", "block.minecraft.crimson_button": "<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON>s", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON> Hanging Token", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON>", "block.minecraft.crying_obsidian": "Weeping <PERSON><PERSON><PERSON>", "block.minecraft.cut_copper": "Snithed Are", "block.minecraft.cut_copper_slab": "Snithed Are Halfclot", "block.minecraft.cut_copper_stairs": "Snithed Are Stairs", "block.minecraft.cut_red_sandstone": "Snithed Red Sandstone", "block.minecraft.cut_red_sandstone_slab": "Snithed Red Sandstone Halfclot", "block.minecraft.cut_sandstone": "Snithed Sandstone", "block.minecraft.cut_sandstone_slab": "Snithed Sandstone Halfclot", "block.minecraft.cyan_banner": "Hewngreen Streamer", "block.minecraft.cyan_bed": "Hewngreen Bed", "block.minecraft.cyan_candle": "Hewngreen Waxlight", "block.minecraft.cyan_candle_cake": "Kitch with <PERSON><PERSON><PERSON> Waxlight", "block.minecraft.cyan_carpet": "Hewngreen Stepcloth", "block.minecraft.cyan_concrete": "Hewngreen Stonelime", "block.minecraft.cyan_concrete_powder": "Hewngreen Stonelime Dust", "block.minecraft.cyan_glazed_terracotta": "Hewngreen Glazed Ba<PERSON>lay", "block.minecraft.cyan_shulker_box": "Hewngreen Shulker Box", "block.minecraft.cyan_stained_glass": "Hewngreen Hued Glass", "block.minecraft.cyan_stained_glass_pane": "Hewngreen Hued Glass Sheet", "block.minecraft.cyan_terracotta": "Hewngreen Bakedclay", "block.minecraft.cyan_wool": "Hewngreen Wool", "block.minecraft.damaged_anvil": "<PERSON>orn Anvil", "block.minecraft.dandelion": "Eywort", "block.minecraft.dark_oak_button": "Dark Oak Knap", "block.minecraft.dark_oak_door": "Dark Oak Door", "block.minecraft.dark_oak_fence": "Dark Oak Edder", "block.minecraft.dark_oak_fence_gate": "Dark Oak Edder Gate", "block.minecraft.dark_oak_hanging_sign": "Dark Oak Hanging Token", "block.minecraft.dark_oak_leaves": "Dark Oak Leaves", "block.minecraft.dark_oak_log": "Dark Oak Stock", "block.minecraft.dark_oak_planks": "Dark Oak Boards", "block.minecraft.dark_oak_pressure_plate": "Dark Oak Thrutch Tile", "block.minecraft.dark_oak_sapling": "Dark Oak Sprout", "block.minecraft.dark_oak_sign": "Dark Oak Token", "block.minecraft.dark_oak_slab": "Dark Oak Halfclot", "block.minecraft.dark_oak_stairs": "Dark Oak Stairs", "block.minecraft.dark_oak_trapdoor": "Dark Oak Trapdoor", "block.minecraft.dark_oak_wall_hanging_sign": "Dark Oak Side Hanging Token", "block.minecraft.dark_oak_wall_sign": "Dark Oak Side Token", "block.minecraft.dark_oak_wood": "Dark Oak Wood", "block.minecraft.dark_prismarine": "<PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON> P<PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.daylight_detector": "Daylight Acknower", "block.minecraft.dead_brain_coral": "Dead Brain Blossomdeer", "block.minecraft.dead_brain_coral_block": "Dead Brain Blossom<PERSON><PERSON>", "block.minecraft.dead_brain_coral_fan": "Dead Brain Blossom<PERSON>er Blower", "block.minecraft.dead_brain_coral_wall_fan": "Dead Brain Blossomdeer Side Blower", "block.minecraft.dead_bubble_coral": "Dead Bubble Blossomdeer", "block.minecraft.dead_bubble_coral_block": "Dead Bubble Blossom<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "Dead Bubble Blossom<PERSON>er Blower", "block.minecraft.dead_bubble_coral_wall_fan": "Dead Bubble Blossomdeer Side Blower", "block.minecraft.dead_bush": "Dead Bush", "block.minecraft.dead_fire_coral": "Dead Fire Blossomdeer", "block.minecraft.dead_fire_coral_block": "Dead Fire Blossom<PERSON><PERSON>", "block.minecraft.dead_fire_coral_fan": "Dead Fire Blossomdeer Blower", "block.minecraft.dead_fire_coral_wall_fan": "Dead Fire Blossomdeer Side Blower", "block.minecraft.dead_horn_coral": "<PERSON> Horn Blossomdeer", "block.minecraft.dead_horn_coral_block": "<PERSON> Horn Blossom<PERSON><PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON> Horn Blossomdeer Blower", "block.minecraft.dead_horn_coral_wall_fan": "Dead Horn Blossomdeer Side Blower", "block.minecraft.dead_tube_coral": "<PERSON> Reed Blossomdeer", "block.minecraft.dead_tube_coral_block": "<PERSON> Reed Blossom<PERSON><PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON> Reed Blossomdeer Blower", "block.minecraft.dead_tube_coral_wall_fan": "Dead Reed Blossomdeer Side Blower", "block.minecraft.decorated_pot": "Bedecked Pot", "block.minecraft.deepslate": "Deepstone", "block.minecraft.deepslate_brick_slab": "Deepstone Woughstone Halfclot", "block.minecraft.deepslate_brick_stairs": "Deepstone Woughstone Stairs", "block.minecraft.deepslate_brick_wall": "Deepstone Woughstone Wough", "block.minecraft.deepslate_bricks": "Deepstone Woughstones", "block.minecraft.deepslate_coal_ore": "Deepstone Coal Ore", "block.minecraft.deepslate_copper_ore": "Deepstone Are Ore", "block.minecraft.deepslate_diamond_ore": "Deepstone Hardhirst Ore", "block.minecraft.deepslate_emerald_ore": "Deepstone Greenhirst Ore", "block.minecraft.deepslate_gold_ore": "Deepstone Gold Ore", "block.minecraft.deepslate_iron_ore": "Deepstone Iron Ore", "block.minecraft.deepslate_lapis_ore": "Deepstone Hewnstone Ore", "block.minecraft.deepslate_redstone_ore": "Deepstone Redstone Ore", "block.minecraft.deepslate_tile_slab": "Deepstone Tile Halfclot", "block.minecraft.deepslate_tile_stairs": "Deepstone Tile Stairs", "block.minecraft.deepslate_tile_wall": "Deepstone Tile Wough", "block.minecraft.deepslate_tiles": "Deepstone Tiles", "block.minecraft.detector_rail": "Acknower Ironroad", "block.minecraft.diamond_block": "Hardhirst Clot", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON> Ore", "block.minecraft.diorite": "Shedstone", "block.minecraft.diorite_slab": "Shedstone Halfclot", "block.minecraft.diorite_stairs": "Shedstone Stairs", "block.minecraft.diorite_wall": "Shedstone Wough", "block.minecraft.dirt": "Earth", "block.minecraft.dirt_path": "Earth Path", "block.minecraft.dispenser": "<PERSON>hrow<PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON> <PERSON>", "block.minecraft.dragon_wall_head": "Worm Side Head", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Dried Ash Seaweed Clot", "block.minecraft.dripstone_block": "Dripstone Clot", "block.minecraft.dropper": "Dropper", "block.minecraft.emerald_block": "Greenhirst Clot", "block.minecraft.emerald_ore": "Greenhirst Ore", "block.minecraft.enchanting_table": "Galdercraft Bench", "block.minecraft.end_gateway": "End Gateway", "block.minecraft.end_portal": "End Ingang", "block.minecraft.end_portal_frame": "<PERSON> Ingang <PERSON>", "block.minecraft.end_rod": "End Rod", "block.minecraft.end_stone": "End Stone", "block.minecraft.end_stone_brick_slab": "End Woughstone Halfclot", "block.minecraft.end_stone_brick_stairs": "End Woughstone Stairs", "block.minecraft.end_stone_brick_wall": "End Woughstone Wough", "block.minecraft.end_stone_bricks": "End Woughstones", "block.minecraft.ender_chest": "<PERSON><PERSON> Chest", "block.minecraft.exposed_chiseled_copper": "Unwried Graven <PERSON>", "block.minecraft.exposed_copper": "Unwried Are", "block.minecraft.exposed_copper_bulb": "Unwried Are Lightvat", "block.minecraft.exposed_copper_door": "Unwried Are Door", "block.minecraft.exposed_copper_grate": "Unwried Are Hurdle", "block.minecraft.exposed_copper_trapdoor": "Unwried Are Trapdoor", "block.minecraft.exposed_cut_copper": "Unwried Snithed Are", "block.minecraft.exposed_cut_copper_slab": "Unwried Snithed Are Halfclot", "block.minecraft.exposed_cut_copper_stairs": "Unwried Snithed Are Stairs", "block.minecraft.farmland": "Cropland", "block.minecraft.fern": "Fern", "block.minecraft.fire": "Fire", "block.minecraft.fire_coral": "Fire Blossomdeer", "block.minecraft.fire_coral_block": "Fire Blossomdeer <PERSON>", "block.minecraft.fire_coral_fan": "Fire Blossomdeer Blower", "block.minecraft.fire_coral_wall_fan": "Fire Blossomdeer Side Blower", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "Arrowmaking Bench", "block.minecraft.flower_pot": "Blossom Pot", "block.minecraft.flowering_azalea": "Blossoming Drywort", "block.minecraft.flowering_azalea_leaves": "Blossoming Drywort Leaves", "block.minecraft.frogspawn": "<PERSON><PERSON>", "block.minecraft.frosted_ice": "Frosted Ice", "block.minecraft.furnace": "Oven", "block.minecraft.gilded_blackstone": "Gilded Blackstone", "block.minecraft.glass": "Glass", "block.minecraft.glass_pane": "Glass Sheet", "block.minecraft.glow_lichen": "Glow Raw", "block.minecraft.glowstone": "Glowstone", "block.minecraft.gold_block": "Gold Clot", "block.minecraft.gold_ore": "Gold Ore", "block.minecraft.granite": "Cornstone", "block.minecraft.granite_slab": "Cornstone Halfclot", "block.minecraft.granite_stairs": "Cornstone Stairs", "block.minecraft.granite_wall": "Cornstone Wough", "block.minecraft.grass": "Grass", "block.minecraft.grass_block": "Grass Clot", "block.minecraft.gravel": "Pebbles", "block.minecraft.gray_banner": "<PERSON>", "block.minecraft.gray_bed": "Gray Bed", "block.minecraft.gray_candle": "<PERSON>", "block.minecraft.gray_candle_cake": "Kitch with <PERSON>", "block.minecraft.gray_carpet": "<PERSON>", "block.minecraft.gray_concrete": "<PERSON>", "block.minecraft.gray_concrete_powder": "Gray Stonelime Dust", "block.minecraft.gray_glazed_terracotta": "<PERSON>lazed <PERSON>", "block.minecraft.gray_shulker_box": "<PERSON>", "block.minecraft.gray_stained_glass": "<PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON> Hued Glass Sheet", "block.minecraft.gray_terracotta": "<PERSON>", "block.minecraft.gray_wool": "Gray <PERSON>", "block.minecraft.green_banner": "Green Streamer", "block.minecraft.green_bed": "Green Bed", "block.minecraft.green_candle": "Green Waxlight", "block.minecraft.green_candle_cake": "Kitch with <PERSON> Waxlight", "block.minecraft.green_carpet": "Green Stepcloth", "block.minecraft.green_concrete": "Green Stonelime", "block.minecraft.green_concrete_powder": "Green Stonelime Dust", "block.minecraft.green_glazed_terracotta": "Green Glazed Bakedclay", "block.minecraft.green_shulker_box": "Green Shulker Box", "block.minecraft.green_stained_glass": "Green Hued Glass", "block.minecraft.green_stained_glass_pane": "Green Hued Glass Sheet", "block.minecraft.green_terracotta": "Green Bakedclay", "block.minecraft.green_wool": "Green Wool", "block.minecraft.grindstone": "Grindstone", "block.minecraft.hanging_roots": "Hanging Mores", "block.minecraft.hay_block": "<PERSON>", "block.minecraft.heavy_core": "Heavy Heart", "block.minecraft.heavy_weighted_pressure_plate": "Heavy Weighted Thrutch Tile", "block.minecraft.honey_block": "<PERSON>", "block.minecraft.honeycomb_block": "Honeycomb Clot", "block.minecraft.hopper": "<PERSON>", "block.minecraft.horn_coral": "<PERSON>", "block.minecraft.horn_coral_block": "<PERSON> Blossom<PERSON>", "block.minecraft.horn_coral_fan": "<PERSON> Blossomdeer Blower", "block.minecraft.horn_coral_wall_fan": "Horn Blossomdeer Side Blower", "block.minecraft.ice": "Ice", "block.minecraft.infested_chiseled_stone_bricks": "Incoathed Graven Woughstones", "block.minecraft.infested_cobblestone": "Incoathed Cobblestone", "block.minecraft.infested_cracked_stone_bricks": "Incoathed Cracked Woughstones", "block.minecraft.infested_deepslate": "Incoathed Deepstone", "block.minecraft.infested_mossy_stone_bricks": "Incoathed Mossy Woughstones", "block.minecraft.infested_stone": "Incoathed Stone", "block.minecraft.infested_stone_bricks": "Incoathed Woughstones", "block.minecraft.iron_bars": "Iron Rods", "block.minecraft.iron_block": "Iron Clot", "block.minecraft.iron_door": "Iron Door", "block.minecraft.iron_ore": "Iron Ore", "block.minecraft.iron_trapdoor": "Iron Trapdoor", "block.minecraft.jack_o_lantern": "Harvestovet Lightvat", "block.minecraft.jigsaw": "<PERSON>lock Clot", "block.minecraft.jukebox": "Gleebox", "block.minecraft.jungle_button": "<PERSON><PERSON>", "block.minecraft.jungle_door": "Rainwold Door", "block.minecraft.jungle_fence": "<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Rainwold Edder Gate", "block.minecraft.jungle_hanging_sign": "Rainwold Hanging Token", "block.minecraft.jungle_leaves": "Rainwold Leaves", "block.minecraft.jungle_log": "Rainwold Stock", "block.minecraft.jungle_planks": "Rainwold Boards", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON> T<PERSON>ch Tile", "block.minecraft.jungle_sapling": "Rainwold Sprout", "block.minecraft.jungle_sign": "<PERSON><PERSON>", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "Rainwold Stairs", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "Rainwold Side Hanging Token", "block.minecraft.jungle_wall_sign": "Rainwold Side Token", "block.minecraft.jungle_wood": "Rainwold Wood", "block.minecraft.kelp": "Ash Seaweed", "block.minecraft.kelp_plant": "Ash Seaweed Wort", "block.minecraft.ladder": "Ladder", "block.minecraft.lantern": "Lightvat", "block.minecraft.lapis_block": "Hewnstone Clot", "block.minecraft.lapis_ore": "Hewnstone Ore", "block.minecraft.large_amethyst_bud": "Great Drunklack Sprout", "block.minecraft.large_fern": "Tall Fern", "block.minecraft.lava": "Moltenstone", "block.minecraft.lava_cauldron": "Moltenstone Ironcrock", "block.minecraft.leaf_litter": "Fallen Leaves", "block.minecraft.lectern": "Reading Stand", "block.minecraft.lever": "Heaver", "block.minecraft.light": "Light", "block.minecraft.light_blue_banner": "Light Hewn Streamer", "block.minecraft.light_blue_bed": "Light Hewn Bed", "block.minecraft.light_blue_candle": "Light Hewn Waxlight", "block.minecraft.light_blue_candle_cake": "Kitch with Light Hewn Waxlight", "block.minecraft.light_blue_carpet": "Light Hewn Stepcloth", "block.minecraft.light_blue_concrete": "Light Hewn Stonelime", "block.minecraft.light_blue_concrete_powder": "Light Hewn Stonelime Dust", "block.minecraft.light_blue_glazed_terracotta": "Light Hewn Glazed Bakedclay", "block.minecraft.light_blue_shulker_box": "Light Hewn Shulker Box", "block.minecraft.light_blue_stained_glass": "Light Hewn Hued Glass", "block.minecraft.light_blue_stained_glass_pane": "Light Hewn Hued Glass Sheet", "block.minecraft.light_blue_terracotta": "Light Hewn Bakedclay", "block.minecraft.light_blue_wool": "Light Hewn Wool", "block.minecraft.light_gray_banner": "Light Gray Streamer", "block.minecraft.light_gray_bed": "Light Gray Bed", "block.minecraft.light_gray_candle": "Light Gray Waxlight", "block.minecraft.light_gray_candle_cake": "Kitch with <PERSON> Gray W<PERSON>light", "block.minecraft.light_gray_carpet": "<PERSON> <PERSON>", "block.minecraft.light_gray_concrete": "Light Gray Stonelime", "block.minecraft.light_gray_concrete_powder": "Light Gray Stonelime Dust", "block.minecraft.light_gray_glazed_terracotta": "<PERSON> Gray Glazed Ba<PERSON>lay", "block.minecraft.light_gray_shulker_box": "Light Gray Shulker Box", "block.minecraft.light_gray_stained_glass": "<PERSON> <PERSON> Hued Glass", "block.minecraft.light_gray_stained_glass_pane": "Light Gray Hued Glass Sheet", "block.minecraft.light_gray_terracotta": "<PERSON> <PERSON>", "block.minecraft.light_gray_wool": "Light Gray Wool", "block.minecraft.light_weighted_pressure_plate": "Light Weighted Thrutch Tile", "block.minecraft.lightning_rod": "Lightning Rod", "block.minecraft.lilac": "Bazeblossom", "block.minecraft.lily_of_the_valley": "Glovewort", "block.minecraft.lily_pad": "Water Leaf", "block.minecraft.lime_banner": "Light Green Streamer", "block.minecraft.lime_bed": "Light Green Bed", "block.minecraft.lime_candle": "Light Green Waxlight", "block.minecraft.lime_candle_cake": "Kitch with Light Green Waxlight", "block.minecraft.lime_carpet": "Light Green Stepcloth", "block.minecraft.lime_concrete": "Light Green Stonelime", "block.minecraft.lime_concrete_powder": "Light Green Stonelime Dust", "block.minecraft.lime_glazed_terracotta": "Light Green Glazed Bakedclay", "block.minecraft.lime_shulker_box": "Light Green Shulker Box", "block.minecraft.lime_stained_glass": "Light Green Hued Glass", "block.minecraft.lime_stained_glass_pane": "Light Green Hued Glass Sheet", "block.minecraft.lime_terracotta": "Light Green Bakedclay", "block.minecraft.lime_wool": "Light Green Wool", "block.minecraft.lodestone": "Lodestone", "block.minecraft.loom": "Loom", "block.minecraft.magenta_banner": "Bazered Streamer", "block.minecraft.magenta_bed": "Bazered Bed", "block.minecraft.magenta_candle": "Bazered Waxlight", "block.minecraft.magenta_candle_cake": "<PERSON>ch with <PERSON><PERSON><PERSON> Waxlight", "block.minecraft.magenta_carpet": "Bazered Stepcloth", "block.minecraft.magenta_concrete": "Bazered Stonelime", "block.minecraft.magenta_concrete_powder": "Bazered Stonelime Dust", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON> Glazed <PERSON>lay", "block.minecraft.magenta_shulker_box": "Bazered Shulker Box", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON>ed Hued Glass", "block.minecraft.magenta_stained_glass_pane": "Bazered Hued Glass Sheet", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON> Wool", "block.minecraft.magma_block": "Moltenstone Clot", "block.minecraft.mangrove_button": "Liftmore Knap", "block.minecraft.mangrove_door": "Liftmore Door", "block.minecraft.mangrove_fence": "Liftmore Edder", "block.minecraft.mangrove_fence_gate": "Liftmore Edder Gate", "block.minecraft.mangrove_hanging_sign": "Liftmore Hanging Token", "block.minecraft.mangrove_leaves": "Liftmore Leaves", "block.minecraft.mangrove_log": "Liftmore Stock", "block.minecraft.mangrove_planks": "Liftmore Boards", "block.minecraft.mangrove_pressure_plate": "Liftmore Thrutch Tile", "block.minecraft.mangrove_propagule": "Liftmore Spreadstem", "block.minecraft.mangrove_roots": "Liftmore Mores", "block.minecraft.mangrove_sign": "Liftmore Token", "block.minecraft.mangrove_slab": "Liftmore Halfclot", "block.minecraft.mangrove_stairs": "Liftmore Stairs", "block.minecraft.mangrove_trapdoor": "Liftmore Trapdoor", "block.minecraft.mangrove_wall_hanging_sign": "Liftmore Side Hanging Token", "block.minecraft.mangrove_wall_sign": "Liftmore Side Token", "block.minecraft.mangrove_wood": "Liftmore Wood", "block.minecraft.medium_amethyst_bud": "Middling Drunklack Sprout", "block.minecraft.melon": "Entapple", "block.minecraft.melon_stem": "Entapple Stem", "block.minecraft.moss_block": "<PERSON>", "block.minecraft.moss_carpet": "<PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON>", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON>tone Halfclot", "block.minecraft.mossy_cobblestone_stairs": "Mossy Cobblestone Stairs", "block.minecraft.mossy_cobblestone_wall": "Mossy Cobblestone Wough", "block.minecraft.mossy_stone_brick_slab": "<PERSON>y Woughstone Halfclot", "block.minecraft.mossy_stone_brick_stairs": "Mossy Woughstone Stairs", "block.minecraft.mossy_stone_brick_wall": "Mossy Woughstone Wough", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON>", "block.minecraft.moving_piston": "Shifting Stampend", "block.minecraft.mud": "Mud", "block.minecraft.mud_brick_slab": "<PERSON>d <PERSON>tone Halfclot", "block.minecraft.mud_brick_stairs": "Mud Woughstone Stairs", "block.minecraft.mud_brick_wall": "<PERSON>d <PERSON> Wough", "block.minecraft.mud_bricks": "<PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON>", "block.minecraft.mushroom_stem": "Toadstool Stem", "block.minecraft.mycelium": "Swambmore", "block.minecraft.nether_brick_fence": "<PERSON><PERSON> <PERSON>", "block.minecraft.nether_brick_slab": "<PERSON>her Tile Half<PERSON>lot", "block.minecraft.nether_brick_stairs": "Nether Tile Stairs", "block.minecraft.nether_brick_wall": "<PERSON>her <PERSON><PERSON>", "block.minecraft.nether_bricks": "<PERSON><PERSON> Tiles", "block.minecraft.nether_gold_ore": "Nether Gold Ore", "block.minecraft.nether_portal": "<PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "Nether Hardstone Ore", "block.minecraft.nether_sprouts": "Nether Sprouts", "block.minecraft.nether_wart": "Nether Wart", "block.minecraft.nether_wart_block": "Nether Wart Stetch", "block.minecraft.netherite_block": "Netherite Clot", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Ringer", "block.minecraft.oak_button": "Oak Knap", "block.minecraft.oak_door": "Oak Door", "block.minecraft.oak_fence": "Oak Edder", "block.minecraft.oak_fence_gate": "Oak Edder Gate", "block.minecraft.oak_hanging_sign": "Oak Hanging Token", "block.minecraft.oak_leaves": "Oak Leaves", "block.minecraft.oak_log": "Oak Stock", "block.minecraft.oak_planks": "Oak Boards", "block.minecraft.oak_pressure_plate": "<PERSON> Thrutch Tile", "block.minecraft.oak_sapling": "Oak Sprout", "block.minecraft.oak_sign": "Oak Token", "block.minecraft.oak_slab": "Oak Halfclot", "block.minecraft.oak_stairs": "Oak Stairs", "block.minecraft.oak_trapdoor": "Oak Trapdoor", "block.minecraft.oak_wall_hanging_sign": "Oak Side Hanging Token", "block.minecraft.oak_wall_sign": "Oak Side Token", "block.minecraft.oak_wood": "Oak Wood", "block.minecraft.observer": "Beholder", "block.minecraft.obsidian": "Ravenflint", "block.minecraft.ochre_froglight": "Yellow Froshlight", "block.minecraft.ominous_banner": "Threatening Streamer", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "<PERSON><PERSON>", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "<PERSON><PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> with <PERSON><PERSON>", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.orange_shulker_box": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "<PERSON><PERSON> Glass Sheet", "block.minecraft.orange_terracotta": "<PERSON><PERSON>", "block.minecraft.orange_tulip": "<PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON>", "block.minecraft.oxeye_daisy": "<PERSON>", "block.minecraft.oxidized_chiseled_copper": "Sourshafted Graven Are", "block.minecraft.oxidized_copper": "Sourshafted Are", "block.minecraft.oxidized_copper_bulb": "Sourshafted Are Lightvat", "block.minecraft.oxidized_copper_door": "Sourshafted Are Door", "block.minecraft.oxidized_copper_grate": "Sourshafted Are Hurdle", "block.minecraft.oxidized_copper_trapdoor": "Sourshafted Are Trapdoor", "block.minecraft.oxidized_cut_copper": "Sourshafted Snithed Are", "block.minecraft.oxidized_cut_copper_slab": "Sourshafted Snithed Are Halfclot", "block.minecraft.oxidized_cut_copper_stairs": "Sourshafted Snithed Are Stairs", "block.minecraft.packed_ice": "Packed Ice", "block.minecraft.packed_mud": "Packed Mud", "block.minecraft.pale_hanging_moss": "Bloak Hanging Moss", "block.minecraft.pale_moss_block": "Bloak Moss Clot", "block.minecraft.pale_moss_carpet": "Bloak Moss Layer", "block.minecraft.pale_oak_button": "Bloak Oak Knap", "block.minecraft.pale_oak_door": "Bloak Oak Door", "block.minecraft.pale_oak_fence": "Bloak Oak Edder", "block.minecraft.pale_oak_fence_gate": "Bloak Oak Edder Gate", "block.minecraft.pale_oak_hanging_sign": "Bloak Oak Hanging Token", "block.minecraft.pale_oak_leaves": "Bloak Oak Leaves", "block.minecraft.pale_oak_log": "Bloak Oak Stock", "block.minecraft.pale_oak_planks": "Bloak Oak Boards", "block.minecraft.pale_oak_pressure_plate": "Bloak Oak Thrutch Tile", "block.minecraft.pale_oak_sapling": "Bloak Oak Sprout", "block.minecraft.pale_oak_sign": "Bloak Oak Token", "block.minecraft.pale_oak_slab": "Bloak Oak Halfclot", "block.minecraft.pale_oak_stairs": "Bloak Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Bloak Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Bloak Oak Side Hanging Token", "block.minecraft.pale_oak_wall_sign": "Bloak Oak Side Token", "block.minecraft.pale_oak_wood": "Bloak Oak Wood", "block.minecraft.pearlescent_froglight": "White Froshlight", "block.minecraft.peony": "Kingsblossom", "block.minecraft.petrified_oak_slab": "Stonened Oak Halfclot", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> Side Head", "block.minecraft.pink_banner": "Light Red Streamer", "block.minecraft.pink_bed": "Light Red Bed", "block.minecraft.pink_candle": "Light Red Waxlight", "block.minecraft.pink_candle_cake": "Kitch with Light Red Waxlight", "block.minecraft.pink_carpet": "Light Red Stepcloth", "block.minecraft.pink_concrete": "Light Red Stonelime", "block.minecraft.pink_concrete_powder": "Light Red Stonelime Dust", "block.minecraft.pink_glazed_terracotta": "Light Red Glazed Bakedclay", "block.minecraft.pink_petals": "Light Red Leaves", "block.minecraft.pink_shulker_box": "Light Red Shulker Box", "block.minecraft.pink_stained_glass": "Light Red Hued Glass", "block.minecraft.pink_stained_glass_pane": "Light Red Hued Glass Sheet", "block.minecraft.pink_terracotta": "Light Red Bakedclay", "block.minecraft.pink_tulip": "Light Red Houveblossom", "block.minecraft.pink_wool": "Light Red Wool", "block.minecraft.piston": "Stampend", "block.minecraft.piston_head": "Stampend Head", "block.minecraft.pitcher_crop": "Vatleaf Crop", "block.minecraft.pitcher_plant": "Vatleaf Wort", "block.minecraft.player_head": "Player Head", "block.minecraft.player_head.named": "%s's Head", "block.minecraft.player_wall_head": "Player Side Head", "block.minecraft.podzol": "Underash", "block.minecraft.pointed_dripstone": "Orded Dripstone", "block.minecraft.polished_andesite": "Sliked Andestone", "block.minecraft.polished_andesite_slab": "Sliked Andestone Halfclot", "block.minecraft.polished_andesite_stairs": "Sliked Andestone Stairs", "block.minecraft.polished_basalt": "Sliked Rinestone", "block.minecraft.polished_blackstone": "Sliked <PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Sliked Blackstone Woughstone Halfclot", "block.minecraft.polished_blackstone_brick_stairs": "Sliked Blackstone Woughstone Stairs", "block.minecraft.polished_blackstone_brick_wall": "Sliked Blackstone Woughstone Wough", "block.minecraft.polished_blackstone_bricks": "Sliked Blackstone Woughstones", "block.minecraft.polished_blackstone_button": "Sliked Blackstone Knap", "block.minecraft.polished_blackstone_pressure_plate": "Sliked Blackstone Thrutch Tile", "block.minecraft.polished_blackstone_slab": "Sliked Blackstone Halfclot", "block.minecraft.polished_blackstone_stairs": "Sliked Blackstone Stairs", "block.minecraft.polished_blackstone_wall": "Sliked Blackstone Wough", "block.minecraft.polished_deepslate": "Sliked Deepstone", "block.minecraft.polished_deepslate_slab": "Sliked Deepstone Halfclot", "block.minecraft.polished_deepslate_stairs": "Sliked Deepstone Stairs", "block.minecraft.polished_deepslate_wall": "Sliked Deepstone Wough", "block.minecraft.polished_diorite": "Sliked Shedstone", "block.minecraft.polished_diorite_slab": "Sliked Shedstone Halfclot", "block.minecraft.polished_diorite_stairs": "Sliked Shedstone Stairs", "block.minecraft.polished_granite": "Sliked Cornstone", "block.minecraft.polished_granite_slab": "Sliked Cornstone Halfclot", "block.minecraft.polished_granite_stairs": "Sliked Cornstone Stairs", "block.minecraft.polished_tuff": "Sliked Ashstone", "block.minecraft.polished_tuff_slab": "Sliked Ashstone Halfclot", "block.minecraft.polished_tuff_stairs": "Sliked Ashstone Stairs", "block.minecraft.polished_tuff_wall": "Sliked Ashstone Wough", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Earthapples", "block.minecraft.potted_acacia_sapling": "Potted Wattletree Sprout", "block.minecraft.potted_allium": "Potted Entleek", "block.minecraft.potted_azalea_bush": "Potted Drywort", "block.minecraft.potted_azure_bluet": "Potted Hewnling", "block.minecraft.potted_bamboo": "Potted Treereed", "block.minecraft.potted_birch_sapling": "Potted Birch Sprout", "block.minecraft.potted_blue_orchid": "Potted Hewn Ballockwort", "block.minecraft.potted_brown_mushroom": "Potted Brown Toadstool", "block.minecraft.potted_cactus": "Potted Thorntree", "block.minecraft.potted_cherry_sapling": "Potted Stoneberry Sprout", "block.minecraft.potted_closed_eyeblossom": "Potted Shut Eyeblossom", "block.minecraft.potted_cornflower": "Potted Cornblossom", "block.minecraft.potted_crimson_fungus": "Potted Fellred Swamb", "block.minecraft.potted_crimson_roots": "Potted Fellred Stems", "block.minecraft.potted_dandelion": "Potted Eywort", "block.minecraft.potted_dark_oak_sapling": "Potted Dark Oak Sprout", "block.minecraft.potted_dead_bush": "Potted Dead Bush", "block.minecraft.potted_fern": "Potted Fern", "block.minecraft.potted_flowering_azalea_bush": "Potted Blossoming Drywort", "block.minecraft.potted_jungle_sapling": "Potted Rainwold Sprout", "block.minecraft.potted_lily_of_the_valley": "Potted Glovewort", "block.minecraft.potted_mangrove_propagule": "Potted Liftmore Spreadstem", "block.minecraft.potted_oak_sapling": "Potted Oak Sprout", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "Potted Yellowred Ho<PERSON>lossom", "block.minecraft.potted_oxeye_daisy": "Potted White Bothen", "block.minecraft.potted_pale_oak_sapling": "Potted Bloak Oak Sprout", "block.minecraft.potted_pink_tulip": "Potted Light Red Houveblossom", "block.minecraft.potted_poppy": "Potted Poppy", "block.minecraft.potted_red_mushroom": "Potted Red Toadstool", "block.minecraft.potted_red_tulip": "Potted Red Houveblossom", "block.minecraft.potted_spruce_sapling": "Potted Harttartree Sprout", "block.minecraft.potted_torchflower": "Potted Thackleblossom", "block.minecraft.potted_warped_fungus": "Potted Warped Swamb", "block.minecraft.potted_warped_roots": "Potted Warped Stems", "block.minecraft.potted_white_tulip": "Potted White Houveblossom", "block.minecraft.potted_wither_rose": "Potted Wither <PERSON>", "block.minecraft.powder_snow": "Dust Snow", "block.minecraft.powder_snow_cauldron": "Dust Snow Ironcrock", "block.minecraft.powered_rail": "Mightened Ironroad", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON><PERSON> Halfclot", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON>ne <PERSON>tone Stairs", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "Harvestovet", "block.minecraft.pumpkin_stem": "Harvestovet Stem", "block.minecraft.purple_banner": "<PERSON><PERSON>", "block.minecraft.purple_bed": "Baze Bed", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "Kitch with <PERSON><PERSON>", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON>ze <PERSON>", "block.minecraft.purple_concrete_powder": "Baze Stonelime Dust", "block.minecraft.purple_glazed_terracotta": "Baze Glazed Bakedclay", "block.minecraft.purple_shulker_box": "<PERSON>ze <PERSON>", "block.minecraft.purple_stained_glass": "<PERSON>ze <PERSON>", "block.minecraft.purple_stained_glass_pane": "Baze Hued Glass Sheet", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "Baze Wool", "block.minecraft.purpur_block": "Purpur Clot", "block.minecraft.purpur_pillar": "Purpur Stud", "block.minecraft.purpur_slab": "Purpur Halfclot", "block.minecraft.purpur_stairs": "Purpur Stairs", "block.minecraft.quartz_block": "Hardstone Clot", "block.minecraft.quartz_bricks": "Hardstone Woughstones", "block.minecraft.quartz_pillar": "Hardstone Stud", "block.minecraft.quartz_slab": "Hardstone Halfclot", "block.minecraft.quartz_stairs": "Hardstone Stairs", "block.minecraft.rail": "Ironroad", "block.minecraft.raw_copper_block": "Raw Are Clot", "block.minecraft.raw_gold_block": "Raw Gold Clot", "block.minecraft.raw_iron_block": "Raw Iron Clot", "block.minecraft.red_banner": "Red Streamer", "block.minecraft.red_bed": "Red Bed", "block.minecraft.red_candle": "Red Waxlight", "block.minecraft.red_candle_cake": "Kitch with <PERSON>", "block.minecraft.red_carpet": "Red Stepcloth", "block.minecraft.red_concrete": "Red Stone<PERSON>e", "block.minecraft.red_concrete_powder": "Red Stonelime Dust", "block.minecraft.red_glazed_terracotta": "Red Glazed Bakedclay", "block.minecraft.red_mushroom": "Red Toadstool", "block.minecraft.red_mushroom_block": "Red Toadstool Clot", "block.minecraft.red_nether_brick_slab": "Red Nether Tile Halfclot", "block.minecraft.red_nether_brick_stairs": "Red Nether Tile Stairs", "block.minecraft.red_nether_brick_wall": "Red Nether Tile Wough", "block.minecraft.red_nether_bricks": "Red Nether Tiles", "block.minecraft.red_sand": "Red Sand", "block.minecraft.red_sandstone": "Red Sandstone", "block.minecraft.red_sandstone_slab": "Red Sandstone Halfclot", "block.minecraft.red_sandstone_stairs": "Red Sandstone Stairs", "block.minecraft.red_sandstone_wall": "Red Sandstone Wough", "block.minecraft.red_shulker_box": "Red Shulker Box", "block.minecraft.red_stained_glass": "Red Hued Glass", "block.minecraft.red_stained_glass_pane": "Red Hued Glass Sheet", "block.minecraft.red_terracotta": "<PERSON>", "block.minecraft.red_tulip": "Red Houveblossom", "block.minecraft.red_wool": "Red Wool", "block.minecraft.redstone_block": "Redstone Clot", "block.minecraft.redstone_lamp": "Redstone Lightvat", "block.minecraft.redstone_ore": "Redstone Ore", "block.minecraft.redstone_torch": "Redstone Thackle", "block.minecraft.redstone_wall_torch": "Redstone Side Thackle", "block.minecraft.redstone_wire": "Redstone Mark", "block.minecraft.reinforced_deepslate": "Strengthened Deepstone", "block.minecraft.repeater": "Redstone Edlocker", "block.minecraft.repeating_command_block": "Edledging He<PERSON> Clot", "block.minecraft.resin_block": "<PERSON>", "block.minecraft.resin_brick_slab": "<PERSON>", "block.minecraft.resin_brick_stairs": "<PERSON>", "block.minecraft.resin_brick_wall": "<PERSON>", "block.minecraft.resin_bricks": "<PERSON>", "block.minecraft.resin_clump": "<PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON> Holder", "block.minecraft.rooted_dirt": "Mored Earth", "block.minecraft.rose_bush": "Loveb<PERSON> Bush", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "Sandstone", "block.minecraft.sandstone_slab": "Sandstone Halfclot", "block.minecraft.sandstone_stairs": "Sandstone Stairs", "block.minecraft.sandstone_wall": "Sandstone Wough", "block.minecraft.scaffolding": "Stelling", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk Sunderer", "block.minecraft.sculk_sensor": "Sculk Feeler", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "Sculk Edder", "block.minecraft.sea_lantern": "Sea Lightvat", "block.minecraft.sea_pickle": "Sea Pod", "block.minecraft.seagrass": "Seagrass", "block.minecraft.set_spawn": "Edstarting ord set", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "Short Grass", "block.minecraft.shroomlight": "Swamblight", "block.minecraft.shulker_box": "Shulker Box", "block.minecraft.skeleton_skull": "Boneframe Headbone", "block.minecraft.skeleton_wall_skull": "Boneframe Side Headbone", "block.minecraft.slime_block": "Slime Clot", "block.minecraft.small_amethyst_bud": "Small Drunklack Sprout", "block.minecraft.small_dripleaf": "Small Dripleaf", "block.minecraft.smithing_table": "Smithing Bench", "block.minecraft.smoker": "Smoker", "block.minecraft.smooth_basalt": "Smooth Rinestone", "block.minecraft.smooth_quartz": "Smooth Hardstone Clot", "block.minecraft.smooth_quartz_slab": "Smooth Hardstone Halfclot", "block.minecraft.smooth_quartz_stairs": "Smooth Hardstone Stairs", "block.minecraft.smooth_red_sandstone": "Smooth Red Sandstone", "block.minecraft.smooth_red_sandstone_slab": "Smooth Red Sandstone Halfclot", "block.minecraft.smooth_red_sandstone_stairs": "Smooth Red Sandstone Stairs", "block.minecraft.smooth_sandstone": "Smooth Sandstone", "block.minecraft.smooth_sandstone_slab": "Smooth Sandstone Halfclot", "block.minecraft.smooth_sandstone_stairs": "Smooth Sandstone Stairs", "block.minecraft.smooth_stone": "Smooth Stone", "block.minecraft.smooth_stone_slab": "Smooth Stone Halfclot", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.snow": "Snow", "block.minecraft.snow_block": "<PERSON>", "block.minecraft.soul_campfire": "Soul Haltfire", "block.minecraft.soul_fire": "Soul Fire", "block.minecraft.soul_lantern": "Soul Lightvat", "block.minecraft.soul_sand": "Soul Sand", "block.minecraft.soul_soil": "Soul Earth", "block.minecraft.soul_torch": "Soul Thackle", "block.minecraft.soul_wall_torch": "Soul Side Thackle", "block.minecraft.spawn.not_valid": "You have no home bed or loaded edstart holder, or it was hindered", "block.minecraft.spawner": "<PERSON><PERSON>", "block.minecraft.spawner.desc1": "Brooking Making Ey:", "block.minecraft.spawner.desc2": "Sets Wight Kind", "block.minecraft.sponge": "Seaswamb", "block.minecraft.spore_blossom": "Seed Blossom", "block.minecraft.spruce_button": "Harttartree Knap", "block.minecraft.spruce_door": "Harttartree Door", "block.minecraft.spruce_fence": "Harttartree Edder", "block.minecraft.spruce_fence_gate": "Harttartree Edder Gate", "block.minecraft.spruce_hanging_sign": "Harttartree Hanging Token", "block.minecraft.spruce_leaves": "Harttartree Leaves", "block.minecraft.spruce_log": "Harttartree Stock", "block.minecraft.spruce_planks": "Harttartree Boards", "block.minecraft.spruce_pressure_plate": "Harttartree Thrutch Tile", "block.minecraft.spruce_sapling": "Harttartree Sprout", "block.minecraft.spruce_sign": "Harttartree Token", "block.minecraft.spruce_slab": "Harttartree Halfclot", "block.minecraft.spruce_stairs": "Harttartree Stairs", "block.minecraft.spruce_trapdoor": "Harttartree Trapdoor", "block.minecraft.spruce_wall_hanging_sign": "Harttartree Side Hanging Token", "block.minecraft.spruce_wall_sign": "Harttartree Side Token", "block.minecraft.spruce_wood": "Harttartree Wood", "block.minecraft.sticky_piston": "<PERSON><PERSON>", "block.minecraft.stone": "Stone", "block.minecraft.stone_brick_slab": "Woughstone Halfclot", "block.minecraft.stone_brick_stairs": "Woughstone Stairs", "block.minecraft.stone_brick_wall": "Woughstone Wough", "block.minecraft.stone_bricks": "Woughstones", "block.minecraft.stone_button": "Stone <PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON> Thrutch Tile", "block.minecraft.stone_slab": "<PERSON>", "block.minecraft.stone_stairs": "Stone Stairs", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Stripped Wattletree Stock", "block.minecraft.stripped_acacia_wood": "Stripped Wattletree Wood", "block.minecraft.stripped_bamboo_block": "Stripped Treereed Clot", "block.minecraft.stripped_birch_log": "Stripped Birch Stock", "block.minecraft.stripped_birch_wood": "Stripped Birch Wood", "block.minecraft.stripped_cherry_log": "Stripped Stoneberry Stock", "block.minecraft.stripped_cherry_wood": "Stripped Stoneberry Wood", "block.minecraft.stripped_crimson_hyphae": "Stripped <PERSON><PERSON>", "block.minecraft.stripped_crimson_stem": "Stripped <PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "Stripped Dark Oak Stock", "block.minecraft.stripped_dark_oak_wood": "Stripped Dark Oak Wood", "block.minecraft.stripped_jungle_log": "Stripped Rainwold Stock", "block.minecraft.stripped_jungle_wood": "Stripped Rainwold Wood", "block.minecraft.stripped_mangrove_log": "Stripped Liftmore Stock", "block.minecraft.stripped_mangrove_wood": "Stripped Liftmore Wood", "block.minecraft.stripped_oak_log": "Stripped Oak Stock", "block.minecraft.stripped_oak_wood": "Stripped Oak Wood", "block.minecraft.stripped_pale_oak_log": "Stripped Bloak Oak Stock", "block.minecraft.stripped_pale_oak_wood": "Stripped Bloak Oak Wood", "block.minecraft.stripped_spruce_log": "Stripped Harttartree Stock", "block.minecraft.stripped_spruce_wood": "Stripped Harttartree Wood", "block.minecraft.stripped_warped_hyphae": "Stripped Warped Swambwire", "block.minecraft.stripped_warped_stem": "Stripped Warped Stem", "block.minecraft.structure_block": "Framework Clot", "block.minecraft.structure_void": "Framework Roomth", "block.minecraft.sugar_cane": "Sweet Reed", "block.minecraft.sunflower": "Sunblossom", "block.minecraft.suspicious_gravel": "Weird Pebbles", "block.minecraft.suspicious_sand": "Weird Sand", "block.minecraft.sweet_berry_bush": "Sweet <PERSON>", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "Tall Grass", "block.minecraft.tall_seagrass": "Tall Seagrass", "block.minecraft.target": "<PERSON><PERSON>", "block.minecraft.terracotta": "Bakedclay", "block.minecraft.test_block": "<PERSON><PERSON>", "block.minecraft.test_instance_block": "Fand Befalling Clot", "block.minecraft.tinted_glass": "Shaded Glass", "block.minecraft.tnt": "B<PERSON>le", "block.minecraft.tnt.disabled": "Blastle blasts are laid off", "block.minecraft.torch": "<PERSON><PERSON>le", "block.minecraft.torchflower": "Thackle<PERSON><PERSON>", "block.minecraft.torchflower_crop": "Thackleblossom Crop", "block.minecraft.trapped_chest": "Trapped Chest", "block.minecraft.trial_spawner": "<PERSON><PERSON>", "block.minecraft.tripwire": "Tripwire", "block.minecraft.tripwire_hook": "Tripwire Hook", "block.minecraft.tube_coral": "<PERSON>", "block.minecraft.tube_coral_block": "<PERSON>", "block.minecraft.tube_coral_fan": "<PERSON> Blower", "block.minecraft.tube_coral_wall_fan": "<PERSON> Blossomdeer Side Blower", "block.minecraft.tuff": "Ashstone", "block.minecraft.tuff_brick_slab": "Ashstone Woughstone Halfclot", "block.minecraft.tuff_brick_stairs": "Ashstone Woughstone Stairs", "block.minecraft.tuff_brick_wall": "Ashstone Woughstone Wough", "block.minecraft.tuff_bricks": "Ashstone Woughstones", "block.minecraft.tuff_slab": "Ashstone Halfclot", "block.minecraft.tuff_stairs": "Ashstone Stairs", "block.minecraft.tuff_wall": "Ashstone Wough", "block.minecraft.turtle_egg": "Shellpad Ey", "block.minecraft.twisting_vines": "Twisting Standgrass", "block.minecraft.twisting_vines_plant": "Twisting Standgrass Wort", "block.minecraft.vault": "<PERSON><PERSON>", "block.minecraft.verdant_froglight": "Green Froshlight", "block.minecraft.vine": "Hanggrass", "block.minecraft.void_air": "Empty Lift", "block.minecraft.wall_torch": "Side Thackle", "block.minecraft.warped_button": "Warped Knap", "block.minecraft.warped_door": "Warped Door", "block.minecraft.warped_fence": "Warped <PERSON>", "block.minecraft.warped_fence_gate": "Warped Edder Gate", "block.minecraft.warped_fungus": "Warped Swamb", "block.minecraft.warped_hanging_sign": "Warped Hanging Token", "block.minecraft.warped_hyphae": "Warped Swambwire", "block.minecraft.warped_nylium": "Warped <PERSON>wamb<PERSON>the", "block.minecraft.warped_planks": "Warped Boards", "block.minecraft.warped_pressure_plate": "Warped Thr<PERSON>ch Tile", "block.minecraft.warped_roots": "Warped Stems", "block.minecraft.warped_sign": "Warped <PERSON>ken", "block.minecraft.warped_slab": "Warped Halfclot", "block.minecraft.warped_stairs": "Warped Stairs", "block.minecraft.warped_stem": "Warped Stem", "block.minecraft.warped_trapdoor": "Warped Trapdoor", "block.minecraft.warped_wall_hanging_sign": "Warped Side Hanging Token", "block.minecraft.warped_wall_sign": "Warped Side Token", "block.minecraft.warped_wart_block": "Warped Wart Stetch", "block.minecraft.water": "Water", "block.minecraft.water_cauldron": "Water Ironcrock", "block.minecraft.waxed_chiseled_copper": "Waxed Graven Are", "block.minecraft.waxed_copper_block": "Waxed Clot of Are", "block.minecraft.waxed_copper_bulb": "Waxed Are Lightvat", "block.minecraft.waxed_copper_door": "Waxed Are Door", "block.minecraft.waxed_copper_grate": "Waxed <PERSON> Hurdle", "block.minecraft.waxed_copper_trapdoor": "Waxed Are Trapdoor", "block.minecraft.waxed_cut_copper": "Waxed Snithed Are", "block.minecraft.waxed_cut_copper_slab": "Waxed Snithed Are Halfclot", "block.minecraft.waxed_cut_copper_stairs": "Waxed Snithed Are Stairs", "block.minecraft.waxed_exposed_chiseled_copper": "Waxed Unwried <PERSON>n Are", "block.minecraft.waxed_exposed_copper": "Waxed Unwried Are", "block.minecraft.waxed_exposed_copper_bulb": "Waxed Unwried Are Lightvat", "block.minecraft.waxed_exposed_copper_door": "Waxed Unwried Are Door", "block.minecraft.waxed_exposed_copper_grate": "Waxed Unwried Are Hurdle", "block.minecraft.waxed_exposed_copper_trapdoor": "Waxed Unwried Are Trapdoor", "block.minecraft.waxed_exposed_cut_copper": "Waxed Unwried Snithed Are", "block.minecraft.waxed_exposed_cut_copper_slab": "Waxed Unwried Snithed Are Halfclot", "block.minecraft.waxed_exposed_cut_copper_stairs": "Waxed Unwried Snithed Are Stairs", "block.minecraft.waxed_oxidized_chiseled_copper": "Waxed Sourshafted Graven Are", "block.minecraft.waxed_oxidized_copper": "Waxed Sourshafted Are", "block.minecraft.waxed_oxidized_copper_bulb": "Waxed Sourshafted Are Lightvat", "block.minecraft.waxed_oxidized_copper_door": "Waxed Sourshafted Are Door", "block.minecraft.waxed_oxidized_copper_grate": "Waxed Sourshafted Are Hurdle", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waxed Sourshafted Are Trapdoor", "block.minecraft.waxed_oxidized_cut_copper": "Waxed Sourshafted Snithed Are", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waxed Sourshafted Snithed Are Halfclot", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Waxed Sourshafted Snithed Are Stairs", "block.minecraft.waxed_weathered_chiseled_copper": "Waxed Weathered Graven Are", "block.minecraft.waxed_weathered_copper": "Waxed Weathered Are", "block.minecraft.waxed_weathered_copper_bulb": "Waxed Weathered Are Lightvat", "block.minecraft.waxed_weathered_copper_door": "Waxed Weathered Are Door", "block.minecraft.waxed_weathered_copper_grate": "Waxed Weathered Are Hurdle", "block.minecraft.waxed_weathered_copper_trapdoor": "Waxed Weathered Are Trapdoor", "block.minecraft.waxed_weathered_cut_copper": "Waxed Weathered Snithed Are", "block.minecraft.waxed_weathered_cut_copper_slab": "Waxed Weathered Snithed Are Halfclot", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waxed Weathered Snithed Are Stairs", "block.minecraft.weathered_chiseled_copper": "Weathered Graven Are", "block.minecraft.weathered_copper": "Weathered Are", "block.minecraft.weathered_copper_bulb": "Weathered Are Lightvat", "block.minecraft.weathered_copper_door": "Weathered Are Door", "block.minecraft.weathered_copper_grate": "Weathered Are Hurdle", "block.minecraft.weathered_copper_trapdoor": "Weathered Are Trapdoor", "block.minecraft.weathered_cut_copper": "Weathered Snithed Are", "block.minecraft.weathered_cut_copper_slab": "Weathered Snithed Are Halfclot", "block.minecraft.weathered_cut_copper_stairs": "Weathered Snithed Are Stairs", "block.minecraft.weeping_vines": "Weeping Hanggrass", "block.minecraft.weeping_vines_plant": "Weeping <PERSON><PERSON> Wort", "block.minecraft.wet_sponge": "Wet Seaswamb", "block.minecraft.wheat": "Wheat Crops", "block.minecraft.white_banner": "White Streamer", "block.minecraft.white_bed": "White Bed", "block.minecraft.white_candle": "White Waxlight", "block.minecraft.white_candle_cake": "Kitch with <PERSON> Waxlight", "block.minecraft.white_carpet": "White Stepcloth", "block.minecraft.white_concrete": "White Stonelime", "block.minecraft.white_concrete_powder": "White Stonelime Dust", "block.minecraft.white_glazed_terracotta": "White Glazed Bakedclay", "block.minecraft.white_shulker_box": "White Shulker Box", "block.minecraft.white_stained_glass": "White Hued Glass", "block.minecraft.white_stained_glass_pane": "White Hued Glass Sheet", "block.minecraft.white_terracotta": "White Bakedclay", "block.minecraft.white_tulip": "White Houveblossom", "block.minecraft.white_wool": "White Wool", "block.minecraft.wildflowers": "Wildblossoms", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON> Headbone", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON> Boneframe Side Headbone", "block.minecraft.yellow_banner": "Yellow Streamer", "block.minecraft.yellow_bed": "Yellow Bed", "block.minecraft.yellow_candle": "Yellow Waxlight", "block.minecraft.yellow_candle_cake": "Kitch with <PERSON> Waxlight", "block.minecraft.yellow_carpet": "Yellow Stepcloth", "block.minecraft.yellow_concrete": "Yellow Stonelime", "block.minecraft.yellow_concrete_powder": "Yellow Stonelime Dust", "block.minecraft.yellow_glazed_terracotta": "Yellow Glazed Bakedclay", "block.minecraft.yellow_shulker_box": "Yellow Shulker Box", "block.minecraft.yellow_stained_glass": "Yellow Hued Glass", "block.minecraft.yellow_stained_glass_pane": "Yellow Hued Glass Sheet", "block.minecraft.yellow_terracotta": "Yellow Bakedclay", "block.minecraft.yellow_wool": "Yellow Wool", "block.minecraft.zombie_head": "Undead Lich Head", "block.minecraft.zombie_wall_head": "Undead Lich Side Head", "book.byAuthor": "by %1$s", "book.edit.title": "Book Bework Shirm", "book.editTitle": "Input Book Name:", "book.finalizeButton": "Underwrite and Shut", "book.finalizeWarning": "Hark! When you underwrite the book, it will no longer be beworkbere.", "book.generation.0": "First Book", "book.generation.1": "Clove of first book", "book.generation.2": "Clove of a clove", "book.generation.3": "Tatticked", "book.invalid.tag": "* Unright book token *", "book.pageIndicator": "Sheet %1$s of %2$s", "book.page_button.next": "Next Sheet", "book.page_button.previous": "Former Sheet", "book.sign.title": "Book Underwrite Shirm", "book.sign.titlebox": "Name", "book.signButton": "Underwrite", "book.view.title": "Book Show Shirm", "build.tooHigh": "Upper height threshold for building is %s", "chat.cannotSend": "Cannot send chat writ", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Click to farferry", "chat.copy": "Clove to Shearboard", "chat.copy.click": "Click to <PERSON><PERSON> to Shearboard", "chat.deleted_marker": "This chat writ has been adwashed by the outreckoner.", "chat.disabled.chain_broken": "<PERSON><PERSON> is off owing to broken rackent. Kindly fand edbinding.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> is off owing to quenched selfleaf open key. Kindly fand edbinding.", "chat.disabled.invalid_command_signature": "Hest had unweened or missing hest flite underwrits.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had a unright underwrit. Kindly fand edbinding.", "chat.disabled.launcher": "<PERSON><PERSON> is off by starter kire. <PERSON><PERSON> send writ.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> is off owing to missing selfleaf open key. Kindly fand edbinding.", "chat.disabled.options": "Chat is off in software kires.", "chat.disabled.out_of_order_chat": "Chat reaped out-of-order. Did your layout time wend?", "chat.disabled.profile": "Chat is not aleaved by reckoning settings. Thring '%s' ayen for more abreasting.", "chat.disabled.profile.moreInfo": "Chat is not aleaved by reckoning settings. Cannot send or show writs.", "chat.editBox": "chat", "chat.filtered": "Sieved by the outreckoner.", "chat.filtered_full": "The outreckoner has hidden your writ for some players.", "chat.link.confirm": "Are you wis you wish to open the following webstead?", "chat.link.confirmTrusted": "Do you wish to open this link or clove it to your shearboard?", "chat.link.open": "Open in Browser", "chat.link.warning": "Never open links from folk that you don't trow!", "chat.queue": "[+%s ongoing streak(s)]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON>reckone<PERSON> sent an unright writ.", "chat.tag.modified": "Writ wended by the outreckoner. Unwended:", "chat.tag.not_secure": "Unasoothed writ. Cannot be mielded.", "chat.tag.system": "Outreckoner writ. Cannot be mielded.", "chat.tag.system_single_player": "Outreckoner writ.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s has fuldone the dare %s", "chat.type.advancement.goal": "%s has reached the goal %s", "chat.type.advancement.task": "%s has made the forthstep %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Writ Team", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s says %s", "chat.validation_error": "Chat rightening dwale", "chat_screen.message": "Writ to send: %s", "chat_screen.title": "<PERSON>t shirm", "chat_screen.usage": "Input writ and thring Streakbreak to send", "chunk.toast.checkLog": "See daybook for more insights", "chunk.toast.loadFailure": "Could not load worldstetch at %s", "chunk.toast.lowDiskSpace": "Neath harddrive room!", "chunk.toast.lowDiskSpace.description": "Might not be fit to keep the world.", "chunk.toast.saveFailure": "Could not keep worldstetch at %s", "clear.failed.multiple": "No things were found on %s players", "clear.failed.single": "No things were found on player %s", "color.minecraft.black": "Black", "color.minecraft.blue": "Hewn", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "Hewngreen", "color.minecraft.gray": "<PERSON>", "color.minecraft.green": "Green", "color.minecraft.light_blue": "Light Hewn", "color.minecraft.light_gray": "Light Gray", "color.minecraft.lime": "Light Green", "color.minecraft.magenta": "<PERSON><PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON>", "color.minecraft.pink": "Light Red", "color.minecraft.purple": "Baze", "color.minecraft.red": "Red", "color.minecraft.white": "White", "color.minecraft.yellow": "Yellow", "command.context.here": "<--[HERE]", "command.context.parse_error": "%s at stow %s: %s", "command.exception": "Could not understand hest: %s", "command.expected.separator": "Abode whitegap to end one flite, but found loasting lore", "command.failed": "An unweened dwale befallen whilst fanding to run that hest", "command.forkLimit": "Greatest rime of frameworks (%s) reached", "command.unknown.argument": "Unright flite for this hest", "command.unknown.command": "Unknown or underdone hest, see underneath for dwale", "commands.advancement.criterionNotFound": "The forthstep %1$s does not inhold the yardstick '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Couldn't yeave yardstick '%s' of forthstep %s to %s players as hie already have it", "commands.advancement.grant.criterion.to.many.success": "Yave yardstick '%s' of forthstep %s to %s players", "commands.advancement.grant.criterion.to.one.failure": "Couldn't yeave yardstick '%s' of forthstep %s to %s as hie already have it", "commands.advancement.grant.criterion.to.one.success": "Yave yardstick '%s' of forthstep %s to %s", "commands.advancement.grant.many.to.many.failure": "Couldn't yeave %s forthsteps to %s players as hie already have hem", "commands.advancement.grant.many.to.many.success": "Yave %s forthsteps to %s players", "commands.advancement.grant.many.to.one.failure": "Couldn't yeave %s forthsteps to %s as hie already have hem", "commands.advancement.grant.many.to.one.success": "Yave %s forthsteps to %s", "commands.advancement.grant.one.to.many.failure": "Couldn't yeave forthstep %s to %s players as hie already have it", "commands.advancement.grant.one.to.many.success": "Yave the forthstep %s to %s players", "commands.advancement.grant.one.to.one.failure": "Couldn't yeave forthstep %s to %s as hie already have it", "commands.advancement.grant.one.to.one.success": "Yave the forthstep %s to %s", "commands.advancement.revoke.criterion.to.many.failure": "Couldn't fornim yardstick '%s' of forthstep %s from %s players as hie don't have it", "commands.advancement.revoke.criterion.to.many.success": "Fornimmed yardstick '%s' of forthstep %s from %s players", "commands.advancement.revoke.criterion.to.one.failure": "Couldn't fornim yardstick '%s' of forthstep %s from %s as hie don't have it", "commands.advancement.revoke.criterion.to.one.success": "Fornimmed yardstick '%s' of forthstep %s from %s", "commands.advancement.revoke.many.to.many.failure": "Couldn't fornim %s forthsteps from %s players as hie don't have hem", "commands.advancement.revoke.many.to.many.success": "Fornimmed %s forthsteps from %s players", "commands.advancement.revoke.many.to.one.failure": "Couldn't fornim %s forthsteps from %s as hie don't have hem", "commands.advancement.revoke.many.to.one.success": "Fornimmed %s forthsteps from %s", "commands.advancement.revoke.one.to.many.failure": "Couldn't fornim forthstep %s from %s players as hie don't have it", "commands.advancement.revoke.one.to.many.success": "Fornimmed the forthstep %s from %s players", "commands.advancement.revoke.one.to.one.failure": "Couldn't fornim forthstep %s from %s as hie don't have it", "commands.advancement.revoke.one.to.one.success": "Fornimmed the forthstep %s from %s", "commands.attribute.base_value.get.success": "Orworth of mark %s for ansen %s is %s", "commands.attribute.base_value.reset.success": "Orworth for mark %s for ansen %s edset to stock %s", "commands.attribute.base_value.set.success": "Orworth for mark %s for ansen %s set to %s", "commands.attribute.failed.entity": "%s is an unright ansen for this hest", "commands.attribute.failed.modifier_already_present": "Tweaker %s is already anward on mark %s for ansen %s", "commands.attribute.failed.no_attribute": "Ansen %s has no mark %s", "commands.attribute.failed.no_modifier": "Mark %s for ansen %s has no tweaker %s", "commands.attribute.modifier.add.success": "Eked tweaker %s to mark %s for ansen %s", "commands.attribute.modifier.remove.success": "Fornimmed tweaker %s from mark %s for ansen %s", "commands.attribute.modifier.value.get.success": "Worth of tweaker %s on mark %s for ansen %s is %s", "commands.attribute.value.get.success": "Worth of mark %s for ansen %s is %s", "commands.ban.failed": "Nothing wended. The player is already forbidden", "commands.ban.success": "Forbidden %s: %s", "commands.banip.failed": "Nothing wended. That WF is already forbidden", "commands.banip.info": "This forbode onworks %s player(s): %s", "commands.banip.invalid": "Unright WF or unknown player", "commands.banip.success": "Forbidden WF %s: %s", "commands.banlist.entry": "%s was forbidden by %s: %s", "commands.banlist.entry.unknown": "(Unknown)", "commands.banlist.list": "There are %s forbode(s):", "commands.banlist.none": "There are no forbodes", "commands.bossbar.create.failed": "A highfiendband already bestands with the IHOOD '%s'", "commands.bossbar.create.success": "Made bespoke highfiendband %s", "commands.bossbar.get.max": "Bespoke highfiendband %s has a highest of %s", "commands.bossbar.get.players.none": "Bespoke highfiendband %s has no players anwardly onweb", "commands.bossbar.get.players.some": "Bespoke highfiendband %s has %s player(s) anwardly onweb: %s", "commands.bossbar.get.value": "Bespoke highfiendband %s has a worth of %s", "commands.bossbar.get.visible.hidden": "Bespoke highfiendband %s is anwardly hided", "commands.bossbar.get.visible.visible": "Bespoke highfiendband %s is being anwardly shown", "commands.bossbar.list.bars.none": "There are no bespoke highfiendbands astirred", "commands.bossbar.list.bars.some": "There are %s bespoke highfiendband(s) astirred: %s", "commands.bossbar.remove.success": "Fornimmed bespoke highfiendband %s", "commands.bossbar.set.color.success": "Bespoke highfiendband %s has wended hue", "commands.bossbar.set.color.unchanged": "Nothing wended. That's already the hue of this highfiendband", "commands.bossbar.set.max.success": "Bespoke highfiendband %s has wended highest to %s", "commands.bossbar.set.max.unchanged": "Nothing wended. That's already the highest of this highfiendband", "commands.bossbar.set.name.success": "Bespoke highfiendband %s has had a wend of name", "commands.bossbar.set.name.unchanged": "Nothing wended. That's already the name of this highfiendband", "commands.bossbar.set.players.success.none": "Bespoke highfiendband %s no longer has any players", "commands.bossbar.set.players.success.some": "Bespoke highfiendband %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Nothing wended. Those players are already on the highfiendband with no one to eke or fornim", "commands.bossbar.set.style.success": "Bespoke highfiendband %s has wended kind", "commands.bossbar.set.style.unchanged": "Nothing wended. That's already the kind of this highfiendband", "commands.bossbar.set.value.success": "Bespoke highfiendband %s has wended worth to %s", "commands.bossbar.set.value.unchanged": "Nothing wended. That's already the worth of this highfiendband", "commands.bossbar.set.visibility.unchanged.hidden": "Nothing wended. The highfiendband is already hided", "commands.bossbar.set.visibility.unchanged.visible": "Nothing wended. The highfiendband is already seenly", "commands.bossbar.set.visible.success.hidden": "Bespoke highfiendband %s is now hided", "commands.bossbar.set.visible.success.visible": "Bespoke highfiendband %s is now seenly", "commands.bossbar.unknown": "No highfiendband bestands with the IHOOD '%s'", "commands.clear.success.multiple": "Emptied %s thing(s) from %s players", "commands.clear.success.single": "Emptied %s thing(s) from player %s", "commands.clear.test.multiple": "Found %s matching thing(s) on %s players", "commands.clear.test.single": "Found %s matching thing(s) on player %s", "commands.clone.failed": "No clots were twinned", "commands.clone.overlap": "The from and goal spots cannot overlap", "commands.clone.success": "Speedfully twinned %s clot(s)", "commands.clone.toobig": "Too many clots in the narrowed spot (highest %s, narrowed %s)", "commands.damage.invulnerable": "<PERSON> is shielded from the yeaven harm kind", "commands.damage.success": "Besought %s harm to %s", "commands.data.block.get": "%s on cleat %s, %s, %s, after meter difference of %s is %s", "commands.data.block.invalid": "The marked clot is not a clot ansen", "commands.data.block.modified": "Wended clot lore of %s, %s, %s", "commands.data.block.query": "%s, %s, %s has the following clot lore: %s", "commands.data.entity.get": "%s on %s after mete difference of %s is %s", "commands.data.entity.invalid": "Cannot wend player lore", "commands.data.entity.modified": "Wended ansen lore of %s", "commands.data.entity.query": "%s has the following ansen lore: %s", "commands.data.get.invalid": "Can't reap %s; only rimish tokens are aleaved", "commands.data.get.multiple": "This flite bears one NTT worth", "commands.data.get.unknown": "Can't reap %s; token doesn't bestand", "commands.data.merge.failed": "Nothing wended. The narrowed holdings already have these worths", "commands.data.modify.expected_list": "Abode list, reaped: %s", "commands.data.modify.expected_object": "Abode thing, reaped: %s", "commands.data.modify.expected_value": "Abode worth, reaped: %s", "commands.data.modify.invalid_index": "Unright list lead: %s", "commands.data.modify.invalid_substring": "Unright understring leads: %s to %s", "commands.data.storage.get": "%s in stow %s after meter difference of %s is %s", "commands.data.storage.modified": "Wended stow %s", "commands.data.storage.query": "Stow %s has the following inholdings: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already bestands", "commands.datapack.create.invalid_full_name": "Unright new pack name '%s'", "commands.datapack.create.invalid_name": "Unright staves in new pack name '%s'", "commands.datapack.create.io_failure": "Can't make pack with name '%s', soothe daybooks", "commands.datapack.create.metadata_encode_failure": "Could not inrown umblore for pack with name '%s': %s", "commands.datapack.create.success": "Made new empty pack with name '%s'", "commands.datapack.disable.failed": "Pack '%s' is not on!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be laid off, since it is part of a flag which is laid on!", "commands.datapack.enable.failed": "Pack '%s' is already on!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be laid on, since foreneeded flags are not laid on in this world: %s!", "commands.datapack.list.available.none": "There are no more lore packs at hand", "commands.datapack.list.available.success": "There are %s lore pack(s) at hand: %s", "commands.datapack.list.enabled.none": "There are no lore packs on", "commands.datapack.list.enabled.success": "There are %s lore pack(s) on: %s", "commands.datapack.modify.disable": "Laying off lore pack %s", "commands.datapack.modify.enable": "Laying on lore pack %s", "commands.datapack.unknown": "Unknown lore pack '%s'", "commands.debug.alreadyRunning": "The tick forefinder is already started", "commands.debug.function.noRecursion": "Can't follow from inside of working", "commands.debug.function.noReturnRun": "Following can't be brooked with return run", "commands.debug.function.success.multiple": "Followed %s hest(s) from %s workings to output thread %s", "commands.debug.function.success.single": "Followed %s hest(s) from working '%s' to output thread %s", "commands.debug.function.traceFailed": "Could not follow working", "commands.debug.notRunning": "The tick forefinder hasn't started", "commands.debug.started": "Started tick forefinding", "commands.debug.stopped": "Stopped tick forefinding after %s braid(s) and %s tick(s) (%s tick(s)/braid)", "commands.defaultgamemode.success": "The stock game wayset is now %s", "commands.deop.failed": "Nothing wended. The play is not an overseer", "commands.deop.success": "Made %s no longer an outreckoner overseer", "commands.dialog.clear.multiple": "Emptied talk opener for %s players", "commands.dialog.clear.single": "Emptied talk opener for %s", "commands.dialog.show.multiple": "Showed talk opener to %s players", "commands.dialog.show.single": "Showed talk opener to %s", "commands.difficulty.failure": "The toughness did not wend; it is already set to %s", "commands.difficulty.query": "The toughness is %s", "commands.difficulty.success": "The toughness has been set to %s", "commands.drop.no_held_items": "<PERSON><PERSON> can't hold any things", "commands.drop.no_loot_table": "Ansen %s has no meeds set", "commands.drop.no_loot_table.block": "Clot %s has no meeds set", "commands.drop.success.multiple": "Dropped %s things", "commands.drop.success.multiple_with_table": "Dropped %s things from meeds set %s", "commands.drop.success.single": "Dropped %s %s", "commands.drop.success.single_with_table": "Dropped %s %s from meeds set %s", "commands.effect.clear.everything.failed": "<PERSON> has no rines to fornim", "commands.effect.clear.everything.success.multiple": "Emptied off every rine from %s marks", "commands.effect.clear.everything.success.single": "Emptied off every rine from %s", "commands.effect.clear.specific.failed": "<PERSON> doesn't have the asked rine", "commands.effect.clear.specific.success.multiple": "Emptied off rine %s from %s marks", "commands.effect.clear.specific.success.single": "Emptied off rine %s from %s", "commands.effect.give.failed": "Cannot beseech this rine (mark is either shielded to rines, or has something stronger)", "commands.effect.give.success.multiple": "Besought rine %s to %s marks", "commands.effect.give.success.single": "Besought rine %s to %s", "commands.enchant.failed": "Nothing wended. Marks either have nothing in hir hands or the galder could not be besought", "commands.enchant.failed.entity": "%s is an unright ansen for this hest", "commands.enchant.failed.incompatible": "%s cannot uphold that galder", "commands.enchant.failed.itemless": "%s is not holding any thing", "commands.enchant.failed.level": "%s is higher than the top layer of %s upheld by that galder", "commands.enchant.success.multiple": "Besought galder %s to %s ansens", "commands.enchant.success.single": "Besought galder %s to %s's thing", "commands.execute.blocks.toobig": "Too many clots in the narrowed spot (highest %s, narrowed %s)", "commands.execute.conditional.fail": "Fand trucked", "commands.execute.conditional.fail_count": "Fand trucked, rime: %s", "commands.execute.conditional.pass": "Fand byfared", "commands.execute.conditional.pass_count": "Fand byfared, rime: %s", "commands.execute.function.instantiationFailure": "Could not set working %s: %s", "commands.experience.add.levels.success.multiple": "Yave %s cunning layers to %s players", "commands.experience.add.levels.success.single": "Yave %s cunning layers to %s", "commands.experience.add.points.success.multiple": "Yave %s cunning ords to %s players", "commands.experience.add.points.success.single": "Yave %s cunning ords to %s", "commands.experience.query.levels": "%s has %s cunning layers", "commands.experience.query.points": "%s has %s cunning ords", "commands.experience.set.levels.success.multiple": "Set %s cunning layers on %s players", "commands.experience.set.levels.success.single": "Set %s cunning layers on %s", "commands.experience.set.points.invalid": "<PERSON>not set cunning ords above the highest ords for the player's anward layer", "commands.experience.set.points.success.multiple": "Set %s cunning ords on %s players", "commands.experience.set.points.success.single": "Set %s cunning ords on %s", "commands.fill.failed": "No clots were filled", "commands.fill.success": "Speedfully filled %s clot(s)", "commands.fill.toobig": "Too many clots in the narrowed spot (highest %s, narrowed %s)", "commands.fillbiome.success": "Lifeheaps set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s lifeheap thing(s) set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many clots in the narrowed roomth (highest %s, narrowed %s)", "commands.forceload.added.failure": "No worldstetches were marked for fordrive loading", "commands.forceload.added.multiple": "Marked %s worldstetches in %s from %s to %s to be loaded with fordrive", "commands.forceload.added.none": "No worldstetches loaded with fordrive were found in %s", "commands.forceload.added.single": "Marked worldstetch %s in %s to be loaded with fordrive", "commands.forceload.list.multiple": "%s worldstetches loaded with fordrive were found in %s at: %s", "commands.forceload.list.single": "A worldstetch loaded with fordrive was found in %s at: %s", "commands.forceload.query.failure": "Worldstetch at %s in %s is not marked to be loaded with fordrive", "commands.forceload.query.success": "Worldstetch at %s in %s is marked to be loaded with fordrive", "commands.forceload.removed.all": "Unmarked all worldstetches loaded with fordrive in %s", "commands.forceload.removed.failure": "No worldstetches were fornimmed from loading with fordrive", "commands.forceload.removed.multiple": "Unmarked %s worldstetches in %s from %s to %s for loading with fordrive", "commands.forceload.removed.single": "Unmarked worldstetch %s in %s for loading with fordrive", "commands.forceload.toobig": "Too many worldstetches in the narrowed spot (highest %s, narrowed %s)", "commands.function.error.argument_not_compound": "Unright flite kind: %s, abode Compound", "commands.function.error.missing_argument": "Missing flite %2$s to working %1$s", "commands.function.error.missing_arguments": "Missing flites to working %s", "commands.function.error.parse": "While setting long-behest %s: Hest '%s' made dwale: %s", "commands.function.instantiationFailure": "Could not set working %s: %s", "commands.function.result": "Working %s sent back %s", "commands.function.scheduled.multiple": "Running working %s", "commands.function.scheduled.no_functions": "Can't find any working for the name %s", "commands.function.scheduled.single": "Running working %s", "commands.function.success.multiple": "Ran %s hest(s) from %s workings", "commands.function.success.multiple.result": "Ran %s workings", "commands.function.success.single": "Ran %s hest(s) from working '%s'", "commands.function.success.single.result": "Working '%2$s' sent back %1$s", "commands.gamemode.success.other": "Set %s's game wayset to %s", "commands.gamemode.success.self": "Set own game wayset to %s", "commands.gamerule.query": "Gamewield %s is now set to: %s", "commands.gamerule.set": "Gamewield %s is now set to: %s", "commands.give.failed.toomanyitems": "Can't yeave more than %s of %s", "commands.give.success.multiple": "Yave %s %s to %s players", "commands.give.success.single": "Yave %s %s to %s", "commands.help.failed": "Unknown hest or not enough thaving", "commands.item.block.set.success": "Swapped out a groovestead at %s, %s, %s with %s", "commands.item.entity.set.success.multiple": "Swapped out a groovestead on %s ansens with %s", "commands.item.entity.set.success.single": "Swapped out a groovestead on %s with %s", "commands.item.source.no_such_slot": "The ordfrom does not have groovestead %s", "commands.item.source.not_a_container": "Ordfrom stow %s, %s, %s is not an inholder", "commands.item.target.no_changed.known_item": "No marks borne thing %s into groovestead %s", "commands.item.target.no_changes": "No marks borne thing into groovestead %s", "commands.item.target.no_such_slot": "The mark does not have groovestead %s", "commands.item.target.not_a_container": "Mark stow %s, %s, %s is not an inholder", "commands.jfr.dump.failed": "Could not shift JFR writing: %s", "commands.jfr.start.failed": "Could not start JFR forefinding", "commands.jfr.started": "JFR forefinding started", "commands.jfr.stopped": "JFR forefinding stopped and shifted to %s", "commands.kick.owner.failed": "Cannot throw outreckoner owner out in NSN game", "commands.kick.singleplayer.failed": "Cannot throw out in an offweb oneplayer game", "commands.kick.success": "Thrown out %s: %s", "commands.kill.success.multiple": "Killed %s ansens", "commands.kill.success.single": "Killed %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "There are %s of a highest of %s players onweb: %s", "commands.locate.biome.not_found": "Could not find a lifeheap of kind \"%s\" within well-grounded farness", "commands.locate.biome.success": "The nearest %s is at %s (%s m away)", "commands.locate.poi.not_found": "Could not find an ord of indraw of kind \"%s\" within well-grounded farness", "commands.locate.poi.success": "The nearest %s is at %s (%s m away)", "commands.locate.structure.invalid": "There is no framework with kind \"%s\"", "commands.locate.structure.not_found": "Could not find a framework of kind \"%s\" nearby", "commands.locate.structure.success": "The nearest %s is at %s (%s m away)", "commands.message.display.incoming": "%s whispers to you: %s", "commands.message.display.outgoing": "You whisper to %s: %s", "commands.op.failed": "Nothing wended. The player already is an overseer", "commands.op.success": "Made %s a outreckoner overseer", "commands.pardon.failed": "Nothing wended. The player isn't forbidden", "commands.pardon.success": "Unforbidden %s", "commands.pardonip.failed": "Nothing wended. That WF isn't forbidden", "commands.pardonip.invalid": "Unright WF", "commands.pardonip.success": "Unforbidden WF %s", "commands.particle.failed": "The speck was not seenly for anybody", "commands.particle.success": "Showing speck %s", "commands.perf.alreadyRunning": "The speed forefinder is already started", "commands.perf.notRunning": "The speed forefinder hasn't started", "commands.perf.reportFailed": "Could not make unbug mield", "commands.perf.reportSaved": "Made unbug mield in %s", "commands.perf.started": "Started 10 braid speed forefinding run (brook '/perf stop' to stop early)", "commands.perf.stopped": "Stopped speed forefinding after %s braid(s) and %s tick(s) (%s tick(s)/braid)", "commands.place.feature.failed": "Could not lay mark", "commands.place.feature.invalid": "There is no mark with kind \"%s\"", "commands.place.feature.success": "Laid \"%s\" at %s, %s, %s", "commands.place.jigsaw.failed": "Could not beyet inlock", "commands.place.jigsaw.invalid": "There is no forelay pool with kind \"%s\"", "commands.place.jigsaw.success": "Beyot inlock at %s, %s, %s", "commands.place.structure.failed": "Could not lay framework", "commands.place.structure.invalid": "There is no framework with kind \"%s\"", "commands.place.structure.success": "Beyot framework \"%s\" at %s, %s, %s", "commands.place.template.failed": "Could not lay forelay", "commands.place.template.invalid": "There is no forelay with ihood \"%s\"", "commands.place.template.success": "Loaded forelay \"%s\" at %s, %s, %s", "commands.playsound.failed": "The loud is too far away to be heard", "commands.playsound.success.multiple": "Played loud %s to %s players", "commands.playsound.success.single": "Played loud %s to %s", "commands.publish.alreadyPublished": "Maniplayer game is already drighted on %s harbor", "commands.publish.failed": "Cannot dright a neigh game", "commands.publish.started": "Neigh game drighted on %s harbor", "commands.publish.success": "Maniplayer game is now drighted on %s harbor", "commands.random.error.range_too_large": "The breadth of hapsome worth must be at most 2147483646", "commands.random.error.range_too_small": "The breadth of hapsome worth must be at least 2", "commands.random.reset.all.success": "Edset %s hapsome string(s)", "commands.random.reset.success": "Edset hapsome string %s", "commands.random.roll": "%s reaped %s (from %s and %s)", "commands.random.sample.success": "Hapsome worth: %s", "commands.recipe.give.failed": "No new knowledge were learned", "commands.recipe.give.success.multiple": "Unlocked %s knowledge for %s players", "commands.recipe.give.success.single": "Unlocked %s knowledge for %s", "commands.recipe.take.failed": "No knowledge could be unknown", "commands.recipe.take.success.multiple": "Nimmed %s knowledge from %s players", "commands.recipe.take.success.single": "Nimmed %s knowledge from %s", "commands.reload.failure": "Edload trucked, keeping old lore", "commands.reload.success": "Edloading!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "Players can't be ridden", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "<PERSON><PERSON> can't ride on itself or any of its riders", "commands.ride.mount.failure.wrong_dimension": "Can't ride ansen in another farstead", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any craft", "commands.rotate.success": "Wharved %s", "commands.save.alreadyOff": "<PERSON><PERSON> is already off", "commands.save.alreadyOn": "Nering is already on", "commands.save.disabled": "Self-nering is now off", "commands.save.enabled": "Self-nering is now on", "commands.save.failed": "Cannot keep the game (Is there enough harddrive room?)", "commands.save.saving": "Nering the game (this may last a little time!)", "commands.save.success": "Kept the game", "commands.schedule.cleared.failure": "No earmarks with namekey %s", "commands.schedule.cleared.success": "Emptied %s earmark(s) with namekey %s", "commands.schedule.created.function": "Earmarked working '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Earmarked token '%s' in %s tick(s) at gametime %s", "commands.schedule.macro": "Can't earmark a long-behest", "commands.schedule.same_tick": "Can't earmark for anward tick", "commands.scoreboard.objectives.add.duplicate": "A goal already bestands by that name", "commands.scoreboard.objectives.add.success": "Made new goal %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nothing wended. That show groove<PERSON> is already empty", "commands.scoreboard.objectives.display.alreadySet": "Nothing changed. That show <PERSON><PERSON> is already showing that goal", "commands.scoreboard.objectives.display.cleared": "Emptied any goals in show groovestead %s", "commands.scoreboard.objectives.display.set": "Set show groovestead %s to show goal %s", "commands.scoreboard.objectives.list.empty": "There are no goals", "commands.scoreboard.objectives.list.success": "There are %s goal(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Laid off show self-anwarden for goal %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Laid on show self-anwarden for goal %s", "commands.scoreboard.objectives.modify.displayname": "Wended the show name of %s to %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Emptied stock rime layout of goal %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Wended stock rime layout of goal %s", "commands.scoreboard.objectives.modify.rendertype": "Wended the draw kind of goal %s", "commands.scoreboard.objectives.remove.success": "Fornimmed goal %s", "commands.scoreboard.players.add.success.multiple": "Eked %s to %s for %s ansens", "commands.scoreboard.players.add.success.single": "Eked %s to %s for %s (now %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Emptied show name for %s ansens in %s", "commands.scoreboard.players.display.name.clear.success.single": "Emptied show name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Wended show name to %s for %s ansens in %s", "commands.scoreboard.players.display.name.set.success.single": "Wended show name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Emptied rime layout for %s ansens in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Emptied rime layout for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Wended rime layout for %s ansens in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Wended rime layout for %s in %s", "commands.scoreboard.players.enable.failed": "Nothing wended. That trigger is already on", "commands.scoreboard.players.enable.invalid": "Lay on only works on trigger-goals", "commands.scoreboard.players.enable.success.multiple": "Laid on trigger %s for %s ansen", "commands.scoreboard.players.enable.success.single": "Laid on trigger %s for %s", "commands.scoreboard.players.get.null": "Can't reap worth of %s for %s; none is set", "commands.scoreboard.players.get.success": "%s has %s %s", "commands.scoreboard.players.list.empty": "There are no marked ansens", "commands.scoreboard.players.list.entity.empty": "%s has no stands to show", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s stand(s):", "commands.scoreboard.players.list.success": "There are %s no marked ansen(s): %s", "commands.scoreboard.players.operation.success.multiple": "Anwardened %s for %s ansens", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "Emptied %s from %s for %s ansens", "commands.scoreboard.players.remove.success.single": "Emptied %s from %s for %s (now %s)", "commands.scoreboard.players.reset.all.multiple": "Edset all stands for %s ansens", "commands.scoreboard.players.reset.all.single": "Edset all stands for %s", "commands.scoreboard.players.reset.specific.multiple": "Edset %s for %s ansens", "commands.scoreboard.players.reset.specific.single": "Edset %s for %s", "commands.scoreboard.players.set.success.multiple": "Set %s for %s ansens to %s", "commands.scoreboard.players.set.success.single": "Set %s for %s to %s", "commands.seed.success": "Seed: %s", "commands.setblock.failed": "Could not set the clot", "commands.setblock.success": "Wended the clot at %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s stoundle(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now laid off", "commands.setworldspawn.failure.not_overworld": "Can only set the world starting ord for overworld", "commands.setworldspawn.success": "Set the world starting ord to %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Set starting ord to %s, %s, %s [%s] in %s for %s players", "commands.spawnpoint.success.single": "Set starting ord to %s, %s, %s [%s] in %s for %s", "commands.spectate.not_spectator": "%s is not in onlooker wayset", "commands.spectate.self": "Cannot onlook yourself", "commands.spectate.success.started": "Now onlooking %s", "commands.spectate.success.stopped": "No longer onlooking an ansen", "commands.spreadplayers.failed.entities": "Could not spread %s ansen(s) umb %s, %s (too many ansens for room - fand brooking a spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Unright maxHeight %s; abode higher than world least %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) umb %s, %s (too many ansens for room - fand brooking spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s ansen(s) umb %s, %s with a middling length of %s clot(s) asunder", "commands.spreadplayers.success.teams": "Spread %s team(s) umb %s, %s with a middling length of %s clot(s) asunder", "commands.stop.stopping": "Stopping the outreckoner", "commands.stopsound.success.source.any": "Stopped all %s' louds", "commands.stopsound.success.source.sound": "Stopped loud '%s' on ordfrom '%s'", "commands.stopsound.success.sourceless.any": "Stopped all louds", "commands.stopsound.success.sourceless.sound": "Stopped loud %s'", "commands.summon.failed": "Cannot beckon ansen", "commands.summon.failed.uuid": "Cannot beckon ansen owing to twofold UUIDs", "commands.summon.invalidPosition": "Unright stow for beck", "commands.summon.success": "Beckoned new %s", "commands.tag.add.failed": "<PERSON> either already has the token or has too many tokens", "commands.tag.add.success.multiple": "Eked token '%s' to %s ansens", "commands.tag.add.success.single": "Eked token '%s' to %s", "commands.tag.list.multiple.empty": "There are no tokens on the %s ansens", "commands.tag.list.multiple.success": "The %s ansens have %s overall tokens: %s", "commands.tag.list.single.empty": "%s has no tokens", "commands.tag.list.single.success": "%s has %s tokens: %s", "commands.tag.remove.failed": "<PERSON> does not have this token", "commands.tag.remove.success.multiple": "Fornimmed token '%s' from %s ansens", "commands.tag.remove.success.single": "Fornimmed token '%s' from %s", "commands.team.add.duplicate": "A team already bestands by that name", "commands.team.add.success": "Made team %s", "commands.team.empty.success": "Fornimmed %s belonger(s) from team %s", "commands.team.empty.unchanged": "Nothing wended. That team is already empty", "commands.team.join.success.multiple": "Eked %s belongers to team %s", "commands.team.join.success.single": "Eked %s to team %s", "commands.team.leave.success.multiple": "Fornimmed %s belongers from any team", "commands.team.leave.success.single": "Fornimmed %s from any team", "commands.team.list.members.empty": "There are no belongers on team %s", "commands.team.list.members.success": "Team %s has %s belonger(s): %s", "commands.team.list.teams.empty": "There are no teams", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "Thrack wield for team %s is now \"%s\"", "commands.team.option.collisionRule.unchanged": "Nothing wended. Thrack wield is already at that worth", "commands.team.option.color.success": "Anwardened the hue for team %s to %s", "commands.team.option.color.unchanged": "Nothing wended. That team already has that hue", "commands.team.option.deathMessageVisibility.success": "Death writ seenliness for team %s is now \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothing wended. Death writ seenliness is already that worth", "commands.team.option.friendlyfire.alreadyDisabled": "Nothing wended. Friendly fire is already off for that team", "commands.team.option.friendlyfire.alreadyEnabled": "Nothing wended. Friendly fire is already on for that team", "commands.team.option.friendlyfire.disabled": "Laid off friendly fire for team %s", "commands.team.option.friendlyfire.enabled": "Laid on friendly fire for team %s", "commands.team.option.name.success": "Anwardened the name of team %s", "commands.team.option.name.unchanged": "Nothing wended. That team already has that name", "commands.team.option.nametagVisibility.success": "<PERSON>token seenliness for team %s is now \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nothing wended. Nametoken seenliness is already at that worth", "commands.team.option.prefix.success": "Team forefastening set to %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nothing wended. That team already can't see unseenly teammates", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nothing wended. That team can already see unseenly teammates", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s can no longer see unseenly teammates", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s can now see unseenly teammates", "commands.team.option.suffix.success": "Team endfastening set to %s", "commands.team.remove.success": "Fornimmed team %s", "commands.teammsg.failed.noteam": "You must be on a team to writ your team", "commands.teleport.invalidPosition": "Unright stow for farferry", "commands.teleport.success.entity.multiple": "Farferried %s ansens to %s", "commands.teleport.success.entity.single": "Farferried %s to %s", "commands.teleport.success.location.multiple": "Farferried %s ansens to %s, %s, %s", "commands.teleport.success.location.single": "Farferried %s to %s, %s, %s", "commands.test.batch.starting": "Starting umbworld %s batch %s", "commands.test.clear.error.no_tests": "Could not find any fands to empty", "commands.test.clear.success": "Emptied %s framework(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to clove to shearboard", "commands.test.create.success": "Made fand setup for fand %s", "commands.test.error.no_test_containing_pos": "Can't find a fand befalling that has %s, %s, %s", "commands.test.error.no_test_instances": "Found no fand befallings", "commands.test.error.non_existant_test": "Fand %s could not be found", "commands.test.error.structure_not_found": "Fand framework %s could not be found", "commands.test.error.test_instance_not_found": "Fand befalling clot ansen could not be found", "commands.test.error.test_instance_not_found.position": "Fand befalling clot ansen could not be found for fand at %s, %s, %s", "commands.test.error.too_large": "The framework breadth must be less than %s clots along each axle", "commands.test.locate.done": "Done finding, found %s framework(s)", "commands.test.locate.found": "Found framework at: %s (length: %s)", "commands.test.locate.started": "Started finding fand frameworks, this might need a while...", "commands.test.no_tests": "No fands to run", "commands.test.relative_position": "Stow akin to %s: %s", "commands.test.reset.error.no_tests": "Could not find any fands to edset", "commands.test.reset.success": "Edset %s framework(s)", "commands.test.run.no_tests": "No fands found", "commands.test.run.running": "Running %s fand(s)...", "commands.test.summary": "Game Fand fuldone! %s fand(s) were run", "commands.test.summary.all_required_passed": "All needed fands byfared :)", "commands.test.summary.failed": "%s needed fand(s) trucked :(", "commands.test.summary.optional_failed": "%s kirely fand(s) trucked", "commands.tick.query.percentiles": "Hundredmeals: 50: %s thousandths of a braid 95: %s thousandths of a braid 99: %s thousandths of a braid, byspel: %s", "commands.tick.query.rate.running": "Mark tick speed: %s per braid.\nMiddling time per tick: %s thousandths of a braid (Mark: %s thousandths of a braid)", "commands.tick.query.rate.sprinting": "Mark tick speed: %s per braid (unheeded, underlying only).\nMiddling time for tick: %s thousandths of a braid", "commands.tick.rate.success": "Set the mark tick speed to %s per braid", "commands.tick.sprint.report": "Fastrun done with %s ticks per braid, or %s thousandths of a braid per tick", "commands.tick.sprint.stop.fail": "No tick fastrun now", "commands.tick.sprint.stop.success": "Broken in the anward tick fastrun", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can't keep up with the mark tick speed", "commands.tick.status.running": "The game is running wontedly", "commands.tick.status.sprinting": "The game is fastrunning", "commands.tick.step.fail": "Cannot step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step now", "commands.tick.step.stop.success": "Broken in the anward tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "The time is %s", "commands.time.set": "Set the time to %s", "commands.title.cleared.multiple": "Emptied names for %s players", "commands.title.cleared.single": "Emptied names for %s", "commands.title.reset.multiple": "Edset name kires for %s players", "commands.title.reset.single": "Edset name kires for %s", "commands.title.show.actionbar.multiple": "Showing new doingband name for %s players", "commands.title.show.actionbar.single": "Showing new doingband name for %s", "commands.title.show.subtitle.multiple": "Showing new undersetting for %s players", "commands.title.show.subtitle.single": "Showing new undersetting for %s", "commands.title.show.title.multiple": "Showing new header for %s players", "commands.title.show.title.single": "Showing new header for %s", "commands.title.times.multiple": "Changed header show times for %s players", "commands.title.times.single": "Changed header show times for %s", "commands.transfer.error.no_players": "Must narrow at least one player to shift", "commands.transfer.success.multiple": "Shifting %s players to %s:%s", "commands.transfer.success.single": "Shifting %s to %s:%s", "commands.trigger.add.success": "Triggered %s (eked %s to worth)", "commands.trigger.failed.invalid": "You can only trigger goals that are 'trigger' kind", "commands.trigger.failed.unprimed": "You cannot trigger this goal yet", "commands.trigger.set.success": "Triggered %s (set worth to %s)", "commands.trigger.simple.success": "Triggered %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No wayords in %s", "commands.waypoint.list.success": "%s wayord(s) in %s: %s", "commands.waypoint.modify.color": "Wayord hue is now %s", "commands.waypoint.modify.color.reset": "<PERSON><PERSON> wayord hue", "commands.waypoint.modify.style": "Wayord kind wended", "commands.weather.set.clear": "Set the weather to smolt", "commands.weather.set.rain": "Set the weather to rain", "commands.weather.set.thunder": "Set the weather to rain & thunder", "commands.whitelist.add.failed": "Player is already whitelisted", "commands.whitelist.add.success": "Eked %s to the whitelist", "commands.whitelist.alreadyOff": "Whitelist is already off", "commands.whitelist.alreadyOn": "Whitelist is already on", "commands.whitelist.disabled": "Whitelist is now off", "commands.whitelist.enabled": "Whitelist is now on", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "There are no whitelisted players", "commands.whitelist.reloaded": "Edloaded the whitelist", "commands.whitelist.remove.failed": "Player is not whitelisted", "commands.whitelist.remove.success": "Emptied %s off the whitelist", "commands.worldborder.center.failed": "Nothing wended. The world rim is already middled there", "commands.worldborder.center.success": "Set the middle of the world rim to %s, %s", "commands.worldborder.damage.amount.failed": "Nothing wended. The world rim harm is already at that deal", "commands.worldborder.damage.amount.success": "Set the world rim harm to %s for each block each braid", "commands.worldborder.damage.buffer.failed": "Nothing wended. The world rim harm bulwark is already that length", "commands.worldborder.damage.buffer.success": "Set the world rim harm bulwark to %sm", "commands.worldborder.get": "The world rim is now %sm wide", "commands.worldborder.set.failed.big": "World rim cannot be more than %s clots wide", "commands.worldborder.set.failed.far": "World rim cannot be further out than %s clots", "commands.worldborder.set.failed.nochange": "Nothing wended. The world rim is already at that breadth", "commands.worldborder.set.failed.small": "World rim cannot be smaller than 1 m wide", "commands.worldborder.set.grow": "Growing the world rim to %s blocks wide over %s braids", "commands.worldborder.set.immediate": "Set the world rim to %sm wide", "commands.worldborder.set.shrink": "Shrinking the world rim to %sm wide over %s braid(s)", "commands.worldborder.warning.distance.failed": "Nothing wended. The world rim warning is already that length", "commands.worldborder.warning.distance.success": "Set the world rim warning length to %sm", "commands.worldborder.warning.time.failed": "Nothing wended. The world rim warning is already at that deal of time", "commands.worldborder.warning.time.success": "Set the world rim warning time to %s braid(s)", "compliance.playtime.greaterThan24Hours": "You've been playing for greater than 24 stounds", "compliance.playtime.hours": "You've been playing for %s stound(s)", "compliance.playtime.message": "Overboard gaming may rine at wonted daily life", "connect.aborted": "Stopped", "connect.authorizing": "Going in...", "connect.connecting": "Binding with the outreckoner...", "connect.encrypting": "Inrowning...", "connect.failed": "Could not bind with the outreckoner", "connect.failed.transfer": "Binding trucked while shifting to the outreckoner", "connect.joining": "Faying world...", "connect.negotiating": "Dealing...", "connect.reconfiging": "Edsetting up...", "connect.reconfiguring": "Edsetting up...", "connect.transferring": "Shifting to new outreckoner...", "container.barrel": "Vat", "container.beacon": "Beacon", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Blast Oven", "container.brewing": "Brewing Stand", "container.cartography_table": "Drafting Bench", "container.chest": "Chest", "container.chestDouble": "Great Chest", "container.crafter": "Crafter", "container.crafting": "Crafting", "container.creative": "<PERSON> Choosing", "container.dispenser": "<PERSON>hrow<PERSON>", "container.dropper": "Dropper", "container.enchant": "Gale", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Hewnstone", "container.enchant.lapis.one": "1 Hewnstone", "container.enchant.level.many": "%s Galdercraft Layers", "container.enchant.level.one": "1 Galdercraft Layer", "container.enchant.level.requirement": "Layer Foreneed: %s", "container.enderchest": "<PERSON><PERSON> Chest", "container.furnace": "Oven", "container.grindstone_title": "Fettle & Ungale", "container.hopper": "<PERSON>", "container.inventory": "Inholding", "container.isLocked": "%s is locked!", "container.lectern": "Reading Stand", "container.loom": "Loom", "container.repair": "Fettle & Name", "container.repair.cost": "Galdercraft Fee: %1$s", "container.repair.expensive": "Too High of a Fee!", "container.shulkerBox": "Shulker Pack", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "and %s more...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "Cannot open. Me<PERSON> not beyotten yet.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Better Things", "container.upgrade.error_tooltip": "Thing can't be bolstered in this way", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON>", "controls.keybinds": "Key Binds...", "controls.keybinds.duplicateKeybinds": "This key is also brooked for:\n%s", "controls.keybinds.title": "Key Binds", "controls.reset": "<PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON>", "controls.title": "Steerings", "createWorld.customize.buffet.biome": "Kindly choose a lifeheap", "createWorld.customize.buffet.title": "Onefold Lifeheap Bespeaking", "createWorld.customize.flat.height": "Height", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bottom - %s", "createWorld.customize.flat.layer.top": "Top - %s", "createWorld.customize.flat.removeLayer": "Fornim Layer", "createWorld.customize.flat.tile": "Layer Anwork", "createWorld.customize.flat.title": "Sinflat Bespeaking", "createWorld.customize.presets": "Foresets", "createWorld.customize.presets.list": "Otherwise, here are some we made earlier!", "createWorld.customize.presets.select": "<PERSON>", "createWorld.customize.presets.share": "Wish to share your foreset with someone? Brook the box underneath!", "createWorld.customize.presets.title": "<PERSON><PERSON> a <PERSON>", "createWorld.preparing": "Readying for world making...", "createWorld.tab.game.title": "Game", "createWorld.tab.more.title": "More", "createWorld.tab.world.title": "World", "credits_and_attribution.button.attribution": "Ownship", "credits_and_attribution.button.credits": "Acknowledgings", "credits_and_attribution.button.licenses": "Leaves", "credits_and_attribution.screen.title": "Acknowledgings and Ownship", "dataPack.bundle.description": "Lays on fandly Bundle thing", "dataPack.bundle.name": "Bundles", "dataPack.locator_bar.description": "Show the bearing of other players in maniplayer", "dataPack.locator_bar.name": "Finder Band", "dataPack.minecart_improvements.description": "Bolstered shrithing for <PERSON><PERSON><PERSON>s", "dataPack.minecart_improvements.name": "<PERSON><PERSON><PERSON>", "dataPack.redstone_experiments.description": "Fandly Redstone wends", "dataPack.redstone_experiments.name": "Redstone Fands", "dataPack.title": "Choose Lore Packs", "dataPack.trade_rebalance.description": "Anward<PERSON> wrixles for Thorps<PERSON>", "dataPack.trade_rebalance.name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "dataPack.update_1_20.description": "New marks and inholding for Minecraft 1.20", "dataPack.update_1_20.name": "Anwardening 1.20", "dataPack.update_1_21.description": "New marks and inholding for Minecraft 1.21", "dataPack.update_1_21.name": "Anwardening 1.21", "dataPack.validation.back": "Go Back", "dataPack.validation.failed": "Lore pack rightening trucked!", "dataPack.validation.reset": "Ed<PERSON> to Stock", "dataPack.validation.working": "Rightening chosen lore packs...", "dataPack.vanilla.description": "The stock lore for Minecraft", "dataPack.vanilla.name": "Stock", "dataPack.winter_drop.description": "New marks and inholding for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "Shielded Wayset", "datapackFailure.safeMode.failed.description": "This world inholds unright or broken nering lore.", "datapackFailure.safeMode.failed.title": "Could not load world in Shielded Wayset.", "datapackFailure.title": "Dwales in anwardly chose lore packs forestalled the world from loading.\nYou can either fand to load it with only the raw lore pack (\"shielded wayset\"), or go back to the main shirm and fettle it by hand.", "death.attack.anvil": "%1$s was squashed by a falling anvil", "death.attack.anvil.player": "%1$s was squashed by a falling anvil while fighting %2$s", "death.attack.arrow": "%1$s was shot by %2$s", "death.attack.arrow.item": "%1$s was shot by %2$s brooking %3$s", "death.attack.badRespawnPoint.link": "Willful Game Layout", "death.attack.badRespawnPoint.message": "%1$s was killed by %2$s", "death.attack.cactus": "%1$s was pricked to death", "death.attack.cactus.player": "%1$s walked into a thorntree while fanding to atwind %2$s", "death.attack.cramming": "%1$s was squished too much", "death.attack.cramming.player": "%1$s was squashed by %2$s", "death.attack.dragonBreath": "%1$s was baked in worm's breath", "death.attack.dragonBreath.player": "%1$s was baked in worm's breath by %2$s", "death.attack.drown": "%1$s choked on water", "death.attack.drown.player": "%1$s choked on water while fanding to atwind %2$s", "death.attack.dryout": "%1$s swelted from unwatering", "death.attack.dryout.player": "%1$s swelted from unwatering while fanding to atwind %2$s", "death.attack.even_more_magic": "%1$s was killed by even more dwimmer", "death.attack.explosion": "%1$s blew up", "death.attack.explosion.player": "%1$s was blown up by %2$s", "death.attack.explosion.player.item": "%1$s was blown up by %2$s brooking %3$s", "death.attack.fall": "%1$s struck the ground too hard", "death.attack.fall.player": "%1$s struck the ground too hard while fanding to atwind %2$s", "death.attack.fallingBlock": "%1$s was squashed by a falling clot", "death.attack.fallingBlock.player": "%1$s was squashed by a falling clot while fighting %2$s", "death.attack.fallingStalactite": "%1$s was spitted by a falling dripstone", "death.attack.fallingStalactite.player": "%1$s was spitted by a falling dripstone while fighting %2$s", "death.attack.fireball": "%1$s was fireballed by %2$s", "death.attack.fireball.item": "%1$s was fireballed by %2$s brooking %3$s", "death.attack.fireworks": "%1$s went off with a bang", "death.attack.fireworks.item": "%1$s went off with a bang owing to a firework fired from %3$s by %2$s", "death.attack.fireworks.player": "%1$s went off with a bang while fighting %2$s", "death.attack.flyIntoWall": "%1$s smashed into a clot", "death.attack.flyIntoWall.player": "%1$s smashed into a clot while fanding to atwind %2$s", "death.attack.freeze": "%1$s froze to death", "death.attack.freeze.player": "%1$s was frozen to death by %2$s", "death.attack.generic": "%1$s swelted", "death.attack.generic.player": "%1$s swelted owing to %2$s", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "%1$s was killed while fighting %2$s", "death.attack.hotFloor": "%1$s found out the floor was moltenstone", "death.attack.hotFloor.player": "%1$s walked into the harmful stead owing to %2$s", "death.attack.inFire": "%1$s went up in a blaze", "death.attack.inFire.player": "%1$s walked into fire while fighting %2$s", "death.attack.inWall": "%1$s was buried alive", "death.attack.inWall.player": "%1$s was buried alive while fighting %2$s", "death.attack.indirectMagic": "%1$s was killed by %2$s brooking dwimmer", "death.attack.indirectMagic.item": "%1$s was killed by %2$s brooking %3$s", "death.attack.lava": "%1$s fanded to swim in moltenstone", "death.attack.lava.player": "%1$s fanded to swim in moltenstone to atwind %2$s", "death.attack.lightningBolt": "%1$s was struck by lightning", "death.attack.lightningBolt.player": "%1$s was struck by lightning while fighting %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s was killed by dwimmer", "death.attack.magic.player": "%1$s was killed by dwimmer while fanding to atwind %2$s", "death.attack.message_too_long": "In truth, the writ was too long to bear fully. Sorry! Here's a stripped one: %s", "death.attack.mob": "%1$s was slain by %2$s", "death.attack.mob.item": "%1$s was slain by %2$s brooking %3$s", "death.attack.onFire": "%1$s burned to death", "death.attack.onFire.item": "%1$s was burned to ashes while fighting %2$s wielding %3$s", "death.attack.onFire.player": "%1$s was burned to ashes while fighting %2$s", "death.attack.outOfWorld": "%1$s fell out of the world", "death.attack.outOfWorld.player": "%1$s didn't wish to live in the same world as %2$s", "death.attack.outsideBorder": "%1$s left the rims of this world", "death.attack.outsideBorder.player": "%1$s left the rims of this world while fighting %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s brooking %3$s", "death.attack.sonic_boom": "%1$s was unmade by a loudly-filled yell", "death.attack.sonic_boom.item": "%1$s was unmade by a loudly-filled yell while fanding to atwind %2$s wielding %3$s", "death.attack.sonic_boom.player": "%1$s was unmade by a loudly-filled yell while fanding to atwind %2$s", "death.attack.stalagmite": "%1$s was sticked on a dripstone", "death.attack.stalagmite.player": "%1$s was sticked on a dripstone while fighting %2$s", "death.attack.starve": "%1$s starved to death", "death.attack.starve.player": "%1$s starved to death while fighting %2$s", "death.attack.sting": "%1$s was stung to death", "death.attack.sting.item": "%1$s was stung to death by %2$s brooking %3$s", "death.attack.sting.player": "%1$s was stung to death by %2$s", "death.attack.sweetBerryBush": "%1$s was holed to death by a sweet berry bush", "death.attack.sweetBerryBush.player": "%1$s was holed to death by a sweet berry bush while fanding to atwind %2$s", "death.attack.thorns": "%1$s was killed while fanding to harm %2$s", "death.attack.thorns.item": "%1$s was killed by %3$s while fanding to harm %2$s", "death.attack.thrown": "%1$s was pummeled by %2$s", "death.attack.thrown.item": "%1$s was pummeled by %2$s brooking %3$s", "death.attack.trident": "%1$s was yeaven holes by %2$s", "death.attack.trident.item": "%1$s was yeaven holes by %2$s with %3$s", "death.attack.wither": "%1$s withered away", "death.attack.wither.player": "%1$s withered away while fighting %2$s", "death.attack.witherSkull": "%1$s was shot by a headbone from %2$s", "death.attack.witherSkull.item": "%1$s was shot by a headbone from %2$s brooking %3$s", "death.fell.accident.generic": "%1$s fell from a high stead", "death.fell.accident.ladder": "%1$s fell off a ladder", "death.fell.accident.other_climbable": "%1$s fell while climbing", "death.fell.accident.scaffolding": "%1$s fell off stelling", "death.fell.accident.twisting_vines": "%1$s fell off some twisting standgrass", "death.fell.accident.vines": "%1$s fell off some hanggrass", "death.fell.accident.weeping_vines": "%1$s fell off some weeping hanggrass", "death.fell.assist": "%1$s was doomed to fall by %2$s", "death.fell.assist.item": "%1$s was doomed to fall by %2$s brooking %3$s", "death.fell.finish": "%1$s fell too far and was ended by %2$s", "death.fell.finish.item": "%1$s fell too far and was ended by %2$s brooking %3$s", "death.fell.killer": "%1$s was doomed to fall", "deathScreen.quit.confirm": "Are you wis you wish to yield?", "deathScreen.respawn": "Edstart", "deathScreen.score": "Stand", "deathScreen.score.value": "Stand: %s", "deathScreen.spectate": "Onlook world", "deathScreen.title": "You swelted!", "deathScreen.title.hardcore": "Game Over!", "deathScreen.titleScreen": "Main Shirm", "debug.advanced_tooltips.help": "F3 + H = Furthered tooltips", "debug.advanced_tooltips.off": "Furthered tooltips: hidden", "debug.advanced_tooltips.on": "Furthered tooltips: shown", "debug.chunk_boundaries.help": "F3 + G = Show worldstetch rims", "debug.chunk_boundaries.off": "Worldstetch rims: hidden", "debug.chunk_boundaries.on": "Worldstetch rims: shown", "debug.clear_chat.help": "F3 + D = Empty chat", "debug.copy_location.help": "F3 + C = <PERSON><PERSON> stow as /tp hest, hold F3 + C to downfall the game", "debug.copy_location.message": "Cloved stow to shearboard", "debug.crash.message": "F3 + C is held down. This will downfall the game unless leesed.", "debug.crash.warning": "Downfall in %s...", "debug.creative_spectator.error": "Cannot wend game wayset, no thaving", "debug.creative_spectator.help": "F3 + N = <PERSON> through former game wayset <-> onlooker", "debug.dump_dynamic_textures": "<PERSON>pt shrithly lodes to %s", "debug.dump_dynamic_textures.help": "F3 + S = Shift shrithly lodes", "debug.gamemodes.error": "Cannot open game wayset wender, no thaving", "debug.gamemodes.help": "F3 + F4 = Open game wayset wender", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Next", "debug.help.help": "F3 + Q = Show this list", "debug.help.message": "Key bindings:", "debug.inspect.client.block": "Cloved software-side clot lore to shearboard", "debug.inspect.client.entity": "Cloved software-side ansen lore to shearboard", "debug.inspect.help": "F3 + I = Clove ansen or clot lore to shearboard", "debug.inspect.server.block": "Cloved outreckoner-side clot lore to shearboard", "debug.inspect.server.entity": "Cloved outreckoner-side ansen lore to shearboard", "debug.pause.help": "F3 + Atwind = Stop without stop list (if stopping is mightly)", "debug.pause_focus.help": "F3 + P = Stop on lost highlight", "debug.pause_focus.off": "Stop on lost highlight: off", "debug.pause_focus.on": "Stop on lost highlight: on", "debug.prefix": "[Unbug]:", "debug.profiling.help": "F3 + L = Start/stop forefinding", "debug.profiling.start": "Forefinding started for %s braids. Brook F3 + L to stop early", "debug.profiling.stop": "Forefinding ended. Kept outcome to %s", "debug.reload_chunks.help": "F3 + A = Edload worldstetches", "debug.reload_chunks.message": "Edloading all worldstetches", "debug.reload_resourcepacks.help": "F3 + T = Edload lode packs", "debug.reload_resourcepacks.message": "Edloaded lode packs", "debug.show_hitboxes.help": "F3 + B = Show strikeboxes", "debug.show_hitboxes.off": "Strikeboxes: hidden", "debug.show_hitboxes.on": "Strikeboxes: shown", "debug.version.header": "Software wharve abrst:", "debug.version.help": "F3 + V = Software wharve abrst", "demo.day.1": "This kithing will last five game days. Do your best!", "demo.day.2": "Day Two", "demo.day.3": "Day Three", "demo.day.4": "Day Four", "demo.day.5": "This is your last day!", "demo.day.6": "You have gone by your fifth day. Brook %s to keep a shirmshot of your ashaping.", "demo.day.warning": "Your time is almost up!", "demo.demoExpired": "Kithing time's up!", "demo.help.buy": "Buy Now!", "demo.help.fullWrapped": "This kithing will last 5 in-game days (about 1 stound and 40 stoundles of insooth time). Look at the forthsteps for hints! Have fun!", "demo.help.inventory": "Brook the %1$s key to open your inholding", "demo.help.jump": "Leap by thringing the %1$s key", "demo.help.later": "Go on Playing!", "demo.help.movement": "Use the %1$s, %2$s, %3$s, %4$s keys and the mouse to shrithe umb", "demo.help.movementMouse": "Look umb brooking the mouse", "demo.help.movementShort": "Walk by thringing the %1$s, %2$s, %3$s, %4$s keys", "demo.help.title": "Minecraft Kithing Wayset", "demo.remainingTime": "Time left: %s", "demo.reminder": "The kithing time has quenched. Buy the game to go on or start a new world!", "difficulty.lock.question": "Are you wis you wish to lock the toughness of this world? This will set this world to always be %1$s, and you can never wend that ayen.", "difficulty.lock.title": "Lock World Toughness", "disconnect.endOfStream": "End of stream", "disconnect.exceeded_packet_rate": "Thrown out for overstepping packling threshold", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Unheeding standing ask", "disconnect.loginFailedInfo": "Could not go in: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Maniplayer is off. Kindly look at your Microsoft reckoning settings.", "disconnect.loginFailedInfo.invalidSession": "Unright besitting (<PERSON><PERSON> edstarting your game and the gamestarter)", "disconnect.loginFailedInfo.serversUnavailable": "The sickerhood outreckoners are anwardly not reachbere. Kindly fand ayen.", "disconnect.loginFailedInfo.userBanned": "You are forbidden from playing onweb", "disconnect.lost": "Binding Lost", "disconnect.packetError": "Network Forthward Dwale", "disconnect.spam": "Thrown out for overchatting", "disconnect.timeout": "Timed out", "disconnect.transfer": "Shifted to another outreckoner", "disconnect.unknownHost": "Unknown dright", "download.pack.failed": "%s out of %s pack(s) could not download", "download.pack.progress.bytes": "Forthship: %s (overall breadth unknown)", "download.pack.progress.percent": "Forthship: %s%%", "download.pack.title": "Downloading lode pack %s/%s", "editGamerule.default": "Stock: %s", "editGamerule.title": "Bework Game Wields", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Upsoaking", "effect.minecraft.bad_omen": "Bad Halsend", "effect.minecraft.blindness": "Blindness", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON>", "effect.minecraft.darkness": "Darkness", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON>'s Est", "effect.minecraft.fire_resistance": "Fire Withstanding", "effect.minecraft.glowing": "Glowing", "effect.minecraft.haste": "Speedy Delving", "effect.minecraft.health_boost": "Health Bolster", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> of the Thorp", "effect.minecraft.hunger": "Hunger", "effect.minecraft.infested": "Incoathed", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "Mididone Health", "effect.minecraft.invisibility": "Unseenliness", "effect.minecraft.jump_boost": "<PERSON><PERSON>", "effect.minecraft.levitation": "Hovering", "effect.minecraft.luck": "Luck", "effect.minecraft.mining_fatigue": "Delving Weariness", "effect.minecraft.nausea": "Lat", "effect.minecraft.night_vision": "Nightsight", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "<PERSON><PERSON>", "effect.minecraft.raid_omen": "Reaving Hal<PERSON>d", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "Withstanding", "effect.minecraft.saturation": "Fulfillness", "effect.minecraft.slow_falling": "Slow Falling", "effect.minecraft.slowness": "Slowness", "effect.minecraft.speed": "Speed", "effect.minecraft.strength": "Strength", "effect.minecraft.trial_omen": "<PERSON><PERSON>", "effect.minecraft.unluck": "Bad Luck", "effect.minecraft.water_breathing": "Water Breathing", "effect.minecraft.weakness": "Woakness", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Loaded", "effect.minecraft.wither": "<PERSON>er", "effect.none": "No Rines", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Water Kinship", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON> of Spiderlikes", "enchantment.minecraft.binding_curse": "Curse of Binding", "enchantment.minecraft.blast_protection": "Blast Shielding", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Thunderwave", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Depth Strider", "enchantment.minecraft.efficiency": "Speed", "enchantment.minecraft.feather_falling": "Feather Falling", "enchantment.minecraft.fire_aspect": "<PERSON> Ansen", "enchantment.minecraft.fire_protection": "Fire Shielding", "enchantment.minecraft.flame": "Brand", "enchantment.minecraft.fortune": "<PERSON>p", "enchantment.minecraft.frost_walker": "<PERSON>", "enchantment.minecraft.impaling": "Fishing", "enchantment.minecraft.infinity": "Boundlessness", "enchantment.minecraft.knockback": "K<PERSON><PERSON>", "enchantment.minecraft.looting": "Reaving", "enchantment.minecraft.loyalty": "<PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Luck of the Sea", "enchantment.minecraft.lure": "<PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Throughshot", "enchantment.minecraft.power": "Might", "enchantment.minecraft.projectile_protection": "Shot Shielding", "enchantment.minecraft.protection": "Shielding", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Quick Load", "enchantment.minecraft.respiration": "Underwater Breathing", "enchantment.minecraft.riptide": "Riptide", "enchantment.minecraft.sharpness": "Sharpness", "enchantment.minecraft.silk_touch": "Soft Rine", "enchantment.minecraft.smite": "Smite", "enchantment.minecraft.soul_speed": "Soul Speed", "enchantment.minecraft.sweeping": "Sweeping Edge", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "Swift Sneak", "enchantment.minecraft.thorns": "Thorns", "enchantment.minecraft.unbreaking": "Unbreaking", "enchantment.minecraft.vanishing_curse": "Curse of Swinding", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Wattletree Boat", "entity.minecraft.acacia_chest_boat": "Wattletree Boat with Chest", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Rine Cloud", "entity.minecraft.armadillo": "Girdled<PERSON>", "entity.minecraft.armor_stand": "Hirsting Stand", "entity.minecraft.arrow": "Arrow", "entity.minecraft.axolotl": "Waterhelper", "entity.minecraft.bamboo_chest_raft": "Treereed Float with Chest", "entity.minecraft.bamboo_raft": "Treereed Float", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Bee", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Clot Showing", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "<PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Blast", "entity.minecraft.camel": "Drylander", "entity.minecraft.cat": "Cat", "entity.minecraft.cave_spider": "<PERSON> Spider", "entity.minecraft.cherry_boat": "Stoneberry Boat", "entity.minecraft.cherry_chest_boat": "Stoneberry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> with Chest", "entity.minecraft.chicken": "Chicken", "entity.minecraft.cod": "Cod", "entity.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON>", "entity.minecraft.cow": "Cow", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "Dunky", "entity.minecraft.dragon_fireball": "Worm Fireball", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Thrown Ey", "entity.minecraft.elder_guardian": "<PERSON>", "entity.minecraft.end_crystal": "End Hirst", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> Ender <PERSON>st", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Awakener", "entity.minecraft.evoker_fangs": "Awakener Fangs", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> Handvat o' <PERSON>ering", "entity.minecraft.experience_orb": "Cunball", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Falling Clot", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "Fireball", "entity.minecraft.firework_rocket": "Firework", "entity.minecraft.fishing_bobber": "Fishing Bobber", "entity.minecraft.fox": "Fox", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Ent", "entity.minecraft.glow_item_frame": "Glow Thing Frame", "entity.minecraft.glow_squid": "Glow Sleevefish", "entity.minecraft.goat": "Goa<PERSON>", "entity.minecraft.guardian": "Ward", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> with <PERSON>", "entity.minecraft.horse": "Horse", "entity.minecraft.husk": "Husk", "entity.minecraft.illusioner": "<PERSON><PERSON>er", "entity.minecraft.interaction": "Brooking", "entity.minecraft.iron_golem": "Iron Livenedman", "entity.minecraft.item": "Thing", "entity.minecraft.item_display": "Thing Showing", "entity.minecraft.item_frame": "<PERSON>ame", "entity.minecraft.jungle_boat": "Rainwold Boat", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON> Boat with Chest", "entity.minecraft.killer_bunny": "The Killer Bunny", "entity.minecraft.leash_knot": "<PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Lightning Bolt", "entity.minecraft.lingering_potion": "Lingering Lib", "entity.minecraft.llama": "Drylandersheep", "entity.minecraft.llama_spit": "Drylandersheep Spit", "entity.minecraft.magma_cube": "Moltenstone Slime", "entity.minecraft.mangrove_boat": "Liftmore Boat", "entity.minecraft.mangrove_chest_boat": "Liftmore Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Dunkyhorse", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "Bushcat", "entity.minecraft.ominous_item_spawner": "Threatening Thing Beyetner", "entity.minecraft.painting": "Meting", "entity.minecraft.pale_oak_boat": "Bloak Oak Boat", "entity.minecraft.pale_oak_chest_boat": "Bloak Oak Boat with Chest", "entity.minecraft.panda": "Catbear", "entity.minecraft.parrot": "Bleefowl", "entity.minecraft.phantom": "Nightgost", "entity.minecraft.pig": "Swine", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON>", "entity.minecraft.pillager": "<PERSON><PERSON>", "entity.minecraft.player": "Player", "entity.minecraft.polar_bear": "Icebear", "entity.minecraft.potion": "Lib", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "<PERSON>", "entity.minecraft.ravager": "Weaster", "entity.minecraft.salmon": "Lax", "entity.minecraft.sheep": "Sheep", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Silverfish", "entity.minecraft.skeleton": "Boneframe", "entity.minecraft.skeleton_horse": "Boneframe Horse", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Small Fireball", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Snowman", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON>", "entity.minecraft.spectral_arrow": "<PERSON><PERSON>", "entity.minecraft.spider": "Spider", "entity.minecraft.splash_potion": "Splash Lib", "entity.minecraft.spruce_boat": "Harttartree Boat", "entity.minecraft.spruce_chest_boat": "Harttartree Boat with Chest", "entity.minecraft.squid": "Sleevefish", "entity.minecraft.stray": "Drifter", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Toadhead", "entity.minecraft.text_display": "Writ Showing", "entity.minecraft.tnt": "Fired <PERSON>", "entity.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON>", "entity.minecraft.trader_llama": "<PERSON>", "entity.minecraft.trident": "<PERSON><PERSON>", "entity.minecraft.tropical_fish": "Southsea Fish", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Black Sawbonesfish", "entity.minecraft.tropical_fish.predefined.10": "Moorish Dwalegod", "entity.minecraft.tropical_fish.predefined.11": "Sheen <PERSON>", "entity.minecraft.tropical_fish.predefined.12": "Bleefowlfish", "entity.minecraft.tropical_fish.predefined.13": "Queen <PERSON>", "entity.minecraft.tropical_fish.predefined.14": "Red Thrushfish", "entity.minecraft.tropical_fish.predefined.15": "Red Lipped Slimefish", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Loveapple Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Triggerfish", "entity.minecraft.tropical_fish.predefined.2": "Hewn Sawbonesfish", "entity.minecraft.tropical_fish.predefined.20": "Yellowtail Bleefowlfish", "entity.minecraft.tropical_fish.predefined.21": "Yellow Sawbonesfish", "entity.minecraft.tropical_fish.predefined.3": "Butterflyfish", "entity.minecraft.tropical_fish.predefined.4": "Thrushfish", "entity.minecraft.tropical_fish.predefined.5": "Clownfish", "entity.minecraft.tropical_fish.predefined.6": "Sweet Woodwool Fightingfish", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Overking Red Snapper", "entity.minecraft.tropical_fish.predefined.9": "Goatfish", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Clot<PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Lixfish", "entity.minecraft.tropical_fish.type.kob": "Drummerfish", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "Spotty", "entity.minecraft.tropical_fish.type.stripey": "Streaky", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Shellpad", "entity.minecraft.vex": "<PERSON><PERSON>", "entity.minecraft.villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Draftsman", "entity.minecraft.villager.cleric": "<PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "Fisherman", "entity.minecraft.villager.fletcher": "Arrowmaker", "entity.minecraft.villager.leatherworker": "Leatherworker", "entity.minecraft.villager.librarian": "Bookward", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Weaponsmith", "entity.minecraft.vindicator": "Whitewasher", "entity.minecraft.wandering_trader": "<PERSON><PERSON> Chapman", "entity.minecraft.warden": "<PERSON><PERSON>", "entity.minecraft.wind_charge": "Wind Blast", "entity.minecraft.witch": "Witch", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Undead Lich", "entity.minecraft.zombie_horse": "Undead Horse", "entity.minecraft.zombie_villager": "Undead <PERSON><PERSON>", "entity.minecraft.zombified_piglin": "Undead Piglin", "entity.not_summonable": "Can't beckon ansen of kind %s", "event.minecraft.raid": "Reaving", "event.minecraft.raid.defeat": "Loss", "event.minecraft.raid.defeat.full": "Reaving - Loss", "event.minecraft.raid.raiders_remaining": "Reavers Left: %s", "event.minecraft.raid.victory": "Win", "event.minecraft.raid.victory.full": "Reaving - Win", "filled_map.buried_treasure": "Buried Hoard Draught", "filled_map.explorer_jungle": "<PERSON><PERSON> Draught", "filled_map.explorer_swamp": "Swamp Seeker Draught", "filled_map.id": "Ihood #%s", "filled_map.level": "(Layer %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "Woodland Seeker Draught", "filled_map.monument": "Sea Seeker Draught", "filled_map.scale": "Meting at 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON> Draught", "filled_map.unknown": "Unknown Draught", "filled_map.village_desert": "<PERSON><PERSON><PERSON>", "filled_map.village_plains": "Grasslands Thor<PERSON>", "filled_map.village_savanna": "<PERSON><PERSON><PERSON>", "filled_map.village_snowy": "<PERSON><PERSON>", "filled_map.village_taiga": "Harttar<PERSON> Thor<PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Bottomless Swallow", "flat_world_preset.minecraft.classic_flat": "Win Flat", "flat_world_preset.minecraft.desert": "<PERSON>asten", "flat_world_preset.minecraft.overworld": "Overworld", "flat_world_preset.minecraft.redstone_ready": "Redstone Ready", "flat_world_preset.minecraft.snowy_kingdom": "Snowy Kingdom", "flat_world_preset.minecraft.the_void": "The Emptiness", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON>' Dream", "flat_world_preset.minecraft.water_world": "Water World", "flat_world_preset.unknown": "???", "gameMode.adventure": "Wayspelling Wayset", "gameMode.changed": "Your game wayset has been anwardened to %s", "gameMode.creative": "<PERSON><PERSON> Wayset", "gameMode.hardcore": "Hardheart Wayset", "gameMode.spectator": "Onlooker <PERSON>et", "gameMode.survival": "Overlive Wayset", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Wields whether or not fire and moltenstone should be fit to tick further than 8 worldstetches away from any player", "gamerule.announceAdvancements": "Bode forthsteps", "gamerule.blockExplosionDropDecay": "In clot blasts, some clots won't drop hir meeds", "gamerule.blockExplosionDropDecay.description": "Some of the drops from clots broken by blasts made by clots are lost in the blast.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Sundries", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "Springing", "gamerule.category.updates": "World Anwardenings", "gamerule.commandBlockOutput": "Broadcast hest clot output", "gamerule.commandModificationBlockLimit": "Hest tweak clot threshold", "gamerule.commandModificationBlockLimit.description": "Rime of clots that can be wended at once by one hest, such as fill or twin.", "gamerule.disableElytraMovementCheck": "Lay off enderglider shrithing soothe", "gamerule.disablePlayerMovementCheck": "Lay off player shrithing soothe", "gamerule.disableRaids": "Lay off reavings", "gamerule.doDaylightCycle": "Forward time of day", "gamerule.doEntityDrops": "Drop ansen hardware", "gamerule.doEntityDrops.description": "Wields drops from delvewains (inning inholdings), thing frames, boats, asf.", "gamerule.doFireTick": "Anwarden fire", "gamerule.doImmediateRespawn": "Edstart anon", "gamerule.doInsomnia": "Spring nightgosts", "gamerule.doLimitedCrafting": "Need knowledge for crafting", "gamerule.doLimitedCrafting.description": "If on, players will be fit to craft only unlocked knowledge.", "gamerule.doMobLoot": "Drop wight meeds", "gamerule.doMobLoot.description": "Wields lode drops from wights, inning cunballs.", "gamerule.doMobSpawning": "Spring wights", "gamerule.doMobSpawning.description": "Some ansens might have other wields.", "gamerule.doPatrolSpawning": "Spring reaver watchmen", "gamerule.doTileDrops": "Drop clots", "gamerule.doTileDrops.description": "Wields lode drops from clots, inning cunballs.", "gamerule.doTraderSpawning": "Spring Wandering Chapmen", "gamerule.doVinesSpread": "Hanggrass spread", "gamerule.doVinesSpread.description": "Wields whether or not the Hanggrass clot spreads hapsomely to nearby clots. Does not onwork other kinds of hanggrass clots such as Weeping Hanggrass, Twisting Standgrass, asf.", "gamerule.doWardenSpawning": "Spring Holdends", "gamerule.doWeatherCycle": "Wend weather", "gamerule.drowningDamage": "Deal waterchoking harm", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender Seahirsts swind on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether <PERSON><PERSON> thrown by a player swind when that player swelts.", "gamerule.entitiesWithPassengersCanUsePortals": "Ansens with riders can brook ingangs", "gamerule.entitiesWithPassengersCanUsePortals.description": "Aleave ansens with riders to farferry through nether ingangs, end ingangs and end gateways.", "gamerule.fallDamage": "Deal fall harm", "gamerule.fireDamage": "Deal fire harm", "gamerule.forgiveDeadPlayers": "Foryeave dead players", "gamerule.forgiveDeadPlayers.description": "Wrathed unsidey wights stop being wroth when the marked player swelts nearby.", "gamerule.freezeDamage": "Deal freeze harm", "gamerule.globalSoundEvents": "Overall loud befallings", "gamerule.globalSoundEvents.description": "When some game befallings befall, like a highfiend beyetting, the loud is heard everywhere.", "gamerule.keepInventory": "Keep inholding after death", "gamerule.lavaSourceConversion": "Moltenstone becomes ordfrom", "gamerule.lavaSourceConversion.description": "When flowing moltenstone is belapped on two sides by moltenstone ordfroms it becomes a ordfrom.", "gamerule.locatorBar": "Lay on player Finder Band", "gamerule.locatorBar.description": "When on, a band is shown on the shirm to show the bearing of players.", "gamerule.logAdminCommands": "Broadcast wielder hests", "gamerule.maxCommandChainLength": "Hest shackling length threshold", "gamerule.maxCommandChainLength.description": "Beseeches to hest clot strings and workings.", "gamerule.maxCommandForkCount": "Hest framework threshold", "gamerule.maxCommandForkCount.description": "Greatest rime of frameworks that can be brooked by hests like 'execute as'.", "gamerule.maxEntityCramming": "<PERSON><PERSON> cramming threshold", "gamerule.minecartMaxSpeed": "Delvewain greatest speed", "gamerule.minecartMaxSpeed.description": "Greatest stock speed of a shrithing Delvewain on land.", "gamerule.mobExplosionDropDecay": "In wight blasts, some clots won't drop hir meeds", "gamerule.mobExplosionDropDecay.description": "Some of the drops from clots broken by blasts made by wights are lost in the blast.", "gamerule.mobGriefing": "Aleave baneful wight doings", "gamerule.naturalRegeneration": "Edheal health", "gamerule.playersNetherPortalCreativeDelay": "Player's <PERSON><PERSON> ingang bide in makerly wayset", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a makerly wayset player needs to stand in a Nether ingang before wending farsteads.", "gamerule.playersNetherPortalDefaultDelay": "Player's <PERSON><PERSON> ingang bide not in makerly wayset", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a player not in makerly wayset needs to stand in a Nether ingang before wending farsteads.", "gamerule.playersSleepingPercentage": "Sleep hundredmeal", "gamerule.playersSleepingPercentage.description": "The hundredmeal of players who must be sleeping to leave out the night.", "gamerule.projectilesCanBreakBlocks": "Shots can break clots", "gamerule.projectilesCanBreakBlocks.description": "Wields whether rine shots will break clots that are breakenly by hem.", "gamerule.randomTickSpeed": "Hapsome tick speed quickness", "gamerule.reducedDebugInfo": "Lessen unbug abrst", "gamerule.reducedDebugInfo.description": "Thresholds inholdings of unbug shirm.", "gamerule.sendCommandFeedback": "Send hest feedback", "gamerule.showDeathMessages": "Show death writ", "gamerule.snowAccumulationHeight": "Greatest snow height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow grow on the ground up to at highest this rime of layers.", "gamerule.spawnChunkRadius": "Starting ord worldstetch strale", "gamerule.spawnChunkRadius.description": "Deal of worldstetch that keep loaded umb the overworld starting stow.", "gamerule.spawnRadius": "Edstart stow strale", "gamerule.spawnRadius.description": "Wields the breadth of the spot umb the starting ord that players can spring in.", "gamerule.spectatorsGenerateChunks": "Aleave onlookers to beyet landship", "gamerule.tntExplodes": "<PERSON><PERSON><PERSON> to be astirred and to blast", "gamerule.tntExplosionDropDecay": "In Blastle blasts, some clots won't drop hir meeds", "gamerule.tntExplosionDropDecay.description": "Some of the drops from clots broken by blasts made by Blastles are lost in the blast.", "gamerule.universalAnger": "<PERSON>kin wrath", "gamerule.universalAnger.description": "Wrathed unsidey wights strike any nearby player, not only the player that wrathed hem. Works best if forgiveDeadPlayers is off.", "gamerule.waterSourceConversion": "Water becomes ordfrom", "gamerule.waterSourceConversion.description": "When flowing water is belapped on two sides by water ordfroms it becomes a ordfrom.", "generator.custom": "Bespoke", "generator.customized": "Old Bespoke", "generator.minecraft.amplified": "OVERDONE", "generator.minecraft.amplified.info": "Heads up: Only for fun! Foreneeds a fleshy reckoner.", "generator.minecraft.debug_all_block_states": "Unbug Wayset", "generator.minecraft.flat": "Sinflat", "generator.minecraft.large_biomes": "Broad Lifeheaps", "generator.minecraft.normal": "Stock", "generator.minecraft.single_biome_surface": "Onefold Lifeheap", "generator.single_biome_caves": "Hollows", "generator.single_biome_floating_islands": "Floating Ilands", "gui.abuseReport.attestation": "By putting in this mield, you sicker that the abreasting you have yeaven is true and fuldone to the best of your knowledge.", "gui.abuseReport.comments": "Bemarks", "gui.abuseReport.describe": "Sharing insights will help us make a well-abreasted settling.", "gui.abuseReport.discard.content": "If you leave, you'll lose this mield and your bemarks.\nAre you wis you wish to leave?", "gui.abuseReport.discard.discard": "Leave and Adwash Mield", "gui.abuseReport.discard.draft": "Keep as Draft", "gui.abuseReport.discard.return": "Go on Beworking", "gui.abuseReport.discard.title": "Adwash mield and bemarks?", "gui.abuseReport.draft.content": "Would you like to go on beworking the bestanding mield or adwash it and make a new one?", "gui.abuseReport.draft.discard": "<PERSON><PERSON>", "gui.abuseReport.draft.edit": "Go on Beworking", "gui.abuseReport.draft.quittotitle.content": "Would you like to go on beworking it or adwash it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat mield that will be lost if you leave", "gui.abuseReport.draft.title": "Bework draft chat mield?", "gui.abuseReport.error.title": "<PERSON><PERSON><PERSON> sending your mield", "gui.abuseReport.message": "Where did you behold the bad behaving?\nThis will help us in looking into your bewriting.", "gui.abuseReport.more_comments": "Kindly retch what befell:", "gui.abuseReport.name.comment_box_label": "Kindly retch why you wish to mield this name:", "gui.abuseReport.name.reporting": "You are mielding \"%s\".", "gui.abuseReport.name.title": "<PERSON><PERSON>see<PERSON>ly Player Name", "gui.abuseReport.observed_what": "Why are you mielding this?", "gui.abuseReport.read_info": "Learn About Mielding", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drenches or firewatered drinks", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is uplifting others to fay in underboard drench akin deeds or uplifting undereld drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child heamedy misbidding", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise uplifting unseemly behaving infolding children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Shending or lying", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is harming your or someone else's name, sharing untruth with the goal of misleading others.", "gui.abuseReport.reason.description": "Retching:", "gui.abuseReport.reason.false_reporting": "Untrue Mielding", "gui.abuseReport.reason.generic": "I wish to mield hem", "gui.abuseReport.reason.generic.description": "I'm mad at hem / hie have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON>", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, striking, or harrying you or someone else. This ins when someone is edledgedly fanding to reach out to you or someone else without thaving or uploading sunder leedy abreasting about you or someone else without thaving.", "gui.abuseReport.reason.hate_speech": "Hate speech", "gui.abuseReport.reason.hate_speech.description": "Someone is hating you or another player on marks of hir selfhood.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in true life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Unthaved couthred bilth", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise uplifting sunderly and couthred bilth.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or selfmurther", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm hemselves in the true life or talking about harming hemselves in true life.", "gui.abuseReport.reason.sexually_inappropriate": "Heamedily unseemly", "gui.abuseReport.reason.sexually_inappropriate.description": "Hides that are bilden in kind akin to swively deeds, kindlimbs, and heamedy heast.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Fearstriking or heast utmosting", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, uplifting, or threatening to do deeds of fearstriking or heast utmosting owing to someone's wieldcraft, troths, beliefs, asf.", "gui.abuseReport.reason.title": "<PERSON><PERSON>", "gui.abuseReport.report_sent_msg": "We've speedfully reaped your mield. Thank you!\n\nOur team will edlook it as soon as mightly.", "gui.abuseReport.select_reason": "<PERSON><PERSON>", "gui.abuseReport.send": "<PERSON>", "gui.abuseReport.send.comment_too_long": "Kindly shorten the bemark", "gui.abuseReport.send.error_message": "A dwale was sent back while sending your mield: '%s'", "gui.abuseReport.send.generic_error": "Met an unweened dwale while sending your mield.", "gui.abuseReport.send.http_error": "An unweened OQSF dwale befallen while sending your mield.", "gui.abuseReport.send.json_error": "Met a crooked abreasting load while sending your mield.", "gui.abuseReport.send.no_reason": "Kindly choose a mield kind", "gui.abuseReport.send.not_attested": "Kindly read the writ above and tick the tickbox to send the mield", "gui.abuseReport.send.service_unavailable": "Cannot reach the Misbid Mielding followth. Kindly make wis you are bound to the web and fand ayen.", "gui.abuseReport.sending.title": "Sending your mield...", "gui.abuseReport.sent.title": "<PERSON><PERSON> sent", "gui.abuseReport.skin.title": "<PERSON><PERSON>", "gui.abuseReport.title": "Mield Player", "gui.abuseReport.type.chat": "Chat Writs", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Hide", "gui.acknowledge": "Acknowledge", "gui.advancements": "Forthsteps", "gui.all": "All", "gui.back": "Back", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Your reckoning is forevermore forbidden, which means you can't play onweb or fay Realms.", "gui.banned.description.reason": "We newly reaped a mield for bad behaving by your reckoning. Our steerers have now edlooked your mield and betokened it as %s, which goes ayenst the Minecraft Fellowship Steer.", "gui.banned.description.reason_id": "Dern: %s", "gui.banned.description.reason_id_message": "Dern: %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play onweb or fay Realms.", "gui.banned.description.temporary.duration": "Your reckoning is hung up for a while and will be edastirred in %s.", "gui.banned.description.unknownreason": "We newly reaped a mield for bad behaving by your reckoning. Our steerers have now edlooked your mield and betokened that it goes ayenst the Minecraft Fellowship Steer.", "gui.banned.name.description": "Your anward name - \"%s\" - goes ayenst our Fellowship Steer. You can play oneplayer, but will need to wend your name to play onweb.\n\nLearn more or put in a bewriting edlook at the following link: %s", "gui.banned.name.title": "Name Not Aleaved in Maniplayer", "gui.banned.reason.defamation_impersonation_false_information": "Lying about who you are or otherwise sharing abreasting to mislead others", "gui.banned.reason.drugs": "Upliftings of underboard drenches", "gui.banned.reason.extreme_violence_or_gore": "Showing true-life overboard bloodshed or gore", "gui.banned.reason.false_reporting": "Overboard untrue mields", "gui.banned.reason.fraud": "Fokenfully reaping or brooking of inholding", "gui.banned.reason.generic_violation": "Going ayenst Fellowship Steer", "gui.banned.reason.harassment_or_bullying": "Scathing tongue brooked in a forthright, harmful way", "gui.banned.reason.hate_speech": "Hate speech or shedmaking", "gui.banned.reason.hate_terrorism_notorious_figure": "Upliftings for hate teams, fearstrike teams, or marked someones", "gui.banned.reason.imminent_harm_to_person_or_property": "Will to make true-life harm to someone or hir belongings", "gui.banned.reason.nudity_or_pornography": "Showing lewd or smutcraftish works", "gui.banned.reason.sexually_inappropriate": "Inholding of a heamedy kind", "gui.banned.reason.spam_or_advertising": "Overchat or uprearing", "gui.banned.skin.description": "Your anward hide goes ayenst our Fellowship Steer. You can still play with a stock hide, or choose a new one.\n\nLearn more or put in a bewriting edlook at the following link: %s", "gui.banned.skin.title": "Hide Not Aleaved", "gui.banned.title.permanent": "Reckoning forevermore forbidden", "gui.banned.title.temporary": "Reckoning hung up for a while", "gui.cancel": "Belay", "gui.chatReport.comments": "Bemarks", "gui.chatReport.describe": "Sharing insights will help us make a well-abreasted settling.", "gui.chatReport.discard.content": "If you leave, you'll lose this mield and your bemarks.\nAre you wis you wish to leave?", "gui.chatReport.discard.discard": "Leave and Adwash Mield", "gui.chatReport.discard.draft": "Keep as Draft", "gui.chatReport.discard.return": "Go on Beworking", "gui.chatReport.discard.title": "Adwash mield and bemarks?", "gui.chatReport.draft.content": "Would you like to go on beworking the bestanding mield or adwash it and make a new one?", "gui.chatReport.draft.discard": "<PERSON><PERSON>", "gui.chatReport.draft.edit": "Go on Beworking", "gui.chatReport.draft.quittotitle.content": "Would you like to go on beworking it or adwash it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat mield that will be lost if you leave", "gui.chatReport.draft.title": "Bework draft chat mield?", "gui.chatReport.more_comments": "Kindly retch what befell:", "gui.chatReport.observed_what": "Why are you mielding this?", "gui.chatReport.read_info": "Learn About Mielding", "gui.chatReport.report_sent_msg": "We've speedfully reaped your mield. Thank you!\n\nOur team will edlook it as soon as mightly.", "gui.chatReport.select_chat": "<PERSON><PERSON> Chat Writs to Mield", "gui.chatReport.select_reason": "<PERSON><PERSON>", "gui.chatReport.selected_chat": "%s <PERSON><PERSON>(s) <PERSON><PERSON> to <PERSON>eld", "gui.chatReport.send": "<PERSON>", "gui.chatReport.send.comments_too_long": "Kindly shorten the bemark", "gui.chatReport.send.no_reason": "Kindly choose a mield kind", "gui.chatReport.send.no_reported_messages": "Kindly choose at least one chat writ to mield", "gui.chatReport.send.too_many_messages": "Fanding to in too many writs in the mield", "gui.chatReport.title": "<PERSON><PERSON>", "gui.chatSelection.context": "Writs umb this choosing will be inned to yeave more of a framework", "gui.chatSelection.fold": "%s writs hided", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s fayed the chat", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s writ(s) chosen", "gui.chatSelection.title": "<PERSON><PERSON> Chat Writs to Mield", "gui.continue": "Go on", "gui.copy_link_to_clipboard": "Clove Link to Shearboard", "gui.days": "%s day(s)", "gui.done": "Done", "gui.down": "Down", "gui.entity_tooltip.type": "Kind: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Forborne %s threads", "gui.fileDropFailure.title": "Could not eke threads", "gui.hours": "%s stound(s)", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s stoundle(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s key", "gui.narrate.editBox": "%s bework box: %s", "gui.narrate.slider": "%s slider", "gui.narrate.tab": "%s stead", "gui.no": "No", "gui.none": "None", "gui.ok": "Alright", "gui.open_report_dir": "Open Mield Folder", "gui.proceed": "Go on", "gui.recipebook.moreRecipes": "Right Click for More", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Seek...", "gui.recipebook.toggleRecipes.all": "Showing All", "gui.recipebook.toggleRecipes.blastable": "Showing Blastenly", "gui.recipebook.toggleRecipes.craftable": "Showing Craftenly", "gui.recipebook.toggleRecipes.smeltable": "Showing Smeltenly", "gui.recipebook.toggleRecipes.smokable": "Showing Smokenly", "gui.report_to_server": "<PERSON><PERSON>reckoner", "gui.socialInteractions.blocking_hint": "Handle with Microsoft reckoning", "gui.socialInteractions.empty_blocked": "No ditted players in chat", "gui.socialInteractions.empty_hidden": "No players hidden in chat", "gui.socialInteractions.hidden_in_chat": "Chat writs from %s will be hidden", "gui.socialInteractions.hide": "Hide in Chat", "gui.socialInteractions.narration.hide": "Hide writs from %s", "gui.socialInteractions.narration.report": "Mield player %s", "gui.socialInteractions.narration.show": "Show writs from %s", "gui.socialInteractions.report": "<PERSON><PERSON>", "gui.socialInteractions.search_empty": "Couldn't find any players with that name", "gui.socialInteractions.search_hint": "Seek...", "gui.socialInteractions.server_label.multiple": "%s - %s players", "gui.socialInteractions.server_label.single": "%s - %s player", "gui.socialInteractions.show": "Show in Chat", "gui.socialInteractions.shown_in_chat": "Chat writs from %s will be shown", "gui.socialInteractions.status_blocked": "Ditted", "gui.socialInteractions.status_blocked_offline": "Ditted - Offweb", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "Hidden - Offweb", "gui.socialInteractions.status_offline": "Offweb", "gui.socialInteractions.tab_all": "All", "gui.socialInteractions.tab_blocked": "Ditted", "gui.socialInteractions.tab_hidden": "Hidden", "gui.socialInteractions.title": "<PERSON><PERSON>", "gui.socialInteractions.tooltip.hide": "Hide writs", "gui.socialInteractions.tooltip.report": "Mield player", "gui.socialInteractions.tooltip.report.disabled": "The mielding followth is unforthcoming", "gui.socialInteractions.tooltip.report.no_messages": "No mieldenly writs from player %s", "gui.socialInteractions.tooltip.report.not_reportable": "This player can't be mielded, as hir chat writs can't be asoothed on this outreckoner", "gui.socialInteractions.tooltip.show": "Show writs", "gui.stats": "Tolllore", "gui.toMenu": "Back to Outreckoner List", "gui.toRealms": "Back to Realms List", "gui.toTitle": "Back to Main Shirm", "gui.toWorld": "Back to World List", "gui.togglable_slot": "Click to lay off groovestead", "gui.up": "Up", "gui.waitingForResponse.button.inactive": "Back (%s braid(s))", "gui.waitingForResponse.title": "Stalling for Outreckoner", "gui.yes": "Yes", "hanging_sign.edit": "Write Hanging Token Writs", "instrument.minecraft.admire_goat_horn": "Belook", "instrument.minecraft.call_goat_horn": "Call", "instrument.minecraft.dream_goat_horn": "Dream", "instrument.minecraft.feel_goat_horn": "Feel", "instrument.minecraft.ponder_goat_horn": "Wonder", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Sing", "instrument.minecraft.yearn_goat_horn": "Yearn", "inventory.binSlot": "Adwash Thing", "inventory.hotbarInfo": "Keep hotband with %1$s+%2$s", "inventory.hotbarSaved": "Thing hotband kept (edstow with %1$s+%2$s)", "item.canBreak": "Can break:", "item.canPlace": "Can be laid on:", "item.canUse.unknown": "Unknown", "item.color": "Hue: %s", "item.components": "%s underdeal(s)", "item.disabled": "Unbrooked thing", "item.durability": "Starkness: %s / %s", "item.dyed": "Dyed", "item.minecraft.acacia_boat": "Wattletree Boat", "item.minecraft.acacia_chest_boat": "Wattletree Boat with Chest", "item.minecraft.allay_spawn_egg": "Allay <PERSON> Ey", "item.minecraft.amethyst_shard": "Drunk<PERSON><PERSON> Shard", "item.minecraft.angler_pottery_shard": "Fisherman <PERSON><PERSON>", "item.minecraft.angler_pottery_sherd": "Fisherman <PERSON><PERSON>", "item.minecraft.apple": "Apple", "item.minecraft.archer_pottery_shard": "<PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON>", "item.minecraft.armadillo_scute": "Gir<PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Girdledeer Making Ey", "item.minecraft.armor_stand": "Hirsting Stand", "item.minecraft.arms_up_pottery_shard": "Arms Up Pot Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up Pot Sherd", "item.minecraft.arrow": "Arrow", "item.minecraft.axolotl_bucket": "Stop of Waterhelper", "item.minecraft.axolotl_spawn_egg": "Waterhelper Making Ey", "item.minecraft.baked_potato": "Baked Earthapple", "item.minecraft.bamboo_chest_raft": "Treereed Float with Chest", "item.minecraft.bamboo_raft": "Treereed Float", "item.minecraft.bat_spawn_egg": "Flittermouse Making Ey", "item.minecraft.bee_spawn_egg": "Bee Making Ey", "item.minecraft.beef": "Raw Cowflesh", "item.minecraft.beetroot": "Red More", "item.minecraft.beetroot_seeds": "Red More Seeds", "item.minecraft.beetroot_soup": "<PERSON> More Broth", "item.minecraft.birch_boat": "<PERSON> Boat", "item.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "Black Dye", "item.minecraft.black_harness": "<PERSON> Read", "item.minecraft.blade_pottery_shard": "<PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON>", "item.minecraft.blaze_powder": "Blaze Dust", "item.minecraft.blaze_rod": "<PERSON>", "item.minecraft.blaze_spawn_egg": "Blaze Making Ey", "item.minecraft.blue_bundle": "<PERSON><PERSON> Bundle", "item.minecraft.blue_dye": "<PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Fenned Making Ey", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.bone": "Bone", "item.minecraft.bone_meal": "<PERSON>", "item.minecraft.book": "Book", "item.minecraft.bordure_indented_banner_pattern": "Sawtoothed Frame Streamer Twill", "item.minecraft.bow": "Bow", "item.minecraft.bowl": "Bowl", "item.minecraft.bread": "Bread", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Whith <PERSON> Ey", "item.minecraft.brewer_pottery_shard": "<PERSON> <PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON>t She<PERSON>", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "Tile", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "<PERSON>", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Stop", "item.minecraft.bundle": "Bundle", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed heap of things", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn <PERSON> Shard", "item.minecraft.burn_pottery_sherd": "Burn Pot Sherd", "item.minecraft.camel_spawn_egg": "Drylander Making Ey", "item.minecraft.carrot": "Walmore", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> on a Stick", "item.minecraft.cat_spawn_egg": "Cat Making Ey", "item.minecraft.cauldron": "Ironcrock", "item.minecraft.cave_spider_spawn_egg": "Hollow Spider Making Ey", "item.minecraft.chainmail_boots": "Mesh Shoes", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON>", "item.minecraft.charcoal": "Charc<PERSON>l", "item.minecraft.cherry_boat": "Stoneberry Boat", "item.minecraft.cherry_chest_boat": "Stoneberry Boat with Chest", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> with Chest", "item.minecraft.chicken": "Raw Chicken", "item.minecraft.chicken_spawn_egg": "Chicken Making Ey", "item.minecraft.chorus_fruit": "<PERSON><PERSON> Ovet", "item.minecraft.clay_ball": "<PERSON>", "item.minecraft.clock": "Daymeal", "item.minecraft.coal": "Coal", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON> Hirsting Trim", "item.minecraft.cocoa_beans": "Muckbeans", "item.minecraft.cod": "Raw Cod", "item.minecraft.cod_bucket": "Stop of Cod", "item.minecraft.cod_spawn_egg": "Cod Making Ey", "item.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON>", "item.minecraft.compass": "Northfinder", "item.minecraft.cooked_beef": "Cooked Cowflesh", "item.minecraft.cooked_chicken": "Cooked Chicken", "item.minecraft.cooked_cod": "Cooked Cod", "item.minecraft.cooked_mutton": "Cooked Sheep", "item.minecraft.cooked_porkchop": "Cooked Swineflesh", "item.minecraft.cooked_rabbit": "Cooked Hare", "item.minecraft.cooked_salmon": "Cooked Lax", "item.minecraft.cookie": "<PERSON><PERSON>", "item.minecraft.copper_ingot": "Are Ingot", "item.minecraft.cow_spawn_egg": "Cow Making Ey", "item.minecraft.creaking_spawn_egg": "Creaking Making Ey", "item.minecraft.creeper_banner_pattern": "Streamer Twill", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.new": "C<PERSON><PERSON> Shape Streamer Twill", "item.minecraft.creeper_spawn_egg": "Creeper Making Ey", "item.minecraft.crossbow": "Steel Bow", "item.minecraft.crossbow.projectile": "Shot:", "item.minecraft.crossbow.projectile.multiple": "Shot: %s x %s", "item.minecraft.crossbow.projectile.single": "Shot: %s", "item.minecraft.cyan_bundle": "Hewngreen Bundle", "item.minecraft.cyan_dye": "Hewngreen Dye", "item.minecraft.cyan_harness": "Hewngreen Read", "item.minecraft.danger_pottery_shard": "Bane Pot Shard", "item.minecraft.danger_pottery_sherd": "Bane Pot Sherd", "item.minecraft.dark_oak_boat": "Dark Oak Boat", "item.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "item.minecraft.debug_stick": "Unbug Stick", "item.minecraft.debug_stick.empty": "%s has no holdings", "item.minecraft.debug_stick.select": "chosen \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" to %s", "item.minecraft.diamond": "Hardhirst", "item.minecraft.diamond_axe": "Hardhirst Axe", "item.minecraft.diamond_boots": "Hardhirst Shoes", "item.minecraft.diamond_chestplate": "Hardhirst Chestshield", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON>lm", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Hardhirst Horse Hirsting", "item.minecraft.diamond_leggings": "Hardhirst Leggings", "item.minecraft.diamond_pickaxe": "Hardhirst Pike", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Hardhirst Sword", "item.minecraft.disc_fragment_5": "<PERSON><PERSON>", "item.minecraft.disc_fragment_5.desc": "Song Shive - 5", "item.minecraft.dolphin_spawn_egg": "Merswine Making Ey", "item.minecraft.donkey_spawn_egg": "Dunky Making Ey", "item.minecraft.dragon_breath": "<PERSON><PERSON>'s Breath", "item.minecraft.dried_kelp": "Dried Ash Seaweed", "item.minecraft.drowned_spawn_egg": "Drenched <PERSON> Ey", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "Sandhill Hirsting Trim", "item.minecraft.echo_shard": "<PERSON><PERSON>", "item.minecraft.egg": "<PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Elder <PERSON> Ey", "item.minecraft.elytra": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Greenhirst", "item.minecraft.enchanted_book": "Galed Book", "item.minecraft.enchanted_golden_apple": "Galed Golden Apple", "item.minecraft.end_crystal": "End Hirst", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ender_eye": "Eye of <PERSON>er", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman Making Ey", "item.minecraft.endermite_spawn_egg": "Endermite Making Ey", "item.minecraft.evoker_spawn_egg": "Awakener Making Ey", "item.minecraft.experience_bottle": "<PERSON><PERSON>", "item.minecraft.explorer_pottery_shard": "Seeker Pot Shard", "item.minecraft.explorer_pottery_sherd": "Seeker Pot Sherd", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON> Hirsting Trim", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Soured Spider Eye", "item.minecraft.field_masoned_banner_pattern": "Field Stonewrighted Streamer Twill", "item.minecraft.filled_map": "<PERSON><PERSON>t", "item.minecraft.fire_charge": "Fire Blast", "item.minecraft.firework_rocket": "Firework", "item.minecraft.firework_rocket.flight": "Flight Lastingness:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Firework Star", "item.minecraft.firework_star.black": "Black", "item.minecraft.firework_star.blue": "Hewn", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "Bespoke", "item.minecraft.firework_star.cyan": "Hewngreen", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON> to", "item.minecraft.firework_star.flicker": "Twinkle", "item.minecraft.firework_star.gray": "<PERSON>", "item.minecraft.firework_star.green": "Green", "item.minecraft.firework_star.light_blue": "Light Hewn", "item.minecraft.firework_star.light_gray": "Light Gray", "item.minecraft.firework_star.lime": "Light Green", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON>", "item.minecraft.firework_star.pink": "Light Red", "item.minecraft.firework_star.purple": "Baze", "item.minecraft.firework_star.red": "Red", "item.minecraft.firework_star.shape": "Unknown <PERSON><PERSON>pe", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "Creeper-shaped", "item.minecraft.firework_star.shape.large_ball": "Great Ball", "item.minecraft.firework_star.shape.small_ball": "Small Ball", "item.minecraft.firework_star.shape.star": "Star-shaped", "item.minecraft.firework_star.trail": "Flightpath", "item.minecraft.firework_star.white": "White", "item.minecraft.firework_star.yellow": "Yellow", "item.minecraft.fishing_rod": "Fishing Rod", "item.minecraft.flint": "Flint", "item.minecraft.flint_and_steel": "Flint and Steel", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Hirsting Trim", "item.minecraft.flow_banner_pattern": "Streamer Twill", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "Flow Streamer Twill", "item.minecraft.flow_pottery_sherd": "Flow Pot Sherd", "item.minecraft.flower_banner_pattern": "Streamer Twill", "item.minecraft.flower_banner_pattern.desc": "Blossom S<PERSON>pe", "item.minecraft.flower_banner_pattern.new": "Blossom <PERSON><PERSON>pe Streamer Twill", "item.minecraft.flower_pot": "Blossom Pot", "item.minecraft.fox_spawn_egg": "Fox Making Ey", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON>", "item.minecraft.frog_spawn_egg": "<PERSON>osh <PERSON> Ey", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON>", "item.minecraft.ghast_spawn_egg": "Ghast Making Ey", "item.minecraft.ghast_tear": "Ghast Tear", "item.minecraft.glass_bottle": "Glass Handvat", "item.minecraft.glistering_melon_slice": "Glistering Entapple Sliver", "item.minecraft.globe_banner_pattern": "Streamer Twill", "item.minecraft.globe_banner_pattern.desc": "World", "item.minecraft.globe_banner_pattern.new": "World Streamer Twill", "item.minecraft.glow_berries": "Glow Berries", "item.minecraft.glow_ink_sac": "Glow Bleck Cod", "item.minecraft.glow_item_frame": "Glow Thing Frame", "item.minecraft.glow_squid_spawn_egg": "Glow Dye Fish Making Ey", "item.minecraft.glowstone_dust": "Glowstone Dust", "item.minecraft.goat_horn": "<PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Goat Making Ey", "item.minecraft.gold_ingot": "Gold Ingot", "item.minecraft.gold_nugget": "Gold Lumpling", "item.minecraft.golden_apple": "Golden Apple", "item.minecraft.golden_axe": "Golden Axe", "item.minecraft.golden_boots": "Golden Shoes", "item.minecraft.golden_carrot": "Golden Walmore", "item.minecraft.golden_chestplate": "Golden Chestshield", "item.minecraft.golden_helmet": "Golden Helm", "item.minecraft.golden_hoe": "<PERSON>", "item.minecraft.golden_horse_armor": "Golden Horse Hirsting", "item.minecraft.golden_leggings": "Golden Leggings", "item.minecraft.golden_pickaxe": "Golden Pike", "item.minecraft.golden_shovel": "Golden Shovel", "item.minecraft.golden_sword": "Golden Sword", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "<PERSON>", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "Green Dye", "item.minecraft.green_harness": "Green Read", "item.minecraft.guardian_spawn_egg": "Ward Making Ey", "item.minecraft.gunpowder": "Gundust", "item.minecraft.guster_banner_pattern": "Streamer Twill", "item.minecraft.guster_banner_pattern.desc": "T<PERSON>der", "item.minecraft.guster_banner_pattern.new": "Thoder Streamer Twill", "item.minecraft.guster_pottery_sherd": "Thoder Pot Sherd", "item.minecraft.happy_ghast_spawn_egg": "<PERSON>t Making Ey", "item.minecraft.harness": "Read", "item.minecraft.heart_of_the_sea": "Heart of the Sea", "item.minecraft.heart_pottery_shard": "Heart Pot Shard", "item.minecraft.heart_pottery_sherd": "Heart Pot Sherd", "item.minecraft.heartbreak_pottery_shard": "Heartbreak <PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak Pot Sherd", "item.minecraft.hoglin_spawn_egg": "Hoglin <PERSON> Ey", "item.minecraft.honey_bottle": "<PERSON>", "item.minecraft.honeycomb": "Honeycomb", "item.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> with <PERSON>", "item.minecraft.horse_spawn_egg": "Horse Making Ey", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Dright Hirsting <PERSON>m", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON> <PERSON>", "item.minecraft.howl_pottery_sherd": "Howl <PERSON>", "item.minecraft.husk_spawn_egg": "Husk Making Ey", "item.minecraft.ink_sac": "Bleck Cod", "item.minecraft.iron_axe": "Iron Axe", "item.minecraft.iron_boots": "Iron Shoes", "item.minecraft.iron_chestplate": "Iron Chestshield", "item.minecraft.iron_golem_spawn_egg": "Iron Livenedman Making Ey", "item.minecraft.iron_helmet": "Iron Helm", "item.minecraft.iron_hoe": "<PERSON> Tiller", "item.minecraft.iron_horse_armor": "Iron Horse Hirsting", "item.minecraft.iron_ingot": "Iron Ingot", "item.minecraft.iron_leggings": "Iron Leggings", "item.minecraft.iron_nugget": "Iron Lumpling", "item.minecraft.iron_pickaxe": "Iron Pike", "item.minecraft.iron_shovel": "Iron Shovel", "item.minecraft.iron_sword": "Iron Sword", "item.minecraft.item_frame": "<PERSON>ame", "item.minecraft.jungle_boat": "Rainwold Boat", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON> Boat with Chest", "item.minecraft.knowledge_book": "Knowledge Book", "item.minecraft.lapis_lazuli": "Hewnstone", "item.minecraft.lava_bucket": "Moltenstone Stop", "item.minecraft.lead": "Lead", "item.minecraft.leather": "Leather", "item.minecraft.leather_boots": "Leather Shoes", "item.minecraft.leather_chestplate": "Leather Shirt", "item.minecraft.leather_helmet": "Leather Hat", "item.minecraft.leather_horse_armor": "Leather Horse Hirsting", "item.minecraft.leather_leggings": "Leather Breeches", "item.minecraft.light_blue_bundle": "Light Hewn Bundle", "item.minecraft.light_blue_dye": "Light Hewn Dye", "item.minecraft.light_blue_harness": "Light Hewn Read", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "Light Gray D<PERSON>", "item.minecraft.light_gray_harness": "<PERSON>", "item.minecraft.lime_bundle": "Light Green Bundle", "item.minecraft.lime_dye": "Light Green Dye", "item.minecraft.lime_harness": "Light Green Read", "item.minecraft.lingering_potion": "Lingering Lib", "item.minecraft.lingering_potion.effect.awkward": "Unhandy Lingering Lib", "item.minecraft.lingering_potion.effect.empty": "Lingering Uncraftbere Lib", "item.minecraft.lingering_potion.effect.fire_resistance": "Lingering Lib of Fire Withstanding", "item.minecraft.lingering_potion.effect.harming": "Lingering Lib of Harming", "item.minecraft.lingering_potion.effect.healing": "Lingering Li<PERSON> of Healing", "item.minecraft.lingering_potion.effect.infested": "Lingering Lib of Incoathing", "item.minecraft.lingering_potion.effect.invisibility": "Lingering Lib of Unseenliness", "item.minecraft.lingering_potion.effect.leaping": "Lingering Lib of Leaping", "item.minecraft.lingering_potion.effect.levitation": "Lingering Lib of Hovering", "item.minecraft.lingering_potion.effect.luck": "Lingering Lib of Luck", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON> Lingering Lib", "item.minecraft.lingering_potion.effect.night_vision": "Lingering Lib of Nightsight", "item.minecraft.lingering_potion.effect.oozing": "Lingering Lib of Oozing", "item.minecraft.lingering_potion.effect.poison": "Lingering Lib of Atter", "item.minecraft.lingering_potion.effect.regeneration": "Lingering Lib of Edhealing", "item.minecraft.lingering_potion.effect.slow_falling": "Lingering Lib of Slow Falling", "item.minecraft.lingering_potion.effect.slowness": "Lingering Lib of Slowness", "item.minecraft.lingering_potion.effect.strength": "Lingering Lib of Strength", "item.minecraft.lingering_potion.effect.swiftness": "Lingering Lib of Swiftness", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON> Lib", "item.minecraft.lingering_potion.effect.turtle_master": "Lingering Lib of the Shellpad Lord", "item.minecraft.lingering_potion.effect.water": "Lingering Water Handvat", "item.minecraft.lingering_potion.effect.water_breathing": "Lingering Lib of Water Breathing", "item.minecraft.lingering_potion.effect.weakness": "Lingering Lib of Woakness", "item.minecraft.lingering_potion.effect.weaving": "Lingering Lib of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Lib of Wind Loading", "item.minecraft.llama_spawn_egg": "Drylandersheep Making Ey", "item.minecraft.lodestone_compass": "Lodestone Northfinder", "item.minecraft.mace": "Beetle", "item.minecraft.magenta_bundle": "Bazered Bundle", "item.minecraft.magenta_dye": "Bazered Dye", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON> Read", "item.minecraft.magma_cream": "Moltenstone Ream", "item.minecraft.magma_cube_spawn_egg": "Moltenstone Slime Making Ey", "item.minecraft.mangrove_boat": "Liftmore Boat", "item.minecraft.mangrove_chest_boat": "Liftmore Boat with Chest", "item.minecraft.map": "Empty Draught", "item.minecraft.melon_seeds": "Entapple Seeds", "item.minecraft.melon_slice": "Entapple Sliver", "item.minecraft.milk_bucket": "Milk Stop", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Delver Pot Shard", "item.minecraft.miner_pottery_sherd": "Delver Pot Sherd", "item.minecraft.mojang_banner_pattern": "Streamer Twill", "item.minecraft.mojang_banner_pattern.desc": "Thing", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Streamer Twill", "item.minecraft.mooshroom_spawn_egg": "Mooshroom Making Ey", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.mule_spawn_egg": "Dunkyhorse Making Ey", "item.minecraft.mushroom_stew": "Toadstool Broth", "item.minecraft.music_disc_11": "Song Shive", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Song Shive", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Song Shive", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Song Shive", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Song Shive", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Song Shive", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Song Shive", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Song Shive", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (Gleecraft Box)", "item.minecraft.music_disc_far": "Song Shive", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Song Shive", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Song Shive", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Song Shive", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Song Shive", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Song Shive", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Song Shive", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Song Shive", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Song Shive", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Song Shive", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Song Shive", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Song Shive", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Song Shive", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Raw Sheep", "item.minecraft.name_tag": "Name Token", "item.minecraft.nautilus_shell": "Shipperfish Shell", "item.minecraft.nether_brick": "Nether Tile", "item.minecraft.nether_star": "Nether Star", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "Netherite Axe", "item.minecraft.netherite_boots": "Netherite Shoes", "item.minecraft.netherite_chestplate": "Netherite Chestshield", "item.minecraft.netherite_helmet": "Netherite Helm", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_ingot": "Netherite Ingot", "item.minecraft.netherite_leggings": "Netherite Leggings", "item.minecraft.netherite_pickaxe": "Netherite Pike", "item.minecraft.netherite_scrap": "Nether<PERSON> Shred", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_sword": "Netherite Sword", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Bolster", "item.minecraft.oak_boat": "Oak Boat", "item.minecraft.oak_chest_boat": "Oak Boat with Chest", "item.minecraft.ocelot_spawn_egg": "Bushcat Making Ey", "item.minecraft.ominous_bottle": "Threatening Handvat", "item.minecraft.ominous_trial_key": "Threatening Fand Key", "item.minecraft.orange_bundle": "<PERSON><PERSON>", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON>", "item.minecraft.painting": "Meting", "item.minecraft.pale_oak_boat": "Bloak Oak Boat", "item.minecraft.pale_oak_chest_boat": "Bloak Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "Catbear Making Ey", "item.minecraft.paper": "Bookfell", "item.minecraft.parrot_spawn_egg": "Bleefowl Making Ey", "item.minecraft.phantom_membrane": "Nightgost Hame", "item.minecraft.phantom_spawn_egg": "Nightgost Making Ey", "item.minecraft.pig_spawn_egg": "Swine Making Ey", "item.minecraft.piglin_banner_pattern": "Streamer Twill", "item.minecraft.piglin_banner_pattern.desc": "Swinenose", "item.minecraft.piglin_banner_pattern.new": "Swinenose Streamer Twill", "item.minecraft.piglin_brute_spawn_egg": "<PERSON> Ey", "item.minecraft.piglin_spawn_egg": "Piglin Making Ey", "item.minecraft.pillager_spawn_egg": "Reaver Making Ey", "item.minecraft.pink_bundle": "Light Red Bundle", "item.minecraft.pink_dye": "Light Red Dye", "item.minecraft.pink_harness": "Light Red Read", "item.minecraft.pitcher_plant": "Vatleaf Wort", "item.minecraft.pitcher_pod": "Vatleaf Pod", "item.minecraft.plenty_pottery_shard": "Fulth Pot Shard", "item.minecraft.plenty_pottery_sherd": "Fulth Pot Sherd", "item.minecraft.poisonous_potato": "Attery Earthapple", "item.minecraft.polar_bear_spawn_egg": "Icebear Making Ey", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON>", "item.minecraft.porkchop": "Raw Swineflesh", "item.minecraft.potato": "Earthapple", "item.minecraft.potion": "Lib", "item.minecraft.potion.effect.awkward": "Unhand<PERSON>", "item.minecraft.potion.effect.empty": "Uncraftbere Lib", "item.minecraft.potion.effect.fire_resistance": "Lib of Fire Withstanding", "item.minecraft.potion.effect.harming": "<PERSON><PERSON> of Harming", "item.minecraft.potion.effect.healing": "<PERSON><PERSON> of Healing", "item.minecraft.potion.effect.infested": "Lib of Incoathing", "item.minecraft.potion.effect.invisibility": "Lib of Unseenliness", "item.minecraft.potion.effect.leaping": "Lib of Leaping", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> of Hovering", "item.minecraft.potion.effect.luck": "Lib of Luck", "item.minecraft.potion.effect.mundane": "Boring Lib", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON> of Nightsight", "item.minecraft.potion.effect.oozing": "Lib of Oozing", "item.minecraft.potion.effect.poison": "<PERSON><PERSON> <PERSON> Atter", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON> of Edhealing", "item.minecraft.potion.effect.slow_falling": "Lib of Slow Falling", "item.minecraft.potion.effect.slowness": "Lib of Slowness", "item.minecraft.potion.effect.strength": "<PERSON><PERSON> of Strength", "item.minecraft.potion.effect.swiftness": "Lib of Swiftness", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Lib of the Shellpad Lord", "item.minecraft.potion.effect.water": "Water Handvat", "item.minecraft.potion.effect.water_breathing": "Lib of Water Breathing", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON> of Woakness", "item.minecraft.potion.effect.weaving": "Lib of Weaving", "item.minecraft.potion.effect.wind_charged": "Lib of Wind Loading", "item.minecraft.pottery_shard_archer": "<PERSON>", "item.minecraft.pottery_shard_arms_up": "Arms Up Pot Shard", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON>", "item.minecraft.pottery_shard_skull": "Headbone <PERSON><PERSON>", "item.minecraft.powder_snow_bucket": "Dust Snow Stop", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON>", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.pufferfish": "Pufferfish", "item.minecraft.pufferfish_bucket": "Stop of Pufferfish", "item.minecraft.pufferfish_spawn_egg": "Pufferfish Making Ey", "item.minecraft.pumpkin_pie": "Harvestovet Bake", "item.minecraft.pumpkin_seeds": "Harvestovet Seeds", "item.minecraft.purple_bundle": "Baze Bundle", "item.minecraft.purple_dye": "Baze Dye", "item.minecraft.purple_harness": "<PERSON><PERSON>", "item.minecraft.quartz": "Nether Hardstone", "item.minecraft.rabbit": "Raw Hare", "item.minecraft.rabbit_foot": "<PERSON>'s Foot", "item.minecraft.rabbit_hide": "Hare Hide", "item.minecraft.rabbit_spawn_egg": "Hare Making Ey", "item.minecraft.rabbit_stew": "<PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Hirsting <PERSON>", "item.minecraft.ravager_spawn_egg": "Weaster Making Ey", "item.minecraft.raw_copper": "Raw Are", "item.minecraft.raw_gold": "Raw Gold", "item.minecraft.raw_iron": "Raw Iron", "item.minecraft.recovery_compass": "Deathfinder", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "Red Dye", "item.minecraft.red_harness": "<PERSON> Read", "item.minecraft.redstone": "Redstone Dust", "item.minecraft.resin_brick": "<PERSON>", "item.minecraft.resin_clump": "<PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "Rotted Flesh", "item.minecraft.saddle": "Saddle", "item.minecraft.salmon": "Raw Lax", "item.minecraft.salmon_bucket": "Stop of Lax", "item.minecraft.salmon_spawn_egg": "Lax Making Ey", "item.minecraft.scrape_pottery_sherd": "S<PERSON><PERSON>", "item.minecraft.scute": "Shale", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "Watchman <PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "Shears", "item.minecraft.sheep_spawn_egg": "Sheep <PERSON>", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shield": "Shield", "item.minecraft.shield.black": "Black Shield", "item.minecraft.shield.blue": "Hewn Shield", "item.minecraft.shield.brown": "Brown Shield", "item.minecraft.shield.cyan": "Hewngreen Shield", "item.minecraft.shield.gray": "Gray Shield", "item.minecraft.shield.green": "Green Shield", "item.minecraft.shield.light_blue": "Light Hewn Shield", "item.minecraft.shield.light_gray": "Light Gray Shield", "item.minecraft.shield.lime": "Light Green Shield", "item.minecraft.shield.magenta": "Bazered Shield", "item.minecraft.shield.orange": "<PERSON><PERSON>", "item.minecraft.shield.pink": "Light Red Shield", "item.minecraft.shield.purple": "Baze Shield", "item.minecraft.shield.red": "Red Shield", "item.minecraft.shield.white": "White Shield", "item.minecraft.shield.yellow": "Yellow Shield", "item.minecraft.shulker_shell": "Shulker Shell", "item.minecraft.shulker_spawn_egg": "Shulker Making Ey", "item.minecraft.sign": "Token", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Stillness Hirsting <PERSON>m", "item.minecraft.silverfish_spawn_egg": "Silverfish Making Ey", "item.minecraft.skeleton_horse_spawn_egg": "Boneframe Horse Making Ey", "item.minecraft.skeleton_spawn_egg": "Boneframe Making Ey", "item.minecraft.skull_banner_pattern": "Streamer Twill", "item.minecraft.skull_banner_pattern.desc": "Headbone S<PERSON>pe", "item.minecraft.skull_banner_pattern.new": "Headbone Shape <PERSON>er Twill", "item.minecraft.skull_pottery_shard": "Headbone <PERSON><PERSON>", "item.minecraft.skull_pottery_sherd": "Headbone <PERSON> She<PERSON>", "item.minecraft.slime_ball": "Slimeball", "item.minecraft.slime_spawn_egg": "Slime Making Ey", "item.minecraft.smithing_template": "<PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Beseeches to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Eke ingot or hirst", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Eke a stetch of hirsting", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Hirsts", "item.minecraft.smithing_template.ingredients": "Anworks:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON>ke <PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Hardhirst Hardware", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Eke hardhirst hirsting, weapon or tool", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Bolster: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Making Ey", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Swinenose Hirsting Trim", "item.minecraft.snow_golem_spawn_egg": "Snowman Making Ey", "item.minecraft.snowball": "Snowball", "item.minecraft.spectral_arrow": "<PERSON><PERSON>", "item.minecraft.spider_eye": "Spider Eye", "item.minecraft.spider_spawn_egg": "<PERSON> Making Ey", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "Splash Lib", "item.minecraft.splash_potion.effect.awkward": "Unhandy S<PERSON>lash <PERSON>b", "item.minecraft.splash_potion.effect.empty": "Splash Uncraftbere Lib", "item.minecraft.splash_potion.effect.fire_resistance": "Splash Lib of Fire Withstanding", "item.minecraft.splash_potion.effect.harming": "Splash Lib of Harming", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON> of Healing", "item.minecraft.splash_potion.effect.infested": "Splash Lib of Incoathing", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON> of Unseenliness", "item.minecraft.splash_potion.effect.leaping": "S<PERSON><PERSON> Lib of Leaping", "item.minecraft.splash_potion.effect.levitation": "S<PERSON><PERSON> Lib of Hovering", "item.minecraft.splash_potion.effect.luck": "Splash Lib of Luck", "item.minecraft.splash_potion.effect.mundane": "Bo<PERSON> Splash Lib", "item.minecraft.splash_potion.effect.night_vision": "Splash Lib of Nightsight", "item.minecraft.splash_potion.effect.oozing": "Splash Lib of Oozing", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON> Lib of Atter", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON> Lib of Edhealing", "item.minecraft.splash_potion.effect.slow_falling": "Splash Lib of Slow Falling", "item.minecraft.splash_potion.effect.slowness": "Splash Lib of Slowness", "item.minecraft.splash_potion.effect.strength": "S<PERSON>lash Lib of Strength", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON> Lib of Swiftness", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON> Splash Lib", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON> Lib of the Shellpad Lord", "item.minecraft.splash_potion.effect.water": "Splash Water Handvat", "item.minecraft.splash_potion.effect.water_breathing": "Splash Lib of Water Breathing", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON> Lib of Woakness", "item.minecraft.splash_potion.effect.weaving": "Splash Lib of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Lib of Wind Loading", "item.minecraft.spruce_boat": "Harttartree Boat", "item.minecraft.spruce_chest_boat": "Harttartree Boat with Chest", "item.minecraft.spyglass": "Zooming Glass", "item.minecraft.squid_spawn_egg": "Sleevefish Making Ey", "item.minecraft.stick": "Stick", "item.minecraft.stone_axe": "Stone Axe", "item.minecraft.stone_hoe": "<PERSON>", "item.minecraft.stone_pickaxe": "Stone Pike", "item.minecraft.stone_shovel": "<PERSON>el", "item.minecraft.stone_sword": "Stone Sword", "item.minecraft.stray_spawn_egg": "Drifter Making Ey", "item.minecraft.strider_spawn_egg": "Strider Making Ey", "item.minecraft.string": "String", "item.minecraft.sugar": "Sweetdust", "item.minecraft.suspicious_stew": "Weird Broth", "item.minecraft.sweet_berries": "Sweet Berries", "item.minecraft.tadpole_bucket": "Stop of Toadhead", "item.minecraft.tadpole_spawn_egg": "Toadhead Making Ey", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON> Hirsting Trim", "item.minecraft.tipped_arrow": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "Uncraftbere Tipped Arrow", "item.minecraft.tipped_arrow.effect.fire_resistance": "Arrow of Fire Withstanding", "item.minecraft.tipped_arrow.effect.harming": "Arrow of Harming", "item.minecraft.tipped_arrow.effect.healing": "Arrow of Healing", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Incoathing", "item.minecraft.tipped_arrow.effect.invisibility": "Arrow of Unseenliness", "item.minecraft.tipped_arrow.effect.leaping": "Arrow of Leaping", "item.minecraft.tipped_arrow.effect.levitation": "Arrow of Hovering", "item.minecraft.tipped_arrow.effect.luck": "Arrow of Luck", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "Arrow of Nightsight", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "Arrow of Atter", "item.minecraft.tipped_arrow.effect.regeneration": "Arrow of Edhealing", "item.minecraft.tipped_arrow.effect.slow_falling": "Arrow of Slow Falling", "item.minecraft.tipped_arrow.effect.slowness": "Arrow of Slowness", "item.minecraft.tipped_arrow.effect.strength": "Arrow of Strength", "item.minecraft.tipped_arrow.effect.swiftness": "Arrow of Swiftness", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "Arrow of the Shellpad Lord", "item.minecraft.tipped_arrow.effect.water": "Arrow of Splashing", "item.minecraft.tipped_arrow.effect.water_breathing": "Arrow of Water Breathing", "item.minecraft.tipped_arrow.effect.weakness": "Arrow of Woakness", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Loading", "item.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON>", "item.minecraft.torchflower_seeds": "Thackleblossom Seeds", "item.minecraft.totem_of_undying": "Token of Undying", "item.minecraft.trader_llama_spawn_egg": "Chapman Drylandersheep Making Ey", "item.minecraft.trial_key": "Fand Key", "item.minecraft.trident": "<PERSON><PERSON>", "item.minecraft.tropical_fish": "Southsea Fish", "item.minecraft.tropical_fish_bucket": "Stop of Southsea Fish", "item.minecraft.tropical_fish_spawn_egg": "Southsea Fish Making Ey", "item.minecraft.turtle_helmet": "Shellpad Shell", "item.minecraft.turtle_scute": "Shellpad Shale", "item.minecraft.turtle_spawn_egg": "Shellpad Making Ey", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Net<PERSON> Hirsting Trim", "item.minecraft.vex_spawn_egg": "Nettler Making Ey", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON><PERSON> Making Ey", "item.minecraft.vindicator_spawn_egg": "Whitewasher Making Ey", "item.minecraft.wandering_trader_spawn_egg": "Wandering Chapman Making Ey", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Holdend Making Ey", "item.minecraft.warped_fungus_on_a_stick": "Warped Swamb on a Stick", "item.minecraft.water_bucket": "Water Stop", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Hirsting Trim", "item.minecraft.wheat": "Wheat", "item.minecraft.wheat_seeds": "Wheat Seeds", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "White Dye", "item.minecraft.white_harness": "<PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON>rst<PERSON>m", "item.minecraft.wind_charge": "Wind Blast", "item.minecraft.witch_spawn_egg": "Witch Making Ey", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> Making Ey", "item.minecraft.wither_spawn_egg": "Wither <PERSON> Ey", "item.minecraft.wolf_armor": "<PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON> Ey", "item.minecraft.wooden_axe": "Wooden Axe", "item.minecraft.wooden_hoe": "<PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Wooden Pike", "item.minecraft.wooden_shovel": "<PERSON>", "item.minecraft.wooden_sword": "Wood Sword", "item.minecraft.writable_book": "Book and Quill", "item.minecraft.written_book": "Written Book", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "Yellow Dye", "item.minecraft.yellow_harness": "Yellow Read", "item.minecraft.zoglin_spawn_egg": "Zoglin Making Ey", "item.minecraft.zombie_horse_spawn_egg": "Undead Horse Making Ey", "item.minecraft.zombie_spawn_egg": "Undead Lich Making Ey", "item.minecraft.zombie_villager_spawn_egg": "Undead Thorpsman Making Ey", "item.minecraft.zombified_piglin_spawn_egg": "Undead Piglin Making Ey", "item.modifiers.any": "When bedighted:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When bedighted:", "item.modifiers.chest": "When on Chest:", "item.modifiers.feet": "When on Feet:", "item.modifiers.hand": "When held:", "item.modifiers.head": "When on Head:", "item.modifiers.legs": "When on Legs:", "item.modifiers.mainhand": "When in Main Hand:", "item.modifiers.offhand": "When in Off Hand:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NTT: %s token(s)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Brook of this thing might lead to hest running", "item.op_block_warning.line3": "Do not brook unless you know the alsuch inholdings!", "item.unbreakable": "Unbreakbere", "itemGroup.buildingBlocks": "Building Clots", "itemGroup.coloredBlocks": "<PERSON><PERSON>s", "itemGroup.combat": "Fighting", "itemGroup.consumables": "<PERSON><PERSON><PERSON>", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "Food & Drinks", "itemGroup.functional": "Working Clots", "itemGroup.hotbar": "Kept Hotbands", "itemGroup.ingredients": "Anworks", "itemGroup.inventory": "Overlive Inholding", "itemGroup.natural": "Wild Clots", "itemGroup.op": "Overseer Tools", "itemGroup.redstone": "Redstone Clots", "itemGroup.search": "Seek Things", "itemGroup.spawnEggs": "Making Eyren", "itemGroup.tools": "Tools", "item_modifier.unknown": "Unknown thing tweak: %s", "jigsaw_block.final_state": "Becomes:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "Unwrithing", "jigsaw_block.joint.rollable": "Wharvely", "jigsaw_block.joint_label": "Lith kind:", "jigsaw_block.keep_jigsaws": "Keep Inlocks", "jigsaw_block.levels": "Layers: %s", "jigsaw_block.name": "Name:", "jigsaw_block.placement_priority": "Stow Forehood:", "jigsaw_block.placement_priority.tooltip": "When this Inlock clot links to a stetch, this is the order in which that stetch is foreworked for links in the wider framework.\n\nStetches will be foreworked in downgoing forehood with input order breaking ties.", "jigsaw_block.pool": "Mark Pool:", "jigsaw_block.selection_priority": "<PERSON><PERSON>:", "jigsaw_block.selection_priority.tooltip": "When the kennend stetch is being foreworked for links, this is the order in which this Inlock clot fands to link to its mark stetch.\n\nInlocks will be foreworked in downgoing forehood with hapsome ordering breaking ties.", "jigsaw_block.target": "Mark name:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (Gleecraft Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Forthsteps", "key.attack": "Strike/Break", "key.back": "Walk Backward", "key.categories.creative": "<PERSON><PERSON> Wayset", "key.categories.gameplay": "Gameplay", "key.categories.inventory": "Inholding", "key.categories.misc": "Sundries", "key.categories.movement": "Shrithing", "key.categories.multiplayer": "Maniplayer", "key.categories.ui": "<PERSON> R<PERSON>leer", "key.chat": "Open Chat", "key.command": "Open Heft", "key.drop": "Drop Chosen Thing", "key.forward": "Walk Forward", "key.fullscreen": "Toggle Fullshirm", "key.hotbar.1": "Hotband Groovestead 1", "key.hotbar.2": "Hotband Groovestead 2", "key.hotbar.3": "Hotband Groovestead 3", "key.hotbar.4": "Hotband Groovestead 4", "key.hotbar.5": "Hotband Groovestead 5", "key.hotbar.6": "Hotband Groovestead 6", "key.hotbar.7": "Hotband Groovestead 7", "key.hotbar.8": "Hotband Groovestead 8", "key.hotbar.9": "Hotband Groovestead 9", "key.inventory": "Open/Shut Inholding", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backgap", "key.keyboard.caps.lock": "Greatstaves Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON>", "key.keyboard.down": "Down Arrow", "key.keyboard.end": "End", "key.keyboard.enter": "Streakbreak", "key.keyboard.equal": "=", "key.keyboard.escape": "Atwind", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Input", "key.keyboard.keypad.0": "Keypad 0", "key.keyboard.keypad.1": "Keypad 1", "key.keyboard.keypad.2": "Keypad 2", "key.keyboard.keypad.3": "Keypad 3", "key.keyboard.keypad.4": "Keypad 4", "key.keyboard.keypad.5": "Keypad 5", "key.keyboard.keypad.6": "Keypad 6", "key.keyboard.keypad.7": "Keypad 7", "key.keyboard.keypad.8": "Keypad 8", "key.keyboard.keypad.9": "Keypad 9", "key.keyboard.keypad.add": "Keypad +", "key.keyboard.keypad.decimal": "Keypad Dot", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "Keypad Streakbreak", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "Left Arrow", "key.keyboard.left.alt": "<PERSON> Wrixle", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Left Steerer", "key.keyboard.left.shift": "Left Shift", "key.keyboard.left.win": "Left Win", "key.keyboard.menu": "List", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Rime Lock", "key.keyboard.page.down": "Sheet Down", "key.keyboard.page.up": "Sheet Up", "key.keyboard.pause": "Stop", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.right": "Right Arrow", "key.keyboard.right.alt": "Right W<PERSON>le", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Right Steerer", "key.keyboard.right.shift": "Right Shift", "key.keyboard.right.win": "Right Win", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Gap", "key.keyboard.tab": "Stead", "key.keyboard.unknown": "Not Bound", "key.keyboard.up": "Up Arrow", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "Sidestep Left", "key.loadToolbarActivator": "Load Hotband Astirrer", "key.mouse": "Key %1$s", "key.mouse.left": "Left Key", "key.mouse.middle": "Middle Key", "key.mouse.right": "Right Key", "key.pickItem": "<PERSON>", "key.playerlist": "List Players", "key.quickActions": "<PERSON> Deeds", "key.right": "Sidestep Right", "key.saveToolbarActivator": "Keep Hotband Astirrer", "key.screenshot": "<PERSON><PERSON>", "key.smoothCamera": "Toggle Filmish Byldmaker", "key.sneak": "<PERSON><PERSON><PERSON>", "key.socialInteractions": "Mingling Shirm", "key.spectatorOutlines": "Highlight Players (Onlookers)", "key.sprint": "Sprint", "key.swapOffhand": "Swap Thing With Off Hand", "key.togglePerspective": "Toggle Outlook", "key.use": "Brook Thing/Lay Clot", "known_server_link.announcements": "Kithings", "known_server_link.community": "Fellowship", "known_server_link.community_guidelines": "Fellowship Steer", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Moots", "known_server_link.news": "News", "known_server_link.report_bug": "<PERSON><PERSON> Bug", "known_server_link.status": "Standing", "known_server_link.support": "Upholding", "known_server_link.website": "Webstead", "lanServer.otherPlayers": "Settings for Other Players", "lanServer.port": "Harbor Rime", "lanServer.port.invalid": "Not a brookbere harbor.\nLeave the bework box empty or input a rime between 1024 and 65535.", "lanServer.port.invalid.new": "Not a brookbere harbor.\nLeave the bework box empty or input a rime between %s and %s.", "lanServer.port.unavailable": "Harbor not forthcoming.\nLeave the bework box empty or input another rime between 1024 and 65535.", "lanServer.port.unavailable.new": "Harbor not forthcoming.\nLeave the bework box empty or input another rime between %s and %s.", "lanServer.scanning": "Seeking games on your neigh network", "lanServer.start": "Start NSN World", "lanServer.title": "NSN World", "language.code": "qep", "language.name": "Anglish", "language.region": "<PERSON><PERSON>", "lectern.take_book": "<PERSON><PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "Read more about <PERSON><PERSON><PERSON> and sundry wields", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang brooks some means to help shield children and hir sundry such as following the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to fetch kennend's thaving before reaching your Realms reckoning.", "mco.account.privacyinfo": "Mojang brooks some means to help shield children and hir sundry such as following the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to fetch kennend's thaving before reaching your Realms reckoning.\n\nIf you have an older Minecraft reckoning (you go in with your brookern<PERSON>), you need to wander out the reckoning to a Mojang reckoning so as to reach Realms.", "mco.account.update": "Anwarden reckoning", "mco.activity.noactivity": "No doing for the former %s day(s)", "mco.activity.title": "Player doing", "mco.backup.button.download": "Download Latest", "mco.backup.button.reset": "Edset World", "mco.backup.button.restore": "Edstowe", "mco.backup.button.upload": "Upload World", "mco.backup.changes.tooltip": "<PERSON><PERSON>", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Retching", "mco.backup.entry.enabledPack": "Laid On Pack(s)", "mco.backup.entry.gameDifficulty": "<PERSON> Toughness", "mco.backup.entry.gameMode": "Game Wayset", "mco.backup.entry.gameServerVersion": "Game Outreckoner Wharve", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Forelay Name", "mco.backup.entry.undefined": "Unmarked Wend", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Kind", "mco.backup.generate.world": "Beyet world", "mco.backup.info.title": "Wends From Last Backup", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "This Realm doesn't have any backups anwardly.", "mco.backup.restoring": "Edstowing your Realm", "mco.backup.unknown": "UNKNOWN", "mco.brokenworld.download": "Download", "mco.brokenworld.downloaded": "Downloaded", "mco.brokenworld.message.line1": "Kindly edset or choose another world.", "mco.brokenworld.message.line2": "You can also choose to download the world in oneplayer.", "mco.brokenworld.minigame.title": "This smallgame is no longer upheld", "mco.brokenworld.nonowner.error": "Kindly stall for the Realm owner to edset the world", "mco.brokenworld.nonowner.title": "World is unanwardened", "mco.brokenworld.play": "Play", "mco.brokenworld.reset": "<PERSON><PERSON>", "mco.brokenworld.title": "Your anward world is no longer upheld", "mco.client.incompatible.msg.line1": "Your software is not kindred with Realms.", "mco.client.incompatible.msg.line2": "Kindly brook the newest wharve of Minecraft.", "mco.client.incompatible.msg.line3": "Realms is not kindred with showshot wharves.", "mco.client.incompatible.title": "Software unkindred!", "mco.client.outdated.stable.version": "Your software wharve (%s) is not kindred with Realms.\n\nKindly brook the newest wharve of Minecraft.", "mco.client.unsupported.snapshot.version": "Your software wharve (%s) is not kindred with Realms.\n\nRealms is not forthcoming for this showshot wharve.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "The world was last played in wharve %s; you are on wharve %s. Worsening a world could make fenowedness - we cannot sicker that it will load or work.\n\nA backup of your world will be kept under \"World Backups\". Kindly edstow your world if needed.", "mco.compatibility.incompatible.popup.title": "Unkindred wharve", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are fanding to fay is unkindred with the wharve you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in wharve %s; you are on wharve %s.\n\nThese strings are not kindred with each other. A new world is needed to play on this wharve.", "mco.compatibility.unverifiable.message": "The wharve this world was last played in could not be asoothed. If the world is bolstered or worsened, a backup will be made and kept under \"World Backups\".", "mco.compatibility.unverifiable.title": "Kindredness not asoothenly", "mco.compatibility.upgrade": "<PERSON>ls<PERSON>", "mco.compatibility.upgrade.description": "This world was last played in wharve %s; you are on wharve %s.\n\nA backup of your world will be kept under \"World Backups\". \n\nKindly edstow your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in wharve %s; you are on wharve %s.\n\nA backup of the world will be kept under \"World Backups\".\n\nThe owner of the Realm can edstow the world if needed.", "mco.compatibility.upgrade.title": "Do you truly wish to bolster this world?", "mco.configure.current.minigame": "<PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Player feed laid off for a while", "mco.configure.world.backup": "World Backups", "mco.configure.world.buttons.activity": "Player doing", "mco.configure.world.buttons.close": "<PERSON><PERSON> Shut Realm", "mco.configure.world.buttons.delete": "<PERSON><PERSON>", "mco.configure.world.buttons.done": "Done", "mco.configure.world.buttons.edit": "Settings", "mco.configure.world.buttons.invite": "Lathe Player", "mco.configure.world.buttons.moreoptions": "More kires", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Edopen Realm", "mco.configure.world.buttons.options": "World Kires", "mco.configure.world.buttons.players": "Players", "mco.configure.world.buttons.region_preference": "Choose Erd...", "mco.configure.world.buttons.resetworld": "Edset World", "mco.configure.world.buttons.save": "Keep", "mco.configure.world.buttons.settings": "Settings", "mco.configure.world.buttons.subscription": "Underwriting", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON>", "mco.configure.world.close.question.line1": "You can tidely shut your Realm, forestalling play while you make forestellings. Open it back up when you're ready. \n\nThis does not belay your Realms Underwriting.", "mco.configure.world.close.question.line2": "Are you wis you wish to go on?", "mco.configure.world.close.question.title": "Need to make wends without breaking in?", "mco.configure.world.closing": "Tidely shutting the Realm...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>s", "mco.configure.world.delete.button": "Adwash Realm", "mco.configure.world.delete.question.line1": "Your Realm will be adwashed forevermore", "mco.configure.world.delete.question.line2": "Are wis you wish to go on?", "mco.configure.world.description": "Realm Retching", "mco.configure.world.edit.slot.name": "World Name", "mco.configure.world.edit.subscreen.adventuremap": "Some settings are off as your running world is a wayspelling", "mco.configure.world.edit.subscreen.experience": "Some settings are off since your running world is an afaring", "mco.configure.world.edit.subscreen.inspiration": "Some settings are off since your running world is a beghasting", "mco.configure.world.forceGameMode": "Fordrive Game Wayset", "mco.configure.world.invite.narration": "You have %s new lathing(s)", "mco.configure.world.invite.profile.name": "Name", "mco.configure.world.invited": "Lathed", "mco.configure.world.invited.number": "Lathed (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Overseer", "mco.configure.world.invites.remove.tooltip": "Fornim", "mco.configure.world.leave.question.line1": "If you leave this Realm you won't see it unless you are lathed ayen", "mco.configure.world.leave.question.line2": "Are you wis you wish to go on?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Now: %s", "mco.configure.world.name": "Realm Name", "mco.configure.world.opening": "Opening the Realm...", "mco.configure.world.players.error": "A player with the yeaven name does not bestand", "mco.configure.world.players.inviting": "Lathing player...", "mco.configure.world.players.title": "Players", "mco.configure.world.pvp": "PAP", "mco.configure.world.region_preference": "<PERSON><PERSON>", "mco.configure.world.region_preference.title": "<PERSON><PERSON>", "mco.configure.world.reset.question.line1": "Your world will be freshened and your anward world will be lost", "mco.configure.world.reset.question.line2": "Are you wis you wish to go on?", "mco.configure.world.resourcepack.question": "You need a bespoke lode pack to play on this Realm\n\nDo you wish to download it and play?", "mco.configure.world.resourcepack.question.line1": "You need a bespoke lode pack to play on this Realm", "mco.configure.world.resourcepack.question.line2": "Do you wish to download it and play?", "mco.configure.world.restore.download.question.line1": "The world will be downloaded and eked to your one player worlds.", "mco.configure.world.restore.download.question.line2": "Do you wish to go on?", "mco.configure.world.restore.question.line1": "Your world will be edstowed to talemark '%s' (%s)", "mco.configure.world.restore.question.line2": "Are you wis you wish to go on?", "mco.configure.world.settings.expired": "You cannot bework settings of a quenched Realm", "mco.configure.world.settings.title": "Settings", "mco.configure.world.slot": "World %s", "mco.configure.world.slot.empty": "Empty", "mco.configure.world.slot.switch.question.line1": "Your Realm will be wended to another world", "mco.configure.world.slot.switch.question.line2": "Are you wis you wish to go on?", "mco.configure.world.slot.tooltip": "Go to world", "mco.configure.world.slot.tooltip.active": "<PERSON>", "mco.configure.world.slot.tooltip.minigame": "Go to smallgame", "mco.configure.world.spawnAnimals": "Beyet Deer", "mco.configure.world.spawnMonsters": "Beyet Fiends", "mco.configure.world.spawnNPCs": "Beyet hoads that are not players", "mco.configure.world.spawnProtection": "Edstarting Shielding", "mco.configure.world.spawn_toggle.message": "Laying this kire off will fornim all bestanding ansens of that kind", "mco.configure.world.spawn_toggle.message.npc": "Laying this kire off will fornim all bestanding ansens of that kind, like <PERSON><PERSON><PERSON>", "mco.configure.world.spawn_toggle.title": "Warning!", "mco.configure.world.status": "Standing", "mco.configure.world.subscription.day": "day", "mco.configure.world.subscription.days": "days", "mco.configure.world.subscription.expired": "Quenched", "mco.configure.world.subscription.extend": "Lengthen Underwriting", "mco.configure.world.subscription.less_than_a_day": "Less than a day", "mco.configure.world.subscription.month": "month", "mco.configure.world.subscription.months": "months", "mco.configure.world.subscription.recurring.daysleft": "Self-ednewed in", "mco.configure.world.subscription.recurring.info": "Wends made to your Realms underwriting such as heaping time or laying off edledging buying will not be shown until your next buying talemark.", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.start": "Start Talemark", "mco.configure.world.subscription.tab": "Underwriting", "mco.configure.world.subscription.timeleft": "Time Left", "mco.configure.world.subscription.title": "Your Underwriting", "mco.configure.world.subscription.unknown": "Unknown", "mco.configure.world.switch.slot": "Make World", "mco.configure.world.switch.slot.subtitle": "This world is empty, choose how to make your world", "mco.configure.world.title": "Set up Realm:", "mco.configure.world.uninvite.player": "Are you wis that you wish to unlathe '%s'?", "mco.configure.world.uninvite.question": "Are you wis that you wish to unlathe", "mco.configure.worlds.title": "Worlds", "mco.connect.authorizing": "Going in...", "mco.connect.connecting": "Binding to the Realm...", "mco.connect.failed": "Could not bind to the Realm", "mco.connect.region": "Outreckoner erd: %s", "mco.connect.success": "Done", "mco.create.world": "Make", "mco.create.world.error": "You must input a name!", "mco.create.world.failed": "Could not make world!", "mco.create.world.reset.title": "Making world...", "mco.create.world.skip": "Leave out", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON>, choose what world to put on your new Realm", "mco.create.world.wait": "Making the Realm...", "mco.download.cancelled": "Download belaid", "mco.download.confirmation.line1": "The world you are going to download is greater than %s", "mco.download.confirmation.line2": "You won't be fit to upload this world to your Realm ayen", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou cannot upload this world to your Realm ayen", "mco.download.done": "Download done", "mco.download.downloading": "Downloading", "mco.download.extracting": "Withdrawing", "mco.download.failed": "Download trucked", "mco.download.percent": "%s %%", "mco.download.preparing": "Readying download", "mco.download.resourcePack.fail": "Could not download lode pack!", "mco.download.speed": "(%s/braid)", "mco.download.speed.narration": "%s/braid", "mco.download.title": "Downloading Latest World", "mco.error.invalid.session.message": "Kindly fand edstarting Minecraft", "mco.error.invalid.session.title": "Unright Besitting", "mco.errorMessage.6001": "Software unanwardened", "mco.errorMessage.6002": "Words of followth not borne", "mco.errorMessage.6003": "Download threshold reached", "mco.errorMessage.6004": "Upload threshold reached", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is unanwardened", "mco.errorMessage.6007": "Brooker in too many Realms", "mco.errorMessage.6008": "Unright Realm name", "mco.errorMessage.6009": "Unright Realm retching", "mco.errorMessage.connectionFailure": "A dwale befallen, kindly fand ayen later.", "mco.errorMessage.generic": "A dwale befallen: ", "mco.errorMessage.initialize.failed": "Could not begin Realm", "mco.errorMessage.noDetails": "No dwale insights yeaven", "mco.errorMessage.realmsService": "A dwale befallen (%s):", "mco.errorMessage.realmsService.configurationError": "An unweened dwale befallen while beworking world kires", "mco.errorMessage.realmsService.connectivity": "Could not bind to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not soothe kindred wharve, reaped answer: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> freeming", "mco.errorMessage.serviceBusy": "Realms is busy right now.\nKindly fand binding to your Realm ayen in a stoundle.", "mco.gui.button": "Key", "mco.gui.ok": "Alright", "mco.info": "Abrst!", "mco.invited.player.narration": "Lathed player %s", "mco.invites.button.accept": "Bear", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "No ongoing lathings!", "mco.invites.pending": "New lathing(s)!", "mco.invites.title": "Ongoing Lathings", "mco.minigame.world.changeButton": "Choose Another Smallgame", "mco.minigame.world.info.line1": "This will tidely swap out your world with a smallgame!", "mco.minigame.world.info.line2": "You can later eftcome to your first world without losing anything.", "mco.minigame.world.noSelection": "Kindly choose one", "mco.minigame.world.restore": "Ending Smallgame...", "mco.minigame.world.restore.question.line1": "The smallgame will end and your Realm will be edstowed.", "mco.minigame.world.restore.question.line2": "Are you wis you wish to go on?", "mco.minigame.world.selected": "<PERSON><PERSON> Smallgame:", "mco.minigame.world.slot.screen.title": "Wending World...", "mco.minigame.world.startButton": "<PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Starting Smallgame...", "mco.minigame.world.stopButton": "End Smallgame", "mco.minigame.world.switch.new": "Choose another smallgame?", "mco.minigame.world.switch.title": "<PERSON><PERSON>", "mco.minigame.world.title": "Wend Realm to Smallgame", "mco.news": "Realms news", "mco.notification.dismiss": "Shut", "mco.notification.transferSubscription.buttonText": "Shift Now", "mco.notification.transferSubscription.message": "Java Realms underwritings are shifting to the Microsoft Chapstow. Do not let your underwriting quench!\nShift now and reap 30 days of Realms for free.\nGo to Selfleaf on minecraft.net to shift your underwriting.", "mco.notification.visitUrl.buttonText.default": "Open Link", "mco.notification.visitUrl.message.default": "Kindly go to the link underneath", "mco.onlinePlayers": "Onweb Players", "mco.play.button.realm.closed": "Realm is shut", "mco.question": "Asking", "mco.reset.world.adventure": "Wayspellings", "mco.reset.world.experience": "Cunnings", "mco.reset.world.generate": "New World", "mco.reset.world.inspiration": "Beghasting", "mco.reset.world.resetting.screen.title": "Edsetting world...", "mco.reset.world.seed": "Seed (Kirely)", "mco.reset.world.template": "World Forelays", "mco.reset.world.title": "Edset World", "mco.reset.world.upload": "Upload world", "mco.reset.world.warning": "This will swap out the anward world of your Realm", "mco.selectServer.buy": "Buy a Realm!", "mco.selectServer.close": "Shut", "mco.selectServer.closed": "Unastirred <PERSON>", "mco.selectServer.closeserver": "Shut Realm", "mco.selectServer.configure": "Set up Realm", "mco.selectServer.configureRealm": "Set up Realm", "mco.selectServer.create": "Make Realm", "mco.selectServer.create.subtitle": "Choose what world to put on your new Realm", "mco.selectServer.expired": "Quenched Realm", "mco.selectServer.expiredList": "Your underwriting has ended", "mco.selectServer.expiredRenew": "Anew", "mco.selectServer.expiredSubscribe": "Underwrite", "mco.selectServer.expiredTrial": "Your fand has ended", "mco.selectServer.expires.day": "Ends in a day", "mco.selectServer.expires.days": "Ends in %s days", "mco.selectServer.expires.soon": "Ends soon", "mco.selectServer.leave": "Leave Realm", "mco.selectServer.loading": "Loading Realm List", "mco.selectServer.mapOnlySupportedForVersion": "This draught is not upheld in %s", "mco.selectServer.minigame": "Smallgame:", "mco.selectServer.minigameName": "Smallgame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Can't play this smallgame in %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Eke a Realm to play together with your friends.", "mco.selectServer.note": "Heed:", "mco.selectServer.open": "Open Realm", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "Play", "mco.selectServer.popup": "Realms is a shielded, onefold way to gladden an onweb Minecraft world with up to ten friends at a time. It upholds loads of smallgames and many bespoke worlds! Only the owner of the realm needs to yield.", "mco.selectServer.purchase": "Add Realm", "mco.selectServer.trial": "Reap a Fand!", "mco.selectServer.uninitialized": "Click to start your new Realm!", "mco.snapshot.createSnapshotPopup.text": "You are about to make a free Showshot Realm that will be put with your yielded Realms underwriting. This new Showshot Realm will be forthcoming for as long as the bought underwriting is astirred. Your yielded Realm will not be onworked.", "mco.snapshot.createSnapshotPopup.title": "Make Showshot Realm?", "mco.snapshot.creating": "Making Showshot Realm...", "mco.snapshot.description": "Put with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on wharve %s to fay this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to bolster hir <PERSON> before you can play from this wharve", "mco.snapshot.paired": "This Showshot Realm is put with \"%s\"", "mco.snapshot.parent.tooltip": "<PERSON> the latest leese of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Showshot Realm", "mco.snapshot.subscription.info": "This is a Showshot Realm that is put to the underwriting of your Realm '%s'. It will keep astirred for as long as its put Realm is.", "mco.snapshot.tooltip": "Brook Showshot Realms to reap a forelook at tocome wharves of Minecraft, which might in new marks and other wends.\n\nYou can find your wonted Realms in the leese wharve of the game.", "mco.snapshotRealmsPopup.message": "Realms are now forthcoming in Showshots starting with Showshot 23w41a. Every Realms underwriting comes with a free Showshot Realm as well as your wonted Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now forthcoming in Showshots", "mco.snapshotRealmsPopup.urlText": "Learn More", "mco.template.button.publisher": "<PERSON><PERSON><PERSON>", "mco.template.button.select": "<PERSON><PERSON>", "mco.template.button.trailer": "Foreshow", "mco.template.default.name": "World forelay", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON> webstead", "mco.template.name": "<PERSON><PERSON><PERSON>", "mco.template.select.failure": "We couldn't find the list of inholding for this kind.\nKindly soothe your web binding, or fand ayen later.", "mco.template.select.narrate.authors": "Bookers: %s", "mco.template.select.narrate.version": "wharve %s", "mco.template.select.none": "Oops, it looks like this inholding kind is now empty.\nKindly soothe back later for new inholding, or if you're a maker,\n%s.", "mco.template.select.none.linkTitle": "think about inputting something yourself", "mco.template.title": "World forelays", "mco.template.title.minigame": "Smallgames", "mco.template.trailer.tooltip": "Draught forelook", "mco.terms.buttons.agree": "Aqueath", "mco.terms.buttons.disagree": "Don't aqueath", "mco.terms.sentence.1": "I aqueath to the Minecraft Realms", "mco.terms.sentence.2": "Words of Followth", "mco.terms.title": "Realms Words of Followth", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s stound(s) ago", "mco.time.minutesAgo": "%1$s stoundle(s) ago", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s braid(s) ago", "mco.trial.message.line1": "Wish to have your own Realm?", "mco.trial.message.line2": "Click here for more abrst!", "mco.upload.button.name": "Upload", "mco.upload.cancelled": "Upload belaid", "mco.upload.close.failure": "Could not shut your Realm, kindly fand ayen later", "mco.upload.done": "Upload done", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Upload trucked! (%s)", "mco.upload.failed.too_big.description": "The chosen world is too great. The greatest aleaved breadth is %s.", "mco.upload.failed.too_big.title": "World too great", "mco.upload.hardcore": "Hardheart worlds can't be uploaded!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Readying your world", "mco.upload.select.world.none": "No oneplayer worlds found!", "mco.upload.select.world.subtitle": "Kindly choose a oneplayer world to upload", "mco.upload.select.world.title": "Upload World", "mco.upload.size.failure.line1": "'%s' is too great!", "mco.upload.size.failure.line2": "It is %s. The greatest aleaved breadth is %s.", "mco.upload.uploading": "Uploading '%s'", "mco.upload.verifying": "Asoothing your world", "mco.version": "Wharve: %s", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Smallgame", "menu.custom_options": "Bespoke Kires...", "menu.custom_options.title": "Bespoke Kires", "menu.custom_options.tooltip": "Heed: Bespoke kires are yeaven by third-group outreckoners and/or inholding.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a bespoke shirm. Learn more.", "menu.custom_screen_info.contents": "The inholdings of this shirm are steered by third-group outreckoners and draughts that are not owned, run, or overseen by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never yeave away your leedy abreasting, inning go-in insights.\n\nIf this shirm forestalls you from playing, you can also unbind from the anward outreckoner by brooking the key beneath.", "menu.custom_screen_info.disconnect": "Bespoke shirm forborne", "menu.custom_screen_info.title": "Heed about bespoke shirms", "menu.custom_screen_info.tooltip": "This is a bespoke shirm. Click here to learn more.", "menu.disconnect": "Unbind", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Game List", "menu.modded": " (Tweaked)", "menu.multiplayer": "Maniplayer", "menu.online": "Minecraft Realms", "menu.options": "<PERSON>res...", "menu.paused": "Game Stopped", "menu.playdemo": "Play Kithing World", "menu.playerReporting": "Player Mielding", "menu.preparingSpawn": "Readying starting ord: %s%%", "menu.quick_actions": "Quick Deeds...", "menu.quick_actions.title": "<PERSON> Deeds", "menu.quit": "End Game", "menu.reportBugs": "<PERSON><PERSON>", "menu.resetdemo": "<PERSON><PERSON> Kithing World", "menu.returnToGame": "Back to Game", "menu.returnToMenu": "Keep and Yield to Main Shirm", "menu.savingChunks": "Keeping worldstetches", "menu.savingLevel": "Keeping world", "menu.sendFeedback": "<PERSON><PERSON>", "menu.server_links": "Outreckoner Links...", "menu.server_links.title": "Outreckoner Links", "menu.shareToLan": "Open to NSN", "menu.singleplayer": "Oneplayer", "menu.working": "Working...", "merchant.deprecated": "Thorpsmen stock up ayen up to twice daily.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "Craftschild", "merchant.level.3": "<PERSON><PERSON>", "merchant.level.4": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Thring %1$s to Unmount", "multiplayer.applyingPack": "Beseeching lode pack", "multiplayer.confirm_command.parse_errors": "You are fanding to run an unacknown or unright hest.\nAre you wis?\nHest: %s", "multiplayer.confirm_command.permissions_required": "You are fanding to run a hest that foreneeds higher thavings.\nThis might badly rine your game.\nAre you wis?\nHest: %s", "multiplayer.confirm_command.title": "Sicker Running Hest", "multiplayer.disconnect.authservers_down": "Sickerhood outreckoners are down. Kindly fand ayen later, sorry!", "multiplayer.disconnect.bad_chat_index": "Acknown missed or edstighted chat writ from outreckoner", "multiplayer.disconnect.banned": "You are forbidden from this outreckoner", "multiplayer.disconnect.banned.expiration": "\nYour forbode will end on %s", "multiplayer.disconnect.banned.reason": "You are forbidden from this outreckoner.\nGrounds: %s", "multiplayer.disconnect.banned_ip.expiration": "\nYour forbode will end on %s", "multiplayer.disconnect.banned_ip.reason": "Your WF is forbidden from this outreckoner.\nGrounds: %s", "multiplayer.disconnect.chat_validation_failed": "Chat writ rightening trucked", "multiplayer.disconnect.duplicate_login": "You brooked this reckoning from another stow", "multiplayer.disconnect.expired_public_key": "Quenched selfleaf open key. See that your layout time is right, and fand edstarting your game.", "multiplayer.disconnect.flying": "Flying is not laid on with this outreckoner", "multiplayer.disconnect.generic": "Unbound", "multiplayer.disconnect.idling": "You have been idle for too long!", "multiplayer.disconnect.illegal_characters": "Underboard staves in chat", "multiplayer.disconnect.incompatible": "Unkindred software! Kindly brook %s", "multiplayer.disconnect.invalid_entity_attacked": "Minting to strike an unright ansen", "multiplayer.disconnect.invalid_packet": "Outreckone<PERSON> sent an unright packling", "multiplayer.disconnect.invalid_player_data": "Unright player lore", "multiplayer.disconnect.invalid_player_movement": "Unright shrithing player packling reaped", "multiplayer.disconnect.invalid_public_key_signature": "Unright underwrit for selfleaf open key.\nFand edstarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Unright underwrit for selfleaf open key.\nFand edstarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Unright shrithing craft packling reaped", "multiplayer.disconnect.ip_banned": "You have been WF forbidden from this outreckoner", "multiplayer.disconnect.kicked": "Thrown out by an overseer", "multiplayer.disconnect.missing_tags": "<PERSON>e set of tokens reaped from the outreckoner.\nKindly speak with outreckoner overseer.", "multiplayer.disconnect.name_taken": "That name is already owned", "multiplayer.disconnect.not_whitelisted": "You are not whitelisted on this outreckoner!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packling reaped. Did your layout time shift?", "multiplayer.disconnect.outdated_client": "Unkindred software! Kindly brook %s", "multiplayer.disconnect.outdated_server": "Unkindred software! Kindly brook %s", "multiplayer.disconnect.server_full": "The outreckoner is full!", "multiplayer.disconnect.server_shutdown": "Outreckoner shut", "multiplayer.disconnect.slow_login": "Too much time going in", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat writs", "multiplayer.disconnect.transfers_disabled": "Outreckoner does not bear shifts", "multiplayer.disconnect.unexpected_query_response": "Unweened bespoke lore from software", "multiplayer.disconnect.unsigned_chat": "Reaped chat packling with missing or unright underwrit.", "multiplayer.disconnect.unverified_username": "Could not asooth brook<PERSON><PERSON>!", "multiplayer.downloadingStats": "Finding tolllore...", "multiplayer.downloadingTerrain": "Loading landship...", "multiplayer.lan.server_found": "New outreckoner found: %s", "multiplayer.message_not_delivered": "Can't send chat writ, see outreckoner writs: %s", "multiplayer.player.joined": "%s fayed the game", "multiplayer.player.joined.renamed": "%s (formerly known as %s) fayed the game", "multiplayer.player.left": "%s left the game", "multiplayer.player.list.hp": "%shealthord", "multiplayer.player.list.narration": "Onweb players: %s", "multiplayer.requiredTexturePrompt.disconnect": "Outreckoner foreneeds a custom lode pack", "multiplayer.requiredTexturePrompt.line1": "This outreckoner redes the brooking of a bespoke lode pack.", "multiplayer.requiredTexturePrompt.line2": "Forbearing this bespoke lode pack will unbind you from this outreckoner.", "multiplayer.socialInteractions.not_available": "Minglings are only forthcoming in Maniplayer worlds", "multiplayer.status.and_more": "... and %s more ...", "multiplayer.status.cancelled": "Belaid", "multiplayer.status.cannot_connect": "Can't bind with outreckoner", "multiplayer.status.cannot_resolve": "Can't acknow drightname", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Unkindred wharve!", "multiplayer.status.motd.narration": "Writ of the day: %s", "multiplayer.status.no_connection": "(no binding)", "multiplayer.status.old": "Old", "multiplayer.status.online": "Onweb", "multiplayer.status.ping": "%s thousandth of a braid", "multiplayer.status.ping.narration": "Ping %s thousandths of a braid", "multiplayer.status.pinging": "Pinging...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s out of %s players onweb", "multiplayer.status.quitting": "Yielding", "multiplayer.status.request_handled": "Standing ask has been handled", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Reaped unasked standing", "multiplayer.status.version.narration": "Outreckoner wharve: %s", "multiplayer.stopSleeping": "Leave Bed", "multiplayer.texturePrompt.failure.line1": "Outreckoner lode pack couldn't be besought", "multiplayer.texturePrompt.failure.line2": "Any workings that foreneeds bespoke lodes might not work as weened", "multiplayer.texturePrompt.line1": "This outreckoner redes the brooking of a bespoke lode pack.", "multiplayer.texturePrompt.line2": "Would you like to download and stell it selfshrithingly?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nWrit from outreckoner:\n%s", "multiplayer.title": "Play Maniplayer", "multiplayer.unsecureserver.toast": "Writs sent on this outreckoner may be tweaked and might not show the first writ", "multiplayer.unsecureserver.toast.title": "Chat writs can't be asoothed", "multiplayerWarning.check": "Do not show this shirm ayen", "multiplayerWarning.header": "Warning: Third-Team Onweb Play", "multiplayerWarning.message": "Warning: Onweb play is yeaven by third-team outreckoners that are not owned, run, or overseen by Mojang Studios or Microsoft. Under onweb play, you may be unwried unsteered chat writs or other kinds of brooker-beyot inholdings that might not be fit for everyone.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Key: %s", "narration.button.usage.focused": "Thring Streakbreak to astir", "narration.button.usage.hovered": "Left click to astir", "narration.checkbox": "Tickbox: %s", "narration.checkbox.usage.focused": "Thring Streakbreak to toggle", "narration.checkbox.usage.hovered": "Left click to toggle", "narration.component_list.usage": "Thring <PERSON> to go to next thing", "narration.cycle_button.usage.focused": "Thring Streakbreak to go to %s", "narration.cycle_button.usage.hovered": "Left click to go to %s", "narration.edit_box": "Bework box: %s", "narration.item": "Thing: %s", "narration.recipe": "Knowledge for %s", "narration.recipe.usage": "Left click to choose", "narration.recipe.usage.more": "Right click to show more knowledge", "narration.selection.usage": "Thring up and down keys to go to another thing", "narration.slider.usage.focused": "Thring left or right keys to wend worth", "narration.slider.usage.hovered": "Draw slider to wend worth", "narration.suggestion": "Chosen forthputting %s out of %s: %s", "narration.suggestion.tooltip": "Chosen forthputting %s out of %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Thring <PERSON> to go to the next forthputting", "narration.suggestion.usage.cycle.hidable": "Thring Stead to go to the next forthputting, or Atwind to unheed forthputtings", "narration.suggestion.usage.fill.fixed": "Thring Stead to brook forthputting", "narration.suggestion.usage.fill.hidable": "Thring Stead to brook forthputting, or Atwind to unheed forthputtings", "narration.tab_navigation.usage": "Thring <PERSON> and <PERSON><PERSON> to go between steads", "narrator.button.accessibility": "Handy", "narrator.button.difficulty_lock": "Arvethness lock", "narrator.button.difficulty_lock.locked": "Locked", "narrator.button.difficulty_lock.unlocked": "Unlocked", "narrator.button.language": "Tongue", "narrator.controls.bound": "%s is bound to %s", "narrator.controls.reset": "Edset %s key", "narrator.controls.unbound": "%s is not bound", "narrator.joining": "<PERSON><PERSON>", "narrator.loading": "Loading: %s", "narrator.loading.done": "Done", "narrator.position.list": "<PERSON><PERSON> list row %s out of %s", "narrator.position.object_list": "Chosen row thing %s out of %s", "narrator.position.screen": "Shirm thing %s out of %s", "narrator.position.tab": "Chosen stead %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "Main Shirm", "narrator.screen.usage": "Brook mouse runner or <PERSON>ead key to choose thing", "narrator.select": "Chosen: %s", "narrator.select.world": "Chosen %s, last played: %s, %s, %s, wharve: %s", "narrator.select.world_info": "Chose %s, last played: %s, %s", "narrator.toast.disabled": "Mielder Off", "narrator.toast.enabled": "<PERSON><PERSON><PERSON>", "optimizeWorld.confirm.description": "This will fand to besten your world by making wis all lore is being kept in the newest game layout. This can last a rather long time, hinging on your world. Once done, your world may play faster but will no longer be kindred with older wharves of the game. Are you wis you wish to do it?", "optimizeWorld.confirm.proceed": "Make Backup and Besten", "optimizeWorld.confirm.title": "Besten World", "optimizeWorld.info.converted": "Bettered worldstetches: %s", "optimizeWorld.info.skipped": "Left out worldstetches: %s", "optimizeWorld.info.total": "Worldstetch rime: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Riming worldstetches...", "optimizeWorld.stage.failed": "Trucked! :(", "optimizeWorld.stage.finished": "Fulcoming...", "optimizeWorld.stage.finished.chunks": "Fulcoming bettering worldstetches...", "optimizeWorld.stage.finished.entities": "Fulcoming bettering ansens...", "optimizeWorld.stage.finished.poi": "Fulcoming bettering ords of indraw...", "optimizeWorld.stage.upgrading": "Bettering all worldstetches...", "optimizeWorld.stage.upgrading.chunks": "Bettering all worldstetches...", "optimizeWorld.stage.upgrading.entities": "Bettering all ansens...", "optimizeWorld.stage.upgrading.poi": "Bettering all ords of indraw...", "optimizeWorld.title": "Bestening World '%s'", "options.accessibility": "Handy Settings...", "options.accessibility.high_contrast": "High Sunderness", "options.accessibility.high_contrast.error.tooltip": "High Sunderness lode pack is not forthcoming.", "options.accessibility.high_contrast.tooltip": "Betters the sunderness of brooker roodleer things.", "options.accessibility.high_contrast_block_outline": "High Sunderness Clot Outstreaks", "options.accessibility.high_contrast_block_outline.tooltip": "Betters the clot outstreak sunderness of the marked clot.", "options.accessibility.link": "Handy Help", "options.accessibility.menu_background_blurriness": "List Background Blur", "options.accessibility.menu_background_blurriness.tooltip": "Wends the blurriness of list backgrounds.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Aleaves the Mielder to be laid on and off with 'Hest+B'.", "options.accessibility.narrator_hotkey.tooltip": "Aleaves the Mielder to be laid on and off with 'Steerer+B'.", "options.accessibility.panorama_speed": "Allsight Shrithing Speed", "options.accessibility.text_background": "Writ Background", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Everywhere", "options.accessibility.text_background_opacity": "Writ Background Throughsight", "options.accessibility.title": "<PERSON><PERSON>", "options.allowServerListing": "Aleave Outreckoner Listings", "options.allowServerListing.tooltip": "Outreckoners may list onweb players as a share of hir open standing.\nWith this kire off your name will not show up on such lists.", "options.ao": "Smooth Lighting", "options.ao.max": "Highest", "options.ao.min": "Least", "options.ao.off": "OFF", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "Hotband", "options.attackIndicator": "Strike Beaconer", "options.audioDevice": "<PERSON><PERSON>", "options.audioDevice.default": "Layout Stock", "options.autoJump": "<PERSON><PERSON><PERSON><PERSON>p", "options.autoSuggestCommands": "<PERSON><PERSON>", "options.autosaveIndicator": "Selfkeep <PERSON>er", "options.biomeBlendRadius": "<PERSON><PERSON><PERSON>", "options.biomeBlendRadius.1": "OFF (Fastest)", "options.biomeBlendRadius.11": "11x11 (<PERSON>ein<PERSON>)", "options.biomeBlendRadius.13": "13x13 (Showoff)", "options.biomeBlendRadius.15": "15x15 (Utmost)", "options.biomeBlendRadius.3": "3x3 (Fast)", "options.biomeBlendRadius.5": "5x5 (Wonted)", "options.biomeBlendRadius.7": "7x7 (High)", "options.biomeBlendRadius.9": "9x9 (Alther<PERSON>gh)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Chat Bide: %s braid(s)", "options.chat.delay_none": "Chat Bide: None", "options.chat.height.focused": "Highlighted Height", "options.chat.height.unfocused": "Unhighlighted Height", "options.chat.line_spacing": "Streak Farness", "options.chat.links": "Weblinks", "options.chat.links.prompt": "Ask on Links", "options.chat.opacity": "Chat Writ Throughsight", "options.chat.scale": "<PERSON><PERSON> <PERSON><PERSON>", "options.chat.title": "<PERSON><PERSON>", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "Hests Only", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s worldstetches", "options.clouds.fancy": "Showy", "options.clouds.fast": "Fast", "options.controls": "Steerings...", "options.credits_and_attribution": "Acknowledgings & Ownship...", "options.damageTiltStrength": "Harm Tilt", "options.damageTiltStrength.tooltip": "The deal of byldmaker shake made by being wounded.", "options.darkMojangStudiosBackgroundColor": "Onehue Brand", "options.darkMojangStudiosBackgroundColor.tooltip": "Wends the Mojang Studios loading shirm background hue to black.", "options.darknessEffectScale": "Darkness Beating", "options.darknessEffectScale.tooltip": "Wields how much the Darkness rine beats when a Holdend or Sculk Yeller yeaves it to you.", "options.difficulty": "Arvethness", "options.difficulty.easy": "Light", "options.difficulty.easy.info": "Foe wights spring but deal less harm. Hunger band goes down and drains health down to 5 hearts.", "options.difficulty.hard": "Hard", "options.difficulty.hard.info": "Foe wights spring and deal more harm. Hunger band goes down and drains all health.", "options.difficulty.hardcore": "Hardheart", "options.difficulty.normal": "Wonted", "options.difficulty.normal.info": "Foe wights spring and deal stock harm. Hunger band goes down and drains health down to half a heart.", "options.difficulty.online": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful": "Frithful", "options.difficulty.peaceful.info": "No foe wights and only some unsidey wights spring. Hunger band doesn't go down and health edheals over time.", "options.directionalAudio": "Steered Loud", "options.directionalAudio.off.tooltip": "Win Twin loud.", "options.directionalAudio.on.tooltip": "<PERSON> HRTF-grounded steered loud to better the reckoning of 3D sound. Needs HRTF kindred sound hardware, and is best undergone with headspeakers.", "options.discrete_mouse_scroll": "True Mousewheel Shrithing", "options.entityDistanceScaling": "Being Farness", "options.entityShadows": "<PERSON><PERSON>", "options.font": "Staffkind Settings...", "options.font.title": "Staff<PERSON> Settings", "options.forceUnicodeFont": "Fordrive Unicode Staffkind", "options.fov": "Sightfield", "options.fov.max": "Quake Lord", "options.fov.min": "Wonted", "options.fovEffectScale": "Sightfield Rines", "options.fovEffectScale.tooltip": "Wields how much the sightfield can wend with gameplay rines.", "options.framerate": "%s fab", "options.framerateLimit": "Top Framespeed", "options.framerateLimit.max": "Unfettered", "options.fullscreen": "Fullshirm", "options.fullscreen.current": "<PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%scleft)", "options.fullscreen.resolution": "Fullshirm Bitgreat", "options.fullscreen.unavailable": "Setting unforthcoming", "options.gamma": "Brightness", "options.gamma.default": "Stock", "options.gamma.max": "<PERSON>", "options.gamma.min": "<PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Galelight Speed", "options.glintSpeed.tooltip": "Wield how fast the light shimmers along galed things.", "options.glintStrength": "Galelight Strength", "options.glintStrength.tooltip": "Wields how throughshine the light is on galed things.", "options.graphics": "Bildens", "options.graphics.fabulous": "Wonderful!", "options.graphics.fabulous.tooltip": "%s bildens brooks shirm shaders for drawing weather, clouds and specks behind see-through clots and water.\nThis may greatly rine speed for handheld sares and 4K shirms.", "options.graphics.fancy": "Showy", "options.graphics.fancy.tooltip": "Showy bildens evens out game speed and goodth for most sares.\nWeather, clouds and specks may not show up behind seethrough clots or water.", "options.graphics.fast": "Fast", "options.graphics.fast.tooltip": "Fast bildens shrinks the deal of seenly rain and snow.\nSee-throughness rines are off for many clots such as leaves.", "options.graphics.warning.accept": "Go On Without Uphold", "options.graphics.warning.cancel": "Go Back", "options.graphics.warning.message": "Your bildens sare was found not upheld for the %s bildens kire.\n\nYou may unheed this and go on, however uphold will not be yeaven for your sare if you choose to brook %s bildens.", "options.graphics.warning.renderer": "Drawer found: [%s]", "options.graphics.warning.title": "Bildens Sare Not Upheld", "options.graphics.warning.vendor": "Seller found: [%s]", "options.graphics.warning.version": "OpenGL Wharve found: [%s]", "options.guiScale": "BBR Meter", "options.guiScale.auto": "Selfsetting", "options.hidden": "Hidden", "options.hideLightningFlashes": "Hide Lightning Flashes", "options.hideLightningFlashes.tooltip": "Forestalls Lightning Bolts from making the welkin flash. The bolts themselves will still be seenly.", "options.hideMatchedNames": "Hide Matched Names", "options.hideMatchedNames.tooltip": "3rd-team Outreckoners may send chat writs in not-stock layouts.\nWith this kire on, hidden players will be matched grounded on chat sender names.", "options.hideSplashTexts": "Hide Splash Writs", "options.hideSplashTexts.tooltip": "Hides the yellow splash writ in the main list.", "options.inactivityFpsLimit": "Lessen FAB when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Thresholds framespeed to 30 when the game is not reaping any player input for more than a stoundle. Further thresholds it to 10 after 9 more stoundles.", "options.inactivityFpsLimit.minimized": "Shrinked", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON><PERSON><PERSON><PERSON>s framespeed only when the game eyethurl is shrinked.", "options.invertMouse": "Flip <PERSON>", "options.japaneseGlyphVariants": "Yapanish Carving Sundries", "options.japaneseGlyphVariants.tooltip": "<PERSON>ish sundries of <PERSON>ish, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> staves in the stock staffkind.", "options.key.hold": "Hold", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Tongue...", "options.language.title": "Tongue", "options.languageAccuracyWarning": "(Tongue wendings may not be 100%% onmark)", "options.languageWarning": "Tongue wendings may not be 100%% onmark", "options.mainHand": "Main Hand", "options.mainHand.left": "Left", "options.mainHand.right": "Right", "options.mipmapLevels": "Much-in-Little Plot Layers", "options.modelPart.cape": "Backstreamer", "options.modelPart.hat": "Hat", "options.modelPart.jacket": "Coat", "options.modelPart.left_pants_leg": "Left Breech Leg", "options.modelPart.left_sleeve": "Left Sleeve", "options.modelPart.right_pants_leg": "Right Breech Leg", "options.modelPart.right_sleeve": "Right Sleeve", "options.mouseWheelSensitivity": "Mousewheel Anyetishness", "options.mouse_settings": "Mouse Settings...", "options.mouse_settings.title": "Mouse Settings", "options.multiplayer.title": "Maniplayer Settings...", "options.multiplier": "%sx", "options.music_frequency": "<PERSON><PERSON><PERSON>ness", "options.music_frequency.constant": "Unyielding", "options.music_frequency.default": "Stock", "options.music_frequency.frequent": "Often", "options.music_frequency.tooltip": "<PERSON>ds how often gleecraft plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Mields All", "options.narrator.chat": "<PERSON><PERSON><PERSON>", "options.narrator.notavailable": "Not Forthcoming", "options.narrator.off": "OFF", "options.narrator.system": "Mields Layout", "options.notifications.display_time": "Heedening Time", "options.notifications.display_time.tooltip": "Onworks the length of time that all heedenings are seenly on the shirm.", "options.off": "OFF", "options.off.composed": "%s: OFF", "options.on": "ON", "options.on.composed": "%s: ON", "options.online": "Onweb...", "options.online.title": "<PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "Only Show Shielded Chat", "options.onlyShowSecureChat.tooltip": "Only show writs from other players that can be asoothed to have come from that player, and have not been wended.", "options.operatorItemsTab": "Overseer Things Stead", "options.particles": "Specks", "options.particles.all": "All", "options.particles.decreased": "Fewer", "options.particles.minimal": "Least", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %s dots", "options.prioritizeChunkUpdates": "Worldstetch Builder", "options.prioritizeChunkUpdates.byPlayer": "Half Clotting", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Some deeds within a worldstetch will put the worldstetch together ayen anon. This ins clot laying and breaking.", "options.prioritizeChunkUpdates.nearby": "Fully Clotting", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby worldstetches are always put together anon. This may rine game speed when clots are laid or broken.", "options.prioritizeChunkUpdates.none": "Threaded", "options.prioritizeChunkUpdates.none.tooltip": "Nearby worldstetches are put together in matching threads. This may lead to short seenly holes when clots are broken.", "options.rawMouseInput": "Raw Input", "options.realmsNotifications": "Realms News & Lathings", "options.realmsNotifications.tooltip": "Fetches Realms news and lathings in the main shirm and shows their own byldle on the Realms key.", "options.reducedDebugInfo": "Lessened Unbug Abrst", "options.renderClouds": "Clouds", "options.renderCloudsDistance": "Cloud Farness", "options.renderDistance": "Draw Farness", "options.resourcepack": "Lode Packs...", "options.rotateWithMinecart": "Wharve with <PERSON><PERSON><PERSON><PERSON>", "options.rotateWithMinecart.tooltip": "Whether the player's sight should wharve with a spinning Delvewain. Only forthcoming in worlds with the 'Delvewain Bolsterings' fandly setting laid on.", "options.screenEffectScale": "Warping Rines", "options.screenEffectScale.tooltip": "Strength of lat and Nether ingang shirm warping rines. \nAt smaller worths, the lat rine is swapped out with a green overlay.", "options.sensitivity": "Anyetishness", "options.sensitivity.max": "OVERSPEED!!!", "options.sensitivity.min": "*yawn*", "options.showNowPlayingToast": "Show Gleecraft Pop-up", "options.showNowPlayingToast.tooltip": "Shows a pop-up whenever a song starts playing. The same pop-up is unyieldingly shown in the in-game stop list while a song is playing.", "options.showSubtitles": "Show Undersettings", "options.simulationDistance": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "options.skinCustomisation": "Hide Bespeaking...", "options.skinCustomisation.title": "Hide Bespeaking", "options.sounds": "Gleecraft & Louds...", "options.sounds.title": "Gleecraft & Loud Kires", "options.telemetry": "Farmeting Lore...", "options.telemetry.button": "Lore Gathering", "options.telemetry.button.tooltip": "\"%s\" ins only the foreneeded lore.\n\"%s\" ins kirely, as well as the foreneeded lore.", "options.telemetry.disabled": "Farmeting is laid off.", "options.telemetry.state.all": "All", "options.telemetry.state.minimal": "Least", "options.telemetry.state.none": "None", "options.title": "<PERSON><PERSON>", "options.touchscreen": "<PERSON><PERSON><PERSON><PERSON>", "options.video": "Filmlike Settings...", "options.videoTitle": "Filmlike Settings", "options.viewBobbing": "Show Bobbing", "options.visible": "Shown", "options.vsync": "Upright Timekeeping", "outOfMemory.message": "Minecraft has run out of inmind.\n\nThis could be made by a bug in the game or by the Java Aped Sare not being fordealt enough inmind.\n\nTo forestall breaking the world, the anward game has ended. We've fanded to free up enough inmind to let you go back to the main list and back to playing, but this may not have worked.\n\nKindly edstart the game if you see this writ ayen.", "outOfMemory.title": "Out of inmind!", "pack.available.title": "Forthcoming", "pack.copyFailure": "Could not clove packs", "pack.dropConfirm": "Do you wish to eke the following packs to Minecraft?", "pack.dropInfo": "Draw and drop threads into this eyethurl to eke packs", "pack.dropRejected.message": "The following inputs were not right packs and were not cloved:\n %s", "pack.dropRejected.title": "Inputs that are not packs", "pack.folderInfo": "(Put pack threads here)", "pack.incompatible": "Unkindred", "pack.incompatible.confirm.new": "This pack was made for a newer wharve of Minecraft and may no longer work rightly.", "pack.incompatible.confirm.old": "This pack was made for an older wharve of Minecraft and may no longer work rightly.", "pack.incompatible.confirm.title": "Are you wis you wish to load this pack?", "pack.incompatible.new": "(Made for a newer wharve of Minecraft)", "pack.incompatible.old": "(Made for an older wharve of Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Open Pack Folder", "pack.selected.title": "<PERSON><PERSON>", "pack.source.builtin": "built-in", "pack.source.feature": "mark", "pack.source.local": "neigh", "pack.source.server": "outreckoner", "pack.source.world": "world", "painting.dimensions": "%s by %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Hapsome metinglike", "parsing.bool.expected": "Abode boolean", "parsing.bool.invalid": "Unright boolean, abode 'true' or 'false' but found '%s'", "parsing.double.expected": "Abode twofold", "parsing.double.invalid": "Unright twofold '%s'", "parsing.expected": "Abode '%s'", "parsing.float.expected": "Abode float", "parsing.float.invalid": "Unright float '%s'", "parsing.int.expected": "Abode wholerime", "parsing.int.invalid": "Unright wholerime '%s'", "parsing.long.expected": "Abode long", "parsing.long.invalid": "Unright long '%s'", "parsing.quote.escape": "Unright atwind string '\\%s' in forthteed string", "parsing.quote.expected.end": "Unshut forthteed string", "parsing.quote.expected.start": "Abode forthtee to start a string", "particle.invalidOptions": "Can't understand speck kires: %s", "particle.notFound": "Unknown Speck: %s", "permissions.requires.entity": "An ansen is foreneeded to run this hest here", "permissions.requires.player": "A player is foreneeded to run this hest here", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "When Drunk:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unknown foreworth: %s", "quickplay.error.invalid_identifier": "Could not find world with the yeaven token", "quickplay.error.realm_connect": "Could not link with Realm", "quickplay.error.realm_permission": "Lacking thaving to bind with this Realm", "quickplay.error.title": "Could not Quick Play", "realms.configuration.region.australia_east": "New South Wales, New Holland", "realms.configuration.region.australia_southeast": "Victoria, New Holland", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "Indy", "realms.configuration.region.central_us": "Iowa, ORA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, ORA", "realms.configuration.region.east_us_2": "North Carolina, ORA", "realms.configuration.region.france_central": "Frankland", "realms.configuration.region.japan_east": "Eastern Yapan", "realms.configuration.region.japan_west": "Western Yapan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, ORA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, ORA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweeland", "realms.configuration.region.uae_north": "Oned Arab Emirdoms", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, ORA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, ORA", "realms.configuration.region.west_us_2": "Washington, ORA", "realms.configuration.region_preference.automatic_owner": "<PERSON><PERSON><PERSON><PERSON> (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "<PERSON>shrithing (first to fay besitting)", "realms.missing.snapshot.error.text": "Realms is anwardly not upheld in showshots", "recipe.notFound": "Unknown knowledge: %s", "recipe.toast.description": "See your knowledgebook", "recipe.toast.title": "New Knowledge Unlocked!", "record.nowPlaying": "Now Playing: %s", "recover_world.bug_tracker": "Mield a Bug", "recover_world.button": "Fand to Heal", "recover_world.done.failed": "Could not heal from former hoad.", "recover_world.done.success": "Healing was speedful!", "recover_world.done.title": "Healing done", "recover_world.issue.missing_file": "Missing thread", "recover_world.issue.none": "No hinderings", "recover_world.message": "The following hinderings befallen while fanding to read world folder \"%s\".\nIt might be mightly to edstow the world from an older hoad or you can mield this hindering on the bug follower.", "recover_world.no_fallback": "No hoad to heal from forthcoming", "recover_world.restore": "Fand to Edstow", "recover_world.restoring": "Fanding to edstow world...", "recover_world.state_entry": "Hoad from %s: ", "recover_world.state_entry.unknown": "unknown", "recover_world.title": "Could not load world", "recover_world.warning": "Could not load world writ", "resourcePack.broken_assets": "BROKEN HOLDINGS FOUND", "resourcePack.high_contrast.name": "High Sunderness", "resourcePack.load_fail": "Lode edload trucked", "resourcePack.programmer_art.name": "Foredrafter Craft", "resourcePack.runtime_failure": "Lode pack dwale acknown", "resourcePack.server.name": "World Narrowed Lodes", "resourcePack.title": "Choose Lode Packs", "resourcePack.vanilla.description": "The stock look and feel of Minecraft", "resourcePack.vanilla.name": "Stock", "resourcepack.downloading": "Downloading Lode Pack", "resourcepack.progress": "Downloading thread (%s thousand thousand eightclefts)...", "resourcepack.requesting": "Making Ask...", "screenshot.failure": "Couldn't keep shirmshot: %s", "screenshot.success": "<PERSON>pt shirmshot as %s", "selectServer.add": "<PERSON><PERSON>", "selectServer.defaultName": "Minecraft Outreckoner", "selectServer.delete": "<PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON>", "selectServer.deleteQuestion": "Are you wis you wish to fornim this outreckoner?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "Forthright Bind", "selectServer.edit": "Bework", "selectServer.hiddenAddress": "(Hidden)", "selectServer.refresh": "<PERSON><PERSON><PERSON>", "selectServer.select": "<PERSON>", "selectWorld.access_failure": "Could not reach world", "selectWorld.allowCommands": "Aleave <PERSON>", "selectWorld.allowCommands.info": "Hests like /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON>ve <PERSON>", "selectWorld.backupEraseCache": "Adwash Derned <PERSON>re", "selectWorld.backupJoinConfirmButton": "Make Backup and Load", "selectWorld.backupJoinSkipButton": "I know what I'm doing!", "selectWorld.backupQuestion.customized": "Bespoke worlds are no longer upheld", "selectWorld.backupQuestion.downgrade": "Worsening a world is not upheld", "selectWorld.backupQuestion.experimental": "Worlds brooking Fandly Settings are not upheld", "selectWorld.backupQuestion.snapshot": "Do you truly wish to load this world?", "selectWorld.backupWarning.customized": "Haplessly, we do not uphold besunderleged worlds in this wharve of Minecraft. We can still load this world and keep everything the way it was, but and newly beyotten landship will no longer be besunderleged. We're sorry for the unhandiness!", "selectWorld.backupWarning.downgrade": "This world was last played in wharve %s; you are on wharve %s. Worsening a world could make fenowedness - we cannot sicker that it will load or work. If you still wish to go on, please make a backup.", "selectWorld.backupWarning.experimental": "This world brooks fandly settings that could stop working at any time. We cannot make wis that it will load or work. Here be worms!", "selectWorld.backupWarning.snapshot": "This world was last played in wharve %s; you are on wharve %s. Kindly make a backup wald you undergo world fenowedness.", "selectWorld.bonusItems": "Start Chest", "selectWorld.cheats": "Blenches", "selectWorld.commands": "<PERSON><PERSON>", "selectWorld.conversion": "Must be wharved!", "selectWorld.conversion.tooltip": "This world must be opened in an older wharve (like 1.6.4) to be soundly wharved", "selectWorld.create": "Make New World", "selectWorld.customizeType": "Bespeak", "selectWorld.dataPacks": "Lore Packs", "selectWorld.data_read": "Reading world lore...", "selectWorld.delete": "<PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON>", "selectWorld.deleteQuestion": "Are you wis you wish to adwash this world?", "selectWorld.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectWorld.delete_failure": "Could not adwash world", "selectWorld.edit": "Bework", "selectWorld.edit.backup": "Make Backup", "selectWorld.edit.backupCreated": "Backed up: %s", "selectWorld.edit.backupFailed": "Backup trucked", "selectWorld.edit.backupFolder": "Open Backups Folder", "selectWorld.edit.backupSize": "breadth: %s thousand thousand eightclefts", "selectWorld.edit.export_worldgen_settings": "Outsend World Beyetting Settings", "selectWorld.edit.export_worldgen_settings.failure": "Outsend trucked", "selectWorld.edit.export_worldgen_settings.success": "Outsent", "selectWorld.edit.openFolder": "Open World Folder", "selectWorld.edit.optimize": "Besten World", "selectWorld.edit.resetIcon": "<PERSON><PERSON>", "selectWorld.edit.save": "Keep", "selectWorld.edit.title": "Bework World", "selectWorld.enterName": "World Name", "selectWorld.enterSeed": "Seed for the world beyetter", "selectWorld.experimental": "Fandly", "selectWorld.experimental.details": "Insights", "selectWorld.experimental.details.entry": "Needed fandly marks: %s", "selectWorld.experimental.details.title": "Fandly <PERSON>", "selectWorld.experimental.message": "Be careful!\nThis setting foreneeds marks that are still being made. Your world might downfall, break, or not work with with tocome anwardenings.", "selectWorld.experimental.title": "Fandly Marks Warning", "selectWorld.experiments": "Fands", "selectWorld.experiments.info": "Fands are maybe new marks. Be careful as things might break. Fands can't be laid off after making the world.", "selectWorld.futureworld.error.text": "Something went bad while fanding to load a world from a forthcoming wharve. This was a gamblesome deed to begin with, sorry it didn't work.", "selectWorld.futureworld.error.title": "A dwale befallen!", "selectWorld.gameMode": "Game Wayset", "selectWorld.gameMode.adventure": "Wayspelling", "selectWorld.gameMode.adventure.info": "Same as <PERSON>live Wayset, but clots can't be eked or fornimmed.", "selectWorld.gameMode.adventure.line1": "Same as <PERSON><PERSON><PERSON>, but clots can't", "selectWorld.gameMode.adventure.line2": "be eked or fornimmed", "selectWorld.gameMode.creative": "Makerly", "selectWorld.gameMode.creative.info": "Make, build, and seek without thresholds. You can fly, have endless anworks, and can't be wounded by fiends.", "selectWorld.gameMode.creative.line1": "Unfettered many lodes, free flying and", "selectWorld.gameMode.creative.line2": "break clots mididonely", "selectWorld.gameMode.hardcore": "Hardheart", "selectWorld.gameMode.hardcore.info": "Overlive Wayset locked to 'Hard' toughness. You can't edstart if you swelt.", "selectWorld.gameMode.hardcore.line1": "Same as <PERSON><PERSON><PERSON>, locked at hardest", "selectWorld.gameMode.hardcore.line2": "toughness, and one life only", "selectWorld.gameMode.spectator": "Onlooker", "selectWorld.gameMode.spectator.info": "You can look but don't rine.", "selectWorld.gameMode.spectator.line1": "You can look but don't rine", "selectWorld.gameMode.survival": "Overlive", "selectWorld.gameMode.survival.info": "Seek a wonderful world where you can build, gather, craft, and fight fiends.", "selectWorld.gameMode.survival.line1": "Look for lodes, craft, gain", "selectWorld.gameMode.survival.line2": "layers, health and hunger", "selectWorld.gameRules": "Game Wields", "selectWorld.import_worldgen_settings": "Inbring Settings", "selectWorld.import_worldgen_settings.failure": "Dwale inbringing settings", "selectWorld.import_worldgen_settings.select_file": "Choose settings thread (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this wharve.\nIt was last played in wharve %s.", "selectWorld.incompatible.info": "Unkindred wharve: %s", "selectWorld.incompatible.title": "Unkindred wharve", "selectWorld.incompatible.tooltip": "This world cannot be opened as it was made by an unkindred wharve.", "selectWorld.incompatible_series": "Made by an unkindred wharve", "selectWorld.load_folder_access": "Cannot read or reach folder where game worlds are kept!", "selectWorld.loading_list": "Loading World List", "selectWorld.locked": "Locked by another running befalling of Mine<PERSON>", "selectWorld.mapFeatures": "Beyet Frameworks", "selectWorld.mapFeatures.info": "<PERSON><PERSON>, Broke Ships, asf.", "selectWorld.mapType": "Worldkind", "selectWorld.mapType.normal": "Wonted", "selectWorld.moreWorldOptions": "More World Kires...", "selectWorld.newWorld": "New World", "selectWorld.recreate": "Ed-Make", "selectWorld.recreate.customized.text": "Bespoke worlds are no longer upheld in this wharve of Minecraft. We can fand to edmake it with the same seed and holdings, but any landship bespeaking will be lost. We're sorry for the unhandiness!", "selectWorld.recreate.customized.title": "Bespoke worlds are no longer upheld", "selectWorld.recreate.error.text": "Something went bad while fanding to edmake a world.", "selectWorld.recreate.error.title": "A dwale befallen!", "selectWorld.resource_load": "Readying Lodes...", "selectWorld.resultFolder": "Will be kept in:", "selectWorld.search": "seek for worlds", "selectWorld.seedInfo": "Leave blank for a hapsome seed", "selectWorld.select": "Play Chosen World", "selectWorld.targetFolder": "Nering folder: %s", "selectWorld.title": "Choose World", "selectWorld.tooltip.fromNewerVersion1": "World was kept in a newer wharve,", "selectWorld.tooltip.fromNewerVersion2": "loading this world could make badberes!", "selectWorld.tooltip.snapshot1": "Don't forget to back up this world", "selectWorld.tooltip.snapshot2": "before you load it in this showshot.", "selectWorld.unable_to_load": "Cannot load worlds", "selectWorld.version": "Wharve:", "selectWorld.versionJoinButton": "Load Anyway", "selectWorld.versionQuestion": "Do you truly wish to load this world?", "selectWorld.versionUnknown": "unknown", "selectWorld.versionWarning": "This world was played in wharve '%s' and loading it in this wharve could make fenowedness!", "selectWorld.warning.deprecated.question": "Some of the marks brooked in this world are forolded and will stop working in the time to come. Do you wish to go on?", "selectWorld.warning.deprecated.title": "Warning! These settings are using forolded marks", "selectWorld.warning.experimental.question": "These settings are fandly and could one day stop working. Do you wish to go on?", "selectWorld.warning.experimental.title": "Warning! These settings are brooking fandly marks", "selectWorld.warning.lowDiskSpace.description": "There is not much room left on your sare.\nRunning out of harddrive room while in game can lead to your world being harmed.", "selectWorld.warning.lowDiskSpace.title": "Warning! Neath harddrive room!", "selectWorld.world": "World", "sign.edit": "Bework Token Writ", "sleep.not_possible": "No deal of rest can go over this night", "sleep.players_sleeping": "%s/%s players sleeping", "sleep.skipping_night": "Sleeping through this night", "slot.only_single_allowed": "Only onefold groovesteads aleaved, reaped '%s'", "slot.unknown": "Unknown groovestead '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Abode a twoish rime", "snbt.parser.expected_decimal_numeral": "Abode a tenish rime", "snbt.parser.expected_float_type": "Abode a floating ord rime", "snbt.parser.expected_hex_escape": "Abode a staff staffly of length %s", "snbt.parser.expected_hex_numeral": "Abode a sixteenish rime", "snbt.parser.expected_integer_type": "Abode a wholerime", "snbt.parser.expected_non_negative_number": "Abode a rime that is not undernaught", "snbt.parser.expected_number_or_boolean": "Abode a rime or a boolean", "snbt.parser.expected_string_uuid": "Abode a string bodying a right UUID", "snbt.parser.expected_unquoted_string": "Abode a right forthteed string", "snbt.parser.infinity_not_allowed": "Endless rimes are not aleaved", "snbt.parser.invalid_array_element_type": "Unright list thing kind", "snbt.parser.invalid_character_name": "Unright Unicode staff name", "snbt.parser.invalid_codepoint": "Unright Unicode staff worth: %s", "snbt.parser.invalid_string_contents": "Unright string inholdings", "snbt.parser.invalid_unquoted_start": "Unforthteed strings can't start with fingers 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Tenish rimes can't start with 0", "snbt.parser.no_such_operation": "No such freeming: %s", "snbt.parser.number_parse_failure": "Could not understand rime: %s", "snbt.parser.undescore_not_allowed": "Underline staves are not aleaved at the start ot end of a rime", "soundCategory.ambient": "Umbworld", "soundCategory.block": "Clots", "soundCategory.hostile": "<PERSON><PERSON>", "soundCategory.master": "Main Loudness", "soundCategory.music": "<PERSON><PERSON><PERSON>", "soundCategory.neutral": "Friendly Wights", "soundCategory.player": "Players", "soundCategory.record": "Gleebox/Ringers", "soundCategory.ui": "BR", "soundCategory.voice": "Reard/Speech", "soundCategory.weather": "Weather", "spectatorMenu.close": "Shut List", "spectatorMenu.next_page": "Next Sheet", "spectatorMenu.previous_page": "Former Sheet", "spectatorMenu.root.prompt": "Thring a key to choose a hest, and ayen to brook it.", "spectatorMenu.team_teleport": "<PERSON><PERSON><PERSON> to Team Belonger", "spectatorMenu.team_teleport.prompt": "Choose a team to farferry to", "spectatorMenu.teleport": "Far<PERSON><PERSON> to Player", "spectatorMenu.teleport.prompt": "Choose a player to farferry to", "stat.generalButton": "Mean", "stat.itemsButton": "Things", "stat.minecraft.animals_bred": "<PERSON>", "stat.minecraft.aviate_one_cm": "Length by <PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.bell_ring": "Bells Rung", "stat.minecraft.boat_one_cm": "Length by Boat", "stat.minecraft.clean_armor": "Hirsting Stetches Cleaned", "stat.minecraft.clean_banner": "Streamers Cleaned", "stat.minecraft.clean_shulker_box": "Shulker Boxes Cleaned", "stat.minecraft.climb_one_cm": "Length Climbed", "stat.minecraft.crouch_one_cm": "Length Snuck", "stat.minecraft.damage_absorbed": "Harm Soaked Up", "stat.minecraft.damage_blocked_by_shield": "Harm Stopped by Shield", "stat.minecraft.damage_dealt": "Harm Dealt", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> (Soaked Up)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON>t (Withstood)", "stat.minecraft.damage_resisted": "Harm Withstood", "stat.minecraft.damage_taken": "<PERSON><PERSON>", "stat.minecraft.deaths": "Rime of Deaths", "stat.minecraft.drop": "Things Dropped", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON>", "stat.minecraft.enchant_item": "Things Galed", "stat.minecraft.fall_one_cm": "Length Fell", "stat.minecraft.fill_cauldron": "Ironcrocks Filled", "stat.minecraft.fish_caught": "Fish Holed", "stat.minecraft.fly_one_cm": "Length Flew", "stat.minecraft.happy_ghast_one_cm": "Length by <PERSON>", "stat.minecraft.horse_one_cm": "Length by Horse", "stat.minecraft.inspect_dispenser": "Throwers Sought Through", "stat.minecraft.inspect_dropper": "Droppers Sought Through", "stat.minecraft.inspect_hopper": "<PERSON>s Sought Through", "stat.minecraft.interact_with_anvil": "Brookings of an Anvil", "stat.minecraft.interact_with_beacon": "Brookings of a Beacon", "stat.minecraft.interact_with_blast_furnace": "Brookings of a Blast Oven", "stat.minecraft.interact_with_brewingstand": "Brookings of a Brewing Stand", "stat.minecraft.interact_with_campfire": "Brookings of a Haltfire", "stat.minecraft.interact_with_cartography_table": "Brookings of a Drafting Bench", "stat.minecraft.interact_with_crafting_table": "Brookings of a Workbench", "stat.minecraft.interact_with_furnace": "Brookings of an Oven", "stat.minecraft.interact_with_grindstone": "Brookings of a Grindstone", "stat.minecraft.interact_with_lectern": "Brookings of a Reading Stand", "stat.minecraft.interact_with_loom": "Brookings of a Loom", "stat.minecraft.interact_with_smithing_table": "Brookings of a Smithing Bench", "stat.minecraft.interact_with_smoker": "Brookings of a Smoker", "stat.minecraft.interact_with_stonecutter": "Brookings of a Stones<PERSON>ther", "stat.minecraft.jump": "Leaps", "stat.minecraft.leave_game": "Games Ended", "stat.minecraft.minecart_one_cm": "Length by <PERSON><PERSON><PERSON>", "stat.minecraft.mob_kills": "Wight Kills", "stat.minecraft.open_barrel": "Vats Opened", "stat.minecraft.open_chest": "Chests Opened", "stat.minecraft.open_enderchest": "<PERSON><PERSON> Chests Opened", "stat.minecraft.open_shulker_box": "Shulker Boxes Opened", "stat.minecraft.pig_one_cm": "Length by Swine", "stat.minecraft.play_noteblock": "Ringers Played", "stat.minecraft.play_record": "<PERSON> Played", "stat.minecraft.play_time": "Time Played", "stat.minecraft.player_kills": "Player Kills", "stat.minecraft.pot_flower": "Worts Potted", "stat.minecraft.raid_trigger": "Reavings Triggered", "stat.minecraft.raid_win": "Reavings Won", "stat.minecraft.sleep_in_bed": "Times Slept in a Bed", "stat.minecraft.sneak_time": "Sneak Time", "stat.minecraft.sprint_one_cm": "Length Sprinted", "stat.minecraft.strider_one_cm": "Length by <PERSON><PERSON><PERSON>", "stat.minecraft.swim_one_cm": "Length Swum", "stat.minecraft.talked_to_villager": "Talked to <PERSON><PERSON><PERSON>", "stat.minecraft.target_hit": "<PERSON><PERSON>", "stat.minecraft.time_since_death": "Time Since Last Death", "stat.minecraft.time_since_rest": "Time Since Last Rest", "stat.minecraft.total_world_time": "Time with World Open", "stat.minecraft.traded_with_villager": "Wrixled with <PERSON><PERSON><PERSON>", "stat.minecraft.trigger_trapped_chest": "Trapped Chests Triggered", "stat.minecraft.tune_noteblock": "Ringers Tweaked", "stat.minecraft.use_cauldron": "Water Nimmed from Ironcrock", "stat.minecraft.walk_on_water_one_cm": "Length Walked on Water", "stat.minecraft.walk_one_cm": "Length Walked", "stat.minecraft.walk_under_water_one_cm": "Length Walked under Water", "stat.mobsButton": "<PERSON><PERSON>", "stat_type.minecraft.broken": "Times Broken", "stat_type.minecraft.crafted": "Times Crafted", "stat_type.minecraft.dropped": "Dropped", "stat_type.minecraft.killed": "You killed %s %s", "stat_type.minecraft.killed.none": "You have never killed %s", "stat_type.minecraft.killed_by": "%s killed you %s time(s)", "stat_type.minecraft.killed_by.none": "You have never been killed by %s", "stat_type.minecraft.mined": "Times Delved", "stat_type.minecraft.picked_up": "Picked Up", "stat_type.minecraft.used": "Times Brooked", "stats.none": "-", "structure_block.button.detect_size": "ACKNOW", "structure_block.button.load": "LOAD", "structure_block.button.save": "KEEP", "structure_block.custom_data": "Bespoke Lore Token Name", "structure_block.detect_size": "Acknow Framework Breadth and Stow:", "structure_block.hover.corner": "Hirn: %s", "structure_block.hover.data": "Lore: %s", "structure_block.hover.load": "Load: %s", "structure_block.hover.save": "Keep: %s", "structure_block.include_entities": "In Ansens:", "structure_block.integrity": "Framework Cleanness and Seed", "structure_block.integrity.integrity": "Framework Cleanness", "structure_block.integrity.seed": "Framework Seed", "structure_block.invalid_structure_name": "Unright framework name '%s'", "structure_block.load_not_found": "Framework '%s' is not free", "structure_block.load_prepare": "Framework '%s' stow readied", "structure_block.load_success": "Framework loaded from '%s'", "structure_block.mode.corner": "<PERSON>rn", "structure_block.mode.data": "Lore", "structure_block.mode.load": "Load", "structure_block.mode.save": "Keep", "structure_block.mode_info.corner": "Hirn Wayset - Stow and breadth marker", "structure_block.mode_info.data": "<PERSON><PERSON>et - Game witcraft marker", "structure_block.mode_info.load": "Load Wayset - Load from thread", "structure_block.mode_info.save": "Keep Wayset - Write to thread", "structure_block.position": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.position.x": "nighakin stow x", "structure_block.position.y": "nighakin stow y", "structure_block.position.z": "nighakin stow z", "structure_block.save_failure": "Cannot keep framework '%s'", "structure_block.save_success": "Framework kept as '%s'", "structure_block.show_air": "Show Unseenly Clots:", "structure_block.show_boundingbox": "Show Bounding Box:", "structure_block.size": "Framework Breadth", "structure_block.size.x": "framework breadth x", "structure_block.size.y": "framework breadth y", "structure_block.size.z": "framework breadth z", "structure_block.size_failure": "Could not acknow framework breadth. <PERSON>ke hirns with matching framework names", "structure_block.size_success": "Breadth speedfully acknown for '%s'", "structure_block.strict": "Stern Stow:", "structure_block.structure_name": "Framework Name", "subtitles.ambient.cave": "Eerie din", "subtitles.ambient.sound": "Eerie din", "subtitles.block.amethyst_block.chime": "Drunklack rings", "subtitles.block.amethyst_block.resonate": "Drunklack tolls", "subtitles.block.anvil.destroy": "Anvil broken", "subtitles.block.anvil.land": "Anvil landed", "subtitles.block.anvil.use": "Anvil brooked", "subtitles.block.barrel.close": "Vat shuts", "subtitles.block.barrel.open": "Vat opens", "subtitles.block.beacon.activate": "Beacon astirs", "subtitles.block.beacon.ambient": "Beacon hums", "subtitles.block.beacon.deactivate": "Beacon unastirs", "subtitles.block.beacon.power_select": "Beacon might chosen", "subtitles.block.beehive.drip": "Honey drips", "subtitles.block.beehive.enter": "<PERSON> goes into hive", "subtitles.block.beehive.exit": "Bee leaves hive", "subtitles.block.beehive.shear": "Shears shrape", "subtitles.block.beehive.work": "Bees work", "subtitles.block.bell.resonate": "Bell tolls", "subtitles.block.bell.use": "Bell rings", "subtitles.block.big_dripleaf.tilt_down": "Dripleaf tilts down", "subtitles.block.big_dripleaf.tilt_up": "Dripleaf tilts up", "subtitles.block.blastfurnace.fire_crackle": "Blast Oven crackles", "subtitles.block.brewing_stand.brew": "Brewing Stand bubbles", "subtitles.block.bubble_column.bubble_pop": "Bubbles pop", "subtitles.block.bubble_column.upwards_ambient": "Bubbles flow", "subtitles.block.bubble_column.upwards_inside": "Bubbles woosh", "subtitles.block.bubble_column.whirlpool_ambient": "Bubbles wharftle", "subtitles.block.bubble_column.whirlpool_inside": "Bubbles zoom", "subtitles.block.button.click": "Knap clicks", "subtitles.block.cake.add_candle": "<PERSON><PERSON> squishes", "subtitles.block.campfire.crackle": "Haltfire crackles", "subtitles.block.candle.crackle": "Waxlight crackles", "subtitles.block.candle.extinguish": "Waxlight douses", "subtitles.block.chest.close": "Chest shuts", "subtitles.block.chest.locked": "Chest locked", "subtitles.block.chest.open": "Chest opens", "subtitles.block.chorus_flower.death": "Glee Blossom withers", "subtitles.block.chorus_flower.grow": "Glee Blossom grows", "subtitles.block.comparator.click": "Bemeter clicks", "subtitles.block.composter.empty": "Bonemealer emptied", "subtitles.block.composter.fill": "Bonemealer filled", "subtitles.block.composter.ready": "Bonemealer bonemeals", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON> as<PERSON>", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON> beats", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON> strikes", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON> unastirs", "subtitles.block.copper_bulb.turn_off": "Are Lightvat lays off", "subtitles.block.copper_bulb.turn_on": "Are Lightvat lays on", "subtitles.block.copper_trapdoor.close": "Trapdoor shuts", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter trucks crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart rumbles", "subtitles.block.creaking_heart.idle": "Eerie din", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry louds", "subtitles.block.decorated_pot.insert": "Bedecked Pot fills", "subtitles.block.decorated_pot.insert_fail": "Bedecked Pot shakes", "subtitles.block.decorated_pot.shatter": "Bedecked Pot shatters", "subtitles.block.dispenser.dispense": "Thing thrown", "subtitles.block.dispenser.fail": "Thrower trucked", "subtitles.block.door.toggle": "Door creaks", "subtitles.block.dried_ghast.ambient": "Louds of dryness", "subtitles.block.dried_ghast.ambient_water": "Dried Ghast edwaters", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy louds", "subtitles.block.enchantment_table.use": "Galdercraft Bench brooked", "subtitles.block.end_portal.spawn": "End Ingang opens", "subtitles.block.end_portal_frame.fill": "Eye of Ender links", "subtitles.block.eyeblossom.close": "Eyeblossom shuts", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Edder Gate creaks", "subtitles.block.fire.ambient": "Fire crackles", "subtitles.block.fire.extinguish": "Fire doused", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Toadhead hatches", "subtitles.block.furnace.fire_crackle": "Oven crackles", "subtitles.block.generic.break": "Clot broken", "subtitles.block.generic.fall": "Something falls on a clot", "subtitles.block.generic.footsteps": "Footsteps", "subtitles.block.generic.hit": "Clot breaking", "subtitles.block.generic.place": "Clot laid", "subtitles.block.grindstone.use": "Grindstone brooked", "subtitles.block.growing_plant.crop": "Wort cropped", "subtitles.block.hanging_sign.waxed_interact_fail": "Token shakes", "subtitles.block.honey_block.slide": "Sliding down a honey clot", "subtitles.block.iron_trapdoor.close": "Trapdoor shuts", "subtitles.block.iron_trapdoor.open": "Trapdoor opens", "subtitles.block.lava.ambient": "Moltenstone pops", "subtitles.block.lava.extinguish": "Moltenstone hisses", "subtitles.block.lever.click": "Heaver clicks", "subtitles.block.note_block.note": "<PERSON><PERSON> plays", "subtitles.block.pale_hanging_moss.idle": "Eerie din", "subtitles.block.piston.move": "Stampend shifts", "subtitles.block.pointed_dripstone.drip_lava": "Moltenstone drips", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Moltenstone drips into Ironcrock", "subtitles.block.pointed_dripstone.drip_water": "Water drips", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Water drips into Ironcrock", "subtitles.block.pointed_dripstone.land": "Dripstone falls down", "subtitles.block.portal.ambient": "Ingang whooshes", "subtitles.block.portal.travel": "Ingang din wanes", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON> din strengthens", "subtitles.block.pressure_plate.click": "Thrutch Tile clicks", "subtitles.block.pumpkin.carve": "Shears carve", "subtitles.block.redstone_torch.burnout": "Thackle fizzes", "subtitles.block.respawn_anchor.ambient": "Edstart Holder whooshes", "subtitles.block.respawn_anchor.charge": "Edstart Holder is loaded", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON> Holder breaks", "subtitles.block.respawn_anchor.set_spawn": "Edstart Holder sets starting ord", "subtitles.block.sand.idle": "Sandy louds", "subtitles.block.sand.wind": "Windy louds", "subtitles.block.sculk.charge": "Sculk bubbles", "subtitles.block.sculk.spread": "Sculk spreads", "subtitles.block.sculk_catalyst.bloom": "Sculk Sunderer blossoms", "subtitles.block.sculk_sensor.clicking": "Sculk Feeler clicks", "subtitles.block.sculk_sensor.clicking_stop": "Sc<PERSON><PERSON> Feeler stops clicking", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> yells", "subtitles.block.shulker_box.close": "Shulker box shuts", "subtitles.block.shulker_box.open": "Shulker box opens", "subtitles.block.sign.waxed_interact_fail": "Token shakes", "subtitles.block.smithing_table.use": "Smith<PERSON> Bench <PERSON>", "subtitles.block.smoker.smoke": "Smoker smokes", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.hatch": "Sniffer Ey hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Seaswamb soaks up", "subtitles.block.sweet_berry_bush.pick_berries": "Berries pop", "subtitles.block.trapdoor.close": "Trapdoor shuts", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Trapdoor creaks", "subtitles.block.trial_spawner.about_to_spawn_item": "Threatening thing readies", "subtitles.block.trial_spawner.ambient": "<PERSON>d <PERSON> crackles", "subtitles.block.trial_spawner.ambient_charged": "Threatening crackling", "subtitles.block.trial_spawner.ambient_ominous": "Threatening crackling", "subtitles.block.trial_spawner.charge_activate": "Halsend whelms <PERSON>d Beyetner", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON> Beyetner shuts", "subtitles.block.trial_spawner.detect_player": "Fand Beyetner loads up", "subtitles.block.trial_spawner.eject_item": "<PERSON><PERSON> throws out things", "subtitles.block.trial_spawner.ominous_activate": "Halsend whelms <PERSON>d Beyetner", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON> opens", "subtitles.block.trial_spawner.spawn_item": "Threatening thing drops", "subtitles.block.trial_spawner.spawn_item_begin": "Threatening thing shows up", "subtitles.block.trial_spawner.spawn_mob": "<PERSON>d Beyetner springs a wight", "subtitles.block.tripwire.attach": "<PERSON><PERSON> fastens on", "subtitles.block.tripwire.click": "Tripwire clicks", "subtitles.block.tripwire.detach": "Tripwire unfastens", "subtitles.block.vault.activate": "Lockvat fires", "subtitles.block.vault.ambient": "<PERSON>vat crackles", "subtitles.block.vault.close_shutter": "Lockvat shuts", "subtitles.block.vault.deactivate": "<PERSON><PERSON> douses", "subtitles.block.vault.eject_item": "<PERSON><PERSON> throws out thing", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "<PERSON>vat forbears thing", "subtitles.block.vault.open_shutter": "<PERSON><PERSON> opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> forbears player", "subtitles.block.water.ambient": "Water flows", "subtitles.block.wet_sponge.dries": "Seaswamb dries", "subtitles.chiseled_bookshelf.insert": "Book laid", "subtitles.chiseled_bookshelf.insert_enchanted": "Galed Book laid", "subtitles.chiseled_bookshelf.take": "Book nimmed", "subtitles.chiseled_bookshelf.take_enchanted": "Galed Book nimmed", "subtitles.enchant.thorns.hit": "<PERSON><PERSON> prick", "subtitles.entity.allay.ambient_with_item": "Allay seeks", "subtitles.entity.allay.ambient_without_item": "Allay yearns", "subtitles.entity.allay.death": "Allay swelts", "subtitles.entity.allay.hurt": "Allay wounds", "subtitles.entity.allay.item_given": "<PERSON><PERSON> laughs", "subtitles.entity.allay.item_taken": "Allay allays", "subtitles.entity.allay.item_thrown": "<PERSON>ay throws", "subtitles.entity.armadillo.ambient": "Girdledeer grunts", "subtitles.entity.armadillo.brush": "Shale is bristled off", "subtitles.entity.armadillo.death": "Girdledeer swelts", "subtitles.entity.armadillo.eat": "Girdledeer eats", "subtitles.entity.armadillo.hurt": "Girdledeer wounds", "subtitles.entity.armadillo.hurt_reduced": "Girdledeer shields itself", "subtitles.entity.armadillo.land": "Girdledeer lands", "subtitles.entity.armadillo.peek": "Girdledeer looks", "subtitles.entity.armadillo.roll": "Girdledeer folds up", "subtitles.entity.armadillo.scute_drop": "Girdledeer sheds shale", "subtitles.entity.armadillo.unroll_finish": "Girdledeer unfolds", "subtitles.entity.armadillo.unroll_start": "Girdledeer looks", "subtitles.entity.armor_stand.fall": "Something fell", "subtitles.entity.arrow.hit": "Arrow strikes", "subtitles.entity.arrow.hit_player": "Player struck", "subtitles.entity.arrow.shoot": "Arrow fired", "subtitles.entity.axolotl.attack": "Waterhelper strikes", "subtitles.entity.axolotl.death": "Waterhelper swelts", "subtitles.entity.axolotl.hurt": "Waterhelper wounds", "subtitles.entity.axolotl.idle_air": "Waterhelper chirps", "subtitles.entity.axolotl.idle_water": "Waterhelper chirps", "subtitles.entity.axolotl.splash": "Waterhelper splashes", "subtitles.entity.axolotl.swim": "Waterhelper swims", "subtitles.entity.bat.ambient": "Flittermouse screeches", "subtitles.entity.bat.death": "Flittermouse swelts", "subtitles.entity.bat.hurt": "Flittermouse wounds", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON> leaps to flight", "subtitles.entity.bee.ambient": "Bee buzzes", "subtitles.entity.bee.death": "<PERSON> swelts", "subtitles.entity.bee.hurt": "Bee wounds", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "Bee buzzes wrothly", "subtitles.entity.bee.pollinate": "Bee buzzes happily", "subtitles.entity.bee.sting": "Bee stings", "subtitles.entity.blaze.ambient": "<PERSON> breathes", "subtitles.entity.blaze.burn": "<PERSON> crackles", "subtitles.entity.blaze.death": "<PERSON> swelts", "subtitles.entity.blaze.hurt": "Blaze wounds", "subtitles.entity.blaze.shoot": "Blaze shoots", "subtitles.entity.boat.paddle_land": "Rowing", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Fenned rattles", "subtitles.entity.bogged.death": "Fenned swelts", "subtitles.entity.bogged.hurt": "Fenned wounds", "subtitles.entity.breeze.charge": "<PERSON>hith fills", "subtitles.entity.breeze.death": "Whith swelts", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> bounces off", "subtitles.entity.breeze.hurt": "Whith wounds", "subtitles.entity.breeze.idle_air": "Whith flies", "subtitles.entity.breeze.idle_ground": "Whith drones", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON> breathes in", "subtitles.entity.breeze.jump": "Whith leaps", "subtitles.entity.breeze.land": "Whith lands", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.breeze.slide": "Whith slides", "subtitles.entity.breeze.whirl": "Whith wharftles", "subtitles.entity.breeze.wind_burst": "Wind Blast bursts", "subtitles.entity.camel.ambient": "Drylander grunts", "subtitles.entity.camel.dash": "Drylander yeets", "subtitles.entity.camel.dash_ready": "Drylander wurps", "subtitles.entity.camel.death": "Drylander swelts", "subtitles.entity.camel.eat": "Drylander eats", "subtitles.entity.camel.hurt": "Drylander wounds", "subtitles.entity.camel.saddle": "Saddle bedights", "subtitles.entity.camel.sit": "Drylander sits down", "subtitles.entity.camel.stand": "Drylander stands up", "subtitles.entity.camel.step": "Drylander steps", "subtitles.entity.camel.step_sand": "Drylander sands", "subtitles.entity.cat.ambient": "<PERSON> meows", "subtitles.entity.cat.beg_for_food": "Cat begs", "subtitles.entity.cat.death": "Cat swelts", "subtitles.entity.cat.eat": "Cat eats", "subtitles.entity.cat.hiss": "Cat hisses", "subtitles.entity.cat.hurt": "Cat wounds", "subtitles.entity.cat.purr": "Cat purrs", "subtitles.entity.chicken.ambient": "Chicken clucks", "subtitles.entity.chicken.death": "Chicken swelts", "subtitles.entity.chicken.egg": "Chicken plops", "subtitles.entity.chicken.hurt": "Chicken wounds", "subtitles.entity.cod.death": "Cod swelts", "subtitles.entity.cod.flop": "Cod flops", "subtitles.entity.cod.hurt": "Cod wounds", "subtitles.entity.cow.ambient": "Cow moos", "subtitles.entity.cow.death": "Cow swelts", "subtitles.entity.cow.hurt": "Cow wounds", "subtitles.entity.cow.milk": "Cow is milked", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking strikes", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking atews", "subtitles.entity.creaking.sway": "Creaking is struck", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking shrithes", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> swelts", "subtitles.entity.creeper.hurt": "Creeper wounds", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> hisses", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON> chirps", "subtitles.entity.dolphin.ambient_water": "Merswine whistles", "subtitles.entity.dolphin.attack": "Merswine strikes", "subtitles.entity.dolphin.death": "Merswine swelts", "subtitles.entity.dolphin.eat": "Merswine eats", "subtitles.entity.dolphin.hurt": "Merswine wounds", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> leaps", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> plays", "subtitles.entity.dolphin.splash": "Merswine splashes", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON> swims", "subtitles.entity.donkey.ambient": "Dunky hee-haws", "subtitles.entity.donkey.angry": "Dunky neighs", "subtitles.entity.donkey.chest": "Dunky Chest bedights", "subtitles.entity.donkey.death": "Dunky swelts", "subtitles.entity.donkey.eat": "Dunky eats", "subtitles.entity.donkey.hurt": "Dunky wounds", "subtitles.entity.donkey.jump": "Dunky leaps", "subtitles.entity.drowned.ambient": "Drenched gurgles", "subtitles.entity.drowned.ambient_water": "Drenched gurgles", "subtitles.entity.drowned.death": "Drenched swelts", "subtitles.entity.drowned.hurt": "Drenched wounds", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> throws <PERSON><PERSON>", "subtitles.entity.drowned.step": "Drenched steps", "subtitles.entity.drowned.swim": "Drenched swims", "subtitles.entity.egg.throw": "Ey flies", "subtitles.entity.elder_guardian.ambient": "<PERSON> moans", "subtitles.entity.elder_guardian.ambient_land": "Elder <PERSON> flaps", "subtitles.entity.elder_guardian.curse": "Elder Ward curses", "subtitles.entity.elder_guardian.death": "<PERSON> swelts", "subtitles.entity.elder_guardian.flop": "<PERSON> flops", "subtitles.entity.elder_guardian.hurt": "Elder Ward wounds", "subtitles.entity.ender_dragon.ambient": "Worm roars", "subtitles.entity.ender_dragon.death": "Worm swelts", "subtitles.entity.ender_dragon.flap": "Worm flaps", "subtitles.entity.ender_dragon.growl": "Worm growls", "subtitles.entity.ender_dragon.hurt": "Worm wounds", "subtitles.entity.ender_dragon.shoot": "Worm shoots", "subtitles.entity.ender_eye.death": "Eye of <PERSON><PERSON> falls", "subtitles.entity.ender_eye.launch": "Eye of <PERSON><PERSON> shoots", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> flies", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoops", "subtitles.entity.enderman.death": "<PERSON><PERSON> swelts", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> wounds", "subtitles.entity.enderman.scream": "<PERSON><PERSON> yells", "subtitles.entity.enderman.stare": "<PERSON><PERSON> yells", "subtitles.entity.enderman.teleport": "Enderman farferries", "subtitles.entity.endermite.ambient": "Endermite scuttles", "subtitles.entity.endermite.death": "Endermite swelts", "subtitles.entity.endermite.hurt": "Endermite wounds", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON> mumbles", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON> casts spell", "subtitles.entity.evoker.celebrate": "Awakener gladdens", "subtitles.entity.evoker.death": "Awakener swelts", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON><PERSON> wounds", "subtitles.entity.evoker.prepare_attack": "Awakener readies strike", "subtitles.entity.evoker.prepare_summon": "Awakener readies beckoning", "subtitles.entity.evoker.prepare_wololo": "Awakener readies begaling", "subtitles.entity.evoker_fangs.attack": "Fangs snap", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON> gained", "subtitles.entity.firework_rocket.blast": "Firework blasts", "subtitles.entity.firework_rocket.launch": "Firework lights", "subtitles.entity.firework_rocket.twinkle": "Firework twinkles", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON> brought back", "subtitles.entity.fishing_bobber.splash": "Fishing Bobber splashes", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> thrown", "subtitles.entity.fox.aggro": "<PERSON> wraths", "subtitles.entity.fox.ambient": "Fox squeaks", "subtitles.entity.fox.bite": "Fox bites", "subtitles.entity.fox.death": "<PERSON> swelts", "subtitles.entity.fox.eat": "Fox eats", "subtitles.entity.fox.hurt": "<PERSON> wounds", "subtitles.entity.fox.screech": "<PERSON> screeches", "subtitles.entity.fox.sleep": "<PERSON> snores", "subtitles.entity.fox.sniff": "Fox sniffs", "subtitles.entity.fox.spit": "Fox spits", "subtitles.entity.fox.teleport": "Fox farferries", "subtitles.entity.frog.ambient": "Frosh croaks", "subtitles.entity.frog.death": "<PERSON>osh swelts", "subtitles.entity.frog.eat": "<PERSON><PERSON> eats", "subtitles.entity.frog.hurt": "Frosh wounds", "subtitles.entity.frog.lay_spawn": "Frosh lays ey", "subtitles.entity.frog.long_jump": "<PERSON><PERSON> leaps", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Burning", "subtitles.entity.generic.death": "Dying", "subtitles.entity.generic.drink": "Sipping", "subtitles.entity.generic.eat": "Eating", "subtitles.entity.generic.explode": "Blast", "subtitles.entity.generic.extinguish_fire": "Fire douses", "subtitles.entity.generic.hurt": "Something wounds", "subtitles.entity.generic.small_fall": "Something trips", "subtitles.entity.generic.splash": "Splashing", "subtitles.entity.generic.swim": "Swimming", "subtitles.entity.generic.wind_burst": "Wind Blast bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> weeps", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> swelts", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> wounds", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "Ghastling swelts", "subtitles.entity.ghastling.hurt": "Ghastling wounds", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> shows up", "subtitles.entity.glow_item_frame.add_item": "Glow Thing Frame fills", "subtitles.entity.glow_item_frame.break": "Glow Thing Frame broken", "subtitles.entity.glow_item_frame.place": "Glow Thing Frame stellt", "subtitles.entity.glow_item_frame.remove_item": "Glow Thing Frame empties", "subtitles.entity.glow_item_frame.rotate_item": "Glow Thing Frame clicks", "subtitles.entity.glow_squid.ambient": "Glow Sleevefish swims", "subtitles.entity.glow_squid.death": "Glow Sleevefish swelts", "subtitles.entity.glow_squid.hurt": "Glow Sleevefish wounds", "subtitles.entity.glow_squid.squirt": "Glow Sleevefish shoots bleck", "subtitles.entity.goat.ambient": "<PERSON><PERSON> bleats", "subtitles.entity.goat.death": "<PERSON><PERSON> swelts", "subtitles.entity.goat.eat": "Goat eats", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> breaks off", "subtitles.entity.goat.hurt": "Goat wounds", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> leaps", "subtitles.entity.goat.milk": "Goat is milked", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomps", "subtitles.entity.goat.ram_impact": "Goat rams", "subtitles.entity.goat.screaming.ambient": "Goat bellows", "subtitles.entity.goat.step": "Goat steps", "subtitles.entity.guardian.ambient": "<PERSON> moans", "subtitles.entity.guardian.ambient_land": "Ward flaps", "subtitles.entity.guardian.attack": "<PERSON> shoots", "subtitles.entity.guardian.death": "<PERSON> swelts", "subtitles.entity.guardian.flop": "Ward flops", "subtitles.entity.guardian.hurt": "Ward wounds", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON> swelts", "subtitles.entity.happy_ghast.equip": "Read bedights", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> wounds", "subtitles.entity.happy_ghast.unequip": "Read unbedights", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> growls", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> growls wrothly", "subtitles.entity.hoglin.attack": "Hoglin strikes", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> shifts to Zoglin", "subtitles.entity.hoglin.death": "<PERSON>glin swelts", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n wounds", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> falls back", "subtitles.entity.hoglin.step": "Hoglin steps", "subtitles.entity.horse.ambient": "Horse neighs", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Horse hirsting bedights", "subtitles.entity.horse.breathe": "Horse breathes", "subtitles.entity.horse.death": "Horse swelts", "subtitles.entity.horse.eat": "Horse eats", "subtitles.entity.horse.gallop": "Horse runs", "subtitles.entity.horse.hurt": "Horse wounds", "subtitles.entity.horse.jump": "Horse leaps", "subtitles.entity.horse.saddle": "Saddle bedights", "subtitles.entity.husk.ambient": "<PERSON><PERSON> groans", "subtitles.entity.husk.converted_to_zombie": "Husk shifts to Undead Lich", "subtitles.entity.husk.death": "Husk swelts", "subtitles.entity.husk.hurt": "Husk wounds", "subtitles.entity.illusioner.ambient": "Fokener mumbles", "subtitles.entity.illusioner.cast_spell": "Fokener casts spell", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.illusioner.hurt": "Fokener wounds", "subtitles.entity.illusioner.mirror_move": "Fokener upends", "subtitles.entity.illusioner.prepare_blindness": "Fokener readies blindness", "subtitles.entity.illusioner.prepare_mirror": "Fokener readies an anlikeness", "subtitles.entity.iron_golem.attack": "Iron Livenedman strikes", "subtitles.entity.iron_golem.damage": "Iron Livenedman breaks", "subtitles.entity.iron_golem.death": "Iron Livenedman swelts", "subtitles.entity.iron_golem.hurt": "Iron Livenedman wounds", "subtitles.entity.iron_golem.repair": "Iron Livenedman healed", "subtitles.entity.item.break": "Thing breaks", "subtitles.entity.item.pickup": "Thing plops", "subtitles.entity.item_frame.add_item": "Thing Frame fills", "subtitles.entity.item_frame.break": "Thing Frame broken", "subtitles.entity.item_frame.place": "Thing Frame stellt", "subtitles.entity.item_frame.remove_item": "Thing Frame empties", "subtitles.entity.item_frame.rotate_item": "Thing Frame clicks", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Lightning strikes", "subtitles.entity.lightning_bolt.thunder": "Thunder roars", "subtitles.entity.llama.ambient": "Drylandersheep bleats", "subtitles.entity.llama.angry": "Drylandersheep bleats wrothly", "subtitles.entity.llama.chest": "Drylandersheep Chest bedights", "subtitles.entity.llama.death": "Drylandersheep swelts", "subtitles.entity.llama.eat": "Drylandersheep eats", "subtitles.entity.llama.hurt": "Drylandersheep wounds", "subtitles.entity.llama.spit": "Drylandersheep spits", "subtitles.entity.llama.step": "Drylandersheep steps", "subtitles.entity.llama.swag": "Llama is fratowed", "subtitles.entity.magma_cube.death": "Moltenstone Slime swelts", "subtitles.entity.magma_cube.hurt": "Moltenstone Slime wounds", "subtitles.entity.magma_cube.squish": "Moltenstone Slime squishes", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> rattles", "subtitles.entity.minecart.inside_underwater": "Del<PERSON><PERSON> rattles underwater", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON> welts", "subtitles.entity.mooshroom.convert": "Mooshroom shapeshifts", "subtitles.entity.mooshroom.eat": "Mooshroom eats", "subtitles.entity.mooshroom.milk": "Mooshroom is milked", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom is milked eerily", "subtitles.entity.mule.ambient": "Dunkyhorse hee-haws", "subtitles.entity.mule.angry": "Dunkyhorse neighs", "subtitles.entity.mule.chest": "Dunkyhorse Chest bedights", "subtitles.entity.mule.death": "Dunkyhorse swelts", "subtitles.entity.mule.eat": "Dunkyhorse eats", "subtitles.entity.mule.hurt": "Dunkyhorse wounds", "subtitles.entity.mule.jump": "Dunkyhorse leaps", "subtitles.entity.painting.break": "Meting broken", "subtitles.entity.painting.place": "Meting stellt", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON>ar huffs", "subtitles.entity.panda.ambient": "Catbear orths", "subtitles.entity.panda.bite": "Catbear bites", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON>ar bleats", "subtitles.entity.panda.death": "<PERSON><PERSON><PERSON> swelts", "subtitles.entity.panda.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.panda.hurt": "Catbear wounds", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "<PERSON><PERSON><PERSON> sneezes", "subtitles.entity.panda.step": "Catbear steps", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON><PERSON> whimpers", "subtitles.entity.parrot.ambient": "Bleefowl talks", "subtitles.entity.parrot.death": "Bleefowl swelts", "subtitles.entity.parrot.eats": "Bleefowl eats", "subtitles.entity.parrot.fly": "Bleefowl flutters", "subtitles.entity.parrot.hurts": "Bleefowl wounds", "subtitles.entity.parrot.imitate.blaze": "Bleefowl breathes", "subtitles.entity.parrot.imitate.bogged": "Bleefowl rattles", "subtitles.entity.parrot.imitate.breeze": "Bleefowl drones", "subtitles.entity.parrot.imitate.creaking": "Bleefowl creaks", "subtitles.entity.parrot.imitate.creeper": "Bleefowl hisses", "subtitles.entity.parrot.imitate.drowned": "Bleefowl gurgles", "subtitles.entity.parrot.imitate.elder_guardian": "Bleefowl moans", "subtitles.entity.parrot.imitate.ender_dragon": "Bleefowl roars", "subtitles.entity.parrot.imitate.endermite": "Bleefowl scuttles", "subtitles.entity.parrot.imitate.evoker": "Bleefowl mumbles", "subtitles.entity.parrot.imitate.ghast": "Bleefowl weeps", "subtitles.entity.parrot.imitate.guardian": "Bleefowl moans", "subtitles.entity.parrot.imitate.hoglin": "Bleefowl growls", "subtitles.entity.parrot.imitate.husk": "Bleefowl groans", "subtitles.entity.parrot.imitate.illusioner": "Bleefowl mumbles", "subtitles.entity.parrot.imitate.magma_cube": "Bleefowl squishes", "subtitles.entity.parrot.imitate.phantom": "Bleefowl screeches", "subtitles.entity.parrot.imitate.piglin": "Bleefowl snorts", "subtitles.entity.parrot.imitate.piglin_brute": "Bleefowl snorts", "subtitles.entity.parrot.imitate.pillager": "Bleefowl mumbles", "subtitles.entity.parrot.imitate.ravager": "Bleefowl grunts", "subtitles.entity.parrot.imitate.shulker": "Bleefowl lurks", "subtitles.entity.parrot.imitate.silverfish": "Bleefowl hisses", "subtitles.entity.parrot.imitate.skeleton": "Bleefowl rattles", "subtitles.entity.parrot.imitate.slime": "Bleefowl squishes", "subtitles.entity.parrot.imitate.spider": "Bleefowl hisses", "subtitles.entity.parrot.imitate.stray": "Bleefowl rattles", "subtitles.entity.parrot.imitate.vex": "Bleefowl nettles", "subtitles.entity.parrot.imitate.vindicator": "Bleefowl mumbles", "subtitles.entity.parrot.imitate.warden": "Bleefowl whines", "subtitles.entity.parrot.imitate.witch": "Bleefowl giggles", "subtitles.entity.parrot.imitate.wither": "Bleefowl wraths", "subtitles.entity.parrot.imitate.wither_skeleton": "Bleefowl rattles", "subtitles.entity.parrot.imitate.zoglin": "Bleefowl growls", "subtitles.entity.parrot.imitate.zombie": "Bleefowl groans", "subtitles.entity.parrot.imitate.zombie_villager": "Bleefowl groans", "subtitles.entity.phantom.ambient": "Nightgost screeches", "subtitles.entity.phantom.bite": "Nightgost bites", "subtitles.entity.phantom.death": "Nightgost swelts", "subtitles.entity.phantom.flap": "Nightgost flaps", "subtitles.entity.phantom.hurt": "Nightgost wounds", "subtitles.entity.phantom.swoop": "Nightgost swoops", "subtitles.entity.pig.ambient": "Swine oinks", "subtitles.entity.pig.death": "Swine swelts", "subtitles.entity.pig.hurt": "Swine wounds", "subtitles.entity.pig.saddle": "Saddle bedights", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> wonders at thing", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin.angry": "<PERSON><PERSON> snorts wrothly", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> merrymakes", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> shifts to Undead Piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> swelts", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> wounds", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snorts ondily", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> falls back", "subtitles.entity.piglin.step": "<PERSON><PERSON> steps", "subtitles.entity.piglin_brute.ambient": "<PERSON> snorts", "subtitles.entity.piglin_brute.angry": "<PERSON> snorts wrothly", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON> shifts to Undead Piglin", "subtitles.entity.piglin_brute.death": "<PERSON> swelts", "subtitles.entity.piglin_brute.hurt": "<PERSON> wounds", "subtitles.entity.piglin_brute.step": "<PERSON> steps", "subtitles.entity.pillager.ambient": "Reaver mumbles", "subtitles.entity.pillager.celebrate": "<PERSON>aver gladdens", "subtitles.entity.pillager.death": "Reaver swelts", "subtitles.entity.pillager.hurt": "Reaver wounds", "subtitles.entity.player.attack.crit": "Heavy strike", "subtitles.entity.player.attack.knockback": "Knockback strike", "subtitles.entity.player.attack.strong": "Strong strike", "subtitles.entity.player.attack.sweep": "Sweeping strike", "subtitles.entity.player.attack.weak": "Woak strike", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Player swelts", "subtitles.entity.player.freeze_hurt": "Player freezes", "subtitles.entity.player.hurt": "Player wounds", "subtitles.entity.player.hurt_drown": "Player choking on water", "subtitles.entity.player.hurt_on_fire": "Player burns", "subtitles.entity.player.levelup": "Player dings", "subtitles.entity.player.teleport": "Player farferries", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.polar_bear.ambient_baby": "Baby <PERSON> hums", "subtitles.entity.polar_bear.death": "Icebear swelts", "subtitles.entity.polar_bear.hurt": "Icebear wounds", "subtitles.entity.polar_bear.warning": "Icebear roars", "subtitles.entity.potion.splash": "<PERSON><PERSON> smashes", "subtitles.entity.potion.throw": "<PERSON><PERSON> thrown", "subtitles.entity.puffer_fish.blow_out": "Pufferfish unpuffs", "subtitles.entity.puffer_fish.blow_up": "Pufferfish puffs up", "subtitles.entity.puffer_fish.death": "Pufferfish swelts", "subtitles.entity.puffer_fish.flop": "Pufferfish flops", "subtitles.entity.puffer_fish.hurt": "Pufferfish wounds", "subtitles.entity.puffer_fish.sting": "Pufferfish stings", "subtitles.entity.rabbit.ambient": "Hare squeaks", "subtitles.entity.rabbit.attack": "Hare strikes", "subtitles.entity.rabbit.death": "Hare swelts", "subtitles.entity.rabbit.hurt": "Hare wounds", "subtitles.entity.rabbit.jump": "Hare hops", "subtitles.entity.ravager.ambient": "Weaster grunts", "subtitles.entity.ravager.attack": "Weaster bites", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON> gladdens", "subtitles.entity.ravager.death": "Weaster swelts", "subtitles.entity.ravager.hurt": "Weaster wounds", "subtitles.entity.ravager.roar": "Weaster roars", "subtitles.entity.ravager.step": "Weaster steps", "subtitles.entity.ravager.stunned": "<PERSON><PERSON> stunned", "subtitles.entity.salmon.death": "Lax swelts", "subtitles.entity.salmon.flop": "Lax flops", "subtitles.entity.salmon.hurt": "Lax wounds", "subtitles.entity.sheep.ambient": "Sheep baahs", "subtitles.entity.sheep.death": "Sheep swelts", "subtitles.entity.sheep.hurt": "Sheep wounds", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> lurks", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> shuts", "subtitles.entity.shulker.death": "<PERSON>lk<PERSON> swelts", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> wounds", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> farferries", "subtitles.entity.shulker_bullet.hit": "Shulk<PERSON> Shot blows up", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> Shot breaks", "subtitles.entity.silverfish.ambient": "Silverfish hisses", "subtitles.entity.silverfish.death": "Silverfish swelts", "subtitles.entity.silverfish.hurt": "Silverfish wounds", "subtitles.entity.skeleton.ambient": "Boneframe rattles", "subtitles.entity.skeleton.converted_to_stray": "Boneframe umbwends to Drifter", "subtitles.entity.skeleton.death": "Boneframe swelts", "subtitles.entity.skeleton.hurt": "Boneframe wounds", "subtitles.entity.skeleton.shoot": "Boneframe shoots", "subtitles.entity.skeleton_horse.ambient": "Boneframe Horse weeps", "subtitles.entity.skeleton_horse.death": "Boneframe Horse swelts", "subtitles.entity.skeleton_horse.hurt": "Boneframe Horse wounds", "subtitles.entity.skeleton_horse.jump_water": "Boneframe Horse leaps", "subtitles.entity.skeleton_horse.swim": "Boneframe Horse swims", "subtitles.entity.slime.attack": "Slime strikes", "subtitles.entity.slime.death": "Slime swelts", "subtitles.entity.slime.hurt": "Slime wounds", "subtitles.entity.slime.squish": "Slime squishes", "subtitles.entity.sniffer.death": "Sniffer swelts", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> stands up", "subtitles.entity.sniffer.drop_seed": "Sniffer drops seed", "subtitles.entity.sniffer.eat": "Sniffer eats", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.egg_hatch": "Sniffer Ey hatches", "subtitles.entity.sniffer.happy": "Sniffer glees", "subtitles.entity.sniffer.hurt": "Sniffer wounds", "subtitles.entity.sniffer.idle": "Sniffer grunts", "subtitles.entity.sniffer.scenting": "Sniffer smells", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON> seeks", "subtitles.entity.sniffer.sniffing": "Sniffer sniffs", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "Snowman swelts", "subtitles.entity.snow_golem.hurt": "Snowman wounds", "subtitles.entity.snowball.throw": "Snowball flies", "subtitles.entity.spider.ambient": "Spider hisses", "subtitles.entity.spider.death": "<PERSON> swelts", "subtitles.entity.spider.hurt": "Spider wounds", "subtitles.entity.squid.ambient": "Sleevefish swims", "subtitles.entity.squid.death": "Sleevefish swelts", "subtitles.entity.squid.hurt": "Sleevefish wounds", "subtitles.entity.squid.squirt": "Sleevefish shoots bleck", "subtitles.entity.stray.ambient": "Drifter rattles", "subtitles.entity.stray.death": "Drift<PERSON> swelts", "subtitles.entity.stray.hurt": "Drifter wounds", "subtitles.entity.strider.death": "Strider swelts", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.strider.happy": "Strider bells", "subtitles.entity.strider.hurt": "Strider wounds", "subtitles.entity.strider.idle": "Strider chirps", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON> falls back", "subtitles.entity.tadpole.death": "Toadhead swelts", "subtitles.entity.tadpole.flop": "Toadhead flops", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON> grows up", "subtitles.entity.tadpole.hurt": "Toadhead wounds", "subtitles.entity.tnt.primed": "Blastle fizzes", "subtitles.entity.tropical_fish.death": "Southsea Fish swelts", "subtitles.entity.tropical_fish.flop": "Southsea Fish flops", "subtitles.entity.tropical_fish.hurt": "Southsea Fish wounds", "subtitles.entity.turtle.ambient_land": "Shellpad chirps", "subtitles.entity.turtle.death": "Shellpad swelts", "subtitles.entity.turtle.death_baby": "<PERSON> swelts", "subtitles.entity.turtle.egg_break": "Shellpad Ey breaks", "subtitles.entity.turtle.egg_crack": "Shellpad Ey cracks", "subtitles.entity.turtle.egg_hatch": "Shellpad Ey hatches", "subtitles.entity.turtle.hurt": "Shellpad wounds", "subtitles.entity.turtle.hurt_baby": "<PERSON> wounds", "subtitles.entity.turtle.lay_egg": "Shellpad lays ey", "subtitles.entity.turtle.shamble": "Shellpad shambles", "subtitles.entity.turtle.shamble_baby": "Baby <PERSON>pad shambles", "subtitles.entity.turtle.swim": "Shellpad swims", "subtitles.entity.vex.ambient": "Nettler nettles", "subtitles.entity.vex.charge": "<PERSON><PERSON> yells", "subtitles.entity.vex.death": "<PERSON><PERSON> swelts", "subtitles.entity.vex.hurt": "Nettler wounds", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON> mumbles", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON> gladdens", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON> swelts", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON> wounds", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON> unathweres", "subtitles.entity.villager.trade": "<PERSON><PERSON><PERSON> wrixles", "subtitles.entity.villager.work_armorer": "Hirstingsmith works", "subtitles.entity.villager.work_butcher": "Flesher works", "subtitles.entity.villager.work_cartographer": "Draftsman works", "subtitles.entity.villager.work_cleric": "Holyman works", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> works", "subtitles.entity.villager.work_fisherman": "Fisherman works", "subtitles.entity.villager.work_fletcher": "Arrowmaker works", "subtitles.entity.villager.work_leatherworker": "Leatherworker works", "subtitles.entity.villager.work_librarian": "Bookward works", "subtitles.entity.villager.work_mason": "Stonewright works", "subtitles.entity.villager.work_shepherd": "Shepherd works", "subtitles.entity.villager.work_toolsmith": "Toolsmith works", "subtitles.entity.villager.work_weaponsmith": "Weaponsmith works", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.ambient": "Whitewasher mumbles", "subtitles.entity.vindicator.celebrate": "Whitewasher gladdens", "subtitles.entity.vindicator.death": "Whitewasher swelts", "subtitles.entity.vindicator.hurt": "Whitewasher wounds", "subtitles.entity.wandering_trader.ambient": "Wandering Chapman mumbles", "subtitles.entity.wandering_trader.death": "Wan<PERSON> Chapman swelts", "subtitles.entity.wandering_trader.disappeared": "Wandering Chapman swinds", "subtitles.entity.wandering_trader.drink_milk": "Wandering Chapman drinks milk", "subtitles.entity.wandering_trader.drink_potion": "Wandering Chapman drinks lib", "subtitles.entity.wandering_trader.hurt": "Wan<PERSON> Chapman wounds", "subtitles.entity.wandering_trader.no": "Wandering <PERSON> unathweres", "subtitles.entity.wandering_trader.reappeared": "Wan<PERSON> Chapman pops out", "subtitles.entity.wandering_trader.trade": "Wandering Chapman wrixles", "subtitles.entity.wandering_trader.yes": "Wandering Chapman athweres", "subtitles.entity.warden.agitated": "<PERSON><PERSON> groans wrothly", "subtitles.entity.warden.ambient": "Holdend whines", "subtitles.entity.warden.angry": "Holdend maddens", "subtitles.entity.warden.attack_impact": "Holdend lands strike", "subtitles.entity.warden.death": "<PERSON><PERSON> swelts", "subtitles.entity.warden.dig": "Holdend digs", "subtitles.entity.warden.emerge": "<PERSON><PERSON> kythes", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON>'s heart beats", "subtitles.entity.warden.hurt": "Holdend wounds", "subtitles.entity.warden.listening": "Holdend heeds", "subtitles.entity.warden.listening_angry": "Holdend heeds wrothly", "subtitles.entity.warden.nearby_close": "Holden<PERSON> comes", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON> goes forth", "subtitles.entity.warden.nearby_closest": "Holdend draws near", "subtitles.entity.warden.roar": "Holdend roars", "subtitles.entity.warden.sniff": "Holdend sniffs", "subtitles.entity.warden.sonic_boom": "Holdend louds", "subtitles.entity.warden.sonic_charge": "Holdend fills", "subtitles.entity.warden.step": "Holdend steps", "subtitles.entity.warden.tendril_clicks": "<PERSON><PERSON>'s feelerheers click", "subtitles.entity.wind_charge.throw": "Wind Blast flies", "subtitles.entity.wind_charge.wind_burst": "Wind Blast bursts", "subtitles.entity.witch.ambient": "Witch giggles", "subtitles.entity.witch.celebrate": "Witch gladdens", "subtitles.entity.witch.death": "Witch swelts", "subtitles.entity.witch.drink": "Witch drinks", "subtitles.entity.witch.hurt": "Witch wounds", "subtitles.entity.witch.throw": "Witch throws", "subtitles.entity.wither.ambient": "Wither wraths", "subtitles.entity.wither.death": "Wither swelts", "subtitles.entity.wither.hurt": "Wither wounds", "subtitles.entity.wither.shoot": "Wither strikes", "subtitles.entity.wither.spawn": "<PERSON><PERSON> freed", "subtitles.entity.wither_skeleton.ambient": "Wither <PERSON>frame rattles", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON> swelts", "subtitles.entity.wither_skeleton.hurt": "Wither Boneframe wounds", "subtitles.entity.wolf.ambient": "Wolf orths", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON> swelts", "subtitles.entity.wolf.growl": "Wolf growls", "subtitles.entity.wolf.hurt": "Wolf wounds", "subtitles.entity.wolf.pant": "Wolf orths", "subtitles.entity.wolf.shake": "Wolf shakes", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> growls", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> growls wrothly", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> strikes", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> swelts", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> wounds", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> steps", "subtitles.entity.zombie.ambient": "Undead Lich groans", "subtitles.entity.zombie.attack_wooden_door": "Door shakes", "subtitles.entity.zombie.break_wooden_door": "Door breaks", "subtitles.entity.zombie.converted_to_drowned": "Undead Lich shifts to Drenched", "subtitles.entity.zombie.death": "Undead Lich swelts", "subtitles.entity.zombie.destroy_egg": "Shellpad Ey stomped", "subtitles.entity.zombie.hurt": "Undead Lich wounds", "subtitles.entity.zombie.infect": "Undead Lich smits", "subtitles.entity.zombie_horse.ambient": "Undead Horse weeps", "subtitles.entity.zombie_horse.death": "Undead Horse swelts", "subtitles.entity.zombie_horse.hurt": "Undead Horse wounds", "subtitles.entity.zombie_villager.ambient": "Undead <PERSON><PERSON><PERSON> groans", "subtitles.entity.zombie_villager.converted": "Undead <PERSON><PERSON><PERSON> yells", "subtitles.entity.zombie_villager.cure": "Undead <PERSON><PERSON><PERSON> snuffles", "subtitles.entity.zombie_villager.death": "Undead <PERSON><PERSON><PERSON> swelts", "subtitles.entity.zombie_villager.hurt": "Undead <PERSON><PERSON><PERSON> wounds", "subtitles.entity.zombified_piglin.ambient": "Undead <PERSON><PERSON> grunts", "subtitles.entity.zombified_piglin.angry": "Undead <PERSON>lin grunts wrothly", "subtitles.entity.zombified_piglin.death": "Undead <PERSON><PERSON> swelts", "subtitles.entity.zombified_piglin.hurt": "Undead <PERSON><PERSON> wounds", "subtitles.event.mob_effect.bad_omen": "Halsend arises", "subtitles.event.mob_effect.raid_omen": "Reaving draws near", "subtitles.event.mob_effect.trial_omen": "Threatening fand draws near", "subtitles.event.raid.horn": "Forboding horn blares", "subtitles.item.armor.equip": "Hirsting bedights", "subtitles.item.armor.equip_chain": "<PERSON>sh hirsting jingles", "subtitles.item.armor.equip_diamond": "Hardhirst hirsting clangs", "subtitles.item.armor.equip_elytra": "Enderglider rustles", "subtitles.item.armor.equip_gold": "Gold hirsting clinks", "subtitles.item.armor.equip_iron": "Iron hirsting clanks", "subtitles.item.armor.equip_leather": "Leather hirsting rustles", "subtitles.item.armor.equip_netherite": "Netherite hirsting clanks", "subtitles.item.armor.equip_turtle": "Shellpad Shell thunks", "subtitles.item.armor.equip_wolf": "<PERSON>ing bedights", "subtitles.item.armor.unequip_wolf": "<PERSON>ing unbedights", "subtitles.item.axe.scrape": "Axe shrapes", "subtitles.item.axe.strip": "Axe strips", "subtitles.item.axe.wax_off": "Wax off", "subtitles.item.bone_meal.use": "Bone Meal crinkles", "subtitles.item.book.page_turn": "Sheet rustles", "subtitles.item.book.put": "Book thumps", "subtitles.item.bottle.empty": "Handvat empties", "subtitles.item.bottle.fill": "Handvat fills", "subtitles.item.brush.brushing.generic": "Bristling", "subtitles.item.brush.brushing.gravel": "Bristling P<PERSON>bles", "subtitles.item.brush.brushing.gravel.complete": "Bristling Pebbles done", "subtitles.item.brush.brushing.sand": "Bristling Sand", "subtitles.item.brush.brushing.sand.complete": "Bristling Sand done", "subtitles.item.bucket.empty": "Stop empties", "subtitles.item.bucket.fill": "Stop fills", "subtitles.item.bucket.fill_axolotl": "Waterhelper scooped", "subtitles.item.bucket.fill_fish": "Fish holed", "subtitles.item.bucket.fill_tadpole": "Toad<PERSON> holed", "subtitles.item.bundle.drop_contents": "Bundle empties", "subtitles.item.bundle.insert": "Thing packed", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Thing unpacked", "subtitles.item.chorus_fruit.teleport": "Player farferries", "subtitles.item.crop.plant": "Crop sown", "subtitles.item.crossbow.charge": "Steel Bow loads up", "subtitles.item.crossbow.hit": "Arrow strikes", "subtitles.item.crossbow.load": "Steel Bow loads", "subtitles.item.crossbow.shoot": "Steel Bow fires", "subtitles.item.dye.use": "Dye stains", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Fireball whooshes", "subtitles.item.flintandsteel.use": "Flint and Steel click", "subtitles.item.glow_ink_sac.use": "Glow Bleck Cod splotches", "subtitles.item.goat_horn.play": "<PERSON><PERSON> plays", "subtitles.item.hoe.till": "Tiller tills", "subtitles.item.honey_bottle.drink": "Gulping", "subtitles.item.honeycomb.wax_on": "Wax on", "subtitles.item.horse_armor.unequip": "Horse Hirsting unbedights", "subtitles.item.ink_sac.use": "Bleck Cod splotches", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Stepcloth unbedights", "subtitles.item.lodestone_compass.lock": "Lodestone Northfinder locks onto Lodestone", "subtitles.item.mace.smash_air": "Beetle smashes", "subtitles.item.mace.smash_ground": "Beetle smashes", "subtitles.item.nether_wart.plant": "Crop sown", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON> breaks", "subtitles.item.saddle.unequip": "Saddle unbedights", "subtitles.item.shears.shear": "Shears click", "subtitles.item.shears.snip": "<PERSON><PERSON> snithe", "subtitles.item.shield.block": "Shield hinders", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> flattens", "subtitles.item.spyglass.stop_using": "Zooming Glass withdraws", "subtitles.item.spyglass.use": "Zooming Glass unfolds", "subtitles.item.totem.use": "Token astirs", "subtitles.item.trident.hit": "Le<PERSON> strikes", "subtitles.item.trident.hit_ground": "<PERSON><PERSON> shivers", "subtitles.item.trident.return": "<PERSON><PERSON> comes back", "subtitles.item.trident.riptide": "Leister zooms", "subtitles.item.trident.throw": "Leister clangs", "subtitles.item.trident.thunder": "Leister thunder cracks", "subtitles.item.wolf_armor.break": "<PERSON> breaks", "subtitles.item.wolf_armor.crack": "<PERSON>ing cracks", "subtitles.item.wolf_armor.damage": "<PERSON> is harmed", "subtitles.item.wolf_armor.repair": "<PERSON> is fettled", "subtitles.particle.soul_escape": "Soul atwinds", "subtitles.ui.cartography_table.take_result": "Draught drawn", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Loom brooked", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON> brooked", "subtitles.weather.rain": "Rain falls", "symlink_warning.message": "Loading worlds from folders with markish links can be unshielded if you don't know what you are doing. Kindly go to %s to learn more.", "symlink_warning.message.pack": "Loading packs with markish links can be unshielded if you don't know what you are doing. Kindly go to %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with markish links can be unshielded if you don't know what you are doing. Kindly go to %s to learn more.", "symlink_warning.more_info": "More Abreasting", "symlink_warning.title": "World folder inholds markish links", "symlink_warning.title.pack": "Eked pack(s) inhold(s) markish links", "symlink_warning.title.world": "The world folder inholds markish links", "team.collision.always": "Always", "team.collision.never": "Never", "team.collision.pushOtherTeams": "Thrutch other teams", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON>ch own team", "team.notFound": "Unknown team '%s", "team.visibility.always": "Always", "team.visibility.hideForOtherTeams": "Hide for other teams", "team.visibility.hideForOwnTeam": "Hide for own team", "team.visibility.never": "Never", "telemetry.event.advancement_made.description": "Understanding the framework behind reaping a forthstep can help us better understand and better the layer of the game.", "telemetry.event.advancement_made.title": "Forthstep Made", "telemetry.event.game_load_times.description": "This befalling can help us find out where startup working betterings are needed by metering the running times of the startup layers.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (<PERSON><PERSON><PERSON>)", "telemetry.event.optional.disabled": "%s (<PERSON><PERSON><PERSON>) - Laid off", "telemetry.event.performance_metrics.description": "Knowing the overall working selfleaf of Minecraft helps us mend and besten the game for many kinds of reckoners and working layouts. \nGame wharve is inned to help us bemete the working selfleaf for new wharves of Minecraft.", "telemetry.event.performance_metrics.title": "Working Metekinds", "telemetry.event.required": "%s (Forneeded)", "telemetry.event.world_load_times.description": "It's key for us to understand how long it needs to fay a world, and how that shifts over time. Say, when we eke new marks or do greater skillful wends, we need to see what rine that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Wayset, software or outreckoner tweaked, and game wharve) allows us to highlight game anwardenings to better the things that players care about most.\nThe World Loaded befalling is put with the World Unloaded befalling to work out how long the play besitting has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This befalling is put with the World Loaded befalling to work out how long the world besitting has lasted.\nThe time (in braids and ticks) is meted when a world besitting has ended (yielding to main shirm, unbinding from an outreckoner).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Forthstep IHOOD", "telemetry.property.client_id.title": "Software IHOOD", "telemetry.property.client_modded.title": "Software Tweaked", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON> (thousands of eightclefts)", "telemetry.property.event_timestamp_utc.title": "Befalling Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame <PERSON> (FAB)", "telemetry.property.game_mode.title": "Game Wayset", "telemetry.property.game_version.title": "Game Wharve", "telemetry.property.launcher_name.title": "Starter Name", "telemetry.property.load_time_bootstrap_ms.title": "Startup Time (Thousandths of a Braid)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Shirm (Thousandths of a Braid)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Eyethurl Opens (Thousandths of a Braid)", "telemetry.property.load_time_total_time_ms.title": "Overall Load Time (Thousandths of a Braid)", "telemetry.property.minecraft_session_id.title": "Minecraft Besitting IHOOD", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Byspel Rime", "telemetry.property.operating_system.title": "Working Layout", "telemetry.property.opt_in.title": "<PERSON><PERSON>-<PERSON>", "telemetry.property.platform.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.realms_map_content.title": "Realms Draught Inholding (Smallgame Name)", "telemetry.property.render_distance.title": "Draw Farness", "telemetry.property.render_time_samples.title": "Drawing Time Byspels", "telemetry.property.seconds_since_load.title": "Time Since Load (Braids)", "telemetry.property.server_modded.title": "Outreckoner Tweaked", "telemetry.property.server_type.title": "Outreckoner Kind", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON>ly Reached Inmind", "telemetry.property.user_id.title": "Brooker IHOOD", "telemetry.property.world_load_time_ms.title": "World Load Time (Thousandths of a Braid)", "telemetry.property.world_session_id.title": "World Besitting IHOOD", "telemetry_info.button.give_feedback": "<PERSON><PERSON>", "telemetry_info.button.privacy_statement": "Sunderly Uttering", "telemetry_info.button.show_data": "Show My Lore", "telemetry_info.opt_in.description": "I thave to sending kirely farmeting lore", "telemetry_info.property_title": "Inned Lore", "telemetry_info.screen.description": "Gathering this lore helps us besten Minecraft by showing our headings that have a bearing on our players.\nYou can also send in more of a feedback to help us keep bestening Minecraft.", "telemetry_info.screen.title": "Farmeting Lore Gathering", "test.error.block_property_mismatch": "Abode holding %s to be %s, was %s", "test.error.block_property_missing": "Clot holding missing, abode holding %s to be %s", "test.error.entity_property": "Ansen %s trucked fand: %s", "test.error.entity_property_details": "Ansen %s trucked fand: %s, abode: %s, was: %s", "test.error.expected_block": "Abode clot %s, reaped %s", "test.error.expected_block_tag": "Abode clot in #%s, reaped %s", "test.error.expected_container_contents": "Inholder should hold: %s", "test.error.expected_container_contents_single": "Inholder should hold one: %s", "test.error.expected_empty_container": "Inholder should be empty", "test.error.expected_entity": "Abode %s", "test.error.expected_entity_around": "Abode %s to bestand umb %s, %s, %s", "test.error.expected_entity_count": "Abode %s ansens of kind %s, found %s", "test.error.expected_entity_data": "Abode ansen lore to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Ansen lore mismatch for: %s", "test.error.expected_entity_effect": "Abode %s to have rine %s %s", "test.error.expected_entity_having": "<PERSON><PERSON> inholding should hold %s", "test.error.expected_entity_holding": "<PERSON><PERSON> should be holding %s", "test.error.expected_entity_in_test": "Abode %s to bestand in fand", "test.error.expected_entity_not_touching": "Did not ween %s rining %s, %s, %s (kin: %s, %s, %s)", "test.error.expected_entity_touching": "Abode %s rining %s, %s, %s (kin: %s, %s, %s)", "test.error.expected_item": "Abode thing of kind %s", "test.error.expected_items_count": "Abode %s things of kind %s, found %s", "test.error.fail": "Truck tharves met", "test.error.invalid_block_type": "Unweened clot kind found: %s", "test.error.missing_block_entity": "Missing clot ansen", "test.error.position": "%s at %s, %s, %s (kin: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Tharf already triggered at %s", "test.error.sequence.condition_not_triggered": "Tharf not triggered", "test.error.sequence.invalid_tick": "Sped in unright tick: abode %s", "test.error.sequence.not_completed": "<PERSON><PERSON> timed out before string fuldone", "test.error.set_biome": "Could not set lifeheap for fand", "test.error.spawn_failure": "Could not make ansen %s", "test.error.state_not_equal": "Wrong hoad. Abode %s, was %s", "test.error.structure.failure": "Could not lay down fand framework for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking fand before laying down framework", "test.error.timeout.no_result": "Didn't speed or truck within %s ticks", "test.error.timeout.no_sequences_finished": "No strings done within %s ticks", "test.error.too_many_entities": "Abode only one %s to bestand umb %s, %s, %s but found %s", "test.error.unexpected_block": "Did not ween clot to be %s", "test.error.unexpected_entity": "Did not ween %s to bestand", "test.error.unexpected_item": "Did not ween thing of kind %s", "test.error.unknown": "Unknown inward dwale: %s", "test.error.value_not_equal": "Abode %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong clot ansen kind: %s", "test_block.error.missing": "Fand framework missing %s clot", "test_block.error.too_many": "Too many %s clots", "test_block.invalid_timeout": "Unright timeout (%s) - must be an overnaught rime of ticks", "test_block.message": "Writ:", "test_block.mode.accept": "Bear", "test_block.mode.fail": "Truck", "test_block.mode.log": "Daybook", "test_block.mode.start": "Start", "test_block.mode_info.accept": "<PERSON> - Bear speed for (deal of) a fand", "test_block.mode_info.fail": "Truck Wayset - Truck the fand", "test_block.mode_info.log": "Daybook Wayset - Write down a writ", "test_block.mode_info.start": "Start Wayset - The starting ord for a fand", "test_instance.action.reset": "Edset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Keep Framework", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Trucked: %s", "test_instance.description.function": "Working: %s", "test_instance.description.invalid_id": "Unright fand IHOOD", "test_instance.description.no_test": "No such fand", "test_instance.description.structure": "Framework: %s", "test_instance.description.type": "Kind: %s", "test_instance.type.block_based": "Clot-Grounded Fand", "test_instance.type.function": "Built-in Working Fand", "test_instance_block.entities": "Ansen<PERSON>:", "test_instance_block.error.no_test": "Could not run fand befalling at %s, %s, %s since it has an unmarked fand", "test_instance_block.error.no_test_structure": "Could not run fand befalling at %s, %s, %s since it has no fand framework", "test_instance_block.error.unable_to_save": "Could not keep fand framework forelay for fand befalling at %s, %s, %s", "test_instance_block.invalid": "[unright]", "test_instance_block.reset_success": "Edset sped for fand: %s", "test_instance_block.rotation": "Wharving:", "test_instance_block.size": "Fand Framework Breadth", "test_instance_block.starting": "Starting fand %s", "test_instance_block.test_id": "Fand Befalling IHOOD", "title.32bit.deprecation": "32-cleft layout found: this may forestall you from playing in the tocome as a 64-cleft layout will be foreneeded!", "title.32bit.deprecation.realms": "Minecraft will soon need a 64-cleft layout, which will forestall you from playing or brooking Realms on this sare. You will need to belay any Realms underwriting by hand.", "title.32bit.deprecation.realms.check": "Do not show this shirm ayen", "title.32bit.deprecation.realms.header": "32-cleft layout found", "title.credits": "© Mojang AB. Do not fordeal!", "title.multiplayer.disabled": "Maniplayer is off. Kindly look at your Microsoft reckoning settings.", "title.multiplayer.disabled.banned.name": "You must wend your name before you can play onweb", "title.multiplayer.disabled.banned.permanent": "Your reckoning is hung up from onweb play forevermore", "title.multiplayer.disabled.banned.temporary": "Your reckoning is hung up from onweb play for a while", "title.multiplayer.lan": "Maniplayer (NSN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON> (3rd-team Outreckoner)", "title.multiplayer.realms": "Maniplayer (Realms)", "title.singleplayer": "Oneplayer", "translation.test.args": "%s %s", "translation.test.complex": "Forefastening, %s%2$s ayen %s and %1$s lastly %s and also %1$s ayen!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %s", "translation.test.none": "Hello, world!", "translation.test.world": "world", "trim_material.minecraft.amethyst": "Drunklack Anwork", "trim_material.minecraft.copper": "Are Anwork", "trim_material.minecraft.diamond": "Hardhirst Anwork", "trim_material.minecraft.emerald": "Greenhirst Anwork", "trim_material.minecraft.gold": "Gold Anwork", "trim_material.minecraft.iron": "Iron Anwork", "trim_material.minecraft.lapis": "Hewnstone Anwork", "trim_material.minecraft.netherite": "Netherite Anwork", "trim_material.minecraft.quartz": "Hardstone Anwork", "trim_material.minecraft.redstone": "Redstone Anwork", "trim_material.minecraft.resin": "<PERSON>", "trim_pattern.minecraft.bolt": "<PERSON><PERSON>", "trim_pattern.minecraft.coast": "<PERSON> Hirsting Trim", "trim_pattern.minecraft.dune": "Sandhill Hirsting Trim", "trim_pattern.minecraft.eye": "<PERSON> Hirsting Trim", "trim_pattern.minecraft.flow": "Flow Hirsting Trim", "trim_pattern.minecraft.host": "Dright Hirsting <PERSON>m", "trim_pattern.minecraft.raiser": "Raiser Hirsting <PERSON>", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Watchman <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "Stillness Hirsting <PERSON>m", "trim_pattern.minecraft.snout": "Swinenose Hirsting Trim", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "<PERSON> Hirsting Trim", "trim_pattern.minecraft.vex": "Net<PERSON> Hirsting Trim", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Hirsting Trim", "trim_pattern.minecraft.wild": "<PERSON>rst<PERSON>m", "tutorial.bundleInsert.description": "Right Click to eke things", "tutorial.bundleInsert.title": "Brook a Bundle", "tutorial.craft_planks.description": "The knowledgebook can help", "tutorial.craft_planks.title": "Craft wooden boards", "tutorial.find_tree.description": "Strike it to gather wood", "tutorial.find_tree.title": "Find a tree", "tutorial.look.description": "Brook your mouse to wharve", "tutorial.look.title": "Look umb", "tutorial.move.description": "Leap with %s", "tutorial.move.title": "Shrithe with %s, %s, %s and %s", "tutorial.open_inventory.description": "Thring %s", "tutorial.open_inventory.title": "Open your inholding", "tutorial.punch_tree.description": "Hold down %s", "tutorial.punch_tree.title": "Break down the tree", "tutorial.socialInteractions.description": "Thring %s to open", "tutorial.socialInteractions.title": "<PERSON><PERSON>", "upgrade.minecraft.netherite_upgrade": "Netherite Bolster"}