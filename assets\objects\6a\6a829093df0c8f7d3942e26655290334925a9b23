{"accessibility.onboarding.accessibility.button": "辅助功能设置…", "accessibility.onboarding.screen.narrator": "按下Enter键启用复述功能", "accessibility.onboarding.screen.title": "欢迎进入Minecraft！\n\n需要启用复述功能或访问辅助功能设置吗？", "addServer.add": "完成", "addServer.enterIp": "服务器地址", "addServer.enterName": "服务器名称", "addServer.resourcePack": "服务器资源包", "addServer.resourcePack.disabled": "禁用", "addServer.resourcePack.enabled": "启用", "addServer.resourcePack.prompt": "询问", "addServer.title": "编辑服务器信息", "advMode.command": "控制台命令", "advMode.mode": "模式", "advMode.mode.auto": "循环", "advMode.mode.autoexec.bat": "保持开启", "advMode.mode.conditional": "条件制约", "advMode.mode.redstone": "脉冲", "advMode.mode.redstoneTriggered": "红石控制", "advMode.mode.sequence": "连锁", "advMode.mode.unconditional": "不受制约", "advMode.notAllowed": "必须是处于创造模式的管理员", "advMode.notEnabled": "此服务器未启用命令方块", "advMode.previousOutput": "上一个输出", "advMode.setCommand": "设置此方块的控制台命令", "advMode.setCommand.success": "成功设置命令：%s", "advMode.trackOutput": "记录输出", "advMode.triggering": "触发方式", "advMode.type": "类型", "advancement.advancementNotFound": "未知的进度：%s", "advancements.adventure.adventuring_time.description": "发现所有的生物群系", "advancements.adventure.adventuring_time.title": "探索的时光", "advancements.adventure.arbalistic.description": "用弩一发击杀五只不同的生物", "advancements.adventure.arbalistic.title": "劲弩手", "advancements.adventure.avoid_vibration.description": "在幽匿感测体或监守者周围潜行以防被它们探测到", "advancements.adventure.avoid_vibration.title": "潜行100级", "advancements.adventure.blowback.description": "反弹旋风人的风弹来击杀旋风人", "advancements.adventure.blowback.title": "逆风翻盘", "advancements.adventure.brush_armadillo.description": "用刷子从犰狳身上获得犰狳鳞甲", "advancements.adventure.brush_armadillo.title": "这不是鳞甲么？", "advancements.adventure.bullseye.description": "从至少30米外射中标靶的靶心", "advancements.adventure.bullseye.title": "正中靶心", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "用4个陶片制作饰纹陶罐", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "精修细补", "advancements.adventure.crafters_crafting_crafters.description": "靠近一个正在合成合成器的合成器", "advancements.adventure.crafters_crafting_crafters.title": "合成器合成合成器", "advancements.adventure.fall_from_world_height.description": "从世界顶部（建筑高度限制处）自由落体，坠至世界底部并存活下来", "advancements.adventure.fall_from_world_height.title": "上天入地", "advancements.adventure.heart_transplanter.description": "将嘎枝之心以正确朝向放在两个苍白橡木原木方块中间", "advancements.adventure.heart_transplanter.title": "移心接木", "advancements.adventure.hero_of_the_village.description": "成功在袭击中保卫村庄", "advancements.adventure.hero_of_the_village.title": "村庄英雄", "advancements.adventure.honey_block_slide.description": "跳入蜂蜜块以缓冲摔落", "advancements.adventure.honey_block_slide.title": "胶着状态", "advancements.adventure.kill_a_mob.description": "杀死任意敌对性怪物", "advancements.adventure.kill_a_mob.title": "怪物猎人", "advancements.adventure.kill_all_mobs.description": "杀死每一种敌对性怪物", "advancements.adventure.kill_all_mobs.title": "资深怪物猎人", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "在幽匿催发体附近杀死生物", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "它蔓延了", "advancements.adventure.lighten_up.description": "用斧刮削铜灯来让它变得更亮", "advancements.adventure.lighten_up.title": "铜光焕发", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "在不引发火灾的前提下保护村民免受雷击", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "电涌保护器", "advancements.adventure.minecraft_trials_edition.description": "踏入试炼密室", "advancements.adventure.minecraft_trials_edition.title": "Minecraft：试炼版", "advancements.adventure.ol_betsy.description": "用弩进行一次射击", "advancements.adventure.ol_betsy.title": "扣下悬刀", "advancements.adventure.overoverkill.description": "使用重锤一击造成50颗心的伤害", "advancements.adventure.overoverkill.title": "天赐良击", "advancements.adventure.play_jukebox_in_meadows.description": "用唱片机的音乐声为草甸增添生机", "advancements.adventure.play_jukebox_in_meadows.title": "音乐之声", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "使用红石比较器获取雕纹书架的信号强度", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "知识就是力量", "advancements.adventure.revaulting.description": "用不祥试炼钥匙解锁不祥宝库", "advancements.adventure.revaulting.title": "宝经磨炼", "advancements.adventure.root.description": "冒险、探索与战斗", "advancements.adventure.root.title": "冒险", "advancements.adventure.salvage_sherd.description": "刷扫可疑的方块来获得陶片", "advancements.adventure.salvage_sherd.title": "探古寻源", "advancements.adventure.shoot_arrow.description": "用弓箭射点什么", "advancements.adventure.shoot_arrow.title": "瞄准目标", "advancements.adventure.sleep_in_bed.description": "在床上睡觉以改变你的重生点", "advancements.adventure.sleep_in_bed.title": "甜蜜的梦", "advancements.adventure.sniper_duel.description": "从至少50米外射杀一只骷髅", "advancements.adventure.sniper_duel.title": "狙击手的对决", "advancements.adventure.spyglass_at_dragon.description": "透过望远镜观察末影龙", "advancements.adventure.spyglass_at_dragon.title": "那是飞机吗？", "advancements.adventure.spyglass_at_ghast.description": "透过望远镜观察恶魂", "advancements.adventure.spyglass_at_ghast.title": "那是气球吗？", "advancements.adventure.spyglass_at_parrot.description": "透过望远镜观察鹦鹉", "advancements.adventure.spyglass_at_parrot.title": "那是鸟吗？", "advancements.adventure.summon_iron_golem.description": "召唤一只铁傀儡来帮忙守卫村庄", "advancements.adventure.summon_iron_golem.title": "招募援兵", "advancements.adventure.throw_trident.description": "往什么东西扔出三叉戟。\n注：别把你唯一的武器也抖掉了。", "advancements.adventure.throw_trident.title": "抖包袱", "advancements.adventure.totem_of_undying.description": "利用不死图腾逃离死神", "advancements.adventure.totem_of_undying.title": "超越生死", "advancements.adventure.trade.description": "成功与一名村民进行交易", "advancements.adventure.trade.title": "成交！", "advancements.adventure.trade_at_world_height.description": "在建筑高度限制处与村民交易", "advancements.adventure.trade_at_world_height.title": "星际商人", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "将下列锻造模板都至少使用一次：尖塔、猪鼻、肋骨、监守、幽静、恼鬼、潮汐、向导", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "匠心独具", "advancements.adventure.trim_with_any_armor_pattern.description": "在锻造台中合成带有纹饰的盔甲", "advancements.adventure.trim_with_any_armor_pattern.title": "旧貌锻新颜", "advancements.adventure.two_birds_one_arrow.description": "用一支穿透箭射杀两只幻翼", "advancements.adventure.two_birds_one_arrow.title": "一箭双雕", "advancements.adventure.under_lock_and_key.description": "用试炼钥匙解锁宝库", "advancements.adventure.under_lock_and_key.title": "珍藏密敛", "advancements.adventure.use_lodestone.description": "对着磁石使用指南针", "advancements.adventure.use_lodestone.title": "天涯共此石", "advancements.adventure.very_very_frightening.description": "雷击一名村民", "advancements.adventure.very_very_frightening.title": "魔女审判", "advancements.adventure.voluntary_exile.description": "杀死一名袭击队长。\n或许该考虑暂时远离村庄……", "advancements.adventure.voluntary_exile.title": "自我放逐", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "在细雪上行走……并且不陷进去", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "轻功雪上飘", "advancements.adventure.who_needs_rockets.description": "使用风弹将自己向上弹射8格", "advancements.adventure.who_needs_rockets.title": "还要啥火箭啊？", "advancements.adventure.whos_the_pillager_now.description": "让掠夺者也尝尝弩的滋味", "advancements.adventure.whos_the_pillager_now.title": "现在谁才是掠夺者？", "advancements.empty": "这里好像什么都没有……", "advancements.end.dragon_breath.description": "用玻璃瓶收集一些龙息", "advancements.end.dragon_breath.title": "你需要来点薄荷糖", "advancements.end.dragon_egg.description": "获得龙蛋", "advancements.end.dragon_egg.title": "下一世代", "advancements.end.elytra.description": "找到鞘翅", "advancements.end.elytra.title": "天空即为极限", "advancements.end.enter_end_gateway.description": "逃离这座岛屿", "advancements.end.enter_end_gateway.title": "远程折跃", "advancements.end.find_end_city.description": "进去吧，又能发生什么呢？", "advancements.end.find_end_city.title": "在游戏尽头的城市", "advancements.end.kill_dragon.description": "祝君好运", "advancements.end.kill_dragon.title": "解放末地", "advancements.end.levitate.description": "利用潜影贝的攻击向上飘浮50个方块", "advancements.end.levitate.title": "这上面的风景不错", "advancements.end.respawn_dragon.description": "复活末影龙", "advancements.end.respawn_dragon.title": "结束了…再一次…", "advancements.end.root.description": "抑或是起点？", "advancements.end.root.title": "末地", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "让悦灵向音符盒投掷一块蛋糕", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "生日快乐歌", "advancements.husbandry.allay_deliver_item_to_player.description": "让悦灵向你投掷物品", "advancements.husbandry.allay_deliver_item_to_player.title": "找到一个好朋友", "advancements.husbandry.axolotl_in_a_bucket.description": "用铁桶捕获一只美西螈", "advancements.husbandry.axolotl_in_a_bucket.title": "最萌捕食者", "advancements.husbandry.balanced_diet.description": "尝遍天下食材，即便是对身体不好的", "advancements.husbandry.balanced_diet.title": "均衡饮食", "advancements.husbandry.breed_all_animals.description": "繁殖每种动物！", "advancements.husbandry.breed_all_animals.title": "成双成对", "advancements.husbandry.breed_an_animal.description": "繁殖一对动物", "advancements.husbandry.breed_an_animal.title": "我从哪儿来？", "advancements.husbandry.complete_catalogue.description": "驯服所有种类的猫！", "advancements.husbandry.complete_catalogue.title": "百猫全书", "advancements.husbandry.feed_snifflet.description": "喂食一只小嗅探兽", "advancements.husbandry.feed_snifflet.title": "小小嗅探兽", "advancements.husbandry.fishy_business.description": "钓到一条鱼", "advancements.husbandry.fishy_business.title": "腥味十足的生意", "advancements.husbandry.froglights.description": "在你的物品栏中集齐所有种类的蛙明灯", "advancements.husbandry.froglights.title": "相映生辉！", "advancements.husbandry.kill_axolotl_target.description": "与美西螈并肩作战并赢得胜利", "advancements.husbandry.kill_axolotl_target.title": "友谊的治愈力！", "advancements.husbandry.leash_all_frog_variants.description": "用拴绳拴住所有种类的青蛙", "advancements.husbandry.leash_all_frog_variants.title": "呱呱队出动", "advancements.husbandry.make_a_sign_glow.description": "让任意种类告示牌上的文本发光", "advancements.husbandry.make_a_sign_glow.title": "眼前一亮！", "advancements.husbandry.netherite_hoe.description": "用下界合金锭升级一把锄，然后重新考虑你的人生抉择", "advancements.husbandry.netherite_hoe.title": "终极奉献", "advancements.husbandry.obtain_sniffer_egg.description": "获得嗅探兽蛋", "advancements.husbandry.obtain_sniffer_egg.title": "怪味蛋", "advancements.husbandry.place_dried_ghast_in_water.description": "将失水恶魂方块放在水中", "advancements.husbandry.place_dried_ghast_in_water.title": "补水保湿！", "advancements.husbandry.plant_any_sniffer_seed.description": "种植任意嗅探兽种子", "advancements.husbandry.plant_any_sniffer_seed.title": "播种往事", "advancements.husbandry.plant_seed.description": "种下种子，见证它的成长", "advancements.husbandry.plant_seed.title": "开荒垦地", "advancements.husbandry.remove_wolf_armor.description": "用剪刀移除狼身上的狼铠", "advancements.husbandry.remove_wolf_armor.title": "华丽一剪", "advancements.husbandry.repair_wolf_armor.description": "用犰狳鳞甲修复损坏的狼铠", "advancements.husbandry.repair_wolf_armor.title": "完好如初", "advancements.husbandry.ride_a_boat_with_a_goat.description": "与山羊同船共渡", "advancements.husbandry.ride_a_boat_with_a_goat.title": "羊帆起航！", "advancements.husbandry.root.description": "世界无处没有朋友与美食", "advancements.husbandry.root.title": "农牧业", "advancements.husbandry.safely_harvest_honey.description": "利用营火在不惊动蜜蜂的情况下从蜂巢收集蜂蜜", "advancements.husbandry.safely_harvest_honey.title": "与蜂共舞", "advancements.husbandry.silk_touch_nest.description": "用精准采集移动住着3只蜜蜂的蜂巢或蜂箱", "advancements.husbandry.silk_touch_nest.title": "举巢搬迁", "advancements.husbandry.tactical_fishing.description": "不用钓鱼竿抓住一条鱼！", "advancements.husbandry.tactical_fishing.title": "战术性钓鱼", "advancements.husbandry.tadpole_in_a_bucket.description": "用铁桶捕获一只蝌蚪", "advancements.husbandry.tadpole_in_a_bucket.title": "蚪到桶里来", "advancements.husbandry.tame_an_animal.description": "驯服一只动物", "advancements.husbandry.tame_an_animal.title": "永恒的伙伴", "advancements.husbandry.wax_off.description": "给铜质方块脱蜡！", "advancements.husbandry.wax_off.title": "脱蜡", "advancements.husbandry.wax_on.description": "将蜜脾涂到铜质方块上！", "advancements.husbandry.wax_on.title": "涂蜡", "advancements.husbandry.whole_pack.description": "驯服所有种类的狼", "advancements.husbandry.whole_pack.title": "群狼聚首", "advancements.nether.all_effects.description": "同时拥有所有状态效果", "advancements.nether.all_effects.title": "为什么会变成这样呢？", "advancements.nether.all_potions.description": "同时拥有所有药水效果", "advancements.nether.all_potions.title": "狂乱的鸡尾酒", "advancements.nether.brew_potion.description": "酿造一瓶药水", "advancements.nether.brew_potion.title": "本地酿造厂", "advancements.nether.charge_respawn_anchor.description": "为重生锚充满能量", "advancements.nether.charge_respawn_anchor.title": "锚没有九条命", "advancements.nether.create_beacon.description": "建造并放置一个信标", "advancements.nether.create_beacon.title": "带信标回家", "advancements.nether.create_full_beacon.description": "让一座信标发挥最大功效", "advancements.nether.create_full_beacon.title": "信标工程师", "advancements.nether.distract_piglin.description": "用金质物品让猪灵分神", "advancements.nether.distract_piglin.title": "金光闪闪", "advancements.nether.explore_nether.description": "探索所有下界生物群系", "advancements.nether.explore_nether.title": "热门景点", "advancements.nether.fast_travel.description": "利用下界移动对应主世界7千米的距离", "advancements.nether.fast_travel.title": "曲速泡", "advancements.nether.find_bastion.description": "进入堡垒遗迹", "advancements.nether.find_bastion.title": "光辉岁月", "advancements.nether.find_fortress.description": "用你的方式进入下界要塞", "advancements.nether.find_fortress.title": "阴森的要塞", "advancements.nether.get_wither_skull.description": "获得凋灵骷髅的头颅", "advancements.nether.get_wither_skull.title": "惊悚恐怖骷髅头", "advancements.nether.loot_bastion.description": "掠夺堡垒遗迹里的箱子", "advancements.nether.loot_bastion.title": "战猪", "advancements.nether.netherite_armor.description": "获得一整套下界合金盔甲", "advancements.nether.netherite_armor.title": "残骸裹身", "advancements.nether.obtain_ancient_debris.description": "获得远古残骸", "advancements.nether.obtain_ancient_debris.title": "深藏不露", "advancements.nether.obtain_blaze_rod.description": "让烈焰人从烈焰棒中解放吧", "advancements.nether.obtain_blaze_rod.title": "与火共舞", "advancements.nether.obtain_crying_obsidian.description": "获得哭泣的黑曜石", "advancements.nether.obtain_crying_obsidian.title": "谁在切洋葱？", "advancements.nether.return_to_sender.description": "用一团火球干掉一只恶魂", "advancements.nether.return_to_sender.title": "见鬼去吧", "advancements.nether.ride_strider.description": "手持诡异菌钓竿骑乘炽足兽", "advancements.nether.ride_strider.title": "画船添足", "advancements.nether.ride_strider_in_overworld_lava.description": "带炽足兽在主世界的熔岩湖上来一场长——途旅行", "advancements.nether.ride_strider_in_overworld_lava.title": "温暖如家", "advancements.nether.root.description": "记得带夏装", "advancements.nether.root.title": "下界", "advancements.nether.summon_wither.description": "召唤凋灵", "advancements.nether.summon_wither.title": "凋零山庄", "advancements.nether.uneasy_alliance.description": "从下界救出一只恶魂，将其安全地带到主世界……然后干掉它", "advancements.nether.uneasy_alliance.title": "脆弱的同盟", "advancements.nether.use_lodestone.description": "对着磁石使用指南针", "advancements.nether.use_lodestone.title": "天涯共此石", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "弱化并治疗一名僵尸村民", "advancements.story.cure_zombie_villager.title": "僵尸科医生", "advancements.story.deflect_arrow.description": "用盾牌反弹一个弹射物", "advancements.story.deflect_arrow.title": "不吃这套，谢谢", "advancements.story.enchant_item.description": "用附魔台附魔一样物品", "advancements.story.enchant_item.title": "附魔师", "advancements.story.enter_the_end.description": "进入末地传送门", "advancements.story.enter_the_end.title": "结束了？", "advancements.story.enter_the_nether.description": "建造、激活并进入一座下界传送门", "advancements.story.enter_the_nether.title": "勇往直下", "advancements.story.follow_ender_eye.description": "跟随末影之眼", "advancements.story.follow_ender_eye.title": "隔墙有眼", "advancements.story.form_obsidian.description": "获得一块黑曜石", "advancements.story.form_obsidian.title": "冰桶挑战", "advancements.story.iron_tools.description": "升级你的镐", "advancements.story.iron_tools.title": "这不是铁镐么", "advancements.story.lava_bucket.description": "用铁桶装点熔岩", "advancements.story.lava_bucket.title": "热腾腾的", "advancements.story.mine_diamond.description": "获得钻石", "advancements.story.mine_diamond.title": "钻石！", "advancements.story.mine_stone.description": "用你的新镐挖掘石头", "advancements.story.mine_stone.title": "石器时代", "advancements.story.obtain_armor.description": "用铁盔甲来保护你自己", "advancements.story.obtain_armor.title": "整装上阵", "advancements.story.root.description": "游戏的核心与故事", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "钻石盔甲能救人", "advancements.story.shiny_gear.title": "钻石护体", "advancements.story.smelt_iron.description": "冶炼出一块铁锭", "advancements.story.smelt_iron.title": "来硬的", "advancements.story.upgrade_tools.description": "制作一把更好的镐", "advancements.story.upgrade_tools.title": "获得升级", "advancements.toast.challenge": "挑战已完成！", "advancements.toast.goal": "目标已达成！", "advancements.toast.task": "进度已达成！", "argument.anchor.invalid": "无效的实体锚点%s", "argument.angle.incomplete": "不完整（应有1个角度）", "argument.angle.invalid": "无效的角度", "argument.block.id.invalid": "未知的方块类型“%s”", "argument.block.property.duplicate": "“%s”属性只能给%s设置一次", "argument.block.property.invalid": "%1$s的%3$s属性不能被设为“%2$s”", "argument.block.property.novalue": "方块%s上的属性“%s”必须要有值", "argument.block.property.unclosed": "方块属性应以]结束", "argument.block.property.unknown": "方块%s没有属性“%s”", "argument.block.tag.disallowed": "无法在此使用标签，只允许使用实际的方块", "argument.color.invalid": "未知的颜色“%s”", "argument.component.invalid": "无效的聊天组件： %s", "argument.criteria.invalid": "未知的准则“%s”", "argument.dimension.invalid": "未知的维度“%s”", "argument.double.big": "双精度浮点型数据不能大于%s，但发现了%s", "argument.double.low": "双精度浮点型数据不能小于%s，但发现了%s", "argument.entity.invalid": "无效的名称或UUID", "argument.entity.notfound.entity": "未找到实体", "argument.entity.notfound.player": "未找到玩家", "argument.entity.options.advancements.description": "玩家拥有的进度", "argument.entity.options.distance.description": "与实体间的距离", "argument.entity.options.distance.negative": "距离不能为负", "argument.entity.options.dx.description": "位于x与x+dx之间的实体", "argument.entity.options.dy.description": "位于y与y+dy之间的实体", "argument.entity.options.dz.description": "位于z与z+dz之间的实体", "argument.entity.options.gamemode.description": "玩家的游戏模式", "argument.entity.options.inapplicable": "“%s”选项不适用于这里", "argument.entity.options.level.description": "经验等级", "argument.entity.options.level.negative": "等级不应该为负数", "argument.entity.options.limit.description": "最大返回实体数", "argument.entity.options.limit.toosmall": "限制必须至少为1", "argument.entity.options.mode.invalid": "无效或未知的游戏模式“%s”", "argument.entity.options.name.description": "实体名称", "argument.entity.options.nbt.description": "实体所带的NBT", "argument.entity.options.predicate.description": "自定义谓词", "argument.entity.options.scores.description": "实体的分数", "argument.entity.options.sort.description": "对实体排序", "argument.entity.options.sort.irreversible": "无效或未知的排序类型“%s”", "argument.entity.options.tag.description": "实体所带的标签", "argument.entity.options.team.description": "实体所在的队伍", "argument.entity.options.type.description": "实体类型", "argument.entity.options.type.invalid": "无效或未知的实体类型“%s”", "argument.entity.options.unknown": "未知的选项“%s”", "argument.entity.options.unterminated": "选项的方括号不成对", "argument.entity.options.valueless": "选项“%s”应有值", "argument.entity.options.x.description": "X轴位置", "argument.entity.options.x_rotation.description": "实体的X轴旋转角度", "argument.entity.options.y.description": "Y轴位置", "argument.entity.options.y_rotation.description": "实体的Y轴旋转角度", "argument.entity.options.z.description": "Z轴位置", "argument.entity.selector.allEntities": "所有实体", "argument.entity.selector.allPlayers": "所有玩家", "argument.entity.selector.missing": "缺少选择器类型", "argument.entity.selector.nearestEntity": "距离最近的实体", "argument.entity.selector.nearestPlayer": "距离最近的玩家", "argument.entity.selector.not_allowed": "不能使用选择器", "argument.entity.selector.randomPlayer": "随机玩家", "argument.entity.selector.self": "当前实体", "argument.entity.selector.unknown": "未知的选择器类型“%s”", "argument.entity.toomany": "只允许一个实体，但提供的选择器允许多个实体", "argument.enum.invalid": "无效的值“%s”", "argument.float.big": "浮点型数据不能大于%s，但发现了%s", "argument.float.low": "浮点型数据不能小于%s，但发现了%s", "argument.gamemode.invalid": "未知的游戏模式：%s", "argument.hexcolor.invalid": "无效的十六进制颜色代码：“%s”", "argument.id.invalid": "无效的ID", "argument.id.unknown": "未知的ID：%s", "argument.integer.big": "整型数据不能大于%s，但发现了%s", "argument.integer.low": "整型数据不能小于%s，但发现了%s", "argument.item.id.invalid": "未知的物品“%s”", "argument.item.tag.disallowed": "无法在此使用标签，只允许使用实际的物品", "argument.literal.incorrect": "应为字面量%s", "argument.long.big": "长整型数据不能大于%s，但发现了%s", "argument.long.low": "长整型数据不能小于%s，但发现了%s", "argument.message.too_long": "聊天消息过长（%s大于上限%s个字符）", "argument.nbt.array.invalid": "无效的数组类型“%s”", "argument.nbt.array.mixed": "无法将%s插入%s", "argument.nbt.expected.compound": "应为复合标签", "argument.nbt.expected.key": "需要键", "argument.nbt.expected.value": "需要值", "argument.nbt.list.mixed": "无法将%s插入%s的列表", "argument.nbt.trailing": "多余的尾随数据", "argument.player.entities": "只有玩家会受此命令的影响，但提供的选择器包括其他实体", "argument.player.toomany": "只允许一名玩家，但提供的选择器允许多名玩家", "argument.player.unknown": "该玩家不存在", "argument.pos.missing.double": "应为一个坐标", "argument.pos.missing.int": "应为一个方块的位置", "argument.pos.mixed": "不能混用世界与局部坐标（必须全部用^或都不使用）", "argument.pos.outofbounds": "该位置超出了允许的范围。", "argument.pos.outofworld": "该位置已超出此世界！", "argument.pos.unloaded": "该位置尚未被加载", "argument.pos2d.incomplete": "不完整（应有2个坐标）", "argument.pos3d.incomplete": "不完整（应有3个坐标）", "argument.range.empty": "应为值或取值范围", "argument.range.ints": "只允许整数，不允许小数", "argument.range.swapped": "最小值不能大于最大值", "argument.resource.invalid_type": "元素“%s”的类型“%s”错误（应为“%s”）", "argument.resource.not_found": "无法找到类型为“%2$s”的元素“%1$s”", "argument.resource_or_id.failed_to_parse": "解析结构失败：%s", "argument.resource_or_id.invalid": "无效的ID或标签", "argument.resource_or_id.no_such_element": "无法在注册表“%2$s”中找到元素“%1$s”", "argument.resource_selector.not_found": "选择器“%s”和类型“%s”没有匹配项", "argument.resource_tag.invalid_type": "标签“%s”的类型“%s”错误（应为“%s”）", "argument.resource_tag.not_found": "无法找到类型为“%2$s”的标签“%1$s”", "argument.rotation.incomplete": "不完整（应有2个坐标）", "argument.scoreHolder.empty": "找不到与分数关联的持有者", "argument.scoreboardDisplaySlot.invalid": "未知的显示位置“%s”", "argument.style.invalid": "无效的样式：%s", "argument.time.invalid_tick_count": "刻数必须为非负数", "argument.time.invalid_unit": "无效的单位", "argument.time.tick_count_too_low": "刻数不能小于%s，却发现了%s", "argument.uuid.invalid": "无效的UUID", "argument.waypoint.invalid": "选择的实体不是路径点", "arguments.block.tag.unknown": "未知的方块标签“%s”", "arguments.function.tag.unknown": "未知的函数标签“%s”", "arguments.function.unknown": "未知的函数%s", "arguments.item.component.expected": "应为物品组件", "arguments.item.component.malformed": "错误的“%s”组件：“%s”", "arguments.item.component.repeated": "物品组件“%s”重复，只能指定一个值", "arguments.item.component.unknown": "未知的物品组件“%s”", "arguments.item.malformed": "错误的物品：“%s”", "arguments.item.overstacked": "%s只可以堆叠到%s", "arguments.item.predicate.malformed": "错误的“%s”谓词：“%s”", "arguments.item.predicate.unknown": "未知的物品谓词“%s”", "arguments.item.tag.unknown": "未知的物品标签“%s”", "arguments.nbtpath.node.invalid": "无效的NBT路径元素", "arguments.nbtpath.nothing_found": "没有与%s相匹配的元素", "arguments.nbtpath.too_deep": "生成的NBT嵌套过深", "arguments.nbtpath.too_large": "生成的NBT过大", "arguments.objective.notFound": "未知的记分项“%s”", "arguments.objective.readonly": "记分项“%s”为只读类型", "arguments.operation.div0": "不能除以零", "arguments.operation.invalid": "无效的操作", "arguments.swizzle.invalid": "无效的坐标组合，需要“x”、“y”和“z”的组合", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "护甲值", "attribute.name.armor_toughness": "盔甲韧性", "attribute.name.attack_damage": "攻击伤害", "attribute.name.attack_knockback": "击退", "attribute.name.attack_speed": "攻击速度", "attribute.name.block_break_speed": "方块破坏速度", "attribute.name.block_interaction_range": "方块交互距离", "attribute.name.burning_time": "着火时间", "attribute.name.camera_distance": "摄像机距离", "attribute.name.entity_interaction_range": "实体交互距离", "attribute.name.explosion_knockback_resistance": "爆炸击退抗性", "attribute.name.fall_damage_multiplier": "摔落伤害倍数", "attribute.name.flying_speed": "飞行速度", "attribute.name.follow_range": "生物跟随距离", "attribute.name.generic.armor": "护甲值", "attribute.name.generic.armor_toughness": "盔甲韧性", "attribute.name.generic.attack_damage": "攻击伤害", "attribute.name.generic.attack_knockback": "击退", "attribute.name.generic.attack_speed": "攻击速度", "attribute.name.generic.block_interaction_range": "方块交互距离", "attribute.name.generic.burning_time": "着火时间", "attribute.name.generic.entity_interaction_range": "实体交互距离", "attribute.name.generic.explosion_knockback_resistance": "爆炸击退抗性", "attribute.name.generic.fall_damage_multiplier": "摔落伤害倍数", "attribute.name.generic.flying_speed": "飞行速度", "attribute.name.generic.follow_range": "生物跟随距离", "attribute.name.generic.gravity": "重力", "attribute.name.generic.jump_strength": "跳跃力度", "attribute.name.generic.knockback_resistance": "击退抗性", "attribute.name.generic.luck": "幸运值", "attribute.name.generic.max_absorption": "最大伤害吸收值", "attribute.name.generic.max_health": "最大生命值", "attribute.name.generic.movement_efficiency": "移动效率", "attribute.name.generic.movement_speed": "速度", "attribute.name.generic.oxygen_bonus": "额外氧气", "attribute.name.generic.safe_fall_distance": "安全摔落高度", "attribute.name.generic.scale": "尺寸", "attribute.name.generic.step_height": "最大行走高度", "attribute.name.generic.water_movement_efficiency": "水中移动效率", "attribute.name.gravity": "重力", "attribute.name.horse.jump_strength": "马匹跳跃能力", "attribute.name.jump_strength": "跳跃力度", "attribute.name.knockback_resistance": "击退抗性", "attribute.name.luck": "幸运值", "attribute.name.max_absorption": "最大伤害吸收值", "attribute.name.max_health": "最大生命值", "attribute.name.mining_efficiency": "挖掘效率", "attribute.name.movement_efficiency": "移动效率", "attribute.name.movement_speed": "速度", "attribute.name.oxygen_bonus": "额外氧气", "attribute.name.player.block_break_speed": "方块破坏速度", "attribute.name.player.block_interaction_range": "方块交互距离", "attribute.name.player.entity_interaction_range": "实体交互距离", "attribute.name.player.mining_efficiency": "挖掘效率", "attribute.name.player.sneaking_speed": "潜行速度", "attribute.name.player.submerged_mining_speed": "水下挖掘速度", "attribute.name.player.sweeping_damage_ratio": "横扫伤害比率", "attribute.name.safe_fall_distance": "安全摔落高度", "attribute.name.scale": "尺寸", "attribute.name.sneaking_speed": "潜行速度", "attribute.name.spawn_reinforcements": "僵尸增援", "attribute.name.step_height": "最大行走高度", "attribute.name.submerged_mining_speed": "水下挖掘速度", "attribute.name.sweeping_damage_ratio": "横扫伤害比率", "attribute.name.tempt_range": "生物引诱范围", "attribute.name.water_movement_efficiency": "水中移动效率", "attribute.name.waypoint_receive_range": "路径点接收距离", "attribute.name.waypoint_transmit_range": "路径点传输距离", "attribute.name.zombie.spawn_reinforcements": "僵尸增援", "biome.minecraft.badlands": "恶地", "biome.minecraft.bamboo_jungle": "竹林", "biome.minecraft.basalt_deltas": "玄武岩三角洲", "biome.minecraft.beach": "沙滩", "biome.minecraft.birch_forest": "桦木森林", "biome.minecraft.cherry_grove": "樱花树林", "biome.minecraft.cold_ocean": "冷水海洋", "biome.minecraft.crimson_forest": "绯红森林", "biome.minecraft.dark_forest": "黑森林", "biome.minecraft.deep_cold_ocean": "冷水深海", "biome.minecraft.deep_dark": "深暗之域", "biome.minecraft.deep_frozen_ocean": "冰冻深海", "biome.minecraft.deep_lukewarm_ocean": "温水深海", "biome.minecraft.deep_ocean": "深海", "biome.minecraft.desert": "沙漠", "biome.minecraft.dripstone_caves": "溶洞", "biome.minecraft.end_barrens": "末地荒地", "biome.minecraft.end_highlands": "末地高地", "biome.minecraft.end_midlands": "末地内陆", "biome.minecraft.eroded_badlands": "风蚀恶地", "biome.minecraft.flower_forest": "繁花森林", "biome.minecraft.forest": "森林", "biome.minecraft.frozen_ocean": "冻洋", "biome.minecraft.frozen_peaks": "冰封山峰", "biome.minecraft.frozen_river": "冻河", "biome.minecraft.grove": "雪林", "biome.minecraft.ice_spikes": "冰刺之地", "biome.minecraft.jagged_peaks": "尖峭山峰", "biome.minecraft.jungle": "丛林", "biome.minecraft.lukewarm_ocean": "温水海洋", "biome.minecraft.lush_caves": "繁茂洞穴", "biome.minecraft.mangrove_swamp": "红树林沼泽", "biome.minecraft.meadow": "草甸", "biome.minecraft.mushroom_fields": "蘑菇岛", "biome.minecraft.nether_wastes": "下界荒地", "biome.minecraft.ocean": "海洋", "biome.minecraft.old_growth_birch_forest": "原始桦木森林", "biome.minecraft.old_growth_pine_taiga": "原始松木针叶林", "biome.minecraft.old_growth_spruce_taiga": "原始云杉针叶林", "biome.minecraft.pale_garden": "苍白之园", "biome.minecraft.plains": "平原", "biome.minecraft.river": "河流", "biome.minecraft.savanna": "热带草原", "biome.minecraft.savanna_plateau": "热带高原", "biome.minecraft.small_end_islands": "末地小型岛屿", "biome.minecraft.snowy_beach": "积雪沙滩", "biome.minecraft.snowy_plains": "雪原", "biome.minecraft.snowy_slopes": "积雪山坡", "biome.minecraft.snowy_taiga": "积雪针叶林", "biome.minecraft.soul_sand_valley": "灵魂沙峡谷", "biome.minecraft.sparse_jungle": "稀疏丛林", "biome.minecraft.stony_peaks": "裸岩山峰", "biome.minecraft.stony_shore": "石岸", "biome.minecraft.sunflower_plains": "向日葵平原", "biome.minecraft.swamp": "沼泽", "biome.minecraft.taiga": "针叶林", "biome.minecraft.the_end": "末地", "biome.minecraft.the_void": "虚空", "biome.minecraft.warm_ocean": "暖水海洋", "biome.minecraft.warped_forest": "诡异森林", "biome.minecraft.windswept_forest": "风袭森林", "biome.minecraft.windswept_gravelly_hills": "风袭沙砾丘陵", "biome.minecraft.windswept_hills": "风袭丘陵", "biome.minecraft.windswept_savanna": "风袭热带草原", "biome.minecraft.wooded_badlands": "疏林恶地", "block.minecraft.acacia_button": "金合欢木按钮", "block.minecraft.acacia_door": "金合欢木门", "block.minecraft.acacia_fence": "金合欢木栅栏", "block.minecraft.acacia_fence_gate": "金合欢木栅栏门", "block.minecraft.acacia_hanging_sign": "悬挂式金合欢木告示牌", "block.minecraft.acacia_leaves": "金合欢树叶", "block.minecraft.acacia_log": "金合欢原木", "block.minecraft.acacia_planks": "金合欢木板", "block.minecraft.acacia_pressure_plate": "金合欢木压力板", "block.minecraft.acacia_sapling": "金合欢树苗", "block.minecraft.acacia_sign": "金合欢木告示牌", "block.minecraft.acacia_slab": "金合欢木台阶", "block.minecraft.acacia_stairs": "金合欢木楼梯", "block.minecraft.acacia_trapdoor": "金合欢木活板门", "block.minecraft.acacia_wall_hanging_sign": "墙上的悬挂式金合欢木告示牌", "block.minecraft.acacia_wall_sign": "墙上的金合欢木告示牌", "block.minecraft.acacia_wood": "金合欢木", "block.minecraft.activator_rail": "激活铁轨", "block.minecraft.air": "空气", "block.minecraft.allium": "绒球葱", "block.minecraft.amethyst_block": "紫水晶块", "block.minecraft.amethyst_cluster": "紫水晶簇", "block.minecraft.ancient_debris": "远古残骸", "block.minecraft.andesite": "安山岩", "block.minecraft.andesite_slab": "安山岩台阶", "block.minecraft.andesite_stairs": "安山岩楼梯", "block.minecraft.andesite_wall": "安山岩墙", "block.minecraft.anvil": "铁砧", "block.minecraft.attached_melon_stem": "结果的西瓜茎", "block.minecraft.attached_pumpkin_stem": "结果的南瓜茎", "block.minecraft.azalea": "杜鹃花丛", "block.minecraft.azalea_leaves": "杜鹃树叶", "block.minecraft.azure_bluet": "蓝花美耳草", "block.minecraft.bamboo": "竹子", "block.minecraft.bamboo_block": "竹块", "block.minecraft.bamboo_button": "竹按钮", "block.minecraft.bamboo_door": "竹门", "block.minecraft.bamboo_fence": "竹栅栏", "block.minecraft.bamboo_fence_gate": "竹栅栏门", "block.minecraft.bamboo_hanging_sign": "悬挂式竹告示牌", "block.minecraft.bamboo_mosaic": "竹马赛克", "block.minecraft.bamboo_mosaic_slab": "竹马赛克台阶", "block.minecraft.bamboo_mosaic_stairs": "竹马赛克楼梯", "block.minecraft.bamboo_planks": "竹板", "block.minecraft.bamboo_pressure_plate": "竹压力板", "block.minecraft.bamboo_sapling": "竹笋", "block.minecraft.bamboo_sign": "竹告示牌", "block.minecraft.bamboo_slab": "竹台阶", "block.minecraft.bamboo_stairs": "竹楼梯", "block.minecraft.bamboo_trapdoor": "竹活板门", "block.minecraft.bamboo_wall_hanging_sign": "墙上的悬挂式竹告示牌", "block.minecraft.bamboo_wall_sign": "墙上的竹告示牌", "block.minecraft.banner.base.black": "黑底", "block.minecraft.banner.base.blue": "蓝底", "block.minecraft.banner.base.brown": "棕底", "block.minecraft.banner.base.cyan": "青底", "block.minecraft.banner.base.gray": "灰底", "block.minecraft.banner.base.green": "绿底", "block.minecraft.banner.base.light_blue": "淡蓝底", "block.minecraft.banner.base.light_gray": "淡灰底", "block.minecraft.banner.base.lime": "黄绿底", "block.minecraft.banner.base.magenta": "品红底", "block.minecraft.banner.base.orange": "橙底", "block.minecraft.banner.base.pink": "粉红底", "block.minecraft.banner.base.purple": "紫底", "block.minecraft.banner.base.red": "红底", "block.minecraft.banner.base.white": "白底", "block.minecraft.banner.base.yellow": "黄底", "block.minecraft.banner.border.black": "黑色方框边", "block.minecraft.banner.border.blue": "蓝色方框边", "block.minecraft.banner.border.brown": "棕色方框边", "block.minecraft.banner.border.cyan": "青色方框边", "block.minecraft.banner.border.gray": "灰色方框边", "block.minecraft.banner.border.green": "绿色方框边", "block.minecraft.banner.border.light_blue": "淡蓝色方框边", "block.minecraft.banner.border.light_gray": "淡灰色方框边", "block.minecraft.banner.border.lime": "黄绿色方框边", "block.minecraft.banner.border.magenta": "品红色方框边", "block.minecraft.banner.border.orange": "橙色方框边", "block.minecraft.banner.border.pink": "粉红色方框边", "block.minecraft.banner.border.purple": "紫色方框边", "block.minecraft.banner.border.red": "红色方框边", "block.minecraft.banner.border.white": "白色方框边", "block.minecraft.banner.border.yellow": "黄色方框边", "block.minecraft.banner.bricks.black": "黑色砖纹", "block.minecraft.banner.bricks.blue": "蓝色砖纹", "block.minecraft.banner.bricks.brown": "棕色砖纹", "block.minecraft.banner.bricks.cyan": "青色砖纹", "block.minecraft.banner.bricks.gray": "灰色砖纹", "block.minecraft.banner.bricks.green": "绿色砖纹", "block.minecraft.banner.bricks.light_blue": "淡蓝色砖纹", "block.minecraft.banner.bricks.light_gray": "淡灰色砖纹", "block.minecraft.banner.bricks.lime": "黄绿色砖纹", "block.minecraft.banner.bricks.magenta": "品红色砖纹", "block.minecraft.banner.bricks.orange": "橙色砖纹", "block.minecraft.banner.bricks.pink": "粉红色砖纹", "block.minecraft.banner.bricks.purple": "紫色砖纹", "block.minecraft.banner.bricks.red": "红色砖纹", "block.minecraft.banner.bricks.white": "白色砖纹", "block.minecraft.banner.bricks.yellow": "黄色砖纹", "block.minecraft.banner.circle.black": "黑色圆形", "block.minecraft.banner.circle.blue": "蓝色圆形", "block.minecraft.banner.circle.brown": "棕色圆形", "block.minecraft.banner.circle.cyan": "青色圆形", "block.minecraft.banner.circle.gray": "灰色圆形", "block.minecraft.banner.circle.green": "绿色圆形", "block.minecraft.banner.circle.light_blue": "淡蓝色圆形", "block.minecraft.banner.circle.light_gray": "淡灰色圆形", "block.minecraft.banner.circle.lime": "黄绿色圆形", "block.minecraft.banner.circle.magenta": "品红色圆形", "block.minecraft.banner.circle.orange": "橙色圆形", "block.minecraft.banner.circle.pink": "粉红色圆形", "block.minecraft.banner.circle.purple": "紫色圆形", "block.minecraft.banner.circle.red": "红色圆形", "block.minecraft.banner.circle.white": "白色圆形", "block.minecraft.banner.circle.yellow": "黄色圆形", "block.minecraft.banner.creeper.black": "黑色苦力怕盾徽", "block.minecraft.banner.creeper.blue": "蓝色苦力怕盾徽", "block.minecraft.banner.creeper.brown": "棕色苦力怕盾徽", "block.minecraft.banner.creeper.cyan": "青色苦力怕盾徽", "block.minecraft.banner.creeper.gray": "灰色苦力怕盾徽", "block.minecraft.banner.creeper.green": "绿色苦力怕盾徽", "block.minecraft.banner.creeper.light_blue": "淡蓝色苦力怕盾徽", "block.minecraft.banner.creeper.light_gray": "淡灰色苦力怕盾徽", "block.minecraft.banner.creeper.lime": "黄绿色苦力怕盾徽", "block.minecraft.banner.creeper.magenta": "品红色苦力怕盾徽", "block.minecraft.banner.creeper.orange": "橙色苦力怕盾徽", "block.minecraft.banner.creeper.pink": "粉红色苦力怕盾徽", "block.minecraft.banner.creeper.purple": "紫色苦力怕盾徽", "block.minecraft.banner.creeper.red": "红色苦力怕盾徽", "block.minecraft.banner.creeper.white": "白色苦力怕盾徽", "block.minecraft.banner.creeper.yellow": "黄色苦力怕盾徽", "block.minecraft.banner.cross.black": "黑斜十字", "block.minecraft.banner.cross.blue": "蓝斜十字", "block.minecraft.banner.cross.brown": "棕斜十字", "block.minecraft.banner.cross.cyan": "青斜十字", "block.minecraft.banner.cross.gray": "灰斜十字", "block.minecraft.banner.cross.green": "绿斜十字", "block.minecraft.banner.cross.light_blue": "淡蓝斜十字", "block.minecraft.banner.cross.light_gray": "淡灰斜十字", "block.minecraft.banner.cross.lime": "黄绿斜十字", "block.minecraft.banner.cross.magenta": "品红斜十字", "block.minecraft.banner.cross.orange": "橙斜十字", "block.minecraft.banner.cross.pink": "粉红斜十字", "block.minecraft.banner.cross.purple": "紫斜十字", "block.minecraft.banner.cross.red": "红斜十字", "block.minecraft.banner.cross.white": "白斜十字", "block.minecraft.banner.cross.yellow": "黄斜十字", "block.minecraft.banner.curly_border.black": "黑色波纹边", "block.minecraft.banner.curly_border.blue": "蓝色波纹边", "block.minecraft.banner.curly_border.brown": "棕色波纹边", "block.minecraft.banner.curly_border.cyan": "青色波纹边", "block.minecraft.banner.curly_border.gray": "灰色波纹边", "block.minecraft.banner.curly_border.green": "绿色波纹边", "block.minecraft.banner.curly_border.light_blue": "淡蓝色波纹边", "block.minecraft.banner.curly_border.light_gray": "淡灰色波纹边", "block.minecraft.banner.curly_border.lime": "黄绿色波纹边", "block.minecraft.banner.curly_border.magenta": "品红色波纹边", "block.minecraft.banner.curly_border.orange": "橙色波纹边", "block.minecraft.banner.curly_border.pink": "粉红色波纹边", "block.minecraft.banner.curly_border.purple": "紫色波纹边", "block.minecraft.banner.curly_border.red": "红色波纹边", "block.minecraft.banner.curly_border.white": "白色波纹边", "block.minecraft.banner.curly_border.yellow": "黄色波纹边", "block.minecraft.banner.diagonal_left.black": "黑色右上三角", "block.minecraft.banner.diagonal_left.blue": "蓝色右上三角", "block.minecraft.banner.diagonal_left.brown": "棕色右上三角", "block.minecraft.banner.diagonal_left.cyan": "青色右上三角", "block.minecraft.banner.diagonal_left.gray": "灰色右上三角", "block.minecraft.banner.diagonal_left.green": "绿色右上三角", "block.minecraft.banner.diagonal_left.light_blue": "淡蓝色右上三角", "block.minecraft.banner.diagonal_left.light_gray": "淡灰色右上三角", "block.minecraft.banner.diagonal_left.lime": "黄绿色右上三角", "block.minecraft.banner.diagonal_left.magenta": "品红色右上三角", "block.minecraft.banner.diagonal_left.orange": "橙色右上三角", "block.minecraft.banner.diagonal_left.pink": "粉红色右上三角", "block.minecraft.banner.diagonal_left.purple": "紫色右上三角", "block.minecraft.banner.diagonal_left.red": "红色右上三角", "block.minecraft.banner.diagonal_left.white": "白色右上三角", "block.minecraft.banner.diagonal_left.yellow": "黄色右上三角", "block.minecraft.banner.diagonal_right.black": "黑色左上三角", "block.minecraft.banner.diagonal_right.blue": "蓝色左上三角", "block.minecraft.banner.diagonal_right.brown": "棕色左上三角", "block.minecraft.banner.diagonal_right.cyan": "青色左上三角", "block.minecraft.banner.diagonal_right.gray": "灰色左上三角", "block.minecraft.banner.diagonal_right.green": "绿色左上三角", "block.minecraft.banner.diagonal_right.light_blue": "淡蓝色左上三角", "block.minecraft.banner.diagonal_right.light_gray": "淡灰色左上三角", "block.minecraft.banner.diagonal_right.lime": "黄绿色左上三角", "block.minecraft.banner.diagonal_right.magenta": "品红色左上三角", "block.minecraft.banner.diagonal_right.orange": "橙色左上三角", "block.minecraft.banner.diagonal_right.pink": "粉红色左上三角", "block.minecraft.banner.diagonal_right.purple": "紫色左上三角", "block.minecraft.banner.diagonal_right.red": "红色左上三角", "block.minecraft.banner.diagonal_right.white": "白色左上三角", "block.minecraft.banner.diagonal_right.yellow": "黄色左上三角", "block.minecraft.banner.diagonal_up_left.black": "黑色右下三角", "block.minecraft.banner.diagonal_up_left.blue": "蓝色右下三角", "block.minecraft.banner.diagonal_up_left.brown": "棕色右下三角", "block.minecraft.banner.diagonal_up_left.cyan": "青色右下三角", "block.minecraft.banner.diagonal_up_left.gray": "灰色右下三角", "block.minecraft.banner.diagonal_up_left.green": "绿色右下三角", "block.minecraft.banner.diagonal_up_left.light_blue": "淡蓝色右下三角", "block.minecraft.banner.diagonal_up_left.light_gray": "淡灰色右下三角", "block.minecraft.banner.diagonal_up_left.lime": "黄绿色右下三角", "block.minecraft.banner.diagonal_up_left.magenta": "品红色右下三角", "block.minecraft.banner.diagonal_up_left.orange": "橙色右下三角", "block.minecraft.banner.diagonal_up_left.pink": "粉红色右下三角", "block.minecraft.banner.diagonal_up_left.purple": "紫色右下三角", "block.minecraft.banner.diagonal_up_left.red": "红色右下三角", "block.minecraft.banner.diagonal_up_left.white": "白色右下三角", "block.minecraft.banner.diagonal_up_left.yellow": "黄色右下三角", "block.minecraft.banner.diagonal_up_right.black": "黑色左下三角", "block.minecraft.banner.diagonal_up_right.blue": "蓝色左下三角", "block.minecraft.banner.diagonal_up_right.brown": "棕色左下三角", "block.minecraft.banner.diagonal_up_right.cyan": "青色左下三角", "block.minecraft.banner.diagonal_up_right.gray": "灰色左下三角", "block.minecraft.banner.diagonal_up_right.green": "绿色左下三角", "block.minecraft.banner.diagonal_up_right.light_blue": "淡蓝色左下三角", "block.minecraft.banner.diagonal_up_right.light_gray": "淡灰色左下三角", "block.minecraft.banner.diagonal_up_right.lime": "黄绿色左下三角", "block.minecraft.banner.diagonal_up_right.magenta": "品红色左下三角", "block.minecraft.banner.diagonal_up_right.orange": "橙色左下三角", "block.minecraft.banner.diagonal_up_right.pink": "粉红色左下三角", "block.minecraft.banner.diagonal_up_right.purple": "紫色左下三角", "block.minecraft.banner.diagonal_up_right.red": "红色左下三角", "block.minecraft.banner.diagonal_up_right.white": "白色左下三角", "block.minecraft.banner.diagonal_up_right.yellow": "黄色左下三角", "block.minecraft.banner.flow.black": "黑色涡流", "block.minecraft.banner.flow.blue": "蓝色涡流", "block.minecraft.banner.flow.brown": "棕色涡流", "block.minecraft.banner.flow.cyan": "青色涡流", "block.minecraft.banner.flow.gray": "灰色涡流", "block.minecraft.banner.flow.green": "绿色涡流", "block.minecraft.banner.flow.light_blue": "淡蓝色涡流", "block.minecraft.banner.flow.light_gray": "淡灰色涡流", "block.minecraft.banner.flow.lime": "黄绿色涡流", "block.minecraft.banner.flow.magenta": "品红色涡流", "block.minecraft.banner.flow.orange": "橙色涡流", "block.minecraft.banner.flow.pink": "粉红色涡流", "block.minecraft.banner.flow.purple": "紫色涡流", "block.minecraft.banner.flow.red": "红色涡流", "block.minecraft.banner.flow.white": "白色涡流", "block.minecraft.banner.flow.yellow": "黄色涡流", "block.minecraft.banner.flower.black": "黑色花朵盾徽", "block.minecraft.banner.flower.blue": "蓝色花朵盾徽", "block.minecraft.banner.flower.brown": "棕色花朵盾徽", "block.minecraft.banner.flower.cyan": "青色花朵盾徽", "block.minecraft.banner.flower.gray": "灰色花朵盾徽", "block.minecraft.banner.flower.green": "绿色花朵盾徽", "block.minecraft.banner.flower.light_blue": "淡蓝色花朵盾徽", "block.minecraft.banner.flower.light_gray": "淡灰色花朵盾徽", "block.minecraft.banner.flower.lime": "黄绿色花朵盾徽", "block.minecraft.banner.flower.magenta": "品红色花朵盾徽", "block.minecraft.banner.flower.orange": "橙色花朵盾徽", "block.minecraft.banner.flower.pink": "粉红色花朵盾徽", "block.minecraft.banner.flower.purple": "紫色花朵盾徽", "block.minecraft.banner.flower.red": "红色花朵盾徽", "block.minecraft.banner.flower.white": "白色花朵盾徽", "block.minecraft.banner.flower.yellow": "黄色花朵盾徽", "block.minecraft.banner.globe.black": "黑色地球", "block.minecraft.banner.globe.blue": "蓝色地球", "block.minecraft.banner.globe.brown": "棕色地球", "block.minecraft.banner.globe.cyan": "青色地球", "block.minecraft.banner.globe.gray": "灰色地球", "block.minecraft.banner.globe.green": "绿色地球", "block.minecraft.banner.globe.light_blue": "淡蓝色地球", "block.minecraft.banner.globe.light_gray": "淡灰色地球", "block.minecraft.banner.globe.lime": "黄绿色地球", "block.minecraft.banner.globe.magenta": "品红色地球", "block.minecraft.banner.globe.orange": "橙色地球", "block.minecraft.banner.globe.pink": "粉红色地球", "block.minecraft.banner.globe.purple": "紫色地球", "block.minecraft.banner.globe.red": "红色地球", "block.minecraft.banner.globe.white": "白色地球", "block.minecraft.banner.globe.yellow": "黄色地球", "block.minecraft.banner.gradient.black": "黑色自上渐淡", "block.minecraft.banner.gradient.blue": "蓝色自上渐淡", "block.minecraft.banner.gradient.brown": "棕色自上渐淡", "block.minecraft.banner.gradient.cyan": "青色自上渐淡", "block.minecraft.banner.gradient.gray": "灰色自上渐淡", "block.minecraft.banner.gradient.green": "绿色自上渐淡", "block.minecraft.banner.gradient.light_blue": "淡蓝色自上渐淡", "block.minecraft.banner.gradient.light_gray": "淡灰色自上渐淡", "block.minecraft.banner.gradient.lime": "黄绿色自上渐淡", "block.minecraft.banner.gradient.magenta": "品红色自上渐淡", "block.minecraft.banner.gradient.orange": "橙色自上渐淡", "block.minecraft.banner.gradient.pink": "粉红色自上渐淡", "block.minecraft.banner.gradient.purple": "紫色自上渐淡", "block.minecraft.banner.gradient.red": "红色自上渐淡", "block.minecraft.banner.gradient.white": "白色自上渐淡", "block.minecraft.banner.gradient.yellow": "黄色自上渐淡", "block.minecraft.banner.gradient_up.black": "黑色自下渐淡", "block.minecraft.banner.gradient_up.blue": "蓝色自下渐淡", "block.minecraft.banner.gradient_up.brown": "棕色自下渐淡", "block.minecraft.banner.gradient_up.cyan": "青色自下渐淡", "block.minecraft.banner.gradient_up.gray": "灰色自下渐淡", "block.minecraft.banner.gradient_up.green": "绿色自下渐淡", "block.minecraft.banner.gradient_up.light_blue": "淡蓝色自下渐淡", "block.minecraft.banner.gradient_up.light_gray": "淡灰色自下渐淡", "block.minecraft.banner.gradient_up.lime": "黄绿色自下渐淡", "block.minecraft.banner.gradient_up.magenta": "品红色自下渐淡", "block.minecraft.banner.gradient_up.orange": "橙色自下渐淡", "block.minecraft.banner.gradient_up.pink": "粉红色自下渐淡", "block.minecraft.banner.gradient_up.purple": "紫色自下渐淡", "block.minecraft.banner.gradient_up.red": "红色自下渐淡", "block.minecraft.banner.gradient_up.white": "白色自下渐淡", "block.minecraft.banner.gradient_up.yellow": "黄色自下渐淡", "block.minecraft.banner.guster.black": "黑色旋风", "block.minecraft.banner.guster.blue": "蓝色旋风", "block.minecraft.banner.guster.brown": "棕色旋风", "block.minecraft.banner.guster.cyan": "青色旋风", "block.minecraft.banner.guster.gray": "灰色旋风", "block.minecraft.banner.guster.green": "绿色旋风", "block.minecraft.banner.guster.light_blue": "淡蓝色旋风", "block.minecraft.banner.guster.light_gray": "淡灰色旋风", "block.minecraft.banner.guster.lime": "黄绿色旋风", "block.minecraft.banner.guster.magenta": "品红色旋风", "block.minecraft.banner.guster.orange": "橙色旋风", "block.minecraft.banner.guster.pink": "粉红色旋风", "block.minecraft.banner.guster.purple": "紫色旋风", "block.minecraft.banner.guster.red": "红色旋风", "block.minecraft.banner.guster.white": "白色旋风", "block.minecraft.banner.guster.yellow": "黄色旋风", "block.minecraft.banner.half_horizontal.black": "黑色上半方形", "block.minecraft.banner.half_horizontal.blue": "蓝色上半方形", "block.minecraft.banner.half_horizontal.brown": "棕色上半方形", "block.minecraft.banner.half_horizontal.cyan": "青色上半方形", "block.minecraft.banner.half_horizontal.gray": "灰色上半方形", "block.minecraft.banner.half_horizontal.green": "绿色上半方形", "block.minecraft.banner.half_horizontal.light_blue": "淡蓝色上半方形", "block.minecraft.banner.half_horizontal.light_gray": "淡灰色上半方形", "block.minecraft.banner.half_horizontal.lime": "黄绿色上半方形", "block.minecraft.banner.half_horizontal.magenta": "品红色上半方形", "block.minecraft.banner.half_horizontal.orange": "橙色上半方形", "block.minecraft.banner.half_horizontal.pink": "粉红色上半方形", "block.minecraft.banner.half_horizontal.purple": "紫色上半方形", "block.minecraft.banner.half_horizontal.red": "红色上半方形", "block.minecraft.banner.half_horizontal.white": "白色上半方形", "block.minecraft.banner.half_horizontal.yellow": "黄色上半方形", "block.minecraft.banner.half_horizontal_bottom.black": "黑色下半方形", "block.minecraft.banner.half_horizontal_bottom.blue": "蓝色下半方形", "block.minecraft.banner.half_horizontal_bottom.brown": "棕色下半方形", "block.minecraft.banner.half_horizontal_bottom.cyan": "青色下半方形", "block.minecraft.banner.half_horizontal_bottom.gray": "灰色下半方形", "block.minecraft.banner.half_horizontal_bottom.green": "绿色下半方形", "block.minecraft.banner.half_horizontal_bottom.light_blue": "淡蓝色下半方形", "block.minecraft.banner.half_horizontal_bottom.light_gray": "淡灰色下半方形", "block.minecraft.banner.half_horizontal_bottom.lime": "黄绿色下半方形", "block.minecraft.banner.half_horizontal_bottom.magenta": "品红色下半方形", "block.minecraft.banner.half_horizontal_bottom.orange": "橙色下半方形", "block.minecraft.banner.half_horizontal_bottom.pink": "粉红色下半方形", "block.minecraft.banner.half_horizontal_bottom.purple": "紫色下半方形", "block.minecraft.banner.half_horizontal_bottom.red": "红色下半方形", "block.minecraft.banner.half_horizontal_bottom.white": "白色下半方形", "block.minecraft.banner.half_horizontal_bottom.yellow": "黄色下半方形", "block.minecraft.banner.half_vertical.black": "黑色右半方形", "block.minecraft.banner.half_vertical.blue": "蓝色右半方形", "block.minecraft.banner.half_vertical.brown": "棕色右半方形", "block.minecraft.banner.half_vertical.cyan": "青色右半方形", "block.minecraft.banner.half_vertical.gray": "灰色右半方形", "block.minecraft.banner.half_vertical.green": "绿色右半方形", "block.minecraft.banner.half_vertical.light_blue": "淡蓝色右半方形", "block.minecraft.banner.half_vertical.light_gray": "淡灰色右半方形", "block.minecraft.banner.half_vertical.lime": "黄绿色右半方形", "block.minecraft.banner.half_vertical.magenta": "品红色右半方形", "block.minecraft.banner.half_vertical.orange": "橙色右半方形", "block.minecraft.banner.half_vertical.pink": "粉红色右半方形", "block.minecraft.banner.half_vertical.purple": "紫色右半方形", "block.minecraft.banner.half_vertical.red": "红色右半方形", "block.minecraft.banner.half_vertical.white": "白色右半方形", "block.minecraft.banner.half_vertical.yellow": "黄色右半方形", "block.minecraft.banner.half_vertical_right.black": "黑色左半方形", "block.minecraft.banner.half_vertical_right.blue": "蓝色左半方形", "block.minecraft.banner.half_vertical_right.brown": "棕色左半方形", "block.minecraft.banner.half_vertical_right.cyan": "青色左半方形", "block.minecraft.banner.half_vertical_right.gray": "灰色左半方形", "block.minecraft.banner.half_vertical_right.green": "绿色左半方形", "block.minecraft.banner.half_vertical_right.light_blue": "淡蓝色左半方形", "block.minecraft.banner.half_vertical_right.light_gray": "淡灰色左半方形", "block.minecraft.banner.half_vertical_right.lime": "黄绿色左半方形", "block.minecraft.banner.half_vertical_right.magenta": "品红色左半方形", "block.minecraft.banner.half_vertical_right.orange": "橙色左半方形", "block.minecraft.banner.half_vertical_right.pink": "粉红色左半方形", "block.minecraft.banner.half_vertical_right.purple": "紫色左半方形", "block.minecraft.banner.half_vertical_right.red": "红色左半方形", "block.minecraft.banner.half_vertical_right.white": "白色左半方形", "block.minecraft.banner.half_vertical_right.yellow": "黄色左半方形", "block.minecraft.banner.mojang.black": "黑色Mojang徽标", "block.minecraft.banner.mojang.blue": "蓝色Mojang徽标", "block.minecraft.banner.mojang.brown": "棕色Mojang徽标", "block.minecraft.banner.mojang.cyan": "青色Mojang徽标", "block.minecraft.banner.mojang.gray": "灰色Mojang徽标", "block.minecraft.banner.mojang.green": "绿色Mojang徽标", "block.minecraft.banner.mojang.light_blue": "淡蓝色Mojang徽标", "block.minecraft.banner.mojang.light_gray": "淡灰色Mojang徽标", "block.minecraft.banner.mojang.lime": "黄绿色Mojang徽标", "block.minecraft.banner.mojang.magenta": "品红色Mojang徽标", "block.minecraft.banner.mojang.orange": "橙色Mojang徽标", "block.minecraft.banner.mojang.pink": "粉红色Mojang徽标", "block.minecraft.banner.mojang.purple": "紫色Mojang徽标", "block.minecraft.banner.mojang.red": "红色Mojang徽标", "block.minecraft.banner.mojang.white": "白色Mojang徽标", "block.minecraft.banner.mojang.yellow": "黄色Mojang徽标", "block.minecraft.banner.piglin.black": "黑色猪鼻", "block.minecraft.banner.piglin.blue": "蓝色猪鼻", "block.minecraft.banner.piglin.brown": "棕色猪鼻", "block.minecraft.banner.piglin.cyan": "青色猪鼻", "block.minecraft.banner.piglin.gray": "灰色猪鼻", "block.minecraft.banner.piglin.green": "绿色猪鼻", "block.minecraft.banner.piglin.light_blue": "淡蓝色猪鼻", "block.minecraft.banner.piglin.light_gray": "淡灰色猪鼻", "block.minecraft.banner.piglin.lime": "黄绿色猪鼻", "block.minecraft.banner.piglin.magenta": "品红色猪鼻", "block.minecraft.banner.piglin.orange": "橙色猪鼻", "block.minecraft.banner.piglin.pink": "粉红色猪鼻", "block.minecraft.banner.piglin.purple": "紫色猪鼻", "block.minecraft.banner.piglin.red": "红色猪鼻", "block.minecraft.banner.piglin.white": "白色猪鼻", "block.minecraft.banner.piglin.yellow": "黄色猪鼻", "block.minecraft.banner.rhombus.black": "黑色菱形", "block.minecraft.banner.rhombus.blue": "蓝色菱形", "block.minecraft.banner.rhombus.brown": "棕色菱形", "block.minecraft.banner.rhombus.cyan": "青色菱形", "block.minecraft.banner.rhombus.gray": "灰色菱形", "block.minecraft.banner.rhombus.green": "绿色菱形", "block.minecraft.banner.rhombus.light_blue": "淡蓝色菱形", "block.minecraft.banner.rhombus.light_gray": "淡灰色菱形", "block.minecraft.banner.rhombus.lime": "黄绿色菱形", "block.minecraft.banner.rhombus.magenta": "品红色菱形", "block.minecraft.banner.rhombus.orange": "橙色菱形", "block.minecraft.banner.rhombus.pink": "粉红色菱形", "block.minecraft.banner.rhombus.purple": "紫色菱形", "block.minecraft.banner.rhombus.red": "红色菱形", "block.minecraft.banner.rhombus.white": "白色菱形", "block.minecraft.banner.rhombus.yellow": "黄色菱形", "block.minecraft.banner.skull.black": "黑色头颅盾徽", "block.minecraft.banner.skull.blue": "蓝色头颅盾徽", "block.minecraft.banner.skull.brown": "棕色头颅盾徽", "block.minecraft.banner.skull.cyan": "青色头颅盾徽", "block.minecraft.banner.skull.gray": "灰色头颅盾徽", "block.minecraft.banner.skull.green": "绿色头颅盾徽", "block.minecraft.banner.skull.light_blue": "淡蓝色头颅盾徽", "block.minecraft.banner.skull.light_gray": "淡灰色头颅盾徽", "block.minecraft.banner.skull.lime": "黄绿色头颅盾徽", "block.minecraft.banner.skull.magenta": "品红色头颅盾徽", "block.minecraft.banner.skull.orange": "橙色头颅盾徽", "block.minecraft.banner.skull.pink": "粉红色头颅盾徽", "block.minecraft.banner.skull.purple": "紫色头颅盾徽", "block.minecraft.banner.skull.red": "红色头颅盾徽", "block.minecraft.banner.skull.white": "白色头颅盾徽", "block.minecraft.banner.skull.yellow": "黄色头颅盾徽", "block.minecraft.banner.small_stripes.black": "黑竖条纹", "block.minecraft.banner.small_stripes.blue": "蓝竖条纹", "block.minecraft.banner.small_stripes.brown": "棕竖条纹", "block.minecraft.banner.small_stripes.cyan": "青竖条纹", "block.minecraft.banner.small_stripes.gray": "灰竖条纹", "block.minecraft.banner.small_stripes.green": "绿竖条纹", "block.minecraft.banner.small_stripes.light_blue": "淡蓝竖条纹", "block.minecraft.banner.small_stripes.light_gray": "淡灰竖条纹", "block.minecraft.banner.small_stripes.lime": "黄绿竖条纹", "block.minecraft.banner.small_stripes.magenta": "品红竖条纹", "block.minecraft.banner.small_stripes.orange": "橙竖条纹", "block.minecraft.banner.small_stripes.pink": "粉红竖条纹", "block.minecraft.banner.small_stripes.purple": "紫竖条纹", "block.minecraft.banner.small_stripes.red": "红竖条纹", "block.minecraft.banner.small_stripes.white": "白竖条纹", "block.minecraft.banner.small_stripes.yellow": "黄竖条纹", "block.minecraft.banner.square_bottom_left.black": "右底黑方", "block.minecraft.banner.square_bottom_left.blue": "右底蓝方", "block.minecraft.banner.square_bottom_left.brown": "右底棕方", "block.minecraft.banner.square_bottom_left.cyan": "右底青方", "block.minecraft.banner.square_bottom_left.gray": "右底灰方", "block.minecraft.banner.square_bottom_left.green": "右底绿方", "block.minecraft.banner.square_bottom_left.light_blue": "右底淡蓝方", "block.minecraft.banner.square_bottom_left.light_gray": "右底淡灰方", "block.minecraft.banner.square_bottom_left.lime": "右底黄绿方", "block.minecraft.banner.square_bottom_left.magenta": "右底品红方", "block.minecraft.banner.square_bottom_left.orange": "右底橙方", "block.minecraft.banner.square_bottom_left.pink": "右底粉红方", "block.minecraft.banner.square_bottom_left.purple": "右底紫方", "block.minecraft.banner.square_bottom_left.red": "右底红方", "block.minecraft.banner.square_bottom_left.white": "右底白方", "block.minecraft.banner.square_bottom_left.yellow": "右底黄方", "block.minecraft.banner.square_bottom_right.black": "左底黑方", "block.minecraft.banner.square_bottom_right.blue": "左底蓝方", "block.minecraft.banner.square_bottom_right.brown": "左底棕方", "block.minecraft.banner.square_bottom_right.cyan": "左底青方", "block.minecraft.banner.square_bottom_right.gray": "左底灰方", "block.minecraft.banner.square_bottom_right.green": "左底绿方", "block.minecraft.banner.square_bottom_right.light_blue": "左底淡蓝方", "block.minecraft.banner.square_bottom_right.light_gray": "左底淡灰方", "block.minecraft.banner.square_bottom_right.lime": "左底黄绿方", "block.minecraft.banner.square_bottom_right.magenta": "左底品红方", "block.minecraft.banner.square_bottom_right.orange": "左底橙方", "block.minecraft.banner.square_bottom_right.pink": "左底粉红方", "block.minecraft.banner.square_bottom_right.purple": "左底紫方", "block.minecraft.banner.square_bottom_right.red": "左底红方", "block.minecraft.banner.square_bottom_right.white": "左底白方", "block.minecraft.banner.square_bottom_right.yellow": "左底黄方", "block.minecraft.banner.square_top_left.black": "右顶黑方", "block.minecraft.banner.square_top_left.blue": "右顶蓝方", "block.minecraft.banner.square_top_left.brown": "右顶棕方", "block.minecraft.banner.square_top_left.cyan": "右顶青方", "block.minecraft.banner.square_top_left.gray": "右顶灰方", "block.minecraft.banner.square_top_left.green": "右顶绿方", "block.minecraft.banner.square_top_left.light_blue": "右顶淡蓝方", "block.minecraft.banner.square_top_left.light_gray": "右顶淡灰方", "block.minecraft.banner.square_top_left.lime": "右顶黄绿方", "block.minecraft.banner.square_top_left.magenta": "右顶品红方", "block.minecraft.banner.square_top_left.orange": "右顶橙方", "block.minecraft.banner.square_top_left.pink": "右顶粉红方", "block.minecraft.banner.square_top_left.purple": "右顶紫方", "block.minecraft.banner.square_top_left.red": "右顶红方", "block.minecraft.banner.square_top_left.white": "右顶白方", "block.minecraft.banner.square_top_left.yellow": "右顶黄方", "block.minecraft.banner.square_top_right.black": "左顶黑方", "block.minecraft.banner.square_top_right.blue": "左顶蓝方", "block.minecraft.banner.square_top_right.brown": "左顶棕方", "block.minecraft.banner.square_top_right.cyan": "左顶青方", "block.minecraft.banner.square_top_right.gray": "左顶灰方", "block.minecraft.banner.square_top_right.green": "左顶绿方", "block.minecraft.banner.square_top_right.light_blue": "左顶淡蓝方", "block.minecraft.banner.square_top_right.light_gray": "左顶淡灰方", "block.minecraft.banner.square_top_right.lime": "左顶黄绿方", "block.minecraft.banner.square_top_right.magenta": "左顶品红方", "block.minecraft.banner.square_top_right.orange": "左顶橙方", "block.minecraft.banner.square_top_right.pink": "左顶粉红方", "block.minecraft.banner.square_top_right.purple": "左顶紫方", "block.minecraft.banner.square_top_right.red": "左顶红方", "block.minecraft.banner.square_top_right.white": "左顶白方", "block.minecraft.banner.square_top_right.yellow": "左顶黄方", "block.minecraft.banner.straight_cross.black": "黑正十字", "block.minecraft.banner.straight_cross.blue": "蓝正十字", "block.minecraft.banner.straight_cross.brown": "棕正十字", "block.minecraft.banner.straight_cross.cyan": "青正十字", "block.minecraft.banner.straight_cross.gray": "灰正十字", "block.minecraft.banner.straight_cross.green": "绿正十字", "block.minecraft.banner.straight_cross.light_blue": "淡蓝正十字", "block.minecraft.banner.straight_cross.light_gray": "淡灰正十字", "block.minecraft.banner.straight_cross.lime": "黄绿正十字", "block.minecraft.banner.straight_cross.magenta": "品红正十字", "block.minecraft.banner.straight_cross.orange": "橙正十字", "block.minecraft.banner.straight_cross.pink": "粉红正十字", "block.minecraft.banner.straight_cross.purple": "紫正十字", "block.minecraft.banner.straight_cross.red": "红正十字", "block.minecraft.banner.straight_cross.white": "白正十字", "block.minecraft.banner.straight_cross.yellow": "黄正十字", "block.minecraft.banner.stripe_bottom.black": "底黑横条", "block.minecraft.banner.stripe_bottom.blue": "底蓝横条", "block.minecraft.banner.stripe_bottom.brown": "底棕横条", "block.minecraft.banner.stripe_bottom.cyan": "底青横条", "block.minecraft.banner.stripe_bottom.gray": "底灰横条", "block.minecraft.banner.stripe_bottom.green": "底绿横条", "block.minecraft.banner.stripe_bottom.light_blue": "底淡蓝横条", "block.minecraft.banner.stripe_bottom.light_gray": "底淡灰横条", "block.minecraft.banner.stripe_bottom.lime": "底黄绿横条", "block.minecraft.banner.stripe_bottom.magenta": "底品红横条", "block.minecraft.banner.stripe_bottom.orange": "底橙横条", "block.minecraft.banner.stripe_bottom.pink": "底粉红横条", "block.minecraft.banner.stripe_bottom.purple": "底紫横条", "block.minecraft.banner.stripe_bottom.red": "底红横条", "block.minecraft.banner.stripe_bottom.white": "底白横条", "block.minecraft.banner.stripe_bottom.yellow": "底黄横条", "block.minecraft.banner.stripe_center.black": "中黑竖条", "block.minecraft.banner.stripe_center.blue": "中蓝竖条", "block.minecraft.banner.stripe_center.brown": "中棕竖条", "block.minecraft.banner.stripe_center.cyan": "中青竖条", "block.minecraft.banner.stripe_center.gray": "中灰竖条", "block.minecraft.banner.stripe_center.green": "中绿竖条", "block.minecraft.banner.stripe_center.light_blue": "中淡蓝竖条", "block.minecraft.banner.stripe_center.light_gray": "中淡灰竖条", "block.minecraft.banner.stripe_center.lime": "中黄绿竖条", "block.minecraft.banner.stripe_center.magenta": "中品红竖条", "block.minecraft.banner.stripe_center.orange": "中橙竖条", "block.minecraft.banner.stripe_center.pink": "中粉红竖条", "block.minecraft.banner.stripe_center.purple": "中紫竖条", "block.minecraft.banner.stripe_center.red": "中红竖条", "block.minecraft.banner.stripe_center.white": "中白竖条", "block.minecraft.banner.stripe_center.yellow": "中黄竖条", "block.minecraft.banner.stripe_downleft.black": "左黑斜条", "block.minecraft.banner.stripe_downleft.blue": "左蓝斜条", "block.minecraft.banner.stripe_downleft.brown": "左棕斜条", "block.minecraft.banner.stripe_downleft.cyan": "左青斜条", "block.minecraft.banner.stripe_downleft.gray": "左灰斜条", "block.minecraft.banner.stripe_downleft.green": "左绿斜条", "block.minecraft.banner.stripe_downleft.light_blue": "左淡蓝斜条", "block.minecraft.banner.stripe_downleft.light_gray": "左淡灰斜条", "block.minecraft.banner.stripe_downleft.lime": "左黄绿斜条", "block.minecraft.banner.stripe_downleft.magenta": "左品红斜条", "block.minecraft.banner.stripe_downleft.orange": "左橙斜条", "block.minecraft.banner.stripe_downleft.pink": "左粉红斜条", "block.minecraft.banner.stripe_downleft.purple": "左紫斜条", "block.minecraft.banner.stripe_downleft.red": "左红斜条", "block.minecraft.banner.stripe_downleft.white": "左白斜条", "block.minecraft.banner.stripe_downleft.yellow": "左黄斜条", "block.minecraft.banner.stripe_downright.black": "右黑斜条", "block.minecraft.banner.stripe_downright.blue": "右蓝斜条", "block.minecraft.banner.stripe_downright.brown": "右棕斜条", "block.minecraft.banner.stripe_downright.cyan": "右青斜条", "block.minecraft.banner.stripe_downright.gray": "右灰斜条", "block.minecraft.banner.stripe_downright.green": "右绿斜条", "block.minecraft.banner.stripe_downright.light_blue": "右淡蓝斜条", "block.minecraft.banner.stripe_downright.light_gray": "右淡灰斜条", "block.minecraft.banner.stripe_downright.lime": "右黄绿斜条", "block.minecraft.banner.stripe_downright.magenta": "右品红斜条", "block.minecraft.banner.stripe_downright.orange": "右橙斜条", "block.minecraft.banner.stripe_downright.pink": "右粉红斜条", "block.minecraft.banner.stripe_downright.purple": "右紫斜条", "block.minecraft.banner.stripe_downright.red": "右红斜条", "block.minecraft.banner.stripe_downright.white": "右白斜条", "block.minecraft.banner.stripe_downright.yellow": "右黄斜条", "block.minecraft.banner.stripe_left.black": "右黑竖条", "block.minecraft.banner.stripe_left.blue": "右蓝竖条", "block.minecraft.banner.stripe_left.brown": "右棕竖条", "block.minecraft.banner.stripe_left.cyan": "右青竖条", "block.minecraft.banner.stripe_left.gray": "右灰竖条", "block.minecraft.banner.stripe_left.green": "右绿竖条", "block.minecraft.banner.stripe_left.light_blue": "右淡蓝竖条", "block.minecraft.banner.stripe_left.light_gray": "右淡灰竖条", "block.minecraft.banner.stripe_left.lime": "右黄绿竖条", "block.minecraft.banner.stripe_left.magenta": "右品红竖条", "block.minecraft.banner.stripe_left.orange": "右橙竖条", "block.minecraft.banner.stripe_left.pink": "右粉红竖条", "block.minecraft.banner.stripe_left.purple": "右紫竖条", "block.minecraft.banner.stripe_left.red": "右红竖条", "block.minecraft.banner.stripe_left.white": "右白竖条", "block.minecraft.banner.stripe_left.yellow": "右黄竖条", "block.minecraft.banner.stripe_middle.black": "中黑横条", "block.minecraft.banner.stripe_middle.blue": "中蓝横条", "block.minecraft.banner.stripe_middle.brown": "中棕横条", "block.minecraft.banner.stripe_middle.cyan": "中青横条", "block.minecraft.banner.stripe_middle.gray": "中灰横条", "block.minecraft.banner.stripe_middle.green": "中绿横条", "block.minecraft.banner.stripe_middle.light_blue": "中淡蓝横条", "block.minecraft.banner.stripe_middle.light_gray": "中淡灰横条", "block.minecraft.banner.stripe_middle.lime": "中黄绿横条", "block.minecraft.banner.stripe_middle.magenta": "中品红横条", "block.minecraft.banner.stripe_middle.orange": "中橙横条", "block.minecraft.banner.stripe_middle.pink": "中粉红横条", "block.minecraft.banner.stripe_middle.purple": "中紫横条", "block.minecraft.banner.stripe_middle.red": "中红横条", "block.minecraft.banner.stripe_middle.white": "中白横条", "block.minecraft.banner.stripe_middle.yellow": "中黄横条", "block.minecraft.banner.stripe_right.black": "左黑竖条", "block.minecraft.banner.stripe_right.blue": "左蓝竖条", "block.minecraft.banner.stripe_right.brown": "左棕竖条", "block.minecraft.banner.stripe_right.cyan": "左青竖条", "block.minecraft.banner.stripe_right.gray": "左灰竖条", "block.minecraft.banner.stripe_right.green": "左绿竖条", "block.minecraft.banner.stripe_right.light_blue": "左淡蓝竖条", "block.minecraft.banner.stripe_right.light_gray": "左淡灰竖条", "block.minecraft.banner.stripe_right.lime": "左黄绿竖条", "block.minecraft.banner.stripe_right.magenta": "左品红竖条", "block.minecraft.banner.stripe_right.orange": "左橙竖条", "block.minecraft.banner.stripe_right.pink": "左粉红竖条", "block.minecraft.banner.stripe_right.purple": "左紫竖条", "block.minecraft.banner.stripe_right.red": "左红竖条", "block.minecraft.banner.stripe_right.white": "左白竖条", "block.minecraft.banner.stripe_right.yellow": "左黄竖条", "block.minecraft.banner.stripe_top.black": "顶黑横条", "block.minecraft.banner.stripe_top.blue": "顶蓝横条", "block.minecraft.banner.stripe_top.brown": "顶棕横条", "block.minecraft.banner.stripe_top.cyan": "顶青横条", "block.minecraft.banner.stripe_top.gray": "顶灰横条", "block.minecraft.banner.stripe_top.green": "顶绿横条", "block.minecraft.banner.stripe_top.light_blue": "顶淡蓝横条", "block.minecraft.banner.stripe_top.light_gray": "顶淡灰横条", "block.minecraft.banner.stripe_top.lime": "顶黄绿横条", "block.minecraft.banner.stripe_top.magenta": "顶品红横条", "block.minecraft.banner.stripe_top.orange": "顶橙横条", "block.minecraft.banner.stripe_top.pink": "顶粉红横条", "block.minecraft.banner.stripe_top.purple": "顶紫横条", "block.minecraft.banner.stripe_top.red": "顶红横条", "block.minecraft.banner.stripe_top.white": "顶白横条", "block.minecraft.banner.stripe_top.yellow": "顶黄横条", "block.minecraft.banner.triangle_bottom.black": "底黑三角", "block.minecraft.banner.triangle_bottom.blue": "底蓝三角", "block.minecraft.banner.triangle_bottom.brown": "底棕三角", "block.minecraft.banner.triangle_bottom.cyan": "底青三角", "block.minecraft.banner.triangle_bottom.gray": "底灰三角", "block.minecraft.banner.triangle_bottom.green": "底绿三角", "block.minecraft.banner.triangle_bottom.light_blue": "底淡蓝三角", "block.minecraft.banner.triangle_bottom.light_gray": "底淡灰三角", "block.minecraft.banner.triangle_bottom.lime": "底黄绿三角", "block.minecraft.banner.triangle_bottom.magenta": "底品红三角", "block.minecraft.banner.triangle_bottom.orange": "底橙三角", "block.minecraft.banner.triangle_bottom.pink": "底粉红三角", "block.minecraft.banner.triangle_bottom.purple": "底紫三角", "block.minecraft.banner.triangle_bottom.red": "底红三角", "block.minecraft.banner.triangle_bottom.white": "底白三角", "block.minecraft.banner.triangle_bottom.yellow": "底黄三角", "block.minecraft.banner.triangle_top.black": "顶黑三角", "block.minecraft.banner.triangle_top.blue": "顶蓝三角", "block.minecraft.banner.triangle_top.brown": "顶棕三角", "block.minecraft.banner.triangle_top.cyan": "顶青三角", "block.minecraft.banner.triangle_top.gray": "顶灰三角", "block.minecraft.banner.triangle_top.green": "顶绿三角", "block.minecraft.banner.triangle_top.light_blue": "顶淡蓝三角", "block.minecraft.banner.triangle_top.light_gray": "顶淡灰三角", "block.minecraft.banner.triangle_top.lime": "顶黄绿三角", "block.minecraft.banner.triangle_top.magenta": "顶品红三角", "block.minecraft.banner.triangle_top.orange": "顶橙三角", "block.minecraft.banner.triangle_top.pink": "顶粉红三角", "block.minecraft.banner.triangle_top.purple": "顶紫三角", "block.minecraft.banner.triangle_top.red": "顶红三角", "block.minecraft.banner.triangle_top.white": "顶白三角", "block.minecraft.banner.triangle_top.yellow": "顶黄三角", "block.minecraft.banner.triangles_bottom.black": "黑色底波纹", "block.minecraft.banner.triangles_bottom.blue": "蓝色底波纹", "block.minecraft.banner.triangles_bottom.brown": "棕色底波纹", "block.minecraft.banner.triangles_bottom.cyan": "青色底波纹", "block.minecraft.banner.triangles_bottom.gray": "灰色底波纹", "block.minecraft.banner.triangles_bottom.green": "绿色底波纹", "block.minecraft.banner.triangles_bottom.light_blue": "淡蓝色底波纹", "block.minecraft.banner.triangles_bottom.light_gray": "淡灰色底波纹", "block.minecraft.banner.triangles_bottom.lime": "黄绿色底波纹", "block.minecraft.banner.triangles_bottom.magenta": "品红色底波纹", "block.minecraft.banner.triangles_bottom.orange": "橙色底波纹", "block.minecraft.banner.triangles_bottom.pink": "粉红色底波纹", "block.minecraft.banner.triangles_bottom.purple": "紫色底波纹", "block.minecraft.banner.triangles_bottom.red": "红色底波纹", "block.minecraft.banner.triangles_bottom.white": "白色底波纹", "block.minecraft.banner.triangles_bottom.yellow": "黄色底波纹", "block.minecraft.banner.triangles_top.black": "黑色顶波纹", "block.minecraft.banner.triangles_top.blue": "蓝色顶波纹", "block.minecraft.banner.triangles_top.brown": "棕色顶波纹", "block.minecraft.banner.triangles_top.cyan": "青色顶波纹", "block.minecraft.banner.triangles_top.gray": "灰色顶波纹", "block.minecraft.banner.triangles_top.green": "绿色顶波纹", "block.minecraft.banner.triangles_top.light_blue": "淡蓝色顶波纹", "block.minecraft.banner.triangles_top.light_gray": "淡灰色顶波纹", "block.minecraft.banner.triangles_top.lime": "黄绿色顶波纹", "block.minecraft.banner.triangles_top.magenta": "品红色顶波纹", "block.minecraft.banner.triangles_top.orange": "橙色顶波纹", "block.minecraft.banner.triangles_top.pink": "粉红色顶波纹", "block.minecraft.banner.triangles_top.purple": "紫色顶波纹", "block.minecraft.banner.triangles_top.red": "红色顶波纹", "block.minecraft.banner.triangles_top.white": "白色顶波纹", "block.minecraft.banner.triangles_top.yellow": "黄色顶波纹", "block.minecraft.barrel": "木桶", "block.minecraft.barrier": "屏障", "block.minecraft.basalt": "玄武岩", "block.minecraft.beacon": "信标", "block.minecraft.beacon.primary": "主效果", "block.minecraft.beacon.secondary": "辅助效果", "block.minecraft.bed.no_sleep": "你只能在夜间或雷暴中入眠", "block.minecraft.bed.not_safe": "你现在不能休息，周围有怪物在游荡", "block.minecraft.bed.obstructed": "这张床已被阻挡", "block.minecraft.bed.occupied": "这张床已被占用", "block.minecraft.bed.too_far_away": "你现在不能休息，床太远了", "block.minecraft.bedrock": "基岩", "block.minecraft.bee_nest": "蜂巢", "block.minecraft.beehive": "蜂箱", "block.minecraft.beetroots": "甜菜根", "block.minecraft.bell": "钟", "block.minecraft.big_dripleaf": "大型垂滴叶", "block.minecraft.big_dripleaf_stem": "大型垂滴叶茎", "block.minecraft.birch_button": "白桦木按钮", "block.minecraft.birch_door": "白桦木门", "block.minecraft.birch_fence": "白桦木栅栏", "block.minecraft.birch_fence_gate": "白桦木栅栏门", "block.minecraft.birch_hanging_sign": "悬挂式白桦木告示牌", "block.minecraft.birch_leaves": "白桦树叶", "block.minecraft.birch_log": "白桦原木", "block.minecraft.birch_planks": "白桦木板", "block.minecraft.birch_pressure_plate": "白桦木压力板", "block.minecraft.birch_sapling": "白桦树苗", "block.minecraft.birch_sign": "白桦木告示牌", "block.minecraft.birch_slab": "白桦木台阶", "block.minecraft.birch_stairs": "白桦木楼梯", "block.minecraft.birch_trapdoor": "白桦木活板门", "block.minecraft.birch_wall_hanging_sign": "墙上的悬挂式白桦木告示牌", "block.minecraft.birch_wall_sign": "墙上的白桦木告示牌", "block.minecraft.birch_wood": "白桦木", "block.minecraft.black_banner": "黑色旗帜", "block.minecraft.black_bed": "黑色床", "block.minecraft.black_candle": "黑色蜡烛", "block.minecraft.black_candle_cake": "插上黑色蜡烛的蛋糕", "block.minecraft.black_carpet": "黑色地毯", "block.minecraft.black_concrete": "黑色混凝土", "block.minecraft.black_concrete_powder": "黑色混凝土粉末", "block.minecraft.black_glazed_terracotta": "黑色带釉陶瓦", "block.minecraft.black_shulker_box": "黑色潜影盒", "block.minecraft.black_stained_glass": "黑色染色玻璃", "block.minecraft.black_stained_glass_pane": "黑色染色玻璃板", "block.minecraft.black_terracotta": "黑色陶瓦", "block.minecraft.black_wool": "黑色羊毛", "block.minecraft.blackstone": "黑石", "block.minecraft.blackstone_slab": "黑石台阶", "block.minecraft.blackstone_stairs": "黑石楼梯", "block.minecraft.blackstone_wall": "黑石墙", "block.minecraft.blast_furnace": "高炉", "block.minecraft.blue_banner": "蓝色旗帜", "block.minecraft.blue_bed": "蓝色床", "block.minecraft.blue_candle": "蓝色蜡烛", "block.minecraft.blue_candle_cake": "插上蓝色蜡烛的蛋糕", "block.minecraft.blue_carpet": "蓝色地毯", "block.minecraft.blue_concrete": "蓝色混凝土", "block.minecraft.blue_concrete_powder": "蓝色混凝土粉末", "block.minecraft.blue_glazed_terracotta": "蓝色带釉陶瓦", "block.minecraft.blue_ice": "蓝冰", "block.minecraft.blue_orchid": "兰花", "block.minecraft.blue_shulker_box": "蓝色潜影盒", "block.minecraft.blue_stained_glass": "蓝色染色玻璃", "block.minecraft.blue_stained_glass_pane": "蓝色染色玻璃板", "block.minecraft.blue_terracotta": "蓝色陶瓦", "block.minecraft.blue_wool": "蓝色羊毛", "block.minecraft.bone_block": "骨块", "block.minecraft.bookshelf": "书架", "block.minecraft.brain_coral": "脑纹珊瑚", "block.minecraft.brain_coral_block": "脑纹珊瑚块", "block.minecraft.brain_coral_fan": "脑纹珊瑚扇", "block.minecraft.brain_coral_wall_fan": "墙上的脑纹珊瑚扇", "block.minecraft.brewing_stand": "酿造台", "block.minecraft.brick_slab": "红砖台阶", "block.minecraft.brick_stairs": "红砖楼梯", "block.minecraft.brick_wall": "红砖墙", "block.minecraft.bricks": "红砖块", "block.minecraft.brown_banner": "棕色旗帜", "block.minecraft.brown_bed": "棕色床", "block.minecraft.brown_candle": "棕色蜡烛", "block.minecraft.brown_candle_cake": "插上棕色蜡烛的蛋糕", "block.minecraft.brown_carpet": "棕色地毯", "block.minecraft.brown_concrete": "棕色混凝土", "block.minecraft.brown_concrete_powder": "棕色混凝土粉末", "block.minecraft.brown_glazed_terracotta": "棕色带釉陶瓦", "block.minecraft.brown_mushroom": "棕色蘑菇", "block.minecraft.brown_mushroom_block": "棕色蘑菇方块", "block.minecraft.brown_shulker_box": "棕色潜影盒", "block.minecraft.brown_stained_glass": "棕色染色玻璃", "block.minecraft.brown_stained_glass_pane": "棕色染色玻璃板", "block.minecraft.brown_terracotta": "棕色陶瓦", "block.minecraft.brown_wool": "棕色羊毛", "block.minecraft.bubble_column": "气泡柱", "block.minecraft.bubble_coral": "气泡珊瑚", "block.minecraft.bubble_coral_block": "气泡珊瑚块", "block.minecraft.bubble_coral_fan": "气泡珊瑚扇", "block.minecraft.bubble_coral_wall_fan": "墙上的气泡珊瑚扇", "block.minecraft.budding_amethyst": "紫水晶母岩", "block.minecraft.bush": "灌木丛", "block.minecraft.cactus": "仙人掌", "block.minecraft.cactus_flower": "仙人掌花", "block.minecraft.cake": "蛋糕", "block.minecraft.calcite": "方解石", "block.minecraft.calibrated_sculk_sensor": "校频幽匿感测体", "block.minecraft.campfire": "营火", "block.minecraft.candle": "蜡烛", "block.minecraft.candle_cake": "插上蜡烛的蛋糕", "block.minecraft.carrots": "胡萝卜", "block.minecraft.cartography_table": "制图台", "block.minecraft.carved_pumpkin": "雕刻南瓜", "block.minecraft.cauldron": "炼药锅", "block.minecraft.cave_air": "洞穴空气", "block.minecraft.cave_vines": "洞穴藤蔓", "block.minecraft.cave_vines_plant": "洞穴藤蔓植株", "block.minecraft.chain": "锁链", "block.minecraft.chain_command_block": "连锁型命令方块", "block.minecraft.cherry_button": "樱花木按钮", "block.minecraft.cherry_door": "樱花木门", "block.minecraft.cherry_fence": "樱花木栅栏", "block.minecraft.cherry_fence_gate": "樱花木栅栏门", "block.minecraft.cherry_hanging_sign": "悬挂式樱花木告示牌", "block.minecraft.cherry_leaves": "樱花树叶", "block.minecraft.cherry_log": "樱花原木", "block.minecraft.cherry_planks": "樱花木板", "block.minecraft.cherry_pressure_plate": "樱花木压力板", "block.minecraft.cherry_sapling": "樱花树苗", "block.minecraft.cherry_sign": "樱花木告示牌", "block.minecraft.cherry_slab": "樱花木台阶", "block.minecraft.cherry_stairs": "樱花木楼梯", "block.minecraft.cherry_trapdoor": "樱花木活板门", "block.minecraft.cherry_wall_hanging_sign": "墙上的悬挂式樱花木告示牌", "block.minecraft.cherry_wall_sign": "墙上的樱花木告示牌", "block.minecraft.cherry_wood": "樱花木", "block.minecraft.chest": "箱子", "block.minecraft.chipped_anvil": "开裂的铁砧", "block.minecraft.chiseled_bookshelf": "雕纹书架", "block.minecraft.chiseled_copper": "雕纹铜块", "block.minecraft.chiseled_deepslate": "雕纹深板岩", "block.minecraft.chiseled_nether_bricks": "雕纹下界砖块", "block.minecraft.chiseled_polished_blackstone": "雕纹磨制黑石", "block.minecraft.chiseled_quartz_block": "雕纹石英块", "block.minecraft.chiseled_red_sandstone": "雕纹红砂岩", "block.minecraft.chiseled_resin_bricks": "雕纹树脂砖块", "block.minecraft.chiseled_sandstone": "雕纹砂岩", "block.minecraft.chiseled_stone_bricks": "雕纹石砖", "block.minecraft.chiseled_tuff": "雕纹凝灰岩", "block.minecraft.chiseled_tuff_bricks": "雕纹凝灰岩砖", "block.minecraft.chorus_flower": "紫颂花", "block.minecraft.chorus_plant": "紫颂植株", "block.minecraft.clay": "黏土", "block.minecraft.closed_eyeblossom": "闭合的眼眸花", "block.minecraft.coal_block": "煤炭块", "block.minecraft.coal_ore": "煤矿石", "block.minecraft.coarse_dirt": "砂土", "block.minecraft.cobbled_deepslate": "深板岩圆石", "block.minecraft.cobbled_deepslate_slab": "深板岩圆石台阶", "block.minecraft.cobbled_deepslate_stairs": "深板岩圆石楼梯", "block.minecraft.cobbled_deepslate_wall": "深板岩圆石墙", "block.minecraft.cobblestone": "圆石", "block.minecraft.cobblestone_slab": "圆石台阶", "block.minecraft.cobblestone_stairs": "圆石楼梯", "block.minecraft.cobblestone_wall": "圆石墙", "block.minecraft.cobweb": "蜘蛛网", "block.minecraft.cocoa": "可可果", "block.minecraft.command_block": "命令方块", "block.minecraft.comparator": "红石比较器", "block.minecraft.composter": "堆肥桶", "block.minecraft.conduit": "潮涌核心", "block.minecraft.copper_block": "铜块", "block.minecraft.copper_bulb": "铜灯", "block.minecraft.copper_door": "铜门", "block.minecraft.copper_grate": "铜格栅", "block.minecraft.copper_ore": "铜矿石", "block.minecraft.copper_trapdoor": "铜活板门", "block.minecraft.cornflower": "矢车菊", "block.minecraft.cracked_deepslate_bricks": "裂纹深板岩砖", "block.minecraft.cracked_deepslate_tiles": "裂纹深板岩瓦", "block.minecraft.cracked_nether_bricks": "裂纹下界砖块", "block.minecraft.cracked_polished_blackstone_bricks": "裂纹磨制黑石砖", "block.minecraft.cracked_stone_bricks": "裂纹石砖", "block.minecraft.crafter": "合成器", "block.minecraft.crafting_table": "工作台", "block.minecraft.creaking_heart": "嘎枝之心", "block.minecraft.creeper_head": "苦力怕的头", "block.minecraft.creeper_wall_head": "墙上的苦力怕的头", "block.minecraft.crimson_button": "绯红木按钮", "block.minecraft.crimson_door": "绯红木门", "block.minecraft.crimson_fence": "绯红木栅栏", "block.minecraft.crimson_fence_gate": "绯红木栅栏门", "block.minecraft.crimson_fungus": "绯红菌", "block.minecraft.crimson_hanging_sign": "悬挂式绯红木告示牌", "block.minecraft.crimson_hyphae": "绯红菌核", "block.minecraft.crimson_nylium": "绯红菌岩", "block.minecraft.crimson_planks": "绯红木板", "block.minecraft.crimson_pressure_plate": "绯红木压力板", "block.minecraft.crimson_roots": "绯红菌索", "block.minecraft.crimson_sign": "绯红木告示牌", "block.minecraft.crimson_slab": "绯红木台阶", "block.minecraft.crimson_stairs": "绯红木楼梯", "block.minecraft.crimson_stem": "绯红菌柄", "block.minecraft.crimson_trapdoor": "绯红木活板门", "block.minecraft.crimson_wall_hanging_sign": "墙上的悬挂式绯红木告示牌", "block.minecraft.crimson_wall_sign": "墙上的绯红木告示牌", "block.minecraft.crying_obsidian": "哭泣的黑曜石", "block.minecraft.cut_copper": "切制铜块", "block.minecraft.cut_copper_slab": "切制铜台阶", "block.minecraft.cut_copper_stairs": "切制铜楼梯", "block.minecraft.cut_red_sandstone": "切制红砂岩", "block.minecraft.cut_red_sandstone_slab": "切制红砂岩台阶", "block.minecraft.cut_sandstone": "切制砂岩", "block.minecraft.cut_sandstone_slab": "切制砂岩台阶", "block.minecraft.cyan_banner": "青色旗帜", "block.minecraft.cyan_bed": "青色床", "block.minecraft.cyan_candle": "青色蜡烛", "block.minecraft.cyan_candle_cake": "插上青色蜡烛的蛋糕", "block.minecraft.cyan_carpet": "青色地毯", "block.minecraft.cyan_concrete": "青色混凝土", "block.minecraft.cyan_concrete_powder": "青色混凝土粉末", "block.minecraft.cyan_glazed_terracotta": "青色带釉陶瓦", "block.minecraft.cyan_shulker_box": "青色潜影盒", "block.minecraft.cyan_stained_glass": "青色染色玻璃", "block.minecraft.cyan_stained_glass_pane": "青色染色玻璃板", "block.minecraft.cyan_terracotta": "青色陶瓦", "block.minecraft.cyan_wool": "青色羊毛", "block.minecraft.damaged_anvil": "损坏的铁砧", "block.minecraft.dandelion": "蒲公英", "block.minecraft.dark_oak_button": "深色橡木按钮", "block.minecraft.dark_oak_door": "深色橡木门", "block.minecraft.dark_oak_fence": "深色橡木栅栏", "block.minecraft.dark_oak_fence_gate": "深色橡木栅栏门", "block.minecraft.dark_oak_hanging_sign": "悬挂式深色橡木告示牌", "block.minecraft.dark_oak_leaves": "深色橡树树叶", "block.minecraft.dark_oak_log": "深色橡木原木", "block.minecraft.dark_oak_planks": "深色橡木木板", "block.minecraft.dark_oak_pressure_plate": "深色橡木压力板", "block.minecraft.dark_oak_sapling": "深色橡树树苗", "block.minecraft.dark_oak_sign": "深色橡木告示牌", "block.minecraft.dark_oak_slab": "深色橡木台阶", "block.minecraft.dark_oak_stairs": "深色橡木楼梯", "block.minecraft.dark_oak_trapdoor": "深色橡木活板门", "block.minecraft.dark_oak_wall_hanging_sign": "墙上的悬挂式深色橡木告示牌", "block.minecraft.dark_oak_wall_sign": "墙上的深色橡木告示牌", "block.minecraft.dark_oak_wood": "深色橡木", "block.minecraft.dark_prismarine": "暗海晶石", "block.minecraft.dark_prismarine_slab": "暗海晶石台阶", "block.minecraft.dark_prismarine_stairs": "暗海晶石楼梯", "block.minecraft.daylight_detector": "阳光探测器", "block.minecraft.dead_brain_coral": "失活的脑纹珊瑚", "block.minecraft.dead_brain_coral_block": "失活的脑纹珊瑚块", "block.minecraft.dead_brain_coral_fan": "失活的脑纹珊瑚扇", "block.minecraft.dead_brain_coral_wall_fan": "墙上的失活脑纹珊瑚扇", "block.minecraft.dead_bubble_coral": "失活的气泡珊瑚", "block.minecraft.dead_bubble_coral_block": "失活的气泡珊瑚块", "block.minecraft.dead_bubble_coral_fan": "失活的气泡珊瑚扇", "block.minecraft.dead_bubble_coral_wall_fan": "墙上的失活气泡珊瑚扇", "block.minecraft.dead_bush": "枯萎的灌木", "block.minecraft.dead_fire_coral": "失活的火珊瑚", "block.minecraft.dead_fire_coral_block": "失活的火珊瑚块", "block.minecraft.dead_fire_coral_fan": "失活的火珊瑚扇", "block.minecraft.dead_fire_coral_wall_fan": "墙上的失活火珊瑚扇", "block.minecraft.dead_horn_coral": "失活的鹿角珊瑚", "block.minecraft.dead_horn_coral_block": "失活的鹿角珊瑚块", "block.minecraft.dead_horn_coral_fan": "失活的鹿角珊瑚扇", "block.minecraft.dead_horn_coral_wall_fan": "墙上的失活鹿角珊瑚扇", "block.minecraft.dead_tube_coral": "失活的管珊瑚", "block.minecraft.dead_tube_coral_block": "失活的管珊瑚块", "block.minecraft.dead_tube_coral_fan": "失活的管珊瑚扇", "block.minecraft.dead_tube_coral_wall_fan": "墙上的失活管珊瑚扇", "block.minecraft.decorated_pot": "饰纹陶罐", "block.minecraft.deepslate": "深板岩", "block.minecraft.deepslate_brick_slab": "深板岩砖台阶", "block.minecraft.deepslate_brick_stairs": "深板岩砖楼梯", "block.minecraft.deepslate_brick_wall": "深板岩砖墙", "block.minecraft.deepslate_bricks": "深板岩砖", "block.minecraft.deepslate_coal_ore": "深层煤矿石", "block.minecraft.deepslate_copper_ore": "深层铜矿石", "block.minecraft.deepslate_diamond_ore": "深层钻石矿石", "block.minecraft.deepslate_emerald_ore": "深层绿宝石矿石", "block.minecraft.deepslate_gold_ore": "深层金矿石", "block.minecraft.deepslate_iron_ore": "深层铁矿石", "block.minecraft.deepslate_lapis_ore": "深层青金石矿石", "block.minecraft.deepslate_redstone_ore": "深层红石矿石", "block.minecraft.deepslate_tile_slab": "深板岩瓦台阶", "block.minecraft.deepslate_tile_stairs": "深板岩瓦楼梯", "block.minecraft.deepslate_tile_wall": "深板岩瓦墙", "block.minecraft.deepslate_tiles": "深板岩瓦", "block.minecraft.detector_rail": "探测铁轨", "block.minecraft.diamond_block": "钻石块", "block.minecraft.diamond_ore": "钻石矿石", "block.minecraft.diorite": "闪长岩", "block.minecraft.diorite_slab": "闪长岩台阶", "block.minecraft.diorite_stairs": "闪长岩楼梯", "block.minecraft.diorite_wall": "闪长岩墙", "block.minecraft.dirt": "泥土", "block.minecraft.dirt_path": "土径", "block.minecraft.dispenser": "发射器", "block.minecraft.dragon_egg": "龙蛋", "block.minecraft.dragon_head": "龙首", "block.minecraft.dragon_wall_head": "墙上的龙首", "block.minecraft.dried_ghast": "失水恶魂", "block.minecraft.dried_kelp_block": "干海带块", "block.minecraft.dripstone_block": "滴水石块", "block.minecraft.dropper": "投掷器", "block.minecraft.emerald_block": "绿宝石块", "block.minecraft.emerald_ore": "绿宝石矿石", "block.minecraft.enchanting_table": "附魔台", "block.minecraft.end_gateway": "末地折跃门", "block.minecraft.end_portal": "末地传送门", "block.minecraft.end_portal_frame": "末地传送门框架", "block.minecraft.end_rod": "末地烛", "block.minecraft.end_stone": "末地石", "block.minecraft.end_stone_brick_slab": "末地石砖台阶", "block.minecraft.end_stone_brick_stairs": "末地石砖楼梯", "block.minecraft.end_stone_brick_wall": "末地石砖墙", "block.minecraft.end_stone_bricks": "末地石砖", "block.minecraft.ender_chest": "末影箱", "block.minecraft.exposed_chiseled_copper": "斑驳的雕纹铜块", "block.minecraft.exposed_copper": "斑驳的铜块", "block.minecraft.exposed_copper_bulb": "斑驳的铜灯", "block.minecraft.exposed_copper_door": "斑驳的铜门", "block.minecraft.exposed_copper_grate": "斑驳的铜格栅", "block.minecraft.exposed_copper_trapdoor": "斑驳的铜活板门", "block.minecraft.exposed_cut_copper": "斑驳的切制铜块", "block.minecraft.exposed_cut_copper_slab": "斑驳的切制铜台阶", "block.minecraft.exposed_cut_copper_stairs": "斑驳的切制铜楼梯", "block.minecraft.farmland": "耕地", "block.minecraft.fern": "蕨", "block.minecraft.fire": "火", "block.minecraft.fire_coral": "火珊瑚", "block.minecraft.fire_coral_block": "火珊瑚块", "block.minecraft.fire_coral_fan": "火珊瑚扇", "block.minecraft.fire_coral_wall_fan": "墙上的火珊瑚扇", "block.minecraft.firefly_bush": "萤火虫灌木丛", "block.minecraft.fletching_table": "制箭台", "block.minecraft.flower_pot": "花盆", "block.minecraft.flowering_azalea": "盛开的杜鹃花丛", "block.minecraft.flowering_azalea_leaves": "盛开的杜鹃树叶", "block.minecraft.frogspawn": "青蛙卵", "block.minecraft.frosted_ice": "霜冰", "block.minecraft.furnace": "熔炉", "block.minecraft.gilded_blackstone": "镶金黑石", "block.minecraft.glass": "玻璃", "block.minecraft.glass_pane": "玻璃板", "block.minecraft.glow_lichen": "发光地衣", "block.minecraft.glowstone": "荧石", "block.minecraft.gold_block": "金块", "block.minecraft.gold_ore": "金矿石", "block.minecraft.granite": "花岗岩", "block.minecraft.granite_slab": "花岗岩台阶", "block.minecraft.granite_stairs": "花岗岩楼梯", "block.minecraft.granite_wall": "花岗岩墙", "block.minecraft.grass": "草", "block.minecraft.grass_block": "草方块", "block.minecraft.gravel": "沙砾", "block.minecraft.gray_banner": "灰色旗帜", "block.minecraft.gray_bed": "灰色床", "block.minecraft.gray_candle": "灰色蜡烛", "block.minecraft.gray_candle_cake": "插上灰色蜡烛的蛋糕", "block.minecraft.gray_carpet": "灰色地毯", "block.minecraft.gray_concrete": "灰色混凝土", "block.minecraft.gray_concrete_powder": "灰色混凝土粉末", "block.minecraft.gray_glazed_terracotta": "灰色带釉陶瓦", "block.minecraft.gray_shulker_box": "灰色潜影盒", "block.minecraft.gray_stained_glass": "灰色染色玻璃", "block.minecraft.gray_stained_glass_pane": "灰色染色玻璃板", "block.minecraft.gray_terracotta": "灰色陶瓦", "block.minecraft.gray_wool": "灰色羊毛", "block.minecraft.green_banner": "绿色旗帜", "block.minecraft.green_bed": "绿色床", "block.minecraft.green_candle": "绿色蜡烛", "block.minecraft.green_candle_cake": "插上绿色蜡烛的蛋糕", "block.minecraft.green_carpet": "绿色地毯", "block.minecraft.green_concrete": "绿色混凝土", "block.minecraft.green_concrete_powder": "绿色混凝土粉末", "block.minecraft.green_glazed_terracotta": "绿色带釉陶瓦", "block.minecraft.green_shulker_box": "绿色潜影盒", "block.minecraft.green_stained_glass": "绿色染色玻璃", "block.minecraft.green_stained_glass_pane": "绿色染色玻璃板", "block.minecraft.green_terracotta": "绿色陶瓦", "block.minecraft.green_wool": "绿色羊毛", "block.minecraft.grindstone": "砂轮", "block.minecraft.hanging_roots": "垂根", "block.minecraft.hay_block": "干草捆", "block.minecraft.heavy_core": "沉重核心", "block.minecraft.heavy_weighted_pressure_plate": "重质测重压力板", "block.minecraft.honey_block": "蜂蜜块", "block.minecraft.honeycomb_block": "蜜脾块", "block.minecraft.hopper": "漏斗", "block.minecraft.horn_coral": "鹿角珊瑚", "block.minecraft.horn_coral_block": "鹿角珊瑚块", "block.minecraft.horn_coral_fan": "鹿角珊瑚扇", "block.minecraft.horn_coral_wall_fan": "墙上的鹿角珊瑚扇", "block.minecraft.ice": "冰", "block.minecraft.infested_chiseled_stone_bricks": "虫蚀雕纹石砖", "block.minecraft.infested_cobblestone": "虫蚀圆石", "block.minecraft.infested_cracked_stone_bricks": "虫蚀裂纹石砖", "block.minecraft.infested_deepslate": "虫蚀深板岩", "block.minecraft.infested_mossy_stone_bricks": "虫蚀苔石砖", "block.minecraft.infested_stone": "虫蚀石头", "block.minecraft.infested_stone_bricks": "虫蚀石砖", "block.minecraft.iron_bars": "铁栏杆", "block.minecraft.iron_block": "铁块", "block.minecraft.iron_door": "铁门", "block.minecraft.iron_ore": "铁矿石", "block.minecraft.iron_trapdoor": "铁活板门", "block.minecraft.jack_o_lantern": "南瓜灯", "block.minecraft.jigsaw": "拼图方块", "block.minecraft.jukebox": "唱片机", "block.minecraft.jungle_button": "丛林木按钮", "block.minecraft.jungle_door": "丛林木门", "block.minecraft.jungle_fence": "丛林木栅栏", "block.minecraft.jungle_fence_gate": "丛林木栅栏门", "block.minecraft.jungle_hanging_sign": "悬挂式丛林木告示牌", "block.minecraft.jungle_leaves": "丛林树叶", "block.minecraft.jungle_log": "丛林原木", "block.minecraft.jungle_planks": "丛林木板", "block.minecraft.jungle_pressure_plate": "丛林木压力板", "block.minecraft.jungle_sapling": "丛林树苗", "block.minecraft.jungle_sign": "丛林木告示牌", "block.minecraft.jungle_slab": "丛林木台阶", "block.minecraft.jungle_stairs": "丛林木楼梯", "block.minecraft.jungle_trapdoor": "丛林木活板门", "block.minecraft.jungle_wall_hanging_sign": "墙上的悬挂式丛林木告示牌", "block.minecraft.jungle_wall_sign": "墙上的丛林木告示牌", "block.minecraft.jungle_wood": "丛林木", "block.minecraft.kelp": "海带", "block.minecraft.kelp_plant": "海带植株", "block.minecraft.ladder": "梯子", "block.minecraft.lantern": "灯笼", "block.minecraft.lapis_block": "青金石块", "block.minecraft.lapis_ore": "青金石矿石", "block.minecraft.large_amethyst_bud": "大型紫晶芽", "block.minecraft.large_fern": "大型蕨", "block.minecraft.lava": "熔岩", "block.minecraft.lava_cauldron": "装有熔岩的炼药锅", "block.minecraft.leaf_litter": "枯叶", "block.minecraft.lectern": "讲台", "block.minecraft.lever": "拉杆", "block.minecraft.light": "光源方块", "block.minecraft.light_blue_banner": "淡蓝色旗帜", "block.minecraft.light_blue_bed": "淡蓝色床", "block.minecraft.light_blue_candle": "淡蓝色蜡烛", "block.minecraft.light_blue_candle_cake": "插上淡蓝色蜡烛的蛋糕", "block.minecraft.light_blue_carpet": "淡蓝色地毯", "block.minecraft.light_blue_concrete": "淡蓝色混凝土", "block.minecraft.light_blue_concrete_powder": "淡蓝色混凝土粉末", "block.minecraft.light_blue_glazed_terracotta": "淡蓝色带釉陶瓦", "block.minecraft.light_blue_shulker_box": "淡蓝色潜影盒", "block.minecraft.light_blue_stained_glass": "淡蓝色染色玻璃", "block.minecraft.light_blue_stained_glass_pane": "淡蓝色染色玻璃板", "block.minecraft.light_blue_terracotta": "淡蓝色陶瓦", "block.minecraft.light_blue_wool": "淡蓝色羊毛", "block.minecraft.light_gray_banner": "淡灰色旗帜", "block.minecraft.light_gray_bed": "淡灰色床", "block.minecraft.light_gray_candle": "淡灰色蜡烛", "block.minecraft.light_gray_candle_cake": "插上淡灰色蜡烛的蛋糕", "block.minecraft.light_gray_carpet": "淡灰色地毯", "block.minecraft.light_gray_concrete": "淡灰色混凝土", "block.minecraft.light_gray_concrete_powder": "淡灰色混凝土粉末", "block.minecraft.light_gray_glazed_terracotta": "淡灰色带釉陶瓦", "block.minecraft.light_gray_shulker_box": "淡灰色潜影盒", "block.minecraft.light_gray_stained_glass": "淡灰色染色玻璃", "block.minecraft.light_gray_stained_glass_pane": "淡灰色染色玻璃板", "block.minecraft.light_gray_terracotta": "淡灰色陶瓦", "block.minecraft.light_gray_wool": "淡灰色羊毛", "block.minecraft.light_weighted_pressure_plate": "轻质测重压力板", "block.minecraft.lightning_rod": "避雷针", "block.minecraft.lilac": "丁香", "block.minecraft.lily_of_the_valley": "铃兰", "block.minecraft.lily_pad": "睡莲", "block.minecraft.lime_banner": "黄绿色旗帜", "block.minecraft.lime_bed": "黄绿色床", "block.minecraft.lime_candle": "黄绿色蜡烛", "block.minecraft.lime_candle_cake": "插上黄绿色蜡烛的蛋糕", "block.minecraft.lime_carpet": "黄绿色地毯", "block.minecraft.lime_concrete": "黄绿色混凝土", "block.minecraft.lime_concrete_powder": "黄绿色混凝土粉末", "block.minecraft.lime_glazed_terracotta": "黄绿色带釉陶瓦", "block.minecraft.lime_shulker_box": "黄绿色潜影盒", "block.minecraft.lime_stained_glass": "黄绿色染色玻璃", "block.minecraft.lime_stained_glass_pane": "黄绿色染色玻璃板", "block.minecraft.lime_terracotta": "黄绿色陶瓦", "block.minecraft.lime_wool": "黄绿色羊毛", "block.minecraft.lodestone": "磁石", "block.minecraft.loom": "织布机", "block.minecraft.magenta_banner": "品红色旗帜", "block.minecraft.magenta_bed": "品红色床", "block.minecraft.magenta_candle": "品红色蜡烛", "block.minecraft.magenta_candle_cake": "插上品红色蜡烛的蛋糕", "block.minecraft.magenta_carpet": "品红色地毯", "block.minecraft.magenta_concrete": "品红色混凝土", "block.minecraft.magenta_concrete_powder": "品红色混凝土粉末", "block.minecraft.magenta_glazed_terracotta": "品红色带釉陶瓦", "block.minecraft.magenta_shulker_box": "品红色潜影盒", "block.minecraft.magenta_stained_glass": "品红色染色玻璃", "block.minecraft.magenta_stained_glass_pane": "品红色染色玻璃板", "block.minecraft.magenta_terracotta": "品红色陶瓦", "block.minecraft.magenta_wool": "品红色羊毛", "block.minecraft.magma_block": "岩浆块", "block.minecraft.mangrove_button": "红树木按钮", "block.minecraft.mangrove_door": "红树木门", "block.minecraft.mangrove_fence": "红树木栅栏", "block.minecraft.mangrove_fence_gate": "红树木栅栏门", "block.minecraft.mangrove_hanging_sign": "悬挂式红树木告示牌", "block.minecraft.mangrove_leaves": "红树树叶", "block.minecraft.mangrove_log": "红树原木", "block.minecraft.mangrove_planks": "红树木板", "block.minecraft.mangrove_pressure_plate": "红树木压力板", "block.minecraft.mangrove_propagule": "红树胎生苗", "block.minecraft.mangrove_roots": "红树根", "block.minecraft.mangrove_sign": "红树木告示牌", "block.minecraft.mangrove_slab": "红树木台阶", "block.minecraft.mangrove_stairs": "红树木楼梯", "block.minecraft.mangrove_trapdoor": "红树木活板门", "block.minecraft.mangrove_wall_hanging_sign": "墙上的悬挂式红树木告示牌", "block.minecraft.mangrove_wall_sign": "墙上的红树木告示牌", "block.minecraft.mangrove_wood": "红树木", "block.minecraft.medium_amethyst_bud": "中型紫晶芽", "block.minecraft.melon": "西瓜", "block.minecraft.melon_stem": "西瓜茎", "block.minecraft.moss_block": "苔藓块", "block.minecraft.moss_carpet": "覆地苔藓", "block.minecraft.mossy_cobblestone": "苔石", "block.minecraft.mossy_cobblestone_slab": "苔石台阶", "block.minecraft.mossy_cobblestone_stairs": "苔石楼梯", "block.minecraft.mossy_cobblestone_wall": "苔石墙", "block.minecraft.mossy_stone_brick_slab": "苔石砖台阶", "block.minecraft.mossy_stone_brick_stairs": "苔石砖楼梯", "block.minecraft.mossy_stone_brick_wall": "苔石砖墙", "block.minecraft.mossy_stone_bricks": "苔石砖", "block.minecraft.moving_piston": "移动的活塞", "block.minecraft.mud": "泥巴", "block.minecraft.mud_brick_slab": "泥砖台阶", "block.minecraft.mud_brick_stairs": "泥砖楼梯", "block.minecraft.mud_brick_wall": "泥砖墙", "block.minecraft.mud_bricks": "泥砖", "block.minecraft.muddy_mangrove_roots": "沾泥的红树根", "block.minecraft.mushroom_stem": "蘑菇柄", "block.minecraft.mycelium": "菌丝体", "block.minecraft.nether_brick_fence": "下界砖栅栏", "block.minecraft.nether_brick_slab": "下界砖台阶", "block.minecraft.nether_brick_stairs": "下界砖楼梯", "block.minecraft.nether_brick_wall": "下界砖墙", "block.minecraft.nether_bricks": "下界砖块", "block.minecraft.nether_gold_ore": "下界金矿石", "block.minecraft.nether_portal": "下界传送门", "block.minecraft.nether_quartz_ore": "下界石英矿石", "block.minecraft.nether_sprouts": "下界苗", "block.minecraft.nether_wart": "下界疣", "block.minecraft.nether_wart_block": "下界疣块", "block.minecraft.netherite_block": "下界合金块", "block.minecraft.netherrack": "下界岩", "block.minecraft.note_block": "音符盒", "block.minecraft.oak_button": "橡木按钮", "block.minecraft.oak_door": "橡木门", "block.minecraft.oak_fence": "橡木栅栏", "block.minecraft.oak_fence_gate": "橡木栅栏门", "block.minecraft.oak_hanging_sign": "悬挂式橡木告示牌", "block.minecraft.oak_leaves": "橡树树叶", "block.minecraft.oak_log": "橡木原木", "block.minecraft.oak_planks": "橡木木板", "block.minecraft.oak_pressure_plate": "橡木压力板", "block.minecraft.oak_sapling": "橡树树苗", "block.minecraft.oak_sign": "橡木告示牌", "block.minecraft.oak_slab": "橡木台阶", "block.minecraft.oak_stairs": "橡木楼梯", "block.minecraft.oak_trapdoor": "橡木活板门", "block.minecraft.oak_wall_hanging_sign": "墙上的悬挂式橡木告示牌", "block.minecraft.oak_wall_sign": "墙上的橡木告示牌", "block.minecraft.oak_wood": "橡木", "block.minecraft.observer": "侦测器", "block.minecraft.obsidian": "黑曜石", "block.minecraft.ochre_froglight": "赭黄蛙明灯", "block.minecraft.ominous_banner": "不祥旗帜", "block.minecraft.open_eyeblossom": "张开的眼眸花", "block.minecraft.orange_banner": "橙色旗帜", "block.minecraft.orange_bed": "橙色床", "block.minecraft.orange_candle": "橙色蜡烛", "block.minecraft.orange_candle_cake": "插上橙色蜡烛的蛋糕", "block.minecraft.orange_carpet": "橙色地毯", "block.minecraft.orange_concrete": "橙色混凝土", "block.minecraft.orange_concrete_powder": "橙色混凝土粉末", "block.minecraft.orange_glazed_terracotta": "橙色带釉陶瓦", "block.minecraft.orange_shulker_box": "橙色潜影盒", "block.minecraft.orange_stained_glass": "橙色染色玻璃", "block.minecraft.orange_stained_glass_pane": "橙色染色玻璃板", "block.minecraft.orange_terracotta": "橙色陶瓦", "block.minecraft.orange_tulip": "橙色郁金香", "block.minecraft.orange_wool": "橙色羊毛", "block.minecraft.oxeye_daisy": "滨菊", "block.minecraft.oxidized_chiseled_copper": "氧化的雕纹铜块", "block.minecraft.oxidized_copper": "氧化的铜块", "block.minecraft.oxidized_copper_bulb": "氧化的铜灯", "block.minecraft.oxidized_copper_door": "氧化的铜门", "block.minecraft.oxidized_copper_grate": "氧化的铜格栅", "block.minecraft.oxidized_copper_trapdoor": "氧化的铜活板门", "block.minecraft.oxidized_cut_copper": "氧化的切制铜块", "block.minecraft.oxidized_cut_copper_slab": "氧化的切制铜台阶", "block.minecraft.oxidized_cut_copper_stairs": "氧化的切制铜楼梯", "block.minecraft.packed_ice": "浮冰", "block.minecraft.packed_mud": "泥坯", "block.minecraft.pale_hanging_moss": "苍白垂须", "block.minecraft.pale_moss_block": "苍白苔藓块", "block.minecraft.pale_moss_carpet": "苍白覆地苔藓", "block.minecraft.pale_oak_button": "苍白橡木按钮", "block.minecraft.pale_oak_door": "苍白橡木门", "block.minecraft.pale_oak_fence": "苍白橡木栅栏", "block.minecraft.pale_oak_fence_gate": "苍白橡木栅栏门", "block.minecraft.pale_oak_hanging_sign": "悬挂式苍白橡木告示牌", "block.minecraft.pale_oak_leaves": "苍白橡树树叶", "block.minecraft.pale_oak_log": "苍白橡木原木", "block.minecraft.pale_oak_planks": "苍白橡木木板", "block.minecraft.pale_oak_pressure_plate": "苍白橡木压力板", "block.minecraft.pale_oak_sapling": "苍白橡树树苗", "block.minecraft.pale_oak_sign": "苍白橡木告示牌", "block.minecraft.pale_oak_slab": "苍白橡木台阶", "block.minecraft.pale_oak_stairs": "苍白橡木楼梯", "block.minecraft.pale_oak_trapdoor": "苍白橡木活板门", "block.minecraft.pale_oak_wall_hanging_sign": "墙上的悬挂式苍白橡木告示牌", "block.minecraft.pale_oak_wall_sign": "墙上的苍白橡木告示牌", "block.minecraft.pale_oak_wood": "苍白橡木", "block.minecraft.pearlescent_froglight": "珠光蛙明灯", "block.minecraft.peony": "牡丹", "block.minecraft.petrified_oak_slab": "石化橡木台阶", "block.minecraft.piglin_head": "猪灵的头", "block.minecraft.piglin_wall_head": "墙上的猪灵的头", "block.minecraft.pink_banner": "粉红色旗帜", "block.minecraft.pink_bed": "粉红色床", "block.minecraft.pink_candle": "粉红色蜡烛", "block.minecraft.pink_candle_cake": "插上粉红色蜡烛的蛋糕", "block.minecraft.pink_carpet": "粉红色地毯", "block.minecraft.pink_concrete": "粉红色混凝土", "block.minecraft.pink_concrete_powder": "粉红色混凝土粉末", "block.minecraft.pink_glazed_terracotta": "粉红色带釉陶瓦", "block.minecraft.pink_petals": "粉红色花簇", "block.minecraft.pink_shulker_box": "粉红色潜影盒", "block.minecraft.pink_stained_glass": "粉红色染色玻璃", "block.minecraft.pink_stained_glass_pane": "粉红色染色玻璃板", "block.minecraft.pink_terracotta": "粉红色陶瓦", "block.minecraft.pink_tulip": "粉红色郁金香", "block.minecraft.pink_wool": "粉红色羊毛", "block.minecraft.piston": "活塞", "block.minecraft.piston_head": "活塞头", "block.minecraft.pitcher_crop": "瓶子草植株", "block.minecraft.pitcher_plant": "瓶子草", "block.minecraft.player_head": "玩家的头", "block.minecraft.player_head.named": "%s的头", "block.minecraft.player_wall_head": "墙上的玩家的头", "block.minecraft.podzol": "灰化土", "block.minecraft.pointed_dripstone": "滴水石锥", "block.minecraft.polished_andesite": "磨制安山岩", "block.minecraft.polished_andesite_slab": "磨制安山岩台阶", "block.minecraft.polished_andesite_stairs": "磨制安山岩楼梯", "block.minecraft.polished_basalt": "磨制玄武岩", "block.minecraft.polished_blackstone": "磨制黑石", "block.minecraft.polished_blackstone_brick_slab": "磨制黑石砖台阶", "block.minecraft.polished_blackstone_brick_stairs": "磨制黑石砖楼梯", "block.minecraft.polished_blackstone_brick_wall": "磨制黑石砖墙", "block.minecraft.polished_blackstone_bricks": "磨制黑石砖", "block.minecraft.polished_blackstone_button": "磨制黑石按钮", "block.minecraft.polished_blackstone_pressure_plate": "磨制黑石压力板", "block.minecraft.polished_blackstone_slab": "磨制黑石台阶", "block.minecraft.polished_blackstone_stairs": "磨制黑石楼梯", "block.minecraft.polished_blackstone_wall": "磨制黑石墙", "block.minecraft.polished_deepslate": "磨制深板岩", "block.minecraft.polished_deepslate_slab": "磨制深板岩台阶", "block.minecraft.polished_deepslate_stairs": "磨制深板岩楼梯", "block.minecraft.polished_deepslate_wall": "磨制深板岩墙", "block.minecraft.polished_diorite": "磨制闪长岩", "block.minecraft.polished_diorite_slab": "磨制闪长岩台阶", "block.minecraft.polished_diorite_stairs": "磨制闪长岩楼梯", "block.minecraft.polished_granite": "磨制花岗岩", "block.minecraft.polished_granite_slab": "磨制花岗岩台阶", "block.minecraft.polished_granite_stairs": "磨制花岗岩楼梯", "block.minecraft.polished_tuff": "磨制凝灰岩", "block.minecraft.polished_tuff_slab": "磨制凝灰岩台阶", "block.minecraft.polished_tuff_stairs": "磨制凝灰岩楼梯", "block.minecraft.polished_tuff_wall": "磨制凝灰岩墙", "block.minecraft.poppy": "虞美人", "block.minecraft.potatoes": "马铃薯", "block.minecraft.potted_acacia_sapling": "金合欢树苗盆栽", "block.minecraft.potted_allium": "绒球葱盆栽", "block.minecraft.potted_azalea_bush": "杜鹃花丛盆栽", "block.minecraft.potted_azure_bluet": "蓝花美耳草盆栽", "block.minecraft.potted_bamboo": "竹子盆栽", "block.minecraft.potted_birch_sapling": "白桦树苗盆栽", "block.minecraft.potted_blue_orchid": "兰花盆栽", "block.minecraft.potted_brown_mushroom": "棕色蘑菇盆栽", "block.minecraft.potted_cactus": "仙人掌盆栽", "block.minecraft.potted_cherry_sapling": "樱花树苗盆栽", "block.minecraft.potted_closed_eyeblossom": "闭合的眼眸花盆栽", "block.minecraft.potted_cornflower": "矢车菊盆栽", "block.minecraft.potted_crimson_fungus": "绯红菌盆栽", "block.minecraft.potted_crimson_roots": "绯红菌索盆栽", "block.minecraft.potted_dandelion": "蒲公英盆栽", "block.minecraft.potted_dark_oak_sapling": "深色橡树树苗盆栽", "block.minecraft.potted_dead_bush": "枯萎的灌木盆栽", "block.minecraft.potted_fern": "蕨盆栽", "block.minecraft.potted_flowering_azalea_bush": "盛开的杜鹃花丛盆栽", "block.minecraft.potted_jungle_sapling": "丛林树苗盆栽", "block.minecraft.potted_lily_of_the_valley": "铃兰盆栽", "block.minecraft.potted_mangrove_propagule": "红树胎生苗盆栽", "block.minecraft.potted_oak_sapling": "橡树树苗盆栽", "block.minecraft.potted_open_eyeblossom": "张开的眼眸花盆栽", "block.minecraft.potted_orange_tulip": "橙色郁金香盆栽", "block.minecraft.potted_oxeye_daisy": "滨菊盆栽", "block.minecraft.potted_pale_oak_sapling": "苍白橡树树苗盆栽", "block.minecraft.potted_pink_tulip": "粉红色郁金香盆栽", "block.minecraft.potted_poppy": "虞美人盆栽", "block.minecraft.potted_red_mushroom": "红色蘑菇盆栽", "block.minecraft.potted_red_tulip": "红色郁金香盆栽", "block.minecraft.potted_spruce_sapling": "云杉树苗盆栽", "block.minecraft.potted_torchflower": "火把花盆栽", "block.minecraft.potted_warped_fungus": "诡异菌盆栽", "block.minecraft.potted_warped_roots": "诡异菌索盆栽", "block.minecraft.potted_white_tulip": "白色郁金香盆栽", "block.minecraft.potted_wither_rose": "凋灵玫瑰盆栽", "block.minecraft.powder_snow": "细雪", "block.minecraft.powder_snow_cauldron": "装有细雪的炼药锅", "block.minecraft.powered_rail": "动力铁轨", "block.minecraft.prismarine": "海晶石", "block.minecraft.prismarine_brick_slab": "海晶石砖台阶", "block.minecraft.prismarine_brick_stairs": "海晶石砖楼梯", "block.minecraft.prismarine_bricks": "海晶石砖", "block.minecraft.prismarine_slab": "海晶石台阶", "block.minecraft.prismarine_stairs": "海晶石楼梯", "block.minecraft.prismarine_wall": "海晶石墙", "block.minecraft.pumpkin": "南瓜", "block.minecraft.pumpkin_stem": "南瓜茎", "block.minecraft.purple_banner": "紫色旗帜", "block.minecraft.purple_bed": "紫色床", "block.minecraft.purple_candle": "紫色蜡烛", "block.minecraft.purple_candle_cake": "插上紫色蜡烛的蛋糕", "block.minecraft.purple_carpet": "紫色地毯", "block.minecraft.purple_concrete": "紫色混凝土", "block.minecraft.purple_concrete_powder": "紫色混凝土粉末", "block.minecraft.purple_glazed_terracotta": "紫色带釉陶瓦", "block.minecraft.purple_shulker_box": "紫色潜影盒", "block.minecraft.purple_stained_glass": "紫色染色玻璃", "block.minecraft.purple_stained_glass_pane": "紫色染色玻璃板", "block.minecraft.purple_terracotta": "紫色陶瓦", "block.minecraft.purple_wool": "紫色羊毛", "block.minecraft.purpur_block": "紫珀块", "block.minecraft.purpur_pillar": "紫珀柱", "block.minecraft.purpur_slab": "紫珀台阶", "block.minecraft.purpur_stairs": "紫珀楼梯", "block.minecraft.quartz_block": "石英块", "block.minecraft.quartz_bricks": "石英砖", "block.minecraft.quartz_pillar": "石英柱", "block.minecraft.quartz_slab": "石英台阶", "block.minecraft.quartz_stairs": "石英楼梯", "block.minecraft.rail": "铁轨", "block.minecraft.raw_copper_block": "粗铜块", "block.minecraft.raw_gold_block": "粗金块", "block.minecraft.raw_iron_block": "粗铁块", "block.minecraft.red_banner": "红色旗帜", "block.minecraft.red_bed": "红色床", "block.minecraft.red_candle": "红色蜡烛", "block.minecraft.red_candle_cake": "插上红色蜡烛的蛋糕", "block.minecraft.red_carpet": "红色地毯", "block.minecraft.red_concrete": "红色混凝土", "block.minecraft.red_concrete_powder": "红色混凝土粉末", "block.minecraft.red_glazed_terracotta": "红色带釉陶瓦", "block.minecraft.red_mushroom": "红色蘑菇", "block.minecraft.red_mushroom_block": "红色蘑菇方块", "block.minecraft.red_nether_brick_slab": "红色下界砖台阶", "block.minecraft.red_nether_brick_stairs": "红色下界砖楼梯", "block.minecraft.red_nether_brick_wall": "红色下界砖墙", "block.minecraft.red_nether_bricks": "红色下界砖块", "block.minecraft.red_sand": "红沙", "block.minecraft.red_sandstone": "红砂岩", "block.minecraft.red_sandstone_slab": "红砂岩台阶", "block.minecraft.red_sandstone_stairs": "红砂岩楼梯", "block.minecraft.red_sandstone_wall": "红砂岩墙", "block.minecraft.red_shulker_box": "红色潜影盒", "block.minecraft.red_stained_glass": "红色染色玻璃", "block.minecraft.red_stained_glass_pane": "红色染色玻璃板", "block.minecraft.red_terracotta": "红色陶瓦", "block.minecraft.red_tulip": "红色郁金香", "block.minecraft.red_wool": "红色羊毛", "block.minecraft.redstone_block": "红石块", "block.minecraft.redstone_lamp": "红石灯", "block.minecraft.redstone_ore": "红石矿石", "block.minecraft.redstone_torch": "红石火把", "block.minecraft.redstone_wall_torch": "墙上的红石火把", "block.minecraft.redstone_wire": "红石线", "block.minecraft.reinforced_deepslate": "强化深板岩", "block.minecraft.repeater": "红石中继器", "block.minecraft.repeating_command_block": "循环型命令方块", "block.minecraft.resin_block": "树脂块", "block.minecraft.resin_brick_slab": "树脂砖台阶", "block.minecraft.resin_brick_stairs": "树脂砖楼梯", "block.minecraft.resin_brick_wall": "树脂砖墙", "block.minecraft.resin_bricks": "树脂砖块", "block.minecraft.resin_clump": "树脂团", "block.minecraft.respawn_anchor": "重生锚", "block.minecraft.rooted_dirt": "缠根泥土", "block.minecraft.rose_bush": "玫瑰丛", "block.minecraft.sand": "沙子", "block.minecraft.sandstone": "砂岩", "block.minecraft.sandstone_slab": "砂岩台阶", "block.minecraft.sandstone_stairs": "砂岩楼梯", "block.minecraft.sandstone_wall": "砂岩墙", "block.minecraft.scaffolding": "脚手架", "block.minecraft.sculk": "幽匿块", "block.minecraft.sculk_catalyst": "幽匿催发体", "block.minecraft.sculk_sensor": "幽匿感测体", "block.minecraft.sculk_shrieker": "幽匿尖啸体", "block.minecraft.sculk_vein": "幽匿脉络", "block.minecraft.sea_lantern": "海晶灯", "block.minecraft.sea_pickle": "海泡菜", "block.minecraft.seagrass": "海草", "block.minecraft.set_spawn": "已设置重生点", "block.minecraft.short_dry_grass": "矮枯草丛", "block.minecraft.short_grass": "矮草丛", "block.minecraft.shroomlight": "菌光体", "block.minecraft.shulker_box": "潜影盒", "block.minecraft.skeleton_skull": "骷髅头颅", "block.minecraft.skeleton_wall_skull": "墙上的骷髅头颅", "block.minecraft.slime_block": "黏液块", "block.minecraft.small_amethyst_bud": "小型紫晶芽", "block.minecraft.small_dripleaf": "小型垂滴叶", "block.minecraft.smithing_table": "锻造台", "block.minecraft.smoker": "烟熏炉", "block.minecraft.smooth_basalt": "平滑玄武岩", "block.minecraft.smooth_quartz": "平滑石英块", "block.minecraft.smooth_quartz_slab": "平滑石英台阶", "block.minecraft.smooth_quartz_stairs": "平滑石英楼梯", "block.minecraft.smooth_red_sandstone": "平滑红砂岩", "block.minecraft.smooth_red_sandstone_slab": "平滑红砂岩台阶", "block.minecraft.smooth_red_sandstone_stairs": "平滑红砂岩楼梯", "block.minecraft.smooth_sandstone": "平滑砂岩", "block.minecraft.smooth_sandstone_slab": "平滑砂岩台阶", "block.minecraft.smooth_sandstone_stairs": "平滑砂岩楼梯", "block.minecraft.smooth_stone": "平滑石头", "block.minecraft.smooth_stone_slab": "平滑石头台阶", "block.minecraft.sniffer_egg": "嗅探兽蛋", "block.minecraft.snow": "雪", "block.minecraft.snow_block": "雪块", "block.minecraft.soul_campfire": "灵魂营火", "block.minecraft.soul_fire": "灵魂火", "block.minecraft.soul_lantern": "灵魂灯笼", "block.minecraft.soul_sand": "灵魂沙", "block.minecraft.soul_soil": "灵魂土", "block.minecraft.soul_torch": "灵魂火把", "block.minecraft.soul_wall_torch": "墙上的灵魂火把", "block.minecraft.spawn.not_valid": "你的床或已充能的重生锚不存在或已被阻挡", "block.minecraft.spawner": "刷怪笼", "block.minecraft.spawner.desc1": "用刷怪蛋交互时：", "block.minecraft.spawner.desc2": "设置生物种类", "block.minecraft.sponge": "海绵", "block.minecraft.spore_blossom": "孢子花", "block.minecraft.spruce_button": "云杉木按钮", "block.minecraft.spruce_door": "云杉木门", "block.minecraft.spruce_fence": "云杉木栅栏", "block.minecraft.spruce_fence_gate": "云杉木栅栏门", "block.minecraft.spruce_hanging_sign": "悬挂式云杉木告示牌", "block.minecraft.spruce_leaves": "云杉树叶", "block.minecraft.spruce_log": "云杉原木", "block.minecraft.spruce_planks": "云杉木板", "block.minecraft.spruce_pressure_plate": "云杉木压力板", "block.minecraft.spruce_sapling": "云杉树苗", "block.minecraft.spruce_sign": "云杉木告示牌", "block.minecraft.spruce_slab": "云杉木台阶", "block.minecraft.spruce_stairs": "云杉木楼梯", "block.minecraft.spruce_trapdoor": "云杉木活板门", "block.minecraft.spruce_wall_hanging_sign": "墙上的悬挂式云杉木告示牌", "block.minecraft.spruce_wall_sign": "墙上的云杉木告示牌", "block.minecraft.spruce_wood": "云杉木", "block.minecraft.sticky_piston": "黏性活塞", "block.minecraft.stone": "石头", "block.minecraft.stone_brick_slab": "石砖台阶", "block.minecraft.stone_brick_stairs": "石砖楼梯", "block.minecraft.stone_brick_wall": "石砖墙", "block.minecraft.stone_bricks": "石砖", "block.minecraft.stone_button": "石头按钮", "block.minecraft.stone_pressure_plate": "石头压力板", "block.minecraft.stone_slab": "石头台阶", "block.minecraft.stone_stairs": "石头楼梯", "block.minecraft.stonecutter": "切石机", "block.minecraft.stripped_acacia_log": "去皮金合欢原木", "block.minecraft.stripped_acacia_wood": "去皮金合欢木", "block.minecraft.stripped_bamboo_block": "去皮竹块", "block.minecraft.stripped_birch_log": "去皮白桦原木", "block.minecraft.stripped_birch_wood": "去皮白桦木", "block.minecraft.stripped_cherry_log": "去皮樱花原木", "block.minecraft.stripped_cherry_wood": "去皮樱花木", "block.minecraft.stripped_crimson_hyphae": "去皮绯红菌核", "block.minecraft.stripped_crimson_stem": "去皮绯红菌柄", "block.minecraft.stripped_dark_oak_log": "去皮深色橡木原木", "block.minecraft.stripped_dark_oak_wood": "去皮深色橡木", "block.minecraft.stripped_jungle_log": "去皮丛林原木", "block.minecraft.stripped_jungle_wood": "去皮丛林木", "block.minecraft.stripped_mangrove_log": "去皮红树原木", "block.minecraft.stripped_mangrove_wood": "去皮红树木", "block.minecraft.stripped_oak_log": "去皮橡木原木", "block.minecraft.stripped_oak_wood": "去皮橡木", "block.minecraft.stripped_pale_oak_log": "去皮苍白橡木原木", "block.minecraft.stripped_pale_oak_wood": "去皮苍白橡木", "block.minecraft.stripped_spruce_log": "去皮云杉原木", "block.minecraft.stripped_spruce_wood": "去皮云杉木", "block.minecraft.stripped_warped_hyphae": "去皮诡异菌核", "block.minecraft.stripped_warped_stem": "去皮诡异菌柄", "block.minecraft.structure_block": "结构方块", "block.minecraft.structure_void": "结构空位", "block.minecraft.sugar_cane": "甘蔗", "block.minecraft.sunflower": "向日葵", "block.minecraft.suspicious_gravel": "可疑的沙砾", "block.minecraft.suspicious_sand": "可疑的沙子", "block.minecraft.sweet_berry_bush": "甜浆果丛", "block.minecraft.tall_dry_grass": "高枯草丛", "block.minecraft.tall_grass": "高草丛", "block.minecraft.tall_seagrass": "高海草", "block.minecraft.target": "标靶", "block.minecraft.terracotta": "陶瓦", "block.minecraft.test_block": "测试方块", "block.minecraft.test_instance_block": "测试实例方块", "block.minecraft.tinted_glass": "遮光玻璃", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT爆炸已禁用", "block.minecraft.torch": "火把", "block.minecraft.torchflower": "火把花", "block.minecraft.torchflower_crop": "火把花植株", "block.minecraft.trapped_chest": "陷阱箱", "block.minecraft.trial_spawner": "试炼刷怪笼", "block.minecraft.tripwire": "绊线", "block.minecraft.tripwire_hook": "绊线钩", "block.minecraft.tube_coral": "管珊瑚", "block.minecraft.tube_coral_block": "管珊瑚块", "block.minecraft.tube_coral_fan": "管珊瑚扇", "block.minecraft.tube_coral_wall_fan": "墙上的管珊瑚扇", "block.minecraft.tuff": "凝灰岩", "block.minecraft.tuff_brick_slab": "凝灰岩砖台阶", "block.minecraft.tuff_brick_stairs": "凝灰岩砖楼梯", "block.minecraft.tuff_brick_wall": "凝灰岩砖墙", "block.minecraft.tuff_bricks": "凝灰岩砖", "block.minecraft.tuff_slab": "凝灰岩台阶", "block.minecraft.tuff_stairs": "凝灰岩楼梯", "block.minecraft.tuff_wall": "凝灰岩墙", "block.minecraft.turtle_egg": "海龟蛋", "block.minecraft.twisting_vines": "缠怨藤", "block.minecraft.twisting_vines_plant": "缠怨藤植株", "block.minecraft.vault": "宝库", "block.minecraft.verdant_froglight": "青翠蛙明灯", "block.minecraft.vine": "藤蔓", "block.minecraft.void_air": "虚空空气", "block.minecraft.wall_torch": "墙上的火把", "block.minecraft.warped_button": "诡异木按钮", "block.minecraft.warped_door": "诡异木门", "block.minecraft.warped_fence": "诡异木栅栏", "block.minecraft.warped_fence_gate": "诡异木栅栏门", "block.minecraft.warped_fungus": "诡异菌", "block.minecraft.warped_hanging_sign": "悬挂式诡异木告示牌", "block.minecraft.warped_hyphae": "诡异菌核", "block.minecraft.warped_nylium": "诡异菌岩", "block.minecraft.warped_planks": "诡异木板", "block.minecraft.warped_pressure_plate": "诡异木压力板", "block.minecraft.warped_roots": "诡异菌索", "block.minecraft.warped_sign": "诡异木告示牌", "block.minecraft.warped_slab": "诡异木台阶", "block.minecraft.warped_stairs": "诡异木楼梯", "block.minecraft.warped_stem": "诡异菌柄", "block.minecraft.warped_trapdoor": "诡异木活板门", "block.minecraft.warped_wall_hanging_sign": "墙上的悬挂式诡异木告示牌", "block.minecraft.warped_wall_sign": "墙上的诡异木告示牌", "block.minecraft.warped_wart_block": "诡异疣块", "block.minecraft.water": "水", "block.minecraft.water_cauldron": "装有水的炼药锅", "block.minecraft.waxed_chiseled_copper": "涂蜡的雕纹铜块", "block.minecraft.waxed_copper_block": "涂蜡的铜块", "block.minecraft.waxed_copper_bulb": "涂蜡的铜灯", "block.minecraft.waxed_copper_door": "涂蜡的铜门", "block.minecraft.waxed_copper_grate": "涂蜡的铜格栅", "block.minecraft.waxed_copper_trapdoor": "涂蜡的铜活板门", "block.minecraft.waxed_cut_copper": "涂蜡的切制铜块", "block.minecraft.waxed_cut_copper_slab": "涂蜡的切制铜台阶", "block.minecraft.waxed_cut_copper_stairs": "涂蜡的切制铜楼梯", "block.minecraft.waxed_exposed_chiseled_copper": "涂蜡的斑驳雕纹铜块", "block.minecraft.waxed_exposed_copper": "涂蜡的斑驳铜块", "block.minecraft.waxed_exposed_copper_bulb": "涂蜡的斑驳铜灯", "block.minecraft.waxed_exposed_copper_door": "涂蜡的斑驳铜门", "block.minecraft.waxed_exposed_copper_grate": "涂蜡的斑驳铜格栅", "block.minecraft.waxed_exposed_copper_trapdoor": "涂蜡的斑驳铜活板门", "block.minecraft.waxed_exposed_cut_copper": "涂蜡的斑驳切制铜块", "block.minecraft.waxed_exposed_cut_copper_slab": "涂蜡的斑驳切制铜台阶", "block.minecraft.waxed_exposed_cut_copper_stairs": "涂蜡的斑驳切制铜楼梯", "block.minecraft.waxed_oxidized_chiseled_copper": "涂蜡的氧化雕纹铜块", "block.minecraft.waxed_oxidized_copper": "涂蜡的氧化铜块", "block.minecraft.waxed_oxidized_copper_bulb": "涂蜡的氧化铜灯", "block.minecraft.waxed_oxidized_copper_door": "涂蜡的氧化铜门", "block.minecraft.waxed_oxidized_copper_grate": "涂蜡的氧化铜格栅", "block.minecraft.waxed_oxidized_copper_trapdoor": "涂蜡的氧化铜活板门", "block.minecraft.waxed_oxidized_cut_copper": "涂蜡的氧化切制铜块", "block.minecraft.waxed_oxidized_cut_copper_slab": "涂蜡的氧化切制铜台阶", "block.minecraft.waxed_oxidized_cut_copper_stairs": "涂蜡的氧化切制铜楼梯", "block.minecraft.waxed_weathered_chiseled_copper": "涂蜡的锈蚀雕纹铜块", "block.minecraft.waxed_weathered_copper": "涂蜡的锈蚀铜块", "block.minecraft.waxed_weathered_copper_bulb": "涂蜡的锈蚀铜灯", "block.minecraft.waxed_weathered_copper_door": "涂蜡的锈蚀铜门", "block.minecraft.waxed_weathered_copper_grate": "涂蜡的锈蚀铜格栅", "block.minecraft.waxed_weathered_copper_trapdoor": "涂蜡的锈蚀铜活板门", "block.minecraft.waxed_weathered_cut_copper": "涂蜡的锈蚀切制铜块", "block.minecraft.waxed_weathered_cut_copper_slab": "涂蜡的锈蚀切制铜台阶", "block.minecraft.waxed_weathered_cut_copper_stairs": "涂蜡的锈蚀切制铜楼梯", "block.minecraft.weathered_chiseled_copper": "锈蚀的雕纹铜块", "block.minecraft.weathered_copper": "锈蚀的铜块", "block.minecraft.weathered_copper_bulb": "锈蚀的铜灯", "block.minecraft.weathered_copper_door": "锈蚀的铜门", "block.minecraft.weathered_copper_grate": "锈蚀的铜格栅", "block.minecraft.weathered_copper_trapdoor": "锈蚀的铜活板门", "block.minecraft.weathered_cut_copper": "锈蚀的切制铜块", "block.minecraft.weathered_cut_copper_slab": "锈蚀的切制铜台阶", "block.minecraft.weathered_cut_copper_stairs": "锈蚀的切制铜楼梯", "block.minecraft.weeping_vines": "垂泪藤", "block.minecraft.weeping_vines_plant": "垂泪藤植株", "block.minecraft.wet_sponge": "湿海绵", "block.minecraft.wheat": "小麦植株", "block.minecraft.white_banner": "白色旗帜", "block.minecraft.white_bed": "白色床", "block.minecraft.white_candle": "白色蜡烛", "block.minecraft.white_candle_cake": "插上白色蜡烛的蛋糕", "block.minecraft.white_carpet": "白色地毯", "block.minecraft.white_concrete": "白色混凝土", "block.minecraft.white_concrete_powder": "白色混凝土粉末", "block.minecraft.white_glazed_terracotta": "白色带釉陶瓦", "block.minecraft.white_shulker_box": "白色潜影盒", "block.minecraft.white_stained_glass": "白色染色玻璃", "block.minecraft.white_stained_glass_pane": "白色染色玻璃板", "block.minecraft.white_terracotta": "白色陶瓦", "block.minecraft.white_tulip": "白色郁金香", "block.minecraft.white_wool": "白色羊毛", "block.minecraft.wildflowers": "野花簇", "block.minecraft.wither_rose": "凋灵玫瑰", "block.minecraft.wither_skeleton_skull": "凋灵骷髅头颅", "block.minecraft.wither_skeleton_wall_skull": "墙上的凋灵骷髅头颅", "block.minecraft.yellow_banner": "黄色旗帜", "block.minecraft.yellow_bed": "黄色床", "block.minecraft.yellow_candle": "黄色蜡烛", "block.minecraft.yellow_candle_cake": "插上黄色蜡烛的蛋糕", "block.minecraft.yellow_carpet": "黄色地毯", "block.minecraft.yellow_concrete": "黄色混凝土", "block.minecraft.yellow_concrete_powder": "黄色混凝土粉末", "block.minecraft.yellow_glazed_terracotta": "黄色带釉陶瓦", "block.minecraft.yellow_shulker_box": "黄色潜影盒", "block.minecraft.yellow_stained_glass": "黄色染色玻璃", "block.minecraft.yellow_stained_glass_pane": "黄色染色玻璃板", "block.minecraft.yellow_terracotta": "黄色陶瓦", "block.minecraft.yellow_wool": "黄色羊毛", "block.minecraft.zombie_head": "僵尸的头", "block.minecraft.zombie_wall_head": "墙上的僵尸的头", "book.byAuthor": "%1$s 著", "book.edit.title": "书本编辑屏幕", "book.editTitle": "输入书名：", "book.finalizeButton": "署名并关闭", "book.finalizeWarning": "注意！在你署名后，它将不能再被修改。", "book.generation.0": "原稿", "book.generation.1": "原稿的副本", "book.generation.2": "副本的副本", "book.generation.3": "破烂不堪", "book.invalid.tag": "* 无效的书本标签 *", "book.pageIndicator": "第%1$s页/共%2$s页", "book.page_button.next": "下一页", "book.page_button.previous": "上一页", "book.sign.title": "书本署名屏幕", "book.sign.titlebox": "标题", "book.signButton": "署名", "book.view.title": "书本浏览屏幕", "build.tooHigh": "建筑高度限制是%s", "chat.cannotSend": "无法发送聊天消息", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "点此传送", "chat.copy": "复制到剪贴板", "chat.copy.click": "单击复制到剪贴板", "chat.deleted_marker": "此聊天消息已被服务器删除。", "chat.disabled.chain_broken": "由于消息链损坏，聊天已被禁用。请尝试重新连接。", "chat.disabled.expiredProfileKey": "由于个人信息公钥过期，聊天已被禁用。请尝试重新连接。", "chat.disabled.invalid_command_signature": "命令包含意外或缺失的命令参数签名。", "chat.disabled.invalid_signature": "聊天签名无效。请尝试重新连接。", "chat.disabled.launcher": "聊天在启动器选项中被禁用。无法发送消息。", "chat.disabled.missingProfileKey": "由于个人信息公钥丢失，聊天已被禁用。请尝试重新连接。", "chat.disabled.options": "聊天在客户端选项中被禁用。", "chat.disabled.out_of_order_chat": "接收到了乱序的聊天消息。你的系统时间是否被更改过？", "chat.disabled.profile": "聊天在账户设置中被禁用。再按一次“%s”以获取更多信息。", "chat.disabled.profile.moreInfo": "聊天在账户设置中被禁用。无法发送或查看消息。", "chat.editBox": "聊天", "chat.filtered": "被服务器过滤。", "chat.filtered_full": "服务器已向部分玩家隐藏了你的消息。", "chat.link.confirm": "你确定要打开以下网页？", "chat.link.confirmTrusted": "你想要打开这个链接或将其复制到你的剪贴板吗？", "chat.link.open": "在浏览器中打开", "chat.link.warning": "永远不要打开你不信任的人提供的链接！", "chat.queue": "[+%s行待发送]", "chat.square_brackets": "[%s]", "chat.tag.error": "服务器发送了无效消息。", "chat.tag.modified": "消息被服务器修改。原文：", "chat.tag.not_secure": "未经验证的消息。无法举报。", "chat.tag.system": "服务器消息。无法举报。", "chat.tag.system_single_player": "服务器消息。", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s完成了挑战%s", "chat.type.advancement.goal": "%s达成了目标%s", "chat.type.advancement.task": "%s取得了进度%s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "发送队伍消息", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s说%s", "chat.validation_error": "聊天验证错误", "chat_screen.message": "要发送的消息：%s", "chat_screen.title": "聊天屏幕", "chat_screen.usage": "输入消息并按Enter键发送", "chunk.toast.checkLog": "查看日志获取详情", "chunk.toast.loadFailure": "无法加载%s处的区块", "chunk.toast.lowDiskSpace": "磁盘空间不足！", "chunk.toast.lowDiskSpace.description": "可能无法保存世界。", "chunk.toast.saveFailure": "无法保存%s处的区块", "clear.failed.multiple": "未能从%s名玩家找到任何物品", "clear.failed.single": "未能从玩家%s找到任何物品", "color.minecraft.black": "黑色", "color.minecraft.blue": "蓝色", "color.minecraft.brown": "棕色", "color.minecraft.cyan": "青色", "color.minecraft.gray": "灰色", "color.minecraft.green": "绿色", "color.minecraft.light_blue": "淡蓝色", "color.minecraft.light_gray": "淡灰色", "color.minecraft.lime": "黄绿色", "color.minecraft.magenta": "品红色", "color.minecraft.orange": "橙色", "color.minecraft.pink": "粉红色", "color.minecraft.purple": "紫色", "color.minecraft.red": "红色", "color.minecraft.white": "白色", "color.minecraft.yellow": "黄色", "command.context.here": "<--[此处]", "command.context.parse_error": "%s，位于第%s个字符：%s", "command.exception": "无法解析命令：%s", "command.expected.separator": "参数后应有空格分隔，但发现了紧邻的数据", "command.failed": "试图执行该命令时出现意外错误", "command.forkLimit": "上下文数量已达上限值（%s）", "command.unknown.argument": "错误的命令参数", "command.unknown.command": "未知或不完整的命令，错误见下", "commands.advancement.criterionNotFound": "进度%1$s并不包含条件“%2$s”", "commands.advancement.grant.criterion.to.many.failure": "无法将进度%2$s的达成条件“%1$s”赋予%3$s名玩家，因为他们已达成此条件", "commands.advancement.grant.criterion.to.many.success": "已将进度%2$s的达成条件“%1$s”赋予%3$s名玩家", "commands.advancement.grant.criterion.to.one.failure": "无法将进度%2$s的达成条件“%1$s”赋予%3$s，因为该玩家已达成此条件", "commands.advancement.grant.criterion.to.one.success": "已将进度%2$s的达成条件“%1$s”赋予%3$s", "commands.advancement.grant.many.to.many.failure": "无法将%s项进度赋予%s名玩家，因为他们已达成这些进度", "commands.advancement.grant.many.to.many.success": "已将%s项进度赋予%s名玩家", "commands.advancement.grant.many.to.one.failure": "无法将%s项进度赋予%s，因为该玩家已达成这些进度", "commands.advancement.grant.many.to.one.success": "已将%s项进度赋予%s", "commands.advancement.grant.one.to.many.failure": "无法将进度%s赋予%s名玩家，因为他们已达成此进度", "commands.advancement.grant.one.to.many.success": "已将进度%s赋予%s名玩家", "commands.advancement.grant.one.to.one.failure": "无法将进度%s赋予%s，因为该玩家已达成此进度", "commands.advancement.grant.one.to.one.success": "已将进度%s赋予%s", "commands.advancement.revoke.criterion.to.many.failure": "无法撤销%3$s名玩家关于进度%2$s的达成条件“%1$s”，因为他们并未达成此条件", "commands.advancement.revoke.criterion.to.many.success": "已撤销%3$s名玩家关于进度%2$s的达成条件“%1$s”", "commands.advancement.revoke.criterion.to.one.failure": "无法撤销%3$s关于进度%2$s的达成条件“%1$s”，因为该玩家并未达成此条件", "commands.advancement.revoke.criterion.to.one.success": "已撤销%3$s关于进度%2$s的达成条件“%1$s”", "commands.advancement.revoke.many.to.many.failure": "无法撤销%2$s名玩家的%1$s项进度，因为他们并未达成此进度", "commands.advancement.revoke.many.to.many.success": "已撤销%2$s名玩家的%1$s项进度", "commands.advancement.revoke.many.to.one.failure": "无法撤销%2$s的%1$s项进度，因为该玩家并未达成这些进度", "commands.advancement.revoke.many.to.one.success": "已撤销%2$s的%1$s项进度", "commands.advancement.revoke.one.to.many.failure": "无法撤销%2$s名玩家的进度%1$s，因为他们并未达成此进度", "commands.advancement.revoke.one.to.many.success": "已撤销%2$s名玩家的进度%1$s", "commands.advancement.revoke.one.to.one.failure": "无法撤销%2$s的进度%1$s，因为该玩家并未达成此进度", "commands.advancement.revoke.one.to.one.success": "已撤销%2$s的进度%1$s", "commands.attribute.base_value.get.success": "实体%2$s的属性%1$s的基值为%3$s", "commands.attribute.base_value.reset.success": "实体%2$s的属性%1$s的基值已重置为默认值%3$s", "commands.attribute.base_value.set.success": "实体%2$s的属性%1$s的基值已设置为%3$s", "commands.attribute.failed.entity": "%s不是此命令的有效实体", "commands.attribute.failed.modifier_already_present": "实体%3$s的属性%2$s已存在修饰符%1$s", "commands.attribute.failed.no_attribute": "实体%s没有属性%s", "commands.attribute.failed.no_modifier": "实体%2$s的属性%1$s无修饰符%3$s", "commands.attribute.modifier.add.success": "为实体%3$s的属性%2$s添加了修饰符%1$s", "commands.attribute.modifier.remove.success": "为实体%3$s的属性%2$s移除了修饰符%1$s", "commands.attribute.modifier.value.get.success": "实体%3$s的属性%2$s中修饰符%1$s值为%4$s", "commands.attribute.value.get.success": "实体%2$s的属性%1$s的值为%3$s", "commands.ban.failed": "无变化，该玩家已被封禁", "commands.ban.success": "已封禁%s：%s", "commands.banip.failed": "无变化，该IP地址已被封禁", "commands.banip.info": "此次封禁涉及%s名玩家：%s", "commands.banip.invalid": "无效的IP地址或未知的玩家", "commands.banip.success": "已封禁IP地址%s：%s", "commands.banlist.entry": "%s被%s封禁：%s", "commands.banlist.entry.unknown": "（未知）", "commands.banlist.list": "共有%s条封禁：", "commands.banlist.none": "没有玩家被封禁", "commands.bossbar.create.failed": "ID为“%s”的Boss栏已经存在", "commands.bossbar.create.success": "已创建自定义Boss栏%s", "commands.bossbar.get.max": "自定义Boss栏%s的最大值为%s", "commands.bossbar.get.players.none": "自定义Boss栏%s目前没有在线玩家", "commands.bossbar.get.players.some": "自定义Boss栏%s当前在线的%s名玩家有：%s", "commands.bossbar.get.value": "自定义Boss栏%s的数值为%s", "commands.bossbar.get.visible.hidden": "自定义Boss栏%s现为隐藏", "commands.bossbar.get.visible.visible": "自定义Boss栏%s现为可见", "commands.bossbar.list.bars.none": "无运行中的自定义Boss栏", "commands.bossbar.list.bars.some": "有%s项运行中的自定义Boss栏：%s", "commands.bossbar.remove.success": "已移除自定义Boss栏%s", "commands.bossbar.set.color.success": "已更改自定义Boss栏%s的颜色", "commands.bossbar.set.color.unchanged": "无变化，这本就是这个Boss栏的颜色", "commands.bossbar.set.max.success": "自定义Boss栏%s的最大值已改为%s", "commands.bossbar.set.max.unchanged": "无变化，这本就是这个Boss栏的最大值", "commands.bossbar.set.name.success": "已重命名自定义Boss栏%s", "commands.bossbar.set.name.unchanged": "无变化，这本就是这个Boss栏的名称", "commands.bossbar.set.players.success.none": "自定义Boss栏%s在当下没有在线玩家", "commands.bossbar.set.players.success.some": "自定义Boss栏%s在当下在线的%s名玩家有：%s", "commands.bossbar.set.players.unchanged": "无变化，这些玩家已经在Boss栏上，没有玩家需要被添加或移除", "commands.bossbar.set.style.success": "已改变自定义Boss栏%s的样式", "commands.bossbar.set.style.unchanged": "无变化，这本就是这个Boss栏的样式", "commands.bossbar.set.value.success": "自定义Boss栏%s的值已改为%s", "commands.bossbar.set.value.unchanged": "无变化，这本就是这个Boss栏的值", "commands.bossbar.set.visibility.unchanged.hidden": "无变化，该Boss栏原已隐藏", "commands.bossbar.set.visibility.unchanged.visible": "无变化，该Boss栏原已可见", "commands.bossbar.set.visible.success.hidden": "已将自定义Boss栏%s改为隐藏", "commands.bossbar.set.visible.success.visible": "已将自定义Boss栏%s改为可见", "commands.bossbar.unknown": "不存在ID为“%s”的Boss栏", "commands.clear.success.multiple": "已移除%2$s名玩家的%1$s个物品", "commands.clear.success.single": "已移除玩家%2$s的%1$s个物品", "commands.clear.test.multiple": "已在%2$s名玩家身上找到%1$s个匹配的物品", "commands.clear.test.single": "已在玩家%2$s身上找到%1$s个匹配的物品", "commands.clone.failed": "未复制任何方块", "commands.clone.overlap": "源区域和目标区域不能重叠", "commands.clone.success": "已成功复制%s个方块", "commands.clone.toobig": "指定区域内的方块太多（最大值为%s，指定值为%s）", "commands.damage.invulnerable": "对象免疫指定的伤害类型", "commands.damage.success": "已将%s伤害施加于%s", "commands.data.block.get": "位于%2$s, %3$s, %4$s的方块的%1$s乘以%5$s倍率后的值为%6$s", "commands.data.block.invalid": "目标方块不是方块实体", "commands.data.block.modified": "已修改%s, %s, %s处的方块数据", "commands.data.block.query": "%s, %s, %s拥有以下方块数据：%s", "commands.data.entity.get": "%2$s的%1$s在乘以倍率%3$s后的值是%4$s", "commands.data.entity.invalid": "无法修改玩家数据", "commands.data.entity.modified": "已修改%s的实体数据", "commands.data.entity.query": "%s拥有以下实体数据：%s", "commands.data.get.invalid": "无法获取%s，只接受数字标签", "commands.data.get.multiple": "该参数只接受单个NBT值", "commands.data.get.unknown": "无法获取%s，标签不存在", "commands.data.merge.failed": "无变化，所指定的属性已有这些值", "commands.data.modify.expected_list": "应为列表，实际为：%s", "commands.data.modify.expected_object": "应为对象，实际为：%s", "commands.data.modify.expected_value": "应为值，实际为：%s", "commands.data.modify.invalid_index": "无效的列表索引：%s", "commands.data.modify.invalid_substring": "无效的子串索引：%s到%s", "commands.data.storage.get": "在存储%2$s中的%1$s在乘以倍率%3$s后的值是%4$s", "commands.data.storage.modified": "已修改存储%s", "commands.data.storage.query": "存储%s含有以下内容：%s", "commands.datapack.create.already_exists": "名为“%s”的包已经存在", "commands.datapack.create.invalid_full_name": "无效的新包名“%s”", "commands.datapack.create.invalid_name": "新包名“%s”中包含无效字符", "commands.datapack.create.io_failure": "无法创建名称为“%s”的包，请检查日志", "commands.datapack.create.metadata_encode_failure": "无法编码名称为“%s”的包的元数据：%s", "commands.datapack.create.success": "已创建名为“%s”的空包", "commands.datapack.disable.failed": "数据包“%s”并未启用！", "commands.datapack.disable.failed.feature": "无法禁用数据包“%s”，因为它属于已启用功能！", "commands.datapack.enable.failed": "数据包“%s”已经启用！", "commands.datapack.enable.failed.no_flags": "无法启用数据包“%s”，因为所需的功能未在此世界启用：%s！", "commands.datapack.list.available.none": "已无更多可用的数据包", "commands.datapack.list.available.success": "共有%s个数据包可用：%s", "commands.datapack.list.enabled.none": "没有启用中的数据包", "commands.datapack.list.enabled.success": "已启用%s个数据包：%s", "commands.datapack.modify.disable": "正在禁用数据包%s", "commands.datapack.modify.enable": "正在启用数据包%s", "commands.datapack.unknown": "未知的数据包 “%s”", "commands.debug.alreadyRunning": "刻分析器原已开启", "commands.debug.function.noRecursion": "无法从函数内部开始追踪", "commands.debug.function.noReturnRun": "使用/return run时无法追踪", "commands.debug.function.success.multiple": "已追踪%2$s个函数内的%1$s条命令至输出文件%3$s", "commands.debug.function.success.single": "已追踪函数“%2$s”内的%1$s条命令至输出文件%3$s", "commands.debug.function.traceFailed": "追踪函数失败", "commands.debug.notRunning": "尚未启动刻分析器", "commands.debug.started": "已开始刻分析", "commands.debug.stopped": "已停止刻分析，用时%s秒和%s刻（每秒%s刻）", "commands.defaultgamemode.success": "默认游戏模式现在为%s", "commands.deop.failed": "无变化，此玩家不是管理员", "commands.deop.success": "%s不再是服务器管理员了", "commands.dialog.clear.multiple": "已清除%s名玩家的对话框", "commands.dialog.clear.single": "已清除%s的对话框", "commands.dialog.show.multiple": "已将对话框展示给%s名玩家", "commands.dialog.show.single": "已将对话框展示给%s", "commands.difficulty.failure": "难度未变，它原来就是%s", "commands.difficulty.query": "目前难度为%s", "commands.difficulty.success": "难度已被设置为%s", "commands.drop.no_held_items": "该实体无法持有任何物品", "commands.drop.no_loot_table": "实体%s没有战利品表", "commands.drop.no_loot_table.block": "方块%s没有战利品表", "commands.drop.success.multiple": "掉落了%s个物品", "commands.drop.success.multiple_with_table": "从战利品表%2$s中掉落了%1$s个物品", "commands.drop.success.single": "掉落了%s个%s", "commands.drop.success.single_with_table": "从战利品表%3$s中掉落了%1$s个%2$s", "commands.effect.clear.everything.failed": "对象没有可以移除的效果", "commands.effect.clear.everything.success.multiple": "已移除%s个对象的所有效果", "commands.effect.clear.everything.success.single": "已移除%s的所有效果", "commands.effect.clear.specific.failed": "对象没有指定效果", "commands.effect.clear.specific.success.multiple": "已移除%2$s个对象的%1$s效果", "commands.effect.clear.specific.success.single": "已移除%2$s的%1$s效果", "commands.effect.give.failed": "无法应用此效果（对象对效果免疫，或已有更强的效果）", "commands.effect.give.success.multiple": "已将%s效果应用于%s个对象", "commands.effect.give.success.single": "已将%s效果应用于%s", "commands.enchant.failed": "无变化，对象未持有物品或其不支持此魔咒", "commands.enchant.failed.entity": "%s不是此命令的有效实体", "commands.enchant.failed.incompatible": "%s不支持此魔咒", "commands.enchant.failed.itemless": "%s未手持任何物品", "commands.enchant.failed.level": "%s高于该魔咒支持的最高等级%s", "commands.enchant.success.multiple": "已将%s魔咒应用于%s个实体", "commands.enchant.success.single": "已将%s魔咒应用于%s的物品上", "commands.execute.blocks.toobig": "指定区域内的方块太多（最大值为%s，指定值为%s）", "commands.execute.conditional.fail": "测试失败", "commands.execute.conditional.fail_count": "测试失败，计数：%s", "commands.execute.conditional.pass": "测试通过", "commands.execute.conditional.pass_count": "测试通过，计数：%s", "commands.execute.function.instantiationFailure": "无法实例化函数%s：%s", "commands.experience.add.levels.success.multiple": "已给予%2$s名玩家%1$s级经验", "commands.experience.add.levels.success.single": "已给予%2$s %1$s级经验", "commands.experience.add.points.success.multiple": "已给予%2$s名玩家%1$s点经验", "commands.experience.add.points.success.single": "已给予%2$s %1$s点经验", "commands.experience.query.levels": "%s拥有%s级经验", "commands.experience.query.points": "%s拥有%s点经验", "commands.experience.set.levels.success.multiple": "已将%2$s名玩家的经验等级设为%1$s", "commands.experience.set.levels.success.single": "已将%2$s的经验等级设为%1$s", "commands.experience.set.points.invalid": "无法将此玩家的经验值设置为超过现有等级的最大值", "commands.experience.set.points.success.multiple": "已将%2$s名玩家的经验值设为%1$s", "commands.experience.set.points.success.single": "已将%2$s的经验值设为%1$s", "commands.fill.failed": "没有方块被填充", "commands.fill.success": "已成功填充%s个方块", "commands.fill.toobig": "指定区域内的方块太多（最大值为%s，指定值为%s）", "commands.fillbiome.success": "已在%s, %s, %s与%s, %s, %s之间设置生物群系", "commands.fillbiome.success.count": "已在%2$s, %3$s, %4$s与%5$s, %6$s, %7$s之间设置%1$s个生物群系单元", "commands.fillbiome.toobig": "指定范围内的方块过多（最大值为%s，指定值为%s）", "commands.forceload.added.failure": "没有被标记为强制加载的区块", "commands.forceload.added.multiple": "已将%2$s中的%3$s至%4$s间的%1$s个区块标记为强制加载", "commands.forceload.added.none": "在%s中未找到强制加载的区块", "commands.forceload.added.single": "已将%2$s中的区块%1$s标记为强制加载", "commands.forceload.list.multiple": "在%2$s内找到%1$s个强制加载的区块：%3$s", "commands.forceload.list.single": "在%s内找到一个强制加载的区块：%s", "commands.forceload.query.failure": "在%2$s中%1$s内的区块未被标记为强制加载", "commands.forceload.query.success": "在%2$s中%1$s内的区块被标记为强制加载", "commands.forceload.removed.all": "已解除标记%s内所有的强制加载区块", "commands.forceload.removed.failure": "没有强制加载的区块被移除", "commands.forceload.removed.multiple": "已将%2$s中的%3$s至%4$s间的%1$s个区块取消强制加载", "commands.forceload.removed.single": "已将%2$s中的区块%1$s解除强制加载", "commands.forceload.toobig": "指定区域内区块过多（最大值为%s，指定值为%s）", "commands.function.error.argument_not_compound": "无效的参数类型：%s，应为Compound类型", "commands.function.error.missing_argument": "函数%1$s缺少传入参数%2$s", "commands.function.error.missing_arguments": "函数%s缺少传入参数", "commands.function.error.parse": "实例化宏%s时：命令“%s”发生错误：%s", "commands.function.instantiationFailure": "无法实例化函数%s：%s", "commands.function.result": "函数%s返回了%s", "commands.function.scheduled.multiple": "正在运行函数%s", "commands.function.scheduled.no_functions": "无法找到名称为%s的函数", "commands.function.scheduled.single": "正在运行函数%s", "commands.function.success.multiple": "已执行%2$s个函数中的%1$s条命令", "commands.function.success.multiple.result": "执行了%s个函数", "commands.function.success.single": "已执行函数%2$s中的%1$s条命令", "commands.function.success.single.result": "函数“%2$s”返回了%1$s", "commands.gamemode.success.other": "已将%s的游戏模式改为%s", "commands.gamemode.success.self": "已将自己的游戏模式设置为%s", "commands.gamerule.query": "游戏规则%s目前为：%s", "commands.gamerule.set": "游戏规则%s已被设为：%s", "commands.give.failed.toomanyitems": "最多只能给予%s个%s", "commands.give.success.multiple": "已将%s个%s给予%s名玩家", "commands.give.success.single": "已将%s个%s给予%s", "commands.help.failed": "未知的命令或权限不足", "commands.item.block.set.success": "已用%4$s替换了位于%1$s, %2$s, %3$s的槽位", "commands.item.entity.set.success.multiple": "已用%2$s替换了%1$s个实体的槽位", "commands.item.entity.set.success.single": "已用%2$s替换了%1$s的槽位", "commands.item.source.no_such_slot": "来源没有%s槽位", "commands.item.source.not_a_container": "在来源位置%s, %s, %s上的不是一个容器", "commands.item.target.no_changed.known_item": "没有对象在%2$s槽位接受了物品%1$s", "commands.item.target.no_changes": "没有对象在%s槽位接受了物品", "commands.item.target.no_such_slot": "对象没有%s槽位", "commands.item.target.not_a_container": "在目标位置%s, %s, %s上的不是一个容器", "commands.jfr.dump.failed": "无法转储JFR记录：%s", "commands.jfr.start.failed": "无法开始JFR分析", "commands.jfr.started": "已开始JFR分析", "commands.jfr.stopped": "JFR分析已结束，已转储至%s", "commands.kick.owner.failed": "无法在局域网游戏中踢出服务器所有者", "commands.kick.singleplayer.failed": "无法在离线单人游戏中踢出玩家", "commands.kick.success": "已踢出%s：%s", "commands.kill.success.multiple": "杀死了%s个实体", "commands.kill.success.single": "杀死了%s", "commands.list.nameAndId": "%s（%s）", "commands.list.players": "当前共有%s名玩家在线（最大玩家数为%s）：%s", "commands.locate.biome.not_found": "无法在合理距离内找到类型为“%s”的生物群系", "commands.locate.biome.success": "最近的%s位于%s（%s个方块外）", "commands.locate.poi.not_found": "无法在合理距离内找到类型为“%s”的兴趣点", "commands.locate.poi.success": "最近的%s位于%s（%s个方块外）", "commands.locate.structure.invalid": "没有类型为“%s”的结构", "commands.locate.structure.not_found": "无法在附近找到类型为“%s”的结构", "commands.locate.structure.success": "最近的%s位于%s（%s个方块外）", "commands.message.display.incoming": "%s悄悄地对你说：%s", "commands.message.display.outgoing": "你悄悄地对%s说：%s", "commands.op.failed": "无变化，此玩家已是管理员", "commands.op.success": "已将%s设为服务器管理员", "commands.pardon.failed": "无变化，该玩家未被封禁", "commands.pardon.success": "已解封%s", "commands.pardonip.failed": "无变化，该IP地址未被封禁", "commands.pardonip.invalid": "无效的IP地址", "commands.pardonip.success": "已解封IP地址%s", "commands.particle.failed": "该粒子无法被任何玩家看见", "commands.particle.success": "正在显示粒子%s", "commands.perf.alreadyRunning": "性能分析器已在运行", "commands.perf.notRunning": "性能分析器尚未启动", "commands.perf.reportFailed": "生成调试报告失败", "commands.perf.reportSaved": "已在%s生成调试报告", "commands.perf.started": "已开始时长为10秒的性能分析测试（使用“/perf stop”以提前结束）", "commands.perf.stopped": "已停止性能分析，用时%s秒和%s刻（每秒%s刻）", "commands.place.feature.failed": "放置地物失败", "commands.place.feature.invalid": "没有类型为“%s”的地物", "commands.place.feature.success": "已在%2$s, %3$s, %4$s处放置“%1$s”", "commands.place.jigsaw.failed": "生成拼图失败", "commands.place.jigsaw.invalid": "没有类型为“%s”的模板池", "commands.place.jigsaw.success": "已在%s, %s, %s处生成拼图", "commands.place.structure.failed": "放置结构失败", "commands.place.structure.invalid": "没有类型为“%s”的结构", "commands.place.structure.success": "已在%2$s, %3$s, %4$s处生成结构“%1$s”", "commands.place.template.failed": "放置模板失败", "commands.place.template.invalid": "没有ID为“%s”的模板", "commands.place.template.success": "已在%2$s, %3$s, %4$s处加载模板“%1$s”", "commands.playsound.failed": "声音过远而无法被听见", "commands.playsound.success.multiple": "已将声音%s播放给%s名玩家", "commands.playsound.success.single": "已将声音%s播放给%s", "commands.publish.alreadyPublished": "已存在开放于%s端口的多人游戏", "commands.publish.failed": "无法建立本地游戏", "commands.publish.started": "本地游戏已在端口%s上开启", "commands.publish.success": "多人游戏已在%s端口上开启", "commands.random.error.range_too_large": "随机值的范围最大为2147483646", "commands.random.error.range_too_small": "随机值的范围最小为2", "commands.random.reset.all.success": "已重置%s个随机序列", "commands.random.reset.success": "已重置随机序列%s", "commands.random.roll": "%s掷出了%s（从%s到%s）", "commands.random.sample.success": "随机值：%s", "commands.recipe.give.failed": "没有新配方被解锁", "commands.recipe.give.success.multiple": "已为%2$s名玩家解锁了%1$s条配方", "commands.recipe.give.success.single": "已为%2$s解锁了%1$s条配方", "commands.recipe.take.failed": "没有可遗忘的配方", "commands.recipe.take.success.multiple": "已剥夺%2$s名玩家的%1$s条配方", "commands.recipe.take.success.single": "已剥夺%2$s的%1$s条配方", "commands.reload.failure": "重新加载失败，保留原有数据", "commands.reload.success": "重新加载中！", "commands.ride.already_riding": "%s已经在骑乘%s", "commands.ride.dismount.success": "%s已停止骑乘%s", "commands.ride.mount.failure.cant_ride_players": "玩家无法被骑乘", "commands.ride.mount.failure.generic": "%s无法骑乘%s", "commands.ride.mount.failure.loop": "无法让实体骑乘自身或其乘客", "commands.ride.mount.failure.wrong_dimension": "无法骑乘位于不同维度的实体", "commands.ride.mount.success": "%s已开始骑乘%s", "commands.ride.not_riding": "%s未骑乘任何载具", "commands.rotate.success": "已旋转%s", "commands.save.alreadyOff": "已经关闭世界保存", "commands.save.alreadyOn": "已经打开世界保存", "commands.save.disabled": "自动保存已禁用", "commands.save.enabled": "自动保存已启用", "commands.save.failed": "无法保存游戏（磁盘空间是否足够？）", "commands.save.saving": "正在保存游戏（这可能需要一些时间！）", "commands.save.success": "游戏已保存", "commands.schedule.cleared.failure": "没有ID为%s的计划", "commands.schedule.cleared.success": "已移除%s个ID为%s的计划", "commands.schedule.created.function": "已将函数“%s”计划在%s刻后，即游戏时间%s时执行", "commands.schedule.created.tag": "已将标签“%s”计划在%s刻后，即游戏时间%s时执行", "commands.schedule.macro": "无法计划宏", "commands.schedule.same_tick": "无法将函数计划在当前刻", "commands.scoreboard.objectives.add.duplicate": "已经存在同名记分项", "commands.scoreboard.objectives.add.success": "创建了新的记分项%s", "commands.scoreboard.objectives.display.alreadyEmpty": "无变化，该显示位置本就是空的", "commands.scoreboard.objectives.display.alreadySet": "无变化，该显示位置已经存在该记分项", "commands.scoreboard.objectives.display.cleared": "清空了显示位置%s的所有记分项", "commands.scoreboard.objectives.display.set": "已将显示位置%s设置为展示记分项%s", "commands.scoreboard.objectives.list.empty": "不存在任何记分项", "commands.scoreboard.objectives.list.success": "共有%s个记分项：%s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "已禁用记分项%s的展示自动更新", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "已启用记分项%s的展示自动更新", "commands.scoreboard.objectives.modify.displayname": "已将%s的显示名称更改为%s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "已清除记分项%s的默认数字格式", "commands.scoreboard.objectives.modify.objectiveFormat.set": "已更改记分项%s的默认数字格式", "commands.scoreboard.objectives.modify.rendertype": "已更改记分项%s的渲染类型", "commands.scoreboard.objectives.remove.success": "移除了记分项%s", "commands.scoreboard.players.add.success.multiple": "将%3$s个实体的%2$s增加了%1$s", "commands.scoreboard.players.add.success.single": "将%3$s的%2$s增加了%1$s（现在是%4$s）", "commands.scoreboard.players.display.name.clear.success.multiple": "已清除%s个实体在%s中的显示名称", "commands.scoreboard.players.display.name.clear.success.single": "已清除%s在%s中的显示名称", "commands.scoreboard.players.display.name.set.success.multiple": "已将%2$s个实体在%3$s中的显示名称更改为%1$s", "commands.scoreboard.players.display.name.set.success.single": "已将%2$s在%3$s中的显示名称更改为%1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "已清除%s个实体在%s中的数字格式", "commands.scoreboard.players.display.numberFormat.clear.success.single": "已清除%s在%s中的数字格式", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "已更改%s个实体在%s中的数字格式", "commands.scoreboard.players.display.numberFormat.set.success.single": "已更改%s在%s中的数字格式", "commands.scoreboard.players.enable.failed": "无变化，触发器原已开启", "commands.scoreboard.players.enable.invalid": "只能启用trigger类记分项", "commands.scoreboard.players.enable.success.multiple": "已为%2$s个实体启用了触发器%1$s", "commands.scoreboard.players.enable.success.single": "已为%2$s启用了触发器%1$s", "commands.scoreboard.players.get.null": "无法获取%2$s的%1$s的值，其尚未被赋值", "commands.scoreboard.players.get.success": "%1$s在%3$s记分项里拥有%2$s分", "commands.scoreboard.players.list.empty": "没有正被追踪的实体", "commands.scoreboard.players.list.entity.empty": "%s无可显示的分数", "commands.scoreboard.players.list.entity.entry": "%s：%s", "commands.scoreboard.players.list.entity.success": "%s拥有%s项分数：", "commands.scoreboard.players.list.success": "共有%s个正被追踪的实体：%s", "commands.scoreboard.players.operation.success.multiple": "更新了%2$s个实体的%1$s", "commands.scoreboard.players.operation.success.single": "已将%2$s的%1$s设为%3$s", "commands.scoreboard.players.remove.success.multiple": "已将%3$s个实体的%2$s分数减少%1$s", "commands.scoreboard.players.remove.success.single": "已将%3$s的%2$s减少%1$s（现在是%4$s）", "commands.scoreboard.players.reset.all.multiple": "重置了%s个实体的所有分数", "commands.scoreboard.players.reset.all.single": "重置了%s的所有分数", "commands.scoreboard.players.reset.specific.multiple": "重置了%2$s个实体的%1$s", "commands.scoreboard.players.reset.specific.single": "重置了%2$s的%1$s", "commands.scoreboard.players.set.success.multiple": "已将%2$s个实体的%1$s设为%3$s", "commands.scoreboard.players.set.success.single": "已将%2$s的%1$s分数设为%3$s", "commands.seed.success": "种子：%s", "commands.setblock.failed": "无法放置方块", "commands.setblock.success": "更改了位于%s, %s, %s的方块", "commands.setidletimeout.success": "玩家的闲置限时现在为%s分钟", "commands.setidletimeout.success.disabled": "玩家的闲置限时已禁用", "commands.setworldspawn.failure.not_overworld": "只能为主世界设置世界出生点", "commands.setworldspawn.success": "已将世界的出生点设置为%s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "已将%6$s名玩家的出生点设为%5$s中的%1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "已将%6$s的出生点设为%5$s中的%1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s尚未处于旁观模式", "commands.spectate.self": "不能旁观自己", "commands.spectate.success.started": "正在旁观%s", "commands.spectate.success.stopped": "不再旁观实体", "commands.spreadplayers.failed.entities": "%1$s个实体未能围绕%2$s, %3$s分散（空间过小而实体过多，请将分散间距设为%4$s以下）", "commands.spreadplayers.failed.invalid.height": "无效的maxHeight值：%s，应高于世界最小高度%s", "commands.spreadplayers.failed.teams": "%1$s支队伍未能围绕%2$s, %3$s分散（空间过小而实体过多，请将分散间距设为%4$s以下）", "commands.spreadplayers.success.entities": "已将%s个实体围绕%s, %s分散，平均距离为%s个方块", "commands.spreadplayers.success.teams": "已将%s支队伍围绕%s, %s分散，平均距离为%s个方块", "commands.stop.stopping": "正在关闭服务器", "commands.stopsound.success.source.any": "已停止播放所有“%s”的声音", "commands.stopsound.success.source.sound": "已停止播放来源为“%2$s”的音效“%1$s”", "commands.stopsound.success.sourceless.any": "已停止播放所有声音", "commands.stopsound.success.sourceless.sound": "已停止播放声音“%s”", "commands.summon.failed": "无法召唤实体", "commands.summon.failed.uuid": "UUID重复，无法召唤实体", "commands.summon.invalidPosition": "无效的召唤坐标", "commands.summon.success": "召唤了新的%s", "commands.tag.add.failed": "对象已拥有此标签或拥有过多标签", "commands.tag.add.success.multiple": "已为%2$s个实体添加了标签“%1$s”", "commands.tag.add.success.single": "已为%2$s添加了标签“%1$s”", "commands.tag.list.multiple.empty": "%s个实体没有任何标签", "commands.tag.list.multiple.success": "%s个实体拥有共计%s项标签：%s", "commands.tag.list.single.empty": "%s没有标签", "commands.tag.list.single.success": "%s拥有%s个标签：%s", "commands.tag.remove.failed": "对象没有这个标签", "commands.tag.remove.success.multiple": "已移除%2$s个实体的标签“%1$s”", "commands.tag.remove.success.single": "已移除%2$s的标签“%1$s”", "commands.team.add.duplicate": "已经存在同名队伍", "commands.team.add.success": "已创建队伍%s", "commands.team.empty.success": "已将%s名成员从队伍%s中移除", "commands.team.empty.unchanged": "无变化，该队伍本就是空的", "commands.team.join.success.multiple": "已将%s名成员加入队伍%s", "commands.team.join.success.single": "已将%s加入队伍%s", "commands.team.leave.success.multiple": "已将%s名成员从所有队伍中移除", "commands.team.leave.success.single": "已将%s从所有队伍中移除", "commands.team.list.members.empty": "队伍%s中没有成员", "commands.team.list.members.success": "队伍%s含有%s名成员：%s", "commands.team.list.teams.empty": "没有队伍存在", "commands.team.list.teams.success": "共有%s支队伍：%s", "commands.team.option.collisionRule.success": "队伍%s的碰撞规则现在是“%s”", "commands.team.option.collisionRule.unchanged": "无变化，碰撞规则已经是此值", "commands.team.option.color.success": "队伍%s的颜色已更新为%s", "commands.team.option.color.unchanged": "无变化，此队伍本就为此颜色", "commands.team.option.deathMessageVisibility.success": "队伍%s的死亡消息可见性现在为“%s”", "commands.team.option.deathMessageVisibility.unchanged": "无变化，死亡消息的可见性已经是此值", "commands.team.option.friendlyfire.alreadyDisabled": "无变化，友军伤害本就在此队伍上禁用", "commands.team.option.friendlyfire.alreadyEnabled": "无变化，友军伤害本就在此队伍上启用", "commands.team.option.friendlyfire.disabled": "已禁用队伍%s的友军伤害", "commands.team.option.friendlyfire.enabled": "已启用队伍%s的友军伤害", "commands.team.option.name.success": "已更新队伍%s的名称", "commands.team.option.name.unchanged": "无变化，此队伍本就为该名称", "commands.team.option.nametagVisibility.success": "队伍%s的名称标签可见性现在为“%s”", "commands.team.option.nametagVisibility.unchanged": "无变化，名称标签的可见性已经是此值", "commands.team.option.prefix.success": "队伍前缀已设为%s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "无变化，此队伍本就不可看见隐身的队友", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "无变化，此队伍本就可以看见隐身的队友", "commands.team.option.seeFriendlyInvisibles.disabled": "队伍%s不再可以看见隐身的队友了", "commands.team.option.seeFriendlyInvisibles.enabled": "队伍%s现在可以看见隐身的队友了", "commands.team.option.suffix.success": "队伍后缀已设为%s", "commands.team.remove.success": "移除了队伍%s", "commands.teammsg.failed.noteam": "你必须在一支队伍内才能发出队伍消息", "commands.teleport.invalidPosition": "无效的传送坐标", "commands.teleport.success.entity.multiple": "已传送%s个实体至%s", "commands.teleport.success.entity.single": "已将%s传送至%s", "commands.teleport.success.location.multiple": "已传送%s个实体至%s, %s, %s", "commands.teleport.success.location.single": "已将%s传送至%s, %s, %s", "commands.test.batch.starting": "正在启动环境%s批次%s", "commands.test.clear.error.no_tests": "无法找到任何需要清除的测试", "commands.test.clear.success": "清除了%s个结构", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "单击复制到剪贴板", "commands.test.create.success": "已为测试%s创建测试设置", "commands.test.error.no_test_containing_pos": "找不到包含%s, %s, %s的测试实例", "commands.test.error.no_test_instances": "未找到测试实例", "commands.test.error.non_existant_test": "测试%s不存在", "commands.test.error.structure_not_found": "无法找到测试结构%s", "commands.test.error.test_instance_not_found": "无法找到测试实例方块实体", "commands.test.error.test_instance_not_found.position": "无法在%s, %s, %s处找到测试实例方块实体", "commands.test.error.too_large": "结构在每条轴上的大小必须小于%s个方块", "commands.test.locate.done": "定位完成，找到%s个结构", "commands.test.locate.found": "在以下位置找到结构：%s（距离：%s）", "commands.test.locate.started": "已开始定位测试结构，这可能需要一点时间…", "commands.test.no_tests": "没有需要运行的测试", "commands.test.relative_position": "相对于%s的位置：%s", "commands.test.reset.error.no_tests": "无法找到任何需要重置的测试", "commands.test.reset.success": "重置了%s个结构", "commands.test.run.no_tests": "未找到测试", "commands.test.run.running": "正在运行%s个测试…", "commands.test.summary": "游戏测试完成！已运行%s个测试", "commands.test.summary.all_required_passed": "必要测试均已通过 :)", "commands.test.summary.failed": "%s个必要测试失败 :(", "commands.test.summary.optional_failed": "%s个可选测试失败", "commands.tick.query.percentiles": "百分位数：50%%：%s毫秒 95%%：%s毫秒 99%%：%s毫秒，共采样：%s", "commands.tick.query.rate.running": "目标速率：%s刻每秒。\n每刻平均时间：%s毫秒（目标：%s毫秒）", "commands.tick.query.rate.sprinting": "目标速率：%s刻每秒（已忽略，仅供参考）。\n每刻平均时间：%s毫秒", "commands.tick.rate.success": "已设置目标速率为每秒%s刻", "commands.tick.sprint.report": "游戏加速已完成，速率为每秒%s刻，即每刻%s毫秒", "commands.tick.sprint.stop.fail": "没有正在执行的游戏刻加速", "commands.tick.sprint.stop.success": "当前游戏刻加速已中止", "commands.tick.status.frozen": "游戏冻结中", "commands.tick.status.lagging": "游戏正在运行，但无法达到目标速率", "commands.tick.status.running": "游戏正在以正常速度运行", "commands.tick.status.sprinting": "游戏加速运行中", "commands.tick.step.fail": "无法步进游戏，请先冻结游戏", "commands.tick.step.stop.fail": "没有正在执行的游戏刻步进", "commands.tick.step.stop.success": "当前游戏刻步进已中止", "commands.tick.step.success": "正在步进%s刻", "commands.time.query": "当前时间为%s", "commands.time.set": "已将时间设为%s", "commands.title.cleared.multiple": "已清除%s名玩家的标题", "commands.title.cleared.single": "已清除%s的标题", "commands.title.reset.multiple": "已重置%s名玩家的标题设置", "commands.title.reset.single": "已重置%s的标题设置", "commands.title.show.actionbar.multiple": "正在向%s名玩家显示新的动作栏标题", "commands.title.show.actionbar.single": "正在向%s显示新的动作栏标题", "commands.title.show.subtitle.multiple": "正在向%s名玩家显示新的副标题", "commands.title.show.subtitle.single": "正在向%s显示新的副标题", "commands.title.show.title.multiple": "正在向%s名玩家显示新的标题", "commands.title.show.title.single": "正在向%s显示新的标题", "commands.title.times.multiple": "已更改%s名玩家的标题显示时间", "commands.title.times.single": "已更改%s的标题显示时间", "commands.transfer.error.no_players": "必须至少指定一个待转移的玩家", "commands.transfer.success.multiple": "正在转移%s个玩家至%s:%s", "commands.transfer.success.single": "正在转移%s至%s:%s", "commands.trigger.add.success": "已触发%s（数值已增加%s）", "commands.trigger.failed.invalid": "你只能触发“trigger”类型的记分项", "commands.trigger.failed.unprimed": "你尚无法触发这个记分项", "commands.trigger.set.success": "已触发%s（数值已设为%s）", "commands.trigger.simple.success": "已触发%s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "在%s内没有路径点", "commands.waypoint.list.success": "在%2$s内找到%1$s个路径点：%3$s", "commands.waypoint.modify.color": "路径点颜色现在为%s", "commands.waypoint.modify.color.reset": "已重置路径点颜色", "commands.waypoint.modify.style": "已更改路径点样式", "commands.weather.set.clear": "天气已设为晴天", "commands.weather.set.rain": "天气已设为雨天", "commands.weather.set.thunder": "天气已设为雷雨", "commands.whitelist.add.failed": "玩家已在白名单内", "commands.whitelist.add.success": "已将%s加入白名单", "commands.whitelist.alreadyOff": "白名单原已关闭", "commands.whitelist.alreadyOn": "白名单原已开启", "commands.whitelist.disabled": "白名单已关闭", "commands.whitelist.enabled": "白名单已开启", "commands.whitelist.list": "白名单中共有%s名玩家：%s", "commands.whitelist.none": "白名单中没有玩家", "commands.whitelist.reloaded": "已重新读取白名单", "commands.whitelist.remove.failed": "玩家不在白名单内", "commands.whitelist.remove.success": "已将%s移出白名单", "commands.worldborder.center.failed": "无变化，世界边界中心点已经在该处", "commands.worldborder.center.success": "已将世界边界的中心设为%s, %s", "commands.worldborder.damage.amount.failed": "无变化，世界边界伤害已经是此值", "commands.worldborder.damage.amount.success": "已将世界边界的伤害设置为%s每秒每个方块", "commands.worldborder.damage.buffer.failed": "无变化，世界边界伤害缓冲区已经是此距离", "commands.worldborder.damage.buffer.success": "已将世界边界的伤害缓冲区设置为%s个方块", "commands.worldborder.get": "世界边界的当前宽度为%s个方块", "commands.worldborder.set.failed.big": "世界边界的宽度不能大于%s格", "commands.worldborder.set.failed.far": "世界边界的位置不能远于%s格", "commands.worldborder.set.failed.nochange": "无变化，世界边界已经是此大小", "commands.worldborder.set.failed.small": "世界边界的宽度不能小于1格", "commands.worldborder.set.grow": "正在将世界边界的宽度扩大为%s个方块，时间%s秒", "commands.worldborder.set.immediate": "已将世界边界的宽度设为%s", "commands.worldborder.set.shrink": "正在将世界边界的宽度缩小为%s个方块，用时%s秒", "commands.worldborder.warning.distance.failed": "无变化，世界边界伤害警告区已经是此距离", "commands.worldborder.warning.distance.success": "已将世界边界的警告距离设置为%s个方块", "commands.worldborder.warning.time.failed": "无变化，世界边界警告时间已经是此时长", "commands.worldborder.warning.time.success": "已将世界边界的警告时间设置为%s秒", "compliance.playtime.greaterThan24Hours": "你的游戏时长已超过24小时", "compliance.playtime.hours": "你的游戏时长已达%s小时", "compliance.playtime.message": "适度游戏益脑，沉迷游戏伤身", "connect.aborted": "连接中断", "connect.authorizing": "登录中…", "connect.connecting": "正在连接到服务器…", "connect.encrypting": "通讯加密中…", "connect.failed": "无法连接至服务器", "connect.failed.transfer": "转移到服务器时连接失败", "connect.joining": "加入世界中…", "connect.negotiating": "连接协商中…", "connect.reconfiging": "重新配置中…", "connect.reconfiguring": "重新配置中…", "connect.transferring": "正在转移至新的服务器…", "container.barrel": "木桶", "container.beacon": "信标", "container.beehive.bees": "蜜蜂：%s/%s", "container.beehive.honey": "蜂蜜：%s/%s", "container.blast_furnace": "高炉", "container.brewing": "酿造台", "container.cartography_table": "制图台", "container.chest": "箱子", "container.chestDouble": "大型箱子", "container.crafter": "合成器", "container.crafting": "合成", "container.creative": "物品选栏", "container.dispenser": "发射器", "container.dropper": "投掷器", "container.enchant": "附魔", "container.enchant.clue": "%s…？", "container.enchant.lapis.many": "花费：%s颗青金石", "container.enchant.lapis.one": "花费：1颗青金石", "container.enchant.level.many": "+ %s级经验", "container.enchant.level.one": "+ 1级经验", "container.enchant.level.requirement": "等级要求：%s", "container.enderchest": "末影箱", "container.furnace": "熔炉", "container.grindstone_title": "修复和祛魔", "container.hopper": "漏斗", "container.inventory": "物品栏", "container.isLocked": "%s已被上锁！", "container.lectern": "讲台", "container.loom": "织布机", "container.repair": "修复和命名", "container.repair.cost": "附魔花费：%1$s", "container.repair.expensive": "过于昂贵！", "container.shulkerBox": "潜影盒", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "还有%s项未显示…", "container.shulkerBox.unknownContents": "???????", "container.smoker": "烟熏炉", "container.spectatorCantOpen": "无法打开：战利品尚未生成。", "container.stonecutter": "切石机", "container.upgrade": "升级装备", "container.upgrade.error_tooltip": "该物品无法用此方式升级", "container.upgrade.missing_template_tooltip": "放入锻造模板", "controls.keybinds": "按键绑定…", "controls.keybinds.duplicateKeybinds": "该按键也用于：\n%s", "controls.keybinds.title": "按键绑定", "controls.reset": "重置", "controls.resetAll": "重置按键", "controls.title": "按键控制", "createWorld.customize.buffet.biome": "请选择一种生物群系", "createWorld.customize.buffet.title": "自定义单一生物群系", "createWorld.customize.flat.height": "高度", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "底层 - %s", "createWorld.customize.flat.layer.top": "顶层 - %s", "createWorld.customize.flat.removeLayer": "移除层面", "createWorld.customize.flat.tile": "各层的材料", "createWorld.customize.flat.title": "自定义超平坦世界", "createWorld.customize.presets": "预设", "createWorld.customize.presets.list": "另外，这里是些我们早期制作好的！", "createWorld.customize.presets.select": "使用预设", "createWorld.customize.presets.share": "想要与别人分享你的预设方案吗？使用下面的输入框吧！", "createWorld.customize.presets.title": "选择一种预设", "createWorld.preparing": "正在准备生成世界…", "createWorld.tab.game.title": "游戏", "createWorld.tab.more.title": "更多", "createWorld.tab.world.title": "世界", "credits_and_attribution.button.attribution": "著作权说明", "credits_and_attribution.button.credits": "鸣谢名单", "credits_and_attribution.button.licenses": "许可协议", "credits_and_attribution.screen.title": "鸣谢与著作权说明", "dataPack.bundle.description": "启用实验性收纳袋物品", "dataPack.bundle.name": "收纳袋", "dataPack.locator_bar.description": "在多人游戏中显示其他玩家的方位", "dataPack.locator_bar.name": "定位栏", "dataPack.minecart_improvements.description": "改进了矿车的运动", "dataPack.minecart_improvements.name": "矿车改进", "dataPack.redstone_experiments.description": "实验性红石更改", "dataPack.redstone_experiments.name": "红石实验性内容", "dataPack.title": "选择数据包", "dataPack.trade_rebalance.description": "新版村民交易", "dataPack.trade_rebalance.name": "村民交易的平衡性调整", "dataPack.update_1_20.description": "Minecraft 1.20的新功能与内容", "dataPack.update_1_20.name": "1.20更新", "dataPack.update_1_21.description": "Minecraft 1.21的新功能与内容", "dataPack.update_1_21.name": "1.21更新", "dataPack.validation.back": "返回", "dataPack.validation.failed": "数据包验证失败！", "dataPack.validation.reset": "重置为默认", "dataPack.validation.working": "正在验证选中的数据包…", "dataPack.vanilla.description": "Minecraft的默认数据包", "dataPack.vanilla.name": "默认", "dataPack.winter_drop.description": "冬季小更新的新功能与内容", "dataPack.winter_drop.name": "冬季小更新", "datapackFailure.safeMode": "安全模式", "datapackFailure.safeMode.failed.description": "这个世界包含无效或损坏的存档数据。", "datapackFailure.safeMode.failed.title": "无法在安全模式下加载世界。", "datapackFailure.title": "当前选中的数据包中出现了错误，导致世界无法加载。\n你可以尝试仅加载原版数据包（“安全模式”）或回到标题屏幕手动修复该问题。", "death.attack.anvil": "%1$s被下落的铁砧压扁了", "death.attack.anvil.player": "%1$s在与%2$s战斗时被下落的铁砧压扁了", "death.attack.arrow": "%1$s被%2$s射杀", "death.attack.arrow.item": "%1$s被%2$s用%3$s射杀", "death.attack.badRespawnPoint.link": "刻意的游戏设计", "death.attack.badRespawnPoint.message": "%1$s被%2$s杀死了", "death.attack.cactus": "%1$s被戳死了", "death.attack.cactus.player": "%1$s在试图逃离%2$s时撞上了仙人掌", "death.attack.cramming": "%1$s因被过度挤压而死", "death.attack.cramming.player": "%1$s被%2$s挤扁了", "death.attack.dragonBreath": "%1$s被龙息烤熟了", "death.attack.dragonBreath.player": "%1$s被%2$s的龙息烤熟了", "death.attack.drown": "%1$s淹死了", "death.attack.drown.player": "%1$s在试图逃离%2$s时淹死了", "death.attack.dryout": "%1$s因脱水而死", "death.attack.dryout.player": "%1$s在试图逃离%2$s时因脱水而死", "death.attack.even_more_magic": "%1$s被不为人知的魔法杀死了", "death.attack.explosion": "%1$s爆炸了", "death.attack.explosion.player": "%1$s被%2$s炸死了", "death.attack.explosion.player.item": "%1$s被%2$s用%3$s炸死了", "death.attack.fall": "%1$s落地过猛", "death.attack.fall.player": "%1$s在试图逃离%2$s时落地过猛", "death.attack.fallingBlock": "%1$s被下落的方块压扁了", "death.attack.fallingBlock.player": "%1$s在与%2$s战斗时被下落的方块压扁了", "death.attack.fallingStalactite": "%1$s被下落的钟乳石刺穿了", "death.attack.fallingStalactite.player": "%1$s在与%2$s战斗时被下落的钟乳石刺穿了", "death.attack.fireball": "%1$s被%2$s用火球烧死了", "death.attack.fireball.item": "%1$s被%2$s用%3$s发射的火球烧死了", "death.attack.fireworks": "%1$s随着一声巨响消失了", "death.attack.fireworks.item": "%1$s随着%2$s用%3$s发射的烟花发出的巨响消失了", "death.attack.fireworks.player": "%1$s在与%2$s战斗时随着一声巨响消失了", "death.attack.flyIntoWall": "%1$s感受到了动能", "death.attack.flyIntoWall.player": "%1$s在试图逃离%2$s时感受到了动能", "death.attack.freeze": "%1$s被冻死了", "death.attack.freeze.player": "%1$s被%2$s冻死了", "death.attack.generic": "%1$s死了", "death.attack.generic.player": "%1$s死于%2$s", "death.attack.genericKill": "%1$s被杀死了", "death.attack.genericKill.player": "%1$s在与%2$s战斗时被杀死了", "death.attack.hotFloor": "%1$s发现了地板是熔岩做的", "death.attack.hotFloor.player": "%1$s因%2$s而步入危险之地", "death.attack.inFire": "%1$s浴火焚身", "death.attack.inFire.player": "%1$s在与%2$s战斗时不慎走入了火中", "death.attack.inWall": "%1$s在墙里窒息而亡", "death.attack.inWall.player": "%1$s在与%2$s战斗时在墙里窒息而亡", "death.attack.indirectMagic": "%1$s被%2$s使用的魔法杀死了", "death.attack.indirectMagic.item": "%1$s被%2$s用%3$s杀死了", "death.attack.lava": "%1$s试图在熔岩里游泳", "death.attack.lava.player": "%1$s在逃离%2$s时试图在熔岩里游泳", "death.attack.lightningBolt": "%1$s被闪电击中", "death.attack.lightningBolt.player": "%1$s在与%2$s战斗时被闪电击中", "death.attack.mace_smash": "%1$s被%2$s一锤毙命", "death.attack.mace_smash.item": "%1$s被%2$s用%3$s一锤毙命", "death.attack.magic": "%1$s被魔法杀死了", "death.attack.magic.player": "%1$s在试图逃离%2$s时被魔法杀死了", "death.attack.message_too_long": "抱歉！消息太长，无法完整显示。截断后的消息：%s", "death.attack.mob": "%1$s被%2$s杀死了", "death.attack.mob.item": "%1$s被%2$s用%3$s杀死了", "death.attack.onFire": "%1$s被烧死了", "death.attack.onFire.item": "%1$s在与持有%3$s的%2$s战斗时被烤得酥脆", "death.attack.onFire.player": "%1$s在与%2$s战斗时被烤得酥脆", "death.attack.outOfWorld": "%1$s掉出了这个世界", "death.attack.outOfWorld.player": "%1$s与%2$s不共戴天", "death.attack.outsideBorder": "%1$s脱离了这个世界", "death.attack.outsideBorder.player": "%1$s在与%2$s战斗时脱离了这个世界", "death.attack.player": "%1$s被%2$s杀死了", "death.attack.player.item": "%1$s被%2$s用%3$s杀死了", "death.attack.sonic_boom": "%1$s被一道音波尖啸抹除了", "death.attack.sonic_boom.item": "%1$s在试图逃离持有%3$s的%2$s时被一道音波尖啸抹除了", "death.attack.sonic_boom.player": "%1$s在试图逃离%2$s时被一道音波尖啸抹除了", "death.attack.stalagmite": "%1$s被石笋刺穿了", "death.attack.stalagmite.player": "%1$s在与%2$s战斗时被石笋刺穿了", "death.attack.starve": "%1$s饿死了", "death.attack.starve.player": "%1$s在与%2$s战斗时饿死了", "death.attack.sting": "%1$s被蛰死了", "death.attack.sting.item": "%1$s被%2$s用%3$s蛰死了", "death.attack.sting.player": "%1$s被%2$s蛰死了", "death.attack.sweetBerryBush": "%1$s被甜浆果丛刺死了", "death.attack.sweetBerryBush.player": "%1$s在试图逃离%2$s时被甜浆果丛刺死了", "death.attack.thorns": "%1$s在试图伤害%2$s时被杀", "death.attack.thorns.item": "%1$s在试图伤害%2$s时被%3$s杀死", "death.attack.thrown": "%1$s被%2$s给砸死了", "death.attack.thrown.item": "%1$s被%2$s用%3$s给砸死了", "death.attack.trident": "%1$s被%2$s刺穿了", "death.attack.trident.item": "%1$s被%2$s用%3$s刺穿了", "death.attack.wither": "%1$s凋零了", "death.attack.wither.player": "%1$s在与%2$s战斗时凋零了", "death.attack.witherSkull": "%1$s被%2$s发射的头颅射杀", "death.attack.witherSkull.item": "%1$s被%2$s用%3$s发射的头颅射杀", "death.fell.accident.generic": "%1$s从高处摔了下来", "death.fell.accident.ladder": "%1$s从梯子上摔了下来", "death.fell.accident.other_climbable": "%1$s在攀爬时摔了下来", "death.fell.accident.scaffolding": "%1$s从脚手架上摔了下来", "death.fell.accident.twisting_vines": "%1$s从缠怨藤上摔了下来", "death.fell.accident.vines": "%1$s从藤蔓上摔了下来", "death.fell.accident.weeping_vines": "%1$s从垂泪藤上摔了下来", "death.fell.assist": "%1$s因为%2$s注定要摔死", "death.fell.assist.item": "%1$s因为%2$s使用了%3$s注定要摔死", "death.fell.finish": "%1$s摔伤得太重并被%2$s完结了生命", "death.fell.finish.item": "%1$s摔伤得太重并被%2$s用%3$s完结了生命", "death.fell.killer": "%1$s注定要摔死", "deathScreen.quit.confirm": "你确定要退出吗？", "deathScreen.respawn": "重生", "deathScreen.score": "分数", "deathScreen.score.value": "分数：%s", "deathScreen.spectate": "旁观世界", "deathScreen.title": "你死了！", "deathScreen.title.hardcore": "游戏结束！", "deathScreen.titleScreen": "标题屏幕", "debug.advanced_tooltips.help": "F3 + H = 显示高级提示框", "debug.advanced_tooltips.off": "高级提示框：隐藏", "debug.advanced_tooltips.on": "高级提示框：显示", "debug.chunk_boundaries.help": "F3 + G = 显示区块边界", "debug.chunk_boundaries.off": "区块边界：隐藏", "debug.chunk_boundaries.on": "区块边界：显示", "debug.clear_chat.help": "F3 + D = 清空聊天记录", "debug.copy_location.help": "F3 + C = 用/tp命令的形式复制你的位置，按住F3 + C使游戏崩溃", "debug.copy_location.message": "坐标已复制到剪贴板", "debug.crash.message": "F3 + C已被按下。若不放开按键则会使游戏崩溃。", "debug.crash.warning": "将在%s秒后崩溃…", "debug.creative_spectator.error": "你没有切换游戏模式的权限", "debug.creative_spectator.help": "F3 + N = 在上一个模式和旁观模式间切换", "debug.dump_dynamic_textures": "已将动态纹理保存至%s", "debug.dump_dynamic_textures.help": "F3 + S = 转储动态纹理", "debug.gamemodes.error": "你没有权限打开游戏模式切换器", "debug.gamemodes.help": "F3 + F4 = 打开游戏模式切换器", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s 下一个", "debug.help.help": "F3 + Q = 显示此列表", "debug.help.message": "按键设置：", "debug.inspect.client.block": "客户端方块数据已复制到剪贴板", "debug.inspect.client.entity": "客户端实体数据已复制到剪贴板", "debug.inspect.help": "F3 + I = 将实体或方块的数据复制到剪贴板", "debug.inspect.server.block": "服务端方块数据已复制到剪贴板", "debug.inspect.server.entity": "服务端实体数据已复制到剪贴板", "debug.pause.help": "F3 + Esc = 暂停但不显示菜单（如果可以暂停的话）", "debug.pause_focus.help": "F3 + P = 失去焦点时暂停", "debug.pause_focus.off": "失去焦点时暂停：停用", "debug.pause_focus.on": "失去焦点时暂停：启用", "debug.prefix": "[调试]：", "debug.profiling.help": "F3 + L = 开始/停止分析", "debug.profiling.start": "分析已启动%s秒。使用F3 + L以提前结束", "debug.profiling.stop": "分析已结束。结果已保存至%s", "debug.reload_chunks.help": "F3 + A = 重新加载区块", "debug.reload_chunks.message": "重新加载所有区块中", "debug.reload_resourcepacks.help": "F3 + T = 重新加载资源包", "debug.reload_resourcepacks.message": "已重新加载资源包", "debug.show_hitboxes.help": "F3 + B = 显示判定箱", "debug.show_hitboxes.off": "判定箱：隐藏", "debug.show_hitboxes.on": "判定箱：显示", "debug.version.header": "客户端版本信息：", "debug.version.help": "F3 + V = 显示客户端版本信息", "demo.day.1": "此试玩版会在5个游戏日后结束，尽力而为吧！", "demo.day.2": "第二天", "demo.day.3": "第三天", "demo.day.4": "第四天", "demo.day.5": "这是你游戏内的最后一天！", "demo.day.6": "你已经度过了5个游戏日的试玩时间，按下%s来为你的成果截图留念。", "demo.day.warning": "你的试玩时间即将结束！", "demo.demoExpired": "试玩的时间结束了！", "demo.help.buy": "即刻购买！", "demo.help.fullWrapped": "这个试玩将会持续游戏内5天的时间（现实时间大约为1小时40分钟）。查看进度来获得提示！祝你玩得开心！", "demo.help.inventory": "按%1$s来打开你的物品栏", "demo.help.jump": "按%1$s来跳跃", "demo.help.later": "继续游戏！", "demo.help.movement": "使用%1$s、%2$s、%3$s、%4$s以及鼠标来移动", "demo.help.movementMouse": "使用鼠标来环顾四周", "demo.help.movementShort": "通过按下%1$s、%2$s、%3$s、%4$s来移动", "demo.help.title": "Minecraft试玩模式", "demo.remainingTime": "剩余时间：%s", "demo.reminder": "试玩时间已经结束，请购买游戏来继续或开始一个新的世界！", "difficulty.lock.question": "你确定你要锁定这个世界的难度吗？这会将这个世界的难度锁定为%1$s，并且永远无法再次改变难度。", "difficulty.lock.title": "锁定世界难度", "disconnect.endOfStream": "数据流终止", "disconnect.exceeded_packet_rate": "由于超出数据包速率限制而被踢出游戏", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "忽略状态请求", "disconnect.loginFailedInfo": "登录失败：%s", "disconnect.loginFailedInfo.insufficientPrivileges": "多人游戏已被禁用，请检查你的Microsoft账户设置。", "disconnect.loginFailedInfo.invalidSession": "无效会话（请尝试重启游戏及启动器）", "disconnect.loginFailedInfo.serversUnavailable": "暂时无法连接到身份验证服务器，请稍后再试。", "disconnect.loginFailedInfo.userBanned": "你已被禁止进行在线游戏", "disconnect.lost": "连接已丢失", "disconnect.packetError": "网络协议错误", "disconnect.spam": "由于滥发消息而被踢出游戏", "disconnect.timeout": "连接超时", "disconnect.transfer": "已转移至另一个服务器", "disconnect.unknownHost": "未知的主机", "download.pack.failed": "有%s个包下载失败，共%s个包", "download.pack.progress.bytes": "进度：%s（总大小未知）", "download.pack.progress.percent": "进度：%s%%", "download.pack.title": "正在下载资源包%s/%s", "editGamerule.default": "默认：%s", "editGamerule.title": "编辑游戏规则", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "伤害吸收", "effect.minecraft.bad_omen": "不祥之兆", "effect.minecraft.blindness": "失明", "effect.minecraft.conduit_power": "潮涌能量", "effect.minecraft.darkness": "黑暗", "effect.minecraft.dolphins_grace": "海豚的恩惠", "effect.minecraft.fire_resistance": "抗火", "effect.minecraft.glowing": "发光", "effect.minecraft.haste": "急迫", "effect.minecraft.health_boost": "生命提升", "effect.minecraft.hero_of_the_village": "村庄英雄", "effect.minecraft.hunger": "饥饿", "effect.minecraft.infested": "寄生", "effect.minecraft.instant_damage": "瞬间伤害", "effect.minecraft.instant_health": "瞬间治疗", "effect.minecraft.invisibility": "隐身", "effect.minecraft.jump_boost": "跳跃提升", "effect.minecraft.levitation": "飘浮", "effect.minecraft.luck": "幸运", "effect.minecraft.mining_fatigue": "挖掘疲劳", "effect.minecraft.nausea": "反胃", "effect.minecraft.night_vision": "夜视", "effect.minecraft.oozing": "渗浆", "effect.minecraft.poison": "中毒", "effect.minecraft.raid_omen": "袭击之兆", "effect.minecraft.regeneration": "生命恢复", "effect.minecraft.resistance": "抗性提升", "effect.minecraft.saturation": "饱和", "effect.minecraft.slow_falling": "缓降", "effect.minecraft.slowness": "缓慢", "effect.minecraft.speed": "迅捷", "effect.minecraft.strength": "力量", "effect.minecraft.trial_omen": "试炼之兆", "effect.minecraft.unluck": "霉运", "effect.minecraft.water_breathing": "水下呼吸", "effect.minecraft.weakness": "虚弱", "effect.minecraft.weaving": "盘丝", "effect.minecraft.wind_charged": "蓄风", "effect.minecraft.wither": "凋零", "effect.none": "无效果", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "水下速掘", "enchantment.minecraft.bane_of_arthropods": "节肢杀手", "enchantment.minecraft.binding_curse": "绑定诅咒", "enchantment.minecraft.blast_protection": "爆炸保护", "enchantment.minecraft.breach": "破甲", "enchantment.minecraft.channeling": "引雷", "enchantment.minecraft.density": "致密", "enchantment.minecraft.depth_strider": "深海探索者", "enchantment.minecraft.efficiency": "效率", "enchantment.minecraft.feather_falling": "摔落缓冲", "enchantment.minecraft.fire_aspect": "火焰附加", "enchantment.minecraft.fire_protection": "火焰保护", "enchantment.minecraft.flame": "火矢", "enchantment.minecraft.fortune": "时运", "enchantment.minecraft.frost_walker": "冰霜行者", "enchantment.minecraft.impaling": "穿刺", "enchantment.minecraft.infinity": "无限", "enchantment.minecraft.knockback": "击退", "enchantment.minecraft.looting": "抢夺", "enchantment.minecraft.loyalty": "忠诚", "enchantment.minecraft.luck_of_the_sea": "海之眷顾", "enchantment.minecraft.lure": "饵钓", "enchantment.minecraft.mending": "经验修补", "enchantment.minecraft.multishot": "多重射击", "enchantment.minecraft.piercing": "穿透", "enchantment.minecraft.power": "力量", "enchantment.minecraft.projectile_protection": "弹射物保护", "enchantment.minecraft.protection": "保护", "enchantment.minecraft.punch": "冲击", "enchantment.minecraft.quick_charge": "快速装填", "enchantment.minecraft.respiration": "水下呼吸", "enchantment.minecraft.riptide": "激流", "enchantment.minecraft.sharpness": "锋利", "enchantment.minecraft.silk_touch": "精准采集", "enchantment.minecraft.smite": "亡灵杀手", "enchantment.minecraft.soul_speed": "灵魂疾行", "enchantment.minecraft.sweeping": "横扫之刃", "enchantment.minecraft.sweeping_edge": "横扫之刃", "enchantment.minecraft.swift_sneak": "迅捷潜行", "enchantment.minecraft.thorns": "荆棘", "enchantment.minecraft.unbreaking": "耐久", "enchantment.minecraft.vanishing_curse": "消失诅咒", "enchantment.minecraft.wind_burst": "风爆", "entity.minecraft.acacia_boat": "金合欢木船", "entity.minecraft.acacia_chest_boat": "金合欢木运输船", "entity.minecraft.allay": "悦灵", "entity.minecraft.area_effect_cloud": "区域效果云", "entity.minecraft.armadillo": "犰狳", "entity.minecraft.armor_stand": "盔甲架", "entity.minecraft.arrow": "箭", "entity.minecraft.axolotl": "美西螈", "entity.minecraft.bamboo_chest_raft": "运输竹筏", "entity.minecraft.bamboo_raft": "竹筏", "entity.minecraft.bat": "蝙蝠", "entity.minecraft.bee": "蜜蜂", "entity.minecraft.birch_boat": "白桦木船", "entity.minecraft.birch_chest_boat": "白桦木运输船", "entity.minecraft.blaze": "烈焰人", "entity.minecraft.block_display": "方块展示实体", "entity.minecraft.boat": "船", "entity.minecraft.bogged": "沼骸", "entity.minecraft.breeze": "旋风人", "entity.minecraft.breeze_wind_charge": "风弹", "entity.minecraft.camel": "骆驼", "entity.minecraft.cat": "猫", "entity.minecraft.cave_spider": "洞穴蜘蛛", "entity.minecraft.cherry_boat": "樱花木船", "entity.minecraft.cherry_chest_boat": "樱花木运输船", "entity.minecraft.chest_boat": "运输船", "entity.minecraft.chest_minecart": "运输矿车", "entity.minecraft.chicken": "鸡", "entity.minecraft.cod": "鳕鱼", "entity.minecraft.command_block_minecart": "命令方块矿车", "entity.minecraft.cow": "牛", "entity.minecraft.creaking": "嘎枝", "entity.minecraft.creaking_transient": "嘎枝", "entity.minecraft.creeper": "苦力怕", "entity.minecraft.dark_oak_boat": "深色橡木船", "entity.minecraft.dark_oak_chest_boat": "深色橡木运输船", "entity.minecraft.dolphin": "海豚", "entity.minecraft.donkey": "驴", "entity.minecraft.dragon_fireball": "末影龙火球", "entity.minecraft.drowned": "溺尸", "entity.minecraft.egg": "掷出的鸡蛋", "entity.minecraft.elder_guardian": "远古守卫者", "entity.minecraft.end_crystal": "末地水晶", "entity.minecraft.ender_dragon": "末影龙", "entity.minecraft.ender_pearl": "掷出的末影珍珠", "entity.minecraft.enderman": "末影人", "entity.minecraft.endermite": "末影螨", "entity.minecraft.evoker": "唤魔者", "entity.minecraft.evoker_fangs": "唤魔者尖牙", "entity.minecraft.experience_bottle": "掷出的附魔之瓶", "entity.minecraft.experience_orb": "经验球", "entity.minecraft.eye_of_ender": "末影之眼", "entity.minecraft.falling_block": "下落的方块", "entity.minecraft.falling_block_type": "下落的%s", "entity.minecraft.fireball": "火球", "entity.minecraft.firework_rocket": "烟花火箭", "entity.minecraft.fishing_bobber": "浮漂", "entity.minecraft.fox": "狐狸", "entity.minecraft.frog": "青蛙", "entity.minecraft.furnace_minecart": "动力矿车", "entity.minecraft.ghast": "恶魂", "entity.minecraft.giant": "巨人", "entity.minecraft.glow_item_frame": "荧光物品展示框", "entity.minecraft.glow_squid": "发光鱿鱼", "entity.minecraft.goat": "山羊", "entity.minecraft.guardian": "守卫者", "entity.minecraft.happy_ghast": "快乐恶魂", "entity.minecraft.hoglin": "疣猪兽", "entity.minecraft.hopper_minecart": "漏斗矿车", "entity.minecraft.horse": "马", "entity.minecraft.husk": "尸壳", "entity.minecraft.illusioner": "幻术师", "entity.minecraft.interaction": "交互实体", "entity.minecraft.iron_golem": "铁傀儡", "entity.minecraft.item": "物品", "entity.minecraft.item_display": "物品展示实体", "entity.minecraft.item_frame": "物品展示框", "entity.minecraft.jungle_boat": "丛林木船", "entity.minecraft.jungle_chest_boat": "丛林木运输船", "entity.minecraft.killer_bunny": "杀手兔", "entity.minecraft.leash_knot": "拴绳结", "entity.minecraft.lightning_bolt": "闪电束", "entity.minecraft.lingering_potion": "滞留药水", "entity.minecraft.llama": "羊驼", "entity.minecraft.llama_spit": "羊驼唾沫", "entity.minecraft.magma_cube": "岩浆怪", "entity.minecraft.mangrove_boat": "红树木船", "entity.minecraft.mangrove_chest_boat": "红树木运输船", "entity.minecraft.marker": "标记", "entity.minecraft.minecart": "矿车", "entity.minecraft.mooshroom": "哞菇", "entity.minecraft.mule": "骡", "entity.minecraft.oak_boat": "橡木船", "entity.minecraft.oak_chest_boat": "橡木运输船", "entity.minecraft.ocelot": "豹猫", "entity.minecraft.ominous_item_spawner": "不祥之物生成器", "entity.minecraft.painting": "画", "entity.minecraft.pale_oak_boat": "苍白橡木船", "entity.minecraft.pale_oak_chest_boat": "苍白橡木运输船", "entity.minecraft.panda": "熊猫", "entity.minecraft.parrot": "鹦鹉", "entity.minecraft.phantom": "幻翼", "entity.minecraft.pig": "猪", "entity.minecraft.piglin": "猪灵", "entity.minecraft.piglin_brute": "猪灵蛮兵", "entity.minecraft.pillager": "掠夺者", "entity.minecraft.player": "玩家", "entity.minecraft.polar_bear": "北极熊", "entity.minecraft.potion": "药水", "entity.minecraft.pufferfish": "河豚", "entity.minecraft.rabbit": "兔子", "entity.minecraft.ravager": "劫掠兽", "entity.minecraft.salmon": "鲑鱼", "entity.minecraft.sheep": "绵羊", "entity.minecraft.shulker": "潜影贝", "entity.minecraft.shulker_bullet": "潜影弹", "entity.minecraft.silverfish": "蠹虫", "entity.minecraft.skeleton": "骷髅", "entity.minecraft.skeleton_horse": "骷髅马", "entity.minecraft.slime": "史莱姆", "entity.minecraft.small_fireball": "小火球", "entity.minecraft.sniffer": "嗅探兽", "entity.minecraft.snow_golem": "雪傀儡", "entity.minecraft.snowball": "雪球", "entity.minecraft.spawner_minecart": "刷怪笼矿车", "entity.minecraft.spectral_arrow": "光灵箭", "entity.minecraft.spider": "蜘蛛", "entity.minecraft.splash_potion": "喷溅药水", "entity.minecraft.spruce_boat": "云杉木船", "entity.minecraft.spruce_chest_boat": "云杉木运输船", "entity.minecraft.squid": "鱿鱼", "entity.minecraft.stray": "流浪者", "entity.minecraft.strider": "炽足兽", "entity.minecraft.tadpole": "蝌蚪", "entity.minecraft.text_display": "文本展示实体", "entity.minecraft.tnt": "被激活的TNT", "entity.minecraft.tnt_minecart": "TNT矿车", "entity.minecraft.trader_llama": "行商羊驼", "entity.minecraft.trident": "三叉戟", "entity.minecraft.tropical_fish": "热带鱼", "entity.minecraft.tropical_fish.predefined.0": "海葵鱼", "entity.minecraft.tropical_fish.predefined.1": "黑刺尾鲷", "entity.minecraft.tropical_fish.predefined.10": "镰鱼", "entity.minecraft.tropical_fish.predefined.11": "华丽蝴蝶鱼", "entity.minecraft.tropical_fish.predefined.12": "鹦嘴鱼", "entity.minecraft.tropical_fish.predefined.13": "额斑刺蝶鱼", "entity.minecraft.tropical_fish.predefined.14": "红丽鱼", "entity.minecraft.tropical_fish.predefined.15": "红唇真蛇鳚", "entity.minecraft.tropical_fish.predefined.16": "红边笛鲷", "entity.minecraft.tropical_fish.predefined.17": "马鲅", "entity.minecraft.tropical_fish.predefined.18": "白条双锯鱼", "entity.minecraft.tropical_fish.predefined.19": "鳞鲀", "entity.minecraft.tropical_fish.predefined.2": "蓝刺尾鲷", "entity.minecraft.tropical_fish.predefined.20": "高鳍鹦嘴鱼", "entity.minecraft.tropical_fish.predefined.21": "黄刺尾鲷", "entity.minecraft.tropical_fish.predefined.3": "蝴蝶鱼", "entity.minecraft.tropical_fish.predefined.4": "丽鱼", "entity.minecraft.tropical_fish.predefined.5": "小丑鱼", "entity.minecraft.tropical_fish.predefined.6": "五彩搏鱼", "entity.minecraft.tropical_fish.predefined.7": "绣雀鲷", "entity.minecraft.tropical_fish.predefined.8": "川纹笛鲷", "entity.minecraft.tropical_fish.predefined.9": "拟羊鱼", "entity.minecraft.tropical_fish.type.betty": "背蒂类", "entity.minecraft.tropical_fish.type.blockfish": "方身类", "entity.minecraft.tropical_fish.type.brinely": "咸水类", "entity.minecraft.tropical_fish.type.clayfish": "陶鱼类", "entity.minecraft.tropical_fish.type.dasher": "速跃类", "entity.minecraft.tropical_fish.type.flopper": "飞翼类", "entity.minecraft.tropical_fish.type.glitter": "闪鳞类", "entity.minecraft.tropical_fish.type.kob": "石首类", "entity.minecraft.tropical_fish.type.snooper": "窥伺类", "entity.minecraft.tropical_fish.type.spotty": "多斑类", "entity.minecraft.tropical_fish.type.stripey": "条纹类", "entity.minecraft.tropical_fish.type.sunstreak": "日纹类", "entity.minecraft.turtle": "海龟", "entity.minecraft.vex": "恼鬼", "entity.minecraft.villager": "村民", "entity.minecraft.villager.armorer": "盔甲匠", "entity.minecraft.villager.butcher": "屠夫", "entity.minecraft.villager.cartographer": "制图师", "entity.minecraft.villager.cleric": "牧师", "entity.minecraft.villager.farmer": "农民", "entity.minecraft.villager.fisherman": "渔夫", "entity.minecraft.villager.fletcher": "制箭师", "entity.minecraft.villager.leatherworker": "皮匠", "entity.minecraft.villager.librarian": "图书管理员", "entity.minecraft.villager.mason": "石匠", "entity.minecraft.villager.nitwit": "傻子", "entity.minecraft.villager.none": "村民", "entity.minecraft.villager.shepherd": "牧羊人", "entity.minecraft.villager.toolsmith": "工具匠", "entity.minecraft.villager.weaponsmith": "武器匠", "entity.minecraft.vindicator": "卫道士", "entity.minecraft.wandering_trader": "流浪商人", "entity.minecraft.warden": "监守者", "entity.minecraft.wind_charge": "风弹", "entity.minecraft.witch": "女巫", "entity.minecraft.wither": "凋灵", "entity.minecraft.wither_skeleton": "凋灵骷髅", "entity.minecraft.wither_skull": "凋灵之首", "entity.minecraft.wolf": "狼", "entity.minecraft.zoglin": "僵尸疣猪兽", "entity.minecraft.zombie": "僵尸", "entity.minecraft.zombie_horse": "僵尸马", "entity.minecraft.zombie_villager": "僵尸村民", "entity.minecraft.zombified_piglin": "僵尸猪灵", "entity.not_summonable": "无法召唤类型为%s的实体", "event.minecraft.raid": "袭击", "event.minecraft.raid.defeat": "失败", "event.minecraft.raid.defeat.full": "袭击 - 失败", "event.minecraft.raid.raiders_remaining": "剩余%s名袭击者", "event.minecraft.raid.victory": "胜利", "event.minecraft.raid.victory.full": "袭击 - 胜利", "filled_map.buried_treasure": "藏宝图", "filled_map.explorer_jungle": "丛林探险家地图", "filled_map.explorer_swamp": "沼泽探险家地图", "filled_map.id": "编号#%s", "filled_map.level": "（等级 %s/%s）", "filled_map.locked": "已锁定", "filled_map.mansion": "林地探险家地图", "filled_map.monument": "海洋探险家地图", "filled_map.scale": "比例尺1:%s", "filled_map.trial_chambers": "试炼探险家地图", "filled_map.unknown": "未知地图", "filled_map.village_desert": "沙漠村庄地图", "filled_map.village_plains": "平原村庄地图", "filled_map.village_savanna": "热带草原村庄地图", "filled_map.village_snowy": "雪原村庄地图", "filled_map.village_taiga": "针叶林村庄地图", "flat_world_preset.minecraft.bottomless_pit": "无底深渊", "flat_world_preset.minecraft.classic_flat": "经典平坦", "flat_world_preset.minecraft.desert": "沙漠", "flat_world_preset.minecraft.overworld": "主世界", "flat_world_preset.minecraft.redstone_ready": "红石俱备", "flat_world_preset.minecraft.snowy_kingdom": "雪之王国", "flat_world_preset.minecraft.the_void": "虚空", "flat_world_preset.minecraft.tunnelers_dream": "挖掘工的梦想", "flat_world_preset.minecraft.water_world": "水世界", "flat_world_preset.unknown": "???", "gameMode.adventure": "冒险模式", "gameMode.changed": "你的游戏模式已被更新为%s", "gameMode.creative": "创造模式", "gameMode.hardcore": "极限模式", "gameMode.spectator": "旁观模式", "gameMode.survival": "生存模式", "gamerule.allowFireTicksAwayFromPlayer": "允许火在远离玩家处蔓延", "gamerule.allowFireTicksAwayFromPlayer.description": "控制火和熔岩是否能够在距离任何玩家超过8个区块处蔓延", "gamerule.announceAdvancements": "进度通知", "gamerule.blockExplosionDropDecay": "在方块交互爆炸中，一些方块不会掉落战利品", "gamerule.blockExplosionDropDecay.description": "在与方块交互引起的爆炸中，部分被破坏方块的掉落物会被炸毁。", "gamerule.category.chat": "聊天", "gamerule.category.drops": "掉落", "gamerule.category.misc": "杂项", "gamerule.category.mobs": "生物", "gamerule.category.player": "玩家", "gamerule.category.spawning": "生成", "gamerule.category.updates": "世界更新", "gamerule.commandBlockOutput": "广播命令方块输出", "gamerule.commandModificationBlockLimit": "命令修改方块数量限制", "gamerule.commandModificationBlockLimit.description": "单条命令（如fill和clone）最多能更改的方块数量", "gamerule.disableElytraMovementCheck": "禁用鞘翅移动检测", "gamerule.disablePlayerMovementCheck": "禁用玩家移动检测", "gamerule.disableRaids": "禁用袭击", "gamerule.doDaylightCycle": "游戏内时间流逝", "gamerule.doEntityDrops": "非生物实体掉落", "gamerule.doEntityDrops.description": "控制矿车（包括内容物）、物品展示框、船等的物品掉落。", "gamerule.doFireTick": "火焰蔓延", "gamerule.doImmediateRespawn": "立即重生", "gamerule.doInsomnia": "生成幻翼", "gamerule.doLimitedCrafting": "合成需要配方", "gamerule.doLimitedCrafting.description": "若启用，玩家只能使用已解锁的配方合成。", "gamerule.doMobLoot": "生物战利品掉落", "gamerule.doMobLoot.description": "控制生物死亡后是否掉落资源，包括经验球。", "gamerule.doMobSpawning": "生成生物", "gamerule.doMobSpawning.description": "一些实体可能有其特定的规则。", "gamerule.doPatrolSpawning": "生成灾厄巡逻队", "gamerule.doTileDrops": "方块掉落", "gamerule.doTileDrops.description": "控制破坏方块后是否掉落资源，包括经验球。", "gamerule.doTraderSpawning": "生成流浪商人", "gamerule.doVinesSpread": "藤蔓蔓延", "gamerule.doVinesSpread.description": "控制藤蔓方块是否会随机向相邻的方块蔓延。不会影响其他藤蔓类方块（例如垂泪藤和缠怨藤等）。", "gamerule.doWardenSpawning": "生成监守者", "gamerule.doWeatherCycle": "天气更替", "gamerule.drowningDamage": "溺水伤害", "gamerule.enderPearlsVanishOnDeath": "掷出的末影珍珠在死亡时消失", "gamerule.enderPearlsVanishOnDeath.description": "玩家投掷的末影珍珠是否在玩家死亡时消失。", "gamerule.entitiesWithPassengersCanUsePortals": "被骑乘的实体能否使用传送门", "gamerule.entitiesWithPassengersCanUsePortals.description": "允许被骑乘的实体通过下界传送门、末地传送门和末地折跃门传送。", "gamerule.fallDamage": "摔落伤害", "gamerule.fireDamage": "火焰伤害", "gamerule.forgiveDeadPlayers": "宽恕死亡玩家", "gamerule.forgiveDeadPlayers.description": "愤怒的中立生物将在其目标玩家于附近死亡后息怒。", "gamerule.freezeDamage": "冰冻伤害", "gamerule.globalSoundEvents": "全局声音事件", "gamerule.globalSoundEvents.description": "特定游戏事件（如Boss生成）发生时，声音可在所有地方听见。", "gamerule.keepInventory": "死亡后保留物品栏", "gamerule.lavaSourceConversion": "允许流动熔岩转化为熔岩源", "gamerule.lavaSourceConversion.description": "流动熔岩在两面与熔岩源相邻时转化为熔岩源。", "gamerule.locatorBar": "启用玩家定位栏", "gamerule.locatorBar.description": "启用后，屏幕上会显示指示玩家方位的定位栏。", "gamerule.logAdminCommands": "通告管理员命令", "gamerule.maxCommandChainLength": "命令连锁执行数量限制", "gamerule.maxCommandChainLength.description": "应用于命令方块链和函数。", "gamerule.maxCommandForkCount": "命令上下文数量限制", "gamerule.maxCommandForkCount.description": "“execute as”等命令可以使用的上下文数量最大值。", "gamerule.maxEntityCramming": "实体挤压上限", "gamerule.minecartMaxSpeed": "矿车最大速度", "gamerule.minecartMaxSpeed.description": "矿车在地面上移动的默认最大速度。", "gamerule.mobExplosionDropDecay": "在生物爆炸中，一些方块不会掉落战利品", "gamerule.mobExplosionDropDecay.description": "在生物引起的爆炸中，部分被破坏方块的掉落物会被炸毁。", "gamerule.mobGriefing": "允许破坏性生物行为", "gamerule.naturalRegeneration": "生命值自然恢复", "gamerule.playersNetherPortalCreativeDelay": "创造模式下玩家在下界传送门中等待的时间", "gamerule.playersNetherPortalCreativeDelay.description": "创造模式下的玩家通过下界传送门前往其他维度前需要站在其中等待的时间（以刻为单位）。", "gamerule.playersNetherPortalDefaultDelay": "非创造模式下玩家在下界传送门中等待的时间", "gamerule.playersNetherPortalDefaultDelay.description": "非创造模式下的玩家通过下界传送门前往其他维度前需要站在其中等待的时间（以刻为单位）。", "gamerule.playersSleepingPercentage": "入睡占比", "gamerule.playersSleepingPercentage.description": "跳过夜晚所需的入睡玩家占比。", "gamerule.projectilesCanBreakBlocks": "弹射物能否破坏方块", "gamerule.projectilesCanBreakBlocks.description": "控制弹射物是否能破坏可被其破坏的方块。", "gamerule.randomTickSpeed": "随机刻速率", "gamerule.reducedDebugInfo": "简化调试信息", "gamerule.reducedDebugInfo.description": "限制调试屏幕内容。", "gamerule.sendCommandFeedback": "发送命令反馈", "gamerule.showDeathMessages": "显示死亡消息", "gamerule.snowAccumulationHeight": "积雪厚度", "gamerule.snowAccumulationHeight.description": "降雪时，地面上的雪最多堆积到此处指定的层数。", "gamerule.spawnChunkRadius": "出生区块半径", "gamerule.spawnChunkRadius.description": "主世界出生点周围保持加载的区块数量。", "gamerule.spawnRadius": "重生点半径", "gamerule.spawnRadius.description": "控制适合玩家生成的出生点周围区域大小。", "gamerule.spectatorsGenerateChunks": "允许旁观者生成地形", "gamerule.tntExplodes": "允许TNT被点燃并爆炸", "gamerule.tntExplosionDropDecay": "在TNT爆炸中，一些方块不会掉落战利品", "gamerule.tntExplosionDropDecay.description": "在TNT引起的爆炸中，部分被破坏方块的掉落物会被炸毁。", "gamerule.universalAnger": "无差别愤怒", "gamerule.universalAnger.description": "愤怒的中立生物将攻击附近的所有玩家，而不再限于激怒它们的玩家。禁用“宽恕死亡玩家”可达到最佳效果。", "gamerule.waterSourceConversion": "允许流动水转化为水源", "gamerule.waterSourceConversion.description": "流动水在两面与水源相邻时转化为水源。", "generator.custom": "自定义", "generator.customized": "旧版自定义", "generator.minecraft.amplified": "放大化", "generator.minecraft.amplified.info": "注意：仅供娱乐！需要强劲的电脑。", "generator.minecraft.debug_all_block_states": "调试模式", "generator.minecraft.flat": "超平坦", "generator.minecraft.large_biomes": "巨型生物群系", "generator.minecraft.normal": "默认", "generator.minecraft.single_biome_surface": "单一生物群系", "generator.single_biome_caves": "洞穴", "generator.single_biome_floating_islands": "浮岛", "gui.abuseReport.attestation": "提交此举报，即代表你确认你提供的信息已尽可能准确和完整。", "gui.abuseReport.comments": "留言", "gui.abuseReport.describe": "提供详情可以帮助我们更严谨地做出决定。", "gui.abuseReport.discard.content": "如果离开，该举报及留言将不会被保留。\n确定要离开吗？", "gui.abuseReport.discard.discard": "离开并放弃举报", "gui.abuseReport.discard.draft": "保存为草稿", "gui.abuseReport.discard.return": "继续编辑", "gui.abuseReport.discard.title": "放弃举报和留言？", "gui.abuseReport.draft.content": "继续编辑现有举报还是放弃并新建另一份举报？", "gui.abuseReport.draft.discard": "放弃", "gui.abuseReport.draft.edit": "继续编辑", "gui.abuseReport.draft.quittotitle.content": "继续编辑还是放弃？", "gui.abuseReport.draft.quittotitle.title": "该聊天举报草稿将在退出时丢失", "gui.abuseReport.draft.title": "编辑聊天举报草稿？", "gui.abuseReport.error.title": "发送举报时出现问题", "gui.abuseReport.message": "你在哪里观察到了不良行为？\n这有助于我们调查你的案例。", "gui.abuseReport.more_comments": "请描述发生的状况：", "gui.abuseReport.name.comment_box_label": "请描述举报该名称的理由：", "gui.abuseReport.name.reporting": "正在举报“%s”。", "gui.abuseReport.name.title": "举报玩家名称", "gui.abuseReport.observed_what": "你为什么要举报？", "gui.abuseReport.read_info": "了解举报功能", "gui.abuseReport.reason.alcohol_tobacco_drugs": "吸毒或饮酒", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "有人教唆他人涉毒或教唆未成年人饮酒。", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "对儿童的性剥削或虐待", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "有人谈论或以其他方式宣扬涉及儿童的不当行为。", "gui.abuseReport.reason.defamation_impersonation_false_information": "诽谤", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "有人以利用或误导他人为目的，正在损害你或他人的名誉，例如散布虚假信息。", "gui.abuseReport.reason.description": "描述：", "gui.abuseReport.reason.false_reporting": "不实举报", "gui.abuseReport.reason.generic": "我想举报该玩家", "gui.abuseReport.reason.generic.description": "该玩家使我恼怒或做的事情使我反感。", "gui.abuseReport.reason.harassment_or_bullying": "骚扰或霸凌", "gui.abuseReport.reason.harassment_or_bullying.description": "有人羞辱、攻击、霸凌你或其他人。这包括在未经允许的情况下不断尝试联系你或其他人，或发布你或其他人的隐私信息。", "gui.abuseReport.reason.hate_speech": "仇恨言论", "gui.abuseReport.reason.hate_speech.description": "有人因身份要素（如宗教信仰、种族或性相关）攻击你或其他玩家。", "gui.abuseReport.reason.imminent_harm": "威胁伤害他人", "gui.abuseReport.reason.imminent_harm.description": "有人威胁要在现实生活中伤害你或其他人。", "gui.abuseReport.reason.narration": "%s：%s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "未经同意发布私密图像", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "有人谈论、分享私密或亲密的图像，或以其他方式宣扬有关行为。", "gui.abuseReport.reason.self_harm_or_suicide": "自残或自杀", "gui.abuseReport.reason.self_harm_or_suicide.description": "有人威胁要自残或谈论现实生活中的自残行为。", "gui.abuseReport.reason.sexually_inappropriate": "性相关不当内容", "gui.abuseReport.reason.sexually_inappropriate.description": "皮肤图案与性行为、性器官和性暴力相关。", "gui.abuseReport.reason.terrorism_or_violent_extremism": "恐怖主义或暴力极端主义", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "有人因政治、宗教、意识形态或其他原因，谈论、宣扬、威胁实施恐怖主义或极端暴力行为。", "gui.abuseReport.reason.title": "选择举报类型", "gui.abuseReport.report_sent_msg": "我们已经成功收到你的举报。非常感谢！\n\n我们的团队将尽快进行审核。", "gui.abuseReport.select_reason": "选择举报类型", "gui.abuseReport.send": "发送举报", "gui.abuseReport.send.comment_too_long": "请缩短留言", "gui.abuseReport.send.error_message": "发送举报时返回了错误：\n“%s”", "gui.abuseReport.send.generic_error": "发送举报时遇到意外错误。", "gui.abuseReport.send.http_error": "发送举报时发生了意外的HTTP错误。", "gui.abuseReport.send.json_error": "发送举报时遇到了格式错误的负载。", "gui.abuseReport.send.no_reason": "请选择举报类型", "gui.abuseReport.send.not_attested": "请先阅读上方文本并勾选复选框后，再发送举报", "gui.abuseReport.send.service_unavailable": "无法使用举报服务。请检查是否已联网，然后重试。", "gui.abuseReport.sending.title": "发送举报中…", "gui.abuseReport.sent.title": "举报已发送", "gui.abuseReport.skin.title": "举报玩家皮肤", "gui.abuseReport.title": "举报玩家", "gui.abuseReport.type.chat": "聊天消息", "gui.abuseReport.type.name": "玩家名称", "gui.abuseReport.type.skin": "玩家皮肤", "gui.acknowledge": "了解", "gui.advancements": "进度", "gui.all": "全部", "gui.back": "返回", "gui.banned.description": "%s\n\n%s\n\n点击以下链接了解更多：%s", "gui.banned.description.permanent": "你的账户已被永久封禁，无法进行在线游戏或加入Realms。", "gui.banned.description.reason": "我们最近收到举报称你的账户存在不良行为。我们的监督员已审核你的案件并将其认定为%s，这违反了Minecraft社区准则。", "gui.banned.description.reason_id": "代码：%s", "gui.banned.description.reason_id_message": "代码：%s - %s", "gui.banned.description.temporary": "%s期限结束前，你无法进行在线游戏或加入Realms。", "gui.banned.description.temporary.duration": "你的账户已被暂时封禁并将于%s后解封。", "gui.banned.description.unknownreason": "我们最近收到举报称你的账户存在不良行为。我们的监督员已审核你的案件并认定你违反了Minecraft社区准则。", "gui.banned.name.description": "你当前的名称（“%s”）违反了我们的社区准则。你可以进行单人游戏，但需要更改名称才能进行在线游戏。\n\n通过以下链接了解更多或提交案件审查：%s", "gui.banned.name.title": "多人游戏中不允许使用当前名称", "gui.banned.reason.defamation_impersonation_false_information": "假冒或分享信息以利用或误导他人", "gui.banned.reason.drugs": "推销非法药品", "gui.banned.reason.extreme_violence_or_gore": "描述现实生活中过度暴力或血腥的场面", "gui.banned.reason.false_reporting": "过多虚假或不实举报", "gui.banned.reason.fraud": "欺骗性获取或使用内容", "gui.banned.reason.generic_violation": "违反社区准则", "gui.banned.reason.harassment_or_bullying": "有针对性地、有害地使用辱骂性语言", "gui.banned.reason.hate_speech": "仇恨或歧视言论", "gui.banned.reason.hate_terrorism_notorious_figure": "推广仇恨群体、恐怖组织或不法分子", "gui.banned.reason.imminent_harm_to_person_or_property": "意图在现实生活中造成人身或财产伤害", "gui.banned.reason.nudity_or_pornography": "展示淫秽或色情材料", "gui.banned.reason.sexually_inappropriate": "性相关话题或内容", "gui.banned.reason.spam_or_advertising": "滥发消息或广告宣传", "gui.banned.skin.description": "你当前的皮肤违反了我们的社区准则。你仍然可以使用默认皮肤，或者选择新皮肤进行游戏。\n\n通过以下链接了解更多或提交案件审查：%s", "gui.banned.skin.title": "当前皮肤不允许使用", "gui.banned.title.permanent": "账户已被永久封禁", "gui.banned.title.temporary": "账户已被暂时封禁", "gui.cancel": "取消", "gui.chatReport.comments": "留言", "gui.chatReport.describe": "提供详情可以帮助我们更严谨地做出决定。", "gui.chatReport.discard.content": "如果离开，该举报及留言将不会被保留。\n确定要离开吗？", "gui.chatReport.discard.discard": "离开并放弃举报", "gui.chatReport.discard.draft": "保存为草稿", "gui.chatReport.discard.return": "继续编辑", "gui.chatReport.discard.title": "放弃举报和留言？", "gui.chatReport.draft.content": "继续编辑现有举报还是放弃并新建另一份举报？", "gui.chatReport.draft.discard": "放弃", "gui.chatReport.draft.edit": "继续编辑", "gui.chatReport.draft.quittotitle.content": "继续编辑还是放弃？", "gui.chatReport.draft.quittotitle.title": "你有一份聊天举报草稿将在退出时丢失", "gui.chatReport.draft.title": "编辑聊天举报草稿？", "gui.chatReport.more_comments": "请描述发生的状况：", "gui.chatReport.observed_what": "你为什么要举报？", "gui.chatReport.read_info": "了解举报功能", "gui.chatReport.report_sent_msg": "我们已经成功收到你的举报。非常感谢！\n\n我们的团队会尽快进行审核。", "gui.chatReport.select_chat": "选择要举报的聊天消息", "gui.chatReport.select_reason": "选择举报类型", "gui.chatReport.selected_chat": "已选中%s条要举报的聊天消息", "gui.chatReport.send": "发送举报", "gui.chatReport.send.comments_too_long": "请缩短留言", "gui.chatReport.send.no_reason": "请选择举报类型", "gui.chatReport.send.no_reported_messages": "请选择至少一条要举报的聊天消息", "gui.chatReport.send.too_many_messages": "举报中包含的消息过多", "gui.chatReport.title": "举报玩家聊天", "gui.chatSelection.context": "所选消息前后的部分消息将会用于提供额外的辅助信息", "gui.chatSelection.fold": "已隐藏%s条消息", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s加入了聊天", "gui.chatSelection.message.narrate": "%1$s在%3$s说：%2$s", "gui.chatSelection.selected": "已选中%s条消息，共%s条", "gui.chatSelection.title": "选择要举报的聊天消息", "gui.continue": "继续", "gui.copy_link_to_clipboard": "复制链接到剪贴板", "gui.days": "%s天", "gui.done": "完成", "gui.down": "向下", "gui.entity_tooltip.type": "类型：%s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "拒绝了%s个文件", "gui.fileDropFailure.title": "添加文件失败", "gui.hours": "%s小时", "gui.loadingMinecraft": "正在加载Minecraft", "gui.minutes": "%s分钟", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s按钮", "gui.narrate.editBox": "%s编辑框：%s", "gui.narrate.slider": "%s滑块", "gui.narrate.tab": "%s标签页", "gui.no": "否", "gui.none": "无", "gui.ok": "确定", "gui.open_report_dir": "打开报告目录", "gui.proceed": "继续", "gui.recipebook.moreRecipes": "单击鼠标右键获取更多信息", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "搜索…", "gui.recipebook.toggleRecipes.all": "显示全部", "gui.recipebook.toggleRecipes.blastable": "仅显示可冶炼", "gui.recipebook.toggleRecipes.craftable": "仅显示可合成", "gui.recipebook.toggleRecipes.smeltable": "仅显示可烧炼", "gui.recipebook.toggleRecipes.smokable": "仅显示可熏制", "gui.report_to_server": "向服务器报告", "gui.socialInteractions.blocking_hint": "使用Microsoft账户管理", "gui.socialInteractions.empty_blocked": "未屏蔽任何玩家的聊天消息", "gui.socialInteractions.empty_hidden": "未隐藏任何玩家的聊天消息", "gui.socialInteractions.hidden_in_chat": "%s的聊天消息将会被隐藏", "gui.socialInteractions.hide": "在聊天中隐藏", "gui.socialInteractions.narration.hide": "隐藏%s发送的消息", "gui.socialInteractions.narration.report": "举报玩家%s", "gui.socialInteractions.narration.show": "显示%s发送的消息", "gui.socialInteractions.report": "举报", "gui.socialInteractions.search_empty": "未找到使用此名称的玩家", "gui.socialInteractions.search_hint": "搜索…", "gui.socialInteractions.server_label.multiple": "%s - %s名玩家", "gui.socialInteractions.server_label.single": "%s - %s名玩家", "gui.socialInteractions.show": "在聊天中显示", "gui.socialInteractions.shown_in_chat": "%s的聊天消息将会被显示", "gui.socialInteractions.status_blocked": "已屏蔽", "gui.socialInteractions.status_blocked_offline": "已屏蔽 - 离线", "gui.socialInteractions.status_hidden": "隐藏", "gui.socialInteractions.status_hidden_offline": "隐藏 - 离线", "gui.socialInteractions.status_offline": "离线", "gui.socialInteractions.tab_all": "全部", "gui.socialInteractions.tab_blocked": "已屏蔽", "gui.socialInteractions.tab_hidden": "已隐藏", "gui.socialInteractions.title": "社交", "gui.socialInteractions.tooltip.hide": "隐藏消息", "gui.socialInteractions.tooltip.report": "举报玩家", "gui.socialInteractions.tooltip.report.disabled": "举报服务不可用", "gui.socialInteractions.tooltip.report.no_messages": "玩家%s没有可举报的消息", "gui.socialInteractions.tooltip.report.not_reportable": "无法举报该玩家，服务器无法验证其聊天消息。", "gui.socialInteractions.tooltip.show": "显示消息", "gui.stats": "统计信息", "gui.toMenu": "返回到服务器列表", "gui.toRealms": "返回到Realm列表", "gui.toTitle": "返回到标题屏幕", "gui.toWorld": "返回到世界列表", "gui.togglable_slot": "点击以禁用槽位", "gui.up": "向上", "gui.waitingForResponse.button.inactive": "返回（%s秒）", "gui.waitingForResponse.title": "正在等待服务器响应", "gui.yes": "是", "hanging_sign.edit": "编辑悬挂式告示牌消息", "instrument.minecraft.admire_goat_horn": "仰慕", "instrument.minecraft.call_goat_horn": "呼唤", "instrument.minecraft.dream_goat_horn": "想象", "instrument.minecraft.feel_goat_horn": "感受", "instrument.minecraft.ponder_goat_horn": "沉思", "instrument.minecraft.seek_goat_horn": "寻觅", "instrument.minecraft.sing_goat_horn": "歌颂", "instrument.minecraft.yearn_goat_horn": "憧憬", "inventory.binSlot": "摧毁物品", "inventory.hotbarInfo": "用%1$s+%2$s来保存快捷栏", "inventory.hotbarSaved": "已保存物品快捷栏（用%1$s+%2$s来加载）", "item.canBreak": "能破坏：", "item.canPlace": "可以放在：", "item.canUse.unknown": "未知", "item.color": "颜色：%s", "item.components": "%s个组件", "item.disabled": "已禁用物品", "item.durability": "耐久度：%s / %s", "item.dyed": "已染色", "item.minecraft.acacia_boat": "金合欢木船", "item.minecraft.acacia_chest_boat": "金合欢木运输船", "item.minecraft.allay_spawn_egg": "悦灵刷怪蛋", "item.minecraft.amethyst_shard": "紫水晶碎片", "item.minecraft.angler_pottery_shard": "垂钓纹样陶片", "item.minecraft.angler_pottery_sherd": "垂钓纹样陶片", "item.minecraft.apple": "苹果", "item.minecraft.archer_pottery_shard": "弓箭纹样陶片", "item.minecraft.archer_pottery_sherd": "弓箭纹样陶片", "item.minecraft.armadillo_scute": "犰狳鳞甲", "item.minecraft.armadillo_spawn_egg": "犰狳刷怪蛋", "item.minecraft.armor_stand": "盔甲架", "item.minecraft.arms_up_pottery_shard": "举臂纹样陶片", "item.minecraft.arms_up_pottery_sherd": "举臂纹样陶片", "item.minecraft.arrow": "箭", "item.minecraft.axolotl_bucket": "美西螈桶", "item.minecraft.axolotl_spawn_egg": "美西螈刷怪蛋", "item.minecraft.baked_potato": "烤马铃薯", "item.minecraft.bamboo_chest_raft": "运输竹筏", "item.minecraft.bamboo_raft": "竹筏", "item.minecraft.bat_spawn_egg": "蝙蝠刷怪蛋", "item.minecraft.bee_spawn_egg": "蜜蜂刷怪蛋", "item.minecraft.beef": "生牛肉", "item.minecraft.beetroot": "甜菜根", "item.minecraft.beetroot_seeds": "甜菜种子", "item.minecraft.beetroot_soup": "甜菜汤", "item.minecraft.birch_boat": "白桦木船", "item.minecraft.birch_chest_boat": "白桦木运输船", "item.minecraft.black_bundle": "黑色收纳袋", "item.minecraft.black_dye": "黑色染料", "item.minecraft.black_harness": "黑色挽具", "item.minecraft.blade_pottery_shard": "利刃纹样陶片", "item.minecraft.blade_pottery_sherd": "利刃纹样陶片", "item.minecraft.blaze_powder": "烈焰粉", "item.minecraft.blaze_rod": "烈焰棒", "item.minecraft.blaze_spawn_egg": "烈焰人刷怪蛋", "item.minecraft.blue_bundle": "蓝色收纳袋", "item.minecraft.blue_dye": "蓝色染料", "item.minecraft.blue_egg": "蓝色鸡蛋", "item.minecraft.blue_harness": "蓝色挽具", "item.minecraft.bogged_spawn_egg": "沼骸刷怪蛋", "item.minecraft.bolt_armor_trim_smithing_template": "锻造模板", "item.minecraft.bolt_armor_trim_smithing_template.new": "镶铆盔甲纹饰", "item.minecraft.bone": "骨头", "item.minecraft.bone_meal": "骨粉", "item.minecraft.book": "书", "item.minecraft.bordure_indented_banner_pattern": "波纹边旗帜图案", "item.minecraft.bow": "弓", "item.minecraft.bowl": "碗", "item.minecraft.bread": "面包", "item.minecraft.breeze_rod": "旋风棒", "item.minecraft.breeze_spawn_egg": "旋风人刷怪蛋", "item.minecraft.brewer_pottery_shard": "佳酿纹样陶片", "item.minecraft.brewer_pottery_sherd": "佳酿纹样陶片", "item.minecraft.brewing_stand": "酿造台", "item.minecraft.brick": "红砖", "item.minecraft.brown_bundle": "棕色收纳袋", "item.minecraft.brown_dye": "棕色染料", "item.minecraft.brown_egg": "棕色鸡蛋", "item.minecraft.brown_harness": "棕色挽具", "item.minecraft.brush": "刷子", "item.minecraft.bucket": "铁桶", "item.minecraft.bundle": "收纳袋", "item.minecraft.bundle.empty": "空", "item.minecraft.bundle.empty.description": "可容纳一组混合的物品", "item.minecraft.bundle.full": "满", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "烈焰纹样陶片", "item.minecraft.burn_pottery_sherd": "烈焰纹样陶片", "item.minecraft.camel_spawn_egg": "骆驼刷怪蛋", "item.minecraft.carrot": "胡萝卜", "item.minecraft.carrot_on_a_stick": "胡萝卜钓竿", "item.minecraft.cat_spawn_egg": "猫刷怪蛋", "item.minecraft.cauldron": "炼药锅", "item.minecraft.cave_spider_spawn_egg": "洞穴蜘蛛刷怪蛋", "item.minecraft.chainmail_boots": "锁链靴子", "item.minecraft.chainmail_chestplate": "锁链胸甲", "item.minecraft.chainmail_helmet": "锁链头盔", "item.minecraft.chainmail_leggings": "锁链护腿", "item.minecraft.charcoal": "木炭", "item.minecraft.cherry_boat": "樱花木船", "item.minecraft.cherry_chest_boat": "樱花木运输船", "item.minecraft.chest_minecart": "运输矿车", "item.minecraft.chicken": "生鸡肉", "item.minecraft.chicken_spawn_egg": "鸡刷怪蛋", "item.minecraft.chorus_fruit": "紫颂果", "item.minecraft.clay_ball": "黏土球", "item.minecraft.clock": "时钟", "item.minecraft.coal": "煤炭", "item.minecraft.coast_armor_trim_smithing_template": "锻造模板", "item.minecraft.coast_armor_trim_smithing_template.new": "海岸盔甲纹饰", "item.minecraft.cocoa_beans": "可可豆", "item.minecraft.cod": "生鳕鱼", "item.minecraft.cod_bucket": "鳕鱼桶", "item.minecraft.cod_spawn_egg": "鳕鱼刷怪蛋", "item.minecraft.command_block_minecart": "命令方块矿车", "item.minecraft.compass": "指南针", "item.minecraft.cooked_beef": "牛排", "item.minecraft.cooked_chicken": "熟鸡肉", "item.minecraft.cooked_cod": "熟鳕鱼", "item.minecraft.cooked_mutton": "熟羊肉", "item.minecraft.cooked_porkchop": "熟猪排", "item.minecraft.cooked_rabbit": "熟兔肉", "item.minecraft.cooked_salmon": "熟鲑鱼", "item.minecraft.cookie": "曲奇", "item.minecraft.copper_ingot": "铜锭", "item.minecraft.cow_spawn_egg": "牛刷怪蛋", "item.minecraft.creaking_spawn_egg": "嘎枝刷怪蛋", "item.minecraft.creeper_banner_pattern": "旗帜图案", "item.minecraft.creeper_banner_pattern.desc": "苦力怕盾徽", "item.minecraft.creeper_banner_pattern.new": "苦力怕盾徽旗帜图案", "item.minecraft.creeper_spawn_egg": "苦力怕刷怪蛋", "item.minecraft.crossbow": "弩", "item.minecraft.crossbow.projectile": "弹射物：", "item.minecraft.crossbow.projectile.multiple": "弹射物：%s × %s", "item.minecraft.crossbow.projectile.single": "弹射物：%s", "item.minecraft.cyan_bundle": "青色收纳袋", "item.minecraft.cyan_dye": "青色染料", "item.minecraft.cyan_harness": "青色挽具", "item.minecraft.danger_pottery_shard": "危机纹样陶片", "item.minecraft.danger_pottery_sherd": "危机纹样陶片", "item.minecraft.dark_oak_boat": "深色橡木船", "item.minecraft.dark_oak_chest_boat": "深色橡木运输船", "item.minecraft.debug_stick": "调试棒", "item.minecraft.debug_stick.empty": "%s不具备属性", "item.minecraft.debug_stick.select": "已选择“%s”（%s）", "item.minecraft.debug_stick.update": "“%s”设为%s", "item.minecraft.diamond": "钻石", "item.minecraft.diamond_axe": "钻石斧", "item.minecraft.diamond_boots": "钻石靴子", "item.minecraft.diamond_chestplate": "钻石胸甲", "item.minecraft.diamond_helmet": "钻石头盔", "item.minecraft.diamond_hoe": "钻石锄", "item.minecraft.diamond_horse_armor": "钻石马铠", "item.minecraft.diamond_leggings": "钻石护腿", "item.minecraft.diamond_pickaxe": "钻石镐", "item.minecraft.diamond_shovel": "钻石锹", "item.minecraft.diamond_sword": "钻石剑", "item.minecraft.disc_fragment_5": "唱片残片", "item.minecraft.disc_fragment_5.desc": "音乐唱片 - 5", "item.minecraft.dolphin_spawn_egg": "海豚刷怪蛋", "item.minecraft.donkey_spawn_egg": "驴刷怪蛋", "item.minecraft.dragon_breath": "龙息", "item.minecraft.dried_kelp": "干海带", "item.minecraft.drowned_spawn_egg": "溺尸刷怪蛋", "item.minecraft.dune_armor_trim_smithing_template": "锻造模板", "item.minecraft.dune_armor_trim_smithing_template.new": "沙丘盔甲纹饰", "item.minecraft.echo_shard": "回响碎片", "item.minecraft.egg": "鸡蛋", "item.minecraft.elder_guardian_spawn_egg": "远古守卫者刷怪蛋", "item.minecraft.elytra": "鞘翅", "item.minecraft.emerald": "绿宝石", "item.minecraft.enchanted_book": "附魔书", "item.minecraft.enchanted_golden_apple": "附魔金苹果", "item.minecraft.end_crystal": "末地水晶", "item.minecraft.ender_dragon_spawn_egg": "末影龙刷怪蛋", "item.minecraft.ender_eye": "末影之眼", "item.minecraft.ender_pearl": "末影珍珠", "item.minecraft.enderman_spawn_egg": "末影人刷怪蛋", "item.minecraft.endermite_spawn_egg": "末影螨刷怪蛋", "item.minecraft.evoker_spawn_egg": "唤魔者刷怪蛋", "item.minecraft.experience_bottle": "附魔之瓶", "item.minecraft.explorer_pottery_shard": "探险纹样陶片", "item.minecraft.explorer_pottery_sherd": "探险纹样陶片", "item.minecraft.eye_armor_trim_smithing_template": "锻造模板", "item.minecraft.eye_armor_trim_smithing_template.new": "眼眸盔甲纹饰", "item.minecraft.feather": "羽毛", "item.minecraft.fermented_spider_eye": "发酵蛛眼", "item.minecraft.field_masoned_banner_pattern": "砖纹旗帜图案", "item.minecraft.filled_map": "地图", "item.minecraft.fire_charge": "火焰弹", "item.minecraft.firework_rocket": "烟花火箭", "item.minecraft.firework_rocket.flight": "飞行时间：", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "烟火之星", "item.minecraft.firework_star.black": "黑色", "item.minecraft.firework_star.blue": "蓝色", "item.minecraft.firework_star.brown": "棕色", "item.minecraft.firework_star.custom_color": "自定义", "item.minecraft.firework_star.cyan": "青色", "item.minecraft.firework_star.fade_to": "淡化至", "item.minecraft.firework_star.flicker": "闪烁", "item.minecraft.firework_star.gray": "灰色", "item.minecraft.firework_star.green": "绿色", "item.minecraft.firework_star.light_blue": "淡蓝色", "item.minecraft.firework_star.light_gray": "淡灰色", "item.minecraft.firework_star.lime": "黄绿色", "item.minecraft.firework_star.magenta": "品红色", "item.minecraft.firework_star.orange": "橙色", "item.minecraft.firework_star.pink": "粉红色", "item.minecraft.firework_star.purple": "紫色", "item.minecraft.firework_star.red": "红色", "item.minecraft.firework_star.shape": "未知形状", "item.minecraft.firework_star.shape.burst": "喷发状", "item.minecraft.firework_star.shape.creeper": "苦力怕状", "item.minecraft.firework_star.shape.large_ball": "大型球状", "item.minecraft.firework_star.shape.small_ball": "小型球状", "item.minecraft.firework_star.shape.star": "星形", "item.minecraft.firework_star.trail": "踪迹", "item.minecraft.firework_star.white": "白色", "item.minecraft.firework_star.yellow": "黄色", "item.minecraft.fishing_rod": "钓鱼竿", "item.minecraft.flint": "燧石", "item.minecraft.flint_and_steel": "打火石", "item.minecraft.flow_armor_trim_smithing_template": "锻造模板", "item.minecraft.flow_armor_trim_smithing_template.new": "涡流盔甲纹饰", "item.minecraft.flow_banner_pattern": "旗帜图案", "item.minecraft.flow_banner_pattern.desc": "涡流", "item.minecraft.flow_banner_pattern.new": "涡流旗帜图案", "item.minecraft.flow_pottery_sherd": "涡流纹样陶片", "item.minecraft.flower_banner_pattern": "旗帜图案", "item.minecraft.flower_banner_pattern.desc": "花朵盾徽", "item.minecraft.flower_banner_pattern.new": "花朵盾徽旗帜图案", "item.minecraft.flower_pot": "花盆", "item.minecraft.fox_spawn_egg": "狐狸刷怪蛋", "item.minecraft.friend_pottery_shard": "挚友纹样陶片", "item.minecraft.friend_pottery_sherd": "挚友纹样陶片", "item.minecraft.frog_spawn_egg": "青蛙刷怪蛋", "item.minecraft.furnace_minecart": "动力矿车", "item.minecraft.ghast_spawn_egg": "恶魂刷怪蛋", "item.minecraft.ghast_tear": "恶魂之泪", "item.minecraft.glass_bottle": "玻璃瓶", "item.minecraft.glistering_melon_slice": "闪烁的西瓜片", "item.minecraft.globe_banner_pattern": "旗帜图案", "item.minecraft.globe_banner_pattern.desc": "地球", "item.minecraft.globe_banner_pattern.new": "地球旗帜图案", "item.minecraft.glow_berries": "发光浆果", "item.minecraft.glow_ink_sac": "荧光墨囊", "item.minecraft.glow_item_frame": "荧光物品展示框", "item.minecraft.glow_squid_spawn_egg": "发光鱿鱼刷怪蛋", "item.minecraft.glowstone_dust": "荧石粉", "item.minecraft.goat_horn": "山羊角", "item.minecraft.goat_spawn_egg": "山羊刷怪蛋", "item.minecraft.gold_ingot": "金锭", "item.minecraft.gold_nugget": "金粒", "item.minecraft.golden_apple": "金苹果", "item.minecraft.golden_axe": "金斧", "item.minecraft.golden_boots": "金靴子", "item.minecraft.golden_carrot": "金胡萝卜", "item.minecraft.golden_chestplate": "金胸甲", "item.minecraft.golden_helmet": "金头盔", "item.minecraft.golden_hoe": "金锄", "item.minecraft.golden_horse_armor": "金马铠", "item.minecraft.golden_leggings": "金护腿", "item.minecraft.golden_pickaxe": "金镐", "item.minecraft.golden_shovel": "金锹", "item.minecraft.golden_sword": "金剑", "item.minecraft.gray_bundle": "灰色收纳袋", "item.minecraft.gray_dye": "灰色染料", "item.minecraft.gray_harness": "灰色挽具", "item.minecraft.green_bundle": "绿色收纳袋", "item.minecraft.green_dye": "绿色染料", "item.minecraft.green_harness": "绿色挽具", "item.minecraft.guardian_spawn_egg": "守卫者刷怪蛋", "item.minecraft.gunpowder": "火药", "item.minecraft.guster_banner_pattern": "旗帜图案", "item.minecraft.guster_banner_pattern.desc": "旋风", "item.minecraft.guster_banner_pattern.new": "旋风旗帜图案", "item.minecraft.guster_pottery_sherd": "旋风纹样陶片", "item.minecraft.happy_ghast_spawn_egg": "快乐恶魂刷怪蛋", "item.minecraft.harness": "挽具", "item.minecraft.heart_of_the_sea": "海洋之心", "item.minecraft.heart_pottery_shard": "爱心纹样陶片", "item.minecraft.heart_pottery_sherd": "爱心纹样陶片", "item.minecraft.heartbreak_pottery_shard": "心碎纹样陶片", "item.minecraft.heartbreak_pottery_sherd": "心碎纹样陶片", "item.minecraft.hoglin_spawn_egg": "疣猪兽刷怪蛋", "item.minecraft.honey_bottle": "蜂蜜瓶", "item.minecraft.honeycomb": "蜜脾", "item.minecraft.hopper_minecart": "漏斗矿车", "item.minecraft.horse_spawn_egg": "马刷怪蛋", "item.minecraft.host_armor_trim_smithing_template": "锻造模板", "item.minecraft.host_armor_trim_smithing_template.new": "雇主盔甲纹饰", "item.minecraft.howl_pottery_shard": "狼嚎纹样陶片", "item.minecraft.howl_pottery_sherd": "狼嚎纹样陶片", "item.minecraft.husk_spawn_egg": "尸壳刷怪蛋", "item.minecraft.ink_sac": "墨囊", "item.minecraft.iron_axe": "铁斧", "item.minecraft.iron_boots": "铁靴子", "item.minecraft.iron_chestplate": "铁胸甲", "item.minecraft.iron_golem_spawn_egg": "铁傀儡刷怪蛋", "item.minecraft.iron_helmet": "铁头盔", "item.minecraft.iron_hoe": "铁锄", "item.minecraft.iron_horse_armor": "铁马铠", "item.minecraft.iron_ingot": "铁锭", "item.minecraft.iron_leggings": "铁护腿", "item.minecraft.iron_nugget": "铁粒", "item.minecraft.iron_pickaxe": "铁镐", "item.minecraft.iron_shovel": "铁锹", "item.minecraft.iron_sword": "铁剑", "item.minecraft.item_frame": "物品展示框", "item.minecraft.jungle_boat": "丛林木船", "item.minecraft.jungle_chest_boat": "丛林木运输船", "item.minecraft.knowledge_book": "知识之书", "item.minecraft.lapis_lazuli": "青金石", "item.minecraft.lava_bucket": "熔岩桶", "item.minecraft.lead": "拴绳", "item.minecraft.leather": "皮革", "item.minecraft.leather_boots": "皮革靴子", "item.minecraft.leather_chestplate": "皮革外套", "item.minecraft.leather_helmet": "皮革帽子", "item.minecraft.leather_horse_armor": "皮革马铠", "item.minecraft.leather_leggings": "皮革裤子", "item.minecraft.light_blue_bundle": "淡蓝色收纳袋", "item.minecraft.light_blue_dye": "淡蓝色染料", "item.minecraft.light_blue_harness": "淡蓝色挽具", "item.minecraft.light_gray_bundle": "淡灰色收纳袋", "item.minecraft.light_gray_dye": "淡灰色染料", "item.minecraft.light_gray_harness": "淡灰色挽具", "item.minecraft.lime_bundle": "黄绿色收纳袋", "item.minecraft.lime_dye": "黄绿色染料", "item.minecraft.lime_harness": "黄绿色挽具", "item.minecraft.lingering_potion": "滞留药水", "item.minecraft.lingering_potion.effect.awkward": "滞留型粗制的药水", "item.minecraft.lingering_potion.effect.empty": "不可合成的滞留型药水", "item.minecraft.lingering_potion.effect.fire_resistance": "滞留型抗火药水", "item.minecraft.lingering_potion.effect.harming": "滞留型伤害药水", "item.minecraft.lingering_potion.effect.healing": "滞留型治疗药水", "item.minecraft.lingering_potion.effect.infested": "滞留型虫蚀药水", "item.minecraft.lingering_potion.effect.invisibility": "滞留型隐身药水", "item.minecraft.lingering_potion.effect.leaping": "滞留型跳跃药水", "item.minecraft.lingering_potion.effect.levitation": "滞留型飘浮药水", "item.minecraft.lingering_potion.effect.luck": "滞留型幸运药水", "item.minecraft.lingering_potion.effect.mundane": "滞留型平凡的药水", "item.minecraft.lingering_potion.effect.night_vision": "滞留型夜视药水", "item.minecraft.lingering_potion.effect.oozing": "滞留型渗浆药水", "item.minecraft.lingering_potion.effect.poison": "滞留型剧毒药水", "item.minecraft.lingering_potion.effect.regeneration": "滞留型再生药水", "item.minecraft.lingering_potion.effect.slow_falling": "滞留型缓降药水", "item.minecraft.lingering_potion.effect.slowness": "滞留型迟缓药水", "item.minecraft.lingering_potion.effect.strength": "滞留型力量药水", "item.minecraft.lingering_potion.effect.swiftness": "滞留型迅捷药水", "item.minecraft.lingering_potion.effect.thick": "滞留型浓稠的药水", "item.minecraft.lingering_potion.effect.turtle_master": "滞留型神龟药水", "item.minecraft.lingering_potion.effect.water": "滞留型水瓶", "item.minecraft.lingering_potion.effect.water_breathing": "滞留型水肺药水", "item.minecraft.lingering_potion.effect.weakness": "滞留型虚弱药水", "item.minecraft.lingering_potion.effect.weaving": "滞留型盘丝药水", "item.minecraft.lingering_potion.effect.wind_charged": "滞留型蓄风药水", "item.minecraft.llama_spawn_egg": "羊驼刷怪蛋", "item.minecraft.lodestone_compass": "磁石指针", "item.minecraft.mace": "重锤", "item.minecraft.magenta_bundle": "品红色收纳袋", "item.minecraft.magenta_dye": "品红色染料", "item.minecraft.magenta_harness": "品红色挽具", "item.minecraft.magma_cream": "岩浆膏", "item.minecraft.magma_cube_spawn_egg": "岩浆怪刷怪蛋", "item.minecraft.mangrove_boat": "红树木船", "item.minecraft.mangrove_chest_boat": "红树木运输船", "item.minecraft.map": "空地图", "item.minecraft.melon_seeds": "西瓜种子", "item.minecraft.melon_slice": "西瓜片", "item.minecraft.milk_bucket": "奶桶", "item.minecraft.minecart": "矿车", "item.minecraft.miner_pottery_shard": "采矿纹样陶片", "item.minecraft.miner_pottery_sherd": "采矿纹样陶片", "item.minecraft.mojang_banner_pattern": "旗帜图案", "item.minecraft.mojang_banner_pattern.desc": "Mojang徽标", "item.minecraft.mojang_banner_pattern.new": "Mojang徽标旗帜图案", "item.minecraft.mooshroom_spawn_egg": "哞菇刷怪蛋", "item.minecraft.mourner_pottery_shard": "悲恸纹样陶片", "item.minecraft.mourner_pottery_sherd": "悲恸纹样陶片", "item.minecraft.mule_spawn_egg": "骡刷怪蛋", "item.minecraft.mushroom_stew": "蘑菇煲", "item.minecraft.music_disc_11": "音乐唱片", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "音乐唱片", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "音乐唱片", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "音乐唱片", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "音乐唱片", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "音乐唱片", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "音乐唱片", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "音乐唱片", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON>（八音盒）", "item.minecraft.music_disc_far": "音乐唱片", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "音乐唱片", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "音乐唱片", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "音乐唱片", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "音乐唱片", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "音乐唱片", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "音乐唱片", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "音乐唱片", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "音乐唱片", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "音乐唱片", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "音乐唱片", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "音乐唱片", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "音乐唱片", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "生羊肉", "item.minecraft.name_tag": "命名牌", "item.minecraft.nautilus_shell": "鹦鹉螺壳", "item.minecraft.nether_brick": "下界砖", "item.minecraft.nether_star": "下界之星", "item.minecraft.nether_wart": "下界疣", "item.minecraft.netherite_axe": "下界合金斧", "item.minecraft.netherite_boots": "下界合金靴子", "item.minecraft.netherite_chestplate": "下界合金胸甲", "item.minecraft.netherite_helmet": "下界合金头盔", "item.minecraft.netherite_hoe": "下界合金锄", "item.minecraft.netherite_ingot": "下界合金锭", "item.minecraft.netherite_leggings": "下界合金护腿", "item.minecraft.netherite_pickaxe": "下界合金镐", "item.minecraft.netherite_scrap": "下界合金碎片", "item.minecraft.netherite_shovel": "下界合金锹", "item.minecraft.netherite_sword": "下界合金剑", "item.minecraft.netherite_upgrade_smithing_template": "锻造模板", "item.minecraft.netherite_upgrade_smithing_template.new": "下界合金升级", "item.minecraft.oak_boat": "橡木船", "item.minecraft.oak_chest_boat": "橡木运输船", "item.minecraft.ocelot_spawn_egg": "豹猫刷怪蛋", "item.minecraft.ominous_bottle": "不祥之瓶", "item.minecraft.ominous_trial_key": "不祥试炼钥匙", "item.minecraft.orange_bundle": "橙色收纳袋", "item.minecraft.orange_dye": "橙色染料", "item.minecraft.orange_harness": "橙色挽具", "item.minecraft.painting": "画", "item.minecraft.pale_oak_boat": "苍白橡木船", "item.minecraft.pale_oak_chest_boat": "苍白橡木运输船", "item.minecraft.panda_spawn_egg": "熊猫刷怪蛋", "item.minecraft.paper": "纸", "item.minecraft.parrot_spawn_egg": "鹦鹉刷怪蛋", "item.minecraft.phantom_membrane": "幻翼膜", "item.minecraft.phantom_spawn_egg": "幻翼刷怪蛋", "item.minecraft.pig_spawn_egg": "猪刷怪蛋", "item.minecraft.piglin_banner_pattern": "旗帜图案", "item.minecraft.piglin_banner_pattern.desc": "猪鼻", "item.minecraft.piglin_banner_pattern.new": "猪鼻旗帜图案", "item.minecraft.piglin_brute_spawn_egg": "猪灵蛮兵刷怪蛋", "item.minecraft.piglin_spawn_egg": "猪灵刷怪蛋", "item.minecraft.pillager_spawn_egg": "掠夺者刷怪蛋", "item.minecraft.pink_bundle": "粉红色收纳袋", "item.minecraft.pink_dye": "粉红色染料", "item.minecraft.pink_harness": "粉红色挽具", "item.minecraft.pitcher_plant": "瓶子草", "item.minecraft.pitcher_pod": "瓶子草荚果", "item.minecraft.plenty_pottery_shard": "富饶纹样陶片", "item.minecraft.plenty_pottery_sherd": "富饶纹样陶片", "item.minecraft.poisonous_potato": "毒马铃薯", "item.minecraft.polar_bear_spawn_egg": "北极熊刷怪蛋", "item.minecraft.popped_chorus_fruit": "爆裂紫颂果", "item.minecraft.porkchop": "生猪排", "item.minecraft.potato": "马铃薯", "item.minecraft.potion": "药水", "item.minecraft.potion.effect.awkward": "粗制的药水", "item.minecraft.potion.effect.empty": "不可合成的药水", "item.minecraft.potion.effect.fire_resistance": "抗火药水", "item.minecraft.potion.effect.harming": "伤害药水", "item.minecraft.potion.effect.healing": "治疗药水", "item.minecraft.potion.effect.infested": "虫蚀药水", "item.minecraft.potion.effect.invisibility": "隐身药水", "item.minecraft.potion.effect.leaping": "跳跃药水", "item.minecraft.potion.effect.levitation": "飘浮药水", "item.minecraft.potion.effect.luck": "幸运药水", "item.minecraft.potion.effect.mundane": "平凡的药水", "item.minecraft.potion.effect.night_vision": "夜视药水", "item.minecraft.potion.effect.oozing": "渗浆药水", "item.minecraft.potion.effect.poison": "剧毒药水", "item.minecraft.potion.effect.regeneration": "再生药水", "item.minecraft.potion.effect.slow_falling": "缓降药水", "item.minecraft.potion.effect.slowness": "迟缓药水", "item.minecraft.potion.effect.strength": "力量药水", "item.minecraft.potion.effect.swiftness": "迅捷药水", "item.minecraft.potion.effect.thick": "浓稠的药水", "item.minecraft.potion.effect.turtle_master": "神龟药水", "item.minecraft.potion.effect.water": "水瓶", "item.minecraft.potion.effect.water_breathing": "水肺药水", "item.minecraft.potion.effect.weakness": "虚弱药水", "item.minecraft.potion.effect.weaving": "盘丝药水", "item.minecraft.potion.effect.wind_charged": "蓄风药水", "item.minecraft.pottery_shard_archer": "弓箭纹样陶片", "item.minecraft.pottery_shard_arms_up": "举臂纹样陶片", "item.minecraft.pottery_shard_prize": "珍宝纹样陶片", "item.minecraft.pottery_shard_skull": "头颅纹样陶片", "item.minecraft.powder_snow_bucket": "细雪桶", "item.minecraft.prismarine_crystals": "海晶砂粒", "item.minecraft.prismarine_shard": "海晶碎片", "item.minecraft.prize_pottery_shard": "珍宝纹样陶片", "item.minecraft.prize_pottery_sherd": "珍宝纹样陶片", "item.minecraft.pufferfish": "河豚", "item.minecraft.pufferfish_bucket": "河豚桶", "item.minecraft.pufferfish_spawn_egg": "河豚刷怪蛋", "item.minecraft.pumpkin_pie": "南瓜派", "item.minecraft.pumpkin_seeds": "南瓜种子", "item.minecraft.purple_bundle": "紫色收纳袋", "item.minecraft.purple_dye": "紫色染料", "item.minecraft.purple_harness": "紫色挽具", "item.minecraft.quartz": "下界石英", "item.minecraft.rabbit": "生兔肉", "item.minecraft.rabbit_foot": "兔子脚", "item.minecraft.rabbit_hide": "兔子皮", "item.minecraft.rabbit_spawn_egg": "兔子刷怪蛋", "item.minecraft.rabbit_stew": "兔肉煲", "item.minecraft.raiser_armor_trim_smithing_template": "锻造模板", "item.minecraft.raiser_armor_trim_smithing_template.new": "牧民盔甲纹饰", "item.minecraft.ravager_spawn_egg": "劫掠兽刷怪蛋", "item.minecraft.raw_copper": "粗铜", "item.minecraft.raw_gold": "粗金", "item.minecraft.raw_iron": "粗铁", "item.minecraft.recovery_compass": "追溯指针", "item.minecraft.red_bundle": "红色收纳袋", "item.minecraft.red_dye": "红色染料", "item.minecraft.red_harness": "红色挽具", "item.minecraft.redstone": "红石粉", "item.minecraft.resin_brick": "树脂砖", "item.minecraft.resin_clump": "树脂团", "item.minecraft.rib_armor_trim_smithing_template": "锻造模板", "item.minecraft.rib_armor_trim_smithing_template.new": "肋骨盔甲纹饰", "item.minecraft.rotten_flesh": "腐肉", "item.minecraft.saddle": "鞍", "item.minecraft.salmon": "生鲑鱼", "item.minecraft.salmon_bucket": "鲑鱼桶", "item.minecraft.salmon_spawn_egg": "鲑鱼刷怪蛋", "item.minecraft.scrape_pottery_sherd": "刮削纹样陶片", "item.minecraft.scute": "鳞甲", "item.minecraft.sentry_armor_trim_smithing_template": "锻造模板", "item.minecraft.sentry_armor_trim_smithing_template.new": "哨兵盔甲纹饰", "item.minecraft.shaper_armor_trim_smithing_template": "锻造模板", "item.minecraft.shaper_armor_trim_smithing_template.new": "塑造盔甲纹饰", "item.minecraft.sheaf_pottery_shard": "麦捆纹样陶片", "item.minecraft.sheaf_pottery_sherd": "麦捆纹样陶片", "item.minecraft.shears": "剪刀", "item.minecraft.sheep_spawn_egg": "绵羊刷怪蛋", "item.minecraft.shelter_pottery_shard": "树荫纹样陶片", "item.minecraft.shelter_pottery_sherd": "树荫纹样陶片", "item.minecraft.shield": "盾牌", "item.minecraft.shield.black": "黑色盾牌", "item.minecraft.shield.blue": "蓝色盾牌", "item.minecraft.shield.brown": "棕色盾牌", "item.minecraft.shield.cyan": "青色盾牌", "item.minecraft.shield.gray": "灰色盾牌", "item.minecraft.shield.green": "绿色盾牌", "item.minecraft.shield.light_blue": "淡蓝色盾牌", "item.minecraft.shield.light_gray": "淡灰色盾牌", "item.minecraft.shield.lime": "黄绿色盾牌", "item.minecraft.shield.magenta": "品红色盾牌", "item.minecraft.shield.orange": "橙色盾牌", "item.minecraft.shield.pink": "粉红色盾牌", "item.minecraft.shield.purple": "紫色盾牌", "item.minecraft.shield.red": "红色盾牌", "item.minecraft.shield.white": "白色盾牌", "item.minecraft.shield.yellow": "黄色盾牌", "item.minecraft.shulker_shell": "潜影壳", "item.minecraft.shulker_spawn_egg": "潜影贝刷怪蛋", "item.minecraft.sign": "告示牌", "item.minecraft.silence_armor_trim_smithing_template": "锻造模板", "item.minecraft.silence_armor_trim_smithing_template.new": "幽静盔甲纹饰", "item.minecraft.silverfish_spawn_egg": "蠹虫刷怪蛋", "item.minecraft.skeleton_horse_spawn_egg": "骷髅马刷怪蛋", "item.minecraft.skeleton_spawn_egg": "骷髅刷怪蛋", "item.minecraft.skull_banner_pattern": "旗帜图案", "item.minecraft.skull_banner_pattern.desc": "头颅盾徽", "item.minecraft.skull_banner_pattern.new": "头颅盾徽旗帜图案", "item.minecraft.skull_pottery_shard": "头颅纹样陶片", "item.minecraft.skull_pottery_sherd": "头颅纹样陶片", "item.minecraft.slime_ball": "黏液球", "item.minecraft.slime_spawn_egg": "史莱姆刷怪蛋", "item.minecraft.smithing_template": "锻造模板", "item.minecraft.smithing_template.applies_to": "可应用于：", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "放入铸锭或晶体", "item.minecraft.smithing_template.armor_trim.applies_to": "盔甲", "item.minecraft.smithing_template.armor_trim.base_slot_description": "放入盔甲", "item.minecraft.smithing_template.armor_trim.ingredients": "铸锭或晶体", "item.minecraft.smithing_template.ingredients": "所需原材料：", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "放入下界合金锭", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "钻石装备", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "放入钻石盔甲、武器或工具", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "下界合金锭", "item.minecraft.smithing_template.upgrade": "已有升级：", "item.minecraft.sniffer_spawn_egg": "嗅探兽刷怪蛋", "item.minecraft.snort_pottery_shard": "嗅探纹样陶片", "item.minecraft.snort_pottery_sherd": "嗅探纹样陶片", "item.minecraft.snout_armor_trim_smithing_template": "锻造模板", "item.minecraft.snout_armor_trim_smithing_template.new": "猪鼻盔甲纹饰", "item.minecraft.snow_golem_spawn_egg": "雪傀儡刷怪蛋", "item.minecraft.snowball": "雪球", "item.minecraft.spectral_arrow": "光灵箭", "item.minecraft.spider_eye": "蜘蛛眼", "item.minecraft.spider_spawn_egg": "蜘蛛刷怪蛋", "item.minecraft.spire_armor_trim_smithing_template": "锻造模板", "item.minecraft.spire_armor_trim_smithing_template.new": "尖塔盔甲纹饰", "item.minecraft.splash_potion": "喷溅药水", "item.minecraft.splash_potion.effect.awkward": "喷溅型粗制的药水", "item.minecraft.splash_potion.effect.empty": "不可合成的喷溅型药水", "item.minecraft.splash_potion.effect.fire_resistance": "喷溅型抗火药水", "item.minecraft.splash_potion.effect.harming": "喷溅型伤害药水", "item.minecraft.splash_potion.effect.healing": "喷溅型治疗药水", "item.minecraft.splash_potion.effect.infested": "喷溅型虫蚀药水", "item.minecraft.splash_potion.effect.invisibility": "喷溅型隐身药水", "item.minecraft.splash_potion.effect.leaping": "喷溅型跳跃药水", "item.minecraft.splash_potion.effect.levitation": "喷溅型飘浮药水", "item.minecraft.splash_potion.effect.luck": "喷溅型幸运药水", "item.minecraft.splash_potion.effect.mundane": "喷溅型平凡的药水", "item.minecraft.splash_potion.effect.night_vision": "喷溅型夜视药水", "item.minecraft.splash_potion.effect.oozing": "喷溅型渗浆药水", "item.minecraft.splash_potion.effect.poison": "喷溅型剧毒药水", "item.minecraft.splash_potion.effect.regeneration": "喷溅型再生药水", "item.minecraft.splash_potion.effect.slow_falling": "喷溅型缓降药水", "item.minecraft.splash_potion.effect.slowness": "喷溅型迟缓药水", "item.minecraft.splash_potion.effect.strength": "喷溅型力量药水", "item.minecraft.splash_potion.effect.swiftness": "喷溅型迅捷药水", "item.minecraft.splash_potion.effect.thick": "喷溅型浓稠的药水", "item.minecraft.splash_potion.effect.turtle_master": "喷溅型神龟药水", "item.minecraft.splash_potion.effect.water": "喷溅型水瓶", "item.minecraft.splash_potion.effect.water_breathing": "喷溅型水肺药水", "item.minecraft.splash_potion.effect.weakness": "喷溅型虚弱药水", "item.minecraft.splash_potion.effect.weaving": "喷溅型盘丝药水", "item.minecraft.splash_potion.effect.wind_charged": "喷溅型蓄风药水", "item.minecraft.spruce_boat": "云杉木船", "item.minecraft.spruce_chest_boat": "云杉木运输船", "item.minecraft.spyglass": "望远镜", "item.minecraft.squid_spawn_egg": "鱿鱼刷怪蛋", "item.minecraft.stick": "木棍", "item.minecraft.stone_axe": "石斧", "item.minecraft.stone_hoe": "石锄", "item.minecraft.stone_pickaxe": "石镐", "item.minecraft.stone_shovel": "石锹", "item.minecraft.stone_sword": "石剑", "item.minecraft.stray_spawn_egg": "流浪者刷怪蛋", "item.minecraft.strider_spawn_egg": "炽足兽刷怪蛋", "item.minecraft.string": "线", "item.minecraft.sugar": "糖", "item.minecraft.suspicious_stew": "谜之炖菜", "item.minecraft.sweet_berries": "甜浆果", "item.minecraft.tadpole_bucket": "蝌蚪桶", "item.minecraft.tadpole_spawn_egg": "蝌蚪刷怪蛋", "item.minecraft.tide_armor_trim_smithing_template": "锻造模板", "item.minecraft.tide_armor_trim_smithing_template.new": "潮汐盔甲纹饰", "item.minecraft.tipped_arrow": "药箭", "item.minecraft.tipped_arrow.effect.awkward": "药箭", "item.minecraft.tipped_arrow.effect.empty": "不可合成的药箭", "item.minecraft.tipped_arrow.effect.fire_resistance": "抗火之箭", "item.minecraft.tipped_arrow.effect.harming": "伤害之箭", "item.minecraft.tipped_arrow.effect.healing": "治疗之箭", "item.minecraft.tipped_arrow.effect.infested": "虫蚀之箭", "item.minecraft.tipped_arrow.effect.invisibility": "隐身之箭", "item.minecraft.tipped_arrow.effect.leaping": "跳跃之箭", "item.minecraft.tipped_arrow.effect.levitation": "飘浮之箭", "item.minecraft.tipped_arrow.effect.luck": "幸运之箭", "item.minecraft.tipped_arrow.effect.mundane": "药箭", "item.minecraft.tipped_arrow.effect.night_vision": "夜视之箭", "item.minecraft.tipped_arrow.effect.oozing": "渗浆之箭", "item.minecraft.tipped_arrow.effect.poison": "剧毒之箭", "item.minecraft.tipped_arrow.effect.regeneration": "再生之箭", "item.minecraft.tipped_arrow.effect.slow_falling": "缓降之箭", "item.minecraft.tipped_arrow.effect.slowness": "迟缓之箭", "item.minecraft.tipped_arrow.effect.strength": "力量之箭", "item.minecraft.tipped_arrow.effect.swiftness": "迅捷之箭", "item.minecraft.tipped_arrow.effect.thick": "药箭", "item.minecraft.tipped_arrow.effect.turtle_master": "神龟之箭", "item.minecraft.tipped_arrow.effect.water": "喷溅之箭", "item.minecraft.tipped_arrow.effect.water_breathing": "水肺之箭", "item.minecraft.tipped_arrow.effect.weakness": "虚弱之箭", "item.minecraft.tipped_arrow.effect.weaving": "盘丝之箭", "item.minecraft.tipped_arrow.effect.wind_charged": "蓄风之箭", "item.minecraft.tnt_minecart": "TNT矿车", "item.minecraft.torchflower_seeds": "火把花种子", "item.minecraft.totem_of_undying": "不死图腾", "item.minecraft.trader_llama_spawn_egg": "行商羊驼刷怪蛋", "item.minecraft.trial_key": "试炼钥匙", "item.minecraft.trident": "三叉戟", "item.minecraft.tropical_fish": "热带鱼", "item.minecraft.tropical_fish_bucket": "热带鱼桶", "item.minecraft.tropical_fish_spawn_egg": "热带鱼刷怪蛋", "item.minecraft.turtle_helmet": "海龟壳", "item.minecraft.turtle_scute": "海龟鳞甲", "item.minecraft.turtle_spawn_egg": "海龟刷怪蛋", "item.minecraft.vex_armor_trim_smithing_template": "锻造模板", "item.minecraft.vex_armor_trim_smithing_template.new": "恼鬼盔甲纹饰", "item.minecraft.vex_spawn_egg": "恼鬼刷怪蛋", "item.minecraft.villager_spawn_egg": "村民刷怪蛋", "item.minecraft.vindicator_spawn_egg": "卫道士刷怪蛋", "item.minecraft.wandering_trader_spawn_egg": "流浪商人刷怪蛋", "item.minecraft.ward_armor_trim_smithing_template": "锻造模板", "item.minecraft.ward_armor_trim_smithing_template.new": "监守盔甲纹饰", "item.minecraft.warden_spawn_egg": "监守者刷怪蛋", "item.minecraft.warped_fungus_on_a_stick": "诡异菌钓竿", "item.minecraft.water_bucket": "水桶", "item.minecraft.wayfinder_armor_trim_smithing_template": "锻造模板", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "向导盔甲纹饰", "item.minecraft.wheat": "小麦", "item.minecraft.wheat_seeds": "小麦种子", "item.minecraft.white_bundle": "白色收纳袋", "item.minecraft.white_dye": "白色染料", "item.minecraft.white_harness": "白色挽具", "item.minecraft.wild_armor_trim_smithing_template": "锻造模板", "item.minecraft.wild_armor_trim_smithing_template.new": "荒野盔甲纹饰", "item.minecraft.wind_charge": "风弹", "item.minecraft.witch_spawn_egg": "女巫刷怪蛋", "item.minecraft.wither_skeleton_spawn_egg": "凋灵骷髅刷怪蛋", "item.minecraft.wither_spawn_egg": "凋灵刷怪蛋", "item.minecraft.wolf_armor": "狼铠", "item.minecraft.wolf_spawn_egg": "狼刷怪蛋", "item.minecraft.wooden_axe": "木斧", "item.minecraft.wooden_hoe": "木锄", "item.minecraft.wooden_pickaxe": "木镐", "item.minecraft.wooden_shovel": "木锹", "item.minecraft.wooden_sword": "木剑", "item.minecraft.writable_book": "书与笔", "item.minecraft.written_book": "成书", "item.minecraft.yellow_bundle": "黄色收纳袋", "item.minecraft.yellow_dye": "黄色染料", "item.minecraft.yellow_harness": "黄色挽具", "item.minecraft.zoglin_spawn_egg": "僵尸疣猪兽刷怪蛋", "item.minecraft.zombie_horse_spawn_egg": "僵尸马刷怪蛋", "item.minecraft.zombie_spawn_egg": "僵尸刷怪蛋", "item.minecraft.zombie_villager_spawn_egg": "僵尸村民刷怪蛋", "item.minecraft.zombified_piglin_spawn_egg": "僵尸猪灵刷怪蛋", "item.modifiers.any": "装备时：", "item.modifiers.armor": "穿戴时：", "item.modifiers.body": "装备时：", "item.modifiers.chest": "穿在身上时：", "item.modifiers.feet": "穿在脚上时：", "item.modifiers.hand": "手持时：", "item.modifiers.head": "戴在头上时：", "item.modifiers.legs": "穿在腿上时：", "item.modifiers.mainhand": "在主手时：", "item.modifiers.offhand": "在副手时：", "item.modifiers.saddle": "装备鞍时：", "item.nbt_tags": "NBT：%s个标签", "item.op_block_warning.line1": "警告：", "item.op_block_warning.line2": "使用该物品可能会导致命令执行", "item.op_block_warning.line3": "除非知道确切内容，否则请勿使用！", "item.unbreakable": "无法破坏", "itemGroup.buildingBlocks": "建筑方块", "itemGroup.coloredBlocks": "染色方块", "itemGroup.combat": "战斗用品", "itemGroup.consumables": "消耗品", "itemGroup.crafting": "合成用品", "itemGroup.foodAndDrink": "食物与饮品", "itemGroup.functional": "功能方块", "itemGroup.hotbar": "已保存的快捷栏", "itemGroup.ingredients": "原材料", "itemGroup.inventory": "生存模式物品栏", "itemGroup.natural": "自然方块", "itemGroup.op": "管理员用品", "itemGroup.redstone": "红石方块", "itemGroup.search": "搜索物品", "itemGroup.spawnEggs": "刷怪蛋", "itemGroup.tools": "工具与实用物品", "item_modifier.unknown": "未知的物品修饰器：%s", "jigsaw_block.final_state": "转变为：", "jigsaw_block.generate": "生成", "jigsaw_block.joint.aligned": "固定", "jigsaw_block.joint.rollable": "可旋转", "jigsaw_block.joint_label": "拼接类型：", "jigsaw_block.keep_jigsaws": "保留拼图", "jigsaw_block.levels": "层数：%s", "jigsaw_block.name": "名称：", "jigsaw_block.placement_priority": "放置优先级：", "jigsaw_block.placement_priority.tooltip": "当此拼图方块连接到部件时，此处即为在更大的结构中各部件处理连接的顺序。\n\n部件按照优先级由高到低处理，若优先级相同，则按照插入顺序处理。", "jigsaw_block.pool": "目标池：", "jigsaw_block.selection_priority": "选择优先级：", "jigsaw_block.selection_priority.tooltip": "当父级片段处理连接时，此处即为各拼图方块尝试连接目标部件的顺序。\n\n拼图方块将会按照优先级由高到低处理，同级则随机处理。", "jigsaw_block.target": "目标名称：", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON>（八音盒）", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "进度", "key.attack": "攻击/摧毁", "key.back": "向后移动", "key.categories.creative": "创造模式", "key.categories.gameplay": "游戏内容", "key.categories.inventory": "物品栏", "key.categories.misc": "杂项", "key.categories.movement": "移动", "key.categories.multiplayer": "多人游戏", "key.categories.ui": "游戏界面", "key.chat": "打开聊天栏", "key.command": "输入命令", "key.drop": "丢弃所选物品", "key.forward": "向前移动", "key.fullscreen": "切换全屏显示", "key.hotbar.1": "快捷栏1", "key.hotbar.2": "快捷栏2", "key.hotbar.3": "快捷栏3", "key.hotbar.4": "快捷栏4", "key.hotbar.5": "快捷栏5", "key.hotbar.6": "快捷栏6", "key.hotbar.7": "快捷栏7", "key.hotbar.8": "快捷栏8", "key.hotbar.9": "快捷栏9", "key.inventory": "开启/关闭物品栏", "key.jump": "跳跃", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "下方向键", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "小键盘 0", "key.keyboard.keypad.1": "小键盘 1", "key.keyboard.keypad.2": "小键盘 2", "key.keyboard.keypad.3": "小键盘 3", "key.keyboard.keypad.4": "小键盘 4", "key.keyboard.keypad.5": "小键盘 5", "key.keyboard.keypad.6": "小键盘 6", "key.keyboard.keypad.7": "小键盘 7", "key.keyboard.keypad.8": "小键盘 8", "key.keyboard.keypad.9": "小键盘 9", "key.keyboard.keypad.add": "小键盘 +", "key.keyboard.keypad.decimal": "小键盘 .", "key.keyboard.keypad.divide": "小键盘 /", "key.keyboard.keypad.enter": "小键盘 Enter", "key.keyboard.keypad.equal": "小键盘 =", "key.keyboard.keypad.multiply": "小键盘 *", "key.keyboard.keypad.subtract": "小键盘 -", "key.keyboard.left": "左方向键", "key.keyboard.left.alt": "左Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "左Ctrl", "key.keyboard.left.shift": "左Shift", "key.keyboard.left.win": "左Win", "key.keyboard.menu": "菜单", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "右方向键", "key.keyboard.right.alt": "右Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "右Ctrl", "key.keyboard.right.shift": "右Shift", "key.keyboard.right.win": "右Win", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "空格", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "未指定", "key.keyboard.up": "上方向键", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "向左移动", "key.loadToolbarActivator": "加载快捷栏", "key.mouse": "鼠标按键%1$s", "key.mouse.left": "左键", "key.mouse.middle": "中键", "key.mouse.right": "右键", "key.pickItem": "选取方块", "key.playerlist": "显示玩家列表", "key.quickActions": "快捷操作", "key.right": "向右移动", "key.saveToolbarActivator": "保存快捷栏", "key.screenshot": "截图", "key.smoothCamera": "切换电影视角", "key.sneak": "潜行", "key.socialInteractions": "社交屏幕", "key.spectatorOutlines": "高亮玩家（旁观者）", "key.sprint": "疾跑", "key.swapOffhand": "与副手交换物品", "key.togglePerspective": "切换视角", "key.use": "使用物品/放置方块", "known_server_link.announcements": "公告", "known_server_link.community": "社区", "known_server_link.community_guidelines": "社区方针", "known_server_link.feedback": "反馈", "known_server_link.forums": "论坛", "known_server_link.news": "新闻", "known_server_link.report_bug": "报告服务器漏洞", "known_server_link.status": "状态", "known_server_link.support": "支持", "known_server_link.website": "网站", "lanServer.otherPlayers": "对其他玩家的设置", "lanServer.port": "端口号", "lanServer.port.invalid": "端口无效。\n请将编辑框留空，或输入一个介于1024和65535之间的数字。", "lanServer.port.invalid.new": "端口无效。\n请将编辑框留空，或输入一个介于%s和%s之间的数字。", "lanServer.port.unavailable": "端口不可用。\n请将编辑框留空，或输入一个介于1024和65535之间的新数字。", "lanServer.port.unavailable.new": "端口不可用。\n请将编辑框留空，或输入一个介于%s和%s之间的新数字。", "lanServer.scanning": "正在你的本地网络中寻找游戏", "lanServer.start": "创建局域网世界", "lanServer.title": "局域网世界", "language.code": "zho-Hans_CN", "language.name": "简体中文", "language.region": "中国大陆", "lectern.take_book": "取书", "loading.progress": "%s%%", "mco.account.privacy.info": "阅读更多关于Mojang和其他隐私政策的信息", "mco.account.privacy.info.button": "阅读关于《通用数据保护条例》的内容", "mco.account.privacy.information": "Mojang实施许多方法来帮助保护儿童及其隐私权，包括履行《儿童在线隐私权保护法案》（COPPA）与《通用数据保护条例》（GDPR）。\n\n在取得Realms账户访问权之前，你可能需要获得父母的同意。", "mco.account.privacyinfo": "Mojang实施许多方法来促进儿童及其隐私权的保护，包括履行《儿童在线隐私权保护法案》（COPPA）与《通用数据保护条例》（GDPR）。\n\n在访问你的Realms账户之前，你可能需要征得父母的同意。\n\n如果你拥有的是旧版的Minecraft账户（登录时使用用户名），你需要将其合并到Mojang账户以访问Realms。", "mco.account.update": "更新账户", "mco.activity.noactivity": "过去%s天无动态", "mco.activity.title": "玩家动态", "mco.backup.button.download": "下载最新版本", "mco.backup.button.reset": "重置世界", "mco.backup.button.restore": "还原", "mco.backup.button.upload": "上传世界", "mco.backup.changes.tooltip": "更改", "mco.backup.entry": "备份（%s）", "mco.backup.entry.description": "描述", "mco.backup.entry.enabledPack": "已启用的资源包", "mco.backup.entry.gameDifficulty": "游戏难度", "mco.backup.entry.gameMode": "游戏模式", "mco.backup.entry.gameServerVersion": "游戏服务器版本", "mco.backup.entry.name": "名称", "mco.backup.entry.seed": "种子", "mco.backup.entry.templateName": "模板名称", "mco.backup.entry.undefined": "未定义的更改", "mco.backup.entry.uploaded": "已上传", "mco.backup.entry.worldType": "世界类型", "mco.backup.generate.world": "生成世界", "mco.backup.info.title": "自上次备份以来发生的更改", "mco.backup.narration": "备份自%s", "mco.backup.nobackups": "这个Realm目前没有任何备份。", "mco.backup.restoring": "正在还原你的Realm", "mco.backup.unknown": "未知", "mco.brokenworld.download": "下载", "mco.brokenworld.downloaded": "已下载", "mco.brokenworld.message.line1": "请重置或选择另一个世界。", "mco.brokenworld.message.line2": "你可以选择将世界下载至单人模式中。", "mco.brokenworld.minigame.title": "此小游戏已不受支持", "mco.brokenworld.nonowner.error": "请等待Realm所有者重置世界", "mco.brokenworld.nonowner.title": "世界已过期", "mco.brokenworld.play": "开始游戏", "mco.brokenworld.reset": "重置", "mco.brokenworld.title": "当前的世界已不受支持", "mco.client.incompatible.msg.line1": "你的客户端与Realms不兼容。", "mco.client.incompatible.msg.line2": "请使用最新版本的Minecraft。", "mco.client.incompatible.msg.line3": "Realms不与快照版本兼容。", "mco.client.incompatible.title": "客户端不兼容！", "mco.client.outdated.stable.version": "你的客户端版本（%s）与Realms不兼容。\n\n请使用最新版本的Minecraft。", "mco.client.unsupported.snapshot.version": "你的客户端版本（%s）与Realms不兼容。\n\nRealms在此快照版本不可用。", "mco.compatibility.downgrade": "降级", "mco.compatibility.downgrade.description": "这个世界上次是在%s版本中打开的，你正在使用%s版本。降低世界的游戏版本可能会导致存档损坏，我们无法保证它可以正确加载和运行。\n\n此世界的备份将会保存在“世界备份”下。如有需要，请恢复原来的世界。", "mco.compatibility.incompatible.popup.title": "版本不兼容", "mco.compatibility.incompatible.releaseType.popup.message": "尝试加入的世界与所在版本不兼容。", "mco.compatibility.incompatible.series.popup.message": "这个世界上次是在%s版本中打开的，你正在使用%s版本。\n\n这些系列互不兼容。此版本需要创建新的世界。", "mco.compatibility.unverifiable.message": "无法验证上次打开这个世界的版本。如果升级或降级这个世界，备份将会被自动创建并保存在“世界备份”下。", "mco.compatibility.unverifiable.title": "无法验证兼容性", "mco.compatibility.upgrade": "升级", "mco.compatibility.upgrade.description": "这个世界上次是在%s版本中打开的，你正在使用%s版本。\n\n此世界的备份将会保存在“世界备份”下。如有需要，请恢复原来的世界。", "mco.compatibility.upgrade.friend.description": "这个世界上次是在%s版本中打开的，你正在使用%s版本。\n\n此世界的备份将会保存在“世界备份”下。\n\n如有需要，Realm的所有者可以恢复原来的世界。", "mco.compatibility.upgrade.title": "你真的想升级此世界吗？", "mco.configure.current.minigame": "当前", "mco.configure.world.activityfeed.disabled": "玩家推送暂不可用", "mco.configure.world.backup": "世界备份", "mco.configure.world.buttons.activity": "玩家动态", "mco.configure.world.buttons.close": "暂时关闭Realm", "mco.configure.world.buttons.delete": "删除", "mco.configure.world.buttons.done": "完成", "mco.configure.world.buttons.edit": "设置", "mco.configure.world.buttons.invite": "邀请玩家", "mco.configure.world.buttons.moreoptions": "更多选项", "mco.configure.world.buttons.newworld": "新的世界", "mco.configure.world.buttons.open": "重新打开Realm", "mco.configure.world.buttons.options": "世界选项", "mco.configure.world.buttons.players": "玩家", "mco.configure.world.buttons.region_preference": "选择地区…", "mco.configure.world.buttons.resetworld": "重置世界", "mco.configure.world.buttons.save": "保存", "mco.configure.world.buttons.settings": "设置", "mco.configure.world.buttons.subscription": "订阅", "mco.configure.world.buttons.switchminigame": "切换小游戏", "mco.configure.world.close.question.line1": "你可以暂时关闭Realm，从而防止有人在你调整设置时进入游戏。准备就绪后Realm可以重新打开。\n\n此操作不会取消你的Realms订阅。", "mco.configure.world.close.question.line2": "你确定要继续吗？", "mco.configure.world.close.question.title": "要在不中断的情况下更改吗？", "mco.configure.world.closing": "暂时关闭Realm中…", "mco.configure.world.commandBlocks": "命令方块", "mco.configure.world.delete.button": "删除Realm", "mco.configure.world.delete.question.line1": "你的Realm将被永久删除", "mco.configure.world.delete.question.line2": "你确定要继续吗？", "mco.configure.world.description": "Realm描述", "mco.configure.world.edit.slot.name": "世界名称", "mco.configure.world.edit.subscreen.adventuremap": "由于你当前的世界是冒险世界，有些选项将会被禁用", "mco.configure.world.edit.subscreen.experience": "由于你当前的世界是体验世界，有些选项将会被禁用", "mco.configure.world.edit.subscreen.inspiration": "由于你当前的世界是示例世界，有些选项将会被禁用", "mco.configure.world.forceGameMode": "强制游戏模式", "mco.configure.world.invite.narration": "你有%s个新邀请", "mco.configure.world.invite.profile.name": "名称", "mco.configure.world.invited": "已邀请", "mco.configure.world.invited.number": "已邀请（%s）", "mco.configure.world.invites.normal.tooltip": "普通玩家", "mco.configure.world.invites.ops.tooltip": "管理员", "mco.configure.world.invites.remove.tooltip": "移除", "mco.configure.world.leave.question.line1": "如果你离开这个Realm，你只有被再次邀请才能看见它", "mco.configure.world.leave.question.line2": "你确定要继续吗？", "mco.configure.world.loading": "加载Realm中", "mco.configure.world.location": "位置", "mco.configure.world.minigame": "当前：%s", "mco.configure.world.name": "Realm名称", "mco.configure.world.opening": "开启Realm中…", "mco.configure.world.players.error": "不存在该指定名称的玩家", "mco.configure.world.players.inviting": "邀请玩家中…", "mco.configure.world.players.title": "玩家", "mco.configure.world.pvp": "玩家对战", "mco.configure.world.region_preference": "地区偏好", "mco.configure.world.region_preference.title": "地区偏好选择", "mco.configure.world.reset.question.line1": "你的Realm将被重新生成，而当前的Realm将会消失", "mco.configure.world.reset.question.line2": "你确定要继续吗？", "mco.configure.world.resourcepack.question": "这个Realm需要一个自定义的资源包\n\n你想要自动下载并安装它吗？", "mco.configure.world.resourcepack.question.line1": "这个Realm需要一个自定义的资源包", "mco.configure.world.resourcepack.question.line2": "你想要自动下载并安装它吗？", "mco.configure.world.restore.download.question.line1": "世界将会被下载并添加至你的单人游戏世界中", "mco.configure.world.restore.download.question.line2": "你确定要继续吗？", "mco.configure.world.restore.question.line1": "你的Realm世界将被还原至“%s”（%s）", "mco.configure.world.restore.question.line2": "你确定要继续吗？", "mco.configure.world.settings.expired": "你无法编辑已过期Realm的设置", "mco.configure.world.settings.title": "设置", "mco.configure.world.slot": "世界%s", "mco.configure.world.slot.empty": "空", "mco.configure.world.slot.switch.question.line1": "你的Realm将会被切换至另一个世界", "mco.configure.world.slot.switch.question.line2": "你确定要继续吗？", "mco.configure.world.slot.tooltip": "切换到世界", "mco.configure.world.slot.tooltip.active": "加入", "mco.configure.world.slot.tooltip.minigame": "切换至小游戏", "mco.configure.world.spawnAnimals": "生成动物", "mco.configure.world.spawnMonsters": "生成怪物", "mco.configure.world.spawnNPCs": "生成NPC", "mco.configure.world.spawnProtection": "重生点保护", "mco.configure.world.spawn_toggle.message": "关闭此选项后，所有现有的该类型实体将会被移除", "mco.configure.world.spawn_toggle.message.npc": "关闭此选项后，所有现有的该类型实体将会被移除，例如村民", "mco.configure.world.spawn_toggle.title": "警告！", "mco.configure.world.status": "状态", "mco.configure.world.subscription.day": "天", "mco.configure.world.subscription.days": "天", "mco.configure.world.subscription.expired": "已过期", "mco.configure.world.subscription.extend": "延长订阅时间", "mco.configure.world.subscription.less_than_a_day": "不足一天", "mco.configure.world.subscription.month": "月", "mco.configure.world.subscription.months": "月", "mco.configure.world.subscription.recurring.daysleft": "自动续期剩余时间：", "mco.configure.world.subscription.recurring.info": "对你的Realms订阅所做的更改（如续期和取消自动续订）将在下个结算日生效。", "mco.configure.world.subscription.remaining.days": "%1$s天", "mco.configure.world.subscription.remaining.months": "%1$s个月", "mco.configure.world.subscription.remaining.months.days": "%1$s个月%2$s天", "mco.configure.world.subscription.start": "开始日期", "mco.configure.world.subscription.tab": "订阅", "mco.configure.world.subscription.timeleft": "剩余时间", "mco.configure.world.subscription.title": "订阅信息", "mco.configure.world.subscription.unknown": "未知", "mco.configure.world.switch.slot": "创建世界", "mco.configure.world.switch.slot.subtitle": "这个世界是空的，请选择创建世界的方式", "mco.configure.world.title": "配置Realm：", "mco.configure.world.uninvite.player": "你确定要取消邀请“%s”吗？", "mco.configure.world.uninvite.question": "你确定要取消邀请吗", "mco.configure.worlds.title": "世界", "mco.connect.authorizing": "登录中…", "mco.connect.connecting": "正在连接至Realm…", "mco.connect.failed": "无法连接至Realm", "mco.connect.region": "服务器地区：%s", "mco.connect.success": "完成", "mco.create.world": "创建", "mco.create.world.error": "你必须输入一个名字！", "mco.create.world.failed": "创建世界失败！", "mco.create.world.reset.title": "正在创建世界…", "mco.create.world.skip": "跳过", "mco.create.world.subtitle": "可选，选择你想放进新Realm的世界", "mco.create.world.wait": "正在创建Realm…", "mco.download.cancelled": "下载已取消", "mco.download.confirmation.line1": "你将下载的世界大小大于%s", "mco.download.confirmation.line2": "你无法再次将这个世界上传至Realm", "mco.download.confirmation.oversized": "你将下载的世界大小大于%s\n\n你无法再次将这个世界上传至Realm", "mco.download.done": "下载完成", "mco.download.downloading": "正在下载", "mco.download.extracting": "提取中", "mco.download.failed": "下载失败", "mco.download.percent": "%s %%", "mco.download.preparing": "正在准备下载", "mco.download.resourcePack.fail": "资源包下载失败！", "mco.download.speed": "（%s/s）", "mco.download.speed.narration": "%s每秒", "mco.download.title": "下载最新的世界", "mco.error.invalid.session.message": "请尝试重启Minecraft", "mco.error.invalid.session.title": "无效会话", "mco.errorMessage.6001": "客户端已过期！", "mco.errorMessage.6002": "尚未同意服务条款", "mco.errorMessage.6003": "超过下载限制", "mco.errorMessage.6004": "超过上传限制", "mco.errorMessage.6005": "世界已锁定", "mco.errorMessage.6006": "世界已过期", "mco.errorMessage.6007": "玩家所在的Realm数量过多", "mco.errorMessage.6008": "无效的Realm名称", "mco.errorMessage.6009": "无效的Realm描述", "mco.errorMessage.connectionFailure": "出错了，请稍后再试。", "mco.errorMessage.generic": "出现错误：", "mco.errorMessage.initialize.failed": "初始化Realm失败", "mco.errorMessage.noDetails": "未提供错误详情", "mco.errorMessage.realmsService": "出现错误（%s）：", "mco.errorMessage.realmsService.configurationError": "编辑世界选项时出现意外错误", "mco.errorMessage.realmsService.connectivity": "无法连接至Realm：%s", "mco.errorMessage.realmsService.realmsError": "Realms（%s）：", "mco.errorMessage.realmsService.unknownCompatibility": "无法检查兼容版本，响应为：%s", "mco.errorMessage.retry": "重试操作", "mco.errorMessage.serviceBusy": "Realms目前处于繁忙状态。\n请在几分钟后再次尝试连接至你的Realm。", "mco.gui.button": "按钮", "mco.gui.ok": "确定", "mco.info": "信息！", "mco.invited.player.narration": "已邀请玩家%s", "mco.invites.button.accept": "接受", "mco.invites.button.reject": "拒绝", "mco.invites.nopending": "没有未决邀请！", "mco.invites.pending": "新邀请！", "mco.invites.title": "未决邀请", "mco.minigame.world.changeButton": "选择另一个小游戏", "mco.minigame.world.info.line1": "这会暂时将你的世界替换成一个小游戏！", "mco.minigame.world.info.line2": "之后你可以毫无损失地返回到原来的世界。", "mco.minigame.world.noSelection": "请选择", "mco.minigame.world.restore": "结束小游戏中…", "mco.minigame.world.restore.question.line1": "小游戏将结束而你的Realm将被恢复。", "mco.minigame.world.restore.question.line2": "你确定要继续吗？", "mco.minigame.world.selected": "所选的小游戏：", "mco.minigame.world.slot.screen.title": "切换世界中…", "mco.minigame.world.startButton": "切换", "mco.minigame.world.starting.screen.title": "开始小游戏中…", "mco.minigame.world.stopButton": "结束小游戏", "mco.minigame.world.switch.new": "选择另一个小游戏？", "mco.minigame.world.switch.title": "切换小游戏", "mco.minigame.world.title": "将Realm切换成小游戏", "mco.news": "Realms新闻", "mco.notification.dismiss": "关闭", "mco.notification.transferSubscription.buttonText": "立即转移", "mco.notification.transferSubscription.message": "Java版Realms订阅即将转移到Microsoft Store。别让你的订阅过期了！\n现在转移即可免费获得30天的Realms服务。\n请在minecraft.net的“档案”页面转移你的订阅。", "mco.notification.visitUrl.buttonText.default": "打开链接", "mco.notification.visitUrl.message.default": "请访问下方的链接", "mco.onlinePlayers": "在线玩家", "mco.play.button.realm.closed": "Realm已关闭", "mco.question": "问题", "mco.reset.world.adventure": "冒险", "mco.reset.world.experience": "体验", "mco.reset.world.generate": "新的世界", "mco.reset.world.inspiration": "示例", "mco.reset.world.resetting.screen.title": "重置世界中…", "mco.reset.world.seed": "世界种子（可选）", "mco.reset.world.template": "世界模板", "mco.reset.world.title": "重置世界", "mco.reset.world.upload": "上传世界", "mco.reset.world.warning": "这将替换掉你目前的Realm世界", "mco.selectServer.buy": "购买Realm服务器！", "mco.selectServer.close": "关闭", "mco.selectServer.closed": "已失效的Realm", "mco.selectServer.closeserver": "关闭Realm", "mco.selectServer.configure": "配置Realm", "mco.selectServer.configureRealm": "配置Realm", "mco.selectServer.create": "创建Realm", "mco.selectServer.create.subtitle": "选择想放进你新Realm的世界", "mco.selectServer.expired": "已过期的Realm", "mco.selectServer.expiredList": "你的订阅已过期", "mco.selectServer.expiredRenew": "续期", "mco.selectServer.expiredSubscribe": "订阅", "mco.selectServer.expiredTrial": "你的试用期已结束", "mco.selectServer.expires.day": "将在一天内过期", "mco.selectServer.expires.days": "将在%s天后过期", "mco.selectServer.expires.soon": "即将过期", "mco.selectServer.leave": "离开Realm", "mco.selectServer.loading": "加载Realm列表中", "mco.selectServer.mapOnlySupportedForVersion": "此地图不受%s支持", "mco.selectServer.minigame": "小游戏：", "mco.selectServer.minigameName": "小游戏：%s", "mco.selectServer.minigameNotSupportedInVersion": "无法在%s中进行这个小游戏", "mco.selectServer.noRealms": "你似乎还没有Realm。添加Realm与朋友们一同畅玩。", "mco.selectServer.note": "注意：", "mco.selectServer.open": "已开启的Realm", "mco.selectServer.openserver": "开启Realm", "mco.selectServer.play": "开始游戏", "mco.selectServer.popup": "Realms最多可以同时支持十位好友安全且简单地在线享受Minecraft世界。它也支持多种小游戏与海量自定义世界！只有Realm的所有者需要付款。", "mco.selectServer.purchase": "添加Realm", "mco.selectServer.trial": "开始试用！", "mco.selectServer.uninitialized": "点击以创建新的Realm！", "mco.snapshot.createSnapshotPopup.text": "你即将创建免费的快照版Realm，它会与你的付费Realm订阅绑定。新的快照版Realm在付费订阅生效期间可以随时访问。原本的付费Realm不会受到影响。", "mco.snapshot.createSnapshotPopup.title": "是否创建快照版Realm？", "mco.snapshot.creating": "正在创建快照版Realm…", "mco.snapshot.description": "与%s绑定", "mco.snapshot.friendsRealm.downgrade": "你需要使用版本%s加入此Realm", "mco.snapshot.friendsRealm.upgrade": "在你使用此快照进行游戏之前，%s需要升级其Realm", "mco.snapshot.paired": "此快照版Realm与%s绑定", "mco.snapshot.parent.tooltip": "使用最新正式版Minecraft以进入此Realm", "mco.snapshot.start": "开启免费的快照版Realm", "mco.snapshot.subscription.info": "这是与你的Realm订阅“%s”绑定的快照版Realm。它会在与其绑定的Realm可用时保持可用。", "mco.snapshot.tooltip": "使用快照版Realm抢先了解即将推出的Minecraft版本，其中可能包括新功能和其他更改。\n\n你可以在游戏的正式版中找到普通的Realm。", "mco.snapshotRealmsPopup.message": "Realms从23w41a开始可在快照版本中使用。每份Realms订阅中包含一份和你普通Java版Realm相互独立的免费快照版Realm！", "mco.snapshotRealmsPopup.title": "Realms现可在快照版本中使用", "mco.snapshotRealmsPopup.urlText": "了解更多", "mco.template.button.publisher": "发布者", "mco.template.button.select": "选择", "mco.template.button.trailer": "预告片", "mco.template.default.name": "世界模板", "mco.template.info.tooltip": "发布者的网站", "mco.template.name": "模板", "mco.template.select.failure": "我们无法接收这个分类的内容。\n请检查你的网络连接，或稍后再试。", "mco.template.select.narrate.authors": "作者：%s", "mco.template.select.narrate.version": "版本：%s", "mco.template.select.none": "抱歉，看来这个分类目前是空的。\n请之后再回来检查是否有新内容，如果你是一名创作者的话，\n%s。", "mco.template.select.none.linkTitle": "尝试自己提交一些自己的内容", "mco.template.title": "世界模板", "mco.template.title.minigame": "Realm小游戏", "mco.template.trailer.tooltip": "地图预告", "mco.terms.buttons.agree": "同意", "mco.terms.buttons.disagree": "不同意", "mco.terms.sentence.1": "我已阅读并接受Minecraft Realms的", "mco.terms.sentence.2": "服务条款", "mco.terms.title": "Realms服务条款", "mco.time.daysAgo": "%1$s天前", "mco.time.hoursAgo": "%1$s小时前", "mco.time.minutesAgo": "%1$s分钟前", "mco.time.now": "刚刚", "mco.time.secondsAgo": "%1$s秒前", "mco.trial.message.line1": "你想获得属于自己的Realm吗？", "mco.trial.message.line2": "点击此处来获得更多消息！", "mco.upload.button.name": "上传", "mco.upload.cancelled": "上传已取消", "mco.upload.close.failure": "无法关闭你的Realm，请稍后再试", "mco.upload.done": "上传已完成", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s（%2$s）", "mco.upload.failed": "上传失败！（%s）", "mco.upload.failed.too_big.description": "选中的世界过大。最大允许大小为%s。", "mco.upload.failed.too_big.title": "世界过大", "mco.upload.hardcore": "不能上传极限模式世界！", "mco.upload.percent": "%s %%", "mco.upload.preparing": "正在准备你的世界数据", "mco.upload.select.world.none": "找不到单人游戏世界！", "mco.upload.select.world.subtitle": "请选择一个单人游戏世界来上传", "mco.upload.select.world.title": "上传世界", "mco.upload.size.failure.line1": "“%s”太大了！", "mco.upload.size.failure.line2": "你想上传%s的地图，超过了%s的限制。", "mco.upload.uploading": "正在上传“%s”", "mco.upload.verifying": "正在验证你的世界", "mco.version": "版本：%s", "mco.warning": "警告！", "mco.worldSlot.minigame": "小游戏", "menu.custom_options": "自定义选项…", "menu.custom_options.title": "自定义选项", "menu.custom_options.tooltip": "注：自定义选项由第三方服务器或内容提供。\n请小心操作！", "menu.custom_screen_info.button_narration": "这是一个自定义屏幕。了解更多信息。", "menu.custom_screen_info.contents": "此屏幕的内容由第三方服务器或地图控制，不由Mojang Studios或Microsoft拥有、运营和监督。\n\n请小心操作！点击链接时请务必谨慎，确保不要泄露你的个人信息，包括账号密码等。\n\n如果此屏幕阻止你继续，可以点击下方按钮与当前服务器断开连接。", "menu.custom_screen_info.disconnect": "已拒绝自定义屏幕", "menu.custom_screen_info.title": "关于自定义屏幕的说明", "menu.custom_screen_info.tooltip": "这是一个自定义屏幕。点击此处了解更多信息。", "menu.disconnect": "断开连接", "menu.feedback": "反馈…", "menu.feedback.title": "反馈", "menu.game": "游戏菜单", "menu.modded": "（已修改）", "menu.multiplayer": "多人游戏", "menu.online": "Minecraft Realms", "menu.options": "选项…", "menu.paused": "游戏暂停", "menu.playdemo": "开始试玩世界", "menu.playerReporting": "举报玩家", "menu.preparingSpawn": "准备生成区域中：%s%%", "menu.quick_actions": "快捷操作…", "menu.quick_actions.title": "快捷操作", "menu.quit": "退出游戏", "menu.reportBugs": "报告漏洞", "menu.resetdemo": "重置试玩世界", "menu.returnToGame": "回到游戏", "menu.returnToMenu": "保存并退回到标题屏幕", "menu.savingChunks": "保存区块中", "menu.savingLevel": "保存世界中", "menu.sendFeedback": "提供反馈", "menu.server_links": "服务器链接…", "menu.server_links.title": "服务器链接", "menu.shareToLan": "对局域网开放", "menu.singleplayer": "单人游戏", "menu.working": "处理中…", "merchant.deprecated": "村民每天最多补货两次。", "merchant.level.1": "新手", "merchant.level.2": "学徒", "merchant.level.3": "老手", "merchant.level.4": "专家", "merchant.level.5": "大师", "merchant.title": "%s - %s", "merchant.trades": "交易", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "按下%1$s来脱离", "multiplayer.applyingPack": "正在应用资源包", "multiplayer.confirm_command.parse_errors": "你尝试执行的命令无法识别或无效。\n确定要执行吗？\n命令：%s", "multiplayer.confirm_command.permissions_required": "你尝试执行的命令需要较高的权限。\n这可能会对游戏产生负面影响。\n确定要执行吗？\n命令：%s", "multiplayer.confirm_command.title": "确认执行命令", "multiplayer.disconnect.authservers_down": "身份验证服务器目前处于宕机状态。请稍后再试，抱歉！", "multiplayer.disconnect.bad_chat_index": "检测到来自服务器的聊天消息部分缺失或已被重新排序", "multiplayer.disconnect.banned": "你已被此服务器封禁", "multiplayer.disconnect.banned.expiration": "\n你的封禁将于%s解除", "multiplayer.disconnect.banned.reason": "你已被此服务器封禁。\n原因：%s", "multiplayer.disconnect.banned_ip.expiration": "\n你的封禁将于%s解除", "multiplayer.disconnect.banned_ip.reason": "你的IP已被此服务器封禁。\n原因：%s", "multiplayer.disconnect.chat_validation_failed": "聊天消息验证失败", "multiplayer.disconnect.duplicate_login": "你已在异地登录", "multiplayer.disconnect.expired_public_key": "个人信息公钥过期。请确保系统时间同步，然后尝试重启游戏。", "multiplayer.disconnect.flying": "此服务器未启用飞行", "multiplayer.disconnect.generic": "连接中断", "multiplayer.disconnect.idling": "你的未操作时间过长！", "multiplayer.disconnect.illegal_characters": "非法聊天字符", "multiplayer.disconnect.incompatible": "客户端不兼容！请使用%s", "multiplayer.disconnect.invalid_entity_attacked": "试图攻击无效实体", "multiplayer.disconnect.invalid_packet": "服务器发送了一个无效的数据包", "multiplayer.disconnect.invalid_player_data": "无效的玩家数据", "multiplayer.disconnect.invalid_player_movement": "收到了包含非法玩家移动的数据包", "multiplayer.disconnect.invalid_public_key_signature": "无效的个人信息公钥签名。\n请尝试重启游戏。", "multiplayer.disconnect.invalid_public_key_signature.new": "无效的个人信息公钥签名。\n请尝试重启游戏。", "multiplayer.disconnect.invalid_vehicle_movement": "收到了包含非法载具移动的数据包", "multiplayer.disconnect.ip_banned": "你的IP地址已被此服务器封禁", "multiplayer.disconnect.kicked": "被管理员踢出游戏", "multiplayer.disconnect.missing_tags": "从服务器接收到不完整标签集。\n请联系服务器管理员。", "multiplayer.disconnect.name_taken": "此名称已被占用", "multiplayer.disconnect.not_whitelisted": "你不在此服务器的白名单中！", "multiplayer.disconnect.out_of_order_chat": "接收到了乱序的聊天数据包。你的系统时间是否被更改过？", "multiplayer.disconnect.outdated_client": "客户端不兼容！请使用%s", "multiplayer.disconnect.outdated_server": "客户端不兼容！请使用%s", "multiplayer.disconnect.server_full": "服务器已满！", "multiplayer.disconnect.server_shutdown": "服务器已关闭", "multiplayer.disconnect.slow_login": "登录超时", "multiplayer.disconnect.too_many_pending_chats": "没有确认的聊天消息过多", "multiplayer.disconnect.transfers_disabled": "服务器不接受转移", "multiplayer.disconnect.unexpected_query_response": "从客户端收到未知的自定义数据", "multiplayer.disconnect.unsigned_chat": "接收到了缺少签名或含有无效签名的聊天数据包。", "multiplayer.disconnect.unverified_username": "验证用户名失败！", "multiplayer.downloadingStats": "正在获取统计信息…", "multiplayer.downloadingTerrain": "加载地形中…", "multiplayer.lan.server_found": "发现新的服务器：%s", "multiplayer.message_not_delivered": "无法发送聊天消息，请检查服务器日志：%s", "multiplayer.player.joined": "%s加入了游戏", "multiplayer.player.joined.renamed": "%s（之前被称为%s）加入了游戏", "multiplayer.player.left": "%s退出了游戏", "multiplayer.player.list.hp": "%s生命值", "multiplayer.player.list.narration": "在线玩家：%s", "multiplayer.requiredTexturePrompt.disconnect": "服务器需要自定义资源包", "multiplayer.requiredTexturePrompt.line1": "这个服务器需要使用自定义的资源包。", "multiplayer.requiredTexturePrompt.line2": "拒绝使用该自定义资源包将会断开你与该服务器的连接。", "multiplayer.socialInteractions.not_available": "社交屏幕仅在多人游戏世界中可用", "multiplayer.status.and_more": "…及其他%s名玩家…", "multiplayer.status.cancelled": "已取消", "multiplayer.status.cannot_connect": "无法连接到服务器", "multiplayer.status.cannot_resolve": "无法解析主机名", "multiplayer.status.finished": "已完成", "multiplayer.status.incompatible": "版本不兼容！", "multiplayer.status.motd.narration": "服务器信息：%s", "multiplayer.status.no_connection": "（无连接）", "multiplayer.status.old": "旧版", "multiplayer.status.online": "在线", "multiplayer.status.ping": "%s毫秒", "multiplayer.status.ping.narration": "延迟为%s毫秒", "multiplayer.status.pinging": "检测中…", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s名玩家在线，共%s名玩家", "multiplayer.status.quitting": "正在退出", "multiplayer.status.request_handled": "状态请求已处理", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "收到了未请求的状态", "multiplayer.status.version.narration": "服务器版本：%s", "multiplayer.stopSleeping": "起床", "multiplayer.texturePrompt.failure.line1": "无法应用服务器资源包", "multiplayer.texturePrompt.failure.line2": "所有依赖自定义资源包的功能都有可能不按预期工作", "multiplayer.texturePrompt.line1": "这个服务器推荐使用自定义的资源包。", "multiplayer.texturePrompt.line2": "你想要自动下载和安装它吗？", "multiplayer.texturePrompt.serverPrompt": "%s\n\n来自服务器的消息：\n%s", "multiplayer.title": "多人游戏", "multiplayer.unsecureserver.toast": "该服务器上发送的消息可能会被修改，可能无法反映原始消息", "multiplayer.unsecureserver.toast.title": "无法验证聊天消息", "multiplayerWarning.check": "不再显示此屏幕", "multiplayerWarning.header": "警告：在线游戏由第三方提供", "multiplayerWarning.message": "警告：在线游戏由第三方服务器提供，不由Mojang Studios或Microsoft拥有、运营和监督。 在线游戏的过程中，你可能会收到不受规制的聊天消息或者其他用户生成内容，而这些内容可能不适合所有人。", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "谷冈久美 - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "谷冈久美 - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "谷冈久美 - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "谷冈久美 - komorebi", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "谷冈久美 - poko<PERSON>ko", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "谷冈久美 - yakusoku", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "按钮：%s", "narration.button.usage.focused": "按下Enter键激活", "narration.button.usage.hovered": "单击鼠标左键激活", "narration.checkbox": "复选框：%s", "narration.checkbox.usage.focused": "按下Enter键切换", "narration.checkbox.usage.hovered": "单击鼠标左键切换", "narration.component_list.usage": "按下Tab键移动到下一个屏幕控件", "narration.cycle_button.usage.focused": "按下Enter键切换到%s", "narration.cycle_button.usage.hovered": "单击鼠标左键切换到%s", "narration.edit_box": "编辑框：%s", "narration.item": "物品：%s", "narration.recipe": "%s的配方", "narration.recipe.usage": "单击鼠标左键选中", "narration.recipe.usage.more": "单击鼠标右键显示更多配方", "narration.selection.usage": "使用上下方向键移动到另一项", "narration.slider.usage.focused": "使用左右方向键更改数值", "narration.slider.usage.hovered": "拖动滑块更改数值", "narration.suggestion": "已选中%2$s项建议中的第%1$s项：%3$s", "narration.suggestion.tooltip": "已选中%2$s项建议中的第%1$s项：%3$s（%4$s）", "narration.suggestion.usage.cycle.fixed": "按下Tab键切换到下一个建议", "narration.suggestion.usage.cycle.hidable": "按下Tab键切换到下一个建议，或按下Escape键忽略建议", "narration.suggestion.usage.fill.fixed": "按下Tab键使用建议", "narration.suggestion.usage.fill.hidable": "按下Tab键使用建议，或按下Escape键忽略建议", "narration.tab_navigation.usage": "按下Ctrl键和Tab键切换标签页", "narrator.button.accessibility": "辅助功能", "narrator.button.difficulty_lock": "难度锁定", "narrator.button.difficulty_lock.locked": "已锁定", "narrator.button.difficulty_lock.unlocked": "未锁定", "narrator.button.language": "语言", "narrator.controls.bound": "%s已绑定至%s", "narrator.controls.reset": "重置%s按钮", "narrator.controls.unbound": "%s未绑定", "narrator.joining": "加入中", "narrator.loading": "加载中：%s", "narrator.loading.done": "完成", "narrator.position.list": "已选中列表的第%s行，共%s行", "narrator.position.object_list": "已选中一列控件中的第%s项，共%s项", "narrator.position.screen": "第%s个屏幕控件，共%s个", "narrator.position.tab": "已选中第%s个标签页，共%s个", "narrator.ready_to_play": "准备就绪", "narrator.screen.title": "标题屏幕", "narrator.screen.usage": "使用鼠标指针或Tab键选择屏幕控件", "narrator.select": "已选择：%s", "narrator.select.world": "已选择%s，上次进入游戏：%s，%s，%s，版本：%s", "narrator.select.world_info": "已选择%s，上次进入游戏：%s，%s", "narrator.toast.disabled": "复述功能已关闭", "narrator.toast.enabled": "复述功能已开启", "optimizeWorld.confirm.description": "这将尝试用最新的游戏格式储存所有数据来达到优化世界的效果。取决于世界的状况，这可能会花费不少时间。一旦完成，进行游戏时可能会更流畅，但你的世界将不再与旧版游戏兼容。你确定要继续吗？", "optimizeWorld.confirm.proceed": "创建备份并优化", "optimizeWorld.confirm.title": "优化世界", "optimizeWorld.info.converted": "已更新的区块数：%s", "optimizeWorld.info.skipped": "已忽略的区块数：%s", "optimizeWorld.info.total": "总区块数：%s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "统计区块中…", "optimizeWorld.stage.failed": "失败了！:(", "optimizeWorld.stage.finished": "即将完成…", "optimizeWorld.stage.finished.chunks": "即将完成区块的升级…", "optimizeWorld.stage.finished.entities": "即将完成实体的升级…", "optimizeWorld.stage.finished.poi": "即将完成兴趣点的升级…", "optimizeWorld.stage.upgrading": "升级所有区块中…", "optimizeWorld.stage.upgrading.chunks": "升级所有区块中…", "optimizeWorld.stage.upgrading.entities": "升级所有实体中…", "optimizeWorld.stage.upgrading.poi": "升级所有兴趣点中…", "optimizeWorld.title": "正在优化世界“%s”", "options.accessibility": "辅助功能设置…", "options.accessibility.high_contrast": "高对比度", "options.accessibility.high_contrast.error.tooltip": "高对比度资源包不可用。", "options.accessibility.high_contrast.tooltip": "提高UI控件的对比度。", "options.accessibility.high_contrast_block_outline": "高对比度方块轮廓", "options.accessibility.high_contrast_block_outline.tooltip": "提高选中方块的轮廓对比度。", "options.accessibility.link": "辅助功能指南", "options.accessibility.menu_background_blurriness": "菜单背景模糊程度", "options.accessibility.menu_background_blurriness.tooltip": "更改菜单背景的模糊程度。", "options.accessibility.narrator_hotkey": "复述功能快捷键", "options.accessibility.narrator_hotkey.mac.tooltip": "允许使用“Cmd+B”开关复述功能。", "options.accessibility.narrator_hotkey.tooltip": "允许使用“Ctrl+B”开关复述功能。", "options.accessibility.panorama_speed": "全景图滚动速度", "options.accessibility.text_background": "文本背景", "options.accessibility.text_background.chat": "聊天", "options.accessibility.text_background.everywhere": "全局", "options.accessibility.text_background_opacity": "文本背景不透明度", "options.accessibility.title": "辅助功能设置", "options.allowServerListing": "允许列入服务器玩家列表", "options.allowServerListing.tooltip": "服务器可能会公开列出目前在线的玩家。\n若关闭此选项，你的名字将不会显示在此列表中。", "options.ao": "平滑光照", "options.ao.max": "最大", "options.ao.min": "最小", "options.ao.off": "关", "options.attack.crosshair": "十字准星", "options.attack.hotbar": "快捷栏", "options.attackIndicator": "攻击指示器", "options.audioDevice": "设备", "options.audioDevice.default": "系统默认", "options.autoJump": "自动跳跃", "options.autoSuggestCommands": "命令提示", "options.autosaveIndicator": "自动保存指示器", "options.biomeBlendRadius": "生物群系过渡距离", "options.biomeBlendRadius.1": "关（最快）", "options.biomeBlendRadius.11": "11×11（极高）", "options.biomeBlendRadius.13": "13×13（显著）", "options.biomeBlendRadius.15": "15×15（最高）", "options.biomeBlendRadius.3": "3×3（快）", "options.biomeBlendRadius.5": "5×5（普通）", "options.biomeBlendRadius.7": "7×7（高）", "options.biomeBlendRadius.9": "9×9（很高）", "options.chat": "聊天设置…", "options.chat.color": "颜色", "options.chat.delay": "聊天延迟：%s秒", "options.chat.delay_none": "聊天延迟：无", "options.chat.height.focused": "聚焦高度", "options.chat.height.unfocused": "淡化高度", "options.chat.line_spacing": "行距", "options.chat.links": "网页链接", "options.chat.links.prompt": "链接提示", "options.chat.opacity": "聊天文本不透明度", "options.chat.scale": "聊天文本大小", "options.chat.title": "聊天设置", "options.chat.visibility": "聊天", "options.chat.visibility.full": "显示", "options.chat.visibility.hidden": "隐藏", "options.chat.visibility.system": "仅限命令", "options.chat.width": "宽度", "options.chunks": "%s个区块", "options.clouds.fancy": "高品质", "options.clouds.fast": "流畅", "options.controls": "按键控制…", "options.credits_and_attribution": "鸣谢与著作权说明…", "options.damageTiltStrength": "受伤抖动效果", "options.damageTiltStrength.tooltip": "受到伤害导致的游戏视角晃动程度。", "options.darkMojangStudiosBackgroundColor": "黑白徽标", "options.darkMojangStudiosBackgroundColor.tooltip": "将Mojang Studios加载屏幕的背景颜色更改为黑色。", "options.darknessEffectScale": "黑暗脉动效果", "options.darknessEffectScale.tooltip": "控制监守者或幽匿尖啸体给予的黑暗脉动效果程度。", "options.difficulty": "难度", "options.difficulty.easy": "简单", "options.difficulty.easy.info": "敌对生物会生成，但伤害较低。生命值在饥饿值耗尽后最终会降至5颗心。", "options.difficulty.hard": "困难", "options.difficulty.hard.info": "敌对生物会生成，且伤害较高。饥饿值耗尽后最终会饿死。", "options.difficulty.hardcore": "极限", "options.difficulty.normal": "普通", "options.difficulty.normal.info": "敌对生物会生成，且伤害适中。生命值在饥饿值耗尽后最终会降至半颗心。", "options.difficulty.online": "服务器难度", "options.difficulty.peaceful": "和平", "options.difficulty.peaceful.info": "不会生成绝大多数敌对生物。饥饿值不会消耗，且生命值会自然恢复。", "options.directionalAudio": "定向音频", "options.directionalAudio.off.tooltip": "经典立体声。", "options.directionalAudio.on.tooltip": "通过基于HRTF算法的定向环绕音频来改善立体音效的模拟。需使用与HRTF兼容的音频硬件，推荐在使用时佩戴耳机。", "options.discrete_mouse_scroll": "离散滚动", "options.entityDistanceScaling": "实体渲染距离", "options.entityShadows": "实体阴影", "options.font": "字体设置…", "options.font.title": "字体设置", "options.forceUnicodeFont": "强制使用Unicode字体", "options.fov": "视场角", "options.fov.max": "广角", "options.fov.min": "中", "options.fovEffectScale": "视场角效果", "options.fovEffectScale.tooltip": "控制游戏机制效果改变视场角的程度。", "options.framerate": "%s fps", "options.framerateLimit": "最大帧率", "options.framerateLimit.max": "无限制", "options.fullscreen": "全屏", "options.fullscreen.current": "当前分辨率", "options.fullscreen.entry": "%s×%s@%sHz（%s位色深）", "options.fullscreen.resolution": "全屏分辨率", "options.fullscreen.unavailable": "设置不可用", "options.gamma": "亮度", "options.gamma.default": "默认", "options.gamma.max": "明亮", "options.gamma.min": "昏暗", "options.generic_value": "%s：%s", "options.glintSpeed": "附魔光效闪烁速度", "options.glintSpeed.tooltip": "控制附魔物品光效的闪动速度。", "options.glintStrength": "附魔光效闪烁强度", "options.glintStrength.tooltip": "控制附魔物品光效的透明程度。", "options.graphics": "图像品质", "options.graphics.fabulous": "极佳！", "options.graphics.fabulous.tooltip": "%s画质使用屏幕着色器绘制天气、云以及半透明方块和水后面的粒子。\n这也许会在便携设备和4K显示屏上造成严重的性能负担。", "options.graphics.fancy": "高品质", "options.graphics.fancy.tooltip": "高品质画质会为大多数设备平衡性能和质量。\n天气、云和粒子可能不会在半透明方块或水的后面显示。", "options.graphics.fast": "流畅", "options.graphics.fast.tooltip": "流畅画质将减少雨雪的可见数量。\n树叶等方块的透明效果将被禁用。", "options.graphics.warning.accept": "在不受支持的情况下继续", "options.graphics.warning.cancel": "算了", "options.graphics.warning.message": "检测到你的图形设备不支持%s画质选项。\n\n你可以忽略此提示并继续，但如果你依然选择使用%s画质，你的设备将不会受到支持。", "options.graphics.warning.renderer": "检测到渲染器：[%s]", "options.graphics.warning.title": "图形设备不受支持", "options.graphics.warning.vendor": "检测到厂商：[%s]", "options.graphics.warning.version": "检测到OpenGL版本：[%s]", "options.guiScale": "界面尺寸", "options.guiScale.auto": "自动", "options.hidden": "隐藏", "options.hideLightningFlashes": "隐藏闪电的闪烁效果", "options.hideLightningFlashes.tooltip": "关闭闪电造成的天空闪烁效果。闪电本身仍将可见。", "options.hideMatchedNames": "隐藏匹配的名称", "options.hideMatchedNames.tooltip": "第三方服务器也许会发送非标准格式的聊天消息。\n此设置打开后，被隐藏的玩家将会被根据发言者的名字匹配。", "options.hideSplashTexts": "隐藏闪烁标语", "options.hideSplashTexts.tooltip": "隐藏主菜单上的黄色闪烁标语。", "options.inactivityFpsLimit": "降低帧率", "options.inactivityFpsLimit.afk": "无操作时", "options.inactivityFpsLimit.afk.tooltip": "若超过1分钟无操作，帧率将被限制至30。9分钟后进一步降至10。", "options.inactivityFpsLimit.minimized": "最小化时", "options.inactivityFpsLimit.minimized.tooltip": "仅在游戏窗口最小化时限制帧率。", "options.invertMouse": "鼠标反转", "options.japaneseGlyphVariants": "日本字形变体", "options.japaneseGlyphVariants.tooltip": "在默认字体中为CJK字符使用日本字形。", "options.key.hold": "按住", "options.key.toggle": "切换", "options.language": "语言…", "options.language.title": "语言", "options.languageAccuracyWarning": "（语言翻译不一定100%%准确）", "options.languageWarning": "语言翻译不一定100%%准确", "options.mainHand": "主手", "options.mainHand.left": "左手", "options.mainHand.right": "右手", "options.mipmapLevels": "Mipmap级别", "options.modelPart.cape": "披风", "options.modelPart.hat": "帽子", "options.modelPart.jacket": "外套", "options.modelPart.left_pants_leg": "左裤腿", "options.modelPart.left_sleeve": "左袖", "options.modelPart.right_pants_leg": "右裤腿", "options.modelPart.right_sleeve": "右袖", "options.mouseWheelSensitivity": "滚轮灵敏度", "options.mouse_settings": "鼠标设置…", "options.mouse_settings.title": "鼠标设置", "options.multiplayer.title": "多人游戏设置…", "options.multiplier": "%s×", "options.music_frequency": "音乐播放频率", "options.music_frequency.constant": "持续", "options.music_frequency.default": "默认", "options.music_frequency.frequent": "频繁", "options.music_frequency.tooltip": "调整游戏世界中音乐播放的频率。", "options.narrator": "复述功能", "options.narrator.all": "复述所有", "options.narrator.chat": "复述聊天消息", "options.narrator.notavailable": "不可用", "options.narrator.off": "关", "options.narrator.system": "复述系统消息", "options.notifications.display_time": "通知显示时长", "options.notifications.display_time.tooltip": "影响所有通知在屏幕上停留的时长。", "options.off": "关", "options.off.composed": "%s：关", "options.on": "开", "options.on.composed": "%s：开", "options.online": "在线选项…", "options.online.title": "在线选项", "options.onlyShowSecureChat": "仅显示安全的聊天", "options.onlyShowSecureChat.tooltip": "仅显示来自其他玩家的可验证为该玩家发送且未被修改的消息。", "options.operatorItemsTab": "管理员用品标签页", "options.particles": "粒子效果", "options.particles.all": "全部", "options.particles.decreased": "少量", "options.particles.minimal": "最少", "options.percent_add_value": "%s：+%s%%", "options.percent_value": "%s：%s%%", "options.pixel_value": "%s：%spx", "options.prioritizeChunkUpdates": "区块构建器", "options.prioritizeChunkUpdates.byPlayer": "半阻塞", "options.prioritizeChunkUpdates.byPlayer.tooltip": "区块内部的某些行为会导致区块立刻重新编译。这包括放置或破坏方块。", "options.prioritizeChunkUpdates.nearby": "全阻塞", "options.prioritizeChunkUpdates.nearby.tooltip": "附近的区块总会被立刻编译。这可能影响放置或破坏方块时的游戏性能。", "options.prioritizeChunkUpdates.none": "线程化", "options.prioritizeChunkUpdates.none.tooltip": "附近的区块会在并行的线程中编译。这可能导致破坏方块时短暂出现图像空洞。", "options.rawMouseInput": "原始输入", "options.realmsNotifications": "Realms新闻与邀请", "options.realmsNotifications.tooltip": "在标题屏幕上获取Realms新闻和邀请，并在Realms按钮上显示相应图标。", "options.reducedDebugInfo": "简化调试信息", "options.renderClouds": "云", "options.renderCloudsDistance": "云层距离", "options.renderDistance": "渲染距离", "options.resourcepack": "资源包…", "options.rotateWithMinecart": "同步矿车转动", "options.rotateWithMinecart.tooltip": "是否在矿车转动时同步转动玩家视角。仅在开启“矿车改进”实验性设置的世界中可用。", "options.screenEffectScale": "屏幕扭曲效果", "options.screenEffectScale.tooltip": "反胃状态效果和下界传送门所使用的屏幕扭曲效果的强度。\n值较低时，反胃的扭曲效果会被一层绿色的视觉效果替代。", "options.sensitivity": "鼠标灵敏度", "options.sensitivity.max": "超高速！！！", "options.sensitivity.min": "*哈欠*", "options.showNowPlayingToast": "显示音乐弹窗", "options.showNowPlayingToast.tooltip": "当有歌曲开始播放时显示弹窗。歌曲正在播放时，会在游戏暂停菜单中持续显示同一弹窗。", "options.showSubtitles": "显示字幕", "options.simulationDistance": "模拟距离", "options.skinCustomisation": "自定义皮肤…", "options.skinCustomisation.title": "自定义皮肤", "options.sounds": "音乐和声音…", "options.sounds.title": "音乐和声音选项", "options.telemetry": "遥测数据…", "options.telemetry.button": "数据收集", "options.telemetry.button.tooltip": "“%s”仅包含必要数据。\n“%s”包含可选数据和必要数据。", "options.telemetry.disabled": "遥测已禁用。", "options.telemetry.state.all": "全部", "options.telemetry.state.minimal": "最少", "options.telemetry.state.none": "无", "options.title": "选项", "options.touchscreen": "触屏模式", "options.video": "视频设置…", "options.videoTitle": "视频设置", "options.viewBobbing": "视角摇晃", "options.visible": "显示", "options.vsync": "垂直同步", "outOfMemory.message": "Minecraft已耗尽内存。\n\n原因可能是游戏存在漏洞，或分配给Java虚拟机的内存过少。\n\n为避免世界损坏，当前游戏已退出。我们已尝试腾出内存，让你可以回到主菜单并继续游戏，但这不一定奏效。\n\n如果你再次看见此消息，请重启游戏。", "outOfMemory.title": "内存溢出！", "pack.available.title": "可用", "pack.copyFailure": "复制包失败", "pack.dropConfirm": "你确定要将这些包添加进Minecraft中吗？", "pack.dropInfo": "将文件拖放到这个窗口内来添加包", "pack.dropRejected.message": "以下项目不是有效的包，未能执行复制操作：\n %s", "pack.dropRejected.title": "所添加的内容不是包", "pack.folderInfo": "（请将包放在这里）", "pack.incompatible": "不兼容", "pack.incompatible.confirm.new": "这个包是为更新的Minecraft版本所打造的，在此版本可能不会正常工作。", "pack.incompatible.confirm.old": "这个包是为更旧的Minecraft版本所打造的，在此版本可能不会正常工作。", "pack.incompatible.confirm.title": "你确定要加载此包吗？", "pack.incompatible.new": "（适用于新版本的Minecraft）", "pack.incompatible.old": "（适用于旧版本的Minecraft）", "pack.nameAndSource": "%s（%s）", "pack.openFolder": "打开包文件夹", "pack.selected.title": "已选", "pack.source.builtin": "内置", "pack.source.feature": "功能", "pack.source.local": "本地", "pack.source.server": "服务器", "pack.source.world": "世界", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "随机样式", "parsing.bool.expected": "应为布尔型", "parsing.bool.invalid": "无效的布尔型数据，应为“true”或“false”却出现了“%s”", "parsing.double.expected": "应为双精度浮点型", "parsing.double.invalid": "无效的双精度浮点型数据“%s”", "parsing.expected": "缺少“%s”", "parsing.float.expected": "应为浮点型", "parsing.float.invalid": "无效的浮点型数据“%s”", "parsing.int.expected": "应为整型", "parsing.int.invalid": "无效的整型数据“%s”", "parsing.long.expected": "应为长整型", "parsing.long.invalid": "无效的长整型数据“%s”", "parsing.quote.escape": "引号内的字符串包含无效的转义序列“\\%s”", "parsing.quote.expected.end": "字符串引号不成对", "parsing.quote.expected.start": "字符串开端应有引号", "particle.invalidOptions": "无法解析粒子选项：%s", "particle.notFound": "未知的粒子：%s", "permissions.requires.entity": "需要一个实体来执行此命令", "permissions.requires.player": "需要一名玩家来执行此命令", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "当生效后：", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s（%s）", "predicate.unknown": "未知的谓词：%s", "quickplay.error.invalid_identifier": "无法找到具有指定标识符的世界", "quickplay.error.realm_connect": "无法连接至Realm", "quickplay.error.realm_permission": "权限不足，无法连接至此Realm", "quickplay.error.title": "无法快速进入游戏", "realms.configuration.region.australia_east": "澳大利亚新南威尔士州", "realms.configuration.region.australia_southeast": "澳大利亚维多利亚州", "realms.configuration.region.brazil_south": "巴西", "realms.configuration.region.central_india": "印度", "realms.configuration.region.central_us": "美国艾奥瓦州", "realms.configuration.region.east_asia": "香港", "realms.configuration.region.east_us": "美国弗吉尼亚州", "realms.configuration.region.east_us_2": "美国北卡罗来纳州", "realms.configuration.region.france_central": "法国", "realms.configuration.region.japan_east": "日本东部", "realms.configuration.region.japan_west": "日本西部", "realms.configuration.region.korea_central": "韩国", "realms.configuration.region.north_central_us": "美国伊利诺伊州", "realms.configuration.region.north_europe": "爱尔兰", "realms.configuration.region.south_central_us": "美国得克萨斯州", "realms.configuration.region.southeast_asia": "新加坡", "realms.configuration.region.sweden_central": "瑞典", "realms.configuration.region.uae_north": "阿联酋", "realms.configuration.region.uk_south": "英国南部", "realms.configuration.region.west_central_us": "美国犹他州", "realms.configuration.region.west_europe": "荷兰", "realms.configuration.region.west_us": "美国加利福尼亚州", "realms.configuration.region.west_us_2": "美国华盛顿州", "realms.configuration.region_preference.automatic_owner": "自动（取决于Realm所有者的延迟）", "realms.configuration.region_preference.automatic_player": "自动（取决于首个加入会话的玩家）", "realms.missing.snapshot.error.text": "快照当前不支持Realms", "recipe.notFound": "未知的配方：%s", "recipe.toast.description": "请检查你的配方书", "recipe.toast.title": "新配方已解锁！", "record.nowPlaying": "正在播放：%s", "recover_world.bug_tracker": "报告漏洞", "recover_world.button": "尝试恢复", "recover_world.done.failed": "无法从之前的状态恢复。", "recover_world.done.success": "恢复成功！", "recover_world.done.title": "恢复已完成", "recover_world.issue.missing_file": "缺失文件", "recover_world.issue.none": "没有问题", "recover_world.message": "尝试读取世界文件夹“%s”时出现以下问题。\n可以尝试从先前的状态恢复此世界，也可以在漏洞追踪器上报告此问题。", "recover_world.no_fallback": "没有可以恢复的状态", "recover_world.restore": "尝试恢复", "recover_world.restoring": "尝试恢复世界中…", "recover_world.state_entry": "%s的状态：", "recover_world.state_entry.unknown": "未知", "recover_world.title": "加载世界失败", "recover_world.warning": "加载世界摘要失败", "resourcePack.broken_assets": "检测到损坏的资产", "resourcePack.high_contrast.name": "高对比度", "resourcePack.load_fail": "重载资源失败", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "检测到资源包错误", "resourcePack.server.name": "世界指定资源包", "resourcePack.title": "选择资源包", "resourcePack.vanilla.description": "Minecraft的默认观感", "resourcePack.vanilla.name": "默认", "resourcepack.downloading": "正在下载资源包", "resourcepack.progress": "下载文件中（%s MB）…", "resourcepack.requesting": "正在发送请求…", "screenshot.failure": "无法保存截图：%s", "screenshot.success": "已将截图保存为%s", "selectServer.add": "添加服务器", "selectServer.defaultName": "Minecraft服务器", "selectServer.delete": "删除", "selectServer.deleteButton": "删除", "selectServer.deleteQuestion": "你确定要删除此服务器吗？", "selectServer.deleteWarning": "“%s”将会永久消失！（真的很久！）", "selectServer.direct": "直接连接", "selectServer.edit": "编辑", "selectServer.hiddenAddress": "（隐藏）", "selectServer.refresh": "刷新", "selectServer.select": "加入服务器", "selectWorld.access_failure": "加载世界失败", "selectWorld.allowCommands": "允许作弊", "selectWorld.allowCommands.info": "例如/gamemode、/experience等命令", "selectWorld.allowCommands.new": "允许命令", "selectWorld.backupEraseCache": "清除缓存数据", "selectWorld.backupJoinConfirmButton": "创建备份并加载", "selectWorld.backupJoinSkipButton": "我知道我在做什么！", "selectWorld.backupQuestion.customized": "自定义世界已不受支持", "selectWorld.backupQuestion.downgrade": "不支持对存档版本进行降级", "selectWorld.backupQuestion.experimental": "使用“实验性设置”的世界不受支持", "selectWorld.backupQuestion.snapshot": "你真的想加载此世界吗？", "selectWorld.backupWarning.customized": "不巧，我们在这个版本的Minecraft中不支持自定义世界。我们可以继续加载这个世界并保持原状，但任何新生成的地形将不再被自定义。抱歉给你带来不便。", "selectWorld.backupWarning.downgrade": "这个世界上次是在%s版本中打开的，你正在使用%s版本。降低世界的游戏版本可能会导致存档损坏，我们无法保证它可以被加载和运行。如果你仍要继续，请备份该存档。", "selectWorld.backupWarning.experimental": "这个世界使用的实验性设置可能会随时停止运作。我们无法保证这些设置将来能够加载或运作。务必谨慎！", "selectWorld.backupWarning.snapshot": "这个世界上次是在%s版本中打开的，你正在使用%s版本。请备份你的世界，以防世界崩溃。", "selectWorld.bonusItems": "奖励箱", "selectWorld.cheats": "作弊", "selectWorld.commands": "命令", "selectWorld.conversion": "必须进行转换！", "selectWorld.conversion.tooltip": "此世界必须在较旧版本（比如1.6.4）中打开，以便安全转换", "selectWorld.create": "创建新的世界", "selectWorld.customizeType": "自定义", "selectWorld.dataPacks": "数据包", "selectWorld.data_read": "读取世界数据中…", "selectWorld.delete": "删除", "selectWorld.deleteButton": "删除", "selectWorld.deleteQuestion": "你确定要删除这个世界吗？", "selectWorld.deleteWarning": "“%s”将会永久消失！（真的很久！）", "selectWorld.delete_failure": "删除世界失败", "selectWorld.edit": "编辑", "selectWorld.edit.backup": "进行备份", "selectWorld.edit.backupCreated": "已备份：%s", "selectWorld.edit.backupFailed": "备份失败", "selectWorld.edit.backupFolder": "打开备份文件夹", "selectWorld.edit.backupSize": "大小：%s MB", "selectWorld.edit.export_worldgen_settings": "导出世界生成设置", "selectWorld.edit.export_worldgen_settings.failure": "导出失败", "selectWorld.edit.export_worldgen_settings.success": "导出成功", "selectWorld.edit.openFolder": "打开世界文件夹", "selectWorld.edit.optimize": "优化世界", "selectWorld.edit.resetIcon": "重置图标", "selectWorld.edit.save": "保存", "selectWorld.edit.title": "编辑世界", "selectWorld.enterName": "世界名称", "selectWorld.enterSeed": "世界生成器的种子", "selectWorld.experimental": "实验性", "selectWorld.experimental.details": "详细信息", "selectWorld.experimental.details.entry": "需要的实验性功能：%s", "selectWorld.experimental.details.title": "实验性功能需求", "selectWorld.experimental.message": "请注意！\n此配置需要使用仍在开发中的功能。你的世界可能会崩溃、损坏或不兼容未来的更新。", "selectWorld.experimental.title": "实验性功能警告", "selectWorld.experiments": "实验性内容", "selectWorld.experiments.info": "实验性内容，即仍在开发的新功能。请小心，启用这些内容有损坏风险。已开启的实验性内容在创建世界后将无法再关闭。", "selectWorld.futureworld.error.text": "试图加载来自未来版本的世界时发生错误。这本就并非万无一失，很抱歉没能成功。", "selectWorld.futureworld.error.title": "出错了！", "selectWorld.gameMode": "游戏模式", "selectWorld.gameMode.adventure": "冒险", "selectWorld.gameMode.adventure.info": "与生存模式相同，但无法放置或破坏方块。", "selectWorld.gameMode.adventure.line1": "与生存模式相同，但无法", "selectWorld.gameMode.adventure.line2": "放置或者移除方块", "selectWorld.gameMode.creative": "创造", "selectWorld.gameMode.creative.info": "无拘无束地探索创造。可以飞行，材料取之不尽，且不受怪物伤害。", "selectWorld.gameMode.creative.line1": "无限的资源、自由地飞翔", "selectWorld.gameMode.creative.line2": "并且能够瞬间破坏方块", "selectWorld.gameMode.hardcore": "极限", "selectWorld.gameMode.hardcore.info": "难度锁定为“困难”的生存模式，且死亡后无法重生。", "selectWorld.gameMode.hardcore.line1": "难度锁定在困难的生存模式", "selectWorld.gameMode.hardcore.line2": "且只有一条生命", "selectWorld.gameMode.spectator": "旁观", "selectWorld.gameMode.spectator.info": "你可以旁观，但不能互动。", "selectWorld.gameMode.spectator.line1": "你可以旁观，但不能互动", "selectWorld.gameMode.survival": "生存", "selectWorld.gameMode.survival.info": "探索未知的世界，尽情建造、收集、合成并与怪物战斗。", "selectWorld.gameMode.survival.line1": "探索世界、收集资源、合成道具、", "selectWorld.gameMode.survival.line2": "提高等级、补充体力和生命值", "selectWorld.gameRules": "游戏规则", "selectWorld.import_worldgen_settings": "导入设置", "selectWorld.import_worldgen_settings.failure": "导入设置时出错", "selectWorld.import_worldgen_settings.select_file": "选择设置文件（.json）", "selectWorld.incompatible.description": "此世界无法在此版本中打开。\n最后一次打开此世界的版本是%s。", "selectWorld.incompatible.info": "不兼容的版本：%s", "selectWorld.incompatible.title": "版本不兼容", "selectWorld.incompatible.tooltip": "此世界在不兼容的版本上创建，故无法打开。", "selectWorld.incompatible_series": "创建于不兼容的版本", "selectWorld.load_folder_access": "无法读取或访问游戏世界存档所在的文件夹！", "selectWorld.loading_list": "加载世界列表中", "selectWorld.locked": "被另一个正在运行的Minecraft实例锁定", "selectWorld.mapFeatures": "生成结构", "selectWorld.mapFeatures.info": "村庄、沉船等", "selectWorld.mapType": "世界类型", "selectWorld.mapType.normal": "普通", "selectWorld.moreWorldOptions": "更多世界选项…", "selectWorld.newWorld": "新的世界", "selectWorld.recreate": "重建", "selectWorld.recreate.customized.text": "自定义世界在这个版本的Minecraft中已不受支持。我们可以尝试用同样的种子与选项重建它，但任何自定义的地形都会丢失。抱歉给你带来不便。", "selectWorld.recreate.customized.title": "自定义世界已不受支持", "selectWorld.recreate.error.text": "尝试重建世界时出错。", "selectWorld.recreate.error.title": "出错了！", "selectWorld.resource_load": "准备资源中…", "selectWorld.resultFolder": "将会保存于：", "selectWorld.search": "搜索世界", "selectWorld.seedInfo": "留空以生成随机种子", "selectWorld.select": "进入选中的世界", "selectWorld.targetFolder": "存档文件夹：%s", "selectWorld.title": "选择世界", "selectWorld.tooltip.fromNewerVersion1": "世界是在更新的版本中被保存的，", "selectWorld.tooltip.fromNewerVersion2": "加载这个世界可能会产生问题！", "selectWorld.tooltip.snapshot1": "在这个快照中加载它之前，", "selectWorld.tooltip.snapshot2": "不要忘了备份这个世界！", "selectWorld.unable_to_load": "无法加载世界", "selectWorld.version": "版本：", "selectWorld.versionJoinButton": "仍然加载", "selectWorld.versionQuestion": "你真的想加载此世界吗？", "selectWorld.versionUnknown": "未知", "selectWorld.versionWarning": "此世界上次是在%s版本中打开的，在此版本中加载可能会导致数据损坏！", "selectWorld.warning.deprecated.question": "一些使用中的功能已被淘汰并会在将来失效。你确定要继续吗？", "selectWorld.warning.deprecated.title": "警告！这些设置使用了已淘汰的功能", "selectWorld.warning.experimental.question": "这些设置是实验性的，将来可能停止运作。你确定要继续吗？", "selectWorld.warning.experimental.title": "警告！这些设置使用了实验性功能", "selectWorld.warning.lowDiskSpace.description": "设备上的存储空间不足。\n运行游戏时磁盘空间不足可能会导致世界损坏。", "selectWorld.warning.lowDiskSpace.title": "警告！磁盘空间不足！", "selectWorld.world": "世界", "sign.edit": "编辑告示牌消息", "sleep.not_possible": "已入睡玩家的数量不足以跳过夜晚", "sleep.players_sleeping": "%s/%s名玩家已入睡", "sleep.skipping_night": "今夜将在睡梦中度过", "slot.only_single_allowed": "只允许有单个槽位，实际为“%s”", "slot.unknown": "未知的槽位“%s”", "snbt.parser.empty_key": "键不能为空", "snbt.parser.expected_binary_numeral": "应为二进制数", "snbt.parser.expected_decimal_numeral": "应为十进制数", "snbt.parser.expected_float_type": "应为浮点数", "snbt.parser.expected_hex_escape": "字符字面量长度应为%s", "snbt.parser.expected_hex_numeral": "应为十六进制数", "snbt.parser.expected_integer_type": "应为整数", "snbt.parser.expected_non_negative_number": "应为非负数", "snbt.parser.expected_number_or_boolean": "应为数字或布尔型", "snbt.parser.expected_string_uuid": "应为表示有效UUID的字符串", "snbt.parser.expected_unquoted_string": "应为有效的无引号字符串", "snbt.parser.infinity_not_allowed": "不允许使用非有限数的数值", "snbt.parser.invalid_array_element_type": "无效的数组元素类型", "snbt.parser.invalid_character_name": "无效的Unicode字符名称", "snbt.parser.invalid_codepoint": "无效的Unicode字符码位：%s", "snbt.parser.invalid_string_contents": "无效的字符串内容", "snbt.parser.invalid_unquoted_start": "无引号字符串不能以数字0-9、+或-开头", "snbt.parser.leading_zero_not_allowed": "十进制数不能以0开头", "snbt.parser.no_such_operation": "不存在的操作：%s", "snbt.parser.number_parse_failure": "解析数字失败：%s", "snbt.parser.undescore_not_allowed": "数字的开头和结尾不允许使用下划线字符", "soundCategory.ambient": "环境", "soundCategory.block": "方块", "soundCategory.hostile": "敌对生物", "soundCategory.master": "主音量", "soundCategory.music": "音乐", "soundCategory.neutral": "友好生物", "soundCategory.player": "玩家", "soundCategory.record": "唱片机/音符盒", "soundCategory.ui": "用户界面", "soundCategory.voice": "声音/语音", "soundCategory.weather": "天气", "spectatorMenu.close": "关闭菜单", "spectatorMenu.next_page": "下一页", "spectatorMenu.previous_page": "上一页", "spectatorMenu.root.prompt": "按下一个键来选择命令，再按一次来使用它。", "spectatorMenu.team_teleport": "传送到队伍成员", "spectatorMenu.team_teleport.prompt": "选择一支队伍作为传送目标", "spectatorMenu.teleport": "传送到玩家", "spectatorMenu.teleport.prompt": "选择一名玩家作为传送目标", "stat.generalButton": "通用", "stat.itemsButton": "物品", "stat.minecraft.animals_bred": "繁殖动物次数", "stat.minecraft.aviate_one_cm": "鞘翅滑行距离", "stat.minecraft.bell_ring": "鸣钟次数", "stat.minecraft.boat_one_cm": "坐船移动距离", "stat.minecraft.clean_armor": "清洗盔甲次数", "stat.minecraft.clean_banner": "清洗旗帜次数", "stat.minecraft.clean_shulker_box": "潜影盒清洗次数", "stat.minecraft.climb_one_cm": "已攀爬距离", "stat.minecraft.crouch_one_cm": "潜行距离", "stat.minecraft.damage_absorbed": "吸收的伤害", "stat.minecraft.damage_blocked_by_shield": "盾牌抵挡的伤害", "stat.minecraft.damage_dealt": "造成伤害", "stat.minecraft.damage_dealt_absorbed": "造成伤害（被吸收）", "stat.minecraft.damage_dealt_resisted": "造成伤害（被抵挡）", "stat.minecraft.damage_resisted": "抵挡的伤害", "stat.minecraft.damage_taken": "受到伤害", "stat.minecraft.deaths": "死亡次数", "stat.minecraft.drop": "物品掉落", "stat.minecraft.eat_cake_slice": "吃掉的蛋糕片数", "stat.minecraft.enchant_item": "物品附魔次数", "stat.minecraft.fall_one_cm": "摔落高度", "stat.minecraft.fill_cauldron": "炼药锅装水次数", "stat.minecraft.fish_caught": "捕鱼数", "stat.minecraft.fly_one_cm": "飞行距离", "stat.minecraft.happy_ghast_one_cm": "骑乘快乐恶魂移动距离", "stat.minecraft.horse_one_cm": "骑马移动距离", "stat.minecraft.inspect_dispenser": "搜查发射器次数", "stat.minecraft.inspect_dropper": "搜查投掷器次数", "stat.minecraft.inspect_hopper": "搜查漏斗次数", "stat.minecraft.interact_with_anvil": "与铁砧交互次数", "stat.minecraft.interact_with_beacon": "与信标交互次数", "stat.minecraft.interact_with_blast_furnace": "与高炉交互次数", "stat.minecraft.interact_with_brewingstand": "与酿造台交互次数", "stat.minecraft.interact_with_campfire": "与营火交互次数", "stat.minecraft.interact_with_cartography_table": "与制图台交互次数", "stat.minecraft.interact_with_crafting_table": "与工作台交互次数", "stat.minecraft.interact_with_furnace": "与熔炉交互次数", "stat.minecraft.interact_with_grindstone": "与砂轮交互次数", "stat.minecraft.interact_with_lectern": "与讲台交互次数", "stat.minecraft.interact_with_loom": "与织布机交互次数", "stat.minecraft.interact_with_smithing_table": "与锻造台交互次数", "stat.minecraft.interact_with_smoker": "与烟熏炉交互次数", "stat.minecraft.interact_with_stonecutter": "与切石机交互次数", "stat.minecraft.jump": "跳跃次数", "stat.minecraft.leave_game": "游戏退出次数", "stat.minecraft.minecart_one_cm": "坐矿车移动距离", "stat.minecraft.mob_kills": "生物击杀数", "stat.minecraft.open_barrel": "木桶打开次数", "stat.minecraft.open_chest": "箱子打开次数", "stat.minecraft.open_enderchest": "末影箱打开次数", "stat.minecraft.open_shulker_box": "潜影盒打开次数", "stat.minecraft.pig_one_cm": "骑猪移动距离", "stat.minecraft.play_noteblock": "音符盒播放次数", "stat.minecraft.play_record": "播放唱片数", "stat.minecraft.play_time": "游戏时长", "stat.minecraft.player_kills": "玩家击杀数", "stat.minecraft.pot_flower": "盆栽种植数", "stat.minecraft.raid_trigger": "触发袭击次数", "stat.minecraft.raid_win": "袭击胜利次数", "stat.minecraft.sleep_in_bed": "躺在床上的次数", "stat.minecraft.sneak_time": "潜行时间", "stat.minecraft.sprint_one_cm": "疾跑距离", "stat.minecraft.strider_one_cm": "骑炽足兽移动距离", "stat.minecraft.swim_one_cm": "游泳距离", "stat.minecraft.talked_to_villager": "村民交互次数", "stat.minecraft.target_hit": "击中标靶次数", "stat.minecraft.time_since_death": "自上次死亡", "stat.minecraft.time_since_rest": "自上次入眠", "stat.minecraft.total_world_time": "世界打开时间", "stat.minecraft.traded_with_villager": "村民交易次数", "stat.minecraft.trigger_trapped_chest": "陷阱箱触发次数", "stat.minecraft.tune_noteblock": "音符盒调音次数", "stat.minecraft.use_cauldron": "从炼药锅取水次数", "stat.minecraft.walk_on_water_one_cm": "水面行走距离", "stat.minecraft.walk_one_cm": "行走距离", "stat.minecraft.walk_under_water_one_cm": "水下行走距离", "stat.mobsButton": "生物", "stat_type.minecraft.broken": "损坏次数", "stat_type.minecraft.crafted": "合成次数", "stat_type.minecraft.dropped": "丢弃个数", "stat_type.minecraft.killed": "你杀死了%s个%s", "stat_type.minecraft.killed.none": "你从来没有杀死过%s", "stat_type.minecraft.killed_by": "%s杀死了你%s次", "stat_type.minecraft.killed_by.none": "你从来没有被%s杀死过", "stat_type.minecraft.mined": "开采次数", "stat_type.minecraft.picked_up": "拾起个数", "stat_type.minecraft.used": "使用次数", "stats.none": "-", "structure_block.button.detect_size": "探测", "structure_block.button.load": "加载", "structure_block.button.save": "保存", "structure_block.custom_data": "自定义数据标签名", "structure_block.detect_size": "探测结构大小和位置：", "structure_block.hover.corner": "角落：%s", "structure_block.hover.data": "数据：%s", "structure_block.hover.load": "加载：%s", "structure_block.hover.save": "保存：%s", "structure_block.include_entities": "包括实体：", "structure_block.integrity": "结构完整性及种子", "structure_block.integrity.integrity": "结构完整性", "structure_block.integrity.seed": "结构种子", "structure_block.invalid_structure_name": "无效的结构名“%s”", "structure_block.load_not_found": "不存在名为“%s”的结构 ", "structure_block.load_prepare": "结构“%s”的加载位置已就绪", "structure_block.load_success": "成功从“%s”中加载结构", "structure_block.mode.corner": "角落模式", "structure_block.mode.data": "数据模式", "structure_block.mode.load": "加载模式", "structure_block.mode.save": "保存模式", "structure_block.mode_info.corner": "角落模式 — 位置和大小标记", "structure_block.mode_info.data": "数据模式 — 游戏逻辑标记", "structure_block.mode_info.load": "加载模式 — 从文件中加载", "structure_block.mode_info.save": "保存模式 — 写入文件", "structure_block.position": "相对位置", "structure_block.position.x": "相对X坐标", "structure_block.position.y": "相对Y坐标", "structure_block.position.z": "相对Z坐标", "structure_block.save_failure": "无法保存结构“%s”", "structure_block.save_success": "成功将结构保存为“%s”", "structure_block.show_air": "显示隐形方块：", "structure_block.show_boundingbox": "显示边框：", "structure_block.size": "结构大小", "structure_block.size.x": "结构X轴大小", "structure_block.size.y": "结构Y轴大小", "structure_block.size.z": "结构Z轴大小", "structure_block.size_failure": "无法检测结构大小。请放置与结构名称对应的角落结构方块", "structure_block.size_success": "“%s”的大小已成功检测", "structure_block.strict": "严格放置：", "structure_block.structure_name": "结构名称", "subtitles.ambient.cave": "怪异的噪声", "subtitles.ambient.sound": "怪异的噪声", "subtitles.block.amethyst_block.chime": "紫水晶：叮铃", "subtitles.block.amethyst_block.resonate": "紫水晶：共振", "subtitles.block.anvil.destroy": "铁砧：被毁", "subtitles.block.anvil.land": "铁砧：着陆", "subtitles.block.anvil.use": "铁砧：使用", "subtitles.block.barrel.close": "木桶：关闭", "subtitles.block.barrel.open": "木桶：打开", "subtitles.block.beacon.activate": "信标：激活", "subtitles.block.beacon.ambient": "信标：嗡嗡作响", "subtitles.block.beacon.deactivate": "信标：失效", "subtitles.block.beacon.power_select": "信标：选择效果", "subtitles.block.beehive.drip": "蜂蜜：滴落", "subtitles.block.beehive.enter": "蜜蜂：入巢", "subtitles.block.beehive.exit": "蜜蜂：离巢", "subtitles.block.beehive.shear": "剪刀：刮削", "subtitles.block.beehive.work": "蜜蜂：工作", "subtitles.block.bell.resonate": "钟：回响", "subtitles.block.bell.use": "钟：响起", "subtitles.block.big_dripleaf.tilt_down": "垂滴叶：折下", "subtitles.block.big_dripleaf.tilt_up": "垂滴叶：升起", "subtitles.block.blastfurnace.fire_crackle": "高炉：噼啪作响", "subtitles.block.brewing_stand.brew": "酿造台：冒泡", "subtitles.block.bubble_column.bubble_pop": "气泡：破裂", "subtitles.block.bubble_column.upwards_ambient": "气泡：上浮", "subtitles.block.bubble_column.upwards_inside": "气泡：飞升", "subtitles.block.bubble_column.whirlpool_ambient": "气泡：旋转", "subtitles.block.bubble_column.whirlpool_inside": "气泡：骤降", "subtitles.block.button.click": "按钮：咔哒", "subtitles.block.cake.add_candle": "蛋糕：吧唧", "subtitles.block.campfire.crackle": "营火：噼啪作响", "subtitles.block.candle.crackle": "蜡烛：噼啪作响", "subtitles.block.candle.extinguish": "蜡烛：熄灭", "subtitles.block.chest.close": "箱子：关闭", "subtitles.block.chest.locked": "箱子：锁上", "subtitles.block.chest.open": "箱子：开启", "subtitles.block.chorus_flower.death": "紫颂花：凋零", "subtitles.block.chorus_flower.grow": "紫颂花：生长", "subtitles.block.comparator.click": "比较器：模式变更", "subtitles.block.composter.empty": "堆肥桶：清空", "subtitles.block.composter.fill": "堆肥桶：填充", "subtitles.block.composter.ready": "堆肥桶：堆肥", "subtitles.block.conduit.activate": "潮涌核心：激活", "subtitles.block.conduit.ambient": "潮涌核心：涌动", "subtitles.block.conduit.attack.target": "潮涌核心：攻击", "subtitles.block.conduit.deactivate": "潮涌核心：失效", "subtitles.block.copper_bulb.turn_off": "铜灯：熄灭", "subtitles.block.copper_bulb.turn_on": "铜灯：亮起", "subtitles.block.copper_trapdoor.close": "活板门：关闭", "subtitles.block.copper_trapdoor.open": "活板门：打开", "subtitles.block.crafter.craft": "合成器：合成", "subtitles.block.crafter.fail": "合成器：合成失败", "subtitles.block.creaking_heart.hurt": "嘎枝之心：咕哝", "subtitles.block.creaking_heart.idle": "怪异的噪声", "subtitles.block.creaking_heart.spawn": "嘎枝之心：苏醒", "subtitles.block.deadbush.idle": "干枯声", "subtitles.block.decorated_pot.insert": "饰纹陶罐：装入", "subtitles.block.decorated_pot.insert_fail": "饰纹陶罐：晃动", "subtitles.block.decorated_pot.shatter": "饰纹陶罐：碎裂", "subtitles.block.dispenser.dispense": "物品：被射出", "subtitles.block.dispenser.fail": "发射器：发射失败", "subtitles.block.door.toggle": "门：嘎吱作响", "subtitles.block.dried_ghast.ambient": "干燥声", "subtitles.block.dried_ghast.ambient_water": "失水恶魂：补水", "subtitles.block.dried_ghast.place_in_water": "失水恶魂：浸泡", "subtitles.block.dried_ghast.transition": "失水恶魂：恢复", "subtitles.block.dry_grass.ambient": "风声", "subtitles.block.enchantment_table.use": "附魔台：使用", "subtitles.block.end_portal.spawn": "末地传送门：开启", "subtitles.block.end_portal_frame.fill": "末影之眼：嵌入", "subtitles.block.eyeblossom.close": "眼眸花：闭合", "subtitles.block.eyeblossom.idle": "眼眸花：沙沙作响", "subtitles.block.eyeblossom.open": "眼眸花：张开", "subtitles.block.fence_gate.toggle": "栅栏门：嘎吱作响", "subtitles.block.fire.ambient": "火：噼啪作响", "subtitles.block.fire.extinguish": "火：熄灭", "subtitles.block.firefly_bush.idle": "萤火虫：嗡嗡", "subtitles.block.frogspawn.hatch": "蝌蚪：孵化", "subtitles.block.furnace.fire_crackle": "熔炉：噼啪作响", "subtitles.block.generic.break": "方块：被破坏", "subtitles.block.generic.fall": "某物：摔落到方块上", "subtitles.block.generic.footsteps": "脚步声", "subtitles.block.generic.hit": "方块：损坏中", "subtitles.block.generic.place": "方块：被放置", "subtitles.block.grindstone.use": "砂轮：使用", "subtitles.block.growing_plant.crop": "植物：被修剪", "subtitles.block.hanging_sign.waxed_interact_fail": "告示牌：晃动", "subtitles.block.honey_block.slide": "从蜂蜜块滑下", "subtitles.block.iron_trapdoor.close": "活板门：关闭", "subtitles.block.iron_trapdoor.open": "活板门：打开", "subtitles.block.lava.ambient": "熔岩：迸裂", "subtitles.block.lava.extinguish": "熔岩：嘶嘶声", "subtitles.block.lever.click": "拉杆：拉动", "subtitles.block.note_block.note": "音符盒：播放", "subtitles.block.pale_hanging_moss.idle": "怪异的噪声", "subtitles.block.piston.move": "活塞：移动", "subtitles.block.pointed_dripstone.drip_lava": "熔岩：滴落", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "熔岩：滴入炼药锅", "subtitles.block.pointed_dripstone.drip_water": "水：滴落", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "水：滴入炼药锅", "subtitles.block.pointed_dripstone.land": "钟乳石：塌落", "subtitles.block.portal.ambient": "传送门：呼啸", "subtitles.block.portal.travel": "传送门：噪声消散", "subtitles.block.portal.trigger": "传送门：噪声渐响", "subtitles.block.pressure_plate.click": "压力板：咔哒", "subtitles.block.pumpkin.carve": "剪刀：雕刻", "subtitles.block.redstone_torch.burnout": "红石火把：熄灭", "subtitles.block.respawn_anchor.ambient": "重生锚：呼啸", "subtitles.block.respawn_anchor.charge": "重生锚：充能", "subtitles.block.respawn_anchor.deplete": "重生锚：耗能", "subtitles.block.respawn_anchor.set_spawn": "重生锚：设置出生点", "subtitles.block.sand.idle": "沙沙声", "subtitles.block.sand.wind": "风声", "subtitles.block.sculk.charge": "幽匿块：冒泡", "subtitles.block.sculk.spread": "幽匿块：蔓延", "subtitles.block.sculk_catalyst.bloom": "幽匿催发体：催发", "subtitles.block.sculk_sensor.clicking": "幽匿感测体：颤动", "subtitles.block.sculk_sensor.clicking_stop": "幽匿感测体：停息", "subtitles.block.sculk_shrieker.shriek": "幽匿尖啸体：尖啸", "subtitles.block.shulker_box.close": "潜影盒：关闭", "subtitles.block.shulker_box.open": "潜影盒：开启", "subtitles.block.sign.waxed_interact_fail": "告示牌：晃动", "subtitles.block.smithing_table.use": "锻造台：使用", "subtitles.block.smoker.smoke": "烟熏炉：烟熏", "subtitles.block.sniffer_egg.crack": "嗅探兽蛋：裂开", "subtitles.block.sniffer_egg.hatch": "嗅探兽蛋：孵化", "subtitles.block.sniffer_egg.plop": "嗅探兽：下蛋", "subtitles.block.sponge.absorb": "海绵：吸水", "subtitles.block.sweet_berry_bush.pick_berries": "浆果：弹出", "subtitles.block.trapdoor.close": "活板门：关闭", "subtitles.block.trapdoor.open": "活板门：打开", "subtitles.block.trapdoor.toggle": "活板门：嘎吱作响", "subtitles.block.trial_spawner.about_to_spawn_item": "不祥之物：准备", "subtitles.block.trial_spawner.ambient": "试炼刷怪笼：噼啪作响", "subtitles.block.trial_spawner.ambient_charged": "不祥试炼刷怪笼：噼啪作响", "subtitles.block.trial_spawner.ambient_ominous": "不祥试炼刷怪笼：噼啪作响", "subtitles.block.trial_spawner.charge_activate": "试炼刷怪笼：不祥涌动", "subtitles.block.trial_spawner.close_shutter": "试炼刷怪笼：关闭", "subtitles.block.trial_spawner.detect_player": "试炼刷怪笼：充能", "subtitles.block.trial_spawner.eject_item": "试炼刷怪笼：喷出物品", "subtitles.block.trial_spawner.ominous_activate": "试炼刷怪笼：不祥涌动", "subtitles.block.trial_spawner.open_shutter": "试炼刷怪笼：开启", "subtitles.block.trial_spawner.spawn_item": "不祥之物：掉落", "subtitles.block.trial_spawner.spawn_item_begin": "不祥之物：出现", "subtitles.block.trial_spawner.spawn_mob": "试炼刷怪笼：生成生物", "subtitles.block.tripwire.attach": "绊线：连接", "subtitles.block.tripwire.click": "绊线：咔哒", "subtitles.block.tripwire.detach": "绊线：断开", "subtitles.block.vault.activate": "宝库：燃起", "subtitles.block.vault.ambient": "宝库：噼啪作响", "subtitles.block.vault.close_shutter": "宝库：关闭", "subtitles.block.vault.deactivate": "宝库：熄灭", "subtitles.block.vault.eject_item": "宝库：喷出物品", "subtitles.block.vault.insert_item": "宝库：解锁", "subtitles.block.vault.insert_item_fail": "宝库：解锁失败", "subtitles.block.vault.open_shutter": "宝库：打开", "subtitles.block.vault.reject_rewarded_player": "宝库：拒绝已奖励玩家", "subtitles.block.water.ambient": "水：流动", "subtitles.block.wet_sponge.dries": "海绵：烘干", "subtitles.chiseled_bookshelf.insert": "书：被放入", "subtitles.chiseled_bookshelf.insert_enchanted": "附魔书：被放入", "subtitles.chiseled_bookshelf.take": "书：被取出", "subtitles.chiseled_bookshelf.take_enchanted": "附魔书：被取出", "subtitles.enchant.thorns.hit": "荆棘：反刺", "subtitles.entity.allay.ambient_with_item": "悦灵：搜寻", "subtitles.entity.allay.ambient_without_item": "悦灵：渴求", "subtitles.entity.allay.death": "悦灵：死亡", "subtitles.entity.allay.hurt": "悦灵：受伤", "subtitles.entity.allay.item_given": "悦灵：嬉笑", "subtitles.entity.allay.item_taken": "悦灵：愉悦", "subtitles.entity.allay.item_thrown": "悦灵：投掷", "subtitles.entity.armadillo.ambient": "犰狳：呼噜", "subtitles.entity.armadillo.brush": "鳞甲：被刷落", "subtitles.entity.armadillo.death": "犰狳：死亡", "subtitles.entity.armadillo.eat": "犰狳：进食", "subtitles.entity.armadillo.hurt": "犰狳：受伤", "subtitles.entity.armadillo.hurt_reduced": "犰狳：自卫", "subtitles.entity.armadillo.land": "犰狳：着陆", "subtitles.entity.armadillo.peek": "犰狳：窥视", "subtitles.entity.armadillo.roll": "犰狳：蜷缩", "subtitles.entity.armadillo.scute_drop": "犰狳：脱落鳞甲", "subtitles.entity.armadillo.unroll_finish": "犰狳：伸展", "subtitles.entity.armadillo.unroll_start": "犰狳：窥视", "subtitles.entity.armor_stand.fall": "某物：着地", "subtitles.entity.arrow.hit": "箭：击中", "subtitles.entity.arrow.hit_player": "箭：击中玩家", "subtitles.entity.arrow.shoot": "箭：被射出", "subtitles.entity.axolotl.attack": "美西螈：攻击", "subtitles.entity.axolotl.death": "美西螈：死亡", "subtitles.entity.axolotl.hurt": "美西螈：受伤", "subtitles.entity.axolotl.idle_air": "美西螈：啾啾", "subtitles.entity.axolotl.idle_water": "美西螈：啾啾", "subtitles.entity.axolotl.splash": "美西螈：溅起水花", "subtitles.entity.axolotl.swim": "美西螈：游泳", "subtitles.entity.bat.ambient": "蝙蝠：尖声叫", "subtitles.entity.bat.death": "蝙蝠：死亡", "subtitles.entity.bat.hurt": "蝙蝠：受伤", "subtitles.entity.bat.takeoff": "蝙蝠：起飞", "subtitles.entity.bee.ambient": "蜜蜂：嗡嗡", "subtitles.entity.bee.death": "蜜蜂：死亡", "subtitles.entity.bee.hurt": "蜜蜂：受伤", "subtitles.entity.bee.loop": "蜜蜂：嗡嗡", "subtitles.entity.bee.loop_aggressive": "蜜蜂：愤怒地嗡嗡叫", "subtitles.entity.bee.pollinate": "蜜蜂：高兴地嗡嗡叫", "subtitles.entity.bee.sting": "蜜蜂：刺蛰", "subtitles.entity.blaze.ambient": "烈焰人：呼吸", "subtitles.entity.blaze.burn": "烈焰人：噼啪作响", "subtitles.entity.blaze.death": "烈焰人：死亡", "subtitles.entity.blaze.hurt": "烈焰人：受伤", "subtitles.entity.blaze.shoot": "烈焰人：射击", "subtitles.entity.boat.paddle_land": "划船", "subtitles.entity.boat.paddle_water": "划船", "subtitles.entity.bogged.ambient": "沼骸：咯咯声", "subtitles.entity.bogged.death": "沼骸：死亡", "subtitles.entity.bogged.hurt": "沼骸：受伤", "subtitles.entity.breeze.charge": "旋风人：蓄力", "subtitles.entity.breeze.death": "旋风人：死亡", "subtitles.entity.breeze.deflect": "旋风人：反弹", "subtitles.entity.breeze.hurt": "旋风人：受伤", "subtitles.entity.breeze.idle_air": "旋风人：飞行", "subtitles.entity.breeze.idle_ground": "旋风人：呼啸", "subtitles.entity.breeze.inhale": "旋风人：吸气", "subtitles.entity.breeze.jump": "旋风人：跳跃", "subtitles.entity.breeze.land": "旋风人：着陆", "subtitles.entity.breeze.shoot": "旋风人：射击", "subtitles.entity.breeze.slide": "旋风人：滑行", "subtitles.entity.breeze.whirl": "旋风人：旋转", "subtitles.entity.breeze.wind_burst": "风弹：爆裂", "subtitles.entity.camel.ambient": "骆驼：呼噜", "subtitles.entity.camel.dash": "骆驼：冲刺", "subtitles.entity.camel.dash_ready": "骆驼：恢复体力", "subtitles.entity.camel.death": "骆驼：死亡", "subtitles.entity.camel.eat": "骆驼：进食", "subtitles.entity.camel.hurt": "骆驼：受伤", "subtitles.entity.camel.saddle": "鞍：装备", "subtitles.entity.camel.sit": "骆驼：坐下", "subtitles.entity.camel.stand": "骆驼：站起", "subtitles.entity.camel.step": "骆驼：脚步声", "subtitles.entity.camel.step_sand": "骆驼：踏沙", "subtitles.entity.cat.ambient": "猫：喵~", "subtitles.entity.cat.beg_for_food": "猫：求食", "subtitles.entity.cat.death": "猫：死亡", "subtitles.entity.cat.eat": "猫：进食", "subtitles.entity.cat.hiss": "猫：嘶嘶声", "subtitles.entity.cat.hurt": "猫：受伤", "subtitles.entity.cat.purr": "猫：呼噜声", "subtitles.entity.chicken.ambient": "鸡：咯咯叫", "subtitles.entity.chicken.death": "鸡：死亡", "subtitles.entity.chicken.egg": "鸡：下蛋", "subtitles.entity.chicken.hurt": "鸡：受伤", "subtitles.entity.cod.death": "鳕鱼：死亡", "subtitles.entity.cod.flop": "鳕鱼：扑腾", "subtitles.entity.cod.hurt": "鳕鱼：受伤", "subtitles.entity.cow.ambient": "牛：哞~", "subtitles.entity.cow.death": "牛：死亡", "subtitles.entity.cow.hurt": "牛：受伤", "subtitles.entity.cow.milk": "牛：被挤奶", "subtitles.entity.creaking.activate": "嘎枝：注视", "subtitles.entity.creaking.ambient": "嘎枝：嘎吱作响", "subtitles.entity.creaking.attack": "嘎枝：攻击", "subtitles.entity.creaking.deactivate": "嘎枝：静息", "subtitles.entity.creaking.death": "嘎枝：碎裂", "subtitles.entity.creaking.freeze": "嘎枝：驻足", "subtitles.entity.creaking.spawn": "嘎枝：显现", "subtitles.entity.creaking.sway": "嘎枝：被击打", "subtitles.entity.creaking.twitch": "嘎枝：抽搐", "subtitles.entity.creaking.unfreeze": "嘎枝：移动", "subtitles.entity.creeper.death": "苦力怕：死亡", "subtitles.entity.creeper.hurt": "苦力怕：受伤", "subtitles.entity.creeper.primed": "苦力怕：嘶~", "subtitles.entity.dolphin.ambient": "海豚：啾啾", "subtitles.entity.dolphin.ambient_water": "海豚：吹口哨", "subtitles.entity.dolphin.attack": "海豚：攻击", "subtitles.entity.dolphin.death": "海豚：死亡", "subtitles.entity.dolphin.eat": "海豚：进食", "subtitles.entity.dolphin.hurt": "海豚：受伤", "subtitles.entity.dolphin.jump": "海豚：跃起", "subtitles.entity.dolphin.play": "海豚：嬉戏", "subtitles.entity.dolphin.splash": "海豚：溅起水花", "subtitles.entity.dolphin.swim": "海豚：游泳", "subtitles.entity.donkey.ambient": "驴：嘶叫", "subtitles.entity.donkey.angry": "驴：嘶鸣", "subtitles.entity.donkey.chest": "驴：装备箱子", "subtitles.entity.donkey.death": "驴：死亡", "subtitles.entity.donkey.eat": "驴：进食", "subtitles.entity.donkey.hurt": "驴：受伤", "subtitles.entity.donkey.jump": "驴：跳跃", "subtitles.entity.drowned.ambient": "溺尸：呻吟", "subtitles.entity.drowned.ambient_water": "溺尸：呻吟", "subtitles.entity.drowned.death": "溺尸：死亡", "subtitles.entity.drowned.hurt": "溺尸：受伤", "subtitles.entity.drowned.shoot": "溺尸：投掷三叉戟", "subtitles.entity.drowned.step": "溺尸：脚步声", "subtitles.entity.drowned.swim": "溺尸：游泳", "subtitles.entity.egg.throw": "鸡蛋：飞出", "subtitles.entity.elder_guardian.ambient": "远古守卫者：低鸣", "subtitles.entity.elder_guardian.ambient_land": "远古守卫者：弹跳", "subtitles.entity.elder_guardian.curse": "远古守卫者：诅咒", "subtitles.entity.elder_guardian.death": "远古守卫者：死亡", "subtitles.entity.elder_guardian.flop": "远古守卫者：扑腾", "subtitles.entity.elder_guardian.hurt": "远古守卫者：受伤", "subtitles.entity.ender_dragon.ambient": "末影龙：咆哮", "subtitles.entity.ender_dragon.death": "末影龙：死亡", "subtitles.entity.ender_dragon.flap": "末影龙：拍打翅膀", "subtitles.entity.ender_dragon.growl": "末影龙：吼叫", "subtitles.entity.ender_dragon.hurt": "末影龙：受伤", "subtitles.entity.ender_dragon.shoot": "末影龙：射击", "subtitles.entity.ender_eye.death": "末影之眼：掉落", "subtitles.entity.ender_eye.launch": "末影之眼：射出", "subtitles.entity.ender_pearl.throw": "末影珍珠：飞出", "subtitles.entity.enderman.ambient": "末影人：低鸣", "subtitles.entity.enderman.death": "末影人：死亡", "subtitles.entity.enderman.hurt": "末影人：受伤", "subtitles.entity.enderman.scream": "末影人：尖叫", "subtitles.entity.enderman.stare": "末影人：喊叫", "subtitles.entity.enderman.teleport": "末影人：传送", "subtitles.entity.endermite.ambient": "末影螨：窜动", "subtitles.entity.endermite.death": "末影螨：死亡", "subtitles.entity.endermite.hurt": "末影螨：受伤", "subtitles.entity.evoker.ambient": "唤魔者：咕哝", "subtitles.entity.evoker.cast_spell": "唤魔者：施法", "subtitles.entity.evoker.celebrate": "唤魔者：欢呼", "subtitles.entity.evoker.death": "唤魔者：死亡", "subtitles.entity.evoker.hurt": "唤魔者：受伤", "subtitles.entity.evoker.prepare_attack": "唤魔者：准备攻击", "subtitles.entity.evoker.prepare_summon": "唤魔者：准备召唤", "subtitles.entity.evoker.prepare_wololo": "唤魔者：准备施咒", "subtitles.entity.evoker_fangs.attack": "尖牙：咬合", "subtitles.entity.experience_orb.pickup": "获得经验", "subtitles.entity.firework_rocket.blast": "烟花：爆裂", "subtitles.entity.firework_rocket.launch": "烟花：发射", "subtitles.entity.firework_rocket.twinkle": "烟花：闪烁", "subtitles.entity.fish.swim": "溅起水花", "subtitles.entity.fishing_bobber.retrieve": "浮漂：收回", "subtitles.entity.fishing_bobber.splash": "浮漂：溅起水花", "subtitles.entity.fishing_bobber.throw": "浮漂：甩出", "subtitles.entity.fox.aggro": "狐狸：愤怒", "subtitles.entity.fox.ambient": "狐狸：吱吱叫", "subtitles.entity.fox.bite": "狐狸：撕咬", "subtitles.entity.fox.death": "狐狸：死亡", "subtitles.entity.fox.eat": "狐狸：进食", "subtitles.entity.fox.hurt": "狐狸：受伤", "subtitles.entity.fox.screech": "狐狸：尖声叫", "subtitles.entity.fox.sleep": "狐狸：打鼾", "subtitles.entity.fox.sniff": "狐狸：嗅探", "subtitles.entity.fox.spit": "狐狸：吐出", "subtitles.entity.fox.teleport": "狐狸：传送", "subtitles.entity.frog.ambient": "青蛙：咕呱", "subtitles.entity.frog.death": "青蛙：死亡", "subtitles.entity.frog.eat": "青蛙：进食", "subtitles.entity.frog.hurt": "青蛙：受伤", "subtitles.entity.frog.lay_spawn": "青蛙：产卵", "subtitles.entity.frog.long_jump": "青蛙：跳跃", "subtitles.entity.generic.big_fall": "某物：着地", "subtitles.entity.generic.burn": "燃烧", "subtitles.entity.generic.death": "死亡", "subtitles.entity.generic.drink": "啜饮", "subtitles.entity.generic.eat": "进食", "subtitles.entity.generic.explode": "爆炸", "subtitles.entity.generic.extinguish_fire": "火：熄灭", "subtitles.entity.generic.hurt": "某物：受伤", "subtitles.entity.generic.small_fall": "某物：摔倒", "subtitles.entity.generic.splash": "溅起水花", "subtitles.entity.generic.swim": "游泳", "subtitles.entity.generic.wind_burst": "风弹：爆裂", "subtitles.entity.ghast.ambient": "恶魂：哭泣", "subtitles.entity.ghast.death": "恶魂：死亡", "subtitles.entity.ghast.hurt": "恶魂：受伤", "subtitles.entity.ghast.shoot": "恶魂：射击", "subtitles.entity.ghastling.ambient": "小恶魂：咕咕", "subtitles.entity.ghastling.death": "小恶魂：死亡", "subtitles.entity.ghastling.hurt": "小恶魂：受伤", "subtitles.entity.ghastling.spawn": "小恶魂：出现", "subtitles.entity.glow_item_frame.add_item": "荧光物品展示框：填充", "subtitles.entity.glow_item_frame.break": "荧光物品展示框：被破坏", "subtitles.entity.glow_item_frame.place": "荧光物品展示框：被放置", "subtitles.entity.glow_item_frame.remove_item": "荧光物品展示框：清空", "subtitles.entity.glow_item_frame.rotate_item": "荧光物品展示框：转动", "subtitles.entity.glow_squid.ambient": "发光鱿鱼：游泳", "subtitles.entity.glow_squid.death": "发光鱿鱼：死亡", "subtitles.entity.glow_squid.hurt": "发光鱿鱼：受伤", "subtitles.entity.glow_squid.squirt": "发光鱿鱼：喷墨", "subtitles.entity.goat.ambient": "山羊：咩~", "subtitles.entity.goat.death": "山羊：死亡", "subtitles.entity.goat.eat": "山羊：进食", "subtitles.entity.goat.horn_break": "山羊：断角", "subtitles.entity.goat.hurt": "山羊：受伤", "subtitles.entity.goat.long_jump": "山羊：跳跃", "subtitles.entity.goat.milk": "山羊：被挤奶", "subtitles.entity.goat.prepare_ram": "山羊：跺脚", "subtitles.entity.goat.ram_impact": "山羊：冲撞", "subtitles.entity.goat.screaming.ambient": "山羊：喊叫", "subtitles.entity.goat.step": "山羊：脚步声", "subtitles.entity.guardian.ambient": "守卫者：低鸣", "subtitles.entity.guardian.ambient_land": "守卫者：弹跳", "subtitles.entity.guardian.attack": "守卫者：射击", "subtitles.entity.guardian.death": "守卫者：死亡", "subtitles.entity.guardian.flop": "守卫者：扑腾", "subtitles.entity.guardian.hurt": "守卫者：受伤", "subtitles.entity.happy_ghast.ambient": "快乐恶魂：哼唱", "subtitles.entity.happy_ghast.death": "快乐恶魂：死亡", "subtitles.entity.happy_ghast.equip": "挽具：装备", "subtitles.entity.happy_ghast.harness_goggles_down": "快乐恶魂：就绪", "subtitles.entity.happy_ghast.harness_goggles_up": "快乐恶魂：停下", "subtitles.entity.happy_ghast.hurt": "快乐恶魂：受伤", "subtitles.entity.happy_ghast.unequip": "挽具：卸下", "subtitles.entity.hoglin.ambient": "疣猪兽：咆哮", "subtitles.entity.hoglin.angry": "疣猪兽：怒吼", "subtitles.entity.hoglin.attack": "疣猪兽：攻击", "subtitles.entity.hoglin.converted_to_zombified": "疣猪兽：转化为僵尸疣猪兽", "subtitles.entity.hoglin.death": "疣猪兽：死亡", "subtitles.entity.hoglin.hurt": "疣猪兽：受伤", "subtitles.entity.hoglin.retreat": "疣猪兽：退缩", "subtitles.entity.hoglin.step": "疣猪兽：脚步声", "subtitles.entity.horse.ambient": "马：嘶鸣", "subtitles.entity.horse.angry": "马：嘶鸣", "subtitles.entity.horse.armor": "马铠：装备", "subtitles.entity.horse.breathe": "马：呼吸", "subtitles.entity.horse.death": "马：死亡", "subtitles.entity.horse.eat": "马：进食", "subtitles.entity.horse.gallop": "马：奔腾", "subtitles.entity.horse.hurt": "马：受伤", "subtitles.entity.horse.jump": "马：跳跃", "subtitles.entity.horse.saddle": "鞍：装备", "subtitles.entity.husk.ambient": "尸壳：低吼", "subtitles.entity.husk.converted_to_zombie": "尸壳：转化为僵尸", "subtitles.entity.husk.death": "尸壳：死亡", "subtitles.entity.husk.hurt": "尸壳：受伤", "subtitles.entity.illusioner.ambient": "幻术师：咕哝", "subtitles.entity.illusioner.cast_spell": "幻术师：施法", "subtitles.entity.illusioner.death": "幻术师：死亡", "subtitles.entity.illusioner.hurt": "幻术师：受伤", "subtitles.entity.illusioner.mirror_move": "幻术师：分影", "subtitles.entity.illusioner.prepare_blindness": "幻术师：准备失明法术", "subtitles.entity.illusioner.prepare_mirror": "幻术师：准备分影法术", "subtitles.entity.iron_golem.attack": "铁傀儡：攻击", "subtitles.entity.iron_golem.damage": "铁傀儡：受损", "subtitles.entity.iron_golem.death": "铁傀儡：死亡", "subtitles.entity.iron_golem.hurt": "铁傀儡：受伤", "subtitles.entity.iron_golem.repair": "铁傀儡：修复", "subtitles.entity.item.break": "物品：损坏", "subtitles.entity.item.pickup": "物品：被拾起", "subtitles.entity.item_frame.add_item": "物品展示框：填充", "subtitles.entity.item_frame.break": "物品展示框：被破坏", "subtitles.entity.item_frame.place": "物品展示框：被放置", "subtitles.entity.item_frame.remove_item": "物品展示框：清空", "subtitles.entity.item_frame.rotate_item": "物品展示框：转动", "subtitles.entity.leash_knot.break": "拴绳结：被破坏", "subtitles.entity.leash_knot.place": "拴绳结：被系上", "subtitles.entity.lightning_bolt.impact": "电闪", "subtitles.entity.lightning_bolt.thunder": "雷鸣", "subtitles.entity.llama.ambient": "羊驼：吼叫", "subtitles.entity.llama.angry": "羊驼：怒吼", "subtitles.entity.llama.chest": "羊驼：装备箱子", "subtitles.entity.llama.death": "羊驼：死亡", "subtitles.entity.llama.eat": "羊驼：进食", "subtitles.entity.llama.hurt": "羊驼：受伤", "subtitles.entity.llama.spit": "羊驼：喷射唾沫", "subtitles.entity.llama.step": "羊驼：脚步声", "subtitles.entity.llama.swag": "羊驼：被装饰", "subtitles.entity.magma_cube.death": "岩浆怪：死亡", "subtitles.entity.magma_cube.hurt": "岩浆怪：受伤", "subtitles.entity.magma_cube.squish": "岩浆怪：挤压", "subtitles.entity.minecart.inside": "矿车：哐啷作响", "subtitles.entity.minecart.inside_underwater": "矿车：在水中哐啷作响", "subtitles.entity.minecart.riding": "矿车：行进", "subtitles.entity.mooshroom.convert": "哞菇：转化", "subtitles.entity.mooshroom.eat": "哞菇：进食", "subtitles.entity.mooshroom.milk": "哞菇：被挤奶", "subtitles.entity.mooshroom.suspicious_milk": "哞菇：被挤出谜之炖菜", "subtitles.entity.mule.ambient": "骡：鸣叫", "subtitles.entity.mule.angry": "骡：嘶鸣", "subtitles.entity.mule.chest": "骡：装备箱子", "subtitles.entity.mule.death": "骡：死亡", "subtitles.entity.mule.eat": "骡：进食", "subtitles.entity.mule.hurt": "骡：受伤", "subtitles.entity.mule.jump": "骡：跳跃", "subtitles.entity.painting.break": "画：被破坏", "subtitles.entity.painting.place": "画：被放置", "subtitles.entity.panda.aggressive_ambient": "熊猫：发怒", "subtitles.entity.panda.ambient": "熊猫：喘息", "subtitles.entity.panda.bite": "熊猫：撕咬", "subtitles.entity.panda.cant_breed": "熊猫：哀鸣", "subtitles.entity.panda.death": "熊猫：死亡", "subtitles.entity.panda.eat": "熊猫：进食", "subtitles.entity.panda.hurt": "熊猫：受伤", "subtitles.entity.panda.pre_sneeze": "熊猫：鼻痒", "subtitles.entity.panda.sneeze": "熊猫：打喷嚏", "subtitles.entity.panda.step": "熊猫：脚步声", "subtitles.entity.panda.worried_ambient": "熊猫：呜咽", "subtitles.entity.parrot.ambient": "鹦鹉：说话", "subtitles.entity.parrot.death": "鹦鹉：死亡", "subtitles.entity.parrot.eats": "鹦鹉：进食", "subtitles.entity.parrot.fly": "鹦鹉：扑翼", "subtitles.entity.parrot.hurts": "鹦鹉：受伤", "subtitles.entity.parrot.imitate.blaze": "鹦鹉：呼吸", "subtitles.entity.parrot.imitate.bogged": "鹦鹉：咯咯声", "subtitles.entity.parrot.imitate.breeze": "鹦鹉：呼啸", "subtitles.entity.parrot.imitate.creaking": "鹦鹉：嘎吱作响", "subtitles.entity.parrot.imitate.creeper": "鹦鹉：嘶~", "subtitles.entity.parrot.imitate.drowned": "鹦鹉：呻吟", "subtitles.entity.parrot.imitate.elder_guardian": "鹦鹉：低鸣", "subtitles.entity.parrot.imitate.ender_dragon": "鹦鹉：咆哮", "subtitles.entity.parrot.imitate.endermite": "鹦鹉：窜动", "subtitles.entity.parrot.imitate.evoker": "鹦鹉：咕哝", "subtitles.entity.parrot.imitate.ghast": "鹦鹉：哭泣", "subtitles.entity.parrot.imitate.guardian": "鹦鹉：低鸣", "subtitles.entity.parrot.imitate.hoglin": "鹦鹉：咆哮", "subtitles.entity.parrot.imitate.husk": "鹦鹉：低吼", "subtitles.entity.parrot.imitate.illusioner": "鹦鹉：咕哝", "subtitles.entity.parrot.imitate.magma_cube": "鹦鹉：挤压", "subtitles.entity.parrot.imitate.phantom": "鹦鹉：尖声叫", "subtitles.entity.parrot.imitate.piglin": "鹦鹉：哼叫", "subtitles.entity.parrot.imitate.piglin_brute": "鹦鹉：哼叫", "subtitles.entity.parrot.imitate.pillager": "鹦鹉：咕哝", "subtitles.entity.parrot.imitate.ravager": "鹦鹉：呼噜", "subtitles.entity.parrot.imitate.shulker": "鹦鹉：窥视", "subtitles.entity.parrot.imitate.silverfish": "鹦鹉：嘶嘶", "subtitles.entity.parrot.imitate.skeleton": "鹦鹉：咯咯声", "subtitles.entity.parrot.imitate.slime": "鹦鹉：挤压", "subtitles.entity.parrot.imitate.spider": "鹦鹉：嘶嘶", "subtitles.entity.parrot.imitate.stray": "鹦鹉：咯咯声", "subtitles.entity.parrot.imitate.vex": "鹦鹉：恼人", "subtitles.entity.parrot.imitate.vindicator": "鹦鹉：低语", "subtitles.entity.parrot.imitate.warden": "鹦鹉：呻吟", "subtitles.entity.parrot.imitate.witch": "鹦鹉：暗笑", "subtitles.entity.parrot.imitate.wither": "鹦鹉：愤怒", "subtitles.entity.parrot.imitate.wither_skeleton": "鹦鹉：咯咯声", "subtitles.entity.parrot.imitate.zoglin": "鹦鹉：咆哮", "subtitles.entity.parrot.imitate.zombie": "鹦鹉：低吼", "subtitles.entity.parrot.imitate.zombie_villager": "鹦鹉：低吼", "subtitles.entity.phantom.ambient": "幻翼：尖声叫", "subtitles.entity.phantom.bite": "幻翼：撕咬", "subtitles.entity.phantom.death": "幻翼：死亡", "subtitles.entity.phantom.flap": "幻翼：振翅", "subtitles.entity.phantom.hurt": "幻翼：受伤", "subtitles.entity.phantom.swoop": "幻翼：俯冲", "subtitles.entity.pig.ambient": "猪：哼叫", "subtitles.entity.pig.death": "猪：死亡", "subtitles.entity.pig.hurt": "猪：受伤", "subtitles.entity.pig.saddle": "鞍：装备", "subtitles.entity.piglin.admiring_item": "猪灵：端详物品", "subtitles.entity.piglin.ambient": "猪灵：哼叫", "subtitles.entity.piglin.angry": "猪灵：愤怒地哼叫", "subtitles.entity.piglin.celebrate": "猪灵：庆祝", "subtitles.entity.piglin.converted_to_zombified": "猪灵：转化为僵尸猪灵", "subtitles.entity.piglin.death": "猪灵：死亡", "subtitles.entity.piglin.hurt": "猪灵：受伤", "subtitles.entity.piglin.jealous": "猪灵：羡慕地哼叫", "subtitles.entity.piglin.retreat": "猪灵：退缩", "subtitles.entity.piglin.step": "猪灵：脚步声", "subtitles.entity.piglin_brute.ambient": "猪灵蛮兵：哼叫", "subtitles.entity.piglin_brute.angry": "猪灵蛮兵：愤怒地哼叫", "subtitles.entity.piglin_brute.converted_to_zombified": "猪灵蛮兵：转化为僵尸猪灵", "subtitles.entity.piglin_brute.death": "猪灵蛮兵：死亡", "subtitles.entity.piglin_brute.hurt": "猪灵蛮兵：受伤", "subtitles.entity.piglin_brute.step": "猪灵蛮兵：脚步声", "subtitles.entity.pillager.ambient": "掠夺者：咕哝", "subtitles.entity.pillager.celebrate": "掠夺者：欢呼", "subtitles.entity.pillager.death": "掠夺者：死亡", "subtitles.entity.pillager.hurt": "掠夺者：受伤", "subtitles.entity.player.attack.crit": "暴击", "subtitles.entity.player.attack.knockback": "击退攻击", "subtitles.entity.player.attack.strong": "重击", "subtitles.entity.player.attack.sweep": "横扫攻击", "subtitles.entity.player.attack.weak": "轻击", "subtitles.entity.player.burp": "打嗝", "subtitles.entity.player.death": "玩家：死亡", "subtitles.entity.player.freeze_hurt": "玩家：冻伤", "subtitles.entity.player.hurt": "玩家：受伤", "subtitles.entity.player.hurt_drown": "玩家：溺水", "subtitles.entity.player.hurt_on_fire": "玩家：燃烧", "subtitles.entity.player.levelup": "玩家：升级", "subtitles.entity.player.teleport": "玩家：传送", "subtitles.entity.polar_bear.ambient": "北极熊：低吼", "subtitles.entity.polar_bear.ambient_baby": "幼年北极熊：哼哼", "subtitles.entity.polar_bear.death": "北极熊：死亡", "subtitles.entity.polar_bear.hurt": "北极熊：受伤", "subtitles.entity.polar_bear.warning": "北极熊：咆哮", "subtitles.entity.potion.splash": "玻璃瓶：碎裂", "subtitles.entity.potion.throw": "玻璃瓶：扔出", "subtitles.entity.puffer_fish.blow_out": "河豚：收缩", "subtitles.entity.puffer_fish.blow_up": "河豚：膨胀", "subtitles.entity.puffer_fish.death": "河豚：死亡", "subtitles.entity.puffer_fish.flop": "河豚：扑腾", "subtitles.entity.puffer_fish.hurt": "河豚：受伤", "subtitles.entity.puffer_fish.sting": "河豚：刺蛰", "subtitles.entity.rabbit.ambient": "兔子：吱吱叫", "subtitles.entity.rabbit.attack": "兔子：攻击", "subtitles.entity.rabbit.death": "兔子：死亡", "subtitles.entity.rabbit.hurt": "兔子：受伤", "subtitles.entity.rabbit.jump": "兔子：跳动", "subtitles.entity.ravager.ambient": "劫掠兽：呼噜", "subtitles.entity.ravager.attack": "劫掠兽：撕咬", "subtitles.entity.ravager.celebrate": "劫掠兽：欢呼", "subtitles.entity.ravager.death": "劫掠兽：死亡", "subtitles.entity.ravager.hurt": "劫掠兽：受伤", "subtitles.entity.ravager.roar": "劫掠兽：咆哮", "subtitles.entity.ravager.step": "劫掠兽：脚步声", "subtitles.entity.ravager.stunned": "劫掠兽：眩晕", "subtitles.entity.salmon.death": "鲑鱼：死亡", "subtitles.entity.salmon.flop": "鲑鱼：扑腾", "subtitles.entity.salmon.hurt": "鲑鱼：受伤", "subtitles.entity.sheep.ambient": "绵羊：咩~", "subtitles.entity.sheep.death": "绵羊：死亡", "subtitles.entity.sheep.hurt": "绵羊：受伤", "subtitles.entity.shulker.ambient": "潜影贝：窥视", "subtitles.entity.shulker.close": "潜影贝：闭合", "subtitles.entity.shulker.death": "潜影贝：死亡", "subtitles.entity.shulker.hurt": "潜影贝：受伤", "subtitles.entity.shulker.open": "潜影贝：打开", "subtitles.entity.shulker.shoot": "潜影贝：射击", "subtitles.entity.shulker.teleport": "潜影贝：传送", "subtitles.entity.shulker_bullet.hit": "潜影弹：爆炸", "subtitles.entity.shulker_bullet.hurt": "潜影弹：碎裂", "subtitles.entity.silverfish.ambient": "蠹虫：嘶嘶", "subtitles.entity.silverfish.death": "蠹虫：死亡", "subtitles.entity.silverfish.hurt": "蠹虫：受伤", "subtitles.entity.skeleton.ambient": "骷髅：咯咯声", "subtitles.entity.skeleton.converted_to_stray": "骷髅：转化为流浪者", "subtitles.entity.skeleton.death": "骷髅：死亡", "subtitles.entity.skeleton.hurt": "骷髅：受伤", "subtitles.entity.skeleton.shoot": "骷髅：射击", "subtitles.entity.skeleton_horse.ambient": "骷髅马：嘶叫", "subtitles.entity.skeleton_horse.death": "骷髅马：死亡", "subtitles.entity.skeleton_horse.hurt": "骷髅马：受伤", "subtitles.entity.skeleton_horse.jump_water": "骷髅马：跳跃", "subtitles.entity.skeleton_horse.swim": "骷髅马：游泳", "subtitles.entity.slime.attack": "史莱姆：攻击", "subtitles.entity.slime.death": "史莱姆：死亡", "subtitles.entity.slime.hurt": "史莱姆：受伤", "subtitles.entity.slime.squish": "史莱姆：挤压", "subtitles.entity.sniffer.death": "嗅探兽：死亡", "subtitles.entity.sniffer.digging": "嗅探兽：挖掘", "subtitles.entity.sniffer.digging_stop": "嗅探兽：站起", "subtitles.entity.sniffer.drop_seed": "嗅探兽：丢下种子", "subtitles.entity.sniffer.eat": "嗅探兽：进食", "subtitles.entity.sniffer.egg_crack": "嗅探兽蛋：裂开", "subtitles.entity.sniffer.egg_hatch": "嗅探兽蛋：孵化", "subtitles.entity.sniffer.happy": "嗅探兽：愉悦", "subtitles.entity.sniffer.hurt": "嗅探兽：受伤", "subtitles.entity.sniffer.idle": "嗅探兽：呼噜", "subtitles.entity.sniffer.scenting": "嗅探兽：嗅闻", "subtitles.entity.sniffer.searching": "嗅探兽：搜寻", "subtitles.entity.sniffer.sniffing": "嗅探兽：嗅探", "subtitles.entity.sniffer.step": "嗅探兽：脚步声", "subtitles.entity.snow_golem.death": "雪傀儡：死亡", "subtitles.entity.snow_golem.hurt": "雪傀儡：受伤", "subtitles.entity.snowball.throw": "雪球：飞出", "subtitles.entity.spider.ambient": "蜘蛛：嘶嘶", "subtitles.entity.spider.death": "蜘蛛：死亡", "subtitles.entity.spider.hurt": "蜘蛛：受伤", "subtitles.entity.squid.ambient": "鱿鱼：游泳", "subtitles.entity.squid.death": "鱿鱼：死亡", "subtitles.entity.squid.hurt": "鱿鱼：受伤", "subtitles.entity.squid.squirt": "鱿鱼：喷墨", "subtitles.entity.stray.ambient": "流浪者：咯咯声", "subtitles.entity.stray.death": "流浪者：死亡", "subtitles.entity.stray.hurt": "流浪者：受伤", "subtitles.entity.strider.death": "炽足兽：死亡", "subtitles.entity.strider.eat": "炽足兽：进食", "subtitles.entity.strider.happy": "炽足兽：颤鸣", "subtitles.entity.strider.hurt": "炽足兽：受伤", "subtitles.entity.strider.idle": "炽足兽：啾啾", "subtitles.entity.strider.retreat": "炽足兽：退缩", "subtitles.entity.tadpole.death": "蝌蚪：死亡", "subtitles.entity.tadpole.flop": "蝌蚪：扑腾", "subtitles.entity.tadpole.grow_up": "蝌蚪：成长", "subtitles.entity.tadpole.hurt": "蝌蚪：受伤", "subtitles.entity.tnt.primed": "TNT：嘶嘶作响", "subtitles.entity.tropical_fish.death": "热带鱼：死亡", "subtitles.entity.tropical_fish.flop": "热带鱼：扑腾", "subtitles.entity.tropical_fish.hurt": "热带鱼：受伤", "subtitles.entity.turtle.ambient_land": "海龟：啾啾", "subtitles.entity.turtle.death": "海龟：死亡", "subtitles.entity.turtle.death_baby": "幼年海龟：死亡", "subtitles.entity.turtle.egg_break": "海龟蛋：破裂", "subtitles.entity.turtle.egg_crack": "海龟蛋：裂开", "subtitles.entity.turtle.egg_hatch": "海龟蛋：孵化", "subtitles.entity.turtle.hurt": "海龟：受伤", "subtitles.entity.turtle.hurt_baby": "幼年海龟：受伤", "subtitles.entity.turtle.lay_egg": "海龟：产卵", "subtitles.entity.turtle.shamble": "海龟：爬行", "subtitles.entity.turtle.shamble_baby": "幼年海龟：爬行", "subtitles.entity.turtle.swim": "海龟：游泳", "subtitles.entity.vex.ambient": "恼鬼：恼人", "subtitles.entity.vex.charge": "恼鬼：尖叫", "subtitles.entity.vex.death": "恼鬼：死亡", "subtitles.entity.vex.hurt": "恼鬼：受伤", "subtitles.entity.villager.ambient": "村民：喃喃自语", "subtitles.entity.villager.celebrate": "村民：欢呼", "subtitles.entity.villager.death": "村民：死亡", "subtitles.entity.villager.hurt": "村民：受伤", "subtitles.entity.villager.no": "村民：拒绝", "subtitles.entity.villager.trade": "村民：交易", "subtitles.entity.villager.work_armorer": "盔甲匠：工作", "subtitles.entity.villager.work_butcher": "屠夫：工作", "subtitles.entity.villager.work_cartographer": "制图师：工作", "subtitles.entity.villager.work_cleric": "牧师：工作", "subtitles.entity.villager.work_farmer": "农民：工作", "subtitles.entity.villager.work_fisherman": "渔夫：工作", "subtitles.entity.villager.work_fletcher": "制箭师：工作", "subtitles.entity.villager.work_leatherworker": "皮匠：工作", "subtitles.entity.villager.work_librarian": "图书管理员：工作", "subtitles.entity.villager.work_mason": "石匠：工作", "subtitles.entity.villager.work_shepherd": "牧羊人：工作", "subtitles.entity.villager.work_toolsmith": "工具匠：工作", "subtitles.entity.villager.work_weaponsmith": "武器匠：工作", "subtitles.entity.villager.yes": "村民：同意", "subtitles.entity.vindicator.ambient": "卫道士：低语", "subtitles.entity.vindicator.celebrate": "卫道士：欢呼", "subtitles.entity.vindicator.death": "卫道士：死亡", "subtitles.entity.vindicator.hurt": "卫道士：受伤", "subtitles.entity.wandering_trader.ambient": "流浪商人：喃喃自语", "subtitles.entity.wandering_trader.death": "流浪商人：死亡", "subtitles.entity.wandering_trader.disappeared": "流浪商人：隐身", "subtitles.entity.wandering_trader.drink_milk": "流浪商人：喝奶", "subtitles.entity.wandering_trader.drink_potion": "流浪商人：饮用药水", "subtitles.entity.wandering_trader.hurt": "流浪商人：受伤", "subtitles.entity.wandering_trader.no": "流浪商人：拒绝", "subtitles.entity.wandering_trader.reappeared": "流浪商人：现身", "subtitles.entity.wandering_trader.trade": "流浪商人：交易", "subtitles.entity.wandering_trader.yes": "流浪商人：同意", "subtitles.entity.warden.agitated": "监守者：愤怒地呻吟", "subtitles.entity.warden.ambient": "监守者：呻吟", "subtitles.entity.warden.angry": "监守者：狂怒", "subtitles.entity.warden.attack_impact": "监守者：击中", "subtitles.entity.warden.death": "监守者：死亡", "subtitles.entity.warden.dig": "监守者：掘地", "subtitles.entity.warden.emerge": "监守者：现身", "subtitles.entity.warden.heartbeat": "监守者：心跳声", "subtitles.entity.warden.hurt": "监守者：受伤", "subtitles.entity.warden.listening": "监守者：察觉到某物", "subtitles.entity.warden.listening_angry": "监守者：愤怒地察觉到某物", "subtitles.entity.warden.nearby_close": "监守者：接近", "subtitles.entity.warden.nearby_closer": "监守者：逼近", "subtitles.entity.warden.nearby_closest": "监守者：迫近", "subtitles.entity.warden.roar": "监守者：咆哮", "subtitles.entity.warden.sniff": "监守者：嗅闻", "subtitles.entity.warden.sonic_boom": "监守者：发射音波", "subtitles.entity.warden.sonic_charge": "监守者：蓄力", "subtitles.entity.warden.step": "监守者：脚步声", "subtitles.entity.warden.tendril_clicks": "监守者：卷须颤动", "subtitles.entity.wind_charge.throw": "风弹：飞行", "subtitles.entity.wind_charge.wind_burst": "风弹：爆裂", "subtitles.entity.witch.ambient": "女巫：暗笑", "subtitles.entity.witch.celebrate": "女巫：欢呼", "subtitles.entity.witch.death": "女巫：死亡", "subtitles.entity.witch.drink": "女巫：饮用药水", "subtitles.entity.witch.hurt": "女巫：受伤", "subtitles.entity.witch.throw": "女巫：投掷", "subtitles.entity.wither.ambient": "凋灵：愤怒", "subtitles.entity.wither.death": "凋灵：死亡", "subtitles.entity.wither.hurt": "凋灵：受伤", "subtitles.entity.wither.shoot": "凋灵：攻击", "subtitles.entity.wither.spawn": "凋灵：解放", "subtitles.entity.wither_skeleton.ambient": "凋灵骷髅：咯咯声", "subtitles.entity.wither_skeleton.death": "凋灵骷髅：死亡", "subtitles.entity.wither_skeleton.hurt": "凋灵骷髅：受伤", "subtitles.entity.wolf.ambient": "狼：喘息", "subtitles.entity.wolf.bark": "狼：吠叫", "subtitles.entity.wolf.death": "狼：死亡", "subtitles.entity.wolf.growl": "狼：嚎叫", "subtitles.entity.wolf.hurt": "狼：受伤", "subtitles.entity.wolf.pant": "狼：喘息", "subtitles.entity.wolf.shake": "狼：摇动", "subtitles.entity.wolf.whine": "狼：呜咽", "subtitles.entity.zoglin.ambient": "僵尸疣猪兽：咆哮", "subtitles.entity.zoglin.angry": "僵尸疣猪兽：怒吼", "subtitles.entity.zoglin.attack": "僵尸疣猪兽：攻击", "subtitles.entity.zoglin.death": "僵尸疣猪兽：死亡", "subtitles.entity.zoglin.hurt": "僵尸疣猪兽：受伤", "subtitles.entity.zoglin.step": "僵尸疣猪兽：脚步声", "subtitles.entity.zombie.ambient": "僵尸：低吼", "subtitles.entity.zombie.attack_wooden_door": "门：晃动", "subtitles.entity.zombie.break_wooden_door": "门：毁坏", "subtitles.entity.zombie.converted_to_drowned": "僵尸：转化为溺尸", "subtitles.entity.zombie.death": "僵尸：死亡", "subtitles.entity.zombie.destroy_egg": "海龟蛋：被踩踏", "subtitles.entity.zombie.hurt": "僵尸：受伤", "subtitles.entity.zombie.infect": "僵尸：感染", "subtitles.entity.zombie_horse.ambient": "僵尸马：嘶叫", "subtitles.entity.zombie_horse.death": "僵尸马：死亡", "subtitles.entity.zombie_horse.hurt": "僵尸马：受伤", "subtitles.entity.zombie_villager.ambient": "僵尸村民：低吼", "subtitles.entity.zombie_villager.converted": "僵尸村民：哀嚎", "subtitles.entity.zombie_villager.cure": "僵尸村民：撕心裂肺", "subtitles.entity.zombie_villager.death": "僵尸村民：死亡", "subtitles.entity.zombie_villager.hurt": "僵尸村民：受伤", "subtitles.entity.zombified_piglin.ambient": "僵尸猪灵：呼噜", "subtitles.entity.zombified_piglin.angry": "僵尸猪灵：愤怒地呼噜", "subtitles.entity.zombified_piglin.death": "僵尸猪灵：死亡", "subtitles.entity.zombified_piglin.hurt": "僵尸猪灵：受伤", "subtitles.event.mob_effect.bad_omen": "不祥之兆：显现", "subtitles.event.mob_effect.raid_omen": "袭击：迫在眉睫", "subtitles.event.mob_effect.trial_omen": "不祥试炼：迫在眉睫", "subtitles.event.raid.horn": "不祥号角：鸣响", "subtitles.item.armor.equip": "盔甲：装备", "subtitles.item.armor.equip_chain": "锁链盔甲：碰擦", "subtitles.item.armor.equip_diamond": "钻石盔甲：碰擦", "subtitles.item.armor.equip_elytra": "鞘翅：沙沙作响", "subtitles.item.armor.equip_gold": "金盔甲：叮当", "subtitles.item.armor.equip_iron": "铁盔甲：铿锵", "subtitles.item.armor.equip_leather": "皮革盔甲：摩擦", "subtitles.item.armor.equip_netherite": "下界合金盔甲：铿锵", "subtitles.item.armor.equip_turtle": "海龟壳：咕咚", "subtitles.item.armor.equip_wolf": "狼铠：装备", "subtitles.item.armor.unequip_wolf": "狼铠：滑落", "subtitles.item.axe.scrape": "斧：刮削", "subtitles.item.axe.strip": "斧：削皮", "subtitles.item.axe.wax_off": "脱蜡", "subtitles.item.bone_meal.use": "骨粉：沙沙作响", "subtitles.item.book.page_turn": "书页：沙沙作响", "subtitles.item.book.put": "书：放置", "subtitles.item.bottle.empty": "玻璃瓶：倒空", "subtitles.item.bottle.fill": "玻璃瓶：装满", "subtitles.item.brush.brushing.generic": "刷子：刷扫中", "subtitles.item.brush.brushing.gravel": "刷子：刷扫沙砾中", "subtitles.item.brush.brushing.gravel.complete": "刷子：刷扫沙砾完毕", "subtitles.item.brush.brushing.sand": "刷子：刷扫沙子中", "subtitles.item.brush.brushing.sand.complete": "刷子：刷扫沙子完毕", "subtitles.item.bucket.empty": "铁桶：倒空", "subtitles.item.bucket.fill": "铁桶：装满", "subtitles.item.bucket.fill_axolotl": "美西螈：被装起", "subtitles.item.bucket.fill_fish": "鱼：被捕获", "subtitles.item.bucket.fill_tadpole": "蝌蚪：被捕获", "subtitles.item.bundle.drop_contents": "收纳袋：倒空", "subtitles.item.bundle.insert": "物品：装入袋中", "subtitles.item.bundle.insert_fail": "收纳袋：已满", "subtitles.item.bundle.remove_one": "物品：从袋中取出", "subtitles.item.chorus_fruit.teleport": "玩家：传送", "subtitles.item.crop.plant": "作物：种植", "subtitles.item.crossbow.charge": "弩：蓄力", "subtitles.item.crossbow.hit": "箭：击中", "subtitles.item.crossbow.load": "弩：装填", "subtitles.item.crossbow.shoot": "弩：发射", "subtitles.item.dye.use": "染料：染色", "subtitles.item.elytra.flying": "呼啸声", "subtitles.item.firecharge.use": "火焰弹：呼啸", "subtitles.item.flintandsteel.use": "打火石：点火", "subtitles.item.glow_ink_sac.use": "荧光墨囊：涂抹", "subtitles.item.goat_horn.play": "山羊角：吹奏", "subtitles.item.hoe.till": "锄：犁地", "subtitles.item.honey_bottle.drink": "吞咽", "subtitles.item.honeycomb.wax_on": "涂蜡", "subtitles.item.horse_armor.unequip": "马铠：滑落", "subtitles.item.ink_sac.use": "墨囊：涂抹", "subtitles.item.lead.break": "拴绳：剪断", "subtitles.item.lead.tied": "拴绳：系上", "subtitles.item.lead.untied": "拴绳：解开", "subtitles.item.llama_carpet.unequip": "地毯：滑落", "subtitles.item.lodestone_compass.lock": "磁石指针：绑定磁石", "subtitles.item.mace.smash_air": "重锤：猛击", "subtitles.item.mace.smash_ground": "重锤：猛击", "subtitles.item.nether_wart.plant": "作物：种植", "subtitles.item.ominous_bottle.dispose": "玻璃瓶：碎裂", "subtitles.item.saddle.unequip": "鞍：滑落", "subtitles.item.shears.shear": "剪刀：咔嚓", "subtitles.item.shears.snip": "剪刀：剪断", "subtitles.item.shield.block": "盾牌：格挡", "subtitles.item.shovel.flatten": "锹：压地", "subtitles.item.spyglass.stop_using": "望远镜：缩小", "subtitles.item.spyglass.use": "望远镜：放大", "subtitles.item.totem.use": "图腾：发动", "subtitles.item.trident.hit": "三叉戟：突刺", "subtitles.item.trident.hit_ground": "三叉戟：振动", "subtitles.item.trident.return": "三叉戟：收回", "subtitles.item.trident.riptide": "三叉戟：突进", "subtitles.item.trident.throw": "三叉戟：铿锵", "subtitles.item.trident.thunder": "三叉戟：电闪雷鸣", "subtitles.item.wolf_armor.break": "狼铠：损坏", "subtitles.item.wolf_armor.crack": "狼铠：裂开", "subtitles.item.wolf_armor.damage": "狼铠：受损", "subtitles.item.wolf_armor.repair": "狼铠：被修复", "subtitles.particle.soul_escape": "灵魂：逸散", "subtitles.ui.cartography_table.take_result": "地图：绘制", "subtitles.ui.hud.bubble_pop": "氧气值：减少", "subtitles.ui.loom.take_result": "织布机：使用", "subtitles.ui.stonecutter.take_result": "切石机：使用", "subtitles.weather.rain": "雨：落下", "symlink_warning.message": "从含有符号链接的文件夹中加载世界可能导致风险，尤其是在你不清楚链接目标内容时。请访问%s了解更多信息。", "symlink_warning.message.pack": "继续操作前请知晓，加载含有符号链接的包可能会有风险。请访问%s了解更多信息。", "symlink_warning.message.world": "继续操作前请知晓，从含有符号链接的文件夹中加载世界可能会有风险。请访问%s了解更多信息。", "symlink_warning.more_info": "更多信息", "symlink_warning.title": "世界文件夹包含符号链接", "symlink_warning.title.pack": "添加的包中含有符号链接", "symlink_warning.title.world": "世界文件夹中含有符号链接", "team.collision.always": "总是碰撞", "team.collision.never": "禁用碰撞", "team.collision.pushOtherTeams": "队伍间碰撞", "team.collision.pushOwnTeam": "队伍内碰撞", "team.notFound": "未知的队伍“%s”", "team.visibility.always": "始终显示", "team.visibility.hideForOtherTeams": "对别队隐藏", "team.visibility.hideForOwnTeam": "对本队隐藏", "team.visibility.never": "始终隐藏", "telemetry.event.advancement_made.description": "了解达成进度前后发生的事情有助于我们更好了解及改进游戏流程。", "telemetry.event.advancement_made.title": "达成进度", "telemetry.event.game_load_times.description": "此事件会测量游戏启动各个阶段的时长以帮助我们找到游戏启动的性能优化点。", "telemetry.event.game_load_times.title": "游戏加载时间", "telemetry.event.optional": "%s（可选）", "telemetry.event.optional.disabled": "%s（可选，已禁用）", "telemetry.event.performance_metrics.description": "了解Minecraft的整体性能概况，可以帮助我们针对各种机型和操作系统调整与优化游戏。 \n游戏版本信息也包含在其中，以帮助我们比较Minecraft新版本的性能概况。", "telemetry.event.performance_metrics.title": "性能指标", "telemetry.event.required": "%s（必要）", "telemetry.event.world_load_times.description": "对我们而言，了解加入世界所需的时长，以及这一时长随着时间推移会如何变化是很重要的。比如，当我们加入新功能或进行较大的技术更改时，我们需要知道这对加载时间有何影响。", "telemetry.event.world_load_times.title": "世界加载时间", "telemetry.event.world_loaded.description": "了解玩家玩Minecraft时的细节（例如游戏模式、客户端或服务端是否被修改，以及游戏版本）有助于我们在游戏更新时注重改进玩家最关心的部分。\n世界加载事件将同世界卸载事件一起用于计算游戏会话的持续时长。", "telemetry.event.world_loaded.title": "世界加载", "telemetry.event.world_unloaded.description": "此事件与世界加载事件用于计算世界会话的持续时间。\n持续时间（以秒和刻为单位）在世界会话结束时（退回到标题屏幕，或从服务器断开连接）测量。", "telemetry.event.world_unloaded.title": "世界卸载", "telemetry.property.advancement_game_time.title": "游戏时间（刻）", "telemetry.property.advancement_id.title": "进度ID", "telemetry.property.client_id.title": "客户端ID", "telemetry.property.client_modded.title": "客户端是否被修改", "telemetry.property.dedicated_memory_kb.title": "专用内存（kB）", "telemetry.property.event_timestamp_utc.title": "事件时间戳（UTC）", "telemetry.property.frame_rate_samples.title": "帧率样本（FPS）", "telemetry.property.game_mode.title": "游戏模式", "telemetry.property.game_version.title": "游戏版本", "telemetry.property.launcher_name.title": "启动器名称", "telemetry.property.load_time_bootstrap_ms.title": "启动引导时间（毫秒）", "telemetry.property.load_time_loading_overlay_ms.title": "加载屏幕显示时间（毫秒）", "telemetry.property.load_time_pre_window_ms.title": "窗口开启前时间（毫秒）", "telemetry.property.load_time_total_time_ms.title": "总计加载时间（毫秒）", "telemetry.property.minecraft_session_id.title": "Minecraft会话ID", "telemetry.property.new_world.title": "是否为新的世界", "telemetry.property.number_of_samples.title": "样本数", "telemetry.property.operating_system.title": "操作系统", "telemetry.property.opt_in.title": "可选择加入", "telemetry.property.platform.title": "平台", "telemetry.property.realms_map_content.title": "Realm地图内容（小游戏名称）", "telemetry.property.render_distance.title": "渲染距离", "telemetry.property.render_time_samples.title": "渲染用时样本", "telemetry.property.seconds_since_load.title": "自加载后的时间（秒）", "telemetry.property.server_modded.title": "服务端是否被修改", "telemetry.property.server_type.title": "服务器类型", "telemetry.property.ticks_since_load.title": "自加载后的时间（刻）", "telemetry.property.used_memory_samples.title": "已使用的内存", "telemetry.property.user_id.title": "用户ID", "telemetry.property.world_load_time_ms.title": "世界加载时间（毫秒）", "telemetry.property.world_session_id.title": "世界会话ID", "telemetry_info.button.give_feedback": "提供反馈", "telemetry_info.button.privacy_statement": "隐私声明", "telemetry_info.button.show_data": "打开我的数据", "telemetry_info.opt_in.description": "我同意发送可选的遥测数据", "telemetry_info.property_title": "包含的数据", "telemetry_info.screen.description": "收集这些数据可以让我们了解与玩家有关的实际情况，以便帮助我们改进Minecraft。\n你也可以发送其他反馈来帮助我们持续改进Minecraft。", "telemetry_info.screen.title": "遥测数据收集", "test.error.block_property_mismatch": "属性%s应为%s，实际为%s", "test.error.block_property_missing": "缺少方块属性，属性%s应为%s", "test.error.entity_property": "实体%s测试失败：%s", "test.error.entity_property_details": "实体%s测试失败：%s，应为：%s，实际为：%s", "test.error.expected_block": "方块应为%s，实际为%s", "test.error.expected_block_tag": "方块应为#%s中之一，实际为%s", "test.error.expected_container_contents": "容器应包含：%s", "test.error.expected_container_contents_single": "容器应含有单个：%s", "test.error.expected_empty_container": "容器应为空", "test.error.expected_entity": "缺少%s", "test.error.expected_entity_around": "%2$s, %3$s, %4$s周围应存在%1$s", "test.error.expected_entity_count": "应有%s个类型为%s的实体，实际有%s个", "test.error.expected_entity_data": "实体数据应为：%s，实际为：%s", "test.error.expected_entity_data_predicate": "%s的实体数据不匹配", "test.error.expected_entity_effect": "%s应有%s %s效果", "test.error.expected_entity_having": "实体物品栏中应包含%s", "test.error.expected_entity_holding": "实体应持有%s", "test.error.expected_entity_in_test": "%s应存在于测试中", "test.error.expected_entity_not_touching": "%s不应接触%s, %s, %s（相对位置：%s, %s, %s）", "test.error.expected_entity_touching": "%s应接触%s, %s, %s（相对位置：%s, %s, %s）", "test.error.expected_item": "应为%s类型的物品", "test.error.expected_items_count": "应为%s个%s类型的物品，实际为%s个", "test.error.fail": "满足失败条件", "test.error.invalid_block_type": "发现非预期的方块类型：%s", "test.error.missing_block_entity": "缺少方块实体", "test.error.position": "%s，位于%s, %s, %s（相对位置：%s, %s, %s），%s刻", "test.error.sequence.condition_already_triggered": "条件已在%s刻时触发", "test.error.sequence.condition_not_triggered": "条件未触发", "test.error.sequence.invalid_tick": "成功于无效的刻：应为%s", "test.error.sequence.not_completed": "测试在序列完成前超时", "test.error.set_biome": "设置测试生物群系失败", "test.error.spawn_failure": "创建实体%s失败", "test.error.state_not_equal": "状态错误。应为%s，实际为%s", "test.error.structure.failure": "为%s放置测试结构失败", "test.error.tick": "%s，时间为%s刻", "test.error.ticking_without_structure": "尚未放置结构时执行了测试", "test.error.timeout.no_result": "未在%s刻内成功或失败", "test.error.timeout.no_sequences_finished": "在%s刻内没有序列完成", "test.error.too_many_entities": "%2$s, %3$s, %4$s周围应仅存在一个%1$s，实际为%5$s个", "test.error.unexpected_block": "方块不应为%s", "test.error.unexpected_entity": "%s不应存在", "test.error.unexpected_item": "物品不应为类型%s", "test.error.unknown": "未知的内部错误：%s", "test.error.value_not_equal": "属性%s应为%s，实际为%s", "test.error.wrong_block_entity": "错误的方块实体类型：%s", "test_block.error.missing": "测试结构缺少%s方块", "test_block.error.too_many": "%s方块过多", "test_block.invalid_timeout": "无效的时限（%s），刻数必须为正数", "test_block.message": "消息：", "test_block.mode.accept": "接受", "test_block.mode.fail": "失败", "test_block.mode.log": "日志输出", "test_block.mode.start": "启动", "test_block.mode_info.accept": "接受模式 - 接受（部分）测试的成功", "test_block.mode_info.fail": "失败模式 - 使测试失败", "test_block.mode_info.log": "日志输出模式 - 将消息输出到日志", "test_block.mode_info.start": "启动模式 - 测试的起点", "test_instance.action.reset": "重置并加载", "test_instance.action.run": "加载并运行", "test_instance.action.save": "保存结构", "test_instance.description.batch": "批次：%s", "test_instance.description.failed": "失败：%s", "test_instance.description.function": "函数：%s", "test_instance.description.invalid_id": "无效的测试ID", "test_instance.description.no_test": "此测试不存在", "test_instance.description.structure": "结构：%s", "test_instance.description.type": "类型：%s", "test_instance.type.block_based": "基于方块的测试", "test_instance.type.function": "内置函数测试", "test_instance_block.entities": "实体：", "test_instance_block.error.no_test": "存在未定义的测试，无法在%s, %s, %s处运行测试实例", "test_instance_block.error.no_test_structure": "没有测试结构，无法在%s, %s, %s处运行测试实例", "test_instance_block.error.unable_to_save": "无法保存%s, %s, %s处测试实例的测试结构模板", "test_instance_block.invalid": "[无效]", "test_instance_block.reset_success": "测试重置成功：%s", "test_instance_block.rotation": "旋转角度：", "test_instance_block.size": "测试结构大小", "test_instance_block.starting": "正在启动测试%s", "test_instance_block.test_id": "测试实例ID", "title.32bit.deprecation": "检测到32位系统：未来将需要64位系统，使用32位系统可能将无法进行游戏！", "title.32bit.deprecation.realms": "Minecraft不久后需要64位系统才能运行，届时你将无法使用该设备进行游戏或使用Realms服务。你需要自行取消所有Realms订阅。", "title.32bit.deprecation.realms.check": "不再显示此屏幕", "title.32bit.deprecation.realms.header": "检测到32位系统", "title.credits": "© Mojang AB 请勿二次分发！", "title.multiplayer.disabled": "多人游戏已被禁用，请检查你的Microsoft账户设置。", "title.multiplayer.disabled.banned.name": "你必须在进行在线游戏前更改名称", "title.multiplayer.disabled.banned.permanent": "你的账户已被永久封禁，无法进行多人游戏", "title.multiplayer.disabled.banned.temporary": "你的账户已被暂时封禁，无法进行多人游戏", "title.multiplayer.lan": "多人游戏（局域网）", "title.multiplayer.other": "多人游戏（第三方服务器）", "title.multiplayer.realms": "多人游戏（Realms）", "title.singleplayer": "单人游戏", "translation.test.args": "%s %s", "translation.test.complex": "前缀，%s%2$s 然后是 %s 和 %1$s 最后是 %s 还有 %1$s！", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "% 你好", "translation.test.invalid2": "%s 你好", "translation.test.none": "你好，世界！", "translation.test.world": "世界", "trim_material.minecraft.amethyst": "紫水晶质", "trim_material.minecraft.copper": "铜质", "trim_material.minecraft.diamond": "钻石质", "trim_material.minecraft.emerald": "绿宝石质", "trim_material.minecraft.gold": "金质", "trim_material.minecraft.iron": "铁质", "trim_material.minecraft.lapis": "青金石质", "trim_material.minecraft.netherite": "下界合金质", "trim_material.minecraft.quartz": "石英质", "trim_material.minecraft.redstone": "红石质", "trim_material.minecraft.resin": "树脂质", "trim_pattern.minecraft.bolt": "镶铆盔甲纹饰", "trim_pattern.minecraft.coast": "海岸盔甲纹饰", "trim_pattern.minecraft.dune": "沙丘盔甲纹饰", "trim_pattern.minecraft.eye": "眼眸盔甲纹饰", "trim_pattern.minecraft.flow": "涡流盔甲纹饰", "trim_pattern.minecraft.host": "雇主盔甲纹饰", "trim_pattern.minecraft.raiser": "牧民盔甲纹饰", "trim_pattern.minecraft.rib": "肋骨盔甲纹饰", "trim_pattern.minecraft.sentry": "哨兵盔甲纹饰", "trim_pattern.minecraft.shaper": "塑造盔甲纹饰", "trim_pattern.minecraft.silence": "幽静盔甲纹饰", "trim_pattern.minecraft.snout": "猪鼻盔甲纹饰", "trim_pattern.minecraft.spire": "尖塔盔甲纹饰", "trim_pattern.minecraft.tide": "潮汐盔甲纹饰", "trim_pattern.minecraft.vex": "恼鬼盔甲纹饰", "trim_pattern.minecraft.ward": "监守盔甲纹饰", "trim_pattern.minecraft.wayfinder": "向导盔甲纹饰", "trim_pattern.minecraft.wild": "荒野盔甲纹饰", "tutorial.bundleInsert.description": "单击鼠标右键收纳物品", "tutorial.bundleInsert.title": "使用收纳袋", "tutorial.craft_planks.description": "配方书能提供帮助", "tutorial.craft_planks.title": "合成木板", "tutorial.find_tree.description": "敲击木头来收集它", "tutorial.find_tree.title": "找到一棵树", "tutorial.look.description": "使用你的鼠标来转动", "tutorial.look.title": "环顾四周", "tutorial.move.description": "用%s来跳跃", "tutorial.move.title": "用%s、%s、%s和%s来移动", "tutorial.open_inventory.description": "按下%s", "tutorial.open_inventory.title": "打开你的物品栏", "tutorial.punch_tree.description": "按住%s", "tutorial.punch_tree.title": "摧毁树木", "tutorial.socialInteractions.description": "按%s打开", "tutorial.socialInteractions.title": "社交", "upgrade.minecraft.netherite_upgrade": "下界合金升级"}