{"accessibility.onboarding.accessibility.button": "協器置設⋯", "accessibility.onboarding.screen.narrator": "擊Enter以啟復辭", "accessibility.onboarding.screen.title": "恭迎至礦藝！\n\n啟復辭乎，抑覽助乎？", "addServer.add": "畢", "addServer.enterIp": "伺服器之址", "addServer.enterName": "伺服器之名", "addServer.resourcePack": "伺服器之資囊", "addServer.resourcePack.disabled": "既禁", "addServer.resourcePack.enabled": "既啟", "addServer.resourcePack.prompt": "詢", "addServer.title": "更伺服器之訊", "advMode.command": "控制臺之令", "advMode.mode": "式", "advMode.mode.auto": "復", "advMode.mode.autoexec.bat": "恆啟", "advMode.mode.conditional": "限", "advMode.mode.redstone": "興動", "advMode.mode.redstoneTriggered": "啟以赤石", "advMode.mode.sequence": "鏈", "advMode.mode.unconditional": "非限", "advMode.notAllowed": "須創吏", "advMode.notEnabled": "命令塊爲是伺服器所禁", "advMode.previousOutput": "嚮出", "advMode.setCommand": "設是命令塊之令", "advMode.setCommand.success": "設塊令以%s", "advMode.trackOutput": "錄出", "advMode.triggering": "引", "advMode.type": "類", "advancement.advancementNotFound": "未明之功：%s", "advancements.adventure.adventuring_time.description": "覓眾生態域於世", "advancements.adventure.adventuring_time.title": "跋山涉水", "advancements.adventure.arbalistic.description": "援弩一發，貫獸五屬", "advancements.adventure.arbalistic.title": "一矢五穿", "advancements.adventure.avoid_vibration.description": "暗度幽匿探子抑監守旁以免見聞", "advancements.adventure.avoid_vibration.title": "匿影捷步", "advancements.adventure.blowback.description": "反風靈之風彈而斃之", "advancements.adventure.blowback.title": "逆風而舉", "advancements.adventure.brush_armadillo.description": "拭得犰狳之鱗", "advancements.adventure.brush_armadillo.title": "楚楚可鱗", "advancements.adventure.bullseye.description": "去三十方而中鵠心", "advancements.adventure.bullseye.title": "高中紅心", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "以四陶片合爲飾甕", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "內修外華", "advancements.adventure.crafters_crafting_crafters.description": "近製械之方製製械者", "advancements.adventure.crafters_crafting_crafters.title": "還製其身", "advancements.adventure.fall_from_world_height.description": "自生界至高處（築限）墮至其礎而免於死焉", "advancements.adventure.fall_from_world_height.title": "上天入地", "advancements.adventure.heart_transplanter.description": "正置䦪心於縞柞樁之間", "advancements.adventure.heart_transplanter.title": "移心接木", "advancements.adventure.hero_of_the_village.description": "守鄉於襲", "advancements.adventure.hero_of_the_village.title": "鄉里英傑", "advancements.adventure.honey_block_slide.description": "落於蜜塊以免墜傷", "advancements.adventure.honey_block_slide.title": "黏吝繳繞", "advancements.adventure.kill_a_mob.description": "誅一敵獸", "advancements.adventure.kill_a_mob.title": "小試牛刀", "advancements.adventure.kill_all_mobs.description": "誅諸敵獸各一", "advancements.adventure.kill_all_mobs.title": "天下無敵手", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "殺生於幽匿引側", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "蠶食鯨吞", "advancements.adventure.lighten_up.description": "斧削銅燈以明之", "advancements.adventure.lighten_up.title": "銅光煥發", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "全一鄉民於驚雷之下而無火生焉", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "驅雷掣電", "advancements.adventure.minecraft_trials_edition.description": "涉煉武堂", "advancements.adventure.minecraft_trials_edition.title": "礦藝：煉武版", "advancements.adventure.ol_betsy.description": "弩射之", "advancements.adventure.ol_betsy.title": "一觸即發", "advancements.adventure.overoverkill.description": "持椎一擊而傷以五十心", "advancements.adventure.overoverkill.title": "痛貫天靈", "advancements.adventure.play_jukebox_in_meadows.description": "以留聲機之樂章，益艸甸之生氣", "advancements.adventure.play_jukebox_in_meadows.title": "此曲衹應天上有", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "以較赭儀驗雕書櫥之盈虧", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "學富五櫥", "advancements.adventure.revaulting.description": "以厄煉管開厄寶篋", "advancements.adventure.revaulting.title": "礪煉呈華", "advancements.adventure.root.description": "歷險、遊覽、戰鬥", "advancements.adventure.root.title": "歷險", "advancements.adventure.salvage_sherd.description": "拭謎塊以得陶片", "advancements.adventure.salvage_sherd.title": "零珠斷璧", "advancements.adventure.shoot_arrow.description": "以矢射物", "advancements.adventure.shoot_arrow.title": "審顧挽弦", "advancements.adventure.sleep_in_bed.description": "寢以更汝之生處", "advancements.adventure.sleep_in_bed.title": "高枕無憂", "advancements.adventure.sniper_duel.description": "去五十方而殺骷髏", "advancements.adventure.sniper_duel.title": "佻身飛鏃", "advancements.adventure.spyglass_at_dragon.description": "以望遠鏡觀終眇龍", "advancements.adventure.spyglass_at_dragon.title": "其鵬乎？", "advancements.adventure.spyglass_at_ghast.description": "以望遠鏡觀魄", "advancements.adventure.spyglass_at_ghast.title": "其孔明燈乎？", "advancements.adventure.spyglass_at_parrot.description": "以望遠鏡觀鸚鵡", "advancements.adventure.spyglass_at_parrot.title": "其鳥乎？", "advancements.adventure.summon_iron_golem.description": "築鐵傀儡以助衛鄉", "advancements.adventure.summon_iron_golem.title": "招兵買馬", "advancements.adventure.throw_trident.description": "以三叉戟擲物。\n注：唯有一兵而擲之，斯非上矣。", "advancements.adventure.throw_trident.title": "千金一擲", "advancements.adventure.totem_of_undying.description": "以保命符離死境", "advancements.adventure.totem_of_undying.title": "起死回生", "advancements.adventure.trade.description": "與鄉民賈", "advancements.adventure.trade.title": "市不二價", "advancements.adventure.trade_at_world_height.description": "於築限立與鄉民商", "advancements.adventure.trade_at_world_height.title": "九霄商賈", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "咸施旋塔、豕鼻、脅肋、監守、寂靜、惱鬼、潮汐、嚮導八鍛模", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "珠圍翠繞", "advancements.adventure.trim_with_any_armor_pattern.description": "紋甲胄於鍛案", "advancements.adventure.trim_with_any_armor_pattern.title": "妝點一新", "advancements.adventure.two_birds_one_arrow.description": "貫殺二魘靈於一矢", "advancements.adventure.two_birds_one_arrow.title": "一箭雙鵰", "advancements.adventure.under_lock_and_key.description": "以煉管開寶篋", "advancements.adventure.under_lock_and_key.title": "金繩玉鎖", "advancements.adventure.use_lodestone.description": "於礠石使一司南", "advancements.adventure.use_lodestone.title": "天涯共此石", "advancements.adventure.very_very_frightening.description": "雷擊一鄉民", "advancements.adventure.very_very_frightening.title": "一震之威", "advancements.adventure.voluntary_exile.description": "殺劫寇之首。\n或且去此鄉⋯", "advancements.adventure.voluntary_exile.title": "禍不單行", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "行於齏雪而不陷", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "履雪無痕", "advancements.adventure.who_needs_rockets.description": "藉風彈自揚八方", "advancements.adventure.who_needs_rockets.title": "扶搖直上", "advancements.adventure.whos_the_pillager_now.description": "以弩反射劫寇", "advancements.adventure.whos_the_pillager_now.title": "風水輪流轉", "advancements.empty": "空空如也⋯", "advancements.end.dragon_breath.description": "以琉璃甁取龍涎", "advancements.end.dragon_breath.title": "君須涼茶", "advancements.end.dragon_egg.description": "得龍卵", "advancements.end.dragon_egg.title": "生生不息", "advancements.end.elytra.description": "得翼", "advancements.end.elytra.title": "不畏浮雲遮望眼", "advancements.end.enter_end_gateway.description": "去是嶼", "advancements.end.enter_end_gateway.title": "逍遙遊", "advancements.end.find_end_city.description": "咄，行，何難焉？", "advancements.end.find_end_city.title": "危樓高百尺", "advancements.end.kill_dragon.description": "祝君好運", "advancements.end.kill_dragon.title": "龍戰玄黃", "advancements.end.levitate.description": "藉匿贆之擊浮五十方", "advancements.end.levitate.title": "一覽眾山小", "advancements.end.respawn_dragon.description": "復生終眇龍", "advancements.end.respawn_dragon.title": "六道輪回", "advancements.end.root.description": "抑始矣？", "advancements.end.root.title": "終界", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "使悅靈摯洋餻於絲竹匣上", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "萬壽頌", "advancements.husbandry.allay_deliver_item_to_player.description": "使悅靈摯於君", "advancements.husbandry.allay_deliver_item_to_player.title": "義結金蘭", "advancements.husbandry.axolotl_in_a_bucket.description": "獲螈於桶", "advancements.husbandry.axolotl_in_a_bucket.title": "亦獸亦美", "advancements.husbandry.balanced_diet.description": "品盡天下之食，而不計其利害", "advancements.husbandry.balanced_diet.title": "遍嘗百艸", "advancements.husbandry.breed_all_animals.description": "咸媾諸獸", "advancements.husbandry.breed_all_animals.title": "雙宿雙飛", "advancements.husbandry.breed_an_animal.description": "媾二獸", "advancements.husbandry.breed_an_animal.title": "吾命何來", "advancements.husbandry.complete_catalogue.description": "馴全品之貓！", "advancements.husbandry.complete_catalogue.title": "齊貓要術", "advancements.husbandry.feed_snifflet.description": "飼一幼嗅獸", "advancements.husbandry.feed_snifflet.title": "乳嗅未乾", "advancements.husbandry.fishy_business.description": "釣取一魚", "advancements.husbandry.fishy_business.title": "初涉漁道", "advancements.husbandry.froglights.description": "集各鼃光於行囊", "advancements.husbandry.froglights.title": "珠聯璧合！", "advancements.husbandry.kill_axolotl_target.description": "與螈同行而共殺一敵", "advancements.husbandry.kill_axolotl_target.title": "厚誼癒疾！", "advancements.husbandry.leash_all_frog_variants.description": "以韁繫各色之鼃", "advancements.husbandry.leash_all_frog_variants.title": "眾鼃躍集", "advancements.husbandry.make_a_sign_glow.description": "爍一牌文", "advancements.husbandry.make_a_sign_glow.title": "熒熒爍目！", "advancements.husbandry.netherite_hoe.description": "鍛合玄鈺錠於鋤，遂更思汝生之擇", "advancements.husbandry.netherite_hoe.title": "躬耕不輟", "advancements.husbandry.obtain_sniffer_egg.description": "得嗅獸之卵", "advancements.husbandry.obtain_sniffer_egg.title": "饒有興味", "advancements.husbandry.place_dried_ghast_in_water.description": "置乾魄於水中", "advancements.husbandry.place_dried_ghast_in_water.title": "相濡以沐！", "advancements.husbandry.plant_any_sniffer_seed.description": "任栽一古種", "advancements.husbandry.plant_any_sniffer_seed.title": "繩其祖畝", "advancements.husbandry.plant_seed.description": "播種而靜待其成", "advancements.husbandry.plant_seed.title": "咸播秬黍", "advancements.husbandry.remove_wolf_armor.description": "鉸解狼甲", "advancements.husbandry.remove_wolf_armor.title": "錚錚鉸鉸", "advancements.husbandry.repair_wolf_armor.description": "以犰狳鱗繕壞狼甲", "advancements.husbandry.repair_wolf_armor.title": "修我甲兵", "advancements.husbandry.ride_a_boat_with_a_goat.description": "浮舟水上，行有山羊", "advancements.husbandry.ride_a_boat_with_a_goat.title": "漂羊過海", "advancements.husbandry.root.description": "友與食充世", "advancements.husbandry.root.title": "行牧且耕", "advancements.husbandry.safely_harvest_honey.description": "置一營火於蜂箱之下，以琉璃甁取蜜而不擾蜂。", "advancements.husbandry.safely_harvest_honey.title": "蜂場作戲", "advancements.husbandry.silk_touch_nest.description": "以完璧之術遷三蜂之巢", "advancements.husbandry.silk_touch_nest.title": "原蜂不動", "advancements.husbandry.tactical_fishing.description": "以桶得魚！", "advancements.husbandry.tactical_fishing.title": "緵罟漁獵", "advancements.husbandry.tadpole_in_a_bucket.description": "獲蝌蚪於桶", "advancements.husbandry.tadpole_in_a_bucket.title": "請君入桶", "advancements.husbandry.tame_an_animal.description": "馴一獸", "advancements.husbandry.tame_an_animal.title": "金石之交", "advancements.husbandry.wax_off.description": "以斧削蜜脾於銅塊", "advancements.husbandry.wax_off.title": "除蠟", "advancements.husbandry.wax_on.description": "塗蜜脾於銅塊", "advancements.husbandry.wax_on.title": "塗蠟", "advancements.husbandry.whole_pack.description": "馴全品之狼", "advancements.husbandry.whole_pack.title": "群狼有首", "advancements.nether.all_effects.description": "獲全態效於一時", "advancements.nether.all_effects.title": "神魂顛倒", "advancements.nether.all_potions.description": "獲全劑效於一時", "advancements.nether.all_potions.title": "百味雜陳", "advancements.nether.brew_potion.description": "釀一劑", "advancements.nether.brew_potion.title": "靈丹妙藥", "advancements.nether.charge_respawn_anchor.description": "於一復生錨而充其能至極", "advancements.nether.charge_respawn_anchor.title": "休言九命", "advancements.nether.create_beacon.description": "作而置烽火臺", "advancements.nether.create_beacon.title": "烽蓽生輝", "advancements.nether.create_full_beacon.description": "引烽火臺戾全力", "advancements.nether.create_full_beacon.title": "烽芒畢露", "advancements.nether.distract_piglin.description": "綏豕靈以金", "advancements.nether.distract_piglin.title": "金光大耀", "advancements.nether.explore_nether.description": "覓眾生態域於焱界", "advancements.nether.explore_nether.title": "縱橫熱土", "advancements.nether.fast_travel.description": "假焱界之便，行主界十四里之遙", "advancements.nether.fast_travel.title": "日行千里", "advancements.nether.find_bastion.description": "入砦堡古跡", "advancements.nether.find_bastion.title": "韶光荏苒", "advancements.nether.find_fortress.description": "尋其徑以入焱界府", "advancements.nether.find_fortress.title": "風聲鶴唳", "advancements.nether.get_wither_skull.description": "得凋靈骷髏之首", "advancements.nether.get_wither_skull.title": "骸骨森森", "advancements.nether.loot_bastion.description": "掠貨於砦堡之箱", "advancements.nether.loot_bastion.title": "戰豕", "advancements.nether.netherite_armor.description": "得全套玄鈺甲胄", "advancements.nether.netherite_armor.title": "覆軀以骸", "advancements.nether.obtain_ancient_debris.description": "得上古之骸", "advancements.nether.obtain_ancient_debris.title": "巖棲谷隱", "advancements.nether.obtain_blaze_rod.description": "奪炎靈之桿", "advancements.nether.obtain_blaze_rod.title": "赴湯蹈火", "advancements.nether.obtain_crying_obsidian.description": "得泣黑曜石", "advancements.nether.obtain_crying_obsidian.title": "孰方切胡蔥歟？", "advancements.nether.return_to_sender.description": "以火圓殺魄", "advancements.nether.return_to_sender.title": "以牙還牙", "advancements.nether.ride_strider.description": "持譎蕈釣竿而御熾足獸", "advancements.nether.ride_strider.title": "畫舟添足", "advancements.nether.ride_strider_in_overworld_lava.description": "於主界御熾足獸行熔石湖上，爲途遙遙也", "advancements.nether.ride_strider_in_overworld_lava.title": "賓至如歸", "advancements.nether.root.description": "衣絺葛", "advancements.nether.root.title": "焱界", "advancements.nether.summon_wither.description": "召凋靈", "advancements.nether.summon_wither.title": "西風凋碧樹", "advancements.nether.uneasy_alliance.description": "渡魄出焱界，而後誅之", "advancements.nether.uneasy_alliance.title": "遠交近攻", "advancements.nether.use_lodestone.description": "於礠石使一司南", "advancements.nether.use_lodestone.title": "天涯共此石", "advancements.progress": "%2$s之%1$s", "advancements.sad_label": "嗚呼哀哉！", "advancements.story.cure_zombie_villager.description": "虛而療一屍化鄉民", "advancements.story.cure_zombie_villager.title": "杏林春滿", "advancements.story.deflect_arrow.description": "以盾御彈", "advancements.story.deflect_arrow.title": "此時不候", "advancements.story.enchant_item.description": "以淬靈案施靈於器", "advancements.story.enchant_item.title": "淬靈祖師", "advancements.story.enter_the_end.description": "入終界結界門", "advancements.story.enter_the_end.title": "終焉？", "advancements.story.enter_the_nether.description": "築而入一焱界結界門", "advancements.story.enter_the_nether.title": "業火填膺", "advancements.story.follow_ender_eye.description": "隨終眇眼", "advancements.story.follow_ender_eye.title": "望眼欲穿", "advancements.story.form_obsidian.description": "得一黑曜石", "advancements.story.form_obsidian.title": "冰火凝萃", "advancements.story.iron_tools.description": "昇汝之鎬", "advancements.story.iron_tools.title": "此非鐵鎬邪", "advancements.story.lava_bucket.description": "盈桶以熔石", "advancements.story.lava_bucket.title": "炙手可熱", "advancements.story.mine_diamond.description": "得金剛石", "advancements.story.mine_diamond.title": "金剛石！", "advancements.story.mine_stone.description": "採石以新鎬", "advancements.story.mine_stone.title": "石藝之世", "advancements.story.obtain_armor.description": "披以鐵甲", "advancements.story.obtain_armor.title": "嚴陣以待", "advancements.story.root.description": "戲之中，戲之繹", "advancements.story.root.title": "礦藝", "advancements.story.shiny_gear.description": "金剛甲有保命之神效", "advancements.story.shiny_gear.title": "金剛之軀", "advancements.story.smelt_iron.description": "冶一鐵錠", "advancements.story.smelt_iron.title": "鐵藝", "advancements.story.upgrade_tools.description": "作益善之鎬", "advancements.story.upgrade_tools.title": "更上層樓", "advancements.toast.challenge": "鬥勝！", "advancements.toast.goal": "的達！", "advancements.toast.task": "功成！", "argument.anchor.invalid": "無效實體錨座標%s", "argument.angle.incomplete": "未全（求一角）", "argument.angle.invalid": "是角無效", "argument.block.id.invalid": "未明塊類「%s」", "argument.block.property.duplicate": "「%s」之性但可一用於%s塊耳", "argument.block.property.invalid": "塊%s不許以「%s」爲%s之性", "argument.block.property.novalue": "塊%2$s必有%1$s之性", "argument.block.property.unclosed": "塊態之性當收以 ]", "argument.block.property.unknown": "塊%s無「%s」性", "argument.block.tag.disallowed": "此地不可用籤，但諸實塊耳", "argument.color.invalid": "未明之色「%s」", "argument.component.invalid": "無效信組：%s", "argument.criteria.invalid": "未明之則「%s」", "argument.dimension.invalid": "未明之界「%s」", "argument.double.big": "倍精度浮點數不可逾%s之上界，%s逾之", "argument.double.low": "倍精度浮點數不可逾%s之下界，%s逾之", "argument.entity.invalid": "是實體無效", "argument.entity.notfound.entity": "實體無所獲", "argument.entity.notfound.player": "戲者無所獲", "argument.entity.options.advancements.description": "達事之戲者", "argument.entity.options.distance.description": "去實體之距", "argument.entity.options.distance.negative": "距不得赤", "argument.entity.options.dx.description": "實體之x、x + dx間者", "argument.entity.options.dy.description": "實體之y、y + dy間者", "argument.entity.options.dz.description": "實體之z、z + dz間者", "argument.entity.options.gamemode.description": "戲者之具嬉遊之法", "argument.entity.options.inapplicable": "置設「%s」不可用於此", "argument.entity.options.level.description": "經驗之層", "argument.entity.options.level.negative": "階不當赤", "argument.entity.options.limit.description": "歸實體之頂計", "argument.entity.options.limit.toosmall": "限之少，以一爲極", "argument.entity.options.mode.invalid": "是遊戲之法，或未知，或無效：「%s」", "argument.entity.options.name.description": "實體名", "argument.entity.options.nbt.description": "實體之具NBT", "argument.entity.options.predicate.description": "自定之辝", "argument.entity.options.scores.description": "實體所有之分", "argument.entity.options.sort.description": "序實體者", "argument.entity.options.sort.irreversible": "無效抑未明之序類「%s」", "argument.entity.options.tag.description": "實體之具籤", "argument.entity.options.team.description": "實體之於伍", "argument.entity.options.type.description": "實體之類", "argument.entity.options.type.invalid": "無效抑未明之實體類「%s」", "argument.entity.options.unknown": "未明置設「%s」", "argument.entity.options.unterminated": "選項不備", "argument.entity.options.valueless": "期之值於「%s」之置設", "argument.entity.options.x.description": "經距", "argument.entity.options.x_rotation.description": "經旋", "argument.entity.options.y.description": "縱距", "argument.entity.options.y_rotation.description": "縱旋", "argument.entity.options.z.description": "緯距", "argument.entity.selector.allEntities": "眾實體", "argument.entity.selector.allPlayers": "諸戲者", "argument.entity.selector.missing": "擇類不備", "argument.entity.selector.nearestEntity": "至近之實體", "argument.entity.selector.nearestPlayer": "至邇之戲者", "argument.entity.selector.not_allowed": "擇未可用", "argument.entity.selector.randomPlayer": "無定戲者", "argument.entity.selector.self": "此實體", "argument.entity.selector.unknown": "未知選擇器類「%s」", "argument.entity.toomany": "但容獨實體，然此選擇器容他實體焉", "argument.enum.invalid": "舛值「%s」", "argument.float.big": "單精度浮點數不可逾%s之上界，%s逾之", "argument.float.low": "單精度浮點數不可逾%s之下界，%s逾之", "argument.gamemode.invalid": "未明嬉遊之法：%s", "argument.hexcolor.invalid": "無效十六進數色碼「%s」", "argument.id.invalid": "無效ID", "argument.id.unknown": "未明ID：%s", "argument.integer.big": "整型變量不可逾%s之上界，%s逾之", "argument.integer.low": "整型變量不可逾%s之下界，%s逾之", "argument.item.id.invalid": "是物未知：「%s」", "argument.item.tag.disallowed": "此地不可用籤，但諸實物耳", "argument.literal.incorrect": "欲得言量%s", "argument.long.big": "長整型變量不可逾%s之上界，%s逾之", "argument.long.low": "長整型變量不可逾%s之下界，%s逾之", "argument.message.too_long": "信嫌冗談（以%s逾%s字之限）", "argument.nbt.array.invalid": "無效陣類「%s」", "argument.nbt.array.mixed": "不可置%s於%s", "argument.nbt.expected.compound": "當爲複籤", "argument.nbt.expected.key": "所期之鍵", "argument.nbt.expected.value": "所期之值", "argument.nbt.list.mixed": "不可置%s於%s之列", "argument.nbt.trailing": "尾錄違期", "argument.player.entities": "僅戲者與是命令相容，然選擇器含他物", "argument.player.toomany": "但容獨戲者，然此選擇器容他戲者焉", "argument.player.unknown": "是戲者不可尋", "argument.pos.missing.double": "求之坐標", "argument.pos.missing.int": "應爲一方塊之坐標", "argument.pos.mixed": "世之座標與局部座標不可共存（皆用「^」抑或皆捨之）", "argument.pos.outofbounds": "此地不可及也。", "argument.pos.outofworld": "此位去生界之外！", "argument.pos.unloaded": "其地未讀", "argument.pos2d.incomplete": "未全（應爲二座標）", "argument.pos3d.incomplete": "未全（應爲三座標）", "argument.range.empty": "應爲數抑其限", "argument.range.ints": "僅整數可置於此，小數不可", "argument.range.swapped": "下限勝上限不許也", "argument.resource.invalid_type": "元「%s」之類誤作「%s」（應爲「%s」）", "argument.resource.not_found": "尋元「%2$s」於類「%1$s」未成", "argument.resource_or_id.failed_to_parse": "析築未成：%s", "argument.resource_or_id.invalid": "無效ID抑籤", "argument.resource_or_id.no_such_element": "尋元「%2$s」於名簿「%1$s」未成", "argument.resource_selector.not_found": "選擇器「%s」無「%s」之類相匹", "argument.resource_tag.invalid_type": "籤「%s」之類誤作「%s」（應爲「%s」）", "argument.resource_tag.not_found": "尋籤「%s」於類「%s」未成", "argument.rotation.incomplete": "未全（應爲二座標）", "argument.scoreHolder.empty": "無相應之持分者", "argument.scoreboardDisplaySlot.invalid": "未知之顯域：%s", "argument.style.invalid": "無效之式：%s", "argument.time.invalid_tick_count": "刻數須爲非赤", "argument.time.invalid_unit": "無效單位", "argument.time.tick_count_too_low": "刻計不可逾%s之下界，%s逾之", "argument.uuid.invalid": "無效戶碼", "argument.waypoint.invalid": "所擇實體非驛也", "arguments.block.tag.unknown": "未知之塊簽「%s」", "arguments.function.tag.unknown": "未知之術簽「%s」", "arguments.function.unknown": "是術未知：「%s」", "arguments.item.component.expected": "應爲物元", "arguments.item.component.malformed": "元「%s」有舛：「%s」", "arguments.item.component.repeated": "物元「%s」複，然但可定一值焉", "arguments.item.component.unknown": "未明物元%s", "arguments.item.malformed": "舛物：「%s」", "arguments.item.overstacked": "%s但可疊至%s耳", "arguments.item.predicate.malformed": "辝「%s」有舛：「%s」", "arguments.item.predicate.unknown": "物辝「%s」未明", "arguments.item.tag.unknown": "是物簽未知：「%s」", "arguments.nbtpath.node.invalid": "無效NBT路元", "arguments.nbtpath.nothing_found": "未得合%s之元", "arguments.nbtpath.too_deep": "所成NBT過紊", "arguments.nbtpath.too_large": "所成NBT過大", "arguments.objective.notFound": "未知之計分板項「%s」", "arguments.objective.readonly": "計分板項「%s」惟可閱之", "arguments.operation.div0": "不可除以零", "arguments.operation.invalid": "是操作無效", "arguments.swizzle.invalid": "是綴合無效，應爲「x」「y」「z」之綴合", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "甲胄", "attribute.name.armor_toughness": "甲胄之韌", "attribute.name.attack_damage": "擊力", "attribute.name.attack_knockback": "擊而卻", "attribute.name.attack_speed": "擊之緩急", "attribute.name.block_break_speed": "破塊之緩急", "attribute.name.block_interaction_range": "方及距", "attribute.name.burning_time": "焚時", "attribute.name.camera_distance": "視角之距", "attribute.name.entity_interaction_range": "實體及距", "attribute.name.explosion_knockback_resistance": "禦爆退", "attribute.name.fall_damage_multiplier": "墜傷之倍", "attribute.name.flying_speed": "翔之緩急", "attribute.name.follow_range": "生靈從距", "attribute.name.generic.armor": "甲胄", "attribute.name.generic.armor_toughness": "甲胄之韌", "attribute.name.generic.attack_damage": "擊力", "attribute.name.generic.attack_knockback": "擊而退", "attribute.name.generic.attack_speed": "擊之緩急", "attribute.name.generic.block_interaction_range": "塊方及距", "attribute.name.generic.burning_time": "焚程", "attribute.name.generic.entity_interaction_range": "實體及距", "attribute.name.generic.explosion_knockback_resistance": "禦爆退", "attribute.name.generic.fall_damage_multiplier": "墜傷之倍", "attribute.name.generic.flying_speed": "翔之緩急", "attribute.name.generic.follow_range": "生靈從距", "attribute.name.generic.gravity": "墜力", "attribute.name.generic.jump_strength": "躍力", "attribute.name.generic.knockback_resistance": "禦力", "attribute.name.generic.luck": "幸", "attribute.name.generic.max_absorption": "消災之極", "attribute.name.generic.max_health": "命極", "attribute.name.generic.movement_efficiency": "移速", "attribute.name.generic.movement_speed": "緩急", "attribute.name.generic.oxygen_bonus": "益氣", "attribute.name.generic.safe_fall_distance": "無虞之墜距", "attribute.name.generic.scale": "尺寸", "attribute.name.generic.step_height": "步高", "attribute.name.generic.water_movement_efficiency": "水中移速", "attribute.name.gravity": "墜力", "attribute.name.horse.jump_strength": "馬之躍力", "attribute.name.jump_strength": "躍力", "attribute.name.knockback_resistance": "禦力", "attribute.name.luck": "幸", "attribute.name.max_absorption": "消災之極", "attribute.name.max_health": "命極", "attribute.name.mining_efficiency": "掘速", "attribute.name.movement_efficiency": "移速", "attribute.name.movement_speed": "緩急", "attribute.name.oxygen_bonus": "益氣", "attribute.name.player.block_break_speed": "破塊之緩急", "attribute.name.player.block_interaction_range": "塊方及距", "attribute.name.player.entity_interaction_range": "實體及距", "attribute.name.player.mining_efficiency": "掘速", "attribute.name.player.sneaking_speed": "伏速", "attribute.name.player.submerged_mining_speed": "水中掘速", "attribute.name.player.sweeping_damage_ratio": "橫斬傷率", "attribute.name.safe_fall_distance": "無虞之墜距", "attribute.name.scale": "方寸", "attribute.name.sneaking_speed": "伏之緩急", "attribute.name.spawn_reinforcements": "屍軍", "attribute.name.step_height": "步高", "attribute.name.submerged_mining_speed": "水中掘速", "attribute.name.sweeping_damage_ratio": "橫斬傷率", "attribute.name.tempt_range": "生靈誘距", "attribute.name.water_movement_efficiency": "水中移速", "attribute.name.waypoint_receive_range": "觀驛域", "attribute.name.waypoint_transmit_range": "驛觀域", "attribute.name.zombie.spawn_reinforcements": "屍軍", "biome.minecraft.badlands": "塬", "biome.minecraft.bamboo_jungle": "竹林", "biome.minecraft.basalt_deltas": "黑堦石三角洲", "biome.minecraft.beach": "灘", "biome.minecraft.birch_forest": "樺林", "biome.minecraft.cherry_grove": "櫻林", "biome.minecraft.cold_ocean": "寒海", "biome.minecraft.crimson_forest": "緋蕈林", "biome.minecraft.dark_forest": "烏林", "biome.minecraft.deep_cold_ocean": "深寒海", "biome.minecraft.deep_dark": "黯淵", "biome.minecraft.deep_frozen_ocean": "深凍海", "biome.minecraft.deep_lukewarm_ocean": "深溫海", "biome.minecraft.deep_ocean": "深海", "biome.minecraft.desert": "大漠", "biome.minecraft.dripstone_caves": "鐘乳石穴", "biome.minecraft.end_barrens": "終界礁", "biome.minecraft.end_highlands": "終界坻", "biome.minecraft.end_midlands": "終界漠嶼", "biome.minecraft.eroded_badlands": "劣塬", "biome.minecraft.flower_forest": "繁花林", "biome.minecraft.forest": "林", "biome.minecraft.frozen_ocean": "凍海", "biome.minecraft.frozen_peaks": "凍峰", "biome.minecraft.frozen_river": "凍川", "biome.minecraft.grove": "雪林", "biome.minecraft.ice_spikes": "冰錐原", "biome.minecraft.jagged_peaks": "尖峰", "biome.minecraft.jungle": "叢莽", "biome.minecraft.lukewarm_ocean": "溫海", "biome.minecraft.lush_caves": "葳蕤窟", "biome.minecraft.mangrove_swamp": "沒潮木澤", "biome.minecraft.meadow": "甸", "biome.minecraft.mushroom_fields": "蕈野", "biome.minecraft.nether_wastes": "焱界漠", "biome.minecraft.ocean": "海", "biome.minecraft.old_growth_birch_forest": "古樺林", "biome.minecraft.old_growth_pine_taiga": "古松棘林", "biome.minecraft.old_growth_spruce_taiga": "古樅棘林", "biome.minecraft.pale_garden": "縞苑", "biome.minecraft.plains": "原野", "biome.minecraft.river": "川", "biome.minecraft.savanna": "莽原", "biome.minecraft.savanna_plateau": "莽塬", "biome.minecraft.small_end_islands": "終界嶼", "biome.minecraft.snowy_beach": "雪灘", "biome.minecraft.snowy_plains": "雪原", "biome.minecraft.snowy_slopes": "雪阪", "biome.minecraft.snowy_taiga": "雪棘林", "biome.minecraft.soul_sand_valley": "靈沙谷", "biome.minecraft.sparse_jungle": "疏叢莽", "biome.minecraft.stony_peaks": "石峰", "biome.minecraft.stony_shore": "石岸", "biome.minecraft.sunflower_plains": "葵藿原野", "biome.minecraft.swamp": "澤", "biome.minecraft.taiga": "棘林", "biome.minecraft.the_end": "終界", "biome.minecraft.the_void": "太虛", "biome.minecraft.warm_ocean": "暖海", "biome.minecraft.warped_forest": "譎蕈林", "biome.minecraft.windswept_forest": "當風林", "biome.minecraft.windswept_gravelly_hills": "當風礫丘", "biome.minecraft.windswept_hills": "當風丘", "biome.minecraft.windswept_savanna": "當風莽原", "biome.minecraft.wooded_badlands": "塬林", "block.minecraft.acacia_button": "㭜鈕", "block.minecraft.acacia_door": "㭜門", "block.minecraft.acacia_fence": "㭜檻", "block.minecraft.acacia_fence_gate": "㭜扉", "block.minecraft.acacia_hanging_sign": "㭜懸牌", "block.minecraft.acacia_leaves": "㭜葉", "block.minecraft.acacia_log": "㭜樁", "block.minecraft.acacia_planks": "㭜材", "block.minecraft.acacia_pressure_plate": "㭜踏板", "block.minecraft.acacia_sapling": "㭜秧", "block.minecraft.acacia_sign": "㭜牌", "block.minecraft.acacia_slab": "㭜版", "block.minecraft.acacia_stairs": "㭜階", "block.minecraft.acacia_trapdoor": "㭜窖門", "block.minecraft.acacia_wall_hanging_sign": "壁中㭜懸牌", "block.minecraft.acacia_wall_sign": "壁中㭜牌", "block.minecraft.acacia_wood": "㭜木", "block.minecraft.activator_rail": "激軌", "block.minecraft.air": "氣", "block.minecraft.allium": "碩蔥", "block.minecraft.amethyst_block": "紫水玉塊", "block.minecraft.amethyst_cluster": "紫水玉簇", "block.minecraft.ancient_debris": "上古之骸", "block.minecraft.andesite": "安山石", "block.minecraft.andesite_slab": "安山石板", "block.minecraft.andesite_stairs": "安山石階", "block.minecraft.andesite_wall": "安山石垣", "block.minecraft.anvil": "鐵砧", "block.minecraft.attached_melon_stem": "垂寒瓜莖", "block.minecraft.attached_pumpkin_stem": "垂南瓜莖", "block.minecraft.azalea": "杜鵑", "block.minecraft.azalea_leaves": "杜鵑葉", "block.minecraft.azure_bluet": "藍茜", "block.minecraft.bamboo": "竹", "block.minecraft.bamboo_block": "竹塊", "block.minecraft.bamboo_button": "竹鈕", "block.minecraft.bamboo_door": "竹門", "block.minecraft.bamboo_fence": "竹檻", "block.minecraft.bamboo_fence_gate": "竹扉", "block.minecraft.bamboo_hanging_sign": "竹懸牌", "block.minecraft.bamboo_mosaic": "竹嵌工", "block.minecraft.bamboo_mosaic_slab": "竹嵌工版", "block.minecraft.bamboo_mosaic_stairs": "竹嵌工階", "block.minecraft.bamboo_planks": "竹材", "block.minecraft.bamboo_pressure_plate": "竹踏板", "block.minecraft.bamboo_sapling": "竹筍", "block.minecraft.bamboo_sign": "竹牌", "block.minecraft.bamboo_slab": "竹版", "block.minecraft.bamboo_stairs": "竹階", "block.minecraft.bamboo_trapdoor": "竹窖門", "block.minecraft.bamboo_wall_hanging_sign": "壁中竹懸牌", "block.minecraft.bamboo_wall_sign": "壁中竹牌", "block.minecraft.banner.base.black": "玄底", "block.minecraft.banner.base.blue": "靛底", "block.minecraft.banner.base.brown": "赭底", "block.minecraft.banner.base.cyan": "黛底", "block.minecraft.banner.base.gray": "灰底", "block.minecraft.banner.base.green": "綠底", "block.minecraft.banner.base.light_blue": "縹底", "block.minecraft.banner.base.light_gray": "蒼底", "block.minecraft.banner.base.lime": "翠底", "block.minecraft.banner.base.magenta": "赬底", "block.minecraft.banner.base.orange": "橙底", "block.minecraft.banner.base.pink": "紅底", "block.minecraft.banner.base.purple": "紫底", "block.minecraft.banner.base.red": "赤底", "block.minecraft.banner.base.white": "素底", "block.minecraft.banner.base.yellow": "黃底", "block.minecraft.banner.border.black": "玄匡", "block.minecraft.banner.border.blue": "靛匡", "block.minecraft.banner.border.brown": "褐匡", "block.minecraft.banner.border.cyan": "黛匡", "block.minecraft.banner.border.gray": "灰匡", "block.minecraft.banner.border.green": "綠匡", "block.minecraft.banner.border.light_blue": "縹匡", "block.minecraft.banner.border.light_gray": "蒼匡", "block.minecraft.banner.border.lime": "翠匡", "block.minecraft.banner.border.magenta": "赬匡", "block.minecraft.banner.border.orange": "橙匡", "block.minecraft.banner.border.pink": "紅匡", "block.minecraft.banner.border.purple": "紫匡", "block.minecraft.banner.border.red": "赤匡", "block.minecraft.banner.border.white": "素匡", "block.minecraft.banner.border.yellow": "黃匡", "block.minecraft.banner.bricks.black": "玄磚紋", "block.minecraft.banner.bricks.blue": "靛磚紋", "block.minecraft.banner.bricks.brown": "褐磚紋", "block.minecraft.banner.bricks.cyan": "黛磚紋", "block.minecraft.banner.bricks.gray": "灰磚紋", "block.minecraft.banner.bricks.green": "綠磚紋", "block.minecraft.banner.bricks.light_blue": "縹磚紋", "block.minecraft.banner.bricks.light_gray": "蒼磚紋", "block.minecraft.banner.bricks.lime": "翠磚紋", "block.minecraft.banner.bricks.magenta": "赬磚紋", "block.minecraft.banner.bricks.orange": "橙磚紋", "block.minecraft.banner.bricks.pink": "紅磚紋", "block.minecraft.banner.bricks.purple": "紫磚紋", "block.minecraft.banner.bricks.red": "赤磚紋", "block.minecraft.banner.bricks.white": "素磚紋", "block.minecraft.banner.bricks.yellow": "黃磚紋", "block.minecraft.banner.circle.black": "玄圈紋", "block.minecraft.banner.circle.blue": "靛圈紋", "block.minecraft.banner.circle.brown": "褐圈紋", "block.minecraft.banner.circle.cyan": "黛圈紋", "block.minecraft.banner.circle.gray": "灰圈紋", "block.minecraft.banner.circle.green": "綠圈紋", "block.minecraft.banner.circle.light_blue": "縹圈紋", "block.minecraft.banner.circle.light_gray": "蒼圈紋", "block.minecraft.banner.circle.lime": "翠圈紋", "block.minecraft.banner.circle.magenta": "赬圈紋", "block.minecraft.banner.circle.orange": "橙圈紋", "block.minecraft.banner.circle.pink": "紅圈紋", "block.minecraft.banner.circle.purple": "紫圈紋", "block.minecraft.banner.circle.red": "赤圈紋", "block.minecraft.banner.circle.white": "素圈紋", "block.minecraft.banner.circle.yellow": "黃圈紋", "block.minecraft.banner.creeper.black": "玄伏臨紋", "block.minecraft.banner.creeper.blue": "靛伏臨紋", "block.minecraft.banner.creeper.brown": "褐伏臨紋", "block.minecraft.banner.creeper.cyan": "黛伏臨紋", "block.minecraft.banner.creeper.gray": "灰伏臨紋", "block.minecraft.banner.creeper.green": "綠伏臨紋", "block.minecraft.banner.creeper.light_blue": "縹伏臨紋", "block.minecraft.banner.creeper.light_gray": "蒼伏臨紋", "block.minecraft.banner.creeper.lime": "翠伏臨紋", "block.minecraft.banner.creeper.magenta": "赬伏臨紋", "block.minecraft.banner.creeper.orange": "橙伏臨紋", "block.minecraft.banner.creeper.pink": "紅伏臨紋", "block.minecraft.banner.creeper.purple": "紫伏臨紋", "block.minecraft.banner.creeper.red": "赤伏臨紋", "block.minecraft.banner.creeper.white": "素伏臨紋", "block.minecraft.banner.creeper.yellow": "黃伏臨紋", "block.minecraft.banner.cross.black": "玄阡陌", "block.minecraft.banner.cross.blue": "靛阡陌", "block.minecraft.banner.cross.brown": "褐阡陌", "block.minecraft.banner.cross.cyan": "黛阡陌", "block.minecraft.banner.cross.gray": "灰阡陌", "block.minecraft.banner.cross.green": "綠阡陌", "block.minecraft.banner.cross.light_blue": "縹阡陌", "block.minecraft.banner.cross.light_gray": "蒼阡陌", "block.minecraft.banner.cross.lime": "翠阡陌", "block.minecraft.banner.cross.magenta": "赬阡陌", "block.minecraft.banner.cross.orange": "橙阡陌", "block.minecraft.banner.cross.pink": "紅阡陌", "block.minecraft.banner.cross.purple": "紫阡陌", "block.minecraft.banner.cross.red": "赤阡陌", "block.minecraft.banner.cross.white": "素阡陌", "block.minecraft.banner.cross.yellow": "黃阡陌", "block.minecraft.banner.curly_border.black": "玄齒匡", "block.minecraft.banner.curly_border.blue": "靛齒匡", "block.minecraft.banner.curly_border.brown": "褐齒匡", "block.minecraft.banner.curly_border.cyan": "黛齒匡", "block.minecraft.banner.curly_border.gray": "灰齒匡", "block.minecraft.banner.curly_border.green": "綠齒匡", "block.minecraft.banner.curly_border.light_blue": "縹齒匡", "block.minecraft.banner.curly_border.light_gray": "蒼齒匡", "block.minecraft.banner.curly_border.lime": "翠齒匡", "block.minecraft.banner.curly_border.magenta": "赬齒匡", "block.minecraft.banner.curly_border.orange": "橙齒匡", "block.minecraft.banner.curly_border.pink": "紅齒匡", "block.minecraft.banner.curly_border.purple": "紫齒匡", "block.minecraft.banner.curly_border.red": "赤齒匡", "block.minecraft.banner.curly_border.white": "素齒匡", "block.minecraft.banner.curly_border.yellow": "黃齒匡", "block.minecraft.banner.diagonal_left.black": "右頂玄緞", "block.minecraft.banner.diagonal_left.blue": "右頂靛緞", "block.minecraft.banner.diagonal_left.brown": "右頂褐緞", "block.minecraft.banner.diagonal_left.cyan": "右頂黛緞", "block.minecraft.banner.diagonal_left.gray": "右頂灰緞", "block.minecraft.banner.diagonal_left.green": "右頂綠緞", "block.minecraft.banner.diagonal_left.light_blue": "右頂縹緞", "block.minecraft.banner.diagonal_left.light_gray": "右頂蒼緞", "block.minecraft.banner.diagonal_left.lime": "右頂翠緞", "block.minecraft.banner.diagonal_left.magenta": "右頂赬緞", "block.minecraft.banner.diagonal_left.orange": "右頂橙緞", "block.minecraft.banner.diagonal_left.pink": "右頂紅緞", "block.minecraft.banner.diagonal_left.purple": "右頂紫緞", "block.minecraft.banner.diagonal_left.red": "右頂赤緞", "block.minecraft.banner.diagonal_left.white": "右頂素緞", "block.minecraft.banner.diagonal_left.yellow": "右頂黃緞", "block.minecraft.banner.diagonal_right.black": "左頂玄緞", "block.minecraft.banner.diagonal_right.blue": "左頂靛緞", "block.minecraft.banner.diagonal_right.brown": "左頂褐緞", "block.minecraft.banner.diagonal_right.cyan": "左頂黛緞", "block.minecraft.banner.diagonal_right.gray": "左頂灰緞", "block.minecraft.banner.diagonal_right.green": "左頂綠緞", "block.minecraft.banner.diagonal_right.light_blue": "左頂縹緞", "block.minecraft.banner.diagonal_right.light_gray": "左頂蒼緞", "block.minecraft.banner.diagonal_right.lime": "左頂翠緞", "block.minecraft.banner.diagonal_right.magenta": "左頂赬緞", "block.minecraft.banner.diagonal_right.orange": "左頂橙緞", "block.minecraft.banner.diagonal_right.pink": "左頂紅緞", "block.minecraft.banner.diagonal_right.purple": "左頂紫緞", "block.minecraft.banner.diagonal_right.red": "左頂赤緞", "block.minecraft.banner.diagonal_right.white": "左頂素緞", "block.minecraft.banner.diagonal_right.yellow": "左頂黃緞", "block.minecraft.banner.diagonal_up_left.black": "右底玄緞", "block.minecraft.banner.diagonal_up_left.blue": "右底靛緞", "block.minecraft.banner.diagonal_up_left.brown": "右底褐緞", "block.minecraft.banner.diagonal_up_left.cyan": "右底黛緞", "block.minecraft.banner.diagonal_up_left.gray": "右底灰緞", "block.minecraft.banner.diagonal_up_left.green": "右底綠緞", "block.minecraft.banner.diagonal_up_left.light_blue": "右底縹緞", "block.minecraft.banner.diagonal_up_left.light_gray": "右底蒼緞", "block.minecraft.banner.diagonal_up_left.lime": "右底翠緞", "block.minecraft.banner.diagonal_up_left.magenta": "右底赬緞", "block.minecraft.banner.diagonal_up_left.orange": "右底橙緞", "block.minecraft.banner.diagonal_up_left.pink": "右底紅緞", "block.minecraft.banner.diagonal_up_left.purple": "右底紫緞", "block.minecraft.banner.diagonal_up_left.red": "右底赤緞", "block.minecraft.banner.diagonal_up_left.white": "右底素緞", "block.minecraft.banner.diagonal_up_left.yellow": "右底黃緞", "block.minecraft.banner.diagonal_up_right.black": "左底玄緞", "block.minecraft.banner.diagonal_up_right.blue": "左底靛緞", "block.minecraft.banner.diagonal_up_right.brown": "左底褐緞", "block.minecraft.banner.diagonal_up_right.cyan": "左底黛緞", "block.minecraft.banner.diagonal_up_right.gray": "左底灰緞", "block.minecraft.banner.diagonal_up_right.green": "左底綠緞", "block.minecraft.banner.diagonal_up_right.light_blue": "左底縹緞", "block.minecraft.banner.diagonal_up_right.light_gray": "左底蒼緞", "block.minecraft.banner.diagonal_up_right.lime": "左底翠緞", "block.minecraft.banner.diagonal_up_right.magenta": "左底赬緞", "block.minecraft.banner.diagonal_up_right.orange": "左底橙緞", "block.minecraft.banner.diagonal_up_right.pink": "左底紅緞", "block.minecraft.banner.diagonal_up_right.purple": "左底紫緞", "block.minecraft.banner.diagonal_up_right.red": "左底赤緞", "block.minecraft.banner.diagonal_up_right.white": "左底素緞", "block.minecraft.banner.diagonal_up_right.yellow": "左底黃緞", "block.minecraft.banner.flow.black": "玄氣洄紋", "block.minecraft.banner.flow.blue": "靛氣洄紋", "block.minecraft.banner.flow.brown": "褐氣洄紋", "block.minecraft.banner.flow.cyan": "黛氣洄紋", "block.minecraft.banner.flow.gray": "灰氣洄紋", "block.minecraft.banner.flow.green": "綠氣洄紋", "block.minecraft.banner.flow.light_blue": "縹氣洄紋", "block.minecraft.banner.flow.light_gray": "蒼氣洄紋", "block.minecraft.banner.flow.lime": "翠氣洄紋", "block.minecraft.banner.flow.magenta": "赬氣洄紋", "block.minecraft.banner.flow.orange": "橙氣洄紋", "block.minecraft.banner.flow.pink": "紅氣洄紋", "block.minecraft.banner.flow.purple": "紫氣洄紋", "block.minecraft.banner.flow.red": "赤氣洄紋", "block.minecraft.banner.flow.white": "素氣洄紋", "block.minecraft.banner.flow.yellow": "黃氣洄紋", "block.minecraft.banner.flower.black": "玄花紋", "block.minecraft.banner.flower.blue": "靛花紋", "block.minecraft.banner.flower.brown": "褐花紋", "block.minecraft.banner.flower.cyan": "黛花紋", "block.minecraft.banner.flower.gray": "灰花紋", "block.minecraft.banner.flower.green": "綠花紋", "block.minecraft.banner.flower.light_blue": "縹花紋", "block.minecraft.banner.flower.light_gray": "蒼花紋", "block.minecraft.banner.flower.lime": "翠花紋", "block.minecraft.banner.flower.magenta": "赬花紋", "block.minecraft.banner.flower.orange": "橙花紋", "block.minecraft.banner.flower.pink": "紅花紋", "block.minecraft.banner.flower.purple": "紫花紋", "block.minecraft.banner.flower.red": "赤花紋", "block.minecraft.banner.flower.white": "素花紋", "block.minecraft.banner.flower.yellow": "黃花紋", "block.minecraft.banner.globe.black": "玄坤輿紋", "block.minecraft.banner.globe.blue": "靛坤輿紋", "block.minecraft.banner.globe.brown": "褐坤輿紋", "block.minecraft.banner.globe.cyan": "黛坤輿紋", "block.minecraft.banner.globe.gray": "灰坤輿紋", "block.minecraft.banner.globe.green": "綠坤輿紋", "block.minecraft.banner.globe.light_blue": "縹坤輿紋", "block.minecraft.banner.globe.light_gray": "蒼坤輿紋", "block.minecraft.banner.globe.lime": "翠坤輿紋", "block.minecraft.banner.globe.magenta": "赬坤輿紋", "block.minecraft.banner.globe.orange": "橙坤輿紋", "block.minecraft.banner.globe.pink": "紅坤輿紋", "block.minecraft.banner.globe.purple": "紫坤輿紋", "block.minecraft.banner.globe.red": "赤坤輿紋", "block.minecraft.banner.globe.white": "素坤輿紋", "block.minecraft.banner.globe.yellow": "黃坤輿紋", "block.minecraft.banner.gradient.black": "玄底傾", "block.minecraft.banner.gradient.blue": "靛底傾", "block.minecraft.banner.gradient.brown": "褐底傾", "block.minecraft.banner.gradient.cyan": "黛底傾", "block.minecraft.banner.gradient.gray": "灰底傾", "block.minecraft.banner.gradient.green": "綠底傾", "block.minecraft.banner.gradient.light_blue": "縹底傾", "block.minecraft.banner.gradient.light_gray": "蒼底傾", "block.minecraft.banner.gradient.lime": "翠底傾", "block.minecraft.banner.gradient.magenta": "赬底傾", "block.minecraft.banner.gradient.orange": "橙底傾", "block.minecraft.banner.gradient.pink": "紅底傾", "block.minecraft.banner.gradient.purple": "紫底傾", "block.minecraft.banner.gradient.red": "赤底傾", "block.minecraft.banner.gradient.white": "素底傾", "block.minecraft.banner.gradient.yellow": "黃底傾", "block.minecraft.banner.gradient_up.black": "玄頂傾", "block.minecraft.banner.gradient_up.blue": "靛頂傾", "block.minecraft.banner.gradient_up.brown": "褐頂傾", "block.minecraft.banner.gradient_up.cyan": "黛頂傾", "block.minecraft.banner.gradient_up.gray": "灰頂傾", "block.minecraft.banner.gradient_up.green": "綠頂傾", "block.minecraft.banner.gradient_up.light_blue": "縹頂傾", "block.minecraft.banner.gradient_up.light_gray": "蒼頂傾", "block.minecraft.banner.gradient_up.lime": "翠頂傾", "block.minecraft.banner.gradient_up.magenta": "赬頂傾", "block.minecraft.banner.gradient_up.orange": "橙頂傾", "block.minecraft.banner.gradient_up.pink": "紅頂傾", "block.minecraft.banner.gradient_up.purple": "紫頂傾", "block.minecraft.banner.gradient_up.red": "赤頂傾", "block.minecraft.banner.gradient_up.white": "素頂傾", "block.minecraft.banner.gradient_up.yellow": "黃頂傾", "block.minecraft.banner.guster.black": "玄飆紋", "block.minecraft.banner.guster.blue": "靛飆紋", "block.minecraft.banner.guster.brown": "褐飆紋", "block.minecraft.banner.guster.cyan": "黛飆紋", "block.minecraft.banner.guster.gray": "灰飆紋", "block.minecraft.banner.guster.green": "綠飆紋", "block.minecraft.banner.guster.light_blue": "縹飆紋", "block.minecraft.banner.guster.light_gray": "蒼飆紋", "block.minecraft.banner.guster.lime": "翠飆紋", "block.minecraft.banner.guster.magenta": "赬飆紋", "block.minecraft.banner.guster.orange": "橙飆紋", "block.minecraft.banner.guster.pink": "紅飆紋", "block.minecraft.banner.guster.purple": "紫飆紋", "block.minecraft.banner.guster.red": "赤飆紋", "block.minecraft.banner.guster.white": "素飆紋", "block.minecraft.banner.guster.yellow": "黃飆紋", "block.minecraft.banner.half_horizontal.black": "頂玄緞", "block.minecraft.banner.half_horizontal.blue": "頂靛緞", "block.minecraft.banner.half_horizontal.brown": "頂褐緞", "block.minecraft.banner.half_horizontal.cyan": "頂黛緞", "block.minecraft.banner.half_horizontal.gray": "頂灰緞", "block.minecraft.banner.half_horizontal.green": "頂綠緞", "block.minecraft.banner.half_horizontal.light_blue": "頂縹緞", "block.minecraft.banner.half_horizontal.light_gray": "頂蒼緞", "block.minecraft.banner.half_horizontal.lime": "頂翠緞", "block.minecraft.banner.half_horizontal.magenta": "頂赬緞", "block.minecraft.banner.half_horizontal.orange": "頂橙緞", "block.minecraft.banner.half_horizontal.pink": "頂紅緞", "block.minecraft.banner.half_horizontal.purple": "頂紫緞", "block.minecraft.banner.half_horizontal.red": "頂赤緞", "block.minecraft.banner.half_horizontal.white": "頂素緞", "block.minecraft.banner.half_horizontal.yellow": "頂黃緞", "block.minecraft.banner.half_horizontal_bottom.black": "底玄緞", "block.minecraft.banner.half_horizontal_bottom.blue": "底靛緞", "block.minecraft.banner.half_horizontal_bottom.brown": "底褐緞", "block.minecraft.banner.half_horizontal_bottom.cyan": "底黛緞", "block.minecraft.banner.half_horizontal_bottom.gray": "底灰緞", "block.minecraft.banner.half_horizontal_bottom.green": "底綠緞", "block.minecraft.banner.half_horizontal_bottom.light_blue": "底縹緞", "block.minecraft.banner.half_horizontal_bottom.light_gray": "底蒼緞", "block.minecraft.banner.half_horizontal_bottom.lime": "底翠緞", "block.minecraft.banner.half_horizontal_bottom.magenta": "底赬緞", "block.minecraft.banner.half_horizontal_bottom.orange": "底橙緞", "block.minecraft.banner.half_horizontal_bottom.pink": "底紅緞", "block.minecraft.banner.half_horizontal_bottom.purple": "底紫緞", "block.minecraft.banner.half_horizontal_bottom.red": "底赤緞", "block.minecraft.banner.half_horizontal_bottom.white": "底素緞", "block.minecraft.banner.half_horizontal_bottom.yellow": "底黃緞", "block.minecraft.banner.half_vertical.black": "右玄縱", "block.minecraft.banner.half_vertical.blue": "右靛縱", "block.minecraft.banner.half_vertical.brown": "右褐縱", "block.minecraft.banner.half_vertical.cyan": "右黛縱", "block.minecraft.banner.half_vertical.gray": "右灰縱", "block.minecraft.banner.half_vertical.green": "右綠縱", "block.minecraft.banner.half_vertical.light_blue": "右縹縱", "block.minecraft.banner.half_vertical.light_gray": "右蒼縱", "block.minecraft.banner.half_vertical.lime": "右翠縱", "block.minecraft.banner.half_vertical.magenta": "右赬縱", "block.minecraft.banner.half_vertical.orange": "右橙縱", "block.minecraft.banner.half_vertical.pink": "右紅縱", "block.minecraft.banner.half_vertical.purple": "右紫縱", "block.minecraft.banner.half_vertical.red": "右赤縱", "block.minecraft.banner.half_vertical.white": "右素縱", "block.minecraft.banner.half_vertical.yellow": "右黃縱", "block.minecraft.banner.half_vertical_right.black": "左玄縱", "block.minecraft.banner.half_vertical_right.blue": "左靛縱", "block.minecraft.banner.half_vertical_right.brown": "左褐縱", "block.minecraft.banner.half_vertical_right.cyan": "左黛縱", "block.minecraft.banner.half_vertical_right.gray": "左灰縱", "block.minecraft.banner.half_vertical_right.green": "左綠縱", "block.minecraft.banner.half_vertical_right.light_blue": "左縹縱", "block.minecraft.banner.half_vertical_right.light_gray": "左蒼縱", "block.minecraft.banner.half_vertical_right.lime": "左翠縱", "block.minecraft.banner.half_vertical_right.magenta": "左赬縱", "block.minecraft.banner.half_vertical_right.orange": "左橙縱", "block.minecraft.banner.half_vertical_right.pink": "左紅縱", "block.minecraft.banner.half_vertical_right.purple": "左紫縱", "block.minecraft.banner.half_vertical_right.red": "左赤縱", "block.minecraft.banner.half_vertical_right.white": "左素縱", "block.minecraft.banner.half_vertical_right.yellow": "左黃縱", "block.minecraft.banner.mojang.black": "黑魔贊紋", "block.minecraft.banner.mojang.blue": "靛魔贊紋", "block.minecraft.banner.mojang.brown": "褐魔贊紋", "block.minecraft.banner.mojang.cyan": "黛魔贊紋", "block.minecraft.banner.mojang.gray": "灰魔贊紋", "block.minecraft.banner.mojang.green": "綠魔贊紋", "block.minecraft.banner.mojang.light_blue": "縹魔贊紋", "block.minecraft.banner.mojang.light_gray": "蒼魔贊紋", "block.minecraft.banner.mojang.lime": "翠魔贊紋", "block.minecraft.banner.mojang.magenta": "赬魔贊紋", "block.minecraft.banner.mojang.orange": "橙魔贊紋", "block.minecraft.banner.mojang.pink": "紅魔贊紋", "block.minecraft.banner.mojang.purple": "紫魔贊紋", "block.minecraft.banner.mojang.red": "赤魔贊紋", "block.minecraft.banner.mojang.white": "白魔贊紋", "block.minecraft.banner.mojang.yellow": "黃魔贊紋", "block.minecraft.banner.piglin.black": "玄豕鼻", "block.minecraft.banner.piglin.blue": "靛豕鼻", "block.minecraft.banner.piglin.brown": "赭豕鼻", "block.minecraft.banner.piglin.cyan": "黛豕鼻", "block.minecraft.banner.piglin.gray": "灰豕鼻", "block.minecraft.banner.piglin.green": "綠豕鼻", "block.minecraft.banner.piglin.light_blue": "縹豕鼻", "block.minecraft.banner.piglin.light_gray": "蒼豕鼻", "block.minecraft.banner.piglin.lime": "翠豕鼻", "block.minecraft.banner.piglin.magenta": "赬豕鼻", "block.minecraft.banner.piglin.orange": "橙豕鼻", "block.minecraft.banner.piglin.pink": "紅豕鼻", "block.minecraft.banner.piglin.purple": "紫豕鼻", "block.minecraft.banner.piglin.red": "赤豕鼻", "block.minecraft.banner.piglin.white": "素豕鼻", "block.minecraft.banner.piglin.yellow": "黃豕鼻", "block.minecraft.banner.rhombus.black": "玄菱形", "block.minecraft.banner.rhombus.blue": "靛菱形", "block.minecraft.banner.rhombus.brown": "褐菱形", "block.minecraft.banner.rhombus.cyan": "黛菱形", "block.minecraft.banner.rhombus.gray": "灰菱形", "block.minecraft.banner.rhombus.green": "綠菱形", "block.minecraft.banner.rhombus.light_blue": "縹菱形", "block.minecraft.banner.rhombus.light_gray": "蒼菱形", "block.minecraft.banner.rhombus.lime": "翠菱形", "block.minecraft.banner.rhombus.magenta": "赬菱形", "block.minecraft.banner.rhombus.orange": "橙菱形", "block.minecraft.banner.rhombus.pink": "紅菱形", "block.minecraft.banner.rhombus.purple": "紫菱形", "block.minecraft.banner.rhombus.red": "赤菱形", "block.minecraft.banner.rhombus.white": "素菱形", "block.minecraft.banner.rhombus.yellow": "黃菱形", "block.minecraft.banner.skull.black": "玄髑顱紋", "block.minecraft.banner.skull.blue": "靛髑顱紋", "block.minecraft.banner.skull.brown": "褐髑顱紋", "block.minecraft.banner.skull.cyan": "黛髑顱紋", "block.minecraft.banner.skull.gray": "灰髑顱紋", "block.minecraft.banner.skull.green": "綠髑顱紋", "block.minecraft.banner.skull.light_blue": "縹髑顱紋", "block.minecraft.banner.skull.light_gray": "蒼髑顱紋", "block.minecraft.banner.skull.lime": "翠髑顱紋", "block.minecraft.banner.skull.magenta": "赬髑顱紋", "block.minecraft.banner.skull.orange": "橙髑顱紋", "block.minecraft.banner.skull.pink": "紅髑顱紋", "block.minecraft.banner.skull.purple": "紫髑顱紋", "block.minecraft.banner.skull.red": "赤髑顱紋", "block.minecraft.banner.skull.white": "素髑顱紋", "block.minecraft.banner.skull.yellow": "黃髑顱紋", "block.minecraft.banner.small_stripes.black": "直玄緞", "block.minecraft.banner.small_stripes.blue": "直靛緞", "block.minecraft.banner.small_stripes.brown": "直褐緞", "block.minecraft.banner.small_stripes.cyan": "直黛緞", "block.minecraft.banner.small_stripes.gray": "直灰緞", "block.minecraft.banner.small_stripes.green": "直綠緞", "block.minecraft.banner.small_stripes.light_blue": "直縹緞", "block.minecraft.banner.small_stripes.light_gray": "直蒼緞", "block.minecraft.banner.small_stripes.lime": "直翠緞", "block.minecraft.banner.small_stripes.magenta": "直赬緞", "block.minecraft.banner.small_stripes.orange": "直橙緞", "block.minecraft.banner.small_stripes.pink": "直紅緞", "block.minecraft.banner.small_stripes.purple": "直紫緞", "block.minecraft.banner.small_stripes.red": "直赤緞", "block.minecraft.banner.small_stripes.white": "直素緞", "block.minecraft.banner.small_stripes.yellow": "直黃緞", "block.minecraft.banner.square_bottom_left.black": "右底玄方", "block.minecraft.banner.square_bottom_left.blue": "右底靛方", "block.minecraft.banner.square_bottom_left.brown": "右底褐方", "block.minecraft.banner.square_bottom_left.cyan": "右底黛方", "block.minecraft.banner.square_bottom_left.gray": "右底灰方", "block.minecraft.banner.square_bottom_left.green": "右底綠方", "block.minecraft.banner.square_bottom_left.light_blue": "右底縹方", "block.minecraft.banner.square_bottom_left.light_gray": "右底蒼方", "block.minecraft.banner.square_bottom_left.lime": "右底翠方", "block.minecraft.banner.square_bottom_left.magenta": "右底赬方", "block.minecraft.banner.square_bottom_left.orange": "右底橙方", "block.minecraft.banner.square_bottom_left.pink": "右底紅方", "block.minecraft.banner.square_bottom_left.purple": "右底紫方", "block.minecraft.banner.square_bottom_left.red": "右底赤方", "block.minecraft.banner.square_bottom_left.white": "右底素方", "block.minecraft.banner.square_bottom_left.yellow": "右底黃方", "block.minecraft.banner.square_bottom_right.black": "左底玄方", "block.minecraft.banner.square_bottom_right.blue": "左底靛方", "block.minecraft.banner.square_bottom_right.brown": "左底褐方", "block.minecraft.banner.square_bottom_right.cyan": "左底黛方", "block.minecraft.banner.square_bottom_right.gray": "左底灰方", "block.minecraft.banner.square_bottom_right.green": "左底綠方", "block.minecraft.banner.square_bottom_right.light_blue": "左底縹方", "block.minecraft.banner.square_bottom_right.light_gray": "左底蒼方", "block.minecraft.banner.square_bottom_right.lime": "左底翠方", "block.minecraft.banner.square_bottom_right.magenta": "左底赬方", "block.minecraft.banner.square_bottom_right.orange": "左底橙方", "block.minecraft.banner.square_bottom_right.pink": "左底紅方", "block.minecraft.banner.square_bottom_right.purple": "左底紫方", "block.minecraft.banner.square_bottom_right.red": "左底赤方", "block.minecraft.banner.square_bottom_right.white": "左底素方", "block.minecraft.banner.square_bottom_right.yellow": "左底黃方", "block.minecraft.banner.square_top_left.black": "右頂玄方", "block.minecraft.banner.square_top_left.blue": "右頂靛方", "block.minecraft.banner.square_top_left.brown": "右頂褐方", "block.minecraft.banner.square_top_left.cyan": "右頂黛方", "block.minecraft.banner.square_top_left.gray": "右頂灰方", "block.minecraft.banner.square_top_left.green": "右頂綠方", "block.minecraft.banner.square_top_left.light_blue": "右頂縹方", "block.minecraft.banner.square_top_left.light_gray": "右頂蒼方", "block.minecraft.banner.square_top_left.lime": "右頂翠方", "block.minecraft.banner.square_top_left.magenta": "右頂赬方", "block.minecraft.banner.square_top_left.orange": "右頂橙方", "block.minecraft.banner.square_top_left.pink": "右頂紅方", "block.minecraft.banner.square_top_left.purple": "右頂紫方", "block.minecraft.banner.square_top_left.red": "右頂赤方", "block.minecraft.banner.square_top_left.white": "右頂素方", "block.minecraft.banner.square_top_left.yellow": "右頂黃方", "block.minecraft.banner.square_top_right.black": "左頂玄方", "block.minecraft.banner.square_top_right.blue": "左頂靛方", "block.minecraft.banner.square_top_right.brown": "左頂褐方", "block.minecraft.banner.square_top_right.cyan": "左頂黛方", "block.minecraft.banner.square_top_right.gray": "左頂灰方", "block.minecraft.banner.square_top_right.green": "左頂綠方", "block.minecraft.banner.square_top_right.light_blue": "左頂縹方", "block.minecraft.banner.square_top_right.light_gray": "左頂蒼方", "block.minecraft.banner.square_top_right.lime": "左頂翠方", "block.minecraft.banner.square_top_right.magenta": "左頂赬方", "block.minecraft.banner.square_top_right.orange": "左頂橙方", "block.minecraft.banner.square_top_right.pink": "左頂紅方", "block.minecraft.banner.square_top_right.purple": "左頂紫方", "block.minecraft.banner.square_top_right.red": "左頂赤方", "block.minecraft.banner.square_top_right.white": "左頂素方", "block.minecraft.banner.square_top_right.yellow": "左頂黃方", "block.minecraft.banner.straight_cross.black": "玄錯", "block.minecraft.banner.straight_cross.blue": "靛錯", "block.minecraft.banner.straight_cross.brown": "褐錯", "block.minecraft.banner.straight_cross.cyan": "黛錯", "block.minecraft.banner.straight_cross.gray": "灰錯", "block.minecraft.banner.straight_cross.green": "綠錯", "block.minecraft.banner.straight_cross.light_blue": "縹錯", "block.minecraft.banner.straight_cross.light_gray": "蒼錯", "block.minecraft.banner.straight_cross.lime": "翠錯", "block.minecraft.banner.straight_cross.magenta": "赬錯", "block.minecraft.banner.straight_cross.orange": "橙錯", "block.minecraft.banner.straight_cross.pink": "紅錯", "block.minecraft.banner.straight_cross.purple": "紫錯", "block.minecraft.banner.straight_cross.red": "赤錯", "block.minecraft.banner.straight_cross.white": "素錯", "block.minecraft.banner.straight_cross.yellow": "黃錯", "block.minecraft.banner.stripe_bottom.black": "玄底", "block.minecraft.banner.stripe_bottom.blue": "靛底", "block.minecraft.banner.stripe_bottom.brown": "褐底", "block.minecraft.banner.stripe_bottom.cyan": "黛底", "block.minecraft.banner.stripe_bottom.gray": "灰底", "block.minecraft.banner.stripe_bottom.green": "綠底", "block.minecraft.banner.stripe_bottom.light_blue": "縹底", "block.minecraft.banner.stripe_bottom.light_gray": "蒼底", "block.minecraft.banner.stripe_bottom.lime": "翠底", "block.minecraft.banner.stripe_bottom.magenta": "赬底", "block.minecraft.banner.stripe_bottom.orange": "橙底", "block.minecraft.banner.stripe_bottom.pink": "紅底", "block.minecraft.banner.stripe_bottom.purple": "紫底", "block.minecraft.banner.stripe_bottom.red": "赤底", "block.minecraft.banner.stripe_bottom.white": "素底", "block.minecraft.banner.stripe_bottom.yellow": "黃底", "block.minecraft.banner.stripe_center.black": "央玄縱", "block.minecraft.banner.stripe_center.blue": "央靛縱", "block.minecraft.banner.stripe_center.brown": "央褐縱", "block.minecraft.banner.stripe_center.cyan": "央黛縱", "block.minecraft.banner.stripe_center.gray": "央灰縱", "block.minecraft.banner.stripe_center.green": "央綠縱", "block.minecraft.banner.stripe_center.light_blue": "央縹縱", "block.minecraft.banner.stripe_center.light_gray": "央蒼縱", "block.minecraft.banner.stripe_center.lime": "央翠縱", "block.minecraft.banner.stripe_center.magenta": "央赬縱", "block.minecraft.banner.stripe_center.orange": "央橙縱", "block.minecraft.banner.stripe_center.pink": "央紅縱", "block.minecraft.banner.stripe_center.purple": "央紫縱", "block.minecraft.banner.stripe_center.red": "央赤縱", "block.minecraft.banner.stripe_center.white": "央素緯", "block.minecraft.banner.stripe_center.yellow": "央黃縱", "block.minecraft.banner.stripe_downleft.black": "左玄緞", "block.minecraft.banner.stripe_downleft.blue": "左靛緞", "block.minecraft.banner.stripe_downleft.brown": "左褐緞", "block.minecraft.banner.stripe_downleft.cyan": "左黛緞", "block.minecraft.banner.stripe_downleft.gray": "左灰緞", "block.minecraft.banner.stripe_downleft.green": "左綠緞", "block.minecraft.banner.stripe_downleft.light_blue": "左縹緞", "block.minecraft.banner.stripe_downleft.light_gray": "左蒼緞", "block.minecraft.banner.stripe_downleft.lime": "左翠緞", "block.minecraft.banner.stripe_downleft.magenta": "左赬緞", "block.minecraft.banner.stripe_downleft.orange": "左橙緞", "block.minecraft.banner.stripe_downleft.pink": "左紅緞", "block.minecraft.banner.stripe_downleft.purple": "左紫緞", "block.minecraft.banner.stripe_downleft.red": "左赤緞", "block.minecraft.banner.stripe_downleft.white": "左素緞", "block.minecraft.banner.stripe_downleft.yellow": "左黃緞", "block.minecraft.banner.stripe_downright.black": "右玄緞", "block.minecraft.banner.stripe_downright.blue": "右靛緞", "block.minecraft.banner.stripe_downright.brown": "右褐緞", "block.minecraft.banner.stripe_downright.cyan": "右黛緞", "block.minecraft.banner.stripe_downright.gray": "右灰緞", "block.minecraft.banner.stripe_downright.green": "右綠緞", "block.minecraft.banner.stripe_downright.light_blue": "右縹緞", "block.minecraft.banner.stripe_downright.light_gray": "右蒼緞", "block.minecraft.banner.stripe_downright.lime": "右翠緞", "block.minecraft.banner.stripe_downright.magenta": "右赬緞", "block.minecraft.banner.stripe_downright.orange": "右橙緞", "block.minecraft.banner.stripe_downright.pink": "右紅緞", "block.minecraft.banner.stripe_downright.purple": "右紫緞", "block.minecraft.banner.stripe_downright.red": "右赤緞", "block.minecraft.banner.stripe_downright.white": "右素緞", "block.minecraft.banner.stripe_downright.yellow": "右黃緞", "block.minecraft.banner.stripe_left.black": "右玄", "block.minecraft.banner.stripe_left.blue": "右靛", "block.minecraft.banner.stripe_left.brown": "右褐", "block.minecraft.banner.stripe_left.cyan": "右黛", "block.minecraft.banner.stripe_left.gray": "右灰", "block.minecraft.banner.stripe_left.green": "右綠", "block.minecraft.banner.stripe_left.light_blue": "右縹", "block.minecraft.banner.stripe_left.light_gray": "右蒼", "block.minecraft.banner.stripe_left.lime": "右翠", "block.minecraft.banner.stripe_left.magenta": "右赬", "block.minecraft.banner.stripe_left.orange": "右橙", "block.minecraft.banner.stripe_left.pink": "右紅", "block.minecraft.banner.stripe_left.purple": "右紫", "block.minecraft.banner.stripe_left.red": "右赤", "block.minecraft.banner.stripe_left.white": "右素", "block.minecraft.banner.stripe_left.yellow": "右黃", "block.minecraft.banner.stripe_middle.black": "央玄緯", "block.minecraft.banner.stripe_middle.blue": "央靛緯", "block.minecraft.banner.stripe_middle.brown": "央褐緯", "block.minecraft.banner.stripe_middle.cyan": "央黛緯", "block.minecraft.banner.stripe_middle.gray": "央灰緯", "block.minecraft.banner.stripe_middle.green": "央綠緯", "block.minecraft.banner.stripe_middle.light_blue": "央縹緯", "block.minecraft.banner.stripe_middle.light_gray": "央蒼緯", "block.minecraft.banner.stripe_middle.lime": "央翠緯", "block.minecraft.banner.stripe_middle.magenta": "央赬緯", "block.minecraft.banner.stripe_middle.orange": "央橙緯", "block.minecraft.banner.stripe_middle.pink": "央紅緯", "block.minecraft.banner.stripe_middle.purple": "央紫緯", "block.minecraft.banner.stripe_middle.red": "央赤緯", "block.minecraft.banner.stripe_middle.white": "中素橫條", "block.minecraft.banner.stripe_middle.yellow": "央黃緯", "block.minecraft.banner.stripe_right.black": "左玄縱", "block.minecraft.banner.stripe_right.blue": "左靛縱", "block.minecraft.banner.stripe_right.brown": "左褐縱", "block.minecraft.banner.stripe_right.cyan": "左黛縱", "block.minecraft.banner.stripe_right.gray": "左灰縱", "block.minecraft.banner.stripe_right.green": "左綠縱", "block.minecraft.banner.stripe_right.light_blue": "左縹縱", "block.minecraft.banner.stripe_right.light_gray": "左蒼縱", "block.minecraft.banner.stripe_right.lime": "左翠縱", "block.minecraft.banner.stripe_right.magenta": "左赬縱", "block.minecraft.banner.stripe_right.orange": "左橙縱", "block.minecraft.banner.stripe_right.pink": "左紅縱", "block.minecraft.banner.stripe_right.purple": "左紫縱", "block.minecraft.banner.stripe_right.red": "左赤縱", "block.minecraft.banner.stripe_right.white": "左素縱", "block.minecraft.banner.stripe_right.yellow": "左黃縱", "block.minecraft.banner.stripe_top.black": "玄頂", "block.minecraft.banner.stripe_top.blue": "靛頂", "block.minecraft.banner.stripe_top.brown": "褐頂", "block.minecraft.banner.stripe_top.cyan": "黛頂", "block.minecraft.banner.stripe_top.gray": "灰頂", "block.minecraft.banner.stripe_top.green": "綠頂", "block.minecraft.banner.stripe_top.light_blue": "縹頂", "block.minecraft.banner.stripe_top.light_gray": "蒼頂", "block.minecraft.banner.stripe_top.lime": "翠頂", "block.minecraft.banner.stripe_top.magenta": "赬頂", "block.minecraft.banner.stripe_top.orange": "橙頂", "block.minecraft.banner.stripe_top.pink": "紅頂", "block.minecraft.banner.stripe_top.purple": "紫頂", "block.minecraft.banner.stripe_top.red": "赤頂", "block.minecraft.banner.stripe_top.white": "素頂", "block.minecraft.banner.stripe_top.yellow": "黃頂", "block.minecraft.banner.triangle_bottom.black": "底玄勾股紋", "block.minecraft.banner.triangle_bottom.blue": "底靛勾股紋", "block.minecraft.banner.triangle_bottom.brown": "底褐勾股紋", "block.minecraft.banner.triangle_bottom.cyan": "底黛勾股紋", "block.minecraft.banner.triangle_bottom.gray": "底灰勾股紋", "block.minecraft.banner.triangle_bottom.green": "底綠勾股紋", "block.minecraft.banner.triangle_bottom.light_blue": "底縹勾股紋", "block.minecraft.banner.triangle_bottom.light_gray": "底蒼勾股紋", "block.minecraft.banner.triangle_bottom.lime": "底翠勾股紋", "block.minecraft.banner.triangle_bottom.magenta": "底赬勾股紋", "block.minecraft.banner.triangle_bottom.orange": "底橙勾股紋", "block.minecraft.banner.triangle_bottom.pink": "底紅勾股紋", "block.minecraft.banner.triangle_bottom.purple": "底紫勾股紋", "block.minecraft.banner.triangle_bottom.red": "底赤勾股紋", "block.minecraft.banner.triangle_bottom.white": "底素勾股紋", "block.minecraft.banner.triangle_bottom.yellow": "底黃勾股紋", "block.minecraft.banner.triangle_top.black": "頂玄反勾股紋", "block.minecraft.banner.triangle_top.blue": "頂靛反勾股紋", "block.minecraft.banner.triangle_top.brown": "頂褐反勾股紋", "block.minecraft.banner.triangle_top.cyan": "頂黛反勾股紋", "block.minecraft.banner.triangle_top.gray": "頂灰反勾股紋", "block.minecraft.banner.triangle_top.green": "頂綠反勾股紋", "block.minecraft.banner.triangle_top.light_blue": "頂縹反勾股紋", "block.minecraft.banner.triangle_top.light_gray": "頂蒼反勾股紋", "block.minecraft.banner.triangle_top.lime": "頂翠反勾股紋", "block.minecraft.banner.triangle_top.magenta": "頂赬反勾股紋", "block.minecraft.banner.triangle_top.orange": "頂橙反勾股紋", "block.minecraft.banner.triangle_top.pink": "頂紅反勾股紋", "block.minecraft.banner.triangle_top.purple": "頂紫反勾股紋", "block.minecraft.banner.triangle_top.red": "頂赤反勾股紋", "block.minecraft.banner.triangle_top.white": "頂素反勾股紋", "block.minecraft.banner.triangle_top.yellow": "頂黃反勾股紋", "block.minecraft.banner.triangles_bottom.black": "底玄齒紋", "block.minecraft.banner.triangles_bottom.blue": "底靛齒紋", "block.minecraft.banner.triangles_bottom.brown": "底褐齒紋", "block.minecraft.banner.triangles_bottom.cyan": "底黛齒紋", "block.minecraft.banner.triangles_bottom.gray": "底灰齒紋", "block.minecraft.banner.triangles_bottom.green": "底綠齒紋", "block.minecraft.banner.triangles_bottom.light_blue": "底縹齒紋", "block.minecraft.banner.triangles_bottom.light_gray": "底蒼齒紋", "block.minecraft.banner.triangles_bottom.lime": "底翠齒紋", "block.minecraft.banner.triangles_bottom.magenta": "底赬齒紋", "block.minecraft.banner.triangles_bottom.orange": "底橙齒紋", "block.minecraft.banner.triangles_bottom.pink": "底紅齒紋", "block.minecraft.banner.triangles_bottom.purple": "底紫齒紋", "block.minecraft.banner.triangles_bottom.red": "底赤齒紋", "block.minecraft.banner.triangles_bottom.white": "底素齒紋", "block.minecraft.banner.triangles_bottom.yellow": "底黃齒紋", "block.minecraft.banner.triangles_top.black": "頂玄齒紋", "block.minecraft.banner.triangles_top.blue": "頂靛齒紋", "block.minecraft.banner.triangles_top.brown": "頂褐齒紋", "block.minecraft.banner.triangles_top.cyan": "頂黛齒紋", "block.minecraft.banner.triangles_top.gray": "頂灰齒紋", "block.minecraft.banner.triangles_top.green": "頂綠齒紋", "block.minecraft.banner.triangles_top.light_blue": "頂縹齒紋", "block.minecraft.banner.triangles_top.light_gray": "頂蒼齒紋", "block.minecraft.banner.triangles_top.lime": "頂翠齒紋", "block.minecraft.banner.triangles_top.magenta": "頂赬齒紋", "block.minecraft.banner.triangles_top.orange": "頂橙齒紋", "block.minecraft.banner.triangles_top.pink": "頂紅齒紋", "block.minecraft.banner.triangles_top.purple": "頂紫齒紋", "block.minecraft.banner.triangles_top.red": "頂赤齒紋", "block.minecraft.banner.triangles_top.white": "頂素齒紋", "block.minecraft.banner.triangles_top.yellow": "頂黃齒紋", "block.minecraft.barrel": "木桶", "block.minecraft.barrier": "障", "block.minecraft.basalt": "黑堦石", "block.minecraft.beacon": "烽火臺", "block.minecraft.beacon.primary": "主效", "block.minecraft.beacon.secondary": "副效", "block.minecraft.bed.no_sleep": "汝唯可寢於夜抑雷雨之時", "block.minecraft.bed.not_safe": "怪邇而不可寢", "block.minecraft.bed.obstructed": "是床既阻", "block.minecraft.bed.occupied": "是床既佔", "block.minecraft.bed.too_far_away": "床遠而不可寢", "block.minecraft.bedrock": "基石", "block.minecraft.bee_nest": "蜂巢", "block.minecraft.beehive": "蜂箱", "block.minecraft.beetroots": "甘藜根", "block.minecraft.bell": "鐘", "block.minecraft.big_dripleaf": "碩垂滴葉", "block.minecraft.big_dripleaf_stem": "碩垂滴葉莖", "block.minecraft.birch_button": "樺鈕", "block.minecraft.birch_door": "樺門", "block.minecraft.birch_fence": "樺檻", "block.minecraft.birch_fence_gate": "樺扉", "block.minecraft.birch_hanging_sign": "樺懸牌", "block.minecraft.birch_leaves": "樺葉", "block.minecraft.birch_log": "樺樁", "block.minecraft.birch_planks": "樺材", "block.minecraft.birch_pressure_plate": "樺踏板", "block.minecraft.birch_sapling": "樺秧", "block.minecraft.birch_sign": "樺牌", "block.minecraft.birch_slab": "樺版", "block.minecraft.birch_stairs": "樺階", "block.minecraft.birch_trapdoor": "樺窖門", "block.minecraft.birch_wall_hanging_sign": "壁中樺懸牌", "block.minecraft.birch_wall_sign": "壁中樺牌", "block.minecraft.birch_wood": "樺木", "block.minecraft.black_banner": "玄旗", "block.minecraft.black_bed": "黑床", "block.minecraft.black_candle": "黑燭", "block.minecraft.black_candle_cake": "黑燭之洋糕", "block.minecraft.black_carpet": "黑氍毹", "block.minecraft.black_concrete": "黑砼", "block.minecraft.black_concrete_powder": "黑砼粉", "block.minecraft.black_glazed_terracotta": "黑釉陶", "block.minecraft.black_shulker_box": "黑贆櫝", "block.minecraft.black_stained_glass": "黑琉璃", "block.minecraft.black_stained_glass_pane": "黑琉璃嵌板", "block.minecraft.black_terracotta": "黑陶", "block.minecraft.black_wool": "黑羊毛", "block.minecraft.blackstone": "墨石", "block.minecraft.blackstone_slab": "墨石版", "block.minecraft.blackstone_stairs": "墨石階", "block.minecraft.blackstone_wall": "墨石垣", "block.minecraft.blast_furnace": "冶爐", "block.minecraft.blue_banner": "靛旗", "block.minecraft.blue_bed": "靛床", "block.minecraft.blue_candle": "靛燭", "block.minecraft.blue_candle_cake": "靛燭之洋糕", "block.minecraft.blue_carpet": "靛氍毹", "block.minecraft.blue_concrete": "靛砼", "block.minecraft.blue_concrete_powder": "靛砼粉", "block.minecraft.blue_glazed_terracotta": "靛釉陶", "block.minecraft.blue_ice": "藍冰", "block.minecraft.blue_orchid": "蘭", "block.minecraft.blue_shulker_box": "靛贆櫝", "block.minecraft.blue_stained_glass": "靛琉璃", "block.minecraft.blue_stained_glass_pane": "靛琉璃嵌板", "block.minecraft.blue_terracotta": "靛陶", "block.minecraft.blue_wool": "靛羊毛", "block.minecraft.bone_block": "骨塊", "block.minecraft.bookshelf": "書櫥", "block.minecraft.brain_coral": "腦珊瑚", "block.minecraft.brain_coral_block": "腦珊瑚塊", "block.minecraft.brain_coral_fan": "扇狀腦珊瑚", "block.minecraft.brain_coral_wall_fan": "壁中扇狀腦珊瑚", "block.minecraft.brewing_stand": "煉藥臺", "block.minecraft.brick_slab": "磚版", "block.minecraft.brick_stairs": "磚階", "block.minecraft.brick_wall": "磚垣", "block.minecraft.bricks": "磚塊", "block.minecraft.brown_banner": "褐旗", "block.minecraft.brown_bed": "褐床", "block.minecraft.brown_candle": "褐燭", "block.minecraft.brown_candle_cake": "褐燭之洋糕", "block.minecraft.brown_carpet": "褐氍毹", "block.minecraft.brown_concrete": "褐砼", "block.minecraft.brown_concrete_powder": "褐砼粉", "block.minecraft.brown_glazed_terracotta": "褐釉陶", "block.minecraft.brown_mushroom": "褐蕈", "block.minecraft.brown_mushroom_block": "褐蕈塊", "block.minecraft.brown_shulker_box": "褐贆櫝", "block.minecraft.brown_stained_glass": "褐琉璃", "block.minecraft.brown_stained_glass_pane": "褐琉璃嵌板", "block.minecraft.brown_terracotta": "褐陶", "block.minecraft.brown_wool": "褐羊毛", "block.minecraft.bubble_column": "氣柱", "block.minecraft.bubble_coral": "泡珊瑚", "block.minecraft.bubble_coral_block": "泡珊瑚塊", "block.minecraft.bubble_coral_fan": "扇狀泡珊瑚", "block.minecraft.bubble_coral_wall_fan": "壁中扇狀泡珊瑚", "block.minecraft.budding_amethyst": "蘊芽紫水玉", "block.minecraft.bush": "叢", "block.minecraft.cactus": "仙人掌", "block.minecraft.cactus_flower": "仙人掌花", "block.minecraft.cake": "洋糕", "block.minecraft.calcite": "滑方石", "block.minecraft.calibrated_sculk_sensor": "幽匿校探子", "block.minecraft.campfire": "營火", "block.minecraft.candle": "燭", "block.minecraft.candle_cake": "有燭之洋糕", "block.minecraft.carrots": "胡蘆菔", "block.minecraft.cartography_table": "輿圖案", "block.minecraft.carved_pumpkin": "雕南瓜", "block.minecraft.cauldron": "釜", "block.minecraft.cave_air": "穴氣", "block.minecraft.cave_vines": "穴藤", "block.minecraft.cave_vines_plant": "穴藤株", "block.minecraft.chain": "鏈", "block.minecraft.chain_command_block": "連鎖命令塊", "block.minecraft.cherry_button": "櫻鈕", "block.minecraft.cherry_door": "櫻門", "block.minecraft.cherry_fence": "櫻檻", "block.minecraft.cherry_fence_gate": "櫻扉", "block.minecraft.cherry_hanging_sign": "櫻懸牌", "block.minecraft.cherry_leaves": "櫻葉", "block.minecraft.cherry_log": "櫻樁", "block.minecraft.cherry_planks": "櫻材", "block.minecraft.cherry_pressure_plate": "櫻踏板", "block.minecraft.cherry_sapling": "櫻秧", "block.minecraft.cherry_sign": "櫻牌", "block.minecraft.cherry_slab": "櫻版", "block.minecraft.cherry_stairs": "櫻階", "block.minecraft.cherry_trapdoor": "櫻窖門", "block.minecraft.cherry_wall_hanging_sign": "壁中櫻懸牌", "block.minecraft.cherry_wall_sign": "壁中櫻牌", "block.minecraft.cherry_wood": "櫻木", "block.minecraft.chest": "箱", "block.minecraft.chipped_anvil": "裂鐵砧", "block.minecraft.chiseled_bookshelf": "雕書櫥", "block.minecraft.chiseled_copper": "雕銅塊", "block.minecraft.chiseled_deepslate": "雕板石", "block.minecraft.chiseled_nether_bricks": "雕焱界磚", "block.minecraft.chiseled_polished_blackstone": "雕鎣墨石", "block.minecraft.chiseled_quartz_block": "雕石英塊", "block.minecraft.chiseled_red_sandstone": "雕赤砂", "block.minecraft.chiseled_resin_bricks": "雕樹香磚", "block.minecraft.chiseled_sandstone": "雕砂", "block.minecraft.chiseled_stone_bricks": "雕磚", "block.minecraft.chiseled_tuff": "雕積塊石", "block.minecraft.chiseled_tuff_bricks": "雕積塊石磚", "block.minecraft.chorus_flower": "頌緲花", "block.minecraft.chorus_plant": "頌緲枝", "block.minecraft.clay": "埴塊", "block.minecraft.closed_eyeblossom": "瞑瞳榮", "block.minecraft.coal_block": "石炭塊", "block.minecraft.coal_ore": "石炭礦", "block.minecraft.coarse_dirt": "澱土", "block.minecraft.cobbled_deepslate": "碎板石", "block.minecraft.cobbled_deepslate_slab": "碎板石版", "block.minecraft.cobbled_deepslate_stairs": "碎板石階", "block.minecraft.cobbled_deepslate_wall": "碎板石垣", "block.minecraft.cobblestone": "䃮", "block.minecraft.cobblestone_slab": "䃮版", "block.minecraft.cobblestone_stairs": "䃮階", "block.minecraft.cobblestone_wall": "䃮垣", "block.minecraft.cobweb": "蛛網", "block.minecraft.cocoa": "可可荳", "block.minecraft.command_block": "命令塊", "block.minecraft.comparator": "較赭儀", "block.minecraft.composter": "肥箱", "block.minecraft.conduit": "湧靈核", "block.minecraft.copper_block": "銅塊", "block.minecraft.copper_bulb": "銅燈", "block.minecraft.copper_door": "銅門", "block.minecraft.copper_grate": "銅柵網", "block.minecraft.copper_ore": "銅礦", "block.minecraft.copper_trapdoor": "銅窖門", "block.minecraft.cornflower": "矢車菊", "block.minecraft.cracked_deepslate_bricks": "裂板石磚", "block.minecraft.cracked_deepslate_tiles": "裂板石瓦", "block.minecraft.cracked_nether_bricks": "裂焱界磚", "block.minecraft.cracked_polished_blackstone_bricks": "裂鎣墨石磚", "block.minecraft.cracked_stone_bricks": "裂磚", "block.minecraft.crafter": "製械", "block.minecraft.crafting_table": "製物案", "block.minecraft.creaking_heart": "䦪心", "block.minecraft.creeper_head": "伏臨首", "block.minecraft.creeper_wall_head": "壁中伏臨首", "block.minecraft.crimson_button": "緋蕈木鈕", "block.minecraft.crimson_door": "緋蕈木門", "block.minecraft.crimson_fence": "緋蕈木檻", "block.minecraft.crimson_fence_gate": "緋蕈木扉", "block.minecraft.crimson_fungus": "緋蕈", "block.minecraft.crimson_hanging_sign": "緋蕈木懸牌", "block.minecraft.crimson_hyphae": "緋蕈體", "block.minecraft.crimson_nylium": "緋蕈石", "block.minecraft.crimson_planks": "緋蕈木材", "block.minecraft.crimson_pressure_plate": "緋蕈木踏板", "block.minecraft.crimson_roots": "緋蕈索", "block.minecraft.crimson_sign": "緋蕈木牌", "block.minecraft.crimson_slab": "緋蕈木版", "block.minecraft.crimson_stairs": "緋蕈木階", "block.minecraft.crimson_stem": "緋蕈柄", "block.minecraft.crimson_trapdoor": "緋蕈木窖門", "block.minecraft.crimson_wall_hanging_sign": "壁中緋蕈木懸牌", "block.minecraft.crimson_wall_sign": "壁中緋蕈木牌", "block.minecraft.crying_obsidian": "泣黑曜石", "block.minecraft.cut_copper": "割銅塊", "block.minecraft.cut_copper_slab": "割銅版", "block.minecraft.cut_copper_stairs": "割銅階", "block.minecraft.cut_red_sandstone": "割赤砂", "block.minecraft.cut_red_sandstone_slab": "割赤砂版", "block.minecraft.cut_sandstone": "割砂", "block.minecraft.cut_sandstone_slab": "割砂版", "block.minecraft.cyan_banner": "黛旗", "block.minecraft.cyan_bed": "黛床", "block.minecraft.cyan_candle": "黛燭", "block.minecraft.cyan_candle_cake": "黛燭之洋糕", "block.minecraft.cyan_carpet": "黛氍毹", "block.minecraft.cyan_concrete": "黛砼", "block.minecraft.cyan_concrete_powder": "黛砼粉", "block.minecraft.cyan_glazed_terracotta": "黛釉陶", "block.minecraft.cyan_shulker_box": "黛贆櫝", "block.minecraft.cyan_stained_glass": "黛琉璃", "block.minecraft.cyan_stained_glass_pane": "黛琉璃嵌板", "block.minecraft.cyan_terracotta": "黛陶", "block.minecraft.cyan_wool": "黛羊毛", "block.minecraft.damaged_anvil": "壞鐵砧", "block.minecraft.dandelion": "蒲公英", "block.minecraft.dark_oak_button": "黯柞鈕", "block.minecraft.dark_oak_door": "黯柞門", "block.minecraft.dark_oak_fence": "黯柞檻", "block.minecraft.dark_oak_fence_gate": "黯柞扉", "block.minecraft.dark_oak_hanging_sign": "黯柞懸牌", "block.minecraft.dark_oak_leaves": "黯柞葉", "block.minecraft.dark_oak_log": "黯柞樁", "block.minecraft.dark_oak_planks": "黯柞材", "block.minecraft.dark_oak_pressure_plate": "黯柞踏板", "block.minecraft.dark_oak_sapling": "黯柞秧", "block.minecraft.dark_oak_sign": "黯柞牌", "block.minecraft.dark_oak_slab": "黯柞版", "block.minecraft.dark_oak_stairs": "黯柞階", "block.minecraft.dark_oak_trapdoor": "黯柞窖門", "block.minecraft.dark_oak_wall_hanging_sign": "壁中黯柞懸牌", "block.minecraft.dark_oak_wall_sign": "壁中黯柞牌", "block.minecraft.dark_oak_wood": "黯柞木", "block.minecraft.dark_prismarine": "黯磷", "block.minecraft.dark_prismarine_slab": "黯磷版", "block.minecraft.dark_prismarine_stairs": "黯磷階", "block.minecraft.daylight_detector": "測暉儀", "block.minecraft.dead_brain_coral": "枯腦珊瑚", "block.minecraft.dead_brain_coral_block": "枯腦珊瑚塊", "block.minecraft.dead_brain_coral_fan": "枯扇狀腦珊瑚", "block.minecraft.dead_brain_coral_wall_fan": "壁中枯扇狀腦珊瑚", "block.minecraft.dead_bubble_coral": "枯泡珊瑚", "block.minecraft.dead_bubble_coral_block": "枯泡珊瑚塊", "block.minecraft.dead_bubble_coral_fan": "枯扇狀泡珊瑚", "block.minecraft.dead_bubble_coral_wall_fan": "壁中枯扇狀泡珊瑚", "block.minecraft.dead_bush": "枯木", "block.minecraft.dead_fire_coral": "枯火珊瑚", "block.minecraft.dead_fire_coral_block": "枯火珊瑚塊", "block.minecraft.dead_fire_coral_fan": "枯扇狀火珊瑚", "block.minecraft.dead_fire_coral_wall_fan": "壁中枯扇狀火珊瑚", "block.minecraft.dead_horn_coral": "枯角珊瑚", "block.minecraft.dead_horn_coral_block": "枯角珊瑚塊", "block.minecraft.dead_horn_coral_fan": "枯扇狀角珊瑚", "block.minecraft.dead_horn_coral_wall_fan": "壁中枯扇狀角珊瑚", "block.minecraft.dead_tube_coral": "枯管珊瑚", "block.minecraft.dead_tube_coral_block": "枯管珊瑚塊", "block.minecraft.dead_tube_coral_fan": "枯扇狀管珊瑚", "block.minecraft.dead_tube_coral_wall_fan": "壁中枯扇狀管珊瑚", "block.minecraft.decorated_pot": "飾甕", "block.minecraft.deepslate": "板石", "block.minecraft.deepslate_brick_slab": "板石磚版", "block.minecraft.deepslate_brick_stairs": "板石磚階", "block.minecraft.deepslate_brick_wall": "板石磚垣", "block.minecraft.deepslate_bricks": "板石磚", "block.minecraft.deepslate_coal_ore": "深石炭礦", "block.minecraft.deepslate_copper_ore": "深銅礦", "block.minecraft.deepslate_diamond_ore": "深金剛石礦", "block.minecraft.deepslate_emerald_ore": "深祖母綠礦", "block.minecraft.deepslate_gold_ore": "深金礦", "block.minecraft.deepslate_iron_ore": "深鐵礦", "block.minecraft.deepslate_lapis_ore": "深群青礦", "block.minecraft.deepslate_redstone_ore": "深赤石礦", "block.minecraft.deepslate_tile_slab": "板石瓦版", "block.minecraft.deepslate_tile_stairs": "板石瓦階", "block.minecraft.deepslate_tile_wall": "板石瓦垣", "block.minecraft.deepslate_tiles": "板石瓦", "block.minecraft.detector_rail": "測軌", "block.minecraft.diamond_block": "金剛石塊", "block.minecraft.diamond_ore": "金剛石礦", "block.minecraft.diorite": "閃綠石", "block.minecraft.diorite_slab": "閃綠石版", "block.minecraft.diorite_stairs": "閃綠石階", "block.minecraft.diorite_wall": "閃綠石垣", "block.minecraft.dirt": "土", "block.minecraft.dirt_path": "土徑", "block.minecraft.dispenser": "射械", "block.minecraft.dragon_egg": "龍卵", "block.minecraft.dragon_head": "龍首", "block.minecraft.dragon_wall_head": "壁中龍首", "block.minecraft.dried_ghast": "乾魄", "block.minecraft.dried_kelp_block": "乾海帶塊", "block.minecraft.dripstone_block": "鐘乳石塊", "block.minecraft.dropper": "擲械", "block.minecraft.emerald_block": "祖母綠塊", "block.minecraft.emerald_ore": "祖母綠礦", "block.minecraft.enchanting_table": "淬靈案", "block.minecraft.end_gateway": "終界門關", "block.minecraft.end_portal": "終界結界門", "block.minecraft.end_portal_frame": "終界結界門匡", "block.minecraft.end_rod": "終界燭", "block.minecraft.end_stone": "終界石", "block.minecraft.end_stone_brick_slab": "終界石磚版", "block.minecraft.end_stone_brick_stairs": "終界石磚階", "block.minecraft.end_stone_brick_wall": "終界石磚垣", "block.minecraft.end_stone_bricks": "終界石磚", "block.minecraft.ender_chest": "終眇箱", "block.minecraft.exposed_chiseled_copper": "渡濕雕銅塊", "block.minecraft.exposed_copper": "渡濕銅塊", "block.minecraft.exposed_copper_bulb": "渡濕銅燈", "block.minecraft.exposed_copper_door": "渡濕銅門", "block.minecraft.exposed_copper_grate": "渡濕銅柵網", "block.minecraft.exposed_copper_trapdoor": "渡濕銅窖門", "block.minecraft.exposed_cut_copper": "渡濕割銅塊", "block.minecraft.exposed_cut_copper_slab": "渡濕割銅版", "block.minecraft.exposed_cut_copper_stairs": "渡濕割銅階", "block.minecraft.farmland": "疇", "block.minecraft.fern": "蕨", "block.minecraft.fire": "火", "block.minecraft.fire_coral": "火珊瑚", "block.minecraft.fire_coral_block": "火珊瑚塊", "block.minecraft.fire_coral_fan": "扇狀火珊瑚", "block.minecraft.fire_coral_wall_fan": "壁中扇狀火珊瑚", "block.minecraft.firefly_bush": "螢叢", "block.minecraft.fletching_table": "製箭案", "block.minecraft.flower_pot": "盆", "block.minecraft.flowering_azalea": "芳杜鵑", "block.minecraft.flowering_azalea_leaves": "芳杜鵑葉", "block.minecraft.frogspawn": "鼃子", "block.minecraft.frosted_ice": "霜冰", "block.minecraft.furnace": "爐", "block.minecraft.gilded_blackstone": "嵌金之墨石", "block.minecraft.glass": "琉璃", "block.minecraft.glass_pane": "琉璃嵌板", "block.minecraft.glow_lichen": "地踏爍菰", "block.minecraft.glowstone": "硄", "block.minecraft.gold_block": "金塊", "block.minecraft.gold_ore": "金礦", "block.minecraft.granite": "花崗石", "block.minecraft.granite_slab": "花崗石版", "block.minecraft.granite_stairs": "花崗石階", "block.minecraft.granite_wall": "花崗石垣", "block.minecraft.grass": "艸", "block.minecraft.grass_block": "艸方", "block.minecraft.gravel": "礫", "block.minecraft.gray_banner": "灰旗", "block.minecraft.gray_bed": "灰床", "block.minecraft.gray_candle": "灰燭", "block.minecraft.gray_candle_cake": "灰燭之洋糕", "block.minecraft.gray_carpet": "灰氍毹", "block.minecraft.gray_concrete": "灰砼", "block.minecraft.gray_concrete_powder": "灰砼粉", "block.minecraft.gray_glazed_terracotta": "灰釉陶", "block.minecraft.gray_shulker_box": "灰贆櫝", "block.minecraft.gray_stained_glass": "灰琉璃", "block.minecraft.gray_stained_glass_pane": "灰琉璃嵌板", "block.minecraft.gray_terracotta": "灰陶", "block.minecraft.gray_wool": "灰羊毛", "block.minecraft.green_banner": "綠旗", "block.minecraft.green_bed": "綠床", "block.minecraft.green_candle": "綠燭", "block.minecraft.green_candle_cake": "綠燭之洋糕", "block.minecraft.green_carpet": "綠氍毹", "block.minecraft.green_concrete": "綠砼", "block.minecraft.green_concrete_powder": "綠砼粉", "block.minecraft.green_glazed_terracotta": "綠釉陶", "block.minecraft.green_shulker_box": "綠贆櫝", "block.minecraft.green_stained_glass": "綠琉璃", "block.minecraft.green_stained_glass_pane": "綠琉璃嵌板", "block.minecraft.green_terracotta": "綠陶", "block.minecraft.green_wool": "綠羊毛", "block.minecraft.grindstone": "礪", "block.minecraft.hanging_roots": "懸根", "block.minecraft.hay_block": "芻束", "block.minecraft.heavy_core": "重核", "block.minecraft.heavy_weighted_pressure_plate": "重踏板", "block.minecraft.honey_block": "蜜塊", "block.minecraft.honeycomb_block": "蜜脾塊", "block.minecraft.hopper": "漏斗", "block.minecraft.horn_coral": "角珊瑚", "block.minecraft.horn_coral_block": "角珊瑚塊", "block.minecraft.horn_coral_fan": "扇狀角珊瑚", "block.minecraft.horn_coral_wall_fan": "壁中扇狀角珊瑚", "block.minecraft.ice": "冰", "block.minecraft.infested_chiseled_stone_bricks": "蟫蝕雕磚", "block.minecraft.infested_cobblestone": "蟫蝕䃮", "block.minecraft.infested_cracked_stone_bricks": "蟫蝕裂磚", "block.minecraft.infested_deepslate": "蟫蝕板石", "block.minecraft.infested_mossy_stone_bricks": "蟫蝕苔磚", "block.minecraft.infested_stone": "蟫蝕石", "block.minecraft.infested_stone_bricks": "蟫蝕石磚", "block.minecraft.iron_bars": "鐵檻", "block.minecraft.iron_block": "鐵塊", "block.minecraft.iron_door": "鐵門", "block.minecraft.iron_ore": "鐵礦", "block.minecraft.iron_trapdoor": "鐵窖門", "block.minecraft.jack_o_lantern": "南瓜燈", "block.minecraft.jigsaw": "榫卯塊", "block.minecraft.jukebox": "留聲機", "block.minecraft.jungle_button": "叢莽鈕", "block.minecraft.jungle_door": "叢莽門", "block.minecraft.jungle_fence": "叢莽檻", "block.minecraft.jungle_fence_gate": "叢莽扉", "block.minecraft.jungle_hanging_sign": "叢莽懸牌", "block.minecraft.jungle_leaves": "叢莽葉", "block.minecraft.jungle_log": "叢莽樁", "block.minecraft.jungle_planks": "叢莽材", "block.minecraft.jungle_pressure_plate": "叢莽踏板", "block.minecraft.jungle_sapling": "叢莽秧", "block.minecraft.jungle_sign": "叢莽牌", "block.minecraft.jungle_slab": "叢莽版", "block.minecraft.jungle_stairs": "叢莽階", "block.minecraft.jungle_trapdoor": "叢莽窖門", "block.minecraft.jungle_wall_hanging_sign": "壁中叢莽懸牌", "block.minecraft.jungle_wall_sign": "壁中叢莽牌", "block.minecraft.jungle_wood": "叢莽木", "block.minecraft.kelp": "海帶", "block.minecraft.kelp_plant": "海帶株", "block.minecraft.ladder": "梯", "block.minecraft.lantern": "燈籠", "block.minecraft.lapis_block": "群青塊", "block.minecraft.lapis_ore": "群青礦", "block.minecraft.large_amethyst_bud": "大紫水玉苗", "block.minecraft.large_fern": "大蕨", "block.minecraft.lava": "熔石", "block.minecraft.lava_cauldron": "瀦熔石之釜", "block.minecraft.leaf_litter": "枯葉", "block.minecraft.lectern": "書檯", "block.minecraft.lever": "閘刀", "block.minecraft.light": "光", "block.minecraft.light_blue_banner": "縹旗", "block.minecraft.light_blue_bed": "縹床", "block.minecraft.light_blue_candle": "縹燭", "block.minecraft.light_blue_candle_cake": "縹燭之洋糕", "block.minecraft.light_blue_carpet": "縹氍毹", "block.minecraft.light_blue_concrete": "縹砼", "block.minecraft.light_blue_concrete_powder": "縹砼粉", "block.minecraft.light_blue_glazed_terracotta": "縹釉陶", "block.minecraft.light_blue_shulker_box": "縹贆櫝", "block.minecraft.light_blue_stained_glass": "縹琉璃", "block.minecraft.light_blue_stained_glass_pane": "縹琉璃嵌板", "block.minecraft.light_blue_terracotta": "縹陶", "block.minecraft.light_blue_wool": "縹羊毛", "block.minecraft.light_gray_banner": "蒼旗", "block.minecraft.light_gray_bed": "蒼床", "block.minecraft.light_gray_candle": "蒼燭", "block.minecraft.light_gray_candle_cake": "蒼燭之洋糕", "block.minecraft.light_gray_carpet": "蒼氍毹", "block.minecraft.light_gray_concrete": "蒼砼", "block.minecraft.light_gray_concrete_powder": "蒼砼粉", "block.minecraft.light_gray_glazed_terracotta": "蒼釉陶", "block.minecraft.light_gray_shulker_box": "蒼贆櫝", "block.minecraft.light_gray_stained_glass": "蒼琉璃", "block.minecraft.light_gray_stained_glass_pane": "蒼琉璃嵌板", "block.minecraft.light_gray_terracotta": "蒼陶", "block.minecraft.light_gray_wool": "蒼羊毛", "block.minecraft.light_weighted_pressure_plate": "輕踏板", "block.minecraft.lightning_rod": "引雷桿", "block.minecraft.lilac": "丁香", "block.minecraft.lily_of_the_valley": "鈴蘭", "block.minecraft.lily_pad": "蓮葉", "block.minecraft.lime_banner": "翠旗", "block.minecraft.lime_bed": "翠床", "block.minecraft.lime_candle": "翠燭", "block.minecraft.lime_candle_cake": "翠燭之洋糕", "block.minecraft.lime_carpet": "翠氍毹", "block.minecraft.lime_concrete": "翠砼", "block.minecraft.lime_concrete_powder": "翠砼粉", "block.minecraft.lime_glazed_terracotta": "翠釉陶", "block.minecraft.lime_shulker_box": "翠贆櫝", "block.minecraft.lime_stained_glass": "翠琉璃", "block.minecraft.lime_stained_glass_pane": "翠琉璃嵌板", "block.minecraft.lime_terracotta": "翠陶", "block.minecraft.lime_wool": "翠羊毛", "block.minecraft.lodestone": "礠石", "block.minecraft.loom": "機杼", "block.minecraft.magenta_banner": "赬旗", "block.minecraft.magenta_bed": "赬床", "block.minecraft.magenta_candle": "赬燭", "block.minecraft.magenta_candle_cake": "赬燭之洋糕", "block.minecraft.magenta_carpet": "赬氍毹", "block.minecraft.magenta_concrete": "赬砼", "block.minecraft.magenta_concrete_powder": "赬砼粉", "block.minecraft.magenta_glazed_terracotta": "赬釉陶", "block.minecraft.magenta_shulker_box": "赬贆櫝", "block.minecraft.magenta_stained_glass": "赬琉璃", "block.minecraft.magenta_stained_glass_pane": "赬琉璃嵌板", "block.minecraft.magenta_terracotta": "赬陶", "block.minecraft.magenta_wool": "赬羊毛", "block.minecraft.magma_block": "火漿塊", "block.minecraft.mangrove_button": "沒潮木鈕", "block.minecraft.mangrove_door": "沒潮木門", "block.minecraft.mangrove_fence": "沒潮木檻", "block.minecraft.mangrove_fence_gate": "沒潮木扉", "block.minecraft.mangrove_hanging_sign": "沒潮木懸牌", "block.minecraft.mangrove_leaves": "沒潮木葉", "block.minecraft.mangrove_log": "沒潮木樁", "block.minecraft.mangrove_planks": "沒潮木材", "block.minecraft.mangrove_pressure_plate": "沒潮木踏板", "block.minecraft.mangrove_propagule": "沒潮木胚", "block.minecraft.mangrove_roots": "沒潮木根", "block.minecraft.mangrove_sign": "沒潮木牌", "block.minecraft.mangrove_slab": "沒潮木版", "block.minecraft.mangrove_stairs": "沒潮木階", "block.minecraft.mangrove_trapdoor": "沒潮木窖門", "block.minecraft.mangrove_wall_hanging_sign": "壁中沒潮木懸牌", "block.minecraft.mangrove_wall_sign": "壁中沒潮木牌", "block.minecraft.mangrove_wood": "沒潮木", "block.minecraft.medium_amethyst_bud": "中紫水玉苗", "block.minecraft.melon": "寒瓜", "block.minecraft.melon_stem": "寒瓜莖", "block.minecraft.moss_block": "蘚塊", "block.minecraft.moss_carpet": "地蘚", "block.minecraft.mossy_cobblestone": "苔䃮", "block.minecraft.mossy_cobblestone_slab": "苔䃮版", "block.minecraft.mossy_cobblestone_stairs": "苔䃮階", "block.minecraft.mossy_cobblestone_wall": "苔䃮垣", "block.minecraft.mossy_stone_brick_slab": "苔磚版", "block.minecraft.mossy_stone_brick_stairs": "苔磚階", "block.minecraft.mossy_stone_brick_wall": "苔磚垣", "block.minecraft.mossy_stone_bricks": "苔磚", "block.minecraft.moving_piston": "移之鞲鞴", "block.minecraft.mud": "泥", "block.minecraft.mud_brick_slab": "泥磚版", "block.minecraft.mud_brick_stairs": "泥磚階", "block.minecraft.mud_brick_wall": "泥磚垣", "block.minecraft.mud_bricks": "泥磚", "block.minecraft.muddy_mangrove_roots": "泥沒潮木根", "block.minecraft.mushroom_stem": "蕈莖", "block.minecraft.mycelium": "盤菌土", "block.minecraft.nether_brick_fence": "焱界磚檻", "block.minecraft.nether_brick_slab": "焱界磚版", "block.minecraft.nether_brick_stairs": "焱界磚階", "block.minecraft.nether_brick_wall": "焱界磚垣", "block.minecraft.nether_bricks": "焱界磚塊", "block.minecraft.nether_gold_ore": "焱界金礦", "block.minecraft.nether_portal": "焱界結界門", "block.minecraft.nether_quartz_ore": "石英礦", "block.minecraft.nether_sprouts": "焱界苗", "block.minecraft.nether_wart": "焱界疣", "block.minecraft.nether_wart_block": "焱界疣塊", "block.minecraft.netherite_block": "玄鈺塊", "block.minecraft.netherrack": "焱界石", "block.minecraft.note_block": "絲竹匣", "block.minecraft.oak_button": "柞鈕", "block.minecraft.oak_door": "柞門", "block.minecraft.oak_fence": "柞檻", "block.minecraft.oak_fence_gate": "柞扉", "block.minecraft.oak_hanging_sign": "柞懸牌", "block.minecraft.oak_leaves": "柞葉", "block.minecraft.oak_log": "柞樁", "block.minecraft.oak_planks": "柞材", "block.minecraft.oak_pressure_plate": "柞踏板", "block.minecraft.oak_sapling": "柞秧", "block.minecraft.oak_sign": "柞牌", "block.minecraft.oak_slab": "柞版", "block.minecraft.oak_stairs": "柞階", "block.minecraft.oak_trapdoor": "柞窖門", "block.minecraft.oak_wall_hanging_sign": "壁中柞懸牌", "block.minecraft.oak_wall_sign": "壁中柞牌", "block.minecraft.oak_wood": "柞木", "block.minecraft.observer": "探械", "block.minecraft.obsidian": "黑曜石", "block.minecraft.ochre_froglight": "赭鼃光", "block.minecraft.ominous_banner": "厄軍旗麾", "block.minecraft.open_eyeblossom": "盱瞳榮", "block.minecraft.orange_banner": "橙旗", "block.minecraft.orange_bed": "橙床", "block.minecraft.orange_candle": "橙燭", "block.minecraft.orange_candle_cake": "橙燭之洋糕", "block.minecraft.orange_carpet": "橙氍毹", "block.minecraft.orange_concrete": "橙砼", "block.minecraft.orange_concrete_powder": "橙砼粉", "block.minecraft.orange_glazed_terracotta": "橙釉陶", "block.minecraft.orange_shulker_box": "橙贆櫝", "block.minecraft.orange_stained_glass": "橙琉璃", "block.minecraft.orange_stained_glass_pane": "橙琉璃嵌板", "block.minecraft.orange_terracotta": "橙陶", "block.minecraft.orange_tulip": "橙鬱金香", "block.minecraft.orange_wool": "橙羊毛", "block.minecraft.oxeye_daisy": "雛菊", "block.minecraft.oxidized_chiseled_copper": "風蝕雕銅塊", "block.minecraft.oxidized_copper": "風蝕銅塊", "block.minecraft.oxidized_copper_bulb": "風蝕銅燈", "block.minecraft.oxidized_copper_door": "風蝕銅門", "block.minecraft.oxidized_copper_grate": "風蝕銅柵網", "block.minecraft.oxidized_copper_trapdoor": "風蝕銅窖門", "block.minecraft.oxidized_cut_copper": "風蝕割銅塊", "block.minecraft.oxidized_cut_copper_slab": "風蝕割銅版", "block.minecraft.oxidized_cut_copper_stairs": "風蝕割銅階", "block.minecraft.packed_ice": "夯冰", "block.minecraft.packed_mud": "夯泥", "block.minecraft.pale_hanging_moss": "縞懸蘚", "block.minecraft.pale_moss_block": "縞蘚塊", "block.minecraft.pale_moss_carpet": "縞地蘚", "block.minecraft.pale_oak_button": "縞柞鈕", "block.minecraft.pale_oak_door": "縞柞門", "block.minecraft.pale_oak_fence": "縞柞檻", "block.minecraft.pale_oak_fence_gate": "縞柞扉", "block.minecraft.pale_oak_hanging_sign": "縞柞懸牌", "block.minecraft.pale_oak_leaves": "縞柞葉", "block.minecraft.pale_oak_log": "縞柞樁", "block.minecraft.pale_oak_planks": "縞柞材", "block.minecraft.pale_oak_pressure_plate": "縞柞踏板", "block.minecraft.pale_oak_sapling": "縞柞秧", "block.minecraft.pale_oak_sign": "縞柞牌", "block.minecraft.pale_oak_slab": "縞柞版", "block.minecraft.pale_oak_stairs": "縞柞階", "block.minecraft.pale_oak_trapdoor": "縞柞窖門", "block.minecraft.pale_oak_wall_hanging_sign": "壁中縞柞懸牌", "block.minecraft.pale_oak_wall_sign": "壁中縞柞牌", "block.minecraft.pale_oak_wood": "縞柞木", "block.minecraft.pearlescent_froglight": "玢璘鼃光", "block.minecraft.peony": "牡丹", "block.minecraft.petrified_oak_slab": "石化柞版", "block.minecraft.piglin_head": "豕靈首", "block.minecraft.piglin_wall_head": "壁中豕靈首", "block.minecraft.pink_banner": "紅旗", "block.minecraft.pink_bed": "紅床", "block.minecraft.pink_candle": "紅燭", "block.minecraft.pink_candle_cake": "紅燭之洋糕", "block.minecraft.pink_carpet": "紅氍毹", "block.minecraft.pink_concrete": "紅砼", "block.minecraft.pink_concrete_powder": "紅砼粉", "block.minecraft.pink_glazed_terracotta": "紅釉陶", "block.minecraft.pink_petals": "落英", "block.minecraft.pink_shulker_box": "紅贆櫝", "block.minecraft.pink_stained_glass": "紅琉璃", "block.minecraft.pink_stained_glass_pane": "紅琉璃嵌板", "block.minecraft.pink_terracotta": "紅陶", "block.minecraft.pink_tulip": "紅鬱金香", "block.minecraft.pink_wool": "紅羊毛", "block.minecraft.piston": "鞲鞴", "block.minecraft.piston_head": "鞲鞴之首", "block.minecraft.pitcher_crop": "小人蘭株", "block.minecraft.pitcher_plant": "小人蘭", "block.minecraft.player_head": "戲者之首", "block.minecraft.player_head.named": "%s之首", "block.minecraft.player_wall_head": "壁中戲者之首", "block.minecraft.podzol": "灰土", "block.minecraft.pointed_dripstone": "鐘乳石", "block.minecraft.polished_andesite": "鎣安山石", "block.minecraft.polished_andesite_slab": "鎣安山石版", "block.minecraft.polished_andesite_stairs": "鎣安山石階", "block.minecraft.polished_basalt": "鎣黑堦石", "block.minecraft.polished_blackstone": "鎣墨石", "block.minecraft.polished_blackstone_brick_slab": "鎣墨石磚版", "block.minecraft.polished_blackstone_brick_stairs": "鎣墨石磚階", "block.minecraft.polished_blackstone_brick_wall": "鎣墨石磚垣", "block.minecraft.polished_blackstone_bricks": "鎣墨石磚", "block.minecraft.polished_blackstone_button": "鎣墨石鈕", "block.minecraft.polished_blackstone_pressure_plate": "鎣墨石踏板", "block.minecraft.polished_blackstone_slab": "鎣墨石版", "block.minecraft.polished_blackstone_stairs": "鎣墨石階", "block.minecraft.polished_blackstone_wall": "鎣墨石垣", "block.minecraft.polished_deepslate": "鎣板石", "block.minecraft.polished_deepslate_slab": "鎣板石版", "block.minecraft.polished_deepslate_stairs": "鎣板石階", "block.minecraft.polished_deepslate_wall": "鎣板石垣", "block.minecraft.polished_diorite": "鎣閃綠石", "block.minecraft.polished_diorite_slab": "鎣閃綠石版", "block.minecraft.polished_diorite_stairs": "鎣閃綠石階", "block.minecraft.polished_granite": "鎣花崗石", "block.minecraft.polished_granite_slab": "鎣花崗石版", "block.minecraft.polished_granite_stairs": "鎣花崗石階", "block.minecraft.polished_tuff": "鎣積塊石", "block.minecraft.polished_tuff_slab": "鎣積塊石版", "block.minecraft.polished_tuff_stairs": "鎣積塊石階", "block.minecraft.polished_tuff_wall": "鎣積塊垣", "block.minecraft.poppy": "虞美人", "block.minecraft.potatoes": "洋芋", "block.minecraft.potted_acacia_sapling": "㭜秧盆景", "block.minecraft.potted_allium": "碩蔥盆景", "block.minecraft.potted_azalea_bush": "杜鵑盆景", "block.minecraft.potted_azure_bluet": "藍茜盆景", "block.minecraft.potted_bamboo": "竹盆景", "block.minecraft.potted_birch_sapling": "樺秧盆景", "block.minecraft.potted_blue_orchid": "蘭盆景", "block.minecraft.potted_brown_mushroom": "褐蕈盆景", "block.minecraft.potted_cactus": "仙人掌盆景", "block.minecraft.potted_cherry_sapling": "櫻秧盆景", "block.minecraft.potted_closed_eyeblossom": "瞑瞳榮盆景", "block.minecraft.potted_cornflower": "矢車菊盆景", "block.minecraft.potted_crimson_fungus": "緋蕈盆景", "block.minecraft.potted_crimson_roots": "緋蕈索盆景", "block.minecraft.potted_dandelion": "蒲公英盆景", "block.minecraft.potted_dark_oak_sapling": "黯柞秧盆景", "block.minecraft.potted_dead_bush": "枯木盆景", "block.minecraft.potted_fern": "蕨盆景", "block.minecraft.potted_flowering_azalea_bush": "芳杜鵑盆景", "block.minecraft.potted_jungle_sapling": "叢莽秧盆景", "block.minecraft.potted_lily_of_the_valley": "鈴蘭盆景", "block.minecraft.potted_mangrove_propagule": "沒潮木胚盆景", "block.minecraft.potted_oak_sapling": "柞秧盆景", "block.minecraft.potted_open_eyeblossom": "盱瞳榮盆景", "block.minecraft.potted_orange_tulip": "橙鬱金香盆景", "block.minecraft.potted_oxeye_daisy": "雛菊盆景", "block.minecraft.potted_pale_oak_sapling": "縞柞秧盆景", "block.minecraft.potted_pink_tulip": "紅鬱金香盆景", "block.minecraft.potted_poppy": "虞美人盆景", "block.minecraft.potted_red_mushroom": "赤蕈盆景", "block.minecraft.potted_red_tulip": "赤鬱金香盆景", "block.minecraft.potted_spruce_sapling": "樅秧盆景", "block.minecraft.potted_torchflower": "炬蓮盆景", "block.minecraft.potted_warped_fungus": "譎蕈盆景", "block.minecraft.potted_warped_roots": "譎蕈索盆景", "block.minecraft.potted_white_tulip": "白鬱金香盆景", "block.minecraft.potted_wither_rose": "凋靈玫瑰盆景", "block.minecraft.powder_snow": "齏雪", "block.minecraft.powder_snow_cauldron": "瀦齏雪之釜", "block.minecraft.powered_rail": "速軌", "block.minecraft.prismarine": "海磷", "block.minecraft.prismarine_brick_slab": "海磷磚版", "block.minecraft.prismarine_brick_stairs": "海磷磚階", "block.minecraft.prismarine_bricks": "海磷磚", "block.minecraft.prismarine_slab": "海磷版", "block.minecraft.prismarine_stairs": "海磷階", "block.minecraft.prismarine_wall": "海磷垣", "block.minecraft.pumpkin": "南瓜", "block.minecraft.pumpkin_stem": "南瓜莖", "block.minecraft.purple_banner": "紫旗", "block.minecraft.purple_bed": "紫床", "block.minecraft.purple_candle": "紫燭", "block.minecraft.purple_candle_cake": "紫燭之洋糕", "block.minecraft.purple_carpet": "紫氍毹", "block.minecraft.purple_concrete": "紫砼", "block.minecraft.purple_concrete_powder": "紫砼粉", "block.minecraft.purple_glazed_terracotta": "紫釉陶", "block.minecraft.purple_shulker_box": "紫贆櫝", "block.minecraft.purple_stained_glass": "紫琉璃", "block.minecraft.purple_stained_glass_pane": "紫琉璃嵌板", "block.minecraft.purple_terracotta": "紫陶", "block.minecraft.purple_wool": "紫羊毛", "block.minecraft.purpur_block": "紫珀塊", "block.minecraft.purpur_pillar": "紫珀柱", "block.minecraft.purpur_slab": "紫珀版", "block.minecraft.purpur_stairs": "紫珀階", "block.minecraft.quartz_block": "石英塊", "block.minecraft.quartz_bricks": "石英磚", "block.minecraft.quartz_pillar": "石英柱", "block.minecraft.quartz_slab": "石英版", "block.minecraft.quartz_stairs": "石英階", "block.minecraft.rail": "軌", "block.minecraft.raw_copper_block": "銅石塊", "block.minecraft.raw_gold_block": "金石塊", "block.minecraft.raw_iron_block": "鐵石塊", "block.minecraft.red_banner": "赤旗", "block.minecraft.red_bed": "赤床", "block.minecraft.red_candle": "赤燭", "block.minecraft.red_candle_cake": "赤燭之洋糕", "block.minecraft.red_carpet": "赤氍毹", "block.minecraft.red_concrete": "赤砼", "block.minecraft.red_concrete_powder": "赤砼粉", "block.minecraft.red_glazed_terracotta": "赤釉陶", "block.minecraft.red_mushroom": "赤蕈", "block.minecraft.red_mushroom_block": "赤蕈塊", "block.minecraft.red_nether_brick_slab": "焱界赤磚版", "block.minecraft.red_nether_brick_stairs": "焱界赤磚階", "block.minecraft.red_nether_brick_wall": "焱界赤磚垣", "block.minecraft.red_nether_bricks": "焱界赤磚塊", "block.minecraft.red_sand": "赤沙", "block.minecraft.red_sandstone": "赤砂", "block.minecraft.red_sandstone_slab": "赤砂版", "block.minecraft.red_sandstone_stairs": "赤砂階", "block.minecraft.red_sandstone_wall": "赤砂垣", "block.minecraft.red_shulker_box": "赤贆櫝", "block.minecraft.red_stained_glass": "赤琉璃", "block.minecraft.red_stained_glass_pane": "赤琉璃嵌板", "block.minecraft.red_terracotta": "赤陶", "block.minecraft.red_tulip": "赤鬱金香", "block.minecraft.red_wool": "赤羊毛", "block.minecraft.redstone_block": "赤石塊", "block.minecraft.redstone_lamp": "赤石燈", "block.minecraft.redstone_ore": "赤石礦", "block.minecraft.redstone_torch": "赤石炬", "block.minecraft.redstone_wall_torch": "壁中赤石炬", "block.minecraft.redstone_wire": "赤石綫", "block.minecraft.reinforced_deepslate": "固板石", "block.minecraft.repeater": "續赭儀", "block.minecraft.repeating_command_block": "循環命令塊", "block.minecraft.resin_block": "樹香塊", "block.minecraft.resin_brick_slab": "樹香磚版", "block.minecraft.resin_brick_stairs": "樹香磚階", "block.minecraft.resin_brick_wall": "樹香磚垣", "block.minecraft.resin_bricks": "樹香磚塊", "block.minecraft.resin_clump": "樹香", "block.minecraft.respawn_anchor": "復生錨", "block.minecraft.rooted_dirt": "盤根土", "block.minecraft.rose_bush": "玫瑰簇", "block.minecraft.sand": "沙", "block.minecraft.sandstone": "砂", "block.minecraft.sandstone_slab": "砂版", "block.minecraft.sandstone_stairs": "砂階", "block.minecraft.sandstone_wall": "砂垣", "block.minecraft.scaffolding": "棧架", "block.minecraft.sculk": "幽匿", "block.minecraft.sculk_catalyst": "幽匿引", "block.minecraft.sculk_sensor": "幽匿探子", "block.minecraft.sculk_shrieker": "幽匿嘯", "block.minecraft.sculk_vein": "幽匿絡", "block.minecraft.sea_lantern": "海磷燈", "block.minecraft.sea_pickle": "海鞘", "block.minecraft.seagrass": "海艸", "block.minecraft.set_spawn": "生處定也", "block.minecraft.short_dry_grass": "淺枯艸", "block.minecraft.short_grass": "淺艸", "block.minecraft.shroomlight": "螢蕈體", "block.minecraft.shulker_box": "贆櫝", "block.minecraft.skeleton_skull": "髑首", "block.minecraft.skeleton_wall_skull": "壁中髑首", "block.minecraft.slime_block": "黏膠塊", "block.minecraft.small_amethyst_bud": "小紫水玉苗", "block.minecraft.small_dripleaf": "幼垂滴葉", "block.minecraft.smithing_table": "鍛案", "block.minecraft.smoker": "燻爐", "block.minecraft.smooth_basalt": "潤黑堦石", "block.minecraft.smooth_quartz": "潤石英塊", "block.minecraft.smooth_quartz_slab": "潤石英版", "block.minecraft.smooth_quartz_stairs": "潤石英階", "block.minecraft.smooth_red_sandstone": "潤赤砂", "block.minecraft.smooth_red_sandstone_slab": "潤赤砂版", "block.minecraft.smooth_red_sandstone_stairs": "潤赤砂階", "block.minecraft.smooth_sandstone": "潤砂", "block.minecraft.smooth_sandstone_slab": "潤砂版", "block.minecraft.smooth_sandstone_stairs": "潤砂階", "block.minecraft.smooth_stone": "潤石", "block.minecraft.smooth_stone_slab": "潤石版", "block.minecraft.sniffer_egg": "嗅獸卵", "block.minecraft.snow": "雪", "block.minecraft.snow_block": "雪塊", "block.minecraft.soul_campfire": "靈營火", "block.minecraft.soul_fire": "靈火", "block.minecraft.soul_lantern": "靈燈", "block.minecraft.soul_sand": "靈沙", "block.minecraft.soul_soil": "靈土", "block.minecraft.soul_torch": "靈炬", "block.minecraft.soul_wall_torch": "壁中靈炬", "block.minecraft.spawn.not_valid": "汝無床抑得力之復生錨，或其見礙也", "block.minecraft.spawner": "孳衍籠", "block.minecraft.spawner.desc1": "用孳卵之時：", "block.minecraft.spawner.desc2": "設生靈之屬", "block.minecraft.sponge": "海綿", "block.minecraft.spore_blossom": "籽榮", "block.minecraft.spruce_button": "樅鈕", "block.minecraft.spruce_door": "樅門", "block.minecraft.spruce_fence": "樅檻", "block.minecraft.spruce_fence_gate": "樅扉", "block.minecraft.spruce_hanging_sign": "樅懸牌", "block.minecraft.spruce_leaves": "樅葉", "block.minecraft.spruce_log": "樅樁", "block.minecraft.spruce_planks": "樅材", "block.minecraft.spruce_pressure_plate": "樅踏板", "block.minecraft.spruce_sapling": "樅秧", "block.minecraft.spruce_sign": "樅牌", "block.minecraft.spruce_slab": "樅版", "block.minecraft.spruce_stairs": "樅階", "block.minecraft.spruce_trapdoor": "樅窖門", "block.minecraft.spruce_wall_hanging_sign": "壁中樅懸牌", "block.minecraft.spruce_wall_sign": "壁中樅牌", "block.minecraft.spruce_wood": "樅木", "block.minecraft.sticky_piston": "黏膠鞲鞴", "block.minecraft.stone": "石", "block.minecraft.stone_brick_slab": "石磚版", "block.minecraft.stone_brick_stairs": "石磚階", "block.minecraft.stone_brick_wall": "石磚垣", "block.minecraft.stone_bricks": "石磚", "block.minecraft.stone_button": "石鈕", "block.minecraft.stone_pressure_plate": "石踏板", "block.minecraft.stone_slab": "石版", "block.minecraft.stone_stairs": "石階", "block.minecraft.stonecutter": "鑿石器", "block.minecraft.stripped_acacia_log": "既扡㭜樁", "block.minecraft.stripped_acacia_wood": "既扡㭜木", "block.minecraft.stripped_bamboo_block": "既扡竹塊", "block.minecraft.stripped_birch_log": "既扡樺樁", "block.minecraft.stripped_birch_wood": "既扡樺木", "block.minecraft.stripped_cherry_log": "既扡櫻樁", "block.minecraft.stripped_cherry_wood": "既扡櫻木", "block.minecraft.stripped_crimson_hyphae": "既扡緋蕈體", "block.minecraft.stripped_crimson_stem": "既扡緋蕈柄", "block.minecraft.stripped_dark_oak_log": "既扡黯柞樁", "block.minecraft.stripped_dark_oak_wood": "既扡黯柞木", "block.minecraft.stripped_jungle_log": "既扡叢莽樁", "block.minecraft.stripped_jungle_wood": "既扡叢莽木", "block.minecraft.stripped_mangrove_log": "既扡沒潮木樁", "block.minecraft.stripped_mangrove_wood": "既扡沒潮木", "block.minecraft.stripped_oak_log": "既扡柞樁", "block.minecraft.stripped_oak_wood": "既扡柞木", "block.minecraft.stripped_pale_oak_log": "既扡縞柞樁", "block.minecraft.stripped_pale_oak_wood": "既扡縞柞木", "block.minecraft.stripped_spruce_log": "既扡樅樁", "block.minecraft.stripped_spruce_wood": "既扡樅木", "block.minecraft.stripped_warped_hyphae": "既扡譎蕈體", "block.minecraft.stripped_warped_stem": "既扡譎蕈柄", "block.minecraft.structure_block": "結構塊", "block.minecraft.structure_void": "築闕", "block.minecraft.sugar_cane": "蔗", "block.minecraft.sunflower": "葵藿", "block.minecraft.suspicious_gravel": "謎礫", "block.minecraft.suspicious_sand": "謎沙", "block.minecraft.sweet_berry_bush": "甜莓叢", "block.minecraft.tall_dry_grass": "高枯艸", "block.minecraft.tall_grass": "高艸", "block.minecraft.tall_seagrass": "高海艸", "block.minecraft.target": "靶", "block.minecraft.terracotta": "陶", "block.minecraft.test_block": "測塊", "block.minecraft.test_instance_block": "測例塊", "block.minecraft.tinted_glass": "蔽明琉璃", "block.minecraft.tnt": "灹藥", "block.minecraft.tnt.disabled": "灹藥已禁爆", "block.minecraft.torch": "炬", "block.minecraft.torchflower": "炬蓮", "block.minecraft.torchflower_crop": "炬蓮株", "block.minecraft.trapped_chest": "機關箱", "block.minecraft.trial_spawner": "煉孳衍籠", "block.minecraft.tripwire": "絆線", "block.minecraft.tripwire_hook": "絆綫鉤", "block.minecraft.tube_coral": "管珊瑚", "block.minecraft.tube_coral_block": "管珊瑚塊", "block.minecraft.tube_coral_fan": "扇狀管珊瑚", "block.minecraft.tube_coral_wall_fan": "壁中扇狀管珊瑚", "block.minecraft.tuff": "積塊石", "block.minecraft.tuff_brick_slab": "積塊石磚版", "block.minecraft.tuff_brick_stairs": "積塊石磚階", "block.minecraft.tuff_brick_wall": "積塊石磚垣", "block.minecraft.tuff_bricks": "積塊石磚", "block.minecraft.tuff_slab": "積塊石版", "block.minecraft.tuff_stairs": "積塊石階", "block.minecraft.tuff_wall": "積塊石垣", "block.minecraft.turtle_egg": "海龜卵", "block.minecraft.twisting_vines": "輪囷藤", "block.minecraft.twisting_vines_plant": "輪囷藤株", "block.minecraft.vault": "寶篋", "block.minecraft.verdant_froglight": "翠鼃光", "block.minecraft.vine": "藤", "block.minecraft.void_air": "虛氣", "block.minecraft.wall_torch": "壁中炬", "block.minecraft.warped_button": "譎蕈木鈕", "block.minecraft.warped_door": "譎蕈木門", "block.minecraft.warped_fence": "譎蕈木檻", "block.minecraft.warped_fence_gate": "譎蕈木扉", "block.minecraft.warped_fungus": "譎蕈", "block.minecraft.warped_hanging_sign": "譎蕈木懸牌", "block.minecraft.warped_hyphae": "譎蕈體", "block.minecraft.warped_nylium": "譎蕈石", "block.minecraft.warped_planks": "譎蕈木材", "block.minecraft.warped_pressure_plate": "譎蕈木踏板", "block.minecraft.warped_roots": "譎蕈索", "block.minecraft.warped_sign": "譎蕈木牌", "block.minecraft.warped_slab": "譎蕈木版", "block.minecraft.warped_stairs": "譎蕈木階", "block.minecraft.warped_stem": "譎蕈柄", "block.minecraft.warped_trapdoor": "譎蕈木窖門", "block.minecraft.warped_wall_hanging_sign": "壁中譎蕈木懸牌", "block.minecraft.warped_wall_sign": "壁中譎蕈木牌", "block.minecraft.warped_wart_block": "譎疣塊", "block.minecraft.water": "水", "block.minecraft.water_cauldron": "瀦水之釜", "block.minecraft.waxed_chiseled_copper": "蠟引雕銅塊", "block.minecraft.waxed_copper_block": "蠟引銅塊", "block.minecraft.waxed_copper_bulb": "蠟引銅燈", "block.minecraft.waxed_copper_door": "蠟引銅門", "block.minecraft.waxed_copper_grate": "蠟引銅柵網", "block.minecraft.waxed_copper_trapdoor": "蠟引銅窖門", "block.minecraft.waxed_cut_copper": "蠟引割銅塊", "block.minecraft.waxed_cut_copper_slab": "蠟引割銅版", "block.minecraft.waxed_cut_copper_stairs": "蠟引割銅階", "block.minecraft.waxed_exposed_chiseled_copper": "蠟引渡濕雕銅塊", "block.minecraft.waxed_exposed_copper": "蠟引渡濕銅塊", "block.minecraft.waxed_exposed_copper_bulb": "蠟引渡濕銅燈", "block.minecraft.waxed_exposed_copper_door": "蠟引渡濕銅門", "block.minecraft.waxed_exposed_copper_grate": "蠟引渡濕銅柵網", "block.minecraft.waxed_exposed_copper_trapdoor": "蠟引渡濕銅窖門", "block.minecraft.waxed_exposed_cut_copper": "蠟引渡濕割銅塊", "block.minecraft.waxed_exposed_cut_copper_slab": "蠟引渡濕割銅版", "block.minecraft.waxed_exposed_cut_copper_stairs": "蠟引渡濕割銅階", "block.minecraft.waxed_oxidized_chiseled_copper": "蠟引風蝕雕銅塊", "block.minecraft.waxed_oxidized_copper": "蠟引風蝕銅塊", "block.minecraft.waxed_oxidized_copper_bulb": "蠟引風蝕銅燈", "block.minecraft.waxed_oxidized_copper_door": "蠟引風蝕銅門", "block.minecraft.waxed_oxidized_copper_grate": "蠟引風蝕銅柵網", "block.minecraft.waxed_oxidized_copper_trapdoor": "蠟引風蝕銅窖門", "block.minecraft.waxed_oxidized_cut_copper": "蠟引風蝕割銅塊", "block.minecraft.waxed_oxidized_cut_copper_slab": "蠟引風蝕割銅版", "block.minecraft.waxed_oxidized_cut_copper_stairs": "蠟引風蝕割銅階", "block.minecraft.waxed_weathered_chiseled_copper": "蠟引鏽刻雕銅塊", "block.minecraft.waxed_weathered_copper": "蠟引鏽刻銅塊", "block.minecraft.waxed_weathered_copper_bulb": "蠟引鏽刻銅燈", "block.minecraft.waxed_weathered_copper_door": "蠟引鏽刻銅門", "block.minecraft.waxed_weathered_copper_grate": "蠟引鏽刻銅柵網", "block.minecraft.waxed_weathered_copper_trapdoor": "蠟引鏽刻銅窖門", "block.minecraft.waxed_weathered_cut_copper": "蠟引鏽刻割銅塊", "block.minecraft.waxed_weathered_cut_copper_slab": "蠟引鏽刻割銅版", "block.minecraft.waxed_weathered_cut_copper_stairs": "蠟引鏽刻割銅階", "block.minecraft.weathered_chiseled_copper": "鏽刻雕銅塊", "block.minecraft.weathered_copper": "鏽刻銅塊", "block.minecraft.weathered_copper_bulb": "鏽刻銅燈", "block.minecraft.weathered_copper_door": "鏽刻銅門", "block.minecraft.weathered_copper_grate": "鏽刻銅柵網", "block.minecraft.weathered_copper_trapdoor": "鏽刻銅窖門", "block.minecraft.weathered_cut_copper": "鏽刻割銅塊", "block.minecraft.weathered_cut_copper_slab": "鏽刻割銅版", "block.minecraft.weathered_cut_copper_stairs": "鏽刻割銅階", "block.minecraft.weeping_vines": "垂泣藤", "block.minecraft.weeping_vines_plant": "垂泣藤株", "block.minecraft.wet_sponge": "㵖海綿", "block.minecraft.wheat": "麥禾", "block.minecraft.white_banner": "素旗", "block.minecraft.white_bed": "白床", "block.minecraft.white_candle": "白燭", "block.minecraft.white_candle_cake": "白燭之洋糕", "block.minecraft.white_carpet": "白氍毹", "block.minecraft.white_concrete": "白砼", "block.minecraft.white_concrete_powder": "白砼粉", "block.minecraft.white_glazed_terracotta": "白釉陶", "block.minecraft.white_shulker_box": "白贆櫝", "block.minecraft.white_stained_glass": "白琉璃", "block.minecraft.white_stained_glass_pane": "白琉璃嵌板", "block.minecraft.white_terracotta": "白陶", "block.minecraft.white_tulip": "白鬱金香", "block.minecraft.white_wool": "白羊毛", "block.minecraft.wildflowers": "野芳叢", "block.minecraft.wither_rose": "凋靈玫瑰", "block.minecraft.wither_skeleton_skull": "凋靈髑首", "block.minecraft.wither_skeleton_wall_skull": "壁中凋靈髑首", "block.minecraft.yellow_banner": "黃旗", "block.minecraft.yellow_bed": "黃床", "block.minecraft.yellow_candle": "黃燭", "block.minecraft.yellow_candle_cake": "黃燭之洋糕", "block.minecraft.yellow_carpet": "黃氍毹", "block.minecraft.yellow_concrete": "黃砼", "block.minecraft.yellow_concrete_powder": "黃砼粉", "block.minecraft.yellow_glazed_terracotta": "黃釉陶", "block.minecraft.yellow_shulker_box": "黃贆櫝", "block.minecraft.yellow_stained_glass": "黃琉璃", "block.minecraft.yellow_stained_glass_pane": "黃琉璃嵌板", "block.minecraft.yellow_terracotta": "黃陶", "block.minecraft.yellow_wool": "黃羊毛", "block.minecraft.zombie_head": "殭屍首", "block.minecraft.zombie_wall_head": "壁中殭屍首", "book.byAuthor": "%1$s著", "book.edit.title": "纂書之幕", "book.editTitle": "題之：", "book.finalizeButton": "署而結之", "book.finalizeWarning": "愼哉！一旦署書，不得更纂。", "book.generation.0": "原著", "book.generation.1": "複本", "book.generation.2": "再複本", "book.generation.3": "殘章", "book.invalid.tag": "*無效書籤*", "book.pageIndicator": "卷%2$s之%1$s", "book.page_button.next": "次葉", "book.page_button.previous": "前葉", "book.sign.title": "署書之幕", "book.sign.titlebox": "題", "book.signButton": "署", "book.view.title": "覽書之幕", "build.tooHigh": "高之極，以%s爲限", "chat.cannotSend": "信弗能送", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "擊此移形", "chat.copy": "複刻於簿", "chat.copy.click": "擊以複刻之於簿", "chat.deleted_marker": "伺服器已除其論議。", "chat.disabled.chain_broken": "以斷連故，論議已止。請重連之。", "chat.disabled.expiredProfileKey": "公鑰既終，禁議。請重連之。", "chat.disabled.invalid_command_signature": "令之署參，有未料抑闕者。", "chat.disabled.invalid_signature": "論議署名無效。請重連之。", "chat.disabled.launcher": "啟者選項：禁語。信弗能送。", "chat.disabled.missingProfileKey": "以爾談機失故，論議已止。請重連之。", "chat.disabled.options": "客端置設：禁語。", "chat.disabled.out_of_order_chat": "聞論議無序。械綱之時嘗改乎？", "chat.disabled.profile": "爾簿制曰：禁㗫。復按%s以得其詳。", "chat.disabled.profile.moreInfo": "爾簿制曰：禁㗫。信不能傳、閱。", "chat.editBox": "議", "chat.filtered": "爲伺服器所隱。", "chat.filtered_full": "伺服器已隱其論議於數許戲者。", "chat.link.confirm": "確欲啟是網葉乎？", "chat.link.confirmTrusted": "啟之，抑複製於剪貼簿？", "chat.link.open": "啟之於瀏覽器", "chat.link.warning": "勿啟子不信者之連結！", "chat.queue": "[余%s待發之訊]", "chat.square_brackets": "[%s]", "chat.tag.error": "伺服器送無效之訊。", "chat.tag.modified": "論議既爲伺服器所更。原文：", "chat.tag.not_secure": "未驗之訊也。弗能檢舉之。", "chat.tag.system": "伺服器之訊也。弗能檢舉之。", "chat.tag.system_single_player": "伺服器之訊也。", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s勝鬥%s", "chat.type.advancement.goal": "%s達的%s", "chat.type.advancement.task": "%s達事%s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "傳伍信", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s曰：%s", "chat.validation_error": "證論議謬", "chat_screen.message": "寄語：%s", "chat_screen.title": "論板", "chat_screen.usage": "言畢，擊迴車以遞之", "chunk.toast.checkLog": "察誌以閱其詳", "chunk.toast.loadFailure": "載區塊於%s未成", "chunk.toast.lowDiskSpace": "礠庫幾盈！", "chunk.toast.lowDiskSpace.description": "生界或不得存。", "chunk.toast.saveFailure": "存區塊於%s未成", "clear.failed.multiple": "無物得之於%s戲者之身", "clear.failed.single": "無物得之於戲者%s之身", "color.minecraft.black": "黑", "color.minecraft.blue": "靛", "color.minecraft.brown": "褐", "color.minecraft.cyan": "黛", "color.minecraft.gray": "灰", "color.minecraft.green": "綠", "color.minecraft.light_blue": "縹", "color.minecraft.light_gray": "蒼", "color.minecraft.lime": "翠", "color.minecraft.magenta": "赬", "color.minecraft.orange": "橙", "color.minecraft.pink": "紅", "color.minecraft.purple": "紫", "color.minecraft.red": "赤", "color.minecraft.white": "白", "color.minecraft.yellow": "黃", "command.context.here": "<--[此]", "command.context.parse_error": "於字元「%2$s」現%1$s：%3$s", "command.exception": "此令未能析也：%s", "command.expected.separator": "實參應以空白作結，然有物隨焉", "command.failed": "方行其令而生謬不虞", "command.forkLimit": "既及行文之極（%s）", "command.unknown.argument": "令參謬誤", "command.unknown.command": "令未知或殘，請以備述", "commands.advancement.criterionNotFound": "功%1$s不含則%2$s", "commands.advancement.grant.criterion.to.many.failure": "予功%2$s之則%1$s至%3$s位戲者未成，因其已有之", "commands.advancement.grant.criterion.to.many.success": "已予功%2$s之則%1$s至%3$s位戲者", "commands.advancement.grant.criterion.to.one.failure": "予功%2$s之則%1$s至戲者%3$s未成，因其已有之", "commands.advancement.grant.criterion.to.one.success": "已予程%2$s之則%1$s至戲者%3$s", "commands.advancement.grant.many.to.many.failure": "不可施%s進程於%s戲者，因彼數人已立彼諸進程", "commands.advancement.grant.many.to.many.success": "已施%s進程於%s戲者", "commands.advancement.grant.many.to.one.failure": "不可施%s進程於%s，因其已立彼諸進程", "commands.advancement.grant.many.to.one.success": "已使%2$s達%1$s事", "commands.advancement.grant.one.to.many.failure": "不可施%s於%s戲者，因彼數人已立此功", "commands.advancement.grant.one.to.many.success": "已施功%s於%s戲者", "commands.advancement.grant.one.to.one.failure": "不可施功%s於%s， 因其已立此功", "commands.advancement.grant.one.to.one.success": "已施功%s於%s", "commands.advancement.revoke.criterion.to.many.failure": "去功%2$s之則%1$s於%3$s位戲者未成，因其未成之", "commands.advancement.revoke.criterion.to.many.success": "已去功%2$s之則%1$s於%3$s位戲者", "commands.advancement.revoke.criterion.to.one.failure": "去功%2$s之則%1$s於戲者%3$s未成，因其未成之", "commands.advancement.revoke.criterion.to.one.success": "已去功%2$s之則%1$s於戲者%3$s", "commands.advancement.revoke.many.to.many.failure": "去%1$s項進程於%2$s位戲者未成，因其未成之", "commands.advancement.revoke.many.to.many.success": "已去%1$s項進程於%2$s位戲者", "commands.advancement.revoke.many.to.one.failure": "去%1$s項功進程戲者%2$s未成，因其未成之", "commands.advancement.revoke.many.to.one.success": "已去%1$s項進程於戲者%2$s", "commands.advancement.revoke.one.to.many.failure": "去功%1$s於%2$s位戲者未成，因其未成之", "commands.advancement.revoke.one.to.many.success": "已去功%1$s於%2$s位戲者", "commands.advancement.revoke.one.to.one.failure": "去功%1$s於戲者%2$s未成，因其未成之", "commands.advancement.revoke.one.to.one.success": "已去功%1$s於戲者%2$s", "commands.attribute.base_value.get.success": "實體%2$s之品%1$s，其基值爲%3$s", "commands.attribute.base_value.reset.success": "實體%2$s之品%1$s，其基值復於%3$s之本", "commands.attribute.base_value.set.success": "實體%2$s之品%1$s，其基值現爲%3$s", "commands.attribute.failed.entity": "%s非此令堪用之實體也", "commands.attribute.failed.modifier_already_present": "修飾符%1$s既現於實體%3$s之%2$s品", "commands.attribute.failed.no_attribute": "實體%s無品%s", "commands.attribute.failed.no_modifier": "修飾符%3$s不存於實體%2$s之%1$s品", "commands.attribute.modifier.add.success": "既添修飾符%1$s於實體%3$s之品%2$s", "commands.attribute.modifier.remove.success": "除修飾符%1$s於實體%3$s之品%2$s", "commands.attribute.modifier.value.get.success": "修飾符%1$s於實體%3$s之品%2$s，其值爲%4$s", "commands.attribute.value.get.success": "實體%2$s之品%1$s，其值爲%3$s", "commands.ban.failed": "無變。是戲者已爲羈", "commands.ban.success": "既羈%s：%s", "commands.banip.failed": "無變。是IP已爲羈", "commands.banip.info": "是羈令施於%s戲者：%s", "commands.banip.invalid": "是IP無效，抑是戲者未知", "commands.banip.success": "已羈IP：%s", "commands.banlist.entry": "%2$s羈%1$s以%3$s", "commands.banlist.entry.unknown": "（未明）", "commands.banlist.list": "%s戲者已羈：", "commands.banlist.none": "無戲者已羈", "commands.bossbar.create.failed": "既有名曰「%s」之帥條", "commands.bossbar.create.success": "既立自定帥條%s", "commands.bossbar.get.max": "自定帥條%s之極爲%s", "commands.bossbar.get.players.none": "自定帥條%s今無戲者綫焉", "commands.bossbar.get.players.some": "自定帥條%s今有%s戲者綫焉：%s", "commands.bossbar.get.value": "自定帥條%s之值爲%s", "commands.bossbar.get.visible.hidden": "自定帥條%s今隱", "commands.bossbar.get.visible.visible": "自定帥條%s今顯", "commands.bossbar.list.bars.none": "無自定帥條之既啟者", "commands.bossbar.list.bars.some": "既啟之自定帥條有%s：%s", "commands.bossbar.remove.success": "既去自定帥條%s", "commands.bossbar.set.color.success": "已易自定帥條%s之色", "commands.bossbar.set.color.unchanged": "無變。是帥條既爲是色矣", "commands.bossbar.set.max.success": "已易自定帥條%s之極爲%s", "commands.bossbar.set.max.unchanged": "無變。是帥條已爲是極", "commands.bossbar.set.name.success": "已更自定帥條%s之名", "commands.bossbar.set.name.unchanged": "無變。是帥條既爲是名矣", "commands.bossbar.set.players.success.none": "自定帥條%s已無戲者焉", "commands.bossbar.set.players.success.some": "自定帥條%s今有戲者%s：%s", "commands.bossbar.set.players.unchanged": "無變。是戲者數人既於此帥條中矣，故無以去留之也", "commands.bossbar.set.style.success": "已易自定帥條%s之式", "commands.bossbar.set.style.unchanged": "無變。是帥條已爲是式", "commands.bossbar.set.value.success": "已易自定帥條%s之值至%s", "commands.bossbar.set.value.unchanged": "無變。是帥條已爲是值", "commands.bossbar.set.visibility.unchanged.hidden": "無變。是帥條已隱", "commands.bossbar.set.visibility.unchanged.visible": "無變。是帥條已現", "commands.bossbar.set.visible.success.hidden": "自定帥條%s已隱", "commands.bossbar.set.visible.success.visible": "自定帥條%s已顯", "commands.bossbar.unknown": "無帥條之ID爲「%s」", "commands.clear.success.multiple": "既除%s物於%s戲者", "commands.clear.success.single": "既除%s物於戲者%s", "commands.clear.test.multiple": "查明%s物於%s戲者", "commands.clear.test.single": "查明%s物於戲者%s", "commands.clone.failed": "複製未果", "commands.clone.overlap": "源與的不可重疊", "commands.clone.success": "既複製%s塊", "commands.clone.toobig": "所定之域內塊量過載（至多%s，定量%s）", "commands.damage.invulnerable": "的可禦是類之傷", "commands.damage.success": "既施%s傷於%s", "commands.data.block.get": "率倍以%5$s後，塊%2$s, %3$s, %4$s之%1$s爲%6$s", "commands.data.block.invalid": "所指之塊非塊實體也", "commands.data.block.modified": "既更%s, %s, %s之塊錄", "commands.data.block.query": "%s, %s, %s有塊錄如左：%s", "commands.data.entity.get": "率倍以%3$s後，%2$s之%1$s爲%4$s", "commands.data.entity.invalid": "不可更戲者錄", "commands.data.entity.modified": "既更%s之實體錄", "commands.data.entity.query": "%s有實體錄如左：%s", "commands.data.get.invalid": "%s不可得，但容數籤耳", "commands.data.get.multiple": "是引數容一單NBT值矣", "commands.data.get.unknown": "%s不可得，是簽無存焉", "commands.data.merge.failed": "無變。所定之性已爲是值", "commands.data.modify.expected_list": "應爲簿，今得：%s", "commands.data.modify.expected_object": "應爲物，今得：%s", "commands.data.modify.expected_value": "應爲值，今得：%s", "commands.data.modify.invalid_index": "無效簿序：%s", "commands.data.modify.invalid_substring": "無效子串索引：%s 至 %s", "commands.data.storage.get": "率倍以%3$s後，器%2$s中之%1$s爲%4$s", "commands.data.storage.modified": "既更器%s", "commands.data.storage.query": "器%s有物如右焉：%s", "commands.datapack.create.already_exists": "既有名「%s」之囊者", "commands.datapack.create.invalid_full_name": "無效新囊名「%s」", "commands.datapack.create.invalid_name": "新囊之名「%s」有無效之文焉", "commands.datapack.create.io_failure": "立囊以「%s」之名未成，請檢其誌", "commands.datapack.create.metadata_encode_failure": "編碼元錄於名曰「%s」之囊者而未成：%s", "commands.datapack.create.success": "既立新空囊，名曰「%s」", "commands.datapack.disable.failed": "錄囊「%s」未啟也！", "commands.datapack.disable.failed.feature": "以歸於既啟之功故，囊「%s」弗能棄也！", "commands.datapack.enable.failed": "錄囊「%s」既啟也！", "commands.datapack.enable.failed.no_flags": "以所須之功未啟於%2$s故，囊「%1$s」弗能啟也！", "commands.datapack.list.available.none": "無錄囊可啟", "commands.datapack.list.available.success": "可啟錄囊有%s：%s", "commands.datapack.list.enabled.none": "無既啟錄囊", "commands.datapack.list.enabled.success": "既啟錄囊有%s：%s", "commands.datapack.modify.disable": "方棄錄囊%s", "commands.datapack.modify.enable": "方啟錄囊%s", "commands.datapack.unknown": "未知之錄囊「%s」", "commands.debug.alreadyRunning": "刻點分析器已啟也", "commands.debug.function.noRecursion": "弗可於函數之中覓其蹤", "commands.debug.function.noReturnRun": "蹤跡弗能與return run俱用", "commands.debug.function.success.multiple": "既自%2$s函數中跡得%1$s令以稟於%3$s", "commands.debug.function.success.single": "既自%2$s函數中跡得令「%1$s」以稟於%3$s", "commands.debug.function.traceFailed": "無跡函數之得", "commands.debug.notRunning": "刻點分析器未啟也", "commands.debug.started": "刻點分析器既啟", "commands.debug.stopped": "刻析終矣，總耗時%s秒與%s刻（%s刻每秒）", "commands.defaultgamemode.success": "預設之法，更之以%s", "commands.deop.failed": "無變。是戲者非吏", "commands.deop.success": "既解%s之伺服器吏職", "commands.dialog.clear.multiple": "既去%s戲者之札", "commands.dialog.clear.single": "既去%s之札", "commands.dialog.show.multiple": "既顯札於%s戲者", "commands.dialog.show.single": "既顯札於%s", "commands.difficulty.failure": "難易未變；已爲%s", "commands.difficulty.query": "現之難易爲%s", "commands.difficulty.success": "難易，更之以%s", "commands.drop.no_held_items": "現實體不可攜物", "commands.drop.no_loot_table": "實體%s無貨表", "commands.drop.no_loot_table.block": "塊%s無貨表", "commands.drop.success.multiple": "掉落%s物", "commands.drop.success.multiple_with_table": "掉落%1$s物於貨表%2$s", "commands.drop.success.single": "既落%2$s%1$s", "commands.drop.success.single_with_table": "既落%2$s%1$s於貨表%3$s", "commands.effect.clear.everything.failed": "的無效可除", "commands.effect.clear.everything.success.multiple": "已除%s者之效", "commands.effect.clear.everything.success.single": "已除%s之效", "commands.effect.clear.specific.failed": "的無所求之效", "commands.effect.clear.specific.success.multiple": "已除%2$s者之%1$s效", "commands.effect.clear.specific.success.single": "已除%2$s之%1$s效", "commands.effect.give.failed": "是效不可用（的自禦之，抑或其有愈強之效）", "commands.effect.give.success.multiple": "既致%2$s者以%1$s之效", "commands.effect.give.success.single": "既致%2$s以%1$s之效", "commands.enchant.failed": "無變。的未持是物，亦或是靈不可用焉", "commands.enchant.failed.entity": "%s非此令堪用之實體也", "commands.enchant.failed.incompatible": "是靈不可用於%s", "commands.enchant.failed.itemless": "%s手中無物", "commands.enchant.failed.level": "%s超是靈之極品%s之上", "commands.enchant.success.multiple": "已施靈%s於%s實體", "commands.enchant.success.single": "已施靈%s於%s之物", "commands.execute.blocks.toobig": "所定之域內塊量過載「至多%s，定量%s」", "commands.execute.conditional.fail": "試未舉", "commands.execute.conditional.fail_count": "試未舉，凡%s次", "commands.execute.conditional.pass": "試既舉", "commands.execute.conditional.pass_count": "試既舉，凡%s次", "commands.execute.function.instantiationFailure": "實化函數%s未成：%s", "commands.experience.add.levels.success.multiple": "既予%s層經驗於%s戲者", "commands.experience.add.levels.success.single": "既予%s層經驗於%s", "commands.experience.add.points.success.multiple": "既予%s經驗於%s戲者", "commands.experience.add.points.success.single": "既予%s經驗於%s", "commands.experience.query.levels": "%s具%s階經驗", "commands.experience.query.points": "%s具%s經驗", "commands.experience.set.levels.success.multiple": "已定%2$s戲者之經驗爲%1$s層", "commands.experience.set.levels.success.single": "已定%2$s之經驗爲%1$s層", "commands.experience.set.points.invalid": "經驗不可益等限", "commands.experience.set.points.success.multiple": "既更%2$s戲者之經驗爲%1$s", "commands.experience.set.points.success.single": "既更%2$s之經驗爲%1$s", "commands.fill.failed": "填塊未果", "commands.fill.success": "既填%s塊", "commands.fill.toobig": "所定之域內塊量過載「至多%s，定量%s」", "commands.fillbiome.success": "既設生態域於%s, %s, %s與%s, %s, %s間", "commands.fillbiome.success.count": "既設生態域%s格於%s, %s, %s與%s, %s, %s間", "commands.fillbiome.toobig": "所定之囿內塊量過載（至多%s，定量%s）", "commands.forceload.added.failure": "無區塊已標爲常駐", "commands.forceload.added.multiple": "將%2$s之%3$s之%4$s間%1$s區塊標爲常駐", "commands.forceload.added.none": "%s中無常駐區塊可尋矣", "commands.forceload.added.single": "將%2$s之區塊%1$s標爲常駐", "commands.forceload.list.multiple": "%2$s之中有%1$s常駐區塊：%3$s", "commands.forceload.list.single": "%s之中有一常駐區塊：%s", "commands.forceload.query.failure": "%2$s之區塊%1$s非常駐", "commands.forceload.query.success": "%2$s之區塊%1$s爲常駐", "commands.forceload.removed.all": "除皆常駐區塊於%s", "commands.forceload.removed.failure": "未除常駐區塊", "commands.forceload.removed.multiple": "將%2$s之%3$s之%4$s間%1$s區塊除於常駐", "commands.forceload.removed.single": "將%2$s之區塊%1$s除於常駐", "commands.forceload.toobig": "所定之域內區塊量过載（至多%s，定量%s）", "commands.function.error.argument_not_compound": "無效參類：%s，應爲Compound", "commands.function.error.missing_argument": "術%1$s闕參%2$s", "commands.function.error.missing_arguments": "術%s闕參", "commands.function.error.parse": "實巨集%s之時：令「%s」謬曰：%s", "commands.function.instantiationFailure": "實化函數%s未成：%s", "commands.function.result": "函數%s反以%s", "commands.function.scheduled.multiple": "方運行函數%s", "commands.function.scheduled.no_functions": "尋函數%s未果", "commands.function.scheduled.single": "方運行函數%s", "commands.function.success.multiple": "既行%s令於%s函數", "commands.function.success.multiple.result": "既行%s術", "commands.function.success.single": "既行%s令於函數「%s」", "commands.function.success.single.result": "術「%2$s」返以%1$s", "commands.gamemode.success.other": "%s之嬉遊之法既設如%s", "commands.gamemode.success.self": "嬉遊之法既設如%s", "commands.gamerule.query": "章則%s今爲：%s", "commands.gamerule.set": "章則%s即設爲：%s", "commands.give.failed.toomanyitems": "夫予%s者有界，%s爲其限。", "commands.give.success.multiple": "既予%s%s至%s戲者", "commands.give.success.single": "既予%s%s至%s", "commands.help.failed": "是令未知抑無權", "commands.item.block.set.success": "既替%s, %s, %s之槽以%s", "commands.item.entity.set.success.multiple": "既替%s實體之槽以%s", "commands.item.entity.set.success.single": "既替%s之槽以%s", "commands.item.source.no_such_slot": "本無槽%s也", "commands.item.source.not_a_container": "起位%s, %s, %s之上者非器也", "commands.item.target.no_changed.known_item": "無槽%2$s受%1$s者也", "commands.item.target.no_changes": "無槽%s受物者也", "commands.item.target.no_such_slot": "的無槽%s也", "commands.item.target.not_a_container": "終位%s, %s, %s非器也", "commands.jfr.dump.failed": "JFR 之錄印未果：%s", "commands.jfr.start.failed": "始析JFR之能度而未成", "commands.jfr.started": "始析於JFR", "commands.jfr.stopped": "JFR析止，印於%s", "commands.kick.owner.failed": "不可逐區網之主", "commands.kick.singleplayer.failed": "不可逐人於獨戲之離綫者", "commands.kick.success": "既逐%s：%s", "commands.kill.success.multiple": "既殺實體%s", "commands.kill.success.single": "既殺%s", "commands.list.nameAndId": "%s（%s）", "commands.list.players": "頂%2$s戲者中有%1$s人在線：%3$s", "commands.locate.biome.not_found": "尋生態域類「%s」於合理之距未果", "commands.locate.biome.success": "至邇之%s在於%s（去君%s方遠）", "commands.locate.poi.not_found": "尋所好類「%s」於合理之距未果", "commands.locate.poi.success": "至邇之%s在於%s（去君%s方遠）", "commands.locate.structure.invalid": "無所謂構曰「%s」", "commands.locate.structure.not_found": "尋構曰「%s」邇未果", "commands.locate.structure.success": "至邇之%s在於%s（去君%s方遠）", "commands.message.display.incoming": "%s私語君曰：%s", "commands.message.display.outgoing": "君私語%s曰：%s", "commands.op.failed": "無變。是戲者既爲吏矣", "commands.op.success": "既封%s爲伺服器之吏", "commands.pardon.failed": "無變。是戲者未爲羈", "commands.pardon.success": "既赦%s", "commands.pardonip.failed": "無變。是IP未爲羈", "commands.pardonip.invalid": "是IP無效", "commands.pardonip.success": "已赦IP%s", "commands.particle.failed": "是粒弗可爲悉戲者所見", "commands.particle.success": "方顯粒%s", "commands.perf.alreadyRunning": "能力分析器已啟也", "commands.perf.notRunning": "能力分析器未啟也", "commands.perf.reportFailed": "勘報弗能存", "commands.perf.reportSaved": "勘報存之於%s", "commands.perf.started": "十秒析能已作（以「/perf stop」先止之）", "commands.perf.stopped": "析能終矣，總耗時%s秒與%s刻（%s刻每秒）", "commands.place.feature.failed": "無從置地物", "commands.place.feature.invalid": "無所謂地物類爲「%s」", "commands.place.feature.success": "置「%s」之於%s, %s, %s", "commands.place.jigsaw.failed": "載榫卯未成", "commands.place.jigsaw.invalid": "無模板池之類如「%s」者", "commands.place.jigsaw.success": "已載榫卯於%s，%s，%s", "commands.place.structure.failed": "置築未成", "commands.place.structure.invalid": "無所謂地物類爲「%s」", "commands.place.structure.success": "已生築「%s」於%s, %s, %s", "commands.place.template.failed": "置典範未成", "commands.place.template.invalid": "無所謂典範名「%s」者", "commands.place.template.success": "已載典範「%s」於%s，%s，%s", "commands.playsound.failed": "是音過遙，不可聞之", "commands.playsound.success.multiple": "既奏%s音至%s戲者", "commands.playsound.success.single": "既奏%s音至%s", "commands.publish.alreadyPublished": "%s埠既有一眾戲焉", "commands.publish.failed": "無以立戲於私網", "commands.publish.started": "既立此戲於%s埠", "commands.publish.success": "眾戲既立於%s埠", "commands.random.error.range_too_large": "無定值之大，以2147483646爲限", "commands.random.error.range_too_small": "無定值之小，以二爲極", "commands.random.reset.all.success": "已重設%s無定序列", "commands.random.reset.success": "已重設無定序列%s", "commands.random.roll": "%s得無定值%s（間乎%s及%s）", "commands.random.sample.success": "無定值：%s", "commands.recipe.give.failed": "無得新方", "commands.recipe.give.success.multiple": "已啟%s方於%s戲者", "commands.recipe.give.success.single": "已啟%s方於%s", "commands.recipe.take.failed": "無可忘之方", "commands.recipe.take.success.multiple": "已自%2$s戲者去%1$s方", "commands.recipe.take.success.single": "已自%2$s去%1$s方", "commands.reload.failure": "復載而未果；故舊錄", "commands.reload.success": "方復載也！", "commands.ride.already_riding": "%s既騎%s", "commands.ride.dismount.success": "%s止騎%s", "commands.ride.mount.failure.cant_ride_players": "戲者弗能爲人所騎", "commands.ride.mount.failure.generic": "%s弗能始騎%s", "commands.ride.mount.failure.loop": "實體弗能自騎，亦弗能騎其所騎焉者", "commands.ride.mount.failure.wrong_dimension": "異界實體弗能騎也", "commands.ride.mount.success": "%s始騎%s", "commands.ride.not_riding": "%s無所騎", "commands.rotate.success": "既旋以%s", "commands.save.alreadyOff": "存功既閉", "commands.save.alreadyOn": "存功既啟", "commands.save.disabled": "自存術已閉", "commands.save.enabled": "自存術已啟", "commands.save.failed": "不可記戲于錄（礠庫之空足焉乎？）", "commands.save.saving": "方記戲程至錄，少安毋躁", "commands.save.success": "記世成矣", "commands.schedule.cleared.failure": "無ID爲%s之程", "commands.schedule.cleared.success": "既去程之ID爲%2$s者%1$s", "commands.schedule.created.function": "既置術「%1$s」於%3$s時，餘%2$s刻", "commands.schedule.created.tag": "既置簽「%1$s」於%3$s時，餘%2$s刻", "commands.schedule.macro": "規巨集未成", "commands.schedule.same_tick": "無以置於是刻", "commands.scoreboard.objectives.add.duplicate": "已有的用是名", "commands.scoreboard.objectives.add.success": "立新的%s", "commands.scoreboard.objectives.display.alreadyEmpty": "無變。顯區已空", "commands.scoreboard.objectives.display.alreadySet": "無變。顯區已現是的", "commands.scoreboard.objectives.display.cleared": "既去%s顯域之悉的", "commands.scoreboard.objectives.display.set": "已顯的%2$s於顯域%1$s", "commands.scoreboard.objectives.list.empty": "今無的", "commands.scoreboard.objectives.list.success": "記分項有%s：%s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "既禁的%s之現自新", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "既啟的%s之現自新", "commands.scoreboard.objectives.modify.displayname": "既更%s之表名至%s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "既去的%s數之本範", "commands.scoreboard.objectives.modify.objectiveFormat.set": "既更的%s數之本範", "commands.scoreboard.objectives.modify.rendertype": "既更%s之繪式", "commands.scoreboard.objectives.remove.success": "已去的%s", "commands.scoreboard.players.add.success.multiple": "既添%1$s分於%3$s實體之%2$s", "commands.scoreboard.players.add.success.single": "已加%1$s分於%3$s之%2$s（今共計%4$s分）", "commands.scoreboard.players.display.name.clear.success.multiple": "既去%s實體之表名於%s", "commands.scoreboard.players.display.name.clear.success.single": "既去%s之表名於%s", "commands.scoreboard.players.display.name.set.success.multiple": "既更%2$s實體之表名於%3$s至%1$s", "commands.scoreboard.players.display.name.set.success.single": "既更%2$s之表名於%3$s至%1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "既去%s實體之數範於%s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "既去%s之數範於%s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "既更%s實體之數範於%s", "commands.scoreboard.players.display.numberFormat.set.success.single": "既更%s之數範於%s", "commands.scoreboard.players.enable.failed": "無變。是發既啟", "commands.scoreboard.players.enable.invalid": "但可發觸發器屬之的耳", "commands.scoreboard.players.enable.success.multiple": "既發%s於%s實體", "commands.scoreboard.players.enable.success.single": "既發%s於%s", "commands.scoreboard.players.get.null": "不可得值%s於%s；無設", "commands.scoreboard.players.get.success": "%s有%s%s", "commands.scoreboard.players.list.empty": "無所從之實體", "commands.scoreboard.players.list.entity.empty": "%s無分以示之也", "commands.scoreboard.players.list.entity.entry": "%s： %s", "commands.scoreboard.players.list.entity.success": "%s有分%s：", "commands.scoreboard.players.list.success": "今跡%s實體：%s", "commands.scoreboard.players.operation.success.multiple": "既更%2$s實體之%1$s", "commands.scoreboard.players.operation.success.single": "已置%2$s之%1$s爲%3$s", "commands.scoreboard.players.remove.success.multiple": "已既除%1$s分於%3$s實體之%2$s", "commands.scoreboard.players.remove.success.single": "既除%1$s分於%3$s之%2$s（今共計%4$s分）", "commands.scoreboard.players.reset.all.multiple": "已重設%s實體之悉分", "commands.scoreboard.players.reset.all.single": "已重設%s之悉分", "commands.scoreboard.players.reset.specific.multiple": "已重設%2$s實體之%1$s", "commands.scoreboard.players.reset.specific.single": "已重設%2$s之%1$s", "commands.scoreboard.players.set.success.multiple": "已設%2$s實體之%1$s爲%3$s", "commands.scoreboard.players.set.success.single": "已設%2$s之%1$s爲%3$s", "commands.seed.success": "其種曰：%s", "commands.setblock.failed": "不可置塊", "commands.setblock.success": "既更塊之於%s, %s, %s者", "commands.setidletimeout.success": "戲者縵限今爲%s分", "commands.setidletimeout.success.disabled": "戲者縵限既禁", "commands.setworldspawn.failure.not_overworld": "僅可設界生點於主界", "commands.setworldspawn.success": "界生點既爲 %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "已設%6$s戲者%5$s之生處於%1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "已設%6$s%5$s之生處於%1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s未入覽法", "commands.spectate.self": "不可自觀", "commands.spectate.success.started": "方觀%s", "commands.spectate.success.stopped": "不復窺實體", "commands.spreadplayers.failed.entities": "不可散%s實體（%s, %s）焉（此間實體眾矣；願恢弘幅隕以頂%s格）", "commands.spreadplayers.failed.invalid.height": "無用之限高值也： %s ，應高於世之極 %s", "commands.spreadplayers.failed.teams": "不可散%s伍（%s, %s）焉（此間實體眾矣；願恢弘幅隕以頂%s格）", "commands.spreadplayers.success.entities": "既散%s戲者於%s, %s之間，間均距%s方", "commands.spreadplayers.success.teams": "既散%s伍於%s, %s之間，間均距%s方", "commands.stop.stopping": "方止伺服器", "commands.stopsound.success.source.any": "既悉止「%s」之音", "commands.stopsound.success.source.sound": "已止以「%2$s」爲源之音「%1$s」", "commands.stopsound.success.sourceless.any": "萬籟俱靜", "commands.stopsound.success.sourceless.sound": "既止音「%s」", "commands.summon.failed": "召實體未成", "commands.summon.failed.uuid": "召而未果，蓋戶碼重也", "commands.summon.invalidPosition": "是召喚方位無效", "commands.summon.success": "既召一%s", "commands.tag.add.failed": "的已有此簽，亦或其簽繁哉", "commands.tag.add.success.multiple": "已添「%s」簽諸%s實體", "commands.tag.add.success.single": "已添「%s」簽諸「%s」", "commands.tag.list.multiple.empty": "是%s實體間皆無簽", "commands.tag.list.multiple.success": "是%s實體間共%s簽：%s", "commands.tag.list.single.empty": "%s無簽", "commands.tag.list.single.success": "%s有%s簽：%s", "commands.tag.remove.failed": "的無是簽", "commands.tag.remove.success.multiple": "已除「%s」簽自%s實體", "commands.tag.remove.success.single": "已除「%s」簽自「%s」", "commands.team.add.duplicate": "已有伍用是名", "commands.team.add.success": "立伍%s", "commands.team.empty.success": "已去%s戲者自伍%s", "commands.team.empty.unchanged": "無變。伍已無人", "commands.team.join.success.multiple": "已增%s員至伍%s", "commands.team.join.success.single": "%s入伍%s", "commands.team.leave.success.multiple": "已自悉伍去%s戲者", "commands.team.leave.success.single": "已自伍去%s", "commands.team.list.members.empty": "%s伍無員", "commands.team.list.members.success": "%s伍有%s員：%s", "commands.team.list.teams.empty": "無伍也", "commands.team.list.teams.success": "共計%s伍：%s", "commands.team.option.collisionRule.success": "伍%s之相叩法度今爲「%s」", "commands.team.option.collisionRule.unchanged": "無變。相叩法度已爲是值矣", "commands.team.option.color.success": "已易伍%s之色爲%s", "commands.team.option.color.unchanged": "無變。伍已有是色", "commands.team.option.deathMessageVisibility.success": "今伍%s之訃之顯晦爲「%s」", "commands.team.option.deathMessageVisibility.unchanged": "無變。訃之顯晦已爲是值", "commands.team.option.friendlyfire.alreadyDisabled": "無變。是伍已止同室操戈", "commands.team.option.friendlyfire.alreadyEnabled": "無變。是伍已啟同室操戈", "commands.team.option.friendlyfire.disabled": "已閉伍%s之同室操戈", "commands.team.option.friendlyfire.enabled": "已啟伍%s之同室操戈", "commands.team.option.name.success": "既改%s伍名", "commands.team.option.name.unchanged": "無變。伍已有是名", "commands.team.option.nametagVisibility.success": "今伍%s之名刺之顯晦爲「%s」", "commands.team.option.nametagVisibility.unchanged": "無變。名刺之顯晦已爲是值", "commands.team.option.prefix.success": "伍之首綴已設爲%s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "無變。是伍固弗能視隱之同僚", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "無變。是伍固能視隱之同僚", "commands.team.option.seeFriendlyInvisibles.disabled": "伍%s不可見既隠之戰友", "commands.team.option.seeFriendlyInvisibles.enabled": "伍%s可見既隠之戰友", "commands.team.option.suffix.success": "伍之尾綴已設爲%s", "commands.team.remove.success": "既消伍%s", "commands.teammsg.failed.noteam": "君必入一伍以傳伍信", "commands.teleport.invalidPosition": "傳送方位無效", "commands.teleport.success.entity.multiple": "已傳%s實體於%s", "commands.teleport.success.entity.single": "傳%s於%s", "commands.teleport.success.location.multiple": "已送%s實體之%s, %s, %s", "commands.teleport.success.location.single": "已送%s之%s, %s, %s", "commands.test.batch.starting": "方啟%s境之%s次", "commands.test.clear.error.no_tests": "尋須除之試而未果", "commands.test.clear.success": "既除%s構", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "擊以複刻之於簿", "commands.test.create.success": "既初設測%s", "commands.test.error.no_test_containing_pos": "尋具%s, %s, %s之測例而未果", "commands.test.error.no_test_instances": "尋測例而未果", "commands.test.error.non_existant_test": "尋測%s而未果", "commands.test.error.structure_not_found": "尋測構%s而未果", "commands.test.error.test_instance_not_found": "尋測例實體未得", "commands.test.error.test_instance_not_found.position": "尋測例實體於%s, %s, %s處未得", "commands.test.error.too_large": "構之修短，以%s方爲限", "commands.test.locate.done": "尋畢，既尋構有%s", "commands.test.locate.found": "尋構於：%s（距：%s）", "commands.test.locate.started": "始位測構，或須幾許⋯", "commands.test.no_tests": "無需行之測", "commands.test.relative_position": "之於%s：%s", "commands.test.reset.error.no_tests": "尋試之需復置者未果", "commands.test.reset.success": "既復置%s構", "commands.test.run.no_tests": "尋測而未果", "commands.test.run.running": "方行%s測⋯", "commands.test.summary": "戲測畢！既行測%s", "commands.test.summary.all_required_passed": "所需之測皆既成矣 :)", "commands.test.summary.failed": "需行之測%s者未成 :(", "commands.test.summary.optional_failed": "可選之測%s者未成", "commands.tick.query.percentiles": "百分數：五十分：%s毫秒，九十五分：%s毫秒，九十九分：%s毫秒；計有樣%s", "commands.tick.query.rate.running": "的之刻速：%s每秒。\n刻之均時：%s毫秒（的：%s毫秒）", "commands.tick.query.rate.sprinting": "的之刻速：%s每秒（既略之，惟以一比耳）。\n刻之均時：%s毫秒", "commands.tick.rate.success": "既設的之刻率以%s每秒", "commands.tick.sprint.report": "催戲既成，以%s刻爲一秒，%s毫秒爲一刻", "commands.tick.sprint.stop.fail": "方無催刻", "commands.tick.sprint.stop.success": "催刻既止", "commands.tick.status.frozen": "既定戲刻", "commands.tick.status.lagging": "戲正行，然未能齊的之刻速", "commands.tick.status.running": "戲行常速", "commands.tick.status.sprinting": "方催戲刻", "commands.tick.step.fail": "進戲未成，請先定之", "commands.tick.step.stop.fail": "方無進刻", "commands.tick.step.stop.success": "進刻既止", "commands.tick.step.success": "方進%s刻", "commands.time.query": "時%s", "commands.time.set": "時既爲%s", "commands.title.cleared.multiple": "既去%s戲者之題", "commands.title.cleared.single": "既去%s之題", "commands.title.reset.multiple": "已重設%s戲者之題設", "commands.title.reset.single": "已重設%s之題設", "commands.title.show.actionbar.multiple": "已顯新旁注於%s戲者", "commands.title.show.actionbar.single": "已顯新旁注於%s", "commands.title.show.subtitle.multiple": "於%s戲者現新旁註矣", "commands.title.show.subtitle.single": "於%s現新旁註矣", "commands.title.show.title.multiple": "已顯新題於%s戲者", "commands.title.show.title.single": "已顯新題於%s", "commands.title.times.multiple": "已易%s戲者之顯題時", "commands.title.times.single": "已易%s之顯題時", "commands.transfer.error.no_players": "必定戲者以遷之", "commands.transfer.success.multiple": "方遷%s戲者於%s:%s", "commands.transfer.success.single": "方遷%s於%s:%s", "commands.trigger.add.success": "已動%s（閏值以%s）", "commands.trigger.failed.invalid": "但可發「觸發器」屬之的耳", "commands.trigger.failed.unprimed": "尚不可發是的也", "commands.trigger.set.success": "已動%s（設值爲%s）", "commands.trigger.simple.success": "已動%s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "%s內無驛", "commands.waypoint.list.success": "%2$s內得%1$s驛：%3$s", "commands.waypoint.modify.color": "驛色已如%s", "commands.waypoint.modify.color.reset": "復驛色", "commands.waypoint.modify.style": "既更驛式", "commands.weather.set.clear": "妙哉！天公作美", "commands.weather.set.rain": "天象既爲雨", "commands.weather.set.thunder": "天象既爲雷雨", "commands.whitelist.add.failed": "戲者既於準簿焉", "commands.whitelist.add.success": "既添%s入準簿", "commands.whitelist.alreadyOff": "準簿既棄", "commands.whitelist.alreadyOn": "準簿既用", "commands.whitelist.disabled": "既棄準簿", "commands.whitelist.enabled": "既用準簿", "commands.whitelist.list": "%s戲者名列準簿焉：%s", "commands.whitelist.none": "查無名列準簿之戲者", "commands.whitelist.reloaded": "既復載準簿", "commands.whitelist.remove.failed": "戲者未於準簿焉", "commands.whitelist.remove.success": "既除%s於準簿", "commands.worldborder.center.failed": "無變。世疆之心既於此矣", "commands.worldborder.center.success": "已定世疆之心於%s, %s", "commands.worldborder.damage.amount.failed": "無變。世疆之傷既爲是值矣", "commands.worldborder.damage.amount.success": "世疆之傷既距增%s", "commands.worldborder.damage.buffer.failed": "無變。世疆緩傷域既爲是距矣", "commands.worldborder.damage.buffer.success": "世疆之戒既爲%s方", "commands.worldborder.get": "今世疆之尺爲%s方", "commands.worldborder.set.failed.big": "疆有界焉，方%s爲限。", "commands.worldborder.set.failed.far": "疆有界焉，%s方爲限。", "commands.worldborder.set.failed.nochange": "無變。世疆既尺此方矣", "commands.worldborder.set.failed.small": "世疆不可小於一方", "commands.worldborder.set.grow": "增世疆之尺爲%1$s方於%2$s秒間", "commands.worldborder.set.immediate": "已定世疆之尺爲%s方", "commands.worldborder.set.shrink": "縮世疆之尺爲%1$s方於%2$s秒間", "commands.worldborder.warning.distance.failed": "無變。世疆警距既爲是距矣", "commands.worldborder.warning.distance.success": "世疆警距既爲%s方", "commands.worldborder.warning.time.failed": "無變。世疆警時既爲是時矣", "commands.worldborder.warning.time.success": "世疆警時既爲%s秒", "compliance.playtime.greaterThan24Hours": "子戲逾十二時辰矣", "compliance.playtime.hours": "子既戲%s小時矣", "compliance.playtime.message": "戲淫則枉生焉", "connect.aborted": "既斷聯", "connect.authorizing": "方登入⋯", "connect.connecting": "方連伺服器⋯", "connect.encrypting": "方加密⋯", "connect.failed": "伺服器連線未果", "connect.failed.transfer": "遷於伺服器聯綫未成", "connect.joining": "方入生界⋯", "connect.negotiating": "方商其連⋯", "connect.reconfiging": "方復置⋯", "connect.reconfiguring": "方復置⋯", "connect.transferring": "方遷於新伺服器⋯", "container.barrel": "木桶", "container.beacon": "烽火臺", "container.beehive.bees": "蜂：%2$s之%1$s", "container.beehive.honey": "蜜：%2$s之%1$s", "container.blast_furnace": "冶爐", "container.brewing": "煉藥臺", "container.cartography_table": "輿圖案", "container.chest": "箱", "container.chestDouble": "大箱", "container.crafter": "製械", "container.crafting": "製", "container.creative": "擇物", "container.dispenser": "射械", "container.dropper": "擲械", "container.enchant": "淬靈", "container.enchant.clue": "%s…？", "container.enchant.lapis.many": "群青者%s", "container.enchant.lapis.one": "群青者1", "container.enchant.level.many": "並%s階經驗", "container.enchant.level.one": "並1階經驗", "container.enchant.level.requirement": "需%s階", "container.enderchest": "終眇箱", "container.furnace": "爐", "container.grindstone_title": "繕與祛", "container.hopper": "漏斗", "container.inventory": "行囊", "container.isLocked": "%s已鎖！", "container.lectern": "書檯", "container.loom": "機杼", "container.repair": "修與名", "container.repair.cost": "淬靈所需：%1$s", "container.repair.expensive": "貴甚！", "container.shulkerBox": "贆櫝", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "%s者有餘", "container.shulkerBox.unknownContents": "???????", "container.smoker": "燻爐", "container.spectatorCantOpen": "不可啟。貨未生矣。", "container.stonecutter": "鑿石器", "container.upgrade": "昇兵革", "container.upgrade.error_tooltip": "物弗能昇以是法", "container.upgrade.missing_template_tooltip": "置鍛模", "controls.keybinds": "鍵控⋯", "controls.keybinds.duplicateKeybinds": "是鍵亦用於：\n%s", "controls.keybinds.title": "鍵控", "controls.reset": "重設", "controls.resetAll": "重設諸鍵", "controls.title": "控", "createWorld.customize.buffet.biome": "請擇一生態域", "createWorld.customize.buffet.title": "自定生界", "createWorld.customize.flat.height": "上限", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "底 - %s", "createWorld.customize.flat.layer.top": "頂 - %s", "createWorld.customize.flat.removeLayer": "刪此層", "createWorld.customize.flat.tile": "地層之質", "createWorld.customize.flat.title": "自擬平川", "createWorld.customize.presets": "預設", "createWorld.customize.presets.list": "此乃預設之供君所用者也！", "createWorld.customize.presets.select": "施行設", "createWorld.customize.presets.share": "君欲與人共享之？請君試下匡！", "createWorld.customize.presets.title": "擇一預設", "createWorld.preparing": "鴻蒙初闢⋯", "createWorld.tab.game.title": "戲", "createWorld.tab.more.title": "餘者", "createWorld.tab.world.title": "生界", "credits_and_attribution.button.attribution": "權", "credits_and_attribution.button.credits": "署", "credits_and_attribution.button.licenses": "權契", "credits_and_attribution.screen.title": "署權", "dataPack.bundle.description": "啓用試行藝能「皮囊」", "dataPack.bundle.name": "皮囊", "dataPack.locator_bar.description": "示人之所在於眾戲", "dataPack.locator_bar.name": "尋蹤欄", "dataPack.minecart_improvements.description": "礦車移行之補益", "dataPack.minecart_improvements.name": "礦車補益", "dataPack.redstone_experiments.description": "試行更赭", "dataPack.redstone_experiments.name": "試行赭事", "dataPack.title": "擇錄囊", "dataPack.trade_rebalance.description": "鄉民新賈", "dataPack.trade_rebalance.name": "均輸平準", "dataPack.update_1_20.description": "礦藝一點二〇之新物", "dataPack.update_1_20.name": "一點二〇更新", "dataPack.update_1_21.description": "礦藝一點二一之新物", "dataPack.update_1_21.name": "一點二一更新", "dataPack.validation.back": "反", "dataPack.validation.failed": "錄囊證敗！", "dataPack.validation.reset": "回初", "dataPack.validation.working": "證所選之錄囊⋯", "dataPack.vanilla.description": "礦藝之本設", "dataPack.vanilla.name": "預置", "dataPack.winter_drop.description": "冬投之新物", "dataPack.winter_drop.name": "冬投", "datapackFailure.safeMode": "無虞之法", "datapackFailure.safeMode.failed.description": "其世之錄有損失焉。", "datapackFailure.safeMode.failed.title": "以無虞之法載生界而未成。", "datapackFailure.title": "錄囊有謬，是以阻礙天地之載入。\n或可載以原版錄囊一試「無虞之法」，或返卷首躬繕之。", "death.attack.anvil": "%1$s爲墜鐵砧壓斃矣", "death.attack.anvil.player": "搏%2$s之時%1$s爲墜砧壓殺", "death.attack.arrow": "%1$s爲%2$s所射殺", "death.attack.arrow.item": "%1$s爲%2$s以%3$s所射殺", "death.attack.badRespawnPoint.link": "既定之陷阱", "death.attack.badRespawnPoint.message": "%1$s爲%2$s所殺", "death.attack.cactus": "%1$s爲仙人掌所刺斃", "death.attack.cactus.player": "去%2$s之時%1$s爲仙人掌所刺斃", "death.attack.cramming": "%1$s見壓而斃", "death.attack.cramming.player": "%1$s爲%2$s所壓殺", "death.attack.dragonBreath": "%1$s炙亡於龍涎中", "death.attack.dragonBreath.player": "%1$s爲%2$s所炙亡於龍涎中", "death.attack.drown": "%1$s溺斃", "death.attack.drown.player": "去%2$s之時%1$s溺斃", "death.attack.dryout": "%1$s涸斃", "death.attack.dryout.player": "去%2$s之時%1$s涸斃", "death.attack.even_more_magic": "%1$s斃於不測神力", "death.attack.explosion": "%1$s爆斃", "death.attack.explosion.player": "%1$s爲%2$s所爆殺", "death.attack.explosion.player.item": "%1$s爲%2$s以%3$s所爆殺", "death.attack.fall": "%1$s墜斃", "death.attack.fall.player": "去%2$s之時%1$s墜斃", "death.attack.fallingBlock": "%1$s爲墜塊壓斃", "death.attack.fallingBlock.player": "搏%2$s之時%1$s爲墜塊壓斃", "death.attack.fallingStalactite": "%1$s爲穴之石墜而穿", "death.attack.fallingStalactite.player": "搏%2$s之時%1$s爲穴之石墜而穿", "death.attack.fireball": "%1$s爲%2$s以火圓射殺", "death.attack.fireball.item": "%1$s爲%2$s以%3$s之火圓射殺", "death.attack.fireworks": "%1$s斃於火爆", "death.attack.fireworks.item": "%1$s斃於%2$s以%3$s所射火爆", "death.attack.fireworks.player": "搏%2$s之時%1$s斃於火爆", "death.attack.flyIntoWall": "%1$s搶壁而亡", "death.attack.flyIntoWall.player": "去%2$s之時%1$s搶壁而亡", "death.attack.freeze": "%1$s凍斃矣", "death.attack.freeze.player": "%1$s爲%2$s所凍斃", "death.attack.generic": "%1$s既斃", "death.attack.generic.player": "%1$s斃於%2$s", "death.attack.genericKill": "%1$s遭戮", "death.attack.genericKill.player": "搏%2$s之時%1$s遭戮", "death.attack.hotFloor": "%1$s方察地乃熔石也", "death.attack.hotFloor.player": "%1$s欲避%2$s而步入險境", "death.attack.inFire": "%1$s浴火焚身", "death.attack.inFire.player": "搏%2$s之時%1$s斃於焰", "death.attack.inWall": "%1$s窒於壁", "death.attack.inWall.player": "搏%2$s之時%1$s窒於壁", "death.attack.indirectMagic": "%1$s爲%2$s以魔所殺", "death.attack.indirectMagic.item": "%1$s爲%2$s以%3$s所殺", "death.attack.lava": "%1$s融於熔石", "death.attack.lava.player": "去%2$s之時%1$s融於熔石", "death.attack.lightningBolt": "雷殛%1$s", "death.attack.lightningBolt.player": "雷殛%1$s於搏%2$s之時", "death.attack.mace_smash": "%1$s爲%2$s所椎殺", "death.attack.mace_smash.item": "%1$s爲%2$s以%3$s所椎殺", "death.attack.magic": "%1$s斃於神法", "death.attack.magic.player": "去%2$s之時%1$s斃於神法", "death.attack.message_too_long": "祈蒙見恕，是訃誠甚長而弗能皆示。概如是：%s", "death.attack.mob": "%1$s爲%2$s所殺", "death.attack.mob.item": "%1$s爲%2$s以%3$s所殺", "death.attack.onFire": "%1$s灼斃", "death.attack.onFire.item": "搏%2$s持%3$s者之時%1$s灼斃", "death.attack.onFire.player": "搏%2$s之時%1$s灼斃", "death.attack.outOfWorld": "%1$s墜離是世", "death.attack.outOfWorld.player": "%1$s與%2$s不共戴天", "death.attack.outsideBorder": "%1$s遺世而獨立", "death.attack.outsideBorder.player": "搏%2$s之時%1$s遺世而獨立", "death.attack.player": "%1$s爲%2$s所殺", "death.attack.player.item": "%1$s爲%2$s以%3$s所殺", "death.attack.sonic_boom": "%1$s爲厲嘯所夷", "death.attack.sonic_boom.item": "%1$s於去持%3$s之%2$s之時為厲嘯所夷", "death.attack.sonic_boom.player": "去%2$s之時%1$s爲厲嘯所夷", "death.attack.stalagmite": "%1$s爲石筍穿", "death.attack.stalagmite.player": "搏%2$s之時%1$s爲石筍穿之", "death.attack.starve": "%1$s飢斃", "death.attack.starve.player": "搏%2$s之時%1$s飢斃", "death.attack.sting": "%1$s爲螫斃", "death.attack.sting.item": "%1$s爲%2$s以%3$s所螫斃", "death.attack.sting.player": "%1$s爲%2$s所螫斃", "death.attack.sweetBerryBush": "%1$s爲甜莓叢所刺斃", "death.attack.sweetBerryBush.player": "去%2$s之時%1$s爲甜莓叢所刺斃", "death.attack.thorns": "欲傷%2$s之時%1$s斃", "death.attack.thorns.item": "欲傷%2$s之時%1$s爲%3$s所殺", "death.attack.thrown": "%1$s爲%2$s所擲殺", "death.attack.thrown.item": "%1$s爲%2$s以%3$s所擲殺", "death.attack.trident": "%1$s爲%2$s所穿殺", "death.attack.trident.item": "%1$s爲%2$s以%3$s所穿殺", "death.attack.wither": "%1$s凋斃", "death.attack.wither.player": "搏%2$s之時%1$s凋斃", "death.attack.witherSkull": "%1$s爲%2$s之首射而斃", "death.attack.witherSkull.item": "%1$s爲%2$s以%3$s射其首而斃", "death.fell.accident.generic": "%1$s自險墜斃", "death.fell.accident.ladder": "%1$s斃於爬梯", "death.fell.accident.other_climbable": "%1$s緣壁時墜", "death.fell.accident.scaffolding": "%1$s墜於棧架", "death.fell.accident.twisting_vines": "%1$s墜於輪囷藤", "death.fell.accident.vines": "%1$s自藤墜斃", "death.fell.accident.weeping_vines": "%1$s墜於垂泣藤", "death.fell.assist": "%1$s爲%2$s所擊而墜斃", "death.fell.assist.item": "%1$s爲%2$s以%3$s所擊而墜斃", "death.fell.finish": "%1$s墜後爲%2$s所殺", "death.fell.finish.item": "%1$s墜後爲%2$s以%3$s所殺", "death.fell.killer": "%1$s受撃墜斃", "deathScreen.quit.confirm": "君確不留玉步乎？", "deathScreen.respawn": "更生", "deathScreen.score": "分數", "deathScreen.score.value": "分：%s", "deathScreen.spectate": "旁觀生界", "deathScreen.title": "子歿矣！", "deathScreen.title.hardcore": "戲終！", "deathScreen.titleScreen": "卷首", "debug.advanced_tooltips.help": "【F3並H】顯詳註", "debug.advanced_tooltips.off": "既隱詳註", "debug.advanced_tooltips.on": "既現詳注", "debug.chunk_boundaries.help": "【F3並G】顯區塊之界", "debug.chunk_boundaries.off": "區塊界既隱", "debug.chunk_boundaries.on": "區塊界既顯", "debug.clear_chat.help": "【F3並D】剟其議錄", "debug.copy_location.help": "【F3並C】複刻位爲/tp令，恆押F3並C以崩戲", "debug.copy_location.message": "既複刻位於版", "debug.crash.message": "F3並C既押。手不釋鍵則戲將崩於頃刻。", "debug.crash.warning": "戲崩於%s秒", "debug.creative_spectator.error": "未能更嬉遊之法；其權未及也", "debug.creative_spectator.help": "【F3並N】以嚮法換以觀者", "debug.dump_dynamic_textures": "既存動紋至%s", "debug.dump_dynamic_textures.help": "【F3並S】轉存動紋", "debug.gamemodes.error": "修法者不可啟；需權", "debug.gamemodes.help": "【F3並F4】啟修法者", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s次", "debug.help.help": "【F3並Q】示此列", "debug.help.message": "鍵設：", "debug.inspect.client.block": "既複刻客端之塊錄於版", "debug.inspect.client.entity": "既複刻客端之實體錄於版", "debug.inspect.help": "【F3並I】複刻實體或塊之資於版", "debug.inspect.server.block": "既複刻伺服器側之塊錄於版", "debug.inspect.server.entity": "既複刻伺服器側之實體錄於版", "debug.pause.help": "【F3並Esc】戲可止，則止之而不示選單", "debug.pause_focus.help": "【F3並P】止戲於失焦之時", "debug.pause_focus.off": "止於失焦之時既禁", "debug.pause_focus.on": "止於失焦之時既啟", "debug.prefix": "[勘誤]：", "debug.profiling.help": "【F3並L】始或止析也", "debug.profiling.start": "%s秒之析已啟。以F3並L先止之", "debug.profiling.stop": "析已盡，即存至%s已矣", "debug.reload_chunks.help": "【F3並A】復載區塊", "debug.reload_chunks.message": "區塊方悉復載", "debug.reload_resourcepacks.help": "【F3並T】復載資囊", "debug.reload_resourcepacks.message": "既復載資囊", "debug.show_hitboxes.help": "【F3並B】顯方寸匡 ", "debug.show_hitboxes.off": "方寸匡既隠", "debug.show_hitboxes.on": "方寸匡既顯", "debug.version.header": "客端版訊：", "debug.version.help": "【F3並V】顯客端版訊", "demo.day.1": "預演維五日可戲矣。盡子之至！", "demo.day.2": "翌日", "demo.day.3": "三日", "demo.day.4": "四日", "demo.day.5": "今乃試法之末日！", "demo.day.6": "五日既終。以%s存畫。", "demo.day.warning": "汝期將盡！", "demo.demoExpired": "試日終矣！", "demo.help.buy": "立賈！", "demo.help.fullWrapped": "預演維其中五日可戲（蓋實時七刻）。閱進程以得示！與子同樂！", "demo.help.inventory": "擊「%1$s」鍵以解囊", "demo.help.jump": "擊「%1$s」鍵以躍", "demo.help.later": "復戲之！", "demo.help.movement": "以%1$s、%2$s、%3$s、%4$s、滑鼠者遊四方", "demo.help.movementMouse": "以滑鼠視四方", "demo.help.movementShort": "擊「%1$s」「%2$s」「%3$s」「%4$s」鍵以行四方", "demo.help.title": "礦藝之預演", "demo.remainingTime": "餘時：%s", "demo.reminder": "試日畢，賈戲以續抑闢新生界！", "difficulty.lock.question": "誠欲錮此世之難易乎？將縛於%1$s，而無復易之法也。", "difficulty.lock.title": "錮生界之難易", "disconnect.endOfStream": "傳輸終矣", "disconnect.exceeded_packet_rate": "因濫囊而爲逐", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "蔽態之所求", "disconnect.loginFailedInfo": "登入未成，蓋因%s", "disconnect.loginFailedInfo.insufficientPrivileges": "眾戲莫允。但請察汝微軟帳戶之設。", "disconnect.loginFailedInfo.invalidSession": "是會話無效，宜復啟戲與啟者", "disconnect.loginFailedInfo.serversUnavailable": "今不可與有證之伺服者相連。望後而復之。", "disconnect.loginFailedInfo.userBanned": "已爲所羈，弗能入眾戲", "disconnect.lost": "聯線失也", "disconnect.packetError": "網契有舛", "disconnect.spam": "因濫言而爲逐", "disconnect.timeout": "連接逾時也", "disconnect.transfer": "既遷於別伺服器", "disconnect.unknownHost": "未明主機", "download.pack.failed": "%2$s之%1$s囊下傳未成", "download.pack.progress.bytes": "程：%s（廣狹未明）", "download.pack.progress.percent": "程：%s%%", "download.pack.title": "方下傳資囊%2$s之%1$s", "editGamerule.default": "預設：%s", "editGamerule.title": "纂則", "effect.duration.infinite": "無窮", "effect.minecraft.absorption": "消災", "effect.minecraft.bad_omen": "祲", "effect.minecraft.blindness": "盲", "effect.minecraft.conduit_power": "潮信之力", "effect.minecraft.darkness": "黯", "effect.minecraft.dolphins_grace": "海豚之惠", "effect.minecraft.fire_resistance": "抗火", "effect.minecraft.glowing": "耀", "effect.minecraft.haste": "急", "effect.minecraft.health_boost": "益壽", "effect.minecraft.hero_of_the_village": "鄉里英傑", "effect.minecraft.hunger": "飢", "effect.minecraft.infested": "蟫蝕", "effect.minecraft.instant_damage": "瞬傷", "effect.minecraft.instant_health": "瞬療", "effect.minecraft.invisibility": "隱", "effect.minecraft.jump_boost": "躍昇", "effect.minecraft.levitation": "浮", "effect.minecraft.luck": "幸", "effect.minecraft.mining_fatigue": "疲", "effect.minecraft.nausea": "氣逆", "effect.minecraft.night_vision": "夜視", "effect.minecraft.oozing": "滲膠", "effect.minecraft.poison": "毒", "effect.minecraft.raid_omen": "襲祲", "effect.minecraft.regeneration": "復甦", "effect.minecraft.resistance": "堅", "effect.minecraft.saturation": "飽", "effect.minecraft.slow_falling": "輕", "effect.minecraft.slowness": "緩", "effect.minecraft.speed": "速", "effect.minecraft.strength": "力", "effect.minecraft.trial_omen": "煉祲", "effect.minecraft.unluck": "蹇", "effect.minecraft.water_breathing": "水肺", "effect.minecraft.weakness": "虛", "effect.minecraft.weaving": "綴絲", "effect.minecraft.wind_charged": "厲風", "effect.minecraft.wither": "凋零", "effect.none": "無", "enchantment.level.1": "一階", "enchantment.level.10": "十階", "enchantment.level.2": "二階", "enchantment.level.3": "三階", "enchantment.level.4": "四階", "enchantment.level.5": "五階", "enchantment.level.6": "六階", "enchantment.level.7": "七階", "enchantment.level.8": "八階", "enchantment.level.9": "九階", "enchantment.minecraft.aqua_affinity": "習流", "enchantment.minecraft.bane_of_arthropods": "剋蟲", "enchantment.minecraft.binding_curse": "固咒", "enchantment.minecraft.blast_protection": "禦爆", "enchantment.minecraft.breach": "穿甲", "enchantment.minecraft.channeling": "通雷術", "enchantment.minecraft.density": "密質", "enchantment.minecraft.depth_strider": "潮湧行者", "enchantment.minecraft.efficiency": "速", "enchantment.minecraft.feather_falling": "羽落", "enchantment.minecraft.fire_aspect": "附焰", "enchantment.minecraft.fire_protection": "禦焱", "enchantment.minecraft.flame": "焰矢", "enchantment.minecraft.fortune": "運", "enchantment.minecraft.frost_walker": "履霜", "enchantment.minecraft.impaling": "穿刺", "enchantment.minecraft.infinity": "無盡", "enchantment.minecraft.knockback": "叩退", "enchantment.minecraft.looting": "奪", "enchantment.minecraft.loyalty": "忠", "enchantment.minecraft.luck_of_the_sea": "海神之助", "enchantment.minecraft.lure": "餌", "enchantment.minecraft.mending": "修繕", "enchantment.minecraft.multishot": "千影矢", "enchantment.minecraft.piercing": "貫穿", "enchantment.minecraft.power": "強弓", "enchantment.minecraft.projectile_protection": "禦彈", "enchantment.minecraft.protection": "護", "enchantment.minecraft.punch": "勁弓", "enchantment.minecraft.quick_charge": "速彀", "enchantment.minecraft.respiration": "鰓", "enchantment.minecraft.riptide": "㵗㶔", "enchantment.minecraft.sharpness": "銳", "enchantment.minecraft.silk_touch": "完璧", "enchantment.minecraft.smite": "剋亡", "enchantment.minecraft.soul_speed": "靈逸", "enchantment.minecraft.sweeping": "橫斬之刃", "enchantment.minecraft.sweeping_edge": "橫斬之刃", "enchantment.minecraft.swift_sneak": "迅伏", "enchantment.minecraft.thorns": "荊棘", "enchantment.minecraft.unbreaking": "耐久", "enchantment.minecraft.vanishing_curse": "滅咒", "enchantment.minecraft.wind_burst": "風逬", "entity.minecraft.acacia_boat": "㭜舟", "entity.minecraft.acacia_chest_boat": "㭜艚", "entity.minecraft.allay": "悅靈", "entity.minecraft.area_effect_cloud": "劑雲", "entity.minecraft.armadillo": "犰狳", "entity.minecraft.armor_stand": "甲桁", "entity.minecraft.arrow": "矢", "entity.minecraft.axolotl": "螈", "entity.minecraft.bamboo_chest_raft": "竹艚", "entity.minecraft.bamboo_raft": "竹筏", "entity.minecraft.bat": "蟙䘃", "entity.minecraft.bee": "蜂", "entity.minecraft.birch_boat": "樺舟", "entity.minecraft.birch_chest_boat": "樺艚", "entity.minecraft.blaze": "炎靈", "entity.minecraft.block_display": "塊示", "entity.minecraft.boat": "舟", "entity.minecraft.bogged": "濘髑", "entity.minecraft.breeze": "風靈", "entity.minecraft.breeze_wind_charge": "風彈", "entity.minecraft.camel": "橐駝", "entity.minecraft.cat": "貓", "entity.minecraft.cave_spider": "穴蛛", "entity.minecraft.cherry_boat": "櫻舟", "entity.minecraft.cherry_chest_boat": "櫻艚", "entity.minecraft.chest_boat": "艚", "entity.minecraft.chest_minecart": "箱礦車", "entity.minecraft.chicken": "雞", "entity.minecraft.cod": "鱈", "entity.minecraft.command_block_minecart": "命令塊礦車", "entity.minecraft.cow": "牛", "entity.minecraft.creaking": "䦪鬼", "entity.minecraft.creaking_transient": "䦪鬼", "entity.minecraft.creeper": "伏臨", "entity.minecraft.dark_oak_boat": "黯柞舟", "entity.minecraft.dark_oak_chest_boat": "黯柞艚", "entity.minecraft.dolphin": "海豚", "entity.minecraft.donkey": "驢", "entity.minecraft.dragon_fireball": "龍焱彈", "entity.minecraft.drowned": "溺屍", "entity.minecraft.egg": "擲卵", "entity.minecraft.elder_guardian": "古海衛", "entity.minecraft.end_crystal": "終界水玉", "entity.minecraft.ender_dragon": "終眇龍", "entity.minecraft.ender_pearl": "既擲終眇玥", "entity.minecraft.enderman": "終眇使", "entity.minecraft.endermite": "終眇蟎", "entity.minecraft.evoker": "御魔使", "entity.minecraft.evoker_fangs": "魔齒", "entity.minecraft.experience_bottle": "既擲淬靈甁", "entity.minecraft.experience_orb": "經驗珠", "entity.minecraft.eye_of_ender": "終眇眼", "entity.minecraft.falling_block": "墜塊", "entity.minecraft.falling_block_type": "墜%s", "entity.minecraft.fireball": "火圓", "entity.minecraft.firework_rocket": "焰火", "entity.minecraft.fishing_bobber": "氾標", "entity.minecraft.fox": "狐", "entity.minecraft.frog": "鼃", "entity.minecraft.furnace_minecart": "爐礦車", "entity.minecraft.ghast": "魄", "entity.minecraft.giant": "巨人", "entity.minecraft.glow_item_frame": "爍置具匡", "entity.minecraft.glow_squid": "爍鰂", "entity.minecraft.goat": "山羊", "entity.minecraft.guardian": "海衛", "entity.minecraft.happy_ghast": "悅魄", "entity.minecraft.hoglin": "獷豕", "entity.minecraft.hopper_minecart": "漏斗礦車", "entity.minecraft.horse": "馬", "entity.minecraft.husk": "枯屍", "entity.minecraft.illusioner": "幻術師", "entity.minecraft.interaction": "交互", "entity.minecraft.iron_golem": "鐵傀儡", "entity.minecraft.item": "物", "entity.minecraft.item_display": "物示", "entity.minecraft.item_frame": "置具匡", "entity.minecraft.jungle_boat": "叢莽舟", "entity.minecraft.jungle_chest_boat": "叢莽艚", "entity.minecraft.killer_bunny": "刺客兔", "entity.minecraft.leash_knot": "繯", "entity.minecraft.lightning_bolt": "列缺", "entity.minecraft.lingering_potion": "滯劑", "entity.minecraft.llama": "美洲駝", "entity.minecraft.llama_spit": "美洲駝涎", "entity.minecraft.magma_cube": "火漿魔", "entity.minecraft.mangrove_boat": "沒潮木舟", "entity.minecraft.mangrove_chest_boat": "沒潮木艚", "entity.minecraft.marker": "標", "entity.minecraft.minecart": "礦車", "entity.minecraft.mooshroom": "牟蕈", "entity.minecraft.mule": "騾", "entity.minecraft.oak_boat": "柞舟", "entity.minecraft.oak_chest_boat": "柞艚", "entity.minecraft.ocelot": "虎貓", "entity.minecraft.ominous_item_spawner": "厄源", "entity.minecraft.painting": "畫", "entity.minecraft.pale_oak_boat": "縞柞舟", "entity.minecraft.pale_oak_chest_boat": "縞柞艚", "entity.minecraft.panda": "貓熊", "entity.minecraft.parrot": "鸚鵡", "entity.minecraft.phantom": "魘靈", "entity.minecraft.pig": "豕", "entity.minecraft.piglin": "豕靈", "entity.minecraft.piglin_brute": "暴豕靈", "entity.minecraft.pillager": "劫寇", "entity.minecraft.player": "戲者", "entity.minecraft.polar_bear": "雪熊", "entity.minecraft.potion": "藥劑", "entity.minecraft.pufferfish": "河豚", "entity.minecraft.rabbit": "兔", "entity.minecraft.ravager": "劫獸", "entity.minecraft.salmon": "鮭", "entity.minecraft.sheep": "綿羊", "entity.minecraft.shulker": "匿贆", "entity.minecraft.shulker_bullet": "匿贆彈", "entity.minecraft.silverfish": "蟫", "entity.minecraft.skeleton": "骷髏", "entity.minecraft.skeleton_horse": "骷髏馬", "entity.minecraft.slime": "黏膠魔", "entity.minecraft.small_fireball": "小火圓", "entity.minecraft.sniffer": "嗅獸", "entity.minecraft.snow_golem": "雪傀儡", "entity.minecraft.snowball": "雪團", "entity.minecraft.spawner_minecart": "孳衍籠礦車", "entity.minecraft.spectral_arrow": "爍靈矢", "entity.minecraft.spider": "蛛", "entity.minecraft.splash_potion": "噴劑", "entity.minecraft.spruce_boat": "樅舟", "entity.minecraft.spruce_chest_boat": "樅艚", "entity.minecraft.squid": "鰂", "entity.minecraft.stray": "流髑", "entity.minecraft.strider": "熾足獸", "entity.minecraft.tadpole": "蝌蚪", "entity.minecraft.text_display": "文示", "entity.minecraft.tnt": "既燃灹藥", "entity.minecraft.tnt_minecart": "灹藥礦車", "entity.minecraft.trader_llama": "商駝", "entity.minecraft.trident": "三叉戟", "entity.minecraft.tropical_fish": "賞魚", "entity.minecraft.tropical_fish.predefined.0": "海葵魚", "entity.minecraft.tropical_fish.predefined.1": "烏鯛", "entity.minecraft.tropical_fish.predefined.10": "角蝶魚", "entity.minecraft.tropical_fish.predefined.11": "麗蝶魚", "entity.minecraft.tropical_fish.predefined.12": "鸚鵡魚", "entity.minecraft.tropical_fish.predefined.13": "額斑刺蝶魚", "entity.minecraft.tropical_fish.predefined.14": "赤慈鯛", "entity.minecraft.tropical_fish.predefined.15": "赤唇真蛇䲁", "entity.minecraft.tropical_fish.predefined.16": "赤笛鯛", "entity.minecraft.tropical_fish.predefined.17": "馬鮫", "entity.minecraft.tropical_fish.predefined.18": "鱕鯜", "entity.minecraft.tropical_fish.predefined.19": "魨鯸", "entity.minecraft.tropical_fish.predefined.2": "靛鯛", "entity.minecraft.tropical_fish.predefined.20": "黃尾鸚鵡魚", "entity.minecraft.tropical_fish.predefined.21": "黃鯛", "entity.minecraft.tropical_fish.predefined.3": "蝶鱸", "entity.minecraft.tropical_fish.predefined.4": "慈鯛", "entity.minecraft.tropical_fish.predefined.5": "醜角魚", "entity.minecraft.tropical_fish.predefined.6": "彩搏魚", "entity.minecraft.tropical_fish.predefined.7": "雀鯛", "entity.minecraft.tropical_fish.predefined.8": "皇笛鯛", "entity.minecraft.tropical_fish.predefined.9": "鬚鯛", "entity.minecraft.tropical_fish.type.betty": "碚體魚", "entity.minecraft.tropical_fish.type.blockfish": "塊魚", "entity.minecraft.tropical_fish.type.brinely": "鹵魚", "entity.minecraft.tropical_fish.type.clayfish": "陶魚", "entity.minecraft.tropical_fish.type.dasher": "翕魚", "entity.minecraft.tropical_fish.type.flopper": "翅魚", "entity.minecraft.tropical_fish.type.glitter": "光鱗魚", "entity.minecraft.tropical_fish.type.kob": "赤羚魚", "entity.minecraft.tropical_fish.type.snooper": "匿魚", "entity.minecraft.tropical_fish.type.spotty": "斑魚", "entity.minecraft.tropical_fish.type.stripey": "柴魚", "entity.minecraft.tropical_fish.type.sunstreak": "日紋魚", "entity.minecraft.turtle": "海龜", "entity.minecraft.vex": "惱鬼", "entity.minecraft.villager": "鄉民", "entity.minecraft.villager.armorer": "函師", "entity.minecraft.villager.butcher": "屠戶", "entity.minecraft.villager.cartographer": "堪輿師", "entity.minecraft.villager.cleric": "僧", "entity.minecraft.villager.farmer": "農", "entity.minecraft.villager.fisherman": "漁夫", "entity.minecraft.villager.fletcher": "箭匠", "entity.minecraft.villager.leatherworker": "韋人", "entity.minecraft.villager.librarian": "學士", "entity.minecraft.villager.mason": "石匠", "entity.minecraft.villager.nitwit": "愚者", "entity.minecraft.villager.none": "鄉民", "entity.minecraft.villager.shepherd": "羊倌", "entity.minecraft.villager.toolsmith": "工匠", "entity.minecraft.villager.weaponsmith": "戎匠", "entity.minecraft.vindicator": "斫仇者", "entity.minecraft.wandering_trader": "行商", "entity.minecraft.warden": "監守", "entity.minecraft.wind_charge": "風彈", "entity.minecraft.witch": "巫", "entity.minecraft.wither": "凋靈", "entity.minecraft.wither_skeleton": "凋靈骷髏", "entity.minecraft.wither_skull": "凋靈首", "entity.minecraft.wolf": "狼", "entity.minecraft.zoglin": "屍化獷豕", "entity.minecraft.zombie": "殭屍", "entity.minecraft.zombie_horse": "屍馬", "entity.minecraft.zombie_villager": "屍化鄉民", "entity.minecraft.zombified_piglin": "屍化豕靈", "entity.not_summonable": "召%s類實體未成", "event.minecraft.raid": "襲", "event.minecraft.raid.defeat": "敗", "event.minecraft.raid.defeat.full": "襲 - 敗", "event.minecraft.raid.raiders_remaining": "殘寇尚餘：%s", "event.minecraft.raid.victory": "捷", "event.minecraft.raid.victory.full": "襲 - 捷", "filled_map.buried_treasure": "湮寶圖", "filled_map.explorer_jungle": "探叢圖", "filled_map.explorer_swamp": "探澤圖", "filled_map.id": "第%s冊", "filled_map.level": "（級 %2$s之%1$s）", "filled_map.locked": "既鑰", "filled_map.mansion": "探林圖", "filled_map.monument": "探海圖", "filled_map.scale": "幅 %s分之一", "filled_map.trial_chambers": "探煉圖", "filled_map.unknown": "未明之圖", "filled_map.village_desert": "大漠鄉圖", "filled_map.village_plains": "原野鄉圖", "filled_map.village_savanna": "莽原鄉圖", "filled_map.village_snowy": "雪原鄉圖", "filled_map.village_taiga": "棘林鄉圖", "flat_world_preset.minecraft.bottomless_pit": "不測之淵", "flat_world_preset.minecraft.classic_flat": "上古平川", "flat_world_preset.minecraft.desert": "大漠", "flat_world_preset.minecraft.overworld": "主界", "flat_world_preset.minecraft.redstone_ready": "赤石具備", "flat_world_preset.minecraft.snowy_kingdom": "雪境", "flat_world_preset.minecraft.the_void": "太虛", "flat_world_preset.minecraft.tunnelers_dream": "礦工之夢", "flat_world_preset.minecraft.water_world": "汪洋", "flat_world_preset.unknown": "未明", "gameMode.adventure": "勇", "gameMode.changed": "嬉遊之法既更以%s", "gameMode.creative": "創", "gameMode.hardcore": "極", "gameMode.spectator": "覽", "gameMode.survival": "生", "gamerule.allowFireTicksAwayFromPlayer": "許火遠戲者而蔓", "gamerule.allowFireTicksAwayFromPlayer.description": "控火與熔石之去戲者八區塊外而蔓否。", "gamerule.announceAdvancements": "告進程", "gamerule.blockExplosionDropDecay": "塊方交互而爆者，塊或不遺其貨焉", "gamerule.blockExplosionDropDecay.description": "塊之爆也，致有遺失。", "gamerule.category.chat": "論議", "gamerule.category.drops": "落", "gamerule.category.misc": "雜物", "gamerule.category.mobs": "生靈", "gamerule.category.player": "戲者", "gamerule.category.spawning": "孳衍", "gamerule.category.updates": "生界之變", "gamerule.commandBlockOutput": "記命令塊之所出", "gamerule.commandModificationBlockLimit": "令輯塊之限", "gamerule.commandModificationBlockLimit.description": "單令如fill、clone者，一發而能改塊量之極", "gamerule.disableElytraMovementCheck": "禁翼行之測", "gamerule.disablePlayerMovementCheck": "禁戲者行之測", "gamerule.disableRaids": "禁襲", "gamerule.doDaylightCycle": "斗轉星移", "gamerule.doEntityDrops": "遺實體之被掛", "gamerule.doEntityDrops.description": "擇礦車（並其所貯）、置具匡、舟之遺物。", "gamerule.doFireTick": "更火", "gamerule.doImmediateRespawn": "立即復生", "gamerule.doInsomnia": "孳魘靈", "gamerule.doLimitedCrafting": "需製以方", "gamerule.doLimitedCrafting.description": "啟之，則戲者僅可製以既明之方者。", "gamerule.doMobLoot": "生靈亡之遺物", "gamerule.doMobLoot.description": "控生靈之所遺，經驗珠隨焉。", "gamerule.doMobSpawning": "孳生靈", "gamerule.doMobSpawning.description": "或有實體獨具其法。", "gamerule.doPatrolSpawning": "孳遊寇黨", "gamerule.doTileDrops": "塊遺", "gamerule.doTileDrops.description": "控塊方之所遺，經驗珠隨焉。", "gamerule.doTraderSpawning": "孳行商", "gamerule.doVinesSpread": "藤散", "gamerule.doVinesSpread.description": "控藤之旁蔓與否。不涉另藤如垂泣、輪囷者也。", "gamerule.doWardenSpawning": "孳監守", "gamerule.doWeatherCycle": "天象更迭", "gamerule.drowningDamage": "致溺之傷", "gamerule.enderPearlsVanishOnDeath": "擲玥亡於斃時", "gamerule.enderPearlsVanishOnDeath.description": "控戲者所擲之終眇玥隨其斃而亡與否。", "gamerule.entitiesWithPassengersCanUsePortals": "見馭者可入結界門", "gamerule.entitiesWithPassengersCanUsePortals.description": "見馭者得傳以焱界結界門、終界結界門與終界門關。", "gamerule.fallDamage": "致墮之傷", "gamerule.fireDamage": "致炎之傷", "gamerule.forgiveDeadPlayers": "赦亡之戲者", "gamerule.forgiveDeadPlayers.description": "其戲者斃于左右，而中靈平怒。", "gamerule.freezeDamage": "致凍傷", "gamerule.globalSoundEvents": "天下皆聞之事", "gamerule.globalSoundEvents.description": "有特事，如帥生，則天下皆聞也。", "gamerule.keepInventory": "亡後留行囊之物", "gamerule.lavaSourceConversion": "流熔石歸靜", "gamerule.lavaSourceConversion.description": "流熔石攝於二源，則歸於靜。", "gamerule.locatorBar": "啟戲者尋蹤欄", "gamerule.locatorBar.description": "啟之則一欄顯於幕，以明戲者之所在也。", "gamerule.logAdminCommands": "宣吏令", "gamerule.maxCommandChainLength": "令連鎖數制限", "gamerule.maxCommandChainLength.description": "施於命令塊鏈暨術。", "gamerule.maxCommandForkCount": "令行文之極", "gamerule.maxCommandForkCount.description": "如「execute as」等令之行文之極。", "gamerule.maxEntityCramming": "攘實體之界", "gamerule.minecartMaxSpeed": "礦車速極", "gamerule.minecartMaxSpeed.description": "陸行礦車之預置速極。", "gamerule.mobExplosionDropDecay": "生靈爆者，塊或不遺其貨焉", "gamerule.mobExplosionDropDecay.description": "生靈之爆斃也，致有遺失。", "gamerule.mobGriefing": "允生靈之破壞", "gamerule.naturalRegeneration": "癒", "gamerule.playersNetherPortalCreativeDelay": "創者待於焱界結界門之久暫", "gamerule.playersNetherPortalCreativeDelay.description": "創者藉焱界結界門易界立待之刻。", "gamerule.playersNetherPortalDefaultDelay": "非創者待於焱界結界門之久暫", "gamerule.playersNetherPortalDefaultDelay.description": "非創者藉焱界結界門易界立待之刻。", "gamerule.playersSleepingPercentage": "寐者之比", "gamerule.playersSleepingPercentage.description": "逾茲夜所需寐者之比。", "gamerule.projectilesCanBreakBlocks": "彈可破塊", "gamerule.projectilesCanBreakBlocks.description": "控彈可損其可損之塊與否。", "gamerule.randomTickSpeed": "天命刻之緩急", "gamerule.reducedDebugInfo": "遏勘記", "gamerule.reducedDebugInfo.description": "限勘幕之容。", "gamerule.sendCommandFeedback": "回令", "gamerule.showDeathMessages": "示訃", "gamerule.snowAccumulationHeight": "雪厚幾何", "gamerule.snowAccumulationHeight.description": "雪至厚之窮。", "gamerule.spawnChunkRadius": "生處區塊之徑", "gamerule.spawnChunkRadius.description": "主界生處四裏恆載區塊之數。", "gamerule.spawnRadius": "復生地之徑", "gamerule.spawnRadius.description": "控戲者宜生處之廣狹。", "gamerule.spectatorsGenerateChunks": "許觀者生地", "gamerule.tntExplodes": "許燃灹藥而爆", "gamerule.tntExplosionDropDecay": "灹藥爆者，塊或不遺其貨焉", "gamerule.tntExplosionDropDecay.description": "灹藥之爆也，致有遺失。", "gamerule.universalAnger": "無異之怒", "gamerule.universalAnger.description": "中靈之怒也，於近之戲者無有不攻，非限於滋事者。禁法「赦亡之戲者」以得至好之效。", "gamerule.waterSourceConversion": "流水歸靜", "gamerule.waterSourceConversion.description": "流水攝於二源，則歸於靜。", "generator.custom": "自定", "generator.customized": "古版自定", "generator.minecraft.amplified": "俁世", "generator.minecraft.amplified.info": "註：徒爲樂耳，需算械之堅敏者。", "generator.minecraft.debug_all_block_states": "勘", "generator.minecraft.flat": "平川", "generator.minecraft.large_biomes": "巨生態域", "generator.minecraft.normal": "常世", "generator.minecraft.single_biome_surface": "獨生域", "generator.single_biome_caves": "窟", "generator.single_biome_floating_islands": "浮嶼", "gui.abuseReport.attestation": "汝投此檢舉，則自證其所訴確而盡意也。", "gui.abuseReport.comments": "論", "gui.abuseReport.describe": "細述其詳，以俟斷正惡。", "gui.abuseReport.discard.content": "倘離，則是檢舉將無存也。\n確不留步乎？", "gui.abuseReport.discard.discard": "去而棄之", "gui.abuseReport.discard.draft": "存爲新稿", "gui.abuseReport.discard.return": "續纂之", "gui.abuseReport.discard.title": "棄檢舉乎？", "gui.abuseReport.draft.content": "欲改稿乎，抑另起乎？", "gui.abuseReport.draft.discard": "棄", "gui.abuseReport.draft.edit": "續纂之", "gui.abuseReport.draft.quittotitle.content": "續纂抑棄乎？", "gui.abuseReport.draft.quittotitle.title": "若去，檢舉將棄", "gui.abuseReport.draft.title": "欲改稿乎？", "gui.abuseReport.error.title": "舛見於送檢舉時", "gui.abuseReport.message": "汝見劣行於何處？\n此將助勘斷也。", "gui.abuseReport.more_comments": "請述其始末：", "gui.abuseReport.name.comment_box_label": "詳述檢舉斯名之緣：", "gui.abuseReport.name.reporting": "汝方檢舉「%s」。", "gui.abuseReport.name.title": "檢舉戲者之名", "gui.abuseReport.observed_what": "何故而檢舉之？", "gui.abuseReport.read_info": "詳聞檢舉", "gui.abuseReport.reason.alcohol_tobacco_drugs": "鴆抑酒", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "有勸他人與禁藥之事抑勸童子飲酒者。", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "狎童之侵虐", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "有言及或倡言狎童者。", "gui.abuseReport.reason.defamation_impersonation_false_information": "誹謗", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "有損人清譽，妖言以惑眾者。", "gui.abuseReport.reason.description": "情狀：", "gui.abuseReport.reason.false_reporting": "誣", "gui.abuseReport.reason.generic": "吾欲檢舉之", "gui.abuseReport.reason.generic.description": "其人其事忿恚吾。", "gui.abuseReport.reason.harassment_or_bullying": "欺侮之事", "gui.abuseReport.reason.harassment_or_bullying.description": "有辱、攻、侮汝抑他人者。不速而三擾者、洩私事之機者，亦此類也。", "gui.abuseReport.reason.hate_speech": "仇恨之言", "gui.abuseReport.reason.hate_speech.description": "有攻訐汝抑他人以信仰、族類、男女之別者。", "gui.abuseReport.reason.imminent_harm": "懾以傷人", "gui.abuseReport.reason.imminent_harm.description": "有懾以傷汝抑他人於實界也。", "gui.abuseReport.reason.narration": "%s： %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "不速之秘畫", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "或論、或示私密之圖也。", "gui.abuseReport.reason.self_harm_or_suicide": "自殘抑自戕", "gui.abuseReport.reason.self_harm_or_suicide.description": "或將自殘於實界，抑論自傷於實界也。", "gui.abuseReport.reason.sexually_inappropriate": "淫色", "gui.abuseReport.reason.sexually_inappropriate.description": "涉淫猥、陰處及淫虐之膚紋。", "gui.abuseReport.reason.terrorism_or_violent_extremism": "暴恐之屬", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "以政教之故，有言及、倡言或懾以暴恐之行者。", "gui.abuseReport.reason.title": "擇檢舉之類", "gui.abuseReport.report_sent_msg": "檢舉既受，不勝感激！\n\n將速覽之。", "gui.abuseReport.select_reason": "擇檢舉之類", "gui.abuseReport.send": "送檢舉", "gui.abuseReport.send.comment_too_long": "請略言之", "gui.abuseReport.send.error_message": "舛見於送檢舉時：\n%s", "gui.abuseReport.send.generic_error": "遇未料之舛於送檢舉時。", "gui.abuseReport.send.http_error": "方送檢舉而超文郵契生謬不虞。", "gui.abuseReport.send.json_error": "遇舛格之載於送檢舉時。", "gui.abuseReport.send.no_reason": "請擇檢舉之類", "gui.abuseReport.send.not_attested": "前文既閱，選匡既句，則檢舉可送。", "gui.abuseReport.send.service_unavailable": "達檢舉之服務未成。請檢網連，而復試之。", "gui.abuseReport.sending.title": "方送檢舉⋯", "gui.abuseReport.sent.title": "檢舉既送", "gui.abuseReport.skin.title": "檢舉戲者之外觀", "gui.abuseReport.title": "檢舉戲者", "gui.abuseReport.type.chat": "論議", "gui.abuseReport.type.name": "戲者之名", "gui.abuseReport.type.skin": "戲者之外觀", "gui.acknowledge": "知悉", "gui.advancements": "進程", "gui.all": "皆", "gui.back": "還", "gui.banned.description": "%s\n\n%s\n\n於此見其詳：%s", "gui.banned.description.permanent": "戶簿已恆爲所羈，弗能入眾戲抑領域。", "gui.banned.description.reason": "新受一檢舉曰：汝之戶簿有劣行。是案已爲所閱且已斷爲%s。其悖於《礦藝坊律》也。", "gui.banned.description.reason_id": "符：%s", "gui.banned.description.reason_id_message": "符：%s – %s", "gui.banned.description.temporary": "弗能入眾戲抑領域於%s。", "gui.banned.description.temporary.duration": "戶簿已暫爲所羈，將釋諸%s後。", "gui.banned.description.unknownreason": "新受一檢舉曰：汝之戶簿有劣行。是案已爲所閱且已斷爲悖於《礦藝坊律》也。", "gui.banned.name.description": "汝之現名「%s」有悖於坊律。汝猶可獨戲，然非更名不能入眾戲。\n\n博聞之抑申審之於是鏈：%s", "gui.banned.name.title": "名既禁於眾戲", "gui.banned.reason.defamation_impersonation_false_information": "謠以禍人", "gui.banned.reason.drugs": "言涉禁藥", "gui.banned.reason.extreme_violence_or_gore": "述現世之血屍兇惡", "gui.banned.reason.false_reporting": "九誣無實", "gui.banned.reason.fraud": "詐取訊言", "gui.banned.reason.generic_violation": "忤逆社律", "gui.banned.reason.harassment_or_bullying": "言直魯而侮", "gui.banned.reason.hate_speech": "仇歧憤論", "gui.banned.reason.hate_terrorism_notorious_figure": "倡仇惡之羣、江湖惡徒", "gui.banned.reason.imminent_harm_to_person_or_property": "欲害人損財於現世", "gui.banned.reason.nudity_or_pornography": "示淫色下作之訊", "gui.banned.reason.sexually_inappropriate": "言及淫色", "gui.banned.reason.spam_or_advertising": "濫言抑宣傳", "gui.banned.skin.description": "汝今之外觀有悖於坊律。汝可戲以預設外觀，抑自擇新貌。\n\n博聞之抑申審之於是鏈：%s", "gui.banned.skin.title": "外觀既禁", "gui.banned.title.permanent": "戶簿已恆爲所羈", "gui.banned.title.temporary": "戶簿已暫爲所羈", "gui.cancel": "罷", "gui.chatReport.comments": "論", "gui.chatReport.describe": "共享詳細情狀以助正論。", "gui.chatReport.discard.content": "倘離，則是檢舉將無存也。\n確不留步乎？", "gui.chatReport.discard.discard": "離而棄之", "gui.chatReport.discard.draft": "存爲新稿", "gui.chatReport.discard.return": "續纂之", "gui.chatReport.discard.title": "棄檢舉乎？", "gui.chatReport.draft.content": "欲改稿抑另起之？", "gui.chatReport.draft.discard": "棄", "gui.chatReport.draft.edit": "續撰", "gui.chatReport.draft.quittotitle.content": "續撰抑棄乎？", "gui.chatReport.draft.quittotitle.title": "若離此界，檢舉亦棄", "gui.chatReport.draft.title": "欲改稿乎？", "gui.chatReport.more_comments": "請述其始末：", "gui.chatReport.observed_what": "何故而檢舉乎？", "gui.chatReport.read_info": "悉檢舉以詳", "gui.chatReport.report_sent_msg": "檢舉既受，不勝感激！\n\n將速覽之。", "gui.chatReport.select_chat": "擇議言以檢舉", "gui.chatReport.select_reason": "擇檢舉之類", "gui.chatReport.selected_chat": "%s言擇以檢舉", "gui.chatReport.send": "送檢舉", "gui.chatReport.send.comments_too_long": "請略言之", "gui.chatReport.send.no_reason": "擇檢舉之類", "gui.chatReport.send.no_reported_messages": "擇欲檢舉之言或一或多", "gui.chatReport.send.too_many_messages": "擇以檢舉之論議甚多矣", "gui.chatReport.title": "檢舉戲者之論", "gui.chatSelection.context": "前後之議言將一並送之以供物證", "gui.chatSelection.fold": "蔽%s之言", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s來此論議", "gui.chatSelection.message.narrate": "%1$s在%3$s曰：%2$s", "gui.chatSelection.selected": "議言已擇%2$s之%1$s", "gui.chatSelection.title": "擇欲檢舉之言", "gui.continue": "續", "gui.copy_link_to_clipboard": "複刻連結於簿", "gui.days": "%s日", "gui.done": "畢", "gui.down": "下", "gui.entity_tooltip.type": "類： %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "既拒%s案", "gui.fileDropFailure.title": "加案未成", "gui.hours": "%s時", "gui.loadingMinecraft": "方載礦藝", "gui.minutes": "%s分", "gui.multiLineEditBox.character_limit": "%2$s之%1$s", "gui.narrate.button": "%s 鍵", "gui.narrate.editBox": "%s 欄：%s", "gui.narrate.slider": "%s 桿", "gui.narrate.tab": "%s 籤葉", "gui.no": "否", "gui.none": "無", "gui.ok": "善", "gui.open_report_dir": "啟報之綱目", "gui.proceed": "續", "gui.recipebook.moreRecipes": "右擊以覽其詳", "gui.recipebook.page": "%2$s之%1$s", "gui.recipebook.search_hint": "尋⋯", "gui.recipebook.toggleRecipes.all": "悉示", "gui.recipebook.toggleRecipes.blastable": "示可冶者", "gui.recipebook.toggleRecipes.craftable": "示可製者", "gui.recipebook.toggleRecipes.smeltable": "示可煉者", "gui.recipebook.toggleRecipes.smokable": "示可燻者", "gui.report_to_server": "報於伺服器", "gui.socialInteractions.blocking_hint": "司之於微軟簿戶", "gui.socialInteractions.empty_blocked": "無蔽於談者", "gui.socialInteractions.empty_hidden": "無匿於談者", "gui.socialInteractions.hidden_in_chat": "欲隱%s之論議", "gui.socialInteractions.hide": "隱於論議", "gui.socialInteractions.narration.hide": "隱%s之論議", "gui.socialInteractions.narration.report": "檢舉戲者%s", "gui.socialInteractions.narration.show": "現%s之論議", "gui.socialInteractions.report": "檢舉", "gui.socialInteractions.search_empty": "查無此戲者", "gui.socialInteractions.search_hint": "尋⋯", "gui.socialInteractions.server_label.multiple": "%s - %s 戲者", "gui.socialInteractions.server_label.single": "%s - %s 戲者", "gui.socialInteractions.show": "現於論議", "gui.socialInteractions.shown_in_chat": "欲見%s之論議", "gui.socialInteractions.status_blocked": "既蔽", "gui.socialInteractions.status_blocked_offline": "蔽 - 既去", "gui.socialInteractions.status_hidden": "匿", "gui.socialInteractions.status_hidden_offline": "匿 - 既去", "gui.socialInteractions.status_offline": "既去", "gui.socialInteractions.tab_all": "皆", "gui.socialInteractions.tab_blocked": "既蔽", "gui.socialInteractions.tab_hidden": "匿", "gui.socialInteractions.title": "交際", "gui.socialInteractions.tooltip.hide": "隱其論議", "gui.socialInteractions.tooltip.report": "檢舉戲者", "gui.socialInteractions.tooltip.report.disabled": "檢舉服務不可用也", "gui.socialInteractions.tooltip.report.no_messages": "戲者%s無可檢舉之論議", "gui.socialInteractions.tooltip.report.not_reportable": "檢舉戲者未成，伺服器弗能證其論議", "gui.socialInteractions.tooltip.show": "現其論議", "gui.stats": "計", "gui.toMenu": "歸伺服器之列", "gui.toRealms": "歸領域之列", "gui.toTitle": "歸卷首", "gui.toWorld": "歸生界之列", "gui.togglable_slot": "擊以禁是槽", "gui.up": "上", "gui.waitingForResponse.button.inactive": "返（%s秒）", "gui.waitingForResponse.title": "方待伺服器", "gui.yes": "然", "hanging_sign.edit": "纂懸牌文", "instrument.minecraft.admire_goat_horn": "慕", "instrument.minecraft.call_goat_horn": "啼", "instrument.minecraft.dream_goat_horn": "夢", "instrument.minecraft.feel_goat_horn": "感", "instrument.minecraft.ponder_goat_horn": "愐", "instrument.minecraft.seek_goat_horn": "覓", "instrument.minecraft.sing_goat_horn": "奏", "instrument.minecraft.yearn_goat_horn": "求", "inventory.binSlot": "毀之", "inventory.hotbarInfo": "撳%1$s與%2$s以儲捷槽", "inventory.hotbarSaved": "既儲捷槽（撳%1$s與%2$s以復）", "item.canBreak": "可破", "item.canPlace": "可置於：", "item.canUse.unknown": "未明", "item.color": "色：%s", "item.components": "%s元", "item.disabled": "既禁之物", "item.durability": "損：%2$s之%1$s", "item.dyed": "既染", "item.minecraft.acacia_boat": "㭜舟", "item.minecraft.acacia_chest_boat": "㭜艚", "item.minecraft.allay_spawn_egg": "孳悅靈之卵", "item.minecraft.amethyst_shard": "紫水玉片", "item.minecraft.angler_pottery_shard": "釣陶片", "item.minecraft.angler_pottery_sherd": "釣陶片", "item.minecraft.apple": "林檎", "item.minecraft.archer_pottery_shard": "射陶片", "item.minecraft.archer_pottery_sherd": "射陶片", "item.minecraft.armadillo_scute": "犰狳鱗", "item.minecraft.armadillo_spawn_egg": "孳犰狳之卵", "item.minecraft.armor_stand": "甲桁", "item.minecraft.arms_up_pottery_shard": "肱陶片", "item.minecraft.arms_up_pottery_sherd": "肱陶片", "item.minecraft.arrow": "矢", "item.minecraft.axolotl_bucket": "螈桶", "item.minecraft.axolotl_spawn_egg": "孳螈之卵", "item.minecraft.baked_potato": "洋芋炙", "item.minecraft.bamboo_chest_raft": "竹艚", "item.minecraft.bamboo_raft": "竹筏", "item.minecraft.bat_spawn_egg": "孳蟙䘃之卵", "item.minecraft.bee_spawn_egg": "孳蜂之卵", "item.minecraft.beef": "生牛肉", "item.minecraft.beetroot": "甘藜根", "item.minecraft.beetroot_seeds": "甘藜種", "item.minecraft.beetroot_soup": "甘藜羹", "item.minecraft.birch_boat": "樺舟", "item.minecraft.birch_chest_boat": "樺艚", "item.minecraft.black_bundle": "玄皮囊", "item.minecraft.black_dye": "黑染", "item.minecraft.black_harness": "黑轡", "item.minecraft.blade_pottery_shard": "刃陶片", "item.minecraft.blade_pottery_sherd": "刃陶片", "item.minecraft.blaze_powder": "炎靈粉", "item.minecraft.blaze_rod": "炎靈桿", "item.minecraft.blaze_spawn_egg": "孳炎靈之卵", "item.minecraft.blue_bundle": "靛皮囊", "item.minecraft.blue_dye": "靛染", "item.minecraft.blue_egg": "靛雞卵", "item.minecraft.blue_harness": "靛轡", "item.minecraft.bogged_spawn_egg": "孳濘髑之卵", "item.minecraft.bolt_armor_trim_smithing_template": "鍛模", "item.minecraft.bolt_armor_trim_smithing_template.new": "榫卯甲紋", "item.minecraft.bone": "骨", "item.minecraft.bone_meal": "骨塵", "item.minecraft.book": "書", "item.minecraft.bordure_indented_banner_pattern": "齒匡旗楷", "item.minecraft.bow": "弓", "item.minecraft.bowl": "椀", "item.minecraft.bread": "饢", "item.minecraft.breeze_rod": "風靈桿", "item.minecraft.breeze_spawn_egg": "孳風靈之卵", "item.minecraft.brewer_pottery_shard": "釀陶片", "item.minecraft.brewer_pottery_sherd": "釀陶片", "item.minecraft.brewing_stand": "煉藥臺", "item.minecraft.brick": "磚", "item.minecraft.brown_bundle": "褐皮囊", "item.minecraft.brown_dye": "褐染", "item.minecraft.brown_egg": "褐雞卵", "item.minecraft.brown_harness": "褐轡", "item.minecraft.brush": "刷", "item.minecraft.bucket": "桶", "item.minecraft.bundle": "皮囊", "item.minecraft.bundle.empty": "空", "item.minecraft.bundle.empty.description": "可納雜物一組", "item.minecraft.bundle.full": "盈", "item.minecraft.bundle.fullness": "%2$s之%1$s", "item.minecraft.burn_pottery_shard": "燊陶片", "item.minecraft.burn_pottery_sherd": "燊陶片", "item.minecraft.camel_spawn_egg": "孳橐駝之卵", "item.minecraft.carrot": "胡蘆菔", "item.minecraft.carrot_on_a_stick": "胡蘆菔釣竿", "item.minecraft.cat_spawn_egg": "孳貓之卵", "item.minecraft.cauldron": "釜", "item.minecraft.cave_spider_spawn_egg": "孳穴蛛之卵", "item.minecraft.chainmail_boots": "環鎖鞾", "item.minecraft.chainmail_chestplate": "環鎖鎧", "item.minecraft.chainmail_helmet": "環鎖胄", "item.minecraft.chainmail_leggings": "環鎖護腿", "item.minecraft.charcoal": "木炭", "item.minecraft.cherry_boat": "櫻舟", "item.minecraft.cherry_chest_boat": "櫻艚", "item.minecraft.chest_minecart": "箱礦車", "item.minecraft.chicken": "生雞肉", "item.minecraft.chicken_spawn_egg": "孳雞之卵", "item.minecraft.chorus_fruit": "頌緲果", "item.minecraft.clay_ball": "埴團", "item.minecraft.clock": "時鐘", "item.minecraft.coal": "石炭", "item.minecraft.coast_armor_trim_smithing_template": "鍛模", "item.minecraft.coast_armor_trim_smithing_template.new": "邊海甲紋", "item.minecraft.cocoa_beans": "可可荳", "item.minecraft.cod": "生鱈", "item.minecraft.cod_bucket": "鱈桶", "item.minecraft.cod_spawn_egg": "孳鱈之卵", "item.minecraft.command_block_minecart": "命令塊礦車", "item.minecraft.compass": "司南", "item.minecraft.cooked_beef": "牛炙", "item.minecraft.cooked_chicken": "雞炙", "item.minecraft.cooked_cod": "鱈炙", "item.minecraft.cooked_mutton": "羊炙", "item.minecraft.cooked_porkchop": "豕炙", "item.minecraft.cooked_rabbit": "兔炙", "item.minecraft.cooked_salmon": "鮭炙", "item.minecraft.cookie": "餠", "item.minecraft.copper_ingot": "銅錠", "item.minecraft.cow_spawn_egg": "孳牛之卵", "item.minecraft.creaking_spawn_egg": "孳䦪鬼之卵", "item.minecraft.creeper_banner_pattern": "旗楷", "item.minecraft.creeper_banner_pattern.desc": "伏臨紋", "item.minecraft.creeper_banner_pattern.new": "伏臨紋旗楷", "item.minecraft.creeper_spawn_egg": "孳伏臨之卵", "item.minecraft.crossbow": "弩", "item.minecraft.crossbow.projectile": "彈：", "item.minecraft.crossbow.projectile.multiple": "彈：%s者%s", "item.minecraft.crossbow.projectile.single": "彈：%s", "item.minecraft.cyan_bundle": "黛皮囊", "item.minecraft.cyan_dye": "黛染", "item.minecraft.cyan_harness": "黛轡", "item.minecraft.danger_pottery_shard": "殆陶片", "item.minecraft.danger_pottery_sherd": "殆陶片", "item.minecraft.dark_oak_boat": "黯柞舟", "item.minecraft.dark_oak_chest_boat": "黯柞艚", "item.minecraft.debug_stick": "勘誤棍", "item.minecraft.debug_stick.empty": "%s無品", "item.minecraft.debug_stick.select": "擇「%s」（%s）", "item.minecraft.debug_stick.update": "設「%s」爲%s", "item.minecraft.diamond": "金剛石", "item.minecraft.diamond_axe": "金剛斧", "item.minecraft.diamond_boots": "金剛鞾", "item.minecraft.diamond_chestplate": "金剛鎧", "item.minecraft.diamond_helmet": "金剛胄", "item.minecraft.diamond_hoe": "金剛鋤", "item.minecraft.diamond_horse_armor": "金剛馬甲", "item.minecraft.diamond_leggings": "金剛護腿", "item.minecraft.diamond_pickaxe": "金剛鎬", "item.minecraft.diamond_shovel": "金剛鍁", "item.minecraft.diamond_sword": "金剛劍", "item.minecraft.disc_fragment_5": "留聲盤殘片", "item.minecraft.disc_fragment_5.desc": "留聲盤 - 5", "item.minecraft.dolphin_spawn_egg": "孳海豚之卵", "item.minecraft.donkey_spawn_egg": "孳驢之卵", "item.minecraft.dragon_breath": "龍涎", "item.minecraft.dried_kelp": "乾海帶", "item.minecraft.drowned_spawn_egg": "孳溺屍之卵", "item.minecraft.dune_armor_trim_smithing_template": "鍛模", "item.minecraft.dune_armor_trim_smithing_template.new": "沙墩甲紋", "item.minecraft.echo_shard": "回音殘片", "item.minecraft.egg": "雞卵", "item.minecraft.elder_guardian_spawn_egg": "孳古海衛之卵", "item.minecraft.elytra": "翼", "item.minecraft.emerald": "祖母綠", "item.minecraft.enchanted_book": "淬靈書", "item.minecraft.enchanted_golden_apple": "淬靈金林檎", "item.minecraft.end_crystal": "終界水玉", "item.minecraft.ender_dragon_spawn_egg": "孳終眇龍之卵", "item.minecraft.ender_eye": "終眇眼", "item.minecraft.ender_pearl": "終眇玥", "item.minecraft.enderman_spawn_egg": "孳終眇使之卵", "item.minecraft.endermite_spawn_egg": "孳終眇蟎之卵", "item.minecraft.evoker_spawn_egg": "孳御魔使之卵", "item.minecraft.experience_bottle": "淬靈甁", "item.minecraft.explorer_pottery_shard": "探陶片", "item.minecraft.explorer_pottery_sherd": "探陶片", "item.minecraft.eye_armor_trim_smithing_template": "鍛模", "item.minecraft.eye_armor_trim_smithing_template.new": "瞳眸甲紋", "item.minecraft.feather": "羽", "item.minecraft.fermented_spider_eye": "酵化蛛目", "item.minecraft.field_masoned_banner_pattern": "磚紋旗楷", "item.minecraft.filled_map": "輿圖", "item.minecraft.fire_charge": "焰彈", "item.minecraft.firework_rocket": "焰火", "item.minecraft.firework_rocket.flight": "昇之時：", "item.minecraft.firework_rocket.multiple_stars": "%s乘%s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "焰火之星", "item.minecraft.firework_star.black": "黑", "item.minecraft.firework_star.blue": "靛", "item.minecraft.firework_star.brown": "褐", "item.minecraft.firework_star.custom_color": "自定", "item.minecraft.firework_star.cyan": "黛", "item.minecraft.firework_star.fade_to": "漸隱", "item.minecraft.firework_star.flicker": "爍", "item.minecraft.firework_star.gray": "灰", "item.minecraft.firework_star.green": "綠", "item.minecraft.firework_star.light_blue": "縹", "item.minecraft.firework_star.light_gray": "蒼", "item.minecraft.firework_star.lime": "翠", "item.minecraft.firework_star.magenta": "赬", "item.minecraft.firework_star.orange": "橙", "item.minecraft.firework_star.pink": "紅", "item.minecraft.firework_star.purple": "紫", "item.minecraft.firework_star.red": "赤", "item.minecraft.firework_star.shape": "未明之狀", "item.minecraft.firework_star.shape.burst": "爆裂", "item.minecraft.firework_star.shape.creeper": "伏臨狀", "item.minecraft.firework_star.shape.large_ball": "大圓狀", "item.minecraft.firework_star.shape.small_ball": "小圓狀", "item.minecraft.firework_star.shape.star": "星狀", "item.minecraft.firework_star.trail": "跡", "item.minecraft.firework_star.white": "白", "item.minecraft.firework_star.yellow": "黃", "item.minecraft.fishing_rod": "漁竿", "item.minecraft.flint": "燧石", "item.minecraft.flint_and_steel": "燧鐮", "item.minecraft.flow_armor_trim_smithing_template": "鍛模", "item.minecraft.flow_armor_trim_smithing_template.new": "氣洄甲紋", "item.minecraft.flow_banner_pattern": "旗楷", "item.minecraft.flow_banner_pattern.desc": "氣洄紋", "item.minecraft.flow_banner_pattern.new": "氣洄紋旗楷", "item.minecraft.flow_pottery_sherd": "湍陶片", "item.minecraft.flower_banner_pattern": "旗楷", "item.minecraft.flower_banner_pattern.desc": "菊紋", "item.minecraft.flower_banner_pattern.new": "花紋旗楷", "item.minecraft.flower_pot": "盆", "item.minecraft.fox_spawn_egg": "孳狐之卵", "item.minecraft.friend_pottery_shard": "友陶片", "item.minecraft.friend_pottery_sherd": "友陶片", "item.minecraft.frog_spawn_egg": "孳鼃之卵", "item.minecraft.furnace_minecart": "爐礦車", "item.minecraft.ghast_spawn_egg": "孳魄之卵", "item.minecraft.ghast_tear": "魄淚", "item.minecraft.glass_bottle": "琉璃甁", "item.minecraft.glistering_melon_slice": "爍寒瓜片", "item.minecraft.globe_banner_pattern": "旗楷", "item.minecraft.globe_banner_pattern.desc": "坤輿紋", "item.minecraft.globe_banner_pattern.new": "坤輿紋旗楷", "item.minecraft.glow_berries": "爍莓", "item.minecraft.glow_ink_sac": "爍墨", "item.minecraft.glow_item_frame": "爍置具匡", "item.minecraft.glow_squid_spawn_egg": "孳爍鰂之卵", "item.minecraft.glowstone_dust": "硄砂", "item.minecraft.goat_horn": "山羊角", "item.minecraft.goat_spawn_egg": "孳山羊之卵", "item.minecraft.gold_ingot": "金錠", "item.minecraft.gold_nugget": "金粒", "item.minecraft.golden_apple": "金林檎", "item.minecraft.golden_axe": "金斧", "item.minecraft.golden_boots": "金鞾", "item.minecraft.golden_carrot": "金胡蘆菔", "item.minecraft.golden_chestplate": "金鎧", "item.minecraft.golden_helmet": "金胄", "item.minecraft.golden_hoe": "金鋤", "item.minecraft.golden_horse_armor": "金馬甲", "item.minecraft.golden_leggings": "金護腿", "item.minecraft.golden_pickaxe": "金鎬", "item.minecraft.golden_shovel": "金鍁", "item.minecraft.golden_sword": "金劍", "item.minecraft.gray_bundle": "灰皮囊", "item.minecraft.gray_dye": "灰染", "item.minecraft.gray_harness": "灰轡", "item.minecraft.green_bundle": "綠皮囊", "item.minecraft.green_dye": "綠染", "item.minecraft.green_harness": "綠轡", "item.minecraft.guardian_spawn_egg": "孳海衛之卵", "item.minecraft.gunpowder": "火藥", "item.minecraft.guster_banner_pattern": "旗楷", "item.minecraft.guster_banner_pattern.desc": "飆紋", "item.minecraft.guster_banner_pattern.new": "飆紋旗楷", "item.minecraft.guster_pottery_sherd": "飆陶片", "item.minecraft.happy_ghast_spawn_egg": "孳悅魄之卵", "item.minecraft.harness": "轡", "item.minecraft.heart_of_the_sea": "海之心", "item.minecraft.heart_pottery_shard": "心陶片", "item.minecraft.heart_pottery_sherd": "心陶片", "item.minecraft.heartbreak_pottery_shard": "慟陶片", "item.minecraft.heartbreak_pottery_sherd": "慟陶片", "item.minecraft.hoglin_spawn_egg": "孳獷豕之卵", "item.minecraft.honey_bottle": "蜜甁", "item.minecraft.honeycomb": "蜜脾", "item.minecraft.hopper_minecart": "漏斗礦車", "item.minecraft.horse_spawn_egg": "孳馬之卵", "item.minecraft.host_armor_trim_smithing_template": "鍛模", "item.minecraft.host_armor_trim_smithing_template.new": "佃東甲紋", "item.minecraft.howl_pottery_shard": "嚎陶片", "item.minecraft.howl_pottery_sherd": "嚎陶片", "item.minecraft.husk_spawn_egg": "孳枯屍之卵", "item.minecraft.ink_sac": "墨", "item.minecraft.iron_axe": "鐵斧", "item.minecraft.iron_boots": "鐵鞾", "item.minecraft.iron_chestplate": "鐵鎧", "item.minecraft.iron_golem_spawn_egg": "孳鐵傀儡之卵", "item.minecraft.iron_helmet": "鐵胄", "item.minecraft.iron_hoe": "鐵鋤", "item.minecraft.iron_horse_armor": "鐵馬甲", "item.minecraft.iron_ingot": "鐵錠", "item.minecraft.iron_leggings": "鐵護腿", "item.minecraft.iron_nugget": "鐵粒", "item.minecraft.iron_pickaxe": "鐵鎬", "item.minecraft.iron_shovel": "鐵鍁", "item.minecraft.iron_sword": "鐵劍", "item.minecraft.item_frame": "置具匡", "item.minecraft.jungle_boat": "叢莽舟", "item.minecraft.jungle_chest_boat": "叢莽艚", "item.minecraft.knowledge_book": "天工開物", "item.minecraft.lapis_lazuli": "群青", "item.minecraft.lava_bucket": "熔石桶", "item.minecraft.lead": "韁", "item.minecraft.leather": "革", "item.minecraft.leather_boots": "革鞾", "item.minecraft.leather_chestplate": "革衣", "item.minecraft.leather_helmet": "革帽", "item.minecraft.leather_horse_armor": "皮馬甲", "item.minecraft.leather_leggings": "革褲", "item.minecraft.light_blue_bundle": "縹皮囊", "item.minecraft.light_blue_dye": "縹染", "item.minecraft.light_blue_harness": "縹轡", "item.minecraft.light_gray_bundle": "蒼皮囊", "item.minecraft.light_gray_dye": "蒼染", "item.minecraft.light_gray_harness": "蒼轡", "item.minecraft.lime_bundle": "翠皮囊", "item.minecraft.lime_dye": "翠染", "item.minecraft.lime_harness": "翠轡", "item.minecraft.lingering_potion": "滯劑", "item.minecraft.lingering_potion.effect.awkward": "粗滯劑", "item.minecraft.lingering_potion.effect.empty": "空滯劑", "item.minecraft.lingering_potion.effect.fire_resistance": "抗火滯劑", "item.minecraft.lingering_potion.effect.harming": "瞬傷滯劑", "item.minecraft.lingering_potion.effect.healing": "瞬療滯劑", "item.minecraft.lingering_potion.effect.infested": "蟫蝕滯劑", "item.minecraft.lingering_potion.effect.invisibility": "隱滯劑", "item.minecraft.lingering_potion.effect.leaping": "捷滯劑", "item.minecraft.lingering_potion.effect.levitation": "浮滯劑", "item.minecraft.lingering_potion.effect.luck": "幸滯劑", "item.minecraft.lingering_potion.effect.mundane": "凡滯劑", "item.minecraft.lingering_potion.effect.night_vision": "夜視滯劑", "item.minecraft.lingering_potion.effect.oozing": "滲膠滯劑", "item.minecraft.lingering_potion.effect.poison": "毒滯劑", "item.minecraft.lingering_potion.effect.regeneration": "甦滯劑", "item.minecraft.lingering_potion.effect.slow_falling": "輕滯劑", "item.minecraft.lingering_potion.effect.slowness": "緩滯劑", "item.minecraft.lingering_potion.effect.strength": "力滯劑", "item.minecraft.lingering_potion.effect.swiftness": "速滯劑", "item.minecraft.lingering_potion.effect.thick": "濁滯劑", "item.minecraft.lingering_potion.effect.turtle_master": "龜仙滯劑", "item.minecraft.lingering_potion.effect.water": "水滯劑", "item.minecraft.lingering_potion.effect.water_breathing": "水肺滯劑", "item.minecraft.lingering_potion.effect.weakness": "虛滯劑", "item.minecraft.lingering_potion.effect.weaving": "綴絲滯劑", "item.minecraft.lingering_potion.effect.wind_charged": "厲風滯劑", "item.minecraft.llama_spawn_egg": "孳美洲駝之卵", "item.minecraft.lodestone_compass": "礠石司南", "item.minecraft.mace": "椎", "item.minecraft.magenta_bundle": "赬皮囊", "item.minecraft.magenta_dye": "赬染", "item.minecraft.magenta_harness": "赬轡", "item.minecraft.magma_cream": "火漿膏", "item.minecraft.magma_cube_spawn_egg": "孳火漿魔之卵", "item.minecraft.mangrove_boat": "沒潮木舟", "item.minecraft.mangrove_chest_boat": "沒潮木艚", "item.minecraft.map": "空圖", "item.minecraft.melon_seeds": "寒瓜種", "item.minecraft.melon_slice": "寒瓜片", "item.minecraft.milk_bucket": "乳桶", "item.minecraft.minecart": "礦車", "item.minecraft.miner_pottery_shard": "礦陶片", "item.minecraft.miner_pottery_sherd": "礦陶片", "item.minecraft.mojang_banner_pattern": "旗楷", "item.minecraft.mojang_banner_pattern.desc": "魔贊紋", "item.minecraft.mojang_banner_pattern.new": "魔贊紋旗楷", "item.minecraft.mooshroom_spawn_egg": "孳牟蕈之卵", "item.minecraft.mourner_pottery_shard": "悲陶片", "item.minecraft.mourner_pottery_sherd": "悲陶片", "item.minecraft.mule_spawn_egg": "孳騾之卵", "item.minecraft.mushroom_stew": "蕈羹", "item.minecraft.music_disc_11": "留聲盤", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "留聲盤", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "留聲盤", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "留聲盤", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "留聲盤", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "留聲盤", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "留聲盤", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "留聲盤", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> （五音匣）", "item.minecraft.music_disc_far": "留聲盤", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "留聲盤", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "留聲盤", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "留聲盤", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "留聲盤", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "留聲盤", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "留聲盤", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "留聲盤", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "留聲盤", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "留聲盤", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "留聲盤", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "留聲盤", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "留聲盤", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "生羊肉", "item.minecraft.name_tag": "名刺", "item.minecraft.nautilus_shell": "鸚鵡螺殼", "item.minecraft.nether_brick": "焱界磚", "item.minecraft.nether_star": "焱界之星", "item.minecraft.nether_wart": "焱界疣", "item.minecraft.netherite_axe": "玄鈺斧", "item.minecraft.netherite_boots": "玄鈺鞾", "item.minecraft.netherite_chestplate": "玄鈺鎧", "item.minecraft.netherite_helmet": "玄鈺胄", "item.minecraft.netherite_hoe": "玄鈺鋤", "item.minecraft.netherite_ingot": "玄鈺錠", "item.minecraft.netherite_leggings": "玄鈺護腿", "item.minecraft.netherite_pickaxe": "玄鈺鎬", "item.minecraft.netherite_scrap": "玄鈺殘片", "item.minecraft.netherite_shovel": "玄鈺鍁", "item.minecraft.netherite_sword": "玄鈺劍", "item.minecraft.netherite_upgrade_smithing_template": "鍛模", "item.minecraft.netherite_upgrade_smithing_template.new": "玄鈺之昇", "item.minecraft.oak_boat": "柞舟", "item.minecraft.oak_chest_boat": "柞艚", "item.minecraft.ocelot_spawn_egg": "孳虎貓之卵", "item.minecraft.ominous_bottle": "厄甁", "item.minecraft.ominous_trial_key": "厄煉管", "item.minecraft.orange_bundle": "橙皮囊", "item.minecraft.orange_dye": "橙染", "item.minecraft.orange_harness": "橙轡", "item.minecraft.painting": "畫", "item.minecraft.pale_oak_boat": "縞柞舟", "item.minecraft.pale_oak_chest_boat": "縞柞艚", "item.minecraft.panda_spawn_egg": "孳貓熊之卵", "item.minecraft.paper": "紙", "item.minecraft.parrot_spawn_egg": "孳鸚鵡之卵", "item.minecraft.phantom_membrane": "魘靈膜", "item.minecraft.phantom_spawn_egg": "孳魘靈之卵", "item.minecraft.pig_spawn_egg": "孳豕之卵", "item.minecraft.piglin_banner_pattern": "旗楷", "item.minecraft.piglin_banner_pattern.desc": "豕鼻紋", "item.minecraft.piglin_banner_pattern.new": "豕鼻紋旗楷", "item.minecraft.piglin_brute_spawn_egg": "孳暴豕靈之卵", "item.minecraft.piglin_spawn_egg": "孳豕靈之卵", "item.minecraft.pillager_spawn_egg": "孳劫寇之卵", "item.minecraft.pink_bundle": "紅皮囊", "item.minecraft.pink_dye": "紅染", "item.minecraft.pink_harness": "紅轡", "item.minecraft.pitcher_plant": "小人蘭", "item.minecraft.pitcher_pod": "小人蘭莢", "item.minecraft.plenty_pottery_shard": "稷陶片", "item.minecraft.plenty_pottery_sherd": "稷陶片", "item.minecraft.poisonous_potato": "毒洋芋", "item.minecraft.polar_bear_spawn_egg": "孳雪熊之卵", "item.minecraft.popped_chorus_fruit": "裂頌緲果", "item.minecraft.porkchop": "生豕肉", "item.minecraft.potato": "洋芋", "item.minecraft.potion": "藥劑", "item.minecraft.potion.effect.awkward": "粗劑", "item.minecraft.potion.effect.empty": "空劑", "item.minecraft.potion.effect.fire_resistance": "抗火之劑", "item.minecraft.potion.effect.harming": "瞬傷劑", "item.minecraft.potion.effect.healing": "瞬療劑", "item.minecraft.potion.effect.infested": "蟫蝕劑", "item.minecraft.potion.effect.invisibility": "隱劑", "item.minecraft.potion.effect.leaping": "捷劑", "item.minecraft.potion.effect.levitation": "浮劑", "item.minecraft.potion.effect.luck": "幸劑", "item.minecraft.potion.effect.mundane": "凡劑", "item.minecraft.potion.effect.night_vision": "夜視劑", "item.minecraft.potion.effect.oozing": "滲膠劑", "item.minecraft.potion.effect.poison": "毒劑", "item.minecraft.potion.effect.regeneration": "甦劑", "item.minecraft.potion.effect.slow_falling": "輕劑", "item.minecraft.potion.effect.slowness": "緩劑", "item.minecraft.potion.effect.strength": "力劑", "item.minecraft.potion.effect.swiftness": "速劑", "item.minecraft.potion.effect.thick": "濁劑", "item.minecraft.potion.effect.turtle_master": "龜仙之劑", "item.minecraft.potion.effect.water": "水甁", "item.minecraft.potion.effect.water_breathing": "水肺劑", "item.minecraft.potion.effect.weakness": "虛劑", "item.minecraft.potion.effect.weaving": "綴絲劑", "item.minecraft.potion.effect.wind_charged": "厲風劑", "item.minecraft.pottery_shard_archer": "射陶片", "item.minecraft.pottery_shard_arms_up": "肱陶片", "item.minecraft.pottery_shard_prize": "寳陶片", "item.minecraft.pottery_shard_skull": "顱陶片", "item.minecraft.powder_snow_bucket": "齏雪桶", "item.minecraft.prismarine_crystals": "海磷晶", "item.minecraft.prismarine_shard": "海磷殘片", "item.minecraft.prize_pottery_shard": "寳陶片", "item.minecraft.prize_pottery_sherd": "寳陶片", "item.minecraft.pufferfish": "河豚", "item.minecraft.pufferfish_bucket": "河豚桶", "item.minecraft.pufferfish_spawn_egg": "孳河豚之卵", "item.minecraft.pumpkin_pie": "南瓜餠", "item.minecraft.pumpkin_seeds": "南瓜種", "item.minecraft.purple_bundle": "紫皮囊", "item.minecraft.purple_dye": "紫染", "item.minecraft.purple_harness": "紫轡", "item.minecraft.quartz": "石英", "item.minecraft.rabbit": "生兔肉", "item.minecraft.rabbit_foot": "兔足", "item.minecraft.rabbit_hide": "兔皮", "item.minecraft.rabbit_spawn_egg": "孳兔之卵", "item.minecraft.rabbit_stew": "兔肉羹", "item.minecraft.raiser_armor_trim_smithing_template": "鍛模", "item.minecraft.raiser_armor_trim_smithing_template.new": "牧民甲紋", "item.minecraft.ravager_spawn_egg": "孳劫獸之卵", "item.minecraft.raw_copper": "銅璞", "item.minecraft.raw_gold": "馬蹄金", "item.minecraft.raw_iron": "砂鐵", "item.minecraft.recovery_compass": "溯魂司南", "item.minecraft.red_bundle": "赤皮囊", "item.minecraft.red_dye": "赤染", "item.minecraft.red_harness": "赤轡", "item.minecraft.redstone": "赤石末", "item.minecraft.resin_brick": "樹香磚", "item.minecraft.resin_clump": "樹香", "item.minecraft.rib_armor_trim_smithing_template": "鍛模", "item.minecraft.rib_armor_trim_smithing_template.new": "脅肋甲紋", "item.minecraft.rotten_flesh": "胔", "item.minecraft.saddle": "鞍韉", "item.minecraft.salmon": "生鮭", "item.minecraft.salmon_bucket": "鮭桶", "item.minecraft.salmon_spawn_egg": "孳鮭之卵", "item.minecraft.scrape_pottery_sherd": "削陶片", "item.minecraft.scute": "鱗", "item.minecraft.sentry_armor_trim_smithing_template": "鍛模", "item.minecraft.sentry_armor_trim_smithing_template.new": "斥候甲紋", "item.minecraft.shaper_armor_trim_smithing_template": "鍛模", "item.minecraft.shaper_armor_trim_smithing_template.new": "巧匠甲紋", "item.minecraft.sheaf_pottery_shard": "秉陶片", "item.minecraft.sheaf_pottery_sherd": "秉陶片", "item.minecraft.shears": "鉸", "item.minecraft.sheep_spawn_egg": "孳綿羊之卵", "item.minecraft.shelter_pottery_shard": "蔭陶片", "item.minecraft.shelter_pottery_sherd": "蔭陶片", "item.minecraft.shield": "盾", "item.minecraft.shield.black": "黑盾", "item.minecraft.shield.blue": "靛盾", "item.minecraft.shield.brown": "褐盾", "item.minecraft.shield.cyan": "黛盾", "item.minecraft.shield.gray": "灰盾", "item.minecraft.shield.green": "綠盾", "item.minecraft.shield.light_blue": "縹盾", "item.minecraft.shield.light_gray": "蒼盾", "item.minecraft.shield.lime": "翠盾", "item.minecraft.shield.magenta": "赬盾", "item.minecraft.shield.orange": "橙盾", "item.minecraft.shield.pink": "紅盾", "item.minecraft.shield.purple": "紫盾", "item.minecraft.shield.red": "赤盾", "item.minecraft.shield.white": "白盾", "item.minecraft.shield.yellow": "黃盾", "item.minecraft.shulker_shell": "匿贆殼", "item.minecraft.shulker_spawn_egg": "孳匿贆之卵", "item.minecraft.sign": "牌", "item.minecraft.silence_armor_trim_smithing_template": "鍛模", "item.minecraft.silence_armor_trim_smithing_template.new": "寂靜甲紋", "item.minecraft.silverfish_spawn_egg": "孳蟫之卵", "item.minecraft.skeleton_horse_spawn_egg": "孳骷髏馬之卵", "item.minecraft.skeleton_spawn_egg": "孳骷髏之卵", "item.minecraft.skull_banner_pattern": "旗楷", "item.minecraft.skull_banner_pattern.desc": "髑顱紋", "item.minecraft.skull_banner_pattern.new": "髑顱紋旗楷", "item.minecraft.skull_pottery_shard": "顱陶片", "item.minecraft.skull_pottery_sherd": "顱陶片", "item.minecraft.slime_ball": "黏膠球", "item.minecraft.slime_spawn_egg": "孳黏膠魔之卵", "item.minecraft.smithing_template": "鍛模", "item.minecraft.smithing_template.applies_to": "適於：", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "置鑄錠晶石", "item.minecraft.smithing_template.armor_trim.applies_to": "甲胄", "item.minecraft.smithing_template.armor_trim.base_slot_description": "置甲胄", "item.minecraft.smithing_template.armor_trim.ingredients": "鑄錠晶石", "item.minecraft.smithing_template.ingredients": "原料：", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "置玄鈺錠", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "金剛石兵革", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "置金剛石兵革器具", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "玄鈺錠", "item.minecraft.smithing_template.upgrade": "昇：", "item.minecraft.sniffer_spawn_egg": "孳嗅獸之卵", "item.minecraft.snort_pottery_shard": "嗅陶片", "item.minecraft.snort_pottery_sherd": "嗅陶片", "item.minecraft.snout_armor_trim_smithing_template": "鍛模", "item.minecraft.snout_armor_trim_smithing_template.new": "豕鼻甲紋", "item.minecraft.snow_golem_spawn_egg": "孳雪傀儡之卵", "item.minecraft.snowball": "雪團", "item.minecraft.spectral_arrow": "爍靈矢", "item.minecraft.spider_eye": "蛛目", "item.minecraft.spider_spawn_egg": "孳蛛之卵", "item.minecraft.spire_armor_trim_smithing_template": "鍛模", "item.minecraft.spire_armor_trim_smithing_template.new": "旋塔甲紋", "item.minecraft.splash_potion": "噴劑", "item.minecraft.splash_potion.effect.awkward": "粗噴劑", "item.minecraft.splash_potion.effect.empty": "空噴劑", "item.minecraft.splash_potion.effect.fire_resistance": "抗火之噴劑", "item.minecraft.splash_potion.effect.harming": "瞬傷噴劑", "item.minecraft.splash_potion.effect.healing": "瞬療噴劑", "item.minecraft.splash_potion.effect.infested": "蟫蝕噴劑", "item.minecraft.splash_potion.effect.invisibility": "隱噴劑", "item.minecraft.splash_potion.effect.leaping": "捷噴劑", "item.minecraft.splash_potion.effect.levitation": "浮噴劑", "item.minecraft.splash_potion.effect.luck": "幸噴劑", "item.minecraft.splash_potion.effect.mundane": "凡噴劑", "item.minecraft.splash_potion.effect.night_vision": "夜視噴劑", "item.minecraft.splash_potion.effect.oozing": "滲膠噴劑", "item.minecraft.splash_potion.effect.poison": "毒噴劑", "item.minecraft.splash_potion.effect.regeneration": "甦噴劑", "item.minecraft.splash_potion.effect.slow_falling": "輕噴劑", "item.minecraft.splash_potion.effect.slowness": "緩噴劑", "item.minecraft.splash_potion.effect.strength": "力噴劑", "item.minecraft.splash_potion.effect.swiftness": "速噴劑", "item.minecraft.splash_potion.effect.thick": "濁噴劑", "item.minecraft.splash_potion.effect.turtle_master": "龜仙噴劑", "item.minecraft.splash_potion.effect.water": "水噴劑", "item.minecraft.splash_potion.effect.water_breathing": "水肺噴劑", "item.minecraft.splash_potion.effect.weakness": "虛噴劑", "item.minecraft.splash_potion.effect.weaving": "綴絲噴劑", "item.minecraft.splash_potion.effect.wind_charged": "厲風噴劑", "item.minecraft.spruce_boat": "樅舟", "item.minecraft.spruce_chest_boat": "樅艚", "item.minecraft.spyglass": "望遠鏡", "item.minecraft.squid_spawn_egg": "孳鰂之卵", "item.minecraft.stick": "棍", "item.minecraft.stone_axe": "石斧", "item.minecraft.stone_hoe": "石鋤", "item.minecraft.stone_pickaxe": "石鎬", "item.minecraft.stone_shovel": "石鍁", "item.minecraft.stone_sword": "石劍", "item.minecraft.stray_spawn_egg": "孳流髑之卵", "item.minecraft.strider_spawn_egg": "孳熾足獸之卵", "item.minecraft.string": "綫", "item.minecraft.sugar": "糖", "item.minecraft.suspicious_stew": "謎羹", "item.minecraft.sweet_berries": "甜莓", "item.minecraft.tadpole_bucket": "蝌蚪桶", "item.minecraft.tadpole_spawn_egg": "孳蝌蚪之卵", "item.minecraft.tide_armor_trim_smithing_template": "鍛模", "item.minecraft.tide_armor_trim_smithing_template.new": "潮汐甲紋", "item.minecraft.tipped_arrow": "藥矢", "item.minecraft.tipped_arrow.effect.awkward": "藥矢", "item.minecraft.tipped_arrow.effect.empty": "空藥矢", "item.minecraft.tipped_arrow.effect.fire_resistance": "抗火矢", "item.minecraft.tipped_arrow.effect.harming": "瞬傷矢", "item.minecraft.tipped_arrow.effect.healing": "瞬療矢", "item.minecraft.tipped_arrow.effect.infested": "蟫蝕矢", "item.minecraft.tipped_arrow.effect.invisibility": "隱矢", "item.minecraft.tipped_arrow.effect.leaping": "捷矢", "item.minecraft.tipped_arrow.effect.levitation": "浮矢", "item.minecraft.tipped_arrow.effect.luck": "幸矢", "item.minecraft.tipped_arrow.effect.mundane": "藥矢", "item.minecraft.tipped_arrow.effect.night_vision": "夜視矢", "item.minecraft.tipped_arrow.effect.oozing": "滲膠矢", "item.minecraft.tipped_arrow.effect.poison": "毒矢", "item.minecraft.tipped_arrow.effect.regeneration": "甦矢", "item.minecraft.tipped_arrow.effect.slow_falling": "輕矢", "item.minecraft.tipped_arrow.effect.slowness": "緩矢", "item.minecraft.tipped_arrow.effect.strength": "力矢", "item.minecraft.tipped_arrow.effect.swiftness": "速矢", "item.minecraft.tipped_arrow.effect.thick": "藥矢", "item.minecraft.tipped_arrow.effect.turtle_master": "龜仙之矢", "item.minecraft.tipped_arrow.effect.water": "噴矢", "item.minecraft.tipped_arrow.effect.water_breathing": "水肺矢", "item.minecraft.tipped_arrow.effect.weakness": "虛矢", "item.minecraft.tipped_arrow.effect.weaving": "綴絲矢", "item.minecraft.tipped_arrow.effect.wind_charged": "厲風矢", "item.minecraft.tnt_minecart": "灹藥礦車", "item.minecraft.torchflower_seeds": "炬蓮種", "item.minecraft.totem_of_undying": "保命符", "item.minecraft.trader_llama_spawn_egg": "孳商駝之卵", "item.minecraft.trial_key": "煉管", "item.minecraft.trident": "三叉戟", "item.minecraft.tropical_fish": "賞魚", "item.minecraft.tropical_fish_bucket": "賞魚桶", "item.minecraft.tropical_fish_spawn_egg": "孳賞魚之卵", "item.minecraft.turtle_helmet": "龜殼", "item.minecraft.turtle_scute": "海龜鱗", "item.minecraft.turtle_spawn_egg": "孳海龜之卵", "item.minecraft.vex_armor_trim_smithing_template": "鍛模", "item.minecraft.vex_armor_trim_smithing_template.new": "惱鬼甲紋", "item.minecraft.vex_spawn_egg": "孳惱鬼之卵", "item.minecraft.villager_spawn_egg": "孳鄉民之卵", "item.minecraft.vindicator_spawn_egg": "孳斫仇者之卵", "item.minecraft.wandering_trader_spawn_egg": "孳行商之卵", "item.minecraft.ward_armor_trim_smithing_template": "鍛模", "item.minecraft.ward_armor_trim_smithing_template.new": "監守甲紋", "item.minecraft.warden_spawn_egg": "孳監守之卵", "item.minecraft.warped_fungus_on_a_stick": "譎蕈釣竿", "item.minecraft.water_bucket": "水桶", "item.minecraft.wayfinder_armor_trim_smithing_template": "鍛模", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "嚮導甲紋", "item.minecraft.wheat": "麥", "item.minecraft.wheat_seeds": "麥種", "item.minecraft.white_bundle": "素皮囊", "item.minecraft.white_dye": "白染", "item.minecraft.white_harness": "白轡", "item.minecraft.wild_armor_trim_smithing_template": "鍛模", "item.minecraft.wild_armor_trim_smithing_template.new": "林野甲紋", "item.minecraft.wind_charge": "風彈", "item.minecraft.witch_spawn_egg": "孳巫之卵", "item.minecraft.wither_skeleton_spawn_egg": "孳凋靈骷髏之卵", "item.minecraft.wither_spawn_egg": "孳凋靈之卵", "item.minecraft.wolf_armor": "狼甲", "item.minecraft.wolf_spawn_egg": "孳狼之卵", "item.minecraft.wooden_axe": "木斧", "item.minecraft.wooden_hoe": "木鋤", "item.minecraft.wooden_pickaxe": "木鎬", "item.minecraft.wooden_shovel": "木鍁", "item.minecraft.wooden_sword": "木劍", "item.minecraft.writable_book": "書與筆", "item.minecraft.written_book": "成書", "item.minecraft.yellow_bundle": "黃皮囊", "item.minecraft.yellow_dye": "黃染", "item.minecraft.yellow_harness": "黃轡", "item.minecraft.zoglin_spawn_egg": "孳屍化獷豕之卵", "item.minecraft.zombie_horse_spawn_egg": "孳屍馬之卵", "item.minecraft.zombie_spawn_egg": "孳殭屍之卵", "item.minecraft.zombie_villager_spawn_egg": "孳屍化鄉民之卵", "item.minecraft.zombified_piglin_spawn_egg": "孳屍化豕靈之卵", "item.modifiers.any": "著時：", "item.modifiers.armor": "衣時：", "item.modifiers.body": "著時：", "item.modifiers.chest": "於身：", "item.modifiers.feet": "於足：", "item.modifiers.hand": "執時：", "item.modifiers.head": "於首：", "item.modifiers.legs": "於腿：", "item.modifiers.mainhand": "於利手：", "item.modifiers.offhand": "於非利手：", "item.modifiers.saddle": "著鞍時：", "item.nbt_tags": "NBT籤有%s", "item.op_block_warning.line1": "誡：", "item.op_block_warning.line2": "施此物或致行令", "item.op_block_warning.line3": "未審其實，慎勿擅之！", "item.unbreakable": "不壞", "itemGroup.buildingBlocks": "營造之塊", "itemGroup.coloredBlocks": "彩塊", "itemGroup.combat": "戰鬥", "itemGroup.consumables": "耗材", "itemGroup.crafting": "製材", "itemGroup.foodAndDrink": "酒食", "itemGroup.functional": "功用之塊", "itemGroup.hotbar": "既儲捷槽", "itemGroup.ingredients": "原料", "itemGroup.inventory": "行囊", "itemGroup.natural": "天地之塊", "itemGroup.op": "吏之物", "itemGroup.redstone": "赤石之塊", "itemGroup.search": "尋物", "itemGroup.spawnEggs": "孳卵", "itemGroup.tools": "器具", "item_modifier.unknown": "未明物綴：%s", "jigsaw_block.final_state": "更作：", "jigsaw_block.generate": "生", "jigsaw_block.joint.aligned": "既定", "jigsaw_block.joint.rollable": "可旋", "jigsaw_block.joint_label": "聯之類：", "jigsaw_block.keep_jigsaws": "儲榫卯", "jigsaw_block.levels": "%s層", "jigsaw_block.name": "名曰：", "jigsaw_block.placement_priority": "置之所先：", "jigsaw_block.placement_priority.tooltip": "斯榫卯塊既縛於一體，此即體理相接於宏圖之次第。\n\n體自上而下序第以理，會有同第，則以入序爲憑。", "jigsaw_block.pool": "的池：", "jigsaw_block.selection_priority": "擇之所先：", "jigsaw_block.selection_priority.tooltip": "父體之方理也，此即榫卯塊試接其的之次第。\n\n榫卯塊自上而下序第以理，會有同第，則任取其一。", "jigsaw_block.target": "的名：", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> （五音匣）", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "進程", "key.attack": "擊與破", "key.back": "退", "key.categories.creative": "創", "key.categories.gameplay": "嬉遊", "key.categories.inventory": "行囊", "key.categories.misc": "雜", "key.categories.movement": "行", "key.categories.multiplayer": "眾戲", "key.categories.ui": "嬉遊之幕", "key.chat": "議", "key.command": "書令", "key.drop": "棄所擇", "key.forward": "進", "key.fullscreen": "易全幕", "key.hotbar.1": "捷槽㊀", "key.hotbar.2": "捷槽㊁", "key.hotbar.3": "捷槽㊂", "key.hotbar.4": "捷槽㊃", "key.hotbar.5": "捷槽㊄", "key.hotbar.6": "捷槽㊅", "key.hotbar.7": "捷槽㊆", "key.hotbar.8": "捷槽㊇", "key.hotbar.9": "捷槽㊈", "key.inventory": "解囊與收囊", "key.jump": "躍", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "↓", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "數鍵0", "key.keyboard.keypad.1": "數鍵1", "key.keyboard.keypad.2": "數鍵2", "key.keyboard.keypad.3": "數鍵3", "key.keyboard.keypad.4": "數鍵4", "key.keyboard.keypad.5": "數鍵5", "key.keyboard.keypad.6": "數鍵6", "key.keyboard.keypad.7": "數鍵7", "key.keyboard.keypad.8": "數鍵8", "key.keyboard.keypad.9": "數鍵9", "key.keyboard.keypad.add": "數鍵+", "key.keyboard.keypad.decimal": "數鍵.", "key.keyboard.keypad.divide": "數鍵/", "key.keyboard.keypad.enter": "數鍵Enter", "key.keyboard.keypad.equal": "數鍵=", "key.keyboard.keypad.multiply": "數鍵*", "key.keyboard.keypad.subtract": "數鍵-", "key.keyboard.left": "←", "key.keyboard.left.alt": "左Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "左Ctrl", "key.keyboard.left.shift": "左Shift", "key.keyboard.left.win": "左Win", "key.keyboard.menu": "選單", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "→", "key.keyboard.right.alt": "右Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "右Ctrl", "key.keyboard.right.shift": "右Shift", "key.keyboard.right.win": "右Win", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "空白鍵", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "未定", "key.keyboard.up": "↑", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "左行", "key.loadToolbarActivator": "入捷槽", "key.mouse": "鍵 %1$s", "key.mouse.left": "左鍵", "key.mouse.middle": "中鍵", "key.mouse.right": "右鍵", "key.pickItem": "擇塊", "key.playerlist": "列戲者之名", "key.quickActions": "急趨", "key.right": "右行", "key.saveToolbarActivator": "儲捷槽", "key.screenshot": "爲屏作畫", "key.smoothCamera": "易視角之順動", "key.sneak": "潛伏", "key.socialInteractions": "交際之戶", "key.spectatorOutlines": "彰戲者（覽者）", "key.sprint": "走", "key.swapOffhand": "易掌執物", "key.togglePerspective": "易視角", "key.use": "用與置", "known_server_link.announcements": "佈告", "known_server_link.community": "坊間", "known_server_link.community_guidelines": "坊規", "known_server_link.feedback": "建言", "known_server_link.forums": "館肆", "known_server_link.news": "新知", "known_server_link.report_bug": "報伺服器之謬", "known_server_link.status": "狀", "known_server_link.support": "援", "known_server_link.website": "網葉", "lanServer.otherPlayers": "戲者置設", "lanServer.port": "埠碼", "lanServer.port.invalid": "埠碼不可用。\n請闕之，抑再入1024至65536間之數。", "lanServer.port.invalid.new": "埠碼不可用。\n請闕之，抑再入%s至%s間之數。", "lanServer.port.unavailable": "埠碼不可用。\n請闕之，抑再入1024至65536間之另數。", "lanServer.port.unavailable.new": "埠碼不可用。\n請闕之，抑再入%s至%s間之另數。", "lanServer.scanning": "方尋戲於乃私網", "lanServer.start": "闢區網之生界", "lanServer.title": "區網之生界", "language.code": "lzh", "language.name": "文言", "language.region": "華夏", "lectern.take_book": "取書", "loading.progress": "%s%%", "mco.account.privacy.info": "博聞魔贊之於私隱法", "mco.account.privacy.info.button": "博聞GDPR", "mco.account.privacy.information": "魔贊力施諸策以護垂髫、緘私隱，凡遵《兒童網絡隱私保護法》（COPPA）、守《一般資料保護規範》（GDPR）耳。\n\n無父母之命在前，切不可入領域。", "mco.account.privacyinfo": "魔贊力施諸策以護垂髫、緘私隱，凡遵《兒童網絡隱私保護法》（COPPA）、守《一般資料保護規範》（GDPR）耳。\n\n無父母之命在前，切不可入領域。\n\n前簿於礦藝者（登簿以名者），必先遷簿於魔贊，後可入領域。", "mco.account.update": "新其戶簿", "mco.activity.noactivity": "嚮%s日無動靜也", "mco.activity.title": "戲者之動靜", "mco.backup.button.download": "下傳新者", "mco.backup.button.reset": "重設生界", "mco.backup.button.restore": "復", "mco.backup.button.upload": "上傳生界", "mco.backup.changes.tooltip": "更", "mco.backup.entry": "備（%s）", "mco.backup.entry.description": "述", "mco.backup.entry.enabledPack": "所啟之囊", "mco.backup.entry.gameDifficulty": "戲之難易", "mco.backup.entry.gameMode": "嬉遊之法", "mco.backup.entry.gameServerVersion": "伺服器之版", "mco.backup.entry.name": "名", "mco.backup.entry.seed": "種", "mco.backup.entry.templateName": "典範之名", "mco.backup.entry.undefined": "未定之更", "mco.backup.entry.uploaded": "既傳", "mco.backup.entry.worldType": "生界之類", "mco.backup.generate.world": "開天闢地", "mco.backup.info.title": "嚮備以來之所易", "mco.backup.narration": "備於%s", "mco.backup.nobackups": "此領域無備。", "mco.backup.restoring": "方復汝之領域", "mco.backup.unknown": "未知", "mco.brokenworld.download": "下傳", "mco.brokenworld.downloaded": "下傳畢", "mco.brokenworld.message.line1": "請重設抑擇他生界。", "mco.brokenworld.message.line2": "汝亦可下傳生界至獨戲。", "mco.brokenworld.minigame.title": "此微戲既不見容", "mco.brokenworld.nonowner.error": "請候領主改生界", "mco.brokenworld.nonowner.title": "生界逾期", "mco.brokenworld.play": "始戲", "mco.brokenworld.reset": "復位", "mco.brokenworld.title": "汝之生界既不見容", "mco.client.incompatible.msg.line1": "客端不容領域。", "mco.client.incompatible.msg.line2": "請操新之礦藝。", "mco.client.incompatible.msg.line3": "領域不容於試行之版。", "mco.client.incompatible.title": "客端不容！", "mco.client.outdated.stable.version": "客端之版（%s）不與領域容之。\n請以新版之礦藝嬉遊者也。", "mco.client.unsupported.snapshot.version": "客端之版（%s）不容領域。\n領域不適此快照。", "mco.compatibility.downgrade": "降", "mco.compatibility.downgrade.description": "此生界昔見遊於%s，今汝爲%s。降生界或致其毁，吾等未能斷。\n\n將存汝生界於「生界之備」。誠有不測，可復之如初。", "mco.compatibility.incompatible.popup.title": "版不容", "mco.compatibility.incompatible.releaseType.popup.message": "試入之生界不容於所在之版。", "mco.compatibility.incompatible.series.popup.message": "此生界昔見遊於%s，今汝爲%s。\n\n彼此弗容。於是版當另闢生界。", "mco.compatibility.unverifiable.message": "此生界昔見遊之版，無以明辨。苟以昇降之，則將自存錄於「生界之備」。", "mco.compatibility.unverifiable.title": "合之不合，無以驗也", "mco.compatibility.upgrade": "昇", "mco.compatibility.upgrade.description": "此生界昔見遊於%s，今汝爲%s。\n\n將存汝生界於「生界之備」。誠有不測，可復之如初。", "mco.compatibility.upgrade.friend.description": "此生界昔見遊於%s，今汝爲%s。\n\n將存是生界於「生界之備」。\n\n誠有不測，領主可復之如初。", "mco.compatibility.upgrade.title": "誠欲昇此生界乎？", "mco.configure.current.minigame": "今", "mco.configure.world.activityfeed.disabled": "戲者之情且不可用", "mco.configure.world.backup": "生界之備", "mco.configure.world.buttons.activity": "戲者之情", "mco.configure.world.buttons.close": "暫閉領域", "mco.configure.world.buttons.delete": "去", "mco.configure.world.buttons.done": "畢", "mco.configure.world.buttons.edit": "設", "mco.configure.world.buttons.invite": "邀戲者", "mco.configure.world.buttons.moreoptions": "選項餘者", "mco.configure.world.buttons.newworld": "新生界", "mco.configure.world.buttons.open": "復啟領域", "mco.configure.world.buttons.options": "生界選項", "mco.configure.world.buttons.players": "戲者", "mco.configure.world.buttons.region_preference": "擇國域⋯", "mco.configure.world.buttons.resetworld": "輪回生界", "mco.configure.world.buttons.save": "存", "mco.configure.world.buttons.settings": "法", "mco.configure.world.buttons.subscription": "僦約", "mco.configure.world.buttons.switchminigame": "更微戲", "mco.configure.world.close.question.line1": "領域可以暫閉，以免人之方措置而入戲也。措置既備，可復啟之。\n\n領域僦約不以廢焉。", "mco.configure.world.close.question.line2": "確行之？", "mco.configure.world.close.question.title": "須不輟而更之乎？", "mco.configure.world.closing": "方暫閉領域⋯", "mco.configure.world.commandBlocks": "命令塊", "mco.configure.world.delete.button": "剟其領域", "mco.configure.world.delete.question.line1": "汝之領域欲恆失之", "mco.configure.world.delete.question.line2": "確行之？", "mco.configure.world.description": "領域之敘", "mco.configure.world.edit.slot.name": "生界之名", "mco.configure.world.edit.subscreen.adventuremap": "因汝欲歷此天地，許項將畢", "mco.configure.world.edit.subscreen.experience": "因汝惟欲試戲之，限於權也。", "mco.configure.world.edit.subscreen.inspiration": "因汝籌於此天地，限於權也。", "mco.configure.world.forceGameMode": "強置其法", "mco.configure.world.invite.narration": "汝有%s邀約", "mco.configure.world.invite.profile.name": "名", "mco.configure.world.invited": "邀畢", "mco.configure.world.invited.number": "邀（%s）", "mco.configure.world.invites.normal.tooltip": "凡夫", "mco.configure.world.invites.ops.tooltip": "吏", "mco.configure.world.invites.remove.tooltip": "除", "mco.configure.world.leave.question.line1": "汝必去此領域，則自非再見邀，其不復現矣。", "mco.configure.world.leave.question.line2": "確行之？", "mco.configure.world.loading": "方載領域", "mco.configure.world.location": "位", "mco.configure.world.minigame": "今（%s）", "mco.configure.world.name": "領域之名", "mco.configure.world.opening": "方啟領域⋯", "mco.configure.world.players.error": "此名之遊者不存也", "mco.configure.world.players.inviting": "方邀戲者⋯", "mco.configure.world.players.title": "遊者", "mco.configure.world.pvp": "戰", "mco.configure.world.region_preference": "國域之所好", "mco.configure.world.region_preference.title": "擇國域之所好", "mco.configure.world.reset.question.line1": "汝之生界方重生，然則汝之現生界將失", "mco.configure.world.reset.question.line2": "確行之？", "mco.configure.world.resourcepack.question": "以此領域所予之資囊而方可入之\n\n下傳而設之乎？", "mco.configure.world.resourcepack.question.line1": "以此领域所予之資囊而方可入之", "mco.configure.world.resourcepack.question.line2": "下傳而設之乎？", "mco.configure.world.restore.download.question.line1": "生界欲下傳至獨戲。", "mco.configure.world.restore.download.question.line2": "確行之？", "mco.configure.world.restore.question.line1": "汝之領域生界欲還至期「%s」 「%s」", "mco.configure.world.restore.question.line2": "確行之？", "mco.configure.world.settings.expired": "不得纂領域既逾期者之措置", "mco.configure.world.settings.title": "法", "mco.configure.world.slot": "生界%s", "mco.configure.world.slot.empty": "空", "mco.configure.world.slot.switch.question.line1": "汝之領域將遷至非原生界也。", "mco.configure.world.slot.switch.question.line2": "確行之？", "mco.configure.world.slot.tooltip": "更之生界", "mco.configure.world.slot.tooltip.active": "入", "mco.configure.world.slot.tooltip.minigame": "更於微戲", "mco.configure.world.spawnAnimals": "孳禽畜", "mco.configure.world.spawnMonsters": "孳敵獸", "mco.configure.world.spawnNPCs": "孳傀", "mco.configure.world.spawnProtection": "復甦之衛", "mco.configure.world.spawn_toggle.message": "閉其選項以盡除此類實體", "mco.configure.world.spawn_toggle.message.npc": "閉其選項以盡除此類實體，如鄉民等", "mco.configure.world.spawn_toggle.title": "誡！", "mco.configure.world.status": "狀", "mco.configure.world.subscription.day": "日", "mco.configure.world.subscription.days": "日", "mco.configure.world.subscription.expired": "既終", "mco.configure.world.subscription.extend": "續其僦約", "mco.configure.world.subscription.less_than_a_day": "獨日內", "mco.configure.world.subscription.month": "月", "mco.configure.world.subscription.months": "月", "mco.configure.world.subscription.recurring.daysleft": "自續其僦約於", "mco.configure.world.subscription.recurring.info": "領域之所易，如延如輟，至次會日方啟。", "mco.configure.world.subscription.remaining.days": "%1$s日", "mco.configure.world.subscription.remaining.months": "%1$s月", "mco.configure.world.subscription.remaining.months.days": "%1$s月%2$s日", "mco.configure.world.subscription.start": "始日", "mco.configure.world.subscription.tab": "僦約", "mco.configure.world.subscription.timeleft": "餘時", "mco.configure.world.subscription.title": "汝之僦約", "mco.configure.world.subscription.unknown": "未知也", "mco.configure.world.switch.slot": "闢生界", "mco.configure.world.switch.slot.subtitle": "是世虛也，擇創世之法", "mco.configure.world.title": "置其領域：", "mco.configure.world.uninvite.player": "誠欲罷「%s」之邀乎？", "mco.configure.world.uninvite.question": "誠欲罷其邀乎", "mco.configure.worlds.title": "生界", "mco.connect.authorizing": "方登其簿⋯", "mco.connect.connecting": "方連其領域⋯", "mco.connect.failed": "連其領域未成", "mco.connect.region": "伺服器國域：%s", "mco.connect.success": "畢", "mco.create.world": "建", "mco.create.world.error": "須予一名！", "mco.create.world.failed": "建生界未成！", "mco.create.world.reset.title": "方闢生界⋯", "mco.create.world.skip": "越", "mco.create.world.subtitle": "或擇一生界爲新領域", "mco.create.world.wait": "方建其領域⋯", "mco.download.cancelled": "下傳既罷", "mco.download.confirmation.line1": "汝欲下傳之生界越%s", "mco.download.confirmation.line2": "汝再難將此生界上傳至領域", "mco.download.confirmation.oversized": "汝欲下傳之生界逾%s\n\n此生界恐不復得上傳至汝之領域", "mco.download.done": "下傳畢", "mco.download.downloading": "方下傳", "mco.download.extracting": "方取", "mco.download.failed": "未能下傳", "mco.download.percent": "%s %%", "mco.download.preparing": "方籌下傳", "mco.download.resourcePack.fail": "下傳其資囊未成！", "mco.download.speed": "（%s/s）", "mco.download.speed.narration": "%s每秒", "mco.download.title": "生界至新者方下傳", "mco.error.invalid.session.message": "或復啟礦藝", "mco.error.invalid.session.title": "無效會話", "mco.errorMessage.6001": "客端舊也！", "mco.errorMessage.6002": "例約未許", "mco.errorMessage.6003": "下傳逾限", "mco.errorMessage.6004": "上傳逾限", "mco.errorMessage.6005": "生界既鎖", "mco.errorMessage.6006": "生界逾期", "mco.errorMessage.6007": "戲者所入領域者眾", "mco.errorMessage.6008": "無效領域名", "mco.errorMessage.6009": "無效領域之述", "mco.errorMessage.connectionFailure": "遇謬，請於後試之。", "mco.errorMessage.generic": "生謬：", "mco.errorMessage.initialize.failed": "建領域未成", "mco.errorMessage.noDetails": "謬不供其詳", "mco.errorMessage.realmsService": "生謬（%s）：", "mco.errorMessage.realmsService.configurationError": "方纂生界選項而生謬不虞", "mco.errorMessage.realmsService.connectivity": "連領域未成：%s", "mco.errorMessage.realmsService.realmsError": "領域（%s）：", "mco.errorMessage.realmsService.unknownCompatibility": "察相容之版不成，報曰：%s", "mco.errorMessage.retry": "復試所爲", "mco.errorMessage.serviceBusy": "領域疲於此時。\n請復試連領域於須臾。", "mco.gui.button": "鍵", "mco.gui.ok": "善", "mco.info": "訊！", "mco.invited.player.narration": "邀戲者%s", "mco.invites.button.accept": "受", "mco.invites.button.reject": "拒", "mco.invites.nopending": "無待定之客！", "mco.invites.pending": "新邀約！", "mco.invites.title": "待定之邀約", "mco.minigame.world.changeButton": "更微戲", "mco.minigame.world.info.line1": "此行欲更汝之天地以微戲！", "mco.minigame.world.info.line2": "然汝可安反原生界之。", "mco.minigame.world.noSelection": "請擇之", "mco.minigame.world.restore": "微戲方終⋯", "mco.minigame.world.restore.question.line1": "微戲將終，領域將復。", "mco.minigame.world.restore.question.line2": "確行之？", "mco.minigame.world.selected": "微戲既擇：", "mco.minigame.world.slot.screen.title": "方易生界⋯", "mco.minigame.world.startButton": "易", "mco.minigame.world.starting.screen.title": "微戲方始⋯", "mco.minigame.world.stopButton": "終其微戲", "mco.minigame.world.switch.new": "更微戲乎？", "mco.minigame.world.switch.title": "更微戲", "mco.minigame.world.title": "更領域至微戲", "mco.news": "領域之聞", "mco.notification.dismiss": "閉", "mco.notification.transferSubscription.buttonText": "今而遷之", "mco.notification.transferSubscription.message": "爪哇版領域所僦，行將遷於微軟市。勿使之終！\n今而遷之，則得卅日無直。\n請遷僦於minecraft.net之檔章。", "mco.notification.visitUrl.buttonText.default": "啟鏈", "mco.notification.visitUrl.message.default": "請覽下方連結", "mco.onlinePlayers": "在綫戲者", "mco.play.button.realm.closed": "領域既閉", "mco.question": "疑", "mco.reset.world.adventure": "險", "mco.reset.world.experience": "靈力", "mco.reset.world.generate": "闢新生界", "mco.reset.world.inspiration": "妙筆生花", "mco.reset.world.resetting.screen.title": "生界方重設⋯", "mco.reset.world.seed": "構界之種（可選）", "mco.reset.world.template": "世界範典", "mco.reset.world.title": "重設生界", "mco.reset.world.upload": "上傳生界", "mco.reset.world.warning": "此舉將易今之領域也", "mco.selectServer.buy": "賈其領域！", "mco.selectServer.close": "關", "mco.selectServer.closed": "領域既廢者", "mco.selectServer.closeserver": "閉其領域", "mco.selectServer.configure": "置其領域", "mco.selectServer.configureRealm": "置其領域", "mco.selectServer.create": "建其領域", "mco.selectServer.create.subtitle": "擇一生界爲新領域", "mco.selectServer.expired": "領域既終者", "mco.selectServer.expiredList": "領域之僦既終", "mco.selectServer.expiredRenew": "續其購約", "mco.selectServer.expiredSubscribe": "僦", "mco.selectServer.expiredTrial": "此試終矣", "mco.selectServer.expires.day": "餘者不足日", "mco.selectServer.expires.days": "餘者%s日", "mco.selectServer.expires.soon": "俄將終也", "mco.selectServer.leave": "去此領域", "mco.selectServer.loading": "方載領域之列", "mco.selectServer.mapOnlySupportedForVersion": "是輿圖不可援於%s", "mco.selectServer.minigame": "微戲：", "mco.selectServer.minigameName": "微戲：%s", "mco.selectServer.minigameNotSupportedInVersion": "此微戲不可遊於%s", "mco.selectServer.noRealms": "君似無領域。增之以與友同樂。", "mco.selectServer.note": "誡：", "mco.selectServer.open": "開其領域", "mco.selectServer.openserver": "開其領域", "mco.selectServer.play": "戲", "mco.selectServer.popup": "領域者，礦藝眾戲之安易者也，堪十人之同樂。其中博弈雕琢，浩如煙海！惟其領主每須支給。", "mco.selectServer.purchase": "新增領域", "mco.selectServer.trial": "容吾一試！", "mco.selectServer.uninitialized": "擊此建其領域！", "mco.snapshot.createSnapshotPopup.text": "汝將啟無直快照領域，繫於所僦也。僦之未竟，新快照領域可無時存取之。所僦不爲所動焉。", "mco.snapshot.createSnapshotPopup.title": "誠欲立快照領域乎？", "mco.snapshot.creating": "方立快照領域⋯", "mco.snapshot.description": "繫於%s", "mco.snapshot.friendsRealm.downgrade": "子當以%s入此領域", "mco.snapshot.friendsRealm.upgrade": "需昇%s之領域，方得嬉遊於此快照", "mco.snapshot.paired": "是快照領域繫於%s", "mco.snapshot.parent.tooltip": "以至新完版礦藝戲於此領域", "mco.snapshot.start": "啟無直快照領域", "mco.snapshot.subscription.info": "此快照領域也，繫於汝之所僦「%s」。所僦既在，其亦隨焉。", "mco.snapshot.tooltip": "以快照領域先睹礦藝新版爲快，或有所增修焉。\n\n至於完版，可得領域如常者。", "mco.snapshotRealmsPopup.message": "自二三週四一甲始，領域得行諸快照。領域所僦，悉有無直，別於常域也！", "mco.snapshotRealmsPopup.title": "領域今得行於快照", "mco.snapshotRealmsPopup.urlText": "博聞之", "mco.template.button.publisher": "公佈者", "mco.template.button.select": "擇", "mco.template.button.trailer": "豫告", "mco.template.default.name": "天地典範", "mco.template.info.tooltip": "公佈者之舘", "mco.template.name": "典範", "mco.template.select.failure": "不可得此表也。\n可索之網絡，或待時而復行之。", "mco.template.select.narrate.authors": "作者：%s", "mco.template.select.narrate.version": "版%s", "mco.template.select.none": "嗚呼，此類尚無一物，恕罪。\n請待時，復索新物，若君爲創者，\n%s。", "mco.template.select.none.linkTitle": "君可紀汝之物", "mco.template.title": "生界典範", "mco.template.title.minigame": "微戲", "mco.template.trailer.tooltip": "天地豫告", "mco.terms.buttons.agree": "許", "mco.terms.buttons.disagree": "弗許", "mco.terms.sentence.1": "悉聽礦藝領域", "mco.terms.sentence.2": "條例所約", "mco.terms.title": "領域條款", "mco.time.daysAgo": "%1$s日前", "mco.time.hoursAgo": "%1$s時前", "mco.time.minutesAgo": "%1$s分前", "mco.time.now": "隻今", "mco.time.secondsAgo": "%1$s秒前", "mco.trial.message.line1": "欲得己之領域？", "mco.trial.message.line2": "擊此以得其詳！", "mco.upload.button.name": "上傳", "mco.upload.cancelled": "上傳既罷", "mco.upload.close.failure": "領域不得閉，俄請復試之", "mco.upload.done": "上傳畢", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s（%2$s）", "mco.upload.failed": "上傳未成！（%s）", "mco.upload.failed.too_big.description": "所擇之生界過大。尺寸弗能逾%s之限。", "mco.upload.failed.too_big.title": "生界大甚", "mco.upload.hardcore": "歷之生界不可上傳！", "mco.upload.percent": "%s %%", "mco.upload.preparing": "方備汝之生界", "mco.upload.select.world.none": "尋獨戲之生界未果！", "mco.upload.select.world.subtitle": "擇獨戲一生界以上傳", "mco.upload.select.world.title": "上傳生界", "mco.upload.size.failure.line1": "「%s」過大也！", "mco.upload.size.failure.line2": "今其方%s，然限方%s。", "mco.upload.uploading": "方上傳「%s」", "mco.upload.verifying": "堪汝之生界焉", "mco.version": "版：%s", "mco.warning": "誡！", "mco.worldSlot.minigame": "微戲", "menu.custom_options": "自定選項⋯", "menu.custom_options.title": "自定選項", "menu.custom_options.tooltip": "註：自定選項受於外黨之器物。\n惕哉！", "menu.custom_screen_info.button_narration": "此自定幕也。博聞之。", "menu.custom_screen_info.contents": "是幕之物，因於外黨、輿圖，而非魔贊所有，微軟所司。\n\n惕哉！擊鏈以愼，毋洩私情，毋曝戶簿。\n\n苟阻子往戲，可斷連以下鈕。", "menu.custom_screen_info.disconnect": "既拒自定幕", "menu.custom_screen_info.title": "自定幕之註", "menu.custom_screen_info.tooltip": "此自定幕也。擊此以博聞之。", "menu.disconnect": "去", "menu.feedback": "建言⋯", "menu.feedback.title": "建言", "menu.game": "選單", "menu.modded": "（既改）", "menu.multiplayer": "眾戲", "menu.online": "礦藝領域", "menu.options": "置設⋯", "menu.paused": "暫息", "menu.playdemo": "遊於試世", "menu.playerReporting": "檢舉戲者", "menu.preparingSpawn": "方備生域： %s%%", "menu.quick_actions": "急趨⋯", "menu.quick_actions.title": "急趨", "menu.quit": "離去", "menu.reportBugs": "報誤", "menu.resetdemo": "重設試世", "menu.returnToGame": "還於戲", "menu.returnToMenu": "作記乃歸卷首", "menu.savingChunks": "區塊方存", "menu.savingLevel": "生界方存", "menu.sendFeedback": "惠賜卓見", "menu.server_links": "伺服器之鏈⋯", "menu.server_links.title": "伺服器之鏈", "menu.shareToLan": "供區網共戲", "menu.singleplayer": "獨戲", "menu.working": "方作⋯", "merchant.deprecated": "鄉民日多補貨再。", "merchant.level.1": "新人", "merchant.level.2": "門徒", "merchant.level.3": "手熟", "merchant.level.4": "匠人", "merchant.level.5": "巨擘", "merchant.title": "%s - %s", "merchant.trades": "商賈", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "撳%1$s以下騎", "multiplayer.applyingPack": "方用此資囊", "multiplayer.confirm_command.parse_errors": "子所欲行之令，不辨有舛。\n誠行之乎？\n令：%s", "multiplayer.confirm_command.permissions_required": "子所欲行之令，其品次較高。\n此或遺弊於戲。\n誠行之乎？\n令：%s", "multiplayer.confirm_command.title": "確行其令", "multiplayer.disconnect.authservers_down": "驗證伺服器去矣，容後再試，深表歉意！", "multiplayer.disconnect.bad_chat_index": "見議訊之遺闕抑更序者於伺服器", "multiplayer.disconnect.banned": "君既爲該伺服器所羈", "multiplayer.disconnect.banned.expiration": "\n羈之期至%s", "multiplayer.disconnect.banned.reason": "此伺服器已羈君。\n其由爲： %s", "multiplayer.disconnect.banned_ip.expiration": "\n羈之期至%s", "multiplayer.disconnect.banned_ip.reason": "此伺服器已羈君之IP。\n其由爲： %s", "multiplayer.disconnect.chat_validation_failed": "論議證敗", "multiplayer.disconnect.duplicate_login": "已登入別處", "multiplayer.disconnect.expired_public_key": "公鑰既終。請檢校械綱之時齊一與否，或復啟其戲。", "multiplayer.disconnect.flying": "御空之術已爲伺服器所閉", "multiplayer.disconnect.generic": "連線既斷", "multiplayer.disconnect.idling": "縵立久也！", "multiplayer.disconnect.illegal_characters": "異字見於議", "multiplayer.disconnect.incompatible": "不容之客端！當用%s", "multiplayer.disconnect.invalid_entity_attacked": "圖攻無效之物", "multiplayer.disconnect.invalid_packet": "伺服器傳一舛囊也。", "multiplayer.disconnect.invalid_player_data": "戲者檔案有舛。", "multiplayer.disconnect.invalid_player_movement": "承無效行人之囊", "multiplayer.disconnect.invalid_public_key_signature": "狀公鑰署名有舛。\n試重啟戲。", "multiplayer.disconnect.invalid_public_key_signature.new": "公鑰署名無效。\n請重啟其戲。", "multiplayer.disconnect.invalid_vehicle_movement": "承無效行騎之囊", "multiplayer.disconnect.ip_banned": "此伺服器已羈君之IP。", "multiplayer.disconnect.kicked": "既爲吏所逐", "multiplayer.disconnect.missing_tags": "伺服器之籤集未全。\n可訊伺服器之主。", "multiplayer.disconnect.name_taken": "名已爲人所用", "multiplayer.disconnect.not_whitelisted": "君未在是伺服器之準簿中！", "multiplayer.disconnect.out_of_order_chat": "受議囊無序。械綱之時嘗改乎？", "multiplayer.disconnect.outdated_client": "不容之客端！當用%s", "multiplayer.disconnect.outdated_server": "不容之客端！當用%s", "multiplayer.disconnect.server_full": "伺服器滿矣！", "multiplayer.disconnect.server_shutdown": "伺服器既閉", "multiplayer.disconnect.slow_login": "入逾時", "multiplayer.disconnect.too_many_pending_chats": "未証之論議多矣", "multiplayer.disconnect.transfers_disabled": "伺服器不納遷", "multiplayer.disconnect.unexpected_query_response": "客端所送自定錄，非所期也", "multiplayer.disconnect.unsigned_chat": "受署名闕舛之議囊也。", "multiplayer.disconnect.unverified_username": "君之名姓，弗能查驗！", "multiplayer.downloadingStats": "方取計⋯", "multiplayer.downloadingTerrain": "方載地勢⋯", "multiplayer.lan.server_found": "見新伺服器：%s", "multiplayer.message_not_delivered": "無以傳侃談之訊， 請察伺服器之誌： %s", "multiplayer.player.joined": "%s來此共戲", "multiplayer.player.joined.renamed": "%s（舊名%s者）來此共戲", "multiplayer.player.left": "%s去矣", "multiplayer.player.list.hp": "%s命值", "multiplayer.player.list.narration": "在線戲者：%s", "multiplayer.requiredTexturePrompt.disconnect": "伺服器須以其所予之資囊而入之", "multiplayer.requiredTexturePrompt.line1": "伺服器須以其所予之資囊而入之。", "multiplayer.requiredTexturePrompt.line2": "拒此資囊者，弗戲於此伺服器也。", "multiplayer.socialInteractions.not_available": "交際但使於眾戲之生界", "multiplayer.status.and_more": "，及%s人有餘", "multiplayer.status.cancelled": "既罷", "multiplayer.status.cannot_connect": "不可入伺服器", "multiplayer.status.cannot_resolve": "不可析主機名", "multiplayer.status.finished": "既成", "multiplayer.status.incompatible": "版不容！", "multiplayer.status.motd.narration": "伺服器副題：%s", "multiplayer.status.no_connection": "（斷聯）", "multiplayer.status.old": "舊版", "multiplayer.status.online": "在線", "multiplayer.status.ping": "%s毫秒", "multiplayer.status.ping.narration": "延%s毫秒", "multiplayer.status.pinging": "方測⋯", "multiplayer.status.player_count": "%2$s之%1$s", "multiplayer.status.player_count.narration": "%s之%s人在線", "multiplayer.status.quitting": "方去", "multiplayer.status.request_handled": "既理狀態之求", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "受未求之訊也", "multiplayer.status.version.narration": "伺服器之版：%s", "multiplayer.stopSleeping": "寤", "multiplayer.texturePrompt.failure.line1": "伺服器資囊弗可用也", "multiplayer.texturePrompt.failure.line2": "用須自定資囊者咸許不可作如所期", "multiplayer.texturePrompt.line1": "此伺服器宜用其所予之資囊。", "multiplayer.texturePrompt.line2": "下傳措置，可不藉人力而自成，於此欲行之乎？", "multiplayer.texturePrompt.serverPrompt": "%s\n\n伺服器曰：\n%s", "multiplayer.title": "眾戲", "multiplayer.unsecureserver.toast": "此伺服器或將更其論議，未必能見其原文", "multiplayer.unsecureserver.toast.title": "弗能證其論議", "multiplayerWarning.check": "勿復現此景也", "multiplayerWarning.header": "誡： 外黨之眾戲", "multiplayerWarning.message": "誡：眾戲者，外黨之伺服器所供也，而非魔贊所有，微軟所司。 眾戲中或會不司之訊，其用者之所造亦或不宜眾。", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "谷岡久美 - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "谷岡久美 - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "谷岡久美 - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "谷岡久美 - komorebi", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "谷岡久美 - poko<PERSON>ko", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "谷岡久美 - yakusoku", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "鍵：%s", "narration.button.usage.focused": "擊迴車以啟之", "narration.button.usage.hovered": "擊左鍵以啟之", "narration.checkbox": "選匡：%s", "narration.checkbox.usage.focused": "擊迴車以更之", "narration.checkbox.usage.hovered": "擊左鍵以更之", "narration.component_list.usage": "擊Tab移至下者", "narration.cycle_button.usage.focused": "擊迴車以更作%s", "narration.cycle_button.usage.hovered": "擊左鍵以更作%s", "narration.edit_box": "纂輯欄：%s", "narration.item": "物：%s", "narration.recipe": "%s之方", "narration.recipe.usage": "擊左鍵以擇", "narration.recipe.usage.more": "右擊以覽餘方", "narration.selection.usage": "擊上下鍵以移之於餘項", "narration.slider.usage.focused": "撃左右鍵以易其值", "narration.slider.usage.hovered": "曳滑桿以更其值", "narration.suggestion": "擇第%1$s項自%2$s項：%3$s", "narration.suggestion.tooltip": "擇第%1$s項自%2$s項：%3$s（%4$s）", "narration.suggestion.usage.cycle.fixed": "擊Tab移至次者", "narration.suggestion.usage.cycle.hidable": "擊Tab移至次者，抑擊Esc去之", "narration.suggestion.usage.fill.fixed": "擊Tab施此項", "narration.suggestion.usage.fill.hidable": "擊Tab施此項，抑擊Esc去之", "narration.tab_navigation.usage": "押Ctrl與Tab以易籤頁", "narrator.button.accessibility": "助", "narrator.button.difficulty_lock": "難易鑰", "narrator.button.difficulty_lock.locked": "既鑰", "narrator.button.difficulty_lock.unlocked": "未鑰", "narrator.button.language": "文", "narrator.controls.bound": "%s附爲%s", "narrator.controls.reset": "重設%s鍵", "narrator.controls.unbound": "%s未附", "narrator.joining": "方入", "narrator.loading": "方讀：%s", "narrator.loading.done": "畢", "narrator.position.list": "既擇%s列於%s列焉", "narrator.position.object_list": "擇%s列元素於%s列", "narrator.position.screen": "%2$s幕件之%1$s", "narrator.position.tab": "既擇第%s籤頁於%s焉", "narrator.ready_to_play": "萬事俱備", "narrator.screen.title": "卷首", "narrator.screen.usage": "以滑鼠或Tab擇其元", "narrator.select": "既擇：%s", "narrator.select.world": "既擇：%s，向戲：%s，%s，%s，版：%s", "narrator.select.world_info": "既擇%s，向戲：%s，%s", "narrator.toast.disabled": "復辭既關", "narrator.toast.enabled": "復辭既開", "optimizeWorld.confirm.description": "茲有新格式待試者。假令以新式代舊式，則必耗時以更替，蓋其遲速，悉決於該檔之大小。俟畢則遊戲更暢，惟其新式不容於舊版。茲欲以新代舊乎？", "optimizeWorld.confirm.proceed": "立備份而補益之", "optimizeWorld.confirm.title": "補益生界", "optimizeWorld.info.converted": "區塊既更： %s", "optimizeWorld.info.skipped": "區塊既略： %s", "optimizeWorld.info.total": "總區塊數： %s", "optimizeWorld.progress.counter": "%2$s之%1$s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "方計區塊⋯", "optimizeWorld.stage.failed": "惜未成！", "optimizeWorld.stage.finished": "將畢⋯", "optimizeWorld.stage.finished.chunks": "昇區塊將畢⋯", "optimizeWorld.stage.finished.entities": "昇實體將畢⋯", "optimizeWorld.stage.finished.poi": "昇所好將畢⋯", "optimizeWorld.stage.upgrading": "方悉昇區塊⋯", "optimizeWorld.stage.upgrading.chunks": "方悉昇區塊⋯", "optimizeWorld.stage.upgrading.entities": "方悉昇實體⋯", "optimizeWorld.stage.upgrading.poi": "方悉昇所好⋯", "optimizeWorld.title": "方補益生界「%s」", "options.accessibility": "協器之置設⋯", "options.accessibility.high_contrast": "高對比", "options.accessibility.high_contrast.error.tooltip": "涇渭資囊不可用也", "options.accessibility.high_contrast.tooltip": "強介面控具之色差。", "options.accessibility.high_contrast_block_outline": "分明塊勒", "options.accessibility.high_contrast_block_outline.tooltip": "明所指之塊之緣勒。", "options.accessibility.link": "協具之導引", "options.accessibility.menu_background_blurriness": "選單幕曚", "options.accessibility.menu_background_blurriness.tooltip": "改選單幕景之明曚。", "options.accessibility.narrator_hotkey": "復辭捷鍵", "options.accessibility.narrator_hotkey.mac.tooltip": "許復辭之啟閉於「Cmd+B」。", "options.accessibility.narrator_hotkey.tooltip": "許復辭之啟閉於「Ctrl+B」。", "options.accessibility.panorama_speed": "橫卷捲覽遲之緩急", "options.accessibility.text_background": "文之質", "options.accessibility.text_background.chat": "論議", "options.accessibility.text_background.everywhere": "全部", "options.accessibility.text_background_opacity": "文質之陰翳", "options.accessibility.title": "協器之置設", "options.allowServerListing": "允伺服列名", "options.allowServerListing.tooltip": "伺服可公示線上戲者。\n若閉此項，子之名則不列焉。", "options.ao": "潤光", "options.ao.max": "極大", "options.ao.min": "極小", "options.ao.off": "閉", "options.attack.crosshair": "凖", "options.attack.hotbar": "捷槽", "options.attackIndicator": "擊刻漏", "options.audioDevice": "裝置", "options.audioDevice.default": "械綱預定", "options.autoJump": "自躍", "options.autoSuggestCommands": "令註", "options.autosaveIndicator": "自儲刻漏", "options.biomeBlendRadius": "混生態域", "options.biomeBlendRadius.1": "關（極速）", "options.biomeBlendRadius.11": "方十一（極高）", "options.biomeBlendRadius.13": "方十三（炫耀）", "options.biomeBlendRadius.15": "方十五（極）", "options.biomeBlendRadius.3": "方三（速）", "options.biomeBlendRadius.5": "方五（凡）", "options.biomeBlendRadius.7": "方七（高）", "options.biomeBlendRadius.9": "方九（甚高）", "options.chat": "議之置設⋯", "options.chat.color": "色", "options.chat.delay": "㗫之滯：%s秒", "options.chat.delay_none": "議之滯：無", "options.chat.height.focused": "矚之高", "options.chat.height.unfocused": "未矚之高", "options.chat.line_spacing": "行距", "options.chat.links": "萬維連結", "options.chat.links.prompt": "連結提示", "options.chat.opacity": "議文之陰翳", "options.chat.scale": "書議之巨細", "options.chat.title": "議之置設", "options.chat.visibility": "議", "options.chat.visibility.full": "全示", "options.chat.visibility.hidden": "既隱", "options.chat.visibility.system": "僅呈命令", "options.chat.width": "寬", "options.chunks": "%s區塊", "options.clouds.fancy": "佳", "options.clouds.fast": "暢", "options.controls": "控⋯", "options.credits_and_attribution": "署權⋯", "options.damageTiltStrength": "受擊偏頃", "options.damageTiltStrength.tooltip": "傷者視角之偏幅。", "options.darkMojangStudiosBackgroundColor": "玄素徽", "options.darkMojangStudiosBackgroundColor.tooltip": "更魔贊之徽爲黑底。", "options.darknessEffectScale": "暗脈之搏", "options.darknessEffectScale.tooltip": "制監守或幽匿嘯予之暗脈之搏所臨爍幾何。", "options.difficulty": "難易", "options.difficulty.easy": "易", "options.difficulty.easy.info": "敵獸生，其傷微。飢極傷身，命數半折。", "options.difficulty.hard": "難", "options.difficulty.hard.info": "敵獸生，其傷劇。飢極傷身，其傷可斃。", "options.difficulty.hardcore": "極", "options.difficulty.normal": "凡", "options.difficulty.normal.info": "敵獸生，其傷中。飢極傷身，直戾岌岌可危之境。", "options.difficulty.online": "伺服器難易", "options.difficulty.peaceful": "和", "options.difficulty.peaceful.info": "敵不生，惟良友。無飢寒，命數自盈。", "options.directionalAudio": "周迴聲訊", "options.directionalAudio.off.tooltip": "雅眾竅和聲。", "options.directionalAudio.on.tooltip": "用周迴聲訊之本於首之屬易函數者，可補益於擬三維聲。須兼容首之屬易函數之聲訊剛器。必佩耳機而後圓備。", "options.discrete_mouse_scroll": "離散轆", "options.entityDistanceScaling": "實體繪距", "options.entityShadows": "實體之影", "options.font": "字體置設⋯", "options.font.title": "字體置設", "options.forceUnicodeFont": "強用萬國碼字體", "options.fov": "視界", "options.fov.max": "超視", "options.fov.min": "凡", "options.fovEffectScale": "視場之效", "options.fovEffectScale.tooltip": "制戲遊之效所易視野幾何。", "options.framerate": "幀%s", "options.framerateLimit": "極幀", "options.framerateLimit.max": "無限幀", "options.fullscreen": "全幕", "options.fullscreen.current": "當即", "options.fullscreen.entry": "%s×%s@%s（色深%s爻）", "options.fullscreen.resolution": "全幕解析度", "options.fullscreen.unavailable": "置設不可用", "options.gamma": "明度", "options.gamma.default": "預設", "options.gamma.max": "明", "options.gamma.min": "暗", "options.generic_value": "%s： %s", "options.glintSpeed": "爍之緩急", "options.glintSpeed.tooltip": "控淬靈物爍之緩急。", "options.glintStrength": "爍之顯弱", "options.glintStrength.tooltip": "控淬靈物爍之通透。", "options.graphics": "畫質", "options.graphics.fabulous": "華美！", "options.graphics.fabulous.tooltip": "%s畫質著粒效背與氣、雲、半透明塊以熒幕著色器。或成擔與行動裝置及4K顯示器。", "options.graphics.fancy": "佳", "options.graphics.fancy.tooltip": "佳畫質權衡效能與品質，宜於泰半裝置。\n晴雨、雲與粒或弗見於半透明塊抑水後。", "options.graphics.fast": "暢", "options.graphics.fast.tooltip": "流暢畫質，稍削雨雪。\n禁之通透之方塊，以木葉爲例也。", "options.graphics.warning.accept": "無援而進", "options.graphics.warning.cancel": "引吾還", "options.graphics.warning.message": "見君之方矩裝置弗受%s質以援。\n\n君可略此而續之，然若擇%s質，裝置弗受援。", "options.graphics.warning.renderer": "檢諸理質器：[%s]", "options.graphics.warning.title": "方矩裝置弗榰之也", "options.graphics.warning.vendor": "檢爲販者：[%s]", "options.graphics.warning.version": "檢得公圖庫之本：[%s]", "options.guiScale": "介面置設", "options.guiScale.auto": "從宜", "options.hidden": "隱", "options.hideLightningFlashes": "隱列缺之爍", "options.hideLightningFlashes.tooltip": "阻列缺爍天穹。列缺仍可見焉。", "options.hideMatchedNames": "隱名之所匹", "options.hideMatchedNames.tooltip": "他方之伺服器，或傳諸非禮之訊。\n選項既開，若名相符，則蔽戲者。", "options.hideSplashTexts": "隱批文", "options.hideSplashTexts.tooltip": "隱卷首之金批。", "options.inactivityFpsLimit": "遏幀率於", "options.inactivityFpsLimit.afk": "暫離", "options.inactivityFpsLimit.afk.tooltip": "戲者未執逾一分，則遏每秒幀率至卅。逾九分則益遏至十。", "options.inactivityFpsLimit.minimized": "匿", "options.inactivityFpsLimit.minimized.tooltip": "遏幀於戲窗匿時。", "options.invertMouse": "反置滑鼠", "options.japaneseGlyphVariants": "日本字體", "options.japaneseGlyphVariants.tooltip": "以日本之體，合漢字之形", "options.key.hold": "押", "options.key.toggle": "更", "options.language": "文⋯", "options.language.title": "文", "options.languageAccuracyWarning": "（文之所譯，未必悉準）", "options.languageWarning": "文之所譯，未必悉準", "options.mainHand": "利手", "options.mainHand.left": "左", "options.mainHand.right": "右", "options.mipmapLevels": "彌圖之等", "options.modelPart.cape": "蓑衣", "options.modelPart.hat": "冠", "options.modelPart.jacket": "衣", "options.modelPart.left_pants_leg": "左絝", "options.modelPart.left_sleeve": "左袖", "options.modelPart.right_pants_leg": "右絝", "options.modelPart.right_sleeve": "右袖", "options.mouseWheelSensitivity": "輪之敏度", "options.mouse_settings": "滑鼠置設⋯", "options.mouse_settings.title": "滑鼠置設", "options.multiplayer.title": "眾戲置設⋯", "options.multiplier": "%s倍", "options.music_frequency": "絲竹之稀稠", "options.music_frequency.constant": "恆", "options.music_frequency.default": "預設", "options.music_frequency.frequent": "稠", "options.music_frequency.tooltip": "更世中絲竹之稀稠也。", "options.narrator": "復其辭", "options.narrator.all": "咸敘", "options.narrator.chat": "敘議言", "options.narrator.notavailable": "未可使", "options.narrator.off": "閉", "options.narrator.system": "敘其所詔於其條", "options.notifications.display_time": "告示之時", "options.notifications.display_time.tooltip": "改告留於幕之久暫。", "options.off": "關", "options.off.composed": "%s：關", "options.on": "開", "options.on.composed": "%s：開", "options.online": "眾戲⋯", "options.online.title": "眾戲置設", "options.onlyShowSecureChat": "但示無虞之議", "options.onlyShowSecureChat.tooltip": "他人之書，但示其確係自送且未改者。", "options.operatorItemsTab": "吏物欖", "options.particles": "粒", "options.particles.all": "皆有", "options.particles.decreased": "少", "options.particles.minimal": "微", "options.percent_add_value": "%s：增以%s%%", "options.percent_value": "%s：%s%%", "options.pixel_value": "%s：像素%s", "options.prioritizeChunkUpdates": "構區塊法", "options.prioritizeChunkUpdates.byPlayer": "半阻", "options.prioritizeChunkUpdates.byPlayer.tooltip": "區塊內每有殊行，乃重編譯之。築破塊者概在此列矣。", "options.prioritizeChunkUpdates.nearby": "全阻", "options.prioritizeChunkUpdates.nearby.tooltip": "恆編譯四近區塊於即時。築破塊時，此法或損性能。", "options.prioritizeChunkUpdates.none": "依執行緒", "options.prioritizeChunkUpdates.none.tooltip": "依執行緒並編譯四近之區塊。若循此法，隙或現於破塊之須臾。", "options.rawMouseInput": "生素入", "options.realmsNotifications": "領域之新知邀約", "options.realmsNotifications.tooltip": "領域之新知及邀約得於卷首，其徽亦現於「領域」鍵。", "options.reducedDebugInfo": "簡其勘記", "options.renderClouds": "雲", "options.renderCloudsDistance": "雲之遠近", "options.renderDistance": "繪距", "options.resourcepack": "資囊⋯", "options.rotateWithMinecart": "從礦車而旋", "options.rotateWithMinecart.tooltip": "控戲者視角同礦車之旋與否。僅可用於啟「礦車補益」試行置設之生界。", "options.screenEffectScale": "曲視之效", "options.screenEffectScale.tooltip": "氣逆之烈與焱界結界門曲幕之度。\n值微則氣逆徒以綠霧代之。", "options.sensitivity": "敏度", "options.sensitivity.max": "亢速！！！", "options.sensitivity.min": "欠", "options.showNowPlayingToast": "見樂題", "options.showNowPlayingToast.tooltip": "顯樂題於肇奏之時。方奏而暫息，其題亦見焉。", "options.showSubtitles": "現旁註", "options.simulationDistance": "擬距", "options.skinCustomisation": "自定外觀⋯", "options.skinCustomisation.title": "自定外觀", "options.sounds": "聲樂⋯", "options.sounds.title": "聲樂置設", "options.telemetry": "遙測錄⋯", "options.telemetry.button": "錄集", "options.telemetry.button.tooltip": "「%s」僅含必錄。\n「%s」含悉錄。", "options.telemetry.disabled": "遙測既禁。", "options.telemetry.state.all": "悉", "options.telemetry.state.minimal": "寡", "options.telemetry.state.none": "無", "options.title": "置設", "options.touchscreen": "觸控", "options.video": "成像置設⋯", "options.videoTitle": "成像置設", "options.viewBobbing": "視綫之搖", "options.visible": "既顯", "options.vsync": "繪顯同調", "outOfMemory.message": "礦藝殫其憶。\n\n或以戲有舛故，或以爪哇栩生儀之憶分未足故也。\n\n此戲已去，以止生界之敗績。\n我既試以釋憶，欲翛然足之，俾子反於卷首而歸戲，雖然，或未果。\n\n若子復見此書，請重啟其戲。", "outOfMemory.title": "憶殫！", "pack.available.title": "可用", "pack.copyFailure": "複刻錄囊未果", "pack.dropConfirm": "汝確以將下其之資囊增至礦藝乎？", "pack.dropInfo": "曳案至此戶以增一資囊", "pack.dropRejected.message": "如左者非有效之囊，複刻之未成：\n%s", "pack.dropRejected.title": "非囊之項", "pack.folderInfo": "（陳資囊於此）", "pack.incompatible": "不相容", "pack.incompatible.confirm.new": "此資囊專爲新版礦藝而製，茲或不得遂行於此版。", "pack.incompatible.confirm.old": "此資囊著於上古礦藝，無知其可無虞而行。", "pack.incompatible.confirm.title": "誠欲讀此資囊乎？", "pack.incompatible.new": "（容於礦藝新者）", "pack.incompatible.old": "（容於礦藝舊者）", "pack.nameAndSource": "%s（%s）", "pack.openFolder": "啟囊之資料夾", "pack.selected.title": "既選", "pack.source.builtin": "內成", "pack.source.feature": "藝能", "pack.source.local": "本土", "pack.source.server": "伺服器", "pack.source.world": "界", "painting.dimensions": "%s乘%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "魔贊", "painting.minecraft.earth.title": "土", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "魔贊", "painting.minecraft.fire.title": "火", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "豕像", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "潮汐", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "太虛", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "荒原", "painting.minecraft.water.author": "魔贊", "painting.minecraft.water.title": "水", "painting.minecraft.wind.author": "魔贊", "painting.minecraft.wind.title": "風", "painting.minecraft.wither.author": "魔贊", "painting.minecraft.wither.title": "凋靈", "painting.random": "無定之類", "parsing.bool.expected": "當爲是非之值", "parsing.bool.invalid": "是布爾值無效，應爲「true」抑「false」，尋「%s」", "parsing.double.expected": "應爲倍精度浮點數", "parsing.double.invalid": "無效倍精度浮點數「%s」", "parsing.expected": "應爲「%s」", "parsing.float.expected": "應爲零數", "parsing.float.invalid": "無效零數「%s」", "parsing.int.expected": "應爲整數", "parsing.int.invalid": "無效整型數「%s」", "parsing.long.expected": "應爲長整數", "parsing.long.invalid": "無效大數「%s」", "parsing.quote.escape": "引號中之脫序「\\%s」無效", "parsing.quote.expected.end": "字串應終以引號", "parsing.quote.expected.start": "字串應始以引號", "particle.invalidOptions": "析粒之措置未成：%s", "particle.notFound": "未知之粒：%s", "permissions.requires.entity": "此令需行以實體", "permissions.requires.player": "此令需行以戲者", "potion.potency.1": "二階", "potion.potency.2": "三階", "potion.potency.3": "四階", "potion.potency.4": "五階", "potion.potency.5": "六階", "potion.whenDrank": "付之時：", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s（%s）", "predicate.unknown": "未明之辝：%s", "quickplay.error.invalid_identifier": "是符無所指之生界", "quickplay.error.realm_connect": "連領域未成", "quickplay.error.realm_permission": "乏權而未可連此領域", "quickplay.error.title": "趨入未成", "realms.configuration.region.australia_east": "贍部國新南金琉州", "realms.configuration.region.australia_southeast": "贍部國役都梨州", "realms.configuration.region.brazil_south": "婆羅悉", "realms.configuration.region.central_india": "印度", "realms.configuration.region.central_us": "美利堅國愛烏渦州", "realms.configuration.region.east_asia": "香港", "realms.configuration.region.east_us": "美利堅國佛辰泥州", "realms.configuration.region.east_us_2": "美利堅國北伽廬梨那州", "realms.configuration.region.france_central": "芳司", "realms.configuration.region.japan_east": "東日本", "realms.configuration.region.japan_west": "西日本", "realms.configuration.region.korea_central": "韓國", "realms.configuration.region.north_central_us": "美利堅國伊梨芮州", "realms.configuration.region.north_europe": "愛爾蘭", "realms.configuration.region.south_central_us": "美利堅國惕娑思州", "realms.configuration.region.southeast_asia": "蒲羅中", "realms.configuration.region.sweden_central": "遂歷", "realms.configuration.region.uae_north": "大食異密諸部", "realms.configuration.region.uk_south": "南英吉利", "realms.configuration.region.west_central_us": "美利堅國猶他州", "realms.configuration.region.west_europe": "荷蘭", "realms.configuration.region.west_us": "美利堅國伽梨甫泥州", "realms.configuration.region.west_us_2": "美利堅國渦昇暾州", "realms.configuration.region_preference.automatic_owner": "自取（因於領主之延）", "realms.configuration.region_preference.automatic_player": "自取（因於初入會話者）", "realms.missing.snapshot.error.text": "快照不可入領域", "recipe.notFound": "%s乃未知之方", "recipe.toast.description": "請君閱汝方集", "recipe.toast.title": "新方已開！", "record.nowPlaying": "方奏：%s", "recover_world.bug_tracker": "諫誤", "recover_world.button": "試復故", "recover_world.done.failed": "復故其嚮未成。", "recover_world.done.success": "復故成！", "recover_world.done.title": "復故既畢", "recover_world.issue.missing_file": "案闕", "recover_world.issue.none": "無誤", "recover_world.message": "欲讀生界資囊%s時見左誤。\n或復是生界以益舊之態，或報此誤於尋蹤。", "recover_world.no_fallback": "無所復者", "recover_world.restore": "試復故", "recover_world.restoring": "方試復生界⋯", "recover_world.state_entry": "%s之態：", "recover_world.state_entry.unknown": "不明", "recover_world.title": "載生界未成", "recover_world.warning": "載生界之賅未成", "resourcePack.broken_assets": "檔案既毀焉", "resourcePack.high_contrast.name": "高對比", "resourcePack.load_fail": "重載資囊而未成", "resourcePack.programmer_art.name": "碼師之藝", "resourcePack.runtime_failure": "檢得資囊之謬", "resourcePack.server.name": "資囊於此生界既定也", "resourcePack.title": "擇資囊", "resourcePack.vanilla.description": "礦藝之默認紋貌", "resourcePack.vanilla.name": "預置", "resourcepack.downloading": "資囊方下傳", "resourcepack.progress": "方載檔（%s兆節）⋯", "resourcepack.requesting": "方求之⋯", "screenshot.failure": "未可爲%s之畫", "screenshot.success": "爲畫一，題爲%s", "selectServer.add": "添伺服器", "selectServer.defaultName": "礦藝伺服器", "selectServer.delete": "除", "selectServer.deleteButton": "刪", "selectServer.deleteQuestion": "確除此伺服器乎？", "selectServer.deleteWarning": "「%s」永逝！（甚久！）", "selectServer.direct": "直連", "selectServer.edit": "纂", "selectServer.hiddenAddress": "（匿）", "selectServer.refresh": "重整", "selectServer.select": "入伺服器", "selectWorld.access_failure": "訪此生界而不得", "selectWorld.allowCommands": "允舞弊", "selectWorld.allowCommands.info": "令如/gamemode、/experience之屬", "selectWorld.allowCommands.new": "允令", "selectWorld.backupEraseCache": "除快取", "selectWorld.backupJoinConfirmButton": "立備份而載之", "selectWorld.backupJoinSkipButton": "吾知所以爲也！", "selectWorld.backupQuestion.customized": "自定之生界已不復得援", "selectWorld.backupQuestion.downgrade": "此生界逆之不可", "selectWorld.backupQuestion.experimental": "無可援未穩之生界", "selectWorld.backupQuestion.snapshot": "誠欲載入此生界乎?", "selectWorld.backupWarning.customized": "恨此版之礦藝未可自定其界。尚可載之如故，然新成者構如常界。見諒！", "selectWorld.backupWarning.downgrade": "此生界昔見遊於%s，今汝爲%s。將界降可致數據失也，不可保界能爲識作。若將，請先備份之！", "selectWorld.backupWarning.experimental": "茲有新設定試行於此生界者，或隨時而廢。將來亦或不復可用。務慎之。", "selectWorld.backupWarning.snapshot": "此生界昔見遊於%s，今汝爲%s。備以防其損！", "selectWorld.bonusItems": "獎箱", "selectWorld.cheats": "舞弊", "selectWorld.commands": "令", "selectWorld.conversion": "必轉換而戲！", "selectWorld.conversion.tooltip": "此生界必先啟於舊版（若一點六點四者）以求安變", "selectWorld.create": "闢新生界", "selectWorld.customizeType": "自定", "selectWorld.dataPacks": "錄囊", "selectWorld.data_read": "方覽錄⋯", "selectWorld.delete": "除", "selectWorld.deleteButton": "除", "selectWorld.deleteQuestion": "確除此生界乎？", "selectWorld.deleteWarning": "「%s」世將永世消弭！（甚久！）", "selectWorld.delete_failure": "除此生界而不得", "selectWorld.edit": "纂", "selectWorld.edit.backup": "行備世", "selectWorld.edit.backupCreated": "備世創矣：%s", "selectWorld.edit.backupFailed": "備世未果", "selectWorld.edit.backupFolder": "啟備世之資料夾", "selectWorld.edit.backupSize": "世之大，%s兆節也", "selectWorld.edit.export_worldgen_settings": "出錄闢生界之設", "selectWorld.edit.export_worldgen_settings.failure": "出錄未果", "selectWorld.edit.export_worldgen_settings.success": "既錄出", "selectWorld.edit.openFolder": "啟世之資料夾", "selectWorld.edit.optimize": "補益生界", "selectWorld.edit.resetIcon": "復設圖示", "selectWorld.edit.save": "記", "selectWorld.edit.title": "纂生界", "selectWorld.enterName": "生界之名", "selectWorld.enterSeed": "創世之種", "selectWorld.experimental": "試行", "selectWorld.experimental.details": "詳", "selectWorld.experimental.details.entry": "試行藝能之所需：%s", "selectWorld.experimental.details.title": "試行藝能之所需", "selectWorld.experimental.message": "慎哉！\n其設有須試行事者也。生界或崩壞滯阻焉。", "selectWorld.experimental.title": "試行之誡", "selectWorld.experiments": "試行之事", "selectWorld.experiments.info": "試行之事，尚未穩也。慎重而啟，損害難測。既啟則定，再不可閉。", "selectWorld.futureworld.error.text": "讀來版之生界而致誤。此舉固險，恨其未成。", "selectWorld.futureworld.error.title": "謬生！", "selectWorld.gameMode": "嬉遊之法", "selectWorld.gameMode.adventure": "勇", "selectWorld.gameMode.adventure.info": "同乎生，然塊方不爲之增損。", "selectWorld.gameMode.adventure.line1": "同乎生，", "selectWorld.gameMode.adventure.line2": "然塊方不爲之增損", "selectWorld.gameMode.creative": "創", "selectWorld.gameMode.creative.info": "砌磚遠洋，悉無制限。翱翔九天，衣食不竭，妖魔無傷。", "selectWorld.gameMode.creative.line1": "資藉豪富、無束而飛，", "selectWorld.gameMode.creative.line2": "破方石於彈指", "selectWorld.gameMode.hardcore": "極", "selectWorld.gameMode.hardcore.info": "固難莫變，卒不復生。", "selectWorld.gameMode.hardcore.line1": "同乎生，然極難，", "selectWorld.gameMode.hardcore.line2": "僅一命也", "selectWorld.gameMode.spectator": "覽", "selectWorld.gameMode.spectator.info": "可觀物而不可觸之。", "selectWorld.gameMode.spectator.line1": "可觀物而不可觸之", "selectWorld.gameMode.survival": "生", "selectWorld.gameMode.survival.info": "探奇世，築、採、製、鬭。", "selectWorld.gameMode.survival.line1": "獵貨、製物、升品、", "selectWorld.gameMode.survival.line2": "存命、充飢", "selectWorld.gameRules": "章則", "selectWorld.import_worldgen_settings": "入設", "selectWorld.import_worldgen_settings.failure": "入設誤", "selectWorld.import_worldgen_settings.select_file": "擇設定之檔（.json）", "selectWorld.incompatible.description": "弗能以是版啟是生界。\n此生界昔見遊於%s。", "selectWorld.incompatible.info": "版不合：%s", "selectWorld.incompatible.title": "版不合", "selectWorld.incompatible.tooltip": "弗能啟是生界，其建於不合之版。", "selectWorld.incompatible_series": "創於不容之版", "selectWorld.load_folder_access": "不可讀存生界之資料夾！", "selectWorld.loading_list": "方載生界表", "selectWorld.locked": "既佔於他礦藝程式", "selectWorld.mapFeatures": "天工生構", "selectWorld.mapFeatures.info": "鄉、沈舟等", "selectWorld.mapType": "生界之類", "selectWorld.mapType.normal": "凡", "selectWorld.moreWorldOptions": "生界選項之餘者⋯", "selectWorld.newWorld": "新生界", "selectWorld.recreate": "復建", "selectWorld.recreate.customized.text": "自定者不復得其援於是版。尚可復創以其種與置設，而失其自定。見諒！", "selectWorld.recreate.customized.title": "自定之生界不復得其援", "selectWorld.recreate.error.text": "復創此生界未果。", "selectWorld.recreate.error.title": "謬生！", "selectWorld.resource_load": "方備資⋯", "selectWorld.resultFolder": "將記於：", "selectWorld.search": "尋生界", "selectWorld.seedInfo": "闕則依天數", "selectWorld.select": "入所選", "selectWorld.targetFolder": "記世之資料夾：%s", "selectWorld.title": "擇生界", "selectWorld.tooltip.fromNewerVersion1": "此生界以新版存之，", "selectWorld.tooltip.fromNewerVersion2": "若讀此生界，其或成誤焉！", "selectWorld.tooltip.snapshot1": "載此界以快照前，", "selectWorld.tooltip.snapshot2": "勿忘備此界。", "selectWorld.unable_to_load": "未能載世", "selectWorld.version": "版本:", "selectWorld.versionJoinButton": "猶載之", "selectWorld.versionQuestion": "誠欲入此生界乎？", "selectWorld.versionUnknown": "未知", "selectWorld.versionWarning": "昔嬉於此世界者，乃以%s版。今以此版載之，或有損毀之虞！", "selectWorld.warning.deprecated.question": "其然功能汰矣，或失用於未來。確行?", "selectWorld.warning.deprecated.title": "危！上古之性爲斯設所用", "selectWorld.warning.experimental.question": "此設僅供一覽，來日或罷。確行之？", "selectWorld.warning.experimental.title": "危！試行之性爲斯設所用", "selectWorld.warning.lowDiskSpace.description": "爾械之礠庫，其空未足。\n戲之方行而礠庫以滿，生界或損焉。", "selectWorld.warning.lowDiskSpace.title": "誡！礠庫幾盈！", "selectWorld.world": "生界", "sign.edit": "纂牌文", "sleep.not_possible": "寐之遊者數不足逾今夕", "sleep.players_sleeping": "戲者就寢：%2$s之%1$s", "sleep.skipping_night": "寐度今宵", "slot.only_single_allowed": "但容獨槽，今得「%s」", "slot.unknown": "未知之槽：%s", "snbt.parser.empty_key": "鍵不可空", "snbt.parser.expected_binary_numeral": "應爲二進數", "snbt.parser.expected_decimal_numeral": "應爲十進數", "snbt.parser.expected_float_type": "應爲浮點數", "snbt.parser.expected_hex_escape": "字元之字長應爲%s", "snbt.parser.expected_hex_numeral": "應爲十六進數", "snbt.parser.expected_integer_type": "應爲整數", "snbt.parser.expected_non_negative_number": "應爲非赤數", "snbt.parser.expected_number_or_boolean": "應爲數或是非之值", "snbt.parser.expected_string_uuid": "應爲字串之敷有效之戶碼者", "snbt.parser.expected_unquoted_string": "應爲未引字串之有效者", "snbt.parser.infinity_not_allowed": "不得以無限之數", "snbt.parser.invalid_array_element_type": "無效陣元類", "snbt.parser.invalid_character_name": "無效萬國碼字名", "snbt.parser.invalid_codepoint": "無效萬國碼值：%s", "snbt.parser.invalid_string_contents": "無效字串文", "snbt.parser.invalid_unquoted_start": "字串無引號者毋以零—九之數，+ 抑 - 爲首", "snbt.parser.leading_zero_not_allowed": "十進數不可以零爲首", "snbt.parser.no_such_operation": "無是操作：%s", "snbt.parser.number_parse_failure": "析數未成：%s", "snbt.parser.undescore_not_allowed": "數之首尾不可有底綫焉", "soundCategory.ambient": "處地", "soundCategory.block": "塊方", "soundCategory.hostile": "敵者", "soundCategory.master": "主響度", "soundCategory.music": "伴樂", "soundCategory.neutral": "友者", "soundCategory.player": "戲者", "soundCategory.record": "絲竹", "soundCategory.ui": "戲幕", "soundCategory.voice": "聲語", "soundCategory.weather": "氣象", "spectatorMenu.close": "閉選單", "spectatorMenu.next_page": "次葉", "spectatorMenu.previous_page": "前葉", "spectatorMenu.root.prompt": "按一鍵以擇一令也，復按此鍵以用之。", "spectatorMenu.team_teleport": "刻至伍士處", "spectatorMenu.team_teleport.prompt": "擇一伍士，頃刻降臨", "spectatorMenu.teleport": "刻至戲者處", "spectatorMenu.teleport.prompt": "擇一戲者，頃刻降臨", "stat.generalButton": "常", "stat.itemsButton": "物", "stat.minecraft.animals_bred": "殖獸數", "stat.minecraft.aviate_one_cm": "翼翔之距", "stat.minecraft.bell_ring": "鳴鐘數", "stat.minecraft.boat_one_cm": "舟行距", "stat.minecraft.clean_armor": "素甲數", "stat.minecraft.clean_banner": "素旗數", "stat.minecraft.clean_shulker_box": "贆櫝洗數", "stat.minecraft.climb_one_cm": "攀距", "stat.minecraft.crouch_one_cm": "蜷行距", "stat.minecraft.damage_absorbed": "受傷之總", "stat.minecraft.damage_blocked_by_shield": "盾禦之傷", "stat.minecraft.damage_dealt": "施傷之總", "stat.minecraft.damage_dealt_absorbed": "所施之傷（爲受者）", "stat.minecraft.damage_dealt_resisted": "所施之傷（得禦之者）", "stat.minecraft.damage_resisted": "禦傷之總", "stat.minecraft.damage_taken": "總受傷", "stat.minecraft.deaths": "斃數", "stat.minecraft.drop": "棄物數", "stat.minecraft.eat_cake_slice": "食洋糕數", "stat.minecraft.enchant_item": "淬靈物數", "stat.minecraft.fall_one_cm": "墜距", "stat.minecraft.fill_cauldron": "盈釜數", "stat.minecraft.fish_caught": "漁獲", "stat.minecraft.fly_one_cm": "翔距", "stat.minecraft.happy_ghast_one_cm": "馭悅魄之距", "stat.minecraft.horse_one_cm": "馳距", "stat.minecraft.inspect_dispenser": "檢射械數", "stat.minecraft.inspect_dropper": "檢擲械數", "stat.minecraft.inspect_hopper": "檢漏斗數", "stat.minecraft.interact_with_anvil": "鐵砧用數", "stat.minecraft.interact_with_beacon": "烽火臺用數", "stat.minecraft.interact_with_blast_furnace": "冶爐用數", "stat.minecraft.interact_with_brewingstand": "煉藥臺用數", "stat.minecraft.interact_with_campfire": "營火用數", "stat.minecraft.interact_with_cartography_table": "輿圖案用數", "stat.minecraft.interact_with_crafting_table": "製物案用數", "stat.minecraft.interact_with_furnace": "爐用數", "stat.minecraft.interact_with_grindstone": "礪用數", "stat.minecraft.interact_with_lectern": "書檯用數", "stat.minecraft.interact_with_loom": "機杼用數", "stat.minecraft.interact_with_smithing_table": "鍛案用數", "stat.minecraft.interact_with_smoker": "燻爐用數", "stat.minecraft.interact_with_stonecutter": "鑿石器用數", "stat.minecraft.jump": "躍數", "stat.minecraft.leave_game": "離戲數", "stat.minecraft.minecart_one_cm": "礦車行距", "stat.minecraft.mob_kills": "殺生數", "stat.minecraft.open_barrel": "啟木桶數", "stat.minecraft.open_chest": "啟箱數", "stat.minecraft.open_enderchest": "啟終眇箱數", "stat.minecraft.open_shulker_box": "啟贆櫝數", "stat.minecraft.pig_one_cm": "馭豕之距", "stat.minecraft.play_noteblock": "絲竹匣播數", "stat.minecraft.play_record": "奏樂數", "stat.minecraft.play_time": "戲時", "stat.minecraft.player_kills": "殺戲者數", "stat.minecraft.pot_flower": "盆景數", "stat.minecraft.raid_trigger": "遭襲數", "stat.minecraft.raid_win": "捷襲數", "stat.minecraft.sleep_in_bed": "寐計", "stat.minecraft.sneak_time": "伏時", "stat.minecraft.sprint_one_cm": "奔距", "stat.minecraft.strider_one_cm": "馭熾足獸之距", "stat.minecraft.swim_one_cm": "游距", "stat.minecraft.talked_to_villager": "與鄉民語數", "stat.minecraft.target_hit": "中靶數", "stat.minecraft.time_since_death": "去昔死之時", "stat.minecraft.time_since_rest": "去昔寐之時", "stat.minecraft.total_world_time": "生界啟之時", "stat.minecraft.traded_with_villager": "與鄉民賈數", "stat.minecraft.trigger_trapped_chest": "觸機關箱數", "stat.minecraft.tune_noteblock": "絲竹匣調數", "stat.minecraft.use_cauldron": "釜中取水數", "stat.minecraft.walk_on_water_one_cm": "履水行距", "stat.minecraft.walk_one_cm": "行距", "stat.minecraft.walk_under_water_one_cm": "水中行距", "stat.mobsButton": "生靈", "stat_type.minecraft.broken": "破數", "stat_type.minecraft.crafted": "製物數", "stat_type.minecraft.dropped": "棄物數", "stat_type.minecraft.killed": "君既殺%2$s者%1$s回矣", "stat_type.minecraft.killed.none": "君未嘗殺%s", "stat_type.minecraft.killed_by": "%s既殺君%s回矣", "stat_type.minecraft.killed_by.none": "%s未嘗殺君", "stat_type.minecraft.mined": "掘數", "stat_type.minecraft.picked_up": "拾數", "stat_type.minecraft.used": "用數", "stats.none": "-", "structure_block.button.detect_size": "探", "structure_block.button.load": "讀", "structure_block.button.save": "記", "structure_block.custom_data": "自定錄名", "structure_block.detect_size": "尋結構之大小與經緯：", "structure_block.hover.corner": "角：%s", "structure_block.hover.data": "資：%s", "structure_block.hover.load": "讀：%s", "structure_block.hover.save": "記：%s", "structure_block.include_entities": "所含實體：", "structure_block.integrity": "架構之完全及種", "structure_block.integrity.integrity": "架構之完全", "structure_block.integrity.seed": "架構之種", "structure_block.invalid_structure_name": "架構名「%s」無效", "structure_block.load_not_found": "架構「%s」無存焉", "structure_block.load_prepare": "其架構「%s」之位籌也", "structure_block.load_success": "既讀架構於「%s」", "structure_block.mode.corner": "角", "structure_block.mode.data": "錄", "structure_block.mode.load": "讀", "structure_block.mode.save": "記", "structure_block.mode_info.corner": "角 ─ 地積之標", "structure_block.mode_info.data": "錄 ─ 是非之標", "structure_block.mode_info.load": "取 ─ 出案", "structure_block.mode_info.save": "存 ─ 入案", "structure_block.position": "相關經緯", "structure_block.position.x": "相關經距", "structure_block.position.y": "相關高距", "structure_block.position.z": "相關緯距", "structure_block.save_failure": "不可存架構「%s」", "structure_block.save_success": "架構以「%s」記也", "structure_block.show_air": "現不可視之塊：", "structure_block.show_boundingbox": "現界定框：", "structure_block.size": "架構之積", "structure_block.size.x": "經軸架構大小", "structure_block.size.y": "高軸結構大小", "structure_block.size.z": "緯軸架構大小", "structure_block.size_failure": "尋結構之積未得。試添與結構名相應之一角", "structure_block.size_success": "「%s」之幅已得測矣", "structure_block.strict": "謹置：", "structure_block.structure_name": "架構之名", "subtitles.ambient.cave": "悚然之聲", "subtitles.ambient.sound": "悚然之聲", "subtitles.block.amethyst_block.chime": "紫水玉丁丁然", "subtitles.block.amethyst_block.resonate": "紫水玉震", "subtitles.block.anvil.destroy": "鐵砧壞", "subtitles.block.anvil.land": "鐵砧墜", "subtitles.block.anvil.use": "鐵砧爲用", "subtitles.block.barrel.close": "木桶閉", "subtitles.block.barrel.open": "木桶啟", "subtitles.block.beacon.activate": "烽火臺啟", "subtitles.block.beacon.ambient": "烽火臺鳴", "subtitles.block.beacon.deactivate": "烽火臺熄", "subtitles.block.beacon.power_select": "擇烽火臺之效", "subtitles.block.beehive.drip": "蜜落", "subtitles.block.beehive.enter": "蜂入蜂箱", "subtitles.block.beehive.exit": "蜂去蜂箱", "subtitles.block.beehive.shear": "鉸削", "subtitles.block.beehive.work": "蜂作", "subtitles.block.bell.resonate": "鐘震", "subtitles.block.bell.use": "鐘鳴", "subtitles.block.big_dripleaf.tilt_down": "垂滴葉屈", "subtitles.block.big_dripleaf.tilt_up": "垂滴葉彈", "subtitles.block.blastfurnace.fire_crackle": "冶爐燃", "subtitles.block.brewing_stand.brew": "煉藥臺沫聲", "subtitles.block.bubble_column.bubble_pop": "氣泡壞", "subtitles.block.bubble_column.upwards_ambient": "氣泡浮", "subtitles.block.bubble_column.upwards_inside": "氣泡昇", "subtitles.block.bubble_column.whirlpool_ambient": "氣泡旋", "subtitles.block.bubble_column.whirlpool_inside": "氣泡捲", "subtitles.block.button.click": "鈕格然", "subtitles.block.cake.add_candle": "軋洋糕", "subtitles.block.campfire.crackle": "營火霍霍", "subtitles.block.candle.crackle": "燭燃", "subtitles.block.candle.extinguish": "燭滅", "subtitles.block.chest.close": "箱閉", "subtitles.block.chest.locked": "箱閂", "subtitles.block.chest.open": "箱啟", "subtitles.block.chorus_flower.death": "頌緲花凋", "subtitles.block.chorus_flower.grow": "頌緲花綻", "subtitles.block.comparator.click": "撃較赭儀", "subtitles.block.composter.empty": "肥箱空", "subtitles.block.composter.fill": "肥箱盈", "subtitles.block.composter.ready": "肥箱充", "subtitles.block.conduit.activate": "湧靈核啟", "subtitles.block.conduit.ambient": "湧靈核搏", "subtitles.block.conduit.attack.target": "湧靈核攻", "subtitles.block.conduit.deactivate": "湧靈核熄", "subtitles.block.copper_bulb.turn_off": "銅燈熄", "subtitles.block.copper_bulb.turn_on": "銅燈明", "subtitles.block.copper_trapdoor.close": "窖門闔", "subtitles.block.copper_trapdoor.open": "窖門開", "subtitles.block.crafter.craft": "製械製", "subtitles.block.crafter.fail": "製械製而未成", "subtitles.block.creaking_heart.hurt": "䦪心嗔", "subtitles.block.creaking_heart.idle": "悚然之聲", "subtitles.block.creaking_heart.spawn": "䦪心寤", "subtitles.block.deadbush.idle": "燥聲", "subtitles.block.decorated_pot.insert": "飾甕盈", "subtitles.block.decorated_pot.insert_fail": "飾甕搖", "subtitles.block.decorated_pot.shatter": "飾甕破", "subtitles.block.dispenser.dispense": "射械發", "subtitles.block.dispenser.fail": "射械發而未成", "subtitles.block.door.toggle": "門間關", "subtitles.block.dried_ghast.ambient": "乾呴聲", "subtitles.block.dried_ghast.ambient_water": "乾魄潤", "subtitles.block.dried_ghast.place_in_water": "乾魄浸", "subtitles.block.dried_ghast.transition": "乾魄釋然", "subtitles.block.dry_grass.ambient": "飂聲", "subtitles.block.enchantment_table.use": "淬靈案爲用", "subtitles.block.end_portal.spawn": "終界結界門開", "subtitles.block.end_portal_frame.fill": "終眇眼嵌", "subtitles.block.eyeblossom.close": "瞳榮瞑", "subtitles.block.eyeblossom.idle": "瞳榮窸窣", "subtitles.block.eyeblossom.open": "瞳榮盱", "subtitles.block.fence_gate.toggle": "扉間關", "subtitles.block.fire.ambient": "火霍霍", "subtitles.block.fire.extinguish": "火滅", "subtitles.block.firefly_bush.idle": "螢鳴", "subtitles.block.frogspawn.hatch": "蝌蚪孵", "subtitles.block.furnace.fire_crackle": "爐燃", "subtitles.block.generic.break": "塊壞", "subtitles.block.generic.fall": "物墜於塊", "subtitles.block.generic.footsteps": "步聲", "subtitles.block.generic.hit": "塊損", "subtitles.block.generic.place": "塊置", "subtitles.block.grindstone.use": "礪爲用", "subtitles.block.growing_plant.crop": "裁植", "subtitles.block.hanging_sign.waxed_interact_fail": "牌搖", "subtitles.block.honey_block.slide": "溜於蜜上", "subtitles.block.iron_trapdoor.close": "窖門闔", "subtitles.block.iron_trapdoor.open": "窖門開", "subtitles.block.lava.ambient": "熔石迸", "subtitles.block.lava.extinguish": "熔石凝", "subtitles.block.lever.click": "閘刀動", "subtitles.block.note_block.note": "絲竹匣奏鳴", "subtitles.block.pale_hanging_moss.idle": "悚然之聲", "subtitles.block.piston.move": "鞲鞴爲移", "subtitles.block.pointed_dripstone.drip_lava": "熔石落", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "熔石落於釜", "subtitles.block.pointed_dripstone.drip_water": "水落", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "水落於釜", "subtitles.block.pointed_dripstone.land": "鐘乳石崩墜", "subtitles.block.portal.ambient": "結界門作", "subtitles.block.portal.travel": "結界門聲衰", "subtitles.block.portal.trigger": "結界門聲噪", "subtitles.block.pressure_plate.click": "踏板格然", "subtitles.block.pumpkin.carve": "鉸鏤", "subtitles.block.redstone_torch.burnout": "炬焚", "subtitles.block.respawn_anchor.ambient": "復生錨蕭蕭", "subtitles.block.respawn_anchor.charge": "復生錨得力", "subtitles.block.respawn_anchor.deplete": "復生錨虧", "subtitles.block.respawn_anchor.set_spawn": "復生錨定生處", "subtitles.block.sand.idle": "沙聲", "subtitles.block.sand.wind": "飂聲", "subtitles.block.sculk.charge": "幽匿見泡", "subtitles.block.sculk.spread": "幽匿散", "subtitles.block.sculk_catalyst.bloom": "幽匿引蕃", "subtitles.block.sculk_sensor.clicking": "幽匿探子顫", "subtitles.block.sculk_sensor.clicking_stop": "幽匿探子止", "subtitles.block.sculk_shrieker.shriek": "幽匿嘯嚎", "subtitles.block.shulker_box.close": "贆櫝閉", "subtitles.block.shulker_box.open": "贆櫝啟", "subtitles.block.sign.waxed_interact_fail": "牌搖", "subtitles.block.smithing_table.use": "鍛案爲用", "subtitles.block.smoker.smoke": "燻爐出煙", "subtitles.block.sniffer_egg.crack": "嗅獸卵裂", "subtitles.block.sniffer_egg.hatch": "嗅獸卵孵", "subtitles.block.sniffer_egg.plop": "嗅獸產卵", "subtitles.block.sponge.absorb": "海綿汲", "subtitles.block.sweet_berry_bush.pick_berries": "甜莓出", "subtitles.block.trapdoor.close": "窖門闔", "subtitles.block.trapdoor.open": "窖門開", "subtitles.block.trapdoor.toggle": "窖門間關", "subtitles.block.trial_spawner.about_to_spawn_item": "厄物備", "subtitles.block.trial_spawner.ambient": "煉孳衍籠霍霍", "subtitles.block.trial_spawner.ambient_charged": "厄煉孳衍籠霍霍", "subtitles.block.trial_spawner.ambient_ominous": "厄煉孳衍籠霍霍", "subtitles.block.trial_spawner.charge_activate": "煉孳衍籠泛祲", "subtitles.block.trial_spawner.close_shutter": "煉孳衍籠閉", "subtitles.block.trial_spawner.detect_player": "煉孳衍籠備", "subtitles.block.trial_spawner.eject_item": "煉孳衍籠擲物", "subtitles.block.trial_spawner.ominous_activate": "煉孳衍籠泛厄", "subtitles.block.trial_spawner.open_shutter": "煉孳衍籠啟", "subtitles.block.trial_spawner.spawn_item": "厄物遺", "subtitles.block.trial_spawner.spawn_item_begin": "厄物現", "subtitles.block.trial_spawner.spawn_mob": "煉孳衍籠孳生靈", "subtitles.block.tripwire.attach": "絆線合", "subtitles.block.tripwire.click": "絆綫格然", "subtitles.block.tripwire.detach": "絆線分", "subtitles.block.vault.activate": "寶篋燃", "subtitles.block.vault.ambient": "寶篋霍霍", "subtitles.block.vault.close_shutter": "寶篋閉", "subtitles.block.vault.deactivate": "寶篋熄", "subtitles.block.vault.eject_item": "寶篋擲物", "subtitles.block.vault.insert_item": "寶篋開", "subtitles.block.vault.insert_item_fail": "寶篋開而未成", "subtitles.block.vault.open_shutter": "寶篋啟", "subtitles.block.vault.reject_rewarded_player": "寶篋拒復賞", "subtitles.block.water.ambient": "汩汩聲", "subtitles.block.wet_sponge.dries": "海綿乾", "subtitles.chiseled_bookshelf.insert": "置書", "subtitles.chiseled_bookshelf.insert_enchanted": "置淬靈書", "subtitles.chiseled_bookshelf.take": "取書", "subtitles.chiseled_bookshelf.take_enchanted": "取淬靈書", "subtitles.enchant.thorns.hit": "荊棘刺", "subtitles.entity.allay.ambient_with_item": "悅靈覓", "subtitles.entity.allay.ambient_without_item": "悅靈欲", "subtitles.entity.allay.death": "悅靈斃", "subtitles.entity.allay.hurt": "悅靈傷", "subtitles.entity.allay.item_given": "悅靈啞然笑", "subtitles.entity.allay.item_taken": "悅靈釋然", "subtitles.entity.allay.item_thrown": "悅靈擲", "subtitles.entity.armadillo.ambient": "犰狳鳴", "subtitles.entity.armadillo.brush": "鱗落", "subtitles.entity.armadillo.death": "犰狳斃", "subtitles.entity.armadillo.eat": "犰狳食", "subtitles.entity.armadillo.hurt": "犰狳傷", "subtitles.entity.armadillo.hurt_reduced": "犰狳自蔽", "subtitles.entity.armadillo.land": "犰狳落", "subtitles.entity.armadillo.peek": "犰狳窺", "subtitles.entity.armadillo.roll": "犰狳蜷", "subtitles.entity.armadillo.scute_drop": "犰狳遺鱗", "subtitles.entity.armadillo.unroll_finish": "犰狳舒", "subtitles.entity.armadillo.unroll_start": "犰狳窺", "subtitles.entity.armor_stand.fall": "物墜", "subtitles.entity.arrow.hit": "矢中", "subtitles.entity.arrow.hit_player": "撃戲者", "subtitles.entity.arrow.shoot": "矢發", "subtitles.entity.axolotl.attack": "螈攻", "subtitles.entity.axolotl.death": "螈斃", "subtitles.entity.axolotl.hurt": "螈傷", "subtitles.entity.axolotl.idle_air": "螈鳴", "subtitles.entity.axolotl.idle_water": "螈鳴", "subtitles.entity.axolotl.splash": "螈擊水", "subtitles.entity.axolotl.swim": "螈游", "subtitles.entity.bat.ambient": "蟙䘃叫", "subtitles.entity.bat.death": "蟙䘃斃", "subtitles.entity.bat.hurt": "蟙䘃傷", "subtitles.entity.bat.takeoff": "蟙䘃飛", "subtitles.entity.bee.ambient": "蜂䎕鳴", "subtitles.entity.bee.death": "蜂斃", "subtitles.entity.bee.hurt": "蜂傷", "subtitles.entity.bee.loop": "蜂䎕鳴", "subtitles.entity.bee.loop_aggressive": "蜂慍然䎕鳴", "subtitles.entity.bee.pollinate": "蜂悅然䎕鳴", "subtitles.entity.bee.sting": "蜂螫", "subtitles.entity.blaze.ambient": "炎靈息", "subtitles.entity.blaze.burn": "炎靈霍霍", "subtitles.entity.blaze.death": "炎靈斃", "subtitles.entity.blaze.hurt": "炎靈傷", "subtitles.entity.blaze.shoot": "炎靈發", "subtitles.entity.boat.paddle_land": "泛舟", "subtitles.entity.boat.paddle_water": "泛舟", "subtitles.entity.bogged.ambient": "濘髑之格格聲", "subtitles.entity.bogged.death": "濘髑斃", "subtitles.entity.bogged.hurt": "濘髑傷", "subtitles.entity.breeze.charge": "風靈張", "subtitles.entity.breeze.death": "風靈斃", "subtitles.entity.breeze.deflect": "風靈禦", "subtitles.entity.breeze.hurt": "風靈傷", "subtitles.entity.breeze.idle_air": "風靈飛", "subtitles.entity.breeze.idle_ground": "風靈颯颯", "subtitles.entity.breeze.inhale": "風靈屛氣", "subtitles.entity.breeze.jump": "風靈躍", "subtitles.entity.breeze.land": "風靈落", "subtitles.entity.breeze.shoot": "風靈發", "subtitles.entity.breeze.slide": "風靈翔", "subtitles.entity.breeze.whirl": "風靈旋", "subtitles.entity.breeze.wind_burst": "風彈爆", "subtitles.entity.camel.ambient": "橐駝鳴", "subtitles.entity.camel.dash": "橐駝衝", "subtitles.entity.camel.dash_ready": "橐駝復故", "subtitles.entity.camel.death": "橐駝斃", "subtitles.entity.camel.eat": "橐駝食", "subtitles.entity.camel.hurt": "橐駝傷", "subtitles.entity.camel.saddle": "著鞍", "subtitles.entity.camel.sit": "橐駝伏", "subtitles.entity.camel.stand": "橐駝起", "subtitles.entity.camel.step": "橐駝步", "subtitles.entity.camel.step_sand": "橐駝踏沙", "subtitles.entity.cat.ambient": "貓鳴", "subtitles.entity.cat.beg_for_food": "貓乞食", "subtitles.entity.cat.death": "貓斃", "subtitles.entity.cat.eat": "貓食", "subtitles.entity.cat.hiss": "貓嘶鳴", "subtitles.entity.cat.hurt": "貓傷", "subtitles.entity.cat.purr": "貓鼾", "subtitles.entity.chicken.ambient": "雞鳴", "subtitles.entity.chicken.death": "雞斃", "subtitles.entity.chicken.egg": "雞産卵", "subtitles.entity.chicken.hurt": "雞傷", "subtitles.entity.cod.death": "鱈斃", "subtitles.entity.cod.flop": "鱈躍", "subtitles.entity.cod.hurt": "鱈傷", "subtitles.entity.cow.ambient": "牛牟", "subtitles.entity.cow.death": "牛斃", "subtitles.entity.cow.hurt": "牛傷", "subtitles.entity.cow.milk": "牛獻乳", "subtitles.entity.creaking.activate": "䦪鬼瞵", "subtitles.entity.creaking.ambient": "䦪鬼䦪", "subtitles.entity.creaking.attack": "䦪鬼攻", "subtitles.entity.creaking.deactivate": "䦪鬼息", "subtitles.entity.creaking.death": "䦪鬼隤", "subtitles.entity.creaking.freeze": "䦪鬼止", "subtitles.entity.creaking.spawn": "䦪鬼現", "subtitles.entity.creaking.sway": "䦪鬼受擊", "subtitles.entity.creaking.twitch": "䦪鬼搐", "subtitles.entity.creaking.unfreeze": "䦪鬼行", "subtitles.entity.creeper.death": "伏臨斃", "subtitles.entity.creeper.hurt": "伏臨傷", "subtitles.entity.creeper.primed": "伏臨嘶鳴", "subtitles.entity.dolphin.ambient": "海豚之嘰嘰聲", "subtitles.entity.dolphin.ambient_water": "海豚長嘯", "subtitles.entity.dolphin.attack": "海豚攻", "subtitles.entity.dolphin.death": "海豚斃", "subtitles.entity.dolphin.eat": "海豚食", "subtitles.entity.dolphin.hurt": "海豚傷", "subtitles.entity.dolphin.jump": "海豚躍", "subtitles.entity.dolphin.play": "海豚戲", "subtitles.entity.dolphin.splash": "海豚撃水", "subtitles.entity.dolphin.swim": "海豚游", "subtitles.entity.donkey.ambient": "驢鳴", "subtitles.entity.donkey.angry": "驢鳴", "subtitles.entity.donkey.chest": "驢著箱", "subtitles.entity.donkey.death": "驢斃", "subtitles.entity.donkey.eat": "驢食", "subtitles.entity.donkey.hurt": "驢傷", "subtitles.entity.donkey.jump": "驢躍", "subtitles.entity.drowned.ambient": "溺屍之汩汩聲", "subtitles.entity.drowned.ambient_water": "溺屍之汩汩聲", "subtitles.entity.drowned.death": "溺屍斃", "subtitles.entity.drowned.hurt": "溺屍傷", "subtitles.entity.drowned.shoot": "溺屍擲戟", "subtitles.entity.drowned.step": "溺屍步", "subtitles.entity.drowned.swim": "溺屍游", "subtitles.entity.egg.throw": "擲卵", "subtitles.entity.elder_guardian.ambient": "古海衛呻鳴", "subtitles.entity.elder_guardian.ambient_land": "古海衛擺尾", "subtitles.entity.elder_guardian.curse": "古海衛咒", "subtitles.entity.elder_guardian.death": "古海衛斃", "subtitles.entity.elder_guardian.flop": "古海衛躍", "subtitles.entity.elder_guardian.hurt": "古海衛傷", "subtitles.entity.ender_dragon.ambient": "終眇龍號", "subtitles.entity.ender_dragon.death": "終眇龍斃", "subtitles.entity.ender_dragon.flap": "終眇龍撃翼", "subtitles.entity.ender_dragon.growl": "終眇龍吼", "subtitles.entity.ender_dragon.hurt": "終眇龍傷", "subtitles.entity.ender_dragon.shoot": "終眇龍發", "subtitles.entity.ender_eye.death": "終眇眼落", "subtitles.entity.ender_eye.launch": "終眇眼射", "subtitles.entity.ender_pearl.throw": "終眇玥擲", "subtitles.entity.enderman.ambient": "終眇使鳴", "subtitles.entity.enderman.death": "終眇使斃", "subtitles.entity.enderman.hurt": "終眇使傷", "subtitles.entity.enderman.scream": "終眇使嘯", "subtitles.entity.enderman.stare": "終眇使號", "subtitles.entity.enderman.teleport": "終眇使移形", "subtitles.entity.endermite.ambient": "終眇蟎竄", "subtitles.entity.endermite.death": "終眇蟎斃", "subtitles.entity.endermite.hurt": "終眇蟎傷", "subtitles.entity.evoker.ambient": "御魔使低語", "subtitles.entity.evoker.cast_spell": "御魔使咒", "subtitles.entity.evoker.celebrate": "御魔使歡呼", "subtitles.entity.evoker.death": "御魔使斃", "subtitles.entity.evoker.hurt": "御魔使傷", "subtitles.entity.evoker.prepare_attack": "御魔使欲攻", "subtitles.entity.evoker.prepare_summon": "御魔使欲召魔", "subtitles.entity.evoker.prepare_wololo": "御魔使欲惑", "subtitles.entity.evoker_fangs.attack": "魔齒嚙", "subtitles.entity.experience_orb.pickup": "得經驗", "subtitles.entity.firework_rocket.blast": "焰火爆", "subtitles.entity.firework_rocket.launch": "焰火發", "subtitles.entity.firework_rocket.twinkle": "焰火爍", "subtitles.entity.fish.swim": "擊水", "subtitles.entity.fishing_bobber.retrieve": "漁標反", "subtitles.entity.fishing_bobber.splash": "漁標撃水", "subtitles.entity.fishing_bobber.throw": "漁標發", "subtitles.entity.fox.aggro": "狐慍", "subtitles.entity.fox.ambient": "狐之嘰嘰聲", "subtitles.entity.fox.bite": "狐嚙", "subtitles.entity.fox.death": "狐斃", "subtitles.entity.fox.eat": "狐食", "subtitles.entity.fox.hurt": "狐傷", "subtitles.entity.fox.screech": "狐嚎", "subtitles.entity.fox.sleep": "狐鼾", "subtitles.entity.fox.sniff": "狐嗅", "subtitles.entity.fox.spit": "狐之呔呔聲", "subtitles.entity.fox.teleport": "狐移形", "subtitles.entity.frog.ambient": "鼃鳴", "subtitles.entity.frog.death": "鼃斃", "subtitles.entity.frog.eat": "鼃食", "subtitles.entity.frog.hurt": "鼃傷", "subtitles.entity.frog.lay_spawn": "鼃產卵", "subtitles.entity.frog.long_jump": "鼃躍", "subtitles.entity.generic.big_fall": "物墜", "subtitles.entity.generic.burn": "焚", "subtitles.entity.generic.death": "斃", "subtitles.entity.generic.drink": "飲", "subtitles.entity.generic.eat": "食", "subtitles.entity.generic.explode": "爆", "subtitles.entity.generic.extinguish_fire": "火滅", "subtitles.entity.generic.hurt": "物傷", "subtitles.entity.generic.small_fall": "物躓", "subtitles.entity.generic.splash": "撃水", "subtitles.entity.generic.swim": "游", "subtitles.entity.generic.wind_burst": "風彈爆", "subtitles.entity.ghast.ambient": "魄泣", "subtitles.entity.ghast.death": "魄斃", "subtitles.entity.ghast.hurt": "魄傷", "subtitles.entity.ghast.shoot": "魄發", "subtitles.entity.ghastling.ambient": "魄兒啾啾", "subtitles.entity.ghastling.death": "魄兒斃", "subtitles.entity.ghastling.hurt": "魄兒傷", "subtitles.entity.ghastling.spawn": "魄兒現", "subtitles.entity.glow_item_frame.add_item": "爍置具匡填", "subtitles.entity.glow_item_frame.break": "爍置具匡壞", "subtitles.entity.glow_item_frame.place": "爍置具匡置", "subtitles.entity.glow_item_frame.remove_item": "爍置具匡清", "subtitles.entity.glow_item_frame.rotate_item": "爍置具匡旋", "subtitles.entity.glow_squid.ambient": "爍鰂游", "subtitles.entity.glow_squid.death": "爍鰂斃", "subtitles.entity.glow_squid.hurt": "爍鰂傷", "subtitles.entity.glow_squid.squirt": "爍鰂濺墨", "subtitles.entity.goat.ambient": "山羊咩", "subtitles.entity.goat.death": "山羊斃", "subtitles.entity.goat.eat": "山羊食", "subtitles.entity.goat.horn_break": "山羊角解", "subtitles.entity.goat.hurt": "山羊傷", "subtitles.entity.goat.long_jump": "山羊躍", "subtitles.entity.goat.milk": "山羊獻乳", "subtitles.entity.goat.prepare_ram": "山羊踏", "subtitles.entity.goat.ram_impact": "山羊撞", "subtitles.entity.goat.screaming.ambient": "山羊嘶", "subtitles.entity.goat.step": "山羊步", "subtitles.entity.guardian.ambient": "海衛呻鳴", "subtitles.entity.guardian.ambient_land": "海衛擺尾", "subtitles.entity.guardian.attack": "海衛發", "subtitles.entity.guardian.death": "海衛斃", "subtitles.entity.guardian.flop": "海衛躍", "subtitles.entity.guardian.hurt": "海衛傷", "subtitles.entity.happy_ghast.ambient": "悅魄吟哦", "subtitles.entity.happy_ghast.death": "悅魄斃", "subtitles.entity.happy_ghast.equip": "著轡", "subtitles.entity.happy_ghast.harness_goggles_down": "悅魄備", "subtitles.entity.happy_ghast.harness_goggles_up": "悅魄止", "subtitles.entity.happy_ghast.hurt": "悅魄傷", "subtitles.entity.happy_ghast.unequip": "解轡", "subtitles.entity.hoglin.ambient": "獷豕咆哮", "subtitles.entity.hoglin.angry": "獷豕怒吼", "subtitles.entity.hoglin.attack": "獷豕攻", "subtitles.entity.hoglin.converted_to_zombified": "獷豕終腐", "subtitles.entity.hoglin.death": "獷豕斃", "subtitles.entity.hoglin.hurt": "獷豕傷", "subtitles.entity.hoglin.retreat": "獷豕逃", "subtitles.entity.hoglin.step": "獷豕行", "subtitles.entity.horse.ambient": "馬鳴", "subtitles.entity.horse.angry": "馬鳴", "subtitles.entity.horse.armor": "著馬甲", "subtitles.entity.horse.breathe": "馬息", "subtitles.entity.horse.death": "馬斃", "subtitles.entity.horse.eat": "馬食", "subtitles.entity.horse.gallop": "馬馳騁", "subtitles.entity.horse.hurt": "馬傷", "subtitles.entity.horse.jump": "馬躍", "subtitles.entity.horse.saddle": "著鞍", "subtitles.entity.husk.ambient": "枯屍呻鳴", "subtitles.entity.husk.converted_to_zombie": "枯屍終潤", "subtitles.entity.husk.death": "枯屍斃", "subtitles.entity.husk.hurt": "枯屍傷", "subtitles.entity.illusioner.ambient": "幻術師低語", "subtitles.entity.illusioner.cast_spell": "幻術師咒", "subtitles.entity.illusioner.death": "幻術師斃", "subtitles.entity.illusioner.hurt": "幻術師傷", "subtitles.entity.illusioner.mirror_move": "幻術師移影", "subtitles.entity.illusioner.prepare_blindness": "幻術師欲盲", "subtitles.entity.illusioner.prepare_mirror": "幻術師欲影", "subtitles.entity.iron_golem.attack": "鐵傀儡攻", "subtitles.entity.iron_golem.damage": "鐵傀儡損", "subtitles.entity.iron_golem.death": "鐵傀儡斃", "subtitles.entity.iron_golem.hurt": "鐵傀儡傷", "subtitles.entity.iron_golem.repair": "鐵傀儡修", "subtitles.entity.item.break": "物壞", "subtitles.entity.item.pickup": "拾物", "subtitles.entity.item_frame.add_item": "置具匡填", "subtitles.entity.item_frame.break": "置具匡壞", "subtitles.entity.item_frame.place": "置具匡置", "subtitles.entity.item_frame.remove_item": "置具匡清", "subtitles.entity.item_frame.rotate_item": "置具匡旋", "subtitles.entity.leash_knot.break": "繯解", "subtitles.entity.leash_knot.place": "繯繫", "subtitles.entity.lightning_bolt.impact": "列缺霹靂", "subtitles.entity.lightning_bolt.thunder": "雷霆乍驚", "subtitles.entity.llama.ambient": "美洲駝鳴", "subtitles.entity.llama.angry": "美洲駝慍鳴", "subtitles.entity.llama.chest": "美洲駝著箱", "subtitles.entity.llama.death": "美洲駝斃", "subtitles.entity.llama.eat": "美洲駝食", "subtitles.entity.llama.hurt": "美洲駝傷", "subtitles.entity.llama.spit": "美洲駝涎", "subtitles.entity.llama.step": "美洲駝步", "subtitles.entity.llama.swag": "美洲駝著飾", "subtitles.entity.magma_cube.death": "火漿魔斃", "subtitles.entity.magma_cube.hurt": "火漿魔傷", "subtitles.entity.magma_cube.squish": "火漿魔躍", "subtitles.entity.minecart.inside": "礦車甸甸", "subtitles.entity.minecart.inside_underwater": "礦車甸甸水中", "subtitles.entity.minecart.riding": "礦車行", "subtitles.entity.mooshroom.convert": "牟蕈變", "subtitles.entity.mooshroom.eat": "牟蕈食", "subtitles.entity.mooshroom.milk": "牟蕈獻乳", "subtitles.entity.mooshroom.suspicious_milk": "牟蕈獻謎羹", "subtitles.entity.mule.ambient": "騾鳴", "subtitles.entity.mule.angry": "騾嘶鳴", "subtitles.entity.mule.chest": "騾著箱", "subtitles.entity.mule.death": "騾斃", "subtitles.entity.mule.eat": "騾食", "subtitles.entity.mule.hurt": "騾傷", "subtitles.entity.mule.jump": "騾躍", "subtitles.entity.painting.break": "畫壞", "subtitles.entity.painting.place": "畫置", "subtitles.entity.panda.aggressive_ambient": "貓熊慍", "subtitles.entity.panda.ambient": "貓熊鼻息", "subtitles.entity.panda.bite": "貓熊嚙", "subtitles.entity.panda.cant_breed": "貓熊嘯", "subtitles.entity.panda.death": "貓熊斃", "subtitles.entity.panda.eat": "貓熊食", "subtitles.entity.panda.hurt": "貓熊傷", "subtitles.entity.panda.pre_sneeze": "貓熊齉", "subtitles.entity.panda.sneeze": "貓熊嚏", "subtitles.entity.panda.step": "貓熊步", "subtitles.entity.panda.worried_ambient": "貓熊嗚", "subtitles.entity.parrot.ambient": "鸚鵡語", "subtitles.entity.parrot.death": "鸚鵡斃", "subtitles.entity.parrot.eats": "鸚鵡食", "subtitles.entity.parrot.fly": "鸚鵡撲翼", "subtitles.entity.parrot.hurts": "鸚鵡傷", "subtitles.entity.parrot.imitate.blaze": "鸚鵡息", "subtitles.entity.parrot.imitate.bogged": "鸚鵡之格格聲", "subtitles.entity.parrot.imitate.breeze": "鸚鵡颯颯", "subtitles.entity.parrot.imitate.creaking": "鸚鵡䦪", "subtitles.entity.parrot.imitate.creeper": "鸚鵡嘶鳴", "subtitles.entity.parrot.imitate.drowned": "鸚鵡之汩汩聲", "subtitles.entity.parrot.imitate.elder_guardian": "鸚鵡呻鳴", "subtitles.entity.parrot.imitate.ender_dragon": "鸚鵡吼", "subtitles.entity.parrot.imitate.endermite": "鸚鵡竄", "subtitles.entity.parrot.imitate.evoker": "鸚鵡低語", "subtitles.entity.parrot.imitate.ghast": "鸚鵡泣", "subtitles.entity.parrot.imitate.guardian": "鸚鵡呻鳴", "subtitles.entity.parrot.imitate.hoglin": "鸚鵡咆哮", "subtitles.entity.parrot.imitate.husk": "鸚鵡呻鳴", "subtitles.entity.parrot.imitate.illusioner": "鸚鵡低語", "subtitles.entity.parrot.imitate.magma_cube": "鸚鵡躍", "subtitles.entity.parrot.imitate.phantom": "鸚鵡嚎", "subtitles.entity.parrot.imitate.piglin": "鸚鵡哼", "subtitles.entity.parrot.imitate.piglin_brute": "鸚鵡哼", "subtitles.entity.parrot.imitate.pillager": "鸚鵡低語", "subtitles.entity.parrot.imitate.ravager": "鸚鵡鳴", "subtitles.entity.parrot.imitate.shulker": "鸚鵡窺", "subtitles.entity.parrot.imitate.silverfish": "鸚鵡嘶鳴", "subtitles.entity.parrot.imitate.skeleton": "鸚鵡之格格聲", "subtitles.entity.parrot.imitate.slime": "鸚鵡躍", "subtitles.entity.parrot.imitate.spider": "鸚鵡嘶鳴", "subtitles.entity.parrot.imitate.stray": "鸚鵡之格格聲", "subtitles.entity.parrot.imitate.vex": "鸚鵡慍", "subtitles.entity.parrot.imitate.vindicator": "鸚鵡低語", "subtitles.entity.parrot.imitate.warden": "鸚鵡鳴", "subtitles.entity.parrot.imitate.witch": "鸚鵡哂", "subtitles.entity.parrot.imitate.wither": "鸚鵡慍", "subtitles.entity.parrot.imitate.wither_skeleton": "鸚鵡之格格聲", "subtitles.entity.parrot.imitate.zoglin": "鸚鵡咆哮", "subtitles.entity.parrot.imitate.zombie": "鸚鵡呻鳴", "subtitles.entity.parrot.imitate.zombie_villager": "鸚鵡呻鳴", "subtitles.entity.phantom.ambient": "魘靈嚎", "subtitles.entity.phantom.bite": "魘靈嚙", "subtitles.entity.phantom.death": "魘靈斃", "subtitles.entity.phantom.flap": "魘靈撃翼", "subtitles.entity.phantom.hurt": "魘靈傷", "subtitles.entity.phantom.swoop": "魘靈俯翔", "subtitles.entity.pig.ambient": "豕鳴", "subtitles.entity.pig.death": "豕斃", "subtitles.entity.pig.hurt": "豕傷", "subtitles.entity.pig.saddle": "著鞍", "subtitles.entity.piglin.admiring_item": "豕靈察其物", "subtitles.entity.piglin.ambient": "豕靈哼", "subtitles.entity.piglin.angry": "豕靈怒哼", "subtitles.entity.piglin.celebrate": "豕靈慶", "subtitles.entity.piglin.converted_to_zombified": "豕靈終腐", "subtitles.entity.piglin.death": "豕靈斃", "subtitles.entity.piglin.hurt": "豕靈傷", "subtitles.entity.piglin.jealous": "豕靈妒哼", "subtitles.entity.piglin.retreat": "豕靈遁", "subtitles.entity.piglin.step": "豕靈步", "subtitles.entity.piglin_brute.ambient": "暴豕靈哼", "subtitles.entity.piglin_brute.angry": "暴豕靈怒哼", "subtitles.entity.piglin_brute.converted_to_zombified": "暴豕靈終腐", "subtitles.entity.piglin_brute.death": "暴豕靈斃", "subtitles.entity.piglin_brute.hurt": "暴豕靈傷", "subtitles.entity.piglin_brute.step": "暴豕靈步", "subtitles.entity.pillager.ambient": "劫寇低語", "subtitles.entity.pillager.celebrate": "劫寇歡呼", "subtitles.entity.pillager.death": "劫寇斃", "subtitles.entity.pillager.hurt": "劫寇傷", "subtitles.entity.player.attack.crit": "暴擊", "subtitles.entity.player.attack.knockback": "擊退", "subtitles.entity.player.attack.strong": "重擊", "subtitles.entity.player.attack.sweep": "橫斬之擊", "subtitles.entity.player.attack.weak": "弱擊", "subtitles.entity.player.burp": "呃", "subtitles.entity.player.death": "戲者斃", "subtitles.entity.player.freeze_hurt": "戲者凍", "subtitles.entity.player.hurt": "戲者傷", "subtitles.entity.player.hurt_drown": "戲者溺", "subtitles.entity.player.hurt_on_fire": "戲者焚", "subtitles.entity.player.levelup": "戲者晉", "subtitles.entity.player.teleport": "戲者移形", "subtitles.entity.polar_bear.ambient": "雪熊呻鳴", "subtitles.entity.polar_bear.ambient_baby": "幼雪熊低鳴", "subtitles.entity.polar_bear.death": "雪熊斃", "subtitles.entity.polar_bear.hurt": "雪熊傷", "subtitles.entity.polar_bear.warning": "雪熊吼", "subtitles.entity.potion.splash": "甁破", "subtitles.entity.potion.throw": "擲甁", "subtitles.entity.puffer_fish.blow_out": "河豚縮", "subtitles.entity.puffer_fish.blow_up": "河豚擴", "subtitles.entity.puffer_fish.death": "河豚斃", "subtitles.entity.puffer_fish.flop": "河豚躍", "subtitles.entity.puffer_fish.hurt": "河豚傷", "subtitles.entity.puffer_fish.sting": "河豚螫", "subtitles.entity.rabbit.ambient": "兔鳴", "subtitles.entity.rabbit.attack": "兔攻", "subtitles.entity.rabbit.death": "兔斃", "subtitles.entity.rabbit.hurt": "兔傷", "subtitles.entity.rabbit.jump": "兔躍", "subtitles.entity.ravager.ambient": "劫獸鳴", "subtitles.entity.ravager.attack": "劫獸嚙", "subtitles.entity.ravager.celebrate": "劫獸歡呼", "subtitles.entity.ravager.death": "劫獸斃", "subtitles.entity.ravager.hurt": "劫獸傷", "subtitles.entity.ravager.roar": "劫獸吼", "subtitles.entity.ravager.step": "劫獸步", "subtitles.entity.ravager.stunned": "劫獸眩", "subtitles.entity.salmon.death": "鮭斃", "subtitles.entity.salmon.flop": "鮭躍", "subtitles.entity.salmon.hurt": "鮭傷", "subtitles.entity.sheep.ambient": "綿羊鳴", "subtitles.entity.sheep.death": "綿羊斃", "subtitles.entity.sheep.hurt": "綿羊傷", "subtitles.entity.shulker.ambient": "匿贆窺", "subtitles.entity.shulker.close": "匿贆閉", "subtitles.entity.shulker.death": "匿贆斃", "subtitles.entity.shulker.hurt": "匿贆傷", "subtitles.entity.shulker.open": "匿贆啟", "subtitles.entity.shulker.shoot": "匿贆發", "subtitles.entity.shulker.teleport": "匿贆移形", "subtitles.entity.shulker_bullet.hit": "匿贆彈爆", "subtitles.entity.shulker_bullet.hurt": "匿贆彈裂", "subtitles.entity.silverfish.ambient": "蟫嘶鳴", "subtitles.entity.silverfish.death": "蟫斃", "subtitles.entity.silverfish.hurt": "蟫傷", "subtitles.entity.skeleton.ambient": "骷髏之格格聲", "subtitles.entity.skeleton.converted_to_stray": "骷髏終凍", "subtitles.entity.skeleton.death": "骷髏斃", "subtitles.entity.skeleton.hurt": "骷髏傷", "subtitles.entity.skeleton.shoot": "骷髏發", "subtitles.entity.skeleton_horse.ambient": "骷髏馬鳴", "subtitles.entity.skeleton_horse.death": "骷髏馬斃", "subtitles.entity.skeleton_horse.hurt": "骷髏馬傷", "subtitles.entity.skeleton_horse.jump_water": "骷髏馬躍", "subtitles.entity.skeleton_horse.swim": "骷髏馬游", "subtitles.entity.slime.attack": "黏膠魔攻", "subtitles.entity.slime.death": "黏膠魔斃", "subtitles.entity.slime.hurt": "黏膠魔傷", "subtitles.entity.slime.squish": "黏膠魔躍", "subtitles.entity.sniffer.death": "嗅獸斃", "subtitles.entity.sniffer.digging": "嗅獸掘", "subtitles.entity.sniffer.digging_stop": "嗅獸起", "subtitles.entity.sniffer.drop_seed": "嗅獸遺種", "subtitles.entity.sniffer.eat": "嗅獸食", "subtitles.entity.sniffer.egg_crack": "嗅獸卵裂", "subtitles.entity.sniffer.egg_hatch": "嗅獸卵孵", "subtitles.entity.sniffer.happy": "嗅獸悅", "subtitles.entity.sniffer.hurt": "嗅獸傷", "subtitles.entity.sniffer.idle": "嗅獸鳴", "subtitles.entity.sniffer.scenting": "嗅獸嗅", "subtitles.entity.sniffer.searching": "嗅獸覓", "subtitles.entity.sniffer.sniffing": "嗅獸探", "subtitles.entity.sniffer.step": "嗅獸步", "subtitles.entity.snow_golem.death": "雪傀儡斃", "subtitles.entity.snow_golem.hurt": "雪傀儡傷", "subtitles.entity.snowball.throw": "擲雪團", "subtitles.entity.spider.ambient": "蛛嘶鳴", "subtitles.entity.spider.death": "蛛斃", "subtitles.entity.spider.hurt": "蛛傷", "subtitles.entity.squid.ambient": "鰂游", "subtitles.entity.squid.death": "鰂斃", "subtitles.entity.squid.hurt": "鰂傷", "subtitles.entity.squid.squirt": "鰂濺墨", "subtitles.entity.stray.ambient": "流髑之格格聲", "subtitles.entity.stray.death": "流髑斃", "subtitles.entity.stray.hurt": "流髑傷", "subtitles.entity.strider.death": "熾足獸斃", "subtitles.entity.strider.eat": "熾足獸食", "subtitles.entity.strider.happy": "熾足獸顫", "subtitles.entity.strider.hurt": "熾足獸傷", "subtitles.entity.strider.idle": "熾足獸鳴", "subtitles.entity.strider.retreat": "熾足獸卻", "subtitles.entity.tadpole.death": "蝌蚪斃", "subtitles.entity.tadpole.flop": "蝌蚪躍", "subtitles.entity.tadpole.grow_up": "蝌蚪長", "subtitles.entity.tadpole.hurt": "蝌蚪傷", "subtitles.entity.tnt.primed": "灹藥呲呲聲", "subtitles.entity.tropical_fish.death": "賞魚斃", "subtitles.entity.tropical_fish.flop": "賞魚躍", "subtitles.entity.tropical_fish.hurt": "賞魚傷", "subtitles.entity.turtle.ambient_land": "海龜之嘰嘰聲", "subtitles.entity.turtle.death": "海龜斃", "subtitles.entity.turtle.death_baby": "幼海龜斃", "subtitles.entity.turtle.egg_break": "龜卵破", "subtitles.entity.turtle.egg_crack": "龜卵裂", "subtitles.entity.turtle.egg_hatch": "龜卵孵", "subtitles.entity.turtle.hurt": "海龜傷", "subtitles.entity.turtle.hurt_baby": "幼海龜傷", "subtitles.entity.turtle.lay_egg": "海龜產卵", "subtitles.entity.turtle.shamble": "海龜匍匐", "subtitles.entity.turtle.shamble_baby": "幼海龜匍匐", "subtitles.entity.turtle.swim": "海龜游", "subtitles.entity.vex.ambient": "惱鬼慍", "subtitles.entity.vex.charge": "惱鬼嚎", "subtitles.entity.vex.death": "惱鬼斃", "subtitles.entity.vex.hurt": "惱鬼傷", "subtitles.entity.villager.ambient": "鄉民低語", "subtitles.entity.villager.celebrate": "鄉民歡呼", "subtitles.entity.villager.death": "鄉民斃", "subtitles.entity.villager.hurt": "鄉民傷", "subtitles.entity.villager.no": "鄉民拒", "subtitles.entity.villager.trade": "鄉民賈", "subtitles.entity.villager.work_armorer": "函師作", "subtitles.entity.villager.work_butcher": "屠戶作", "subtitles.entity.villager.work_cartographer": "堪輿師作", "subtitles.entity.villager.work_cleric": "僧作", "subtitles.entity.villager.work_farmer": "農作", "subtitles.entity.villager.work_fisherman": "漁夫作", "subtitles.entity.villager.work_fletcher": "箭匠作", "subtitles.entity.villager.work_leatherworker": "韋人作", "subtitles.entity.villager.work_librarian": "學士作", "subtitles.entity.villager.work_mason": "石匠作", "subtitles.entity.villager.work_shepherd": "羊倌作", "subtitles.entity.villager.work_toolsmith": "工匠作", "subtitles.entity.villager.work_weaponsmith": "戎匠作", "subtitles.entity.villager.yes": "鄉民許", "subtitles.entity.vindicator.ambient": "斫仇者低語", "subtitles.entity.vindicator.celebrate": "斫仇者歡呼", "subtitles.entity.vindicator.death": "斫仇者斃", "subtitles.entity.vindicator.hurt": "斫仇者傷", "subtitles.entity.wandering_trader.ambient": "行商低語", "subtitles.entity.wandering_trader.death": "行商斃", "subtitles.entity.wandering_trader.disappeared": "行商隱", "subtitles.entity.wandering_trader.drink_milk": "行商飲乳", "subtitles.entity.wandering_trader.drink_potion": "行商飲劑", "subtitles.entity.wandering_trader.hurt": "行商傷", "subtitles.entity.wandering_trader.no": "行商拒", "subtitles.entity.wandering_trader.reappeared": "行商現", "subtitles.entity.wandering_trader.trade": "行商賈", "subtitles.entity.wandering_trader.yes": "行商許", "subtitles.entity.warden.agitated": "監守怒號", "subtitles.entity.warden.ambient": "監守鳴", "subtitles.entity.warden.angry": "監守忿恚", "subtitles.entity.warden.attack_impact": "監守擊", "subtitles.entity.warden.death": "監守斃", "subtitles.entity.warden.dig": "監守掘", "subtitles.entity.warden.emerge": "監守現", "subtitles.entity.warden.heartbeat": "監守心動", "subtitles.entity.warden.hurt": "監守傷", "subtitles.entity.warden.listening": "監守察", "subtitles.entity.warden.listening_angry": "監守怒察", "subtitles.entity.warden.nearby_close": "監守近", "subtitles.entity.warden.nearby_closer": "監守就", "subtitles.entity.warden.nearby_closest": "監守臨", "subtitles.entity.warden.roar": "監守吼", "subtitles.entity.warden.sniff": "監守嗅", "subtitles.entity.warden.sonic_boom": "監守發", "subtitles.entity.warden.sonic_charge": "監守張", "subtitles.entity.warden.step": "監守步", "subtitles.entity.warden.tendril_clicks": "監守振鬚", "subtitles.entity.wind_charge.throw": "風彈飛", "subtitles.entity.wind_charge.wind_burst": "風彈爆", "subtitles.entity.witch.ambient": "巫哂", "subtitles.entity.witch.celebrate": "巫歡呼", "subtitles.entity.witch.death": "巫斃", "subtitles.entity.witch.drink": "巫飲劑", "subtitles.entity.witch.hurt": "巫傷", "subtitles.entity.witch.throw": "巫擲", "subtitles.entity.wither.ambient": "凋靈慍", "subtitles.entity.wither.death": "凋靈斃", "subtitles.entity.wither.hurt": "凋靈傷", "subtitles.entity.wither.shoot": "凋靈攻", "subtitles.entity.wither.spawn": "凋靈生", "subtitles.entity.wither_skeleton.ambient": "凋靈骷髏之格格聲", "subtitles.entity.wither_skeleton.death": "凋靈骷髏斃", "subtitles.entity.wither_skeleton.hurt": "凋靈骷髏傷", "subtitles.entity.wolf.ambient": "狼息", "subtitles.entity.wolf.bark": "狼吠", "subtitles.entity.wolf.death": "狼斃", "subtitles.entity.wolf.growl": "狼嚎", "subtitles.entity.wolf.hurt": "狼傷", "subtitles.entity.wolf.pant": "狼息", "subtitles.entity.wolf.shake": "狼震軀", "subtitles.entity.wolf.whine": "狼嗚咽", "subtitles.entity.zoglin.ambient": "屍化獷豕咆哮", "subtitles.entity.zoglin.angry": "屍化獷豕怒吼", "subtitles.entity.zoglin.attack": "屍化獷豕攻", "subtitles.entity.zoglin.death": "屍化獷豕斃", "subtitles.entity.zoglin.hurt": "屍化獷豕傷", "subtitles.entity.zoglin.step": "屍化獷豕步", "subtitles.entity.zombie.ambient": "殭屍呻鳴", "subtitles.entity.zombie.attack_wooden_door": "門動", "subtitles.entity.zombie.break_wooden_door": "門毀", "subtitles.entity.zombie.converted_to_drowned": "殭屍終溺", "subtitles.entity.zombie.death": "殭屍斃", "subtitles.entity.zombie.destroy_egg": "龜卵見蹴", "subtitles.entity.zombie.hurt": "殭屍傷", "subtitles.entity.zombie.infect": "殭屍瘟", "subtitles.entity.zombie_horse.ambient": "屍馬鳴", "subtitles.entity.zombie_horse.death": "屍馬斃", "subtitles.entity.zombie_horse.hurt": "屍馬傷", "subtitles.entity.zombie_villager.ambient": "屍化鄉民呻鳴", "subtitles.entity.zombie_villager.converted": "屍化鄉民嚎", "subtitles.entity.zombie_villager.cure": "屍化鄉民顫", "subtitles.entity.zombie_villager.death": "屍化鄉民斃", "subtitles.entity.zombie_villager.hurt": "屍化鄉民傷", "subtitles.entity.zombified_piglin.ambient": "屍化豕靈鳴", "subtitles.entity.zombified_piglin.angry": "屍化豕靈怒鳴", "subtitles.entity.zombified_piglin.death": "屍化豕靈斃", "subtitles.entity.zombified_piglin.hurt": "屍化豕靈傷", "subtitles.event.mob_effect.bad_omen": "祲現", "subtitles.event.mob_effect.raid_omen": "襲迫", "subtitles.event.mob_effect.trial_omen": "厄煉迫", "subtitles.event.raid.horn": "厄角鳴", "subtitles.item.armor.equip": "著甲", "subtitles.item.armor.equip_chain": "著環鎻甲", "subtitles.item.armor.equip_diamond": "著金剛甲", "subtitles.item.armor.equip_elytra": "著翼", "subtitles.item.armor.equip_gold": "著金甲", "subtitles.item.armor.equip_iron": "著鐵甲", "subtitles.item.armor.equip_leather": "著革甲", "subtitles.item.armor.equip_netherite": "著玄鈺甲", "subtitles.item.armor.equip_turtle": "著龜殼", "subtitles.item.armor.equip_wolf": "狼甲繫", "subtitles.item.armor.unequip_wolf": "狼甲解", "subtitles.item.axe.scrape": "斧削", "subtitles.item.axe.strip": "斧扡", "subtitles.item.axe.wax_off": "除蠟", "subtitles.item.bone_meal.use": "施骨塵之聲", "subtitles.item.book.page_turn": "紙之挲挲聲", "subtitles.item.book.put": "置書", "subtitles.item.bottle.empty": "甁空", "subtitles.item.bottle.fill": "盈甁", "subtitles.item.brush.brushing.generic": "方拭", "subtitles.item.brush.brushing.gravel": "方拭礫", "subtitles.item.brush.brushing.gravel.complete": "拭礫既畢", "subtitles.item.brush.brushing.sand": "方拭沙", "subtitles.item.brush.brushing.sand.complete": "拭沙既畢", "subtitles.item.bucket.empty": "桶空", "subtitles.item.bucket.fill": "桶盈", "subtitles.item.bucket.fill_axolotl": "螈儲", "subtitles.item.bucket.fill_fish": "獲魚", "subtitles.item.bucket.fill_tadpole": "獲蝌蚪", "subtitles.item.bundle.drop_contents": "皮囊空", "subtitles.item.bundle.insert": "容物於囊", "subtitles.item.bundle.insert_fail": "皮囊既盈", "subtitles.item.bundle.remove_one": "取物於囊", "subtitles.item.chorus_fruit.teleport": "戲者移形", "subtitles.item.crop.plant": "栽禾", "subtitles.item.crossbow.charge": "彀弩", "subtitles.item.crossbow.hit": "矢中", "subtitles.item.crossbow.load": "填弩", "subtitles.item.crossbow.shoot": "弩發", "subtitles.item.dye.use": "染", "subtitles.item.elytra.flying": "颯颯聲", "subtitles.item.firecharge.use": "火圓簌然", "subtitles.item.flintandsteel.use": "以鐮擊石", "subtitles.item.glow_ink_sac.use": "塗爍墨", "subtitles.item.goat_horn.play": "山羊角奏", "subtitles.item.hoe.till": "鋤犁", "subtitles.item.honey_bottle.drink": "啖", "subtitles.item.honeycomb.wax_on": "蠟塗", "subtitles.item.horse_armor.unequip": "馬甲解", "subtitles.item.ink_sac.use": "塗墨", "subtitles.item.lead.break": "韁裁", "subtitles.item.lead.tied": "韁繫", "subtitles.item.lead.untied": "韁解", "subtitles.item.llama_carpet.unequip": "氍毹解", "subtitles.item.lodestone_compass.lock": "合司南於礠石", "subtitles.item.mace.smash_air": "椎擊", "subtitles.item.mace.smash_ground": "椎擊", "subtitles.item.nether_wart.plant": "栽禾", "subtitles.item.ominous_bottle.dispose": "甁破", "subtitles.item.saddle.unequip": "鞍韉解", "subtitles.item.shears.shear": "鉸合", "subtitles.item.shears.snip": "鉸裁", "subtitles.item.shield.block": "盾禦", "subtitles.item.shovel.flatten": "鍁夷之", "subtitles.item.spyglass.stop_using": "望遠鏡縮", "subtitles.item.spyglass.use": "望遠鏡展", "subtitles.item.totem.use": "保命符發", "subtitles.item.trident.hit": "戟刺", "subtitles.item.trident.hit_ground": "戟震", "subtitles.item.trident.return": "戟返", "subtitles.item.trident.riptide": "戟進", "subtitles.item.trident.throw": "戟之鏗鏘聲", "subtitles.item.trident.thunder": "戟雷鳴", "subtitles.item.wolf_armor.break": "狼甲壞", "subtitles.item.wolf_armor.crack": "狼甲裂", "subtitles.item.wolf_armor.damage": "狼甲損", "subtitles.item.wolf_armor.repair": "狼甲繕", "subtitles.particle.soul_escape": "靈逸", "subtitles.ui.cartography_table.take_result": "繪圖", "subtitles.ui.hud.bubble_pop": "氣數耗", "subtitles.ui.loom.take_result": "機杼爲用", "subtitles.ui.stonecutter.take_result": "鑿石器爲用", "subtitles.weather.rain": "雨音淅淅", "symlink_warning.message": "以符鏈之夾載生界，殆不可測，尤甚於鏈不明時。訪%s以聞其詳。", "symlink_warning.message.pack": "以符鏈之夾載囊，殆不可測，尤甚於鏈不明時。訪%s以聞其詳。", "symlink_warning.message.world": "以符鏈之夾載生界，殆不可測，尤甚於鏈不明時。訪%s以聞其詳。", "symlink_warning.more_info": "詳述", "symlink_warning.title": "生界資囊含符號連結", "symlink_warning.title.pack": "所加之囊含符鏈", "symlink_warning.title.world": "生界資囊含符鏈", "team.collision.always": "固撞", "team.collision.never": "禁撞", "team.collision.pushOtherTeams": "觸他伍", "team.collision.pushOwnTeam": "觸己伍", "team.notFound": "未知之伍「%s」", "team.visibility.always": "固現", "team.visibility.hideForOtherTeams": "隱他伍", "team.visibility.hideForOwnTeam": "隱己伍", "team.visibility.never": "不現", "telemetry.event.advancement_made.description": "知功之所以成，然後得精進於戲程。", "telemetry.event.advancement_made.title": "功之所成", "telemetry.event.game_load_times.description": "此事測起迄之久暫，以察其精勵。", "telemetry.event.game_load_times.title": "載戲之時", "telemetry.event.optional": "%s（可選）", "telemetry.event.optional.disabled": "%s（可選） - 禁", "telemetry.event.performance_metrics.description": "知礦藝之總體效能概況可助吾等為各類裝置與械綱調而優戲。\n所集亦含戲版，以助吾等比礦藝新版之效能概況。", "telemetry.event.performance_metrics.title": "效能指標", "telemetry.event.required": "%s（必）", "telemetry.event.world_load_times.description": "於吾等，入生界之時長短，及何以日久而變，爲須知與必要也。若增新藝能，抑或大更其術時，何以其變，爲吾等之須知者也。", "telemetry.event.world_load_times.title": "載生界之時", "telemetry.event.world_loaded.description": "吾等知戲者之礦藝（如嬉遊之法、伺服器及客端之版、戲版等之錄）可依戲者所註補益新戲。\n生界載與生界解二事一同測算嬉遊會話持續之時。", "telemetry.event.world_loaded.title": "生界載", "telemetry.event.world_unloaded.description": "此事將同生界載之事計生界會話之時。\n時（以秒和刻計）將於生界會話作結時（歸卷首或離伺服器）測。", "telemetry.event.world_unloaded.title": "生界解", "telemetry.property.advancement_game_time.title": "戲時（刻）", "telemetry.property.advancement_id.title": "進程符", "telemetry.property.client_id.title": "客端碼", "telemetry.property.client_modded.title": "客端既改否", "telemetry.property.dedicated_memory_kb.title": "既用之內存（千爻）", "telemetry.property.event_timestamp_utc.title": "事之時記（麒麟尉時）", "telemetry.property.frame_rate_samples.title": "幀率（FPS）", "telemetry.property.game_mode.title": "嬉遊之法", "telemetry.property.game_version.title": "戲版", "telemetry.property.launcher_name.title": "啟者之名", "telemetry.property.load_time_bootstrap_ms.title": "誘引之時（毫秒）", "telemetry.property.load_time_loading_overlay_ms.title": "見載幕之時（毫秒）", "telemetry.property.load_time_pre_window_ms.title": "幕未張之時（毫秒）", "telemetry.property.load_time_total_time_ms.title": "總載時（毫秒）", "telemetry.property.minecraft_session_id.title": "礦藝會話之碼", "telemetry.property.new_world.title": "新生界否", "telemetry.property.number_of_samples.title": "樣數", "telemetry.property.operating_system.title": "械綱", "telemetry.property.opt_in.title": "選入", "telemetry.property.platform.title": "端", "telemetry.property.realms_map_content.title": "領域圖實（微戲名）", "telemetry.property.render_distance.title": "繪距", "telemetry.property.render_time_samples.title": "繪時之樣", "telemetry.property.seconds_since_load.title": "自載之時（秒）", "telemetry.property.server_modded.title": "伺服端既改否", "telemetry.property.server_type.title": "伺服器之類", "telemetry.property.ticks_since_load.title": "自載之時（刻）", "telemetry.property.used_memory_samples.title": "既用之憶", "telemetry.property.user_id.title": "戶碼", "telemetry.property.world_load_time_ms.title": "載生界之時（毫秒）", "telemetry.property.world_session_id.title": "生界會話之碼", "telemetry_info.button.give_feedback": "惠賜卓見", "telemetry_info.button.privacy_statement": "私隱之宣", "telemetry_info.button.show_data": "啟吾錄", "telemetry_info.opt_in.description": "吾許傳可選之遙測錄", "telemetry_info.property_title": "所含之錄", "telemetry_info.screen.description": "集下之錄以助吾等補益與戲者之實況，更補益礦藝。\n君可傳外言回助吾等繼修礦藝。", "telemetry_info.screen.title": "遙測錄之集", "test.error.block_property_mismatch": "期%s性爲%s，而得%s", "test.error.block_property_missing": "塊性不備，%s之性應爲%s。", "test.error.entity_property": "實體%s測未成：%s", "test.error.entity_property_details": "實體%s試未成：%s，應爲：%s，實爲：%s", "test.error.expected_block": "應爲塊%s，得%s", "test.error.expected_block_tag": "塊應爲#%s之一，實爲%s", "test.error.expected_container_contents": "器應含：%s", "test.error.expected_container_contents_single": "器應含單：%s", "test.error.expected_empty_container": "器應空", "test.error.expected_entity": "應爲%s", "test.error.expected_entity_around": "%s應於%s, %s, %s之圍", "test.error.expected_entity_count": "實體之%s類者應有%s，實為%s", "test.error.expected_entity_data": "實體錄應爲%s，而得%s", "test.error.expected_entity_data_predicate": "%s之實體錄不符", "test.error.expected_entity_effect": "%s應有效%s %s", "test.error.expected_entity_having": "實體行囊應含%s", "test.error.expected_entity_holding": "實體應攜%s", "test.error.expected_entity_in_test": "%s應存於測中", "test.error.expected_entity_not_touching": "%s不應觸%s, %s, %s（相對%s, %s, %s處）", "test.error.expected_entity_touching": "%s應觸%s, %s, %s（相對%s, %s, %s處）", "test.error.expected_item": "應為%s類之物", "test.error.expected_items_count": "%s類之物應爲%s，實為%s", "test.error.fail": "足以敗", "test.error.invalid_block_type": "尋未料之塊類：%s", "test.error.missing_block_entity": "塊實體闕", "test.error.position": "%1$s於刻%8$s居%2$s, %3$s, %4$s（相對%5$s, %6$s, %7$s處）", "test.error.sequence.condition_already_triggered": "限既發於%s刻", "test.error.sequence.condition_not_triggered": "限未發", "test.error.sequence.invalid_tick": "成於無效刻：應爲%s", "test.error.sequence.not_completed": "測逾時於序成前", "test.error.set_biome": "設測之生態域未成", "test.error.spawn_failure": "創實體%s未成", "test.error.state_not_equal": "塊態謬誤。應爲%s，而得%s", "test.error.structure.failure": "措測構於%s未成", "test.error.tick": "%s於刻%s", "test.error.ticking_without_structure": "先試而後置構", "test.error.timeout.no_result": "未有成敗於%s刻之內", "test.error.timeout.no_sequences_finished": "未有畢列於%s刻之內", "test.error.too_many_entities": "%2$s, %3$s, %4$s之圍應存%1$s僅一，實%5$s", "test.error.unexpected_block": "塊不應爲%s", "test.error.unexpected_entity": "%s不應存", "test.error.unexpected_item": "物不應爲%s類", "test.error.unknown": "未明之內謬：%s", "test.error.value_not_equal": "%s應爲%s，實爲%s", "test.error.wrong_block_entity": "誤塊實體類：%s", "test_block.error.missing": "測構闕%s塊", "test_block.error.too_many": "%s塊量過載", "test_block.invalid_timeout": "無效時限（%s），刻須正數也", "test_block.message": "訊：", "test_block.mode.accept": "受", "test_block.mode.fail": "敗", "test_block.mode.log": "誌", "test_block.mode.start": "啟", "test_block.mode_info.accept": "受 — 受（片）測之成", "test_block.mode_info.fail": "敗 — 敗其試", "test_block.mode_info.log": "誌 — 佈訊於誌", "test_block.mode_info.start": "啟 — 試之肇", "test_instance.action.reset": "復置而載", "test_instance.action.run": "載且行", "test_instance.action.save": "存構", "test_instance.description.batch": "次：%s", "test_instance.description.failed": "敗：%s", "test_instance.description.function": "函數：%s", "test_instance.description.invalid_id": "無效試之ID", "test_instance.description.no_test": "無是試", "test_instance.description.structure": "構：%s", "test_instance.description.type": "類：%s", "test_instance.type.block_based": "試之於塊者", "test_instance.type.function": "內成函數之試", "test_instance_block.entities": "實體：", "test_instance_block.error.no_test": "因未定之試存，行試例於%s, %s, %s不可", "test_instance_block.error.no_test_structure": "因無試構，未可行試例於%s, %s, %s", "test_instance_block.error.unable_to_save": "存試例於%s, %s, %s之試構模未可", "test_instance_block.invalid": "[無效]", "test_instance_block.reset_success": "既復置試：%s", "test_instance_block.rotation": "旋：", "test_instance_block.size": "試構之積", "test_instance_block.starting": "方始試%s", "test_instance_block.test_id": "試例之ID", "title.32bit.deprecation": "探得三十二爻械綱：以日後需六十四爻械綱以遊故，恐將遺阻！", "title.32bit.deprecation.realms": "不日，礦藝將需六十四爻械綱，是將阻子嬉遊並領域之用於是械。一切領域僦約，請自除卻。", "title.32bit.deprecation.realms.check": "勿復現此景也", "title.32bit.deprecation.realms.header": "探得三十二爻械綱", "title.credits": "魔贊持權，不得轉洩！", "title.multiplayer.disabled": "眾戲未允。請察汝之微軟帳戶置設。", "title.multiplayer.disabled.banned.name": "汝必更名，乃可眾戲", "title.multiplayer.disabled.banned.permanent": "戶簿已恆爲所羈，弗能入眾戲", "title.multiplayer.disabled.banned.temporary": "戶簿已暫爲所羈，弗能入眾戲", "title.multiplayer.lan": "眾戲（區網）", "title.multiplayer.other": "眾戲（第三方伺服器）", "title.multiplayer.realms": "眾戲（領域）", "title.singleplayer": "獨戲", "translation.test.args": "%s %s", "translation.test.complex": "起，%2$s後%s與%1$s卒%s與%1$s！", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "幸會 %", "translation.test.invalid2": "幸會 %s", "translation.test.none": "問天地好在", "translation.test.world": "生界", "trim_material.minecraft.amethyst": "紫水玉質", "trim_material.minecraft.copper": "銅質", "trim_material.minecraft.diamond": "金剛石質", "trim_material.minecraft.emerald": "祖母綠質", "trim_material.minecraft.gold": "金質", "trim_material.minecraft.iron": "鐵質", "trim_material.minecraft.lapis": "群青質", "trim_material.minecraft.netherite": "玄鈺質", "trim_material.minecraft.quartz": "石英質", "trim_material.minecraft.redstone": "赤石質", "trim_material.minecraft.resin": "樹香質", "trim_pattern.minecraft.bolt": "榫卯甲紋", "trim_pattern.minecraft.coast": "邊海甲紋", "trim_pattern.minecraft.dune": "沙墩甲紋", "trim_pattern.minecraft.eye": "瞳眸甲紋", "trim_pattern.minecraft.flow": "氣洄甲紋", "trim_pattern.minecraft.host": "佃東甲紋", "trim_pattern.minecraft.raiser": "牧民甲紋", "trim_pattern.minecraft.rib": "脅肋甲紋", "trim_pattern.minecraft.sentry": "斥候甲紋", "trim_pattern.minecraft.shaper": "巧匠甲紋", "trim_pattern.minecraft.silence": "寂靜甲紋", "trim_pattern.minecraft.snout": "豕鼻甲紋", "trim_pattern.minecraft.spire": "旋塔甲紋", "trim_pattern.minecraft.tide": "潮汐甲紋", "trim_pattern.minecraft.vex": "惱鬼甲紋", "trim_pattern.minecraft.ward": "監守甲紋", "trim_pattern.minecraft.wayfinder": "嚮導甲紋", "trim_pattern.minecraft.wild": "林野甲紋", "tutorial.bundleInsert.description": "擊右鍵以容物", "tutorial.bundleInsert.title": "以皮囊容物", "tutorial.craft_planks.description": "方集或有助", "tutorial.craft_planks.title": "製材", "tutorial.find_tree.description": "擊之以得木", "tutorial.find_tree.title": "尋一樹", "tutorial.look.description": "以滑鼠轉嚮", "tutorial.look.title": "環顧", "tutorial.move.description": "躍以%s", "tutorial.move.title": "移以%s、%s、%s、%s", "tutorial.open_inventory.description": "撳%s", "tutorial.open_inventory.title": "啟行囊", "tutorial.punch_tree.description": "押%s", "tutorial.punch_tree.title": "伐樹", "tutorial.socialInteractions.description": "撳%s以啟", "tutorial.socialInteractions.title": "交際", "upgrade.minecraft.netherite_upgrade": "玄鈺之昇"}