{"accessibility.onboarding.accessibility.button": "<PERSON><PERSON><PERSON>...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON><PERSON> Enter meter vehta i Quentaro", "accessibility.onboarding.screen.title": "<PERSON><PERSON>lië mi Minecraft!\n\nMa merilyë vehta i Quentaro hya cenë i cilmi as<PERSON>?", "addServer.add": "Carina", "addServer.enterIp": "Tengessë servero", "addServer.enterName": "Essë servero", "addServer.resourcePack": "Colcar mairion servero", "addServer.resourcePack.disabled": "Lacaraitë", "addServer.resourcePack.enabled": "Caraitë", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Vista istalë pá server", "advMode.command": "<PERSON><PERSON><PERSON> consol<PERSON>", "advMode.mode": "<PERSON>é", "advMode.mode.auto": "Encarila", "advMode.mode.autoexec.bat": "Caraitë illumë", "advMode.mode.conditional": "Tarmëa", "advMode.mode.redstone": "Hórëa", "advMode.mode.redstoneTriggered": "<PERSON><PERSON><PERSON> s<PERSON>", "advMode.mode.sequence": "Limil", "advMode.mode.unconditional": "Altarmëa", "advMode.notAllowed": "<PERSON><PERSON>a lyen ná cáno <PERSON><PERSON>", "advMode.notEnabled": "<PERSON><PERSON> a<PERSON>n lár caraiti sina serveress<PERSON>", "advMode.previousOutput": "<PERSON>öa <PERSON>", "advMode.setCommand": "<PERSON> a<PERSON> i ronwan", "advMode.setCommand.success": "Axan martaina: %s", "advMode.trackOutput": "Cenda eteleltasta", "advMode.triggering": "Yestië", "advMode.type": "Nostalë", "advancement.advancementNotFound": "Lasinwa empatyellë: %s", "advancements.adventure.adventuring_time.description": "Tuvë ilya yondë", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.arbalistic.description": "Nahta vëor lemp<PERSON> quihtanen lan<PERSON>o", "advancements.adventure.arbalistic.title": "Lan<PERSON><PERSON><PERSON>", "advancements.adventure.avoid_vibration.description": "Hlicë ara esculcova tuvima hya holtur, hya ortirmo itan lá hlaris lye", "advancements.adventure.avoid_vibration.title": "Hlicë ú hlóno", "advancements.adventure.blowback.description": "Nahta hwestar nanhatinwa hwestanen i hwestar hantë", "advancements.adventure.blowback.title": "Aunista", "advancements.adventure.brush_armadillo.description": "Netë satsi nyelcovë hwecilanen", "advancements.adventure.brush_armadillo.title": "Satsë ilcalassë", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON> i endanna meht<PERSON>o tarila palla ronwar 30", "advancements.adventure.bullseye.title": "<PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Carë fintanwa tambë cemnassevë nihtaron canta", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON> ara intamma yá tamis intamma", "advancements.adventure.crafters_crafting_crafters.title": "Intammar tamilë intammar", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON> ing<PERSON> (pel<PERSON>lo car<PERSON>al<PERSON>) talmanna ambaro ar vorë", "advancements.adventure.fall_from_world_height.title": "<PERSON><PERSON><PERSON> ar varassi", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "<PERSON><PERSON> ar varya opelë", "advancements.adventure.hero_of_the_village.title": "<PERSON>o <PERSON>el<PERSON>", "advancements.adventure.honey_block_slide.description": "Lanta ronwanna ne<PERSON>o meter moicata lantalya", "advancements.adventure.honey_block_slide.title": "H<PERSON><PERSON>, mátima", "advancements.adventure.kill_a_mob.description": "Nahta rauco", "advancements.adventure.kill_a_mob.title": "Faramo úvanoron", "advancements.adventure.kill_all_mobs.description": "Nahta min úvano ilyë raucoron", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Nahta vëo ara hausta esculco", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Palië", "advancements.adventure.lighten_up.description": "Nyasë calma urusto peleccunen itan nás ancalima", "advancements.adventure.lighten_up.title": "Utúlië 'n aurë", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Tapë íta ar varya opelemo, ú aparuivëo", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Itandacil", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> sambë riciéron", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> la<PERSON>", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON> hatta sena", "advancements.adventure.overoverkill.description": "<PERSON><PERSON> homi 50 min nambenen rundanen", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Cuita salquenori lindalëo lámanen colca lindal<PERSON>ollo", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON> l<PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Cavë i ítacelmë cantaina palustallo comparmanen", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON><PERSON> parmaron", "advancements.adventure.revaulting.description": "<PERSON><PERSON>a lumna sampo lumna latilanen riciéo", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Veryandë, lelyandë ar mahtalë", "advancements.adventure.root.title": "Veryandë", "advancements.adventure.salvage_sherd.description": "Brush a Suspicious block to obtain a Pottery Sherd", "advancements.adventure.salvage_sherd.title": "Respecting the Remnants", "advancements.adventure.shoot_arrow.description": "Á quihta ma pilindanen", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON>na!", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON>ë caimassë meter vista nómelya enontiëo", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON> olori", "advancements.adventure.sniper_duel.description": "Nahta axoquen tarila palla ronwar 50", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON><PERSON> quingaron", "advancements.adventure.spyglass_at_dragon.description": "Tirë i hlócë Endo tirmanen", "advancements.adventure.spyglass_at_dragon.title": "Ma tana lócë?", "advancements.adventure.spyglass_at_ghast.description": "Tirë Ñasto tirmanen", "advancements.adventure.spyglass_at_ghast.title": "Ma tana lumbo?", "advancements.adventure.spyglass_at_parrot.description": "Tirë quetaiwë tirmanen", "advancements.adventure.spyglass_at_parrot.title": "Ma tana aiwë?", "advancements.adventure.summon_iron_golem.description": "Yalë angaturco meter varya opelë", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Hatë nelcarca aimanna.\nSartë: <PERSON>hat<PERSON> er carma alasaila cé nauva.", "advancements.adventure.throw_trident.title": "Sí man i carma nin na<PERSON>va?", "advancements.adventure.totem_of_undying.description": "Yuhta aian lafírimiëo meter cupta i fírië", "advancements.adventure.totem_of_undying.title": "Enontië", "advancements.adventure.trade.description": "<PERSON><PERSON> as op<PERSON><PERSON>", "advancements.adventure.trade.title": "Yé quapta!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON> as opelemo tára p<PERSON> car<PERSON>alëo", "advancements.adventure.trade_at_world_height.title": "Elemmacar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Napanë sinë cantier macalëo ellumë annún: nelma<PERSON>, neng<PERSON><PERSON>, rim<PERSON>, orti<PERSON>ova, quilda, tarastarwa, sólava, telcontarwa", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Maca ve Telpinquar", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON>ë netinwa varma paluhtassë macalëo", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Nahta lórerauco atta térala pilindanen", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON> atta, pilin min", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON> sampo la<PERSON>anen rici<PERSON>o", "advancements.adventure.under_lock_and_key.title": "Aquapahtanwa", "advancements.adventure.use_lodestone.description": "Yuhta mententar rantondossë", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.very_very_frightening.description": "Namba opelemo ítanen", "advancements.adventure.very_very_frightening.title": "Soryaitë sossë", "advancements.adventure.voluntary_exile.description": "Nahta i cáno nalanto.\nAvanevië opeleron mára cé nauva...", "advancements.adventure.voluntary_exile.title": "Etya-tyal<PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Pata lossëo mulossë... ar vá suvë tassë", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Cólima ve lopo", "advancements.adventure.who_needs_rockets.description": "<PERSON><PERSON> hwesta meter amavilë imlë ronwar 8", "advancements.adventure.who_needs_rockets.title": "<PERSON>á ma<PERSON>vë sorni!", "advancements.adventure.whos_the_pillager_now.description": "Give a Pillager a taste of their own medicine", "advancements.adventure.whos_the_pillager_now.title": "Who's the Pillager Now?", "advancements.empty": "Munta sís tensi...", "advancements.end.dragon_breath.description": "Comya f<PERSON>a hlóceva cilino olpessë", "advancements.end.dragon_breath.title": "You Need a Mint", "advancements.end.dragon_egg.description": "Hold the Dragon Egg", "advancements.end.dragon_egg.title": "The Next Generation", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.title": "Menel i pelma ná", "advancements.end.enter_end_gateway.description": "Escape the island", "advancements.end.enter_end_gateway.title": "Remote Getaway", "advancements.end.find_end_city.description": "Go on in, what could happen?", "advancements.end.find_end_city.title": "The City at the End of the Game", "advancements.end.kill_dragon.description": "Almenna", "advancements.end.kill_dragon.title": "Free the End", "advancements.end.levitate.description": "Amavilë ter ronwar 50 hyulcero nalantalellon", "advancements.end.levitate.title": "Great View From Up Here", "advancements.end.respawn_dragon.description": "Etyalë i hlócë Endo", "advancements.end.respawn_dragon.title": "I metta... enta...", "advancements.end.root.description": "Hya i yesta?", "advancements.end.root.title": "I End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON><PERSON> alyar lerya masta lincolcanna", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Nostalindalë", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON><PERSON> alyar tulë nati lyenna", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON>ë <PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "Atë axolotil calpanen", "advancements.husbandry.axolotl_in_a_bucket.title": "The Cutest Predator", "advancements.husbandry.balanced_diet.description": "Eat everything that is edible, even if it's not good for you", "advancements.husbandry.balanced_diet.title": "A Balanced Diet", "advancements.husbandry.breed_all_animals.description": "Lita ilyë celvar!", "advancements.husbandry.breed_all_animals.title": "<PERSON>ta yullumë", "advancements.husbandry.breed_an_animal.description": "Lita celva atta", "advancements.husbandry.breed_an_animal.title": "I quetaiwi ar i quildari", "advancements.husbandry.complete_catalogue.description": "Tame all Cat variants!", "advancements.husbandry.complete_catalogue.title": "A Complete Catalogue", "advancements.husbandry.feed_snifflet.description": "Feed a Snifflet", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": "Atë lingwë", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "Samë ilya quácalma au<PERSON>", "advancements.husbandry.froglights.title": "I túrin i calmaron!", "advancements.husbandry.kill_axolotl_target.description": "Team up with an Axolotl and win a fight", "advancements.husbandry.kill_axolotl_target.title": "The Healing Power of Friendship!", "advancements.husbandry.leash_all_frog_variants.description": "Atë ilya nostalë quácëo tuncanen", "advancements.husbandry.leash_all_frog_variants.title": "Taureliquácëa", "advancements.husbandry.make_a_sign_glow.description": "Make the text of any kind of sign glow", "advancements.husbandry.make_a_sign_glow.title": "Glow and Behold!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON> minulda neserito meter aryata hyarin<PERSON>, ta sana pá laulelya", "advancements.husbandry.netherite_hoe.title": "Cemendil Vorondo voronwë", "advancements.husbandry.obtain_sniffer_egg.description": "Netë ohtë nustarwa", "advancements.husbandry.obtain_sniffer_egg.title": "Yé nus!", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": "Ala aitë erdë nustaro", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.plant_seed.description": "Ala erdë ar tirë o<PERSON>ya", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Auco<PERSON><PERSON> varma r<PERSON> au<PERSON>", "advancements.husbandry.remove_wolf_armor.title": "Polin cirë", "advancements.husbandry.repair_wolf_armor.description": "A<PERSON><PERSON>a harna varma rácava nyelcovë satsínen", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> envinyanta", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON><PERSON> l<PERSON> as na<PERSON>", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Man cenuva fána naico?", "advancements.husbandry.root.description": "Ambar quanta nilmoron ar matto", "advancements.husbandry.root.title": "Cemendur", "advancements.husbandry.safely_harvest_honey.description": "<PERSON>hta ruinë meter comya nehtë nieressello cilino olpenen, ar avanevë rús<PERSON> i nieron", "advancements.husbandry.safely_harvest_honey.title": "Bee Our Guest", "advancements.husbandry.silk_touch_nest.description": "<PERSON><PERSON><PERSON> nier neld<PERSON> haust<PERSON>, yuh<PERSON>a moica mahtië", "advancements.husbandry.silk_touch_nest.title": "Lauca súmaryassë", "advancements.husbandry.tactical_fishing.description": "Atë lingwë... ú atso!", "advancements.husbandry.tactical_fishing.title": "Lingwiremië", "advancements.husbandry.tadpole_in_a_bucket.description": "Atë quaccas calpanen", "advancements.husbandry.tadpole_in_a_bucket.title": "Qu<PERSON>, cé quácë", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON> celva", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_off.description": "Honyásë líco urus ronwallo!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Antacë nehtelë urusto ronwassë!", "advancements.husbandry.wax_on.title": "Tupië lí<PERSON>", "advancements.husbandry.whole_pack.description": "Numahta min ráca ilyë nostalion", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Have every effect applied at the same time", "advancements.nether.all_effects.title": "How Did We Get Here?", "advancements.nether.all_potions.description": "Have every potion effect applied at the same time", "advancements.nether.all_potions.title": "A Furious Cocktail", "advancements.nether.brew_potion.description": "<PERSON><PERSON> yulda", "advancements.nether.brew_potion.title": "Local Brewery", "advancements.nether.charge_respawn_anchor.description": "Charge a Respawn Anchor to the maximum", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON><PERSON> i \"nertëa coivië\"", "advancements.nether.create_beacon.description": "Construct and place a Beacon", "advancements.nether.create_beacon.title": "Elenion ancalima", "advancements.nether.create_full_beacon.description": "Bring a Beacon to full power", "advancements.nether.create_full_beacon.title": "Na<PERSON>man<PERSON>", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON> pingi<PERSON> malta<PERSON>", "advancements.nether.distract_piglin.title": "A, laurë", "advancements.nether.explore_nether.description": "Ratë ilyë N<PERSON>rwë yondi", "advancements.nether.explore_nether.title": "Úrë, mal mairë", "advancements.nether.fast_travel.description": "Yuhta Nether meter lelya 7 km Ambanóressë", "advancements.nether.fast_travel.title": "Subspace Bubble", "advancements.nether.find_bastion.description": "<PERSON><PERSON>", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_fortress.description": "Break your way into a Nether Fortress", "advancements.nether.find_fortress.title": "A Terrible Fortress", "advancements.nether.get_wither_skull.description": "Netë coropë Wither-axoqueno", "advancements.nether.get_wither_skull.title": "Spooky Scary Skeleton", "advancements.nether.loot_bastion.description": "Rapë taucolca Yára <PERSON>", "advancements.nether.loot_bastion.title": "Polcar polir carë <PERSON>ta", "advancements.nether.netherite_armor.description": "<PERSON><PERSON> aupenya varma neserito", "advancements.nether.netherite_armor.title": "Á tupë ni lemmainen", "advancements.nether.obtain_ancient_debris.description": "Netë yára lemma", "advancements.nether.obtain_ancient_debris.title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "advancements.nether.obtain_blaze_rod.description": "Mapa i pirin uryamollo", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.description": "Netë nyenya moricalca", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON>?", "advancements.nether.return_to_sender.description": "Nancarë Ñasto narcoronanen", "advancements.nether.return_to_sender.title": "Entulë i menta mentaronna", "advancements.nether.ride_strider.description": "Ride a Strider with a Warped Fungus on a Stick", "advancements.nether.ride_strider.title": "Sina luntë samë telcor", "advancements.nether.ride_strider_in_overworld_lava.description": "Take a Strider for a loooong ride on a lava lake in the Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON> ve m<PERSON>ar", "advancements.nether.root.description": "Bring summer clothes", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON>ë <PERSON>", "advancements.nether.summon_wither.title": "Carë ve Melcor", "advancements.nether.uneasy_alliance.description": "Etelehta <PERSON>, autulya se mandonna Ambanóressë... en nahta se", "advancements.nether.uneasy_alliance.title": "Uneasy Alliance", "advancements.nether.use_lodestone.description": "Yuhta mententar rantondossë", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Weaken and then cure a Zombie Villager", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON> urcoron", "advancements.story.deflect_arrow.description": "Nan<PERSON><PERSON> quihta sandanen", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, hantan", "advancements.story.enchant_item.description": "Luhta nat paluhtassë luhtiëo", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "<PERSON><PERSON>", "advancements.story.enter_the_end.title": "I metta?", "advancements.story.enter_the_nether.description": "Carasta Nether fend<PERSON>, nartaitas ar <PERSON>", "advancements.story.enter_the_nether.title": "<PERSON><PERSON><PERSON> ven menë undu", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON> hen <PERSON>", "advancements.story.follow_ender_eye.title": "Hentaitas", "advancements.story.form_obsidian.description": "Netë ronwa morcalco", "advancements.story.form_obsidian.title": "Veryal<PERSON> \"Ice Bucket\"", "advancements.story.iron_tools.description": "Aryata ondopelec<PERSON>lya", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON>, ma naitë", "advancements.story.lava_bucket.description": "Quanta calpa sirru<PERSON>en", "advancements.story.lava_bucket.title": "Yé urra ma!", "advancements.story.mine_diamond.description": "<PERSON><PERSON>", "advancements.story.mine_diamond.title": "Tinw<PERSON>ri!", "advancements.story.mine_stone.description": "Rosta ondo vinya on<PERSON>", "advancements.story.mine_stone.title": "Ondoranda", "advancements.story.obtain_armor.description": "<PERSON><PERSON><PERSON> angaina var<PERSON>en", "advancements.story.obtain_armor.title": "<PERSON><PERSON>", "advancements.story.root.description": "The heart and story of the game", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Tinw<PERSON>rina varma rehtuva lye", "advancements.story.shiny_gear.title": "Tupa ni tin<PERSON>í<PERSON>ínen", "advancements.story.smelt_iron.description": "Ticuta minulda ango", "advancements.story.smelt_iron.title": "Angamaitë, angatur", "advancements.story.upgrade_tools.description": "Á tanë arya ondopelecco", "advancements.story.upgrade_tools.title": "Aryatarë!", "advancements.toast.challenge": "Challenge Complete!", "advancements.toast.goal": "Menesta anyaina!", "advancements.toast.task": "Empatyellë carina!", "argument.anchor.invalid": "Invalid entity anchor position %s", "argument.angle.incomplete": "Incomplete (expected 1 angle)", "argument.angle.invalid": "Invalid angle", "argument.block.id.invalid": "Lasinwa nostalë ronwo: '%s'", "argument.block.property.duplicate": "Nassë '%s' tulcima rië ronwan %s", "argument.block.property.invalid": "Ronwa %s lá cavë '%s' ve nassë %s", "argument.block.property.novalue": "<PERSON><PERSON> mirma nassen '%s' ronwassë %s", "argument.block.property.unclosed": "Ho<PERSON> i tengwa \"]\" nassen tarmëo i ronwo", "argument.block.property.unknown": "Ronwa '%s' lá samë i nassë '%s'", "argument.block.tag.disallowed": "Tagi lár la<PERSON> s<PERSON>, nanwë ronwar rië", "argument.color.invalid": "<PERSON>in<PERSON> quilë: '%s'", "argument.component.invalid": "Invalid chat component: %s", "argument.criteria.invalid": "Unknown criterion '%s'", "argument.dimension.invalid": "Lasinwa arda: '%s'", "argument.double.big": "Double must not be more than %s, found %s", "argument.double.low": "Double must not be less than %s, found %s", "argument.entity.invalid": "Essë hya i UUID lá mára", "argument.entity.notfound.entity": "No entity was found", "argument.entity.notfound.player": "No player was found", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON><PERSON> as empaty<PERSON>", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON> en<PERSON>", "argument.entity.options.distance.negative": "Distance cannot be negative", "argument.entity.options.dx.description": "Entities between x and x + dx", "argument.entity.options.dy.description": "Entities between y and y + dy", "argument.entity.options.dz.description": "Entities between z and z + dz", "argument.entity.options.gamemode.description": "Tyalindor as t<PERSON><PERSON><PERSON>", "argument.entity.options.inapplicable": "Cilmë '%s' lá cárima sís", "argument.entity.options.level.description": "Tyellë", "argument.entity.options.level.negative": "Level shouldn't be negative", "argument.entity.options.limit.description": "Maximum number of entities to return", "argument.entity.options.limit.toosmall": "Limit must be at least 1", "argument.entity.options.mode.invalid": "Lasinwa hya úmára tyalmelë '%s'", "argument.entity.options.name.description": "Essë engwëo", "argument.entity.options.nbt.description": "Engwi as NBT", "argument.entity.options.predicate.description": "Custom predicate", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON> as sari", "argument.entity.options.sort.description": "Sort the entities", "argument.entity.options.sort.irreversible": "Invalid or unknown sort type '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON><PERSON> as tag", "argument.entity.options.team.description": "Entities on team", "argument.entity.options.type.description": "Entities of type", "argument.entity.options.type.invalid": "Lasinwa hya úmára nostalë engwëo '%s'", "argument.entity.options.unknown": "Lasinwa cilmë '%s'", "argument.entity.options.unterminated": "Expected end of options", "argument.entity.options.valueless": "Expected value for option '%s'", "argument.entity.options.x.description": "x position", "argument.entity.options.x_rotation.description": "<PERSON><PERSON><PERSON>'s x rotation", "argument.entity.options.y.description": "y position", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON>'s y rotation", "argument.entity.options.z.description": "z position", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON> en<PERSON>", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON>", "argument.entity.selector.missing": "Nostalë selehtoro lá sís", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON><PERSON> engwë", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> cávima", "argument.entity.selector.randomPlayer": "<PERSON><PERSON> tyalindo", "argument.entity.selector.self": "Silúmëa engwë", "argument.entity.selector.unknown": "Lasinwa nostalë selehtoro '%s'", "argument.entity.toomany": "<PERSON> engwë cá<PERSON>ma, mal antaina selehtor cavë ambë", "argument.enum.invalid": "Mirma \"%s\" lá mára", "argument.float.big": "Float must not be more than %s, found %s", "argument.float.low": "Float must not be less than %s, found %s", "argument.gamemode.invalid": "Lasinwa tyalmelë: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "I nótë ID lá mára", "argument.id.unknown": "Lasinwa nótë ID: %s", "argument.integer.big": "Integer must not be more than %s, found %s", "argument.integer.low": "Integer must not be less than %s, found %s", "argument.item.id.invalid": "Lasinwa nat: '%s'", "argument.item.tag.disallowed": "Tagi lár la<PERSON>, nanwë nati rië", "argument.literal.incorrect": "Expected literal %s", "argument.long.big": "Long must not be more than %s, found %s", "argument.long.low": "Long must not be less than %s, found %s", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Invalid array type '%s'", "argument.nbt.array.mixed": "Can't insert %s into %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "Expected key", "argument.nbt.expected.value": "Expected value", "argument.nbt.list.mixed": "Can't insert %s into list of %s", "argument.nbt.trailing": "Unexpected trailing data", "argument.player.entities": "<PERSON><PERSON><PERSON> tyalindor apinë sina axannen cé nauvar, mal antaina selehtor yorë engwi", "argument.player.toomany": "<PERSON> tyal<PERSON>, mal antaina selehtor cavë ambë", "argument.player.unknown": "Tana tyalindo lá nanwa", "argument.pos.missing.double": "Expected a coordinate", "argument.pos.missing.int": "Expected a block position", "argument.pos.mixed": "Cannot mix world & local coordinates (everything must either use ^ or not)", "argument.pos.outofbounds": "That position is outside the allowed boundaries.", "argument.pos.outofworld": "That position is out of this world!", "argument.pos.unloaded": "That position is not loaded", "argument.pos2d.incomplete": "Incomplete (expected 2 coordinates)", "argument.pos3d.incomplete": "Incomplete (expected 3 coordinates)", "argument.range.empty": "<PERSON><PERSON>n mirma hya anyaiti<PERSON> mirmaron", "argument.range.ints": "<PERSON>uantë nó<PERSON> la<PERSON>, l<PERSON>", "argument.range.swapped": "Min cannot be bigger than max", "argument.resource.invalid_type": "Element '%s' has wrong type '%s' (expected '%s')", "argument.resource.not_found": "Can't find element '%s' of type '%s'", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Tag '%s' has wrong type '%s' (expected '%s')", "argument.resource_tag.not_found": "Can't find tag '%s' of type '%s'", "argument.rotation.incomplete": "Incomplete (expected 2 coordinates)", "argument.scoreHolder.empty": "No relevant score holders could be found", "argument.scoreboardDisplaySlot.invalid": "Lasinwa assa '%s'", "argument.style.invalid": "Invalid style: %s", "argument.time.invalid_tick_count": "The tick count must be non-negative", "argument.time.invalid_unit": "Invalid unit", "argument.time.tick_count_too_low": "The tick count must not be less than %s, found %s", "argument.uuid.invalid": "I UUID lá mára", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Lasinwa tag ronwo '%s'", "arguments.function.tag.unknown": "Unknown function tag '%s'", "arguments.function.unknown": "Unknown function %s", "arguments.item.component.expected": "Expected item component", "arguments.item.component.malformed": "Malformed '%s' component: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s can only stack up to %s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "Lasinwa tag nato '%s'", "arguments.nbtpath.node.invalid": "Invalid NBT path element", "arguments.nbtpath.nothing_found": "Found no elements matching %s", "arguments.nbtpath.too_deep": "Sina NBT caitanwa tumna langë", "arguments.nbtpath.too_large": "Sina NBT höa langë", "arguments.objective.notFound": "Unknown scoreboard objective '%s'", "arguments.objective.readonly": "Scoreboard objective '%s' is read-only", "arguments.operation.div0": "Cannot divide by zero", "arguments.operation.invalid": "Invalid operation", "arguments.swizzle.invalid": "Invalid swizzle, expected combination of 'x', 'y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Varma", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "<PERSON><PERSON><PERSON>", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Anyaitië lancariëo ó ronwar", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Anyaitië lancariëo ó engwi", "attribute.name.explosion_knockback_resistance": "Nornië anat auhatië ruviello", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Lintië vílëo", "attribute.name.follow_range": "Anyaitië hiliëo vëoron", "attribute.name.generic.armor": "Varma", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "<PERSON><PERSON><PERSON>", "attribute.name.generic.attack_knockback": "Attack Knockback", "attribute.name.generic.attack_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.block_interaction_range": "Anyaitië lancariëo ó ronwar", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "Anyaitië lancariëo ó engwi", "attribute.name.generic.explosion_knockback_resistance": "Nornië anat auhatië ruviello", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "Lintië vílëo", "attribute.name.generic.follow_range": "Anyaitië hiliëo vëoron", "attribute.name.generic.gravity": "Gravity", "attribute.name.generic.jump_strength": "Jump Strength", "attribute.name.generic.knockback_resistance": "Nornië anat auhatië", "attribute.name.generic.luck": "Almë", "attribute.name.generic.max_absorption": "Max Absorption", "attribute.name.generic.max_health": "Max Health", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Lintië", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Safe Fall Distance", "attribute.name.generic.scale": "Scale", "attribute.name.generic.step_height": "Step Height", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Gravity", "attribute.name.horse.jump_strength": "Poldorë R<PERSON>", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Nornië anat auhatië", "attribute.name.luck": "Almë", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "Lintië", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Block Break Speed", "attribute.name.player.block_interaction_range": "Anyaitië lancariëo ó ronwar", "attribute.name.player.entity_interaction_range": "Anyaitië lancariëo ó engwi", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "<PERSON><PERSON><PERSON> hlici<PERSON>", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Scale", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON> hlici<PERSON>", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Anyaitië telyantassëo vëoron", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Turyalë urcoron", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON><PERSON> v<PERSON>", "biome.minecraft.basalt_deltas": "Tornondorien", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Hwindetaurë", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "Carnitaurë", "biome.minecraft.dark_forest": "Moritaurë", "biome.minecraft.deep_cold_ocean": "Tumba ringa ëaron", "biome.minecraft.deep_dark": "Tumba mornië", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON> helina <PERSON>", "biome.minecraft.deep_lukewarm_ocean": "Tumba pellauca <PERSON>n", "biome.minecraft.deep_ocean": "Tumba ëaron", "biome.minecraft.desert": "Erumë", "biome.minecraft.dripstone_caves": "Liptondoinë rottor", "biome.minecraft.end_barrens": "Parcanó<PERSON>", "biome.minecraft.end_highlands": "Tarastar Endo", "biome.minecraft.end_midlands": "<PERSON><PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "Almëa taurë", "biome.minecraft.forest": "Taurë", "biome.minecraft.frozen_ocean": "<PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON>", "biome.minecraft.grove": "Töasta", "biome.minecraft.ice_spikes": "Helcaraxë", "biome.minecraft.jagged_peaks": "Ondocaraxë", "biome.minecraft.jungle": "Rostaurë", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON> rottor", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "<PERSON><PERSON> telumbion", "biome.minecraft.nether_wastes": "Nether Wastes", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON><PERSON> hwindion", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> sonoron", "biome.minecraft.old_growth_spruce_taiga": "Enwinalda taurë súcion", "biome.minecraft.pale_garden": "Néca tarwa", "biome.minecraft.plains": "<PERSON><PERSON>", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "Savanna", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "Lossëa hresta", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON>di", "biome.minecraft.snowy_taiga": "Lossëa lostaurë", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON>", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.stony_shore": "Sarnië", "biome.minecraft.sunflower_plains": "Anarilótië landar", "biome.minecraft.swamp": "Hlöa", "biome.minecraft.taiga": "Lostaurë", "biome.minecraft.the_end": "I End", "biome.minecraft.the_void": "<PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON> ta<PERSON>", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON> ta<PERSON>", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON> virittië ambor", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON> ambor", "biome.minecraft.windswept_savanna": "Vailima savanna", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_button": "<PERSON><PERSON>", "block.minecraft.acacia_door": "Fendë nís<PERSON>ldo", "block.minecraft.acacia_fence": "Hahta nísimaldo", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON>", "block.minecraft.acacia_hanging_sign": "Lingatanna nísimaldo", "block.minecraft.acacia_leaves": "Lassi nísimaldavë", "block.minecraft.acacia_log": "Pulco n<PERSON>", "block.minecraft.acacia_planks": "<PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Sang<PERSON><PERSON>", "block.minecraft.acacia_sapling": "Nessornë nísimaldo", "block.minecraft.acacia_sign": "<PERSON><PERSON>", "block.minecraft.acacia_slab": "Perronwa nísimaldo", "block.minecraft.acacia_stairs": "<PERSON><PERSON>", "block.minecraft.acacia_trapdoor": "Lat nísimaldo", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON> linga<PERSON>na <PERSON>", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON><PERSON> tanna n<PERSON>", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.activator_rail": "Caltaitë angatië", "block.minecraft.air": "Vista", "block.minecraft.allium": "Olmelaiquë", "block.minecraft.amethyst_block": "<PERSON><PERSON>", "block.minecraft.amethyst_cluster": "Combë mirumírion", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON> lemma", "block.minecraft.andesite": "Andëondo", "block.minecraft.andesite_slab": "Perronwa andëondo", "block.minecraft.andesite_stairs": "Tyeller andëondo", "block.minecraft.andesite_wall": "Ramba andëondo", "block.minecraft.anvil": "<PERSON><PERSON>", "block.minecraft.attached_melon_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_pumpkin_stem": "<PERSON><PERSON><PERSON> culve<PERSON>", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON>", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "Ronwa vam<PERSON>o", "block.minecraft.bamboo_button": "<PERSON><PERSON> v<PERSON>", "block.minecraft.bamboo_door": "Fendë vambuo", "block.minecraft.bamboo_fence": "Hahta vambuo", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON> v<PERSON>o", "block.minecraft.bamboo_hanging_sign": "Lingatanna vambuo", "block.minecraft.bamboo_mosaic": "Cantië vambuo", "block.minecraft.bamboo_mosaic_slab": "Perronwa cantiëo vambuo", "block.minecraft.bamboo_mosaic_stairs": "Tyeller cantiëo vambuo", "block.minecraft.bamboo_planks": "Panor vam<PERSON>o", "block.minecraft.bamboo_pressure_plate": "Sangapalma vambuo", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON> v<PERSON>bu", "block.minecraft.bamboo_sign": "<PERSON><PERSON> v<PERSON>o", "block.minecraft.bamboo_slab": "Perronwa vambuo", "block.minecraft.bamboo_stairs": "Tyeller vambuo", "block.minecraft.bamboo_trapdoor": "Lat vambuo", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna vam<PERSON>o", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON> tanna vambuo", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> em<PERSON>", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON> em<PERSON>", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON><PERSON> emma", "block.minecraft.banner.base.gray": "<PERSON><PERSON> emma", "block.minecraft.banner.base.green": "<PERSON><PERSON> em<PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON> emma", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON> emma", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON> emma", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON> em<PERSON>", "block.minecraft.banner.base.pink": "Fanyacarnë emma", "block.minecraft.banner.base.purple": "Luica<PERSON><PERSON> emma", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON> em<PERSON>", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> emma", "block.minecraft.banner.base.yellow": "<PERSON><PERSON>", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.cyan": "Fanyaluinë pel<PERSON>", "block.minecraft.banner.border.gray": "Sinda pelmassë", "block.minecraft.banner.border.green": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON>ë p<PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.orange": "Culuina p<PERSON>", "block.minecraft.banner.border.pink": "Fanyacarnë pelmassë", "block.minecraft.banner.border.purple": "Luicarnë pelmassë", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> pel<PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> te<PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON> tesari", "block.minecraft.banner.bricks.gray": "Sindë tesari", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON> tesari", "block.minecraft.banner.bricks.light_blue": "Helwë tesari", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.banner.bricks.lime": "Venyë tesari", "block.minecraft.banner.bricks.magenta": "Mangentë tesari", "block.minecraft.banner.bricks.orange": "Culuinë tesari", "block.minecraft.banner.bricks.pink": "Fanyacarni tesari", "block.minecraft.banner.bricks.purple": "<PERSON>ica<PERSON><PERSON> tesari", "block.minecraft.banner.bricks.red": "<PERSON><PERSON> tesar<PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> tesari", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON> rind<PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON><PERSON> rindë", "block.minecraft.banner.circle.cyan": "Fanyaluinë rindë", "block.minecraft.banner.circle.gray": "Sinda rindë", "block.minecraft.banner.circle.green": "Laica rindë", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON> rind<PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON>ë rind<PERSON>", "block.minecraft.banner.circle.lime": "Venya rindë", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON> rind<PERSON>", "block.minecraft.banner.circle.orange": "Culuina rindë", "block.minecraft.banner.circle.pink": "Fanyacarnë rindë", "block.minecraft.banner.circle.purple": "Luicarnë rindë", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON> rindë", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> rindë", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.cyan": "Fanyaluinë cend<PERSON>", "block.minecraft.banner.creeper.gray": "Sinda cendelë <PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.creeper.orange": "Culuina c<PERSON>", "block.minecraft.banner.creeper.pink": "Fanyacarnë cendelë <PERSON>wa", "block.minecraft.banner.creeper.purple": "Luicarnë cend<PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> cend<PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Morë talta tarwë", "block.minecraft.banner.cross.blue": "Luinë talta tarwë", "block.minecraft.banner.cross.brown": "Varnë talta tarwë", "block.minecraft.banner.cross.cyan": "Fanyaluinë talta tarwë", "block.minecraft.banner.cross.gray": "Sinda talta tarwë", "block.minecraft.banner.cross.green": "Laica talta tarwë", "block.minecraft.banner.cross.light_blue": "Helwa talta tarwë", "block.minecraft.banner.cross.light_gray": "Mísë talta tarwë", "block.minecraft.banner.cross.lime": "Venya talta tarwë", "block.minecraft.banner.cross.magenta": "Mangenta talta tarwë", "block.minecraft.banner.cross.orange": "Culuina talta tarwë", "block.minecraft.banner.cross.pink": "Fanyacarnë talta tarwë", "block.minecraft.banner.cross.purple": "Luicarnë talta tarwë", "block.minecraft.banner.cross.red": "Carnë talta tarwë", "block.minecraft.banner.cross.white": "Ninquë talta tarwë", "block.minecraft.banner.cross.yellow": "Malina talta tarwë", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>n<PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.cyan": "Fanyaluinë carcara pel<PERSON>", "block.minecraft.banner.curly_border.gray": "Sinda carcara pel<PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.light_gray": "Mísë carcara p<PERSON>", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.magenta": "Mangenta carcara pel<PERSON>", "block.minecraft.banner.curly_border.orange": "Culuina carcara p<PERSON>", "block.minecraft.banner.curly_border.pink": "Fanyacarnë carcara pel<PERSON>", "block.minecraft.banner.curly_border.purple": "Luicarnë carcara pel<PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> carcara pel<PERSON>", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> carcara p<PERSON>", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> h<PERSON>a tára peresta", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON> hyal<PERSON>a tára peresta", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON><PERSON> hyallanta tára peresta", "block.minecraft.banner.diagonal_left.cyan": "Fanyaluinë hyallanta tára peresta", "block.minecraft.banner.diagonal_left.gray": "Sinda hyallanta tára peresta", "block.minecraft.banner.diagonal_left.green": "Laica hyallanta tára peresta", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> hyallanta tára peresta", "block.minecraft.banner.diagonal_left.light_gray": "Mísë hyallanta tára peresta", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON>ya hyallanta tára peresta", "block.minecraft.banner.diagonal_left.magenta": "Mangenta hyallanta tára peresta", "block.minecraft.banner.diagonal_left.orange": "Culuina hyallanta tára peresta", "block.minecraft.banner.diagonal_left.pink": "Fanyacarnë hyallanta tára peresta", "block.minecraft.banner.diagonal_left.purple": "Luicarnë hyallanta tára peresta", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON>ë hyallanta tára peresta", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> hyallanta tára peresta", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON> h<PERSON>a tára peresta", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON> follanta tára peresta", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON>ë follanta tára peresta", "block.minecraft.banner.diagonal_right.brown": "Varnë follanta tára peresta", "block.minecraft.banner.diagonal_right.cyan": "Fanyaluinë follanta tára peresta", "block.minecraft.banner.diagonal_right.gray": "Sinda follanta tára peresta", "block.minecraft.banner.diagonal_right.green": "Laica follanta tára peresta", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON> follanta tára peresta", "block.minecraft.banner.diagonal_right.light_gray": "Mísë follanta tára peresta", "block.minecraft.banner.diagonal_right.lime": "Venya follanta tára peresta", "block.minecraft.banner.diagonal_right.magenta": "Mangenta follanta tára peresta", "block.minecraft.banner.diagonal_right.orange": "Culuina follanta tára peresta", "block.minecraft.banner.diagonal_right.pink": "Fanyacarnë follanta tára peresta", "block.minecraft.banner.diagonal_right.purple": "Luicarnë follanta tára peresta", "block.minecraft.banner.diagonal_right.red": "Carnë follanta tára peresta", "block.minecraft.banner.diagonal_right.white": "Ninq<PERSON>ë follanta tára peresta", "block.minecraft.banner.diagonal_right.yellow": "<PERSON>na follanta tára peresta", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON> follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON><PERSON> follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.brown": "Varnë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.cyan": "Fanyaluinë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.gray": "Sinda follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.green": "Laica follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON> follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.light_gray": "Mísë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON>ya follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.magenta": "Mangenta follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.orange": "Culuina follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.pink": "Fanyacarnë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.purple": "Luicarnë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.red": "Carnë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.white": "<PERSON>nq<PERSON>ë follanta tumna peresta", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON>na follanta tumna peresta", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON> h<PERSON>a tumna peresta", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.cyan": "Fanyaluinë hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.gray": "Sinda hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.green": "Laica hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.light_gray": "Mísë hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON>ya hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON>genta hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.orange": "C<PERSON>ina hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.pink": "Fanyacarnë hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.purple": "Luicarnë hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON> hyallanta tumna peresta", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON><PERSON> hwind<PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>uin<PERSON> hwindë", "block.minecraft.banner.flow.gray": "Sinda hwindë", "block.minecraft.banner.flow.green": "<PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> hwind<PERSON>", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.flow.orange": "Culuina hwindë", "block.minecraft.banner.flow.pink": "Fanyacarnë hwindë", "block.minecraft.banner.flow.purple": "Luicarnë hwindë", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON> hwind<PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> hwindë", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON> canta almo", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON> canta almo", "block.minecraft.banner.flower.brown": "Varnë canta almo", "block.minecraft.banner.flower.cyan": "Fanyaluinë canta almo", "block.minecraft.banner.flower.gray": "Sinda canta almo", "block.minecraft.banner.flower.green": "Laica canta almo", "block.minecraft.banner.flower.light_blue": "Helwa canta almo", "block.minecraft.banner.flower.light_gray": "Mísë canta almo", "block.minecraft.banner.flower.lime": "<PERSON><PERSON>ya canta almo", "block.minecraft.banner.flower.magenta": "Mangenta canta almo", "block.minecraft.banner.flower.orange": "Culuina canta almo", "block.minecraft.banner.flower.pink": "Fanyacarnë canta almo", "block.minecraft.banner.flower.purple": "Luicarnë canta almo", "block.minecraft.banner.flower.red": "Carnë canta almo", "block.minecraft.banner.flower.white": "<PERSON>n<PERSON><PERSON><PERSON> canta almo", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> canta almo", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.blue": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.brown": "<PERSON><PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.gray": "Sinda coron", "block.minecraft.banner.globe.green": "<PERSON>ca coron", "block.minecraft.banner.globe.light_blue": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.light_gray": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.lime": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.magenta": "Mangenta coron", "block.minecraft.banner.globe.orange": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.pink": "Fanyacarnë coron", "block.minecraft.banner.globe.purple": "Luicarn<PERSON> coron", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> coron", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON> coron", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> carello", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> carello", "block.minecraft.banner.gradient.cyan": "Fanyaluinë p<PERSON><PERSON>a carello", "block.minecraft.banner.gradient.gray": "Sinda p<PERSON><PERSON>a carello", "block.minecraft.banner.gradient.green": "Laica pí<PERSON> carello", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> carello", "block.minecraft.banner.gradient.light_gray": "Mísë p<PERSON>a carello", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> carello", "block.minecraft.banner.gradient.magenta": "Mangenta p<PERSON>cala carello", "block.minecraft.banner.gradient.orange": "Culuina pícala carello", "block.minecraft.banner.gradient.pink": "Fanyacarnë pícala carello", "block.minecraft.banner.gradient.purple": "Luicarnë p<PERSON><PERSON>a carello", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>a carello", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>a carello", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON>llo", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON><PERSON> ta<PERSON>", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> p<PERSON> talma<PERSON>", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> talma<PERSON>", "block.minecraft.banner.gradient_up.cyan": "Fanyaluinë p<PERSON><PERSON>a talmallo", "block.minecraft.banner.gradient_up.gray": "Sinda pícala talmallo", "block.minecraft.banner.gradient_up.green": "Laica pí<PERSON> talmallo", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON><PERSON> talmallo", "block.minecraft.banner.gradient_up.light_gray": "Mísë p<PERSON> talmallo", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> talmallo", "block.minecraft.banner.gradient_up.magenta": "Mangenta p<PERSON><PERSON>a talmallo", "block.minecraft.banner.gradient_up.orange": "Culuina p<PERSON><PERSON>a talmallo", "block.minecraft.banner.gradient_up.pink": "Fanyacarnë pícala talmallo", "block.minecraft.banner.gradient_up.purple": "Luicarnë p<PERSON>cala talmallo", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON> p<PERSON> talmallo", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>a talmallo", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON> talma<PERSON>", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.guster.cyan": "Fanyaluinë h<PERSON>", "block.minecraft.banner.guster.gray": "Sinda hwesta", "block.minecraft.banner.guster.green": "Laica h<PERSON>ta", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON>ë h<PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON> hwesta", "block.minecraft.banner.guster.orange": "Culuina h<PERSON>ta", "block.minecraft.banner.guster.pink": "Fanyacarnë hwesta", "block.minecraft.banner.guster.purple": "Luicarnë hwesta", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON> t<PERSON>ra peresta", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> tára peresta", "block.minecraft.banner.half_horizontal.brown": "Varnë tára peresta", "block.minecraft.banner.half_horizontal.cyan": "Fanyaluinë tára peresta", "block.minecraft.banner.half_horizontal.gray": "Sinda tára peresta", "block.minecraft.banner.half_horizontal.green": "Laica tára peresta", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> tára peresta", "block.minecraft.banner.half_horizontal.light_gray": "Mísë tára peresta", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON>ya tára peresta", "block.minecraft.banner.half_horizontal.magenta": "Mangenta tára peresta", "block.minecraft.banner.half_horizontal.orange": "Culuina tára peresta", "block.minecraft.banner.half_horizontal.pink": "Fanyacarnë tára peresta", "block.minecraft.banner.half_horizontal.purple": "Luicarnë tára peresta", "block.minecraft.banner.half_horizontal.red": "Carnë tára peresta", "block.minecraft.banner.half_horizontal.white": "<PERSON>n<PERSON><PERSON><PERSON> tára peresta", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> tára peresta", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON><PERSON> tumna peresta", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON> tumna peresta", "block.minecraft.banner.half_horizontal_bottom.brown": "Varnë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.cyan": "Fanyaluinë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.gray": "Sinda tumna peresta", "block.minecraft.banner.half_horizontal_bottom.green": "Laica tumna peresta", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON>wa tumna peresta", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Mísë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.lime": "Venya tumna peresta", "block.minecraft.banner.half_horizontal_bottom.magenta": "Mangenta tumna peresta", "block.minecraft.banner.half_horizontal_bottom.orange": "Culuina tumna peresta", "block.minecraft.banner.half_horizontal_bottom.pink": "Fanyacarnë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.purple": "Luicarnë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.red": "Carnë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON>nq<PERSON>ë tumna peresta", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON>na tumna peresta", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical.cyan": "Fanyaluinë hyarperesta", "block.minecraft.banner.half_vertical.gray": "Sinda hyarperesta", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON> hyar<PERSON>", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical.light_gray": "<PERSON>ísë h<PERSON>", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON> hyar<PERSON>", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON> hyarperesta", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON> hyarperesta", "block.minecraft.banner.half_vertical.pink": "Fanyacarnë hyarperesta", "block.minecraft.banner.half_vertical.purple": "Luicarnë hyarperesta", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> hyarper<PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>n<PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Fanyaluinë forperesta", "block.minecraft.banner.half_vertical_right.gray": "Sinda forperesta", "block.minecraft.banner.half_vertical_right.green": "Lai<PERSON> for<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON>ísë <PERSON>", "block.minecraft.banner.half_vertical_right.lime": "Venya forperesta", "block.minecraft.banner.half_vertical_right.magenta": "Mangenta forperesta", "block.minecraft.banner.half_vertical_right.orange": "Culuina forperesta", "block.minecraft.banner.half_vertical_right.pink": "Fanyacarnë forperesta", "block.minecraft.banner.half_vertical_right.purple": "Luicarnë forperesta", "block.minecraft.banner.half_vertical_right.red": "<PERSON>në <PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> forperesta", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "Varn<PERSON>", "block.minecraft.banner.mojang.cyan": "Fanyaluinë Nat", "block.minecraft.banner.mojang.gray": "Sinda Nat", "block.minecraft.banner.mojang.green": "Laica Nat", "block.minecraft.banner.mojang.light_blue": "Helwa Nat", "block.minecraft.banner.mojang.light_gray": "Mísë Nat", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON> Nat", "block.minecraft.banner.mojang.magenta": "Mangenta Nat", "block.minecraft.banner.mojang.orange": "Culuina Nat", "block.minecraft.banner.mojang.pink": "Fanyacarnë Nat", "block.minecraft.banner.mojang.purple": "Luicarnë Nat", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "Ninq<PERSON>ë Nat", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON>", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON> mundo", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON> mundo", "block.minecraft.banner.piglin.brown": "Varnë mundo", "block.minecraft.banner.piglin.cyan": "Fanyaluinë mundo", "block.minecraft.banner.piglin.gray": "Sinda mundo", "block.minecraft.banner.piglin.green": "Laica mundo", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_gray": "Mísë mundo", "block.minecraft.banner.piglin.lime": "Venya mundo", "block.minecraft.banner.piglin.magenta": "Mangenta <PERSON>ndo", "block.minecraft.banner.piglin.orange": "Culuina Mundo", "block.minecraft.banner.piglin.pink": "Fanyacarnë mundo", "block.minecraft.banner.piglin.purple": "Luicarnë mundo", "block.minecraft.banner.piglin.red": "<PERSON>në mundo", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> talta cantil", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON> talta cantil", "block.minecraft.banner.rhombus.brown": "Varnë talta cantil", "block.minecraft.banner.rhombus.cyan": "Fanyaluinë talta cantil", "block.minecraft.banner.rhombus.gray": "Sinda talta cantil", "block.minecraft.banner.rhombus.green": "Laica talta cantil", "block.minecraft.banner.rhombus.light_blue": "Helwa talta cantil", "block.minecraft.banner.rhombus.light_gray": "Mísë talta cantil", "block.minecraft.banner.rhombus.lime": "Venya talta cantil", "block.minecraft.banner.rhombus.magenta": "Mangenta talta cantil", "block.minecraft.banner.rhombus.orange": "Culuina talta cantil", "block.minecraft.banner.rhombus.pink": "Fanyacarnë talta cantil", "block.minecraft.banner.rhombus.purple": "Luicarnë talta cantil", "block.minecraft.banner.rhombus.red": "Carnë talta cantil", "block.minecraft.banner.rhombus.white": "Ninquë talta cantil", "block.minecraft.banner.rhombus.yellow": "<PERSON>na talta cantil", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON> canta co<PERSON>o", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON> canta coropëo", "block.minecraft.banner.skull.brown": "Varnë canta coropëo", "block.minecraft.banner.skull.cyan": "Fanyaluinë canta coropëo", "block.minecraft.banner.skull.gray": "Sinda canta coropëo", "block.minecraft.banner.skull.green": "Laica canta coropëo", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON> canta co<PERSON>o", "block.minecraft.banner.skull.light_gray": "Mísë canta coropëo", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON> canta co<PERSON>o", "block.minecraft.banner.skull.magenta": "Mangenta canta coropëo", "block.minecraft.banner.skull.orange": "Culuina canta co<PERSON>o", "block.minecraft.banner.skull.pink": "Fanyacarnë canta coropëo", "block.minecraft.banner.skull.purple": "Luicarnë canta coropëo", "block.minecraft.banner.skull.red": "Carnë canta coropëo", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> canta coropëo", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> canta co<PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "Sindë <PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Culuinë <PERSON>", "block.minecraft.banner.small_stripes.pink": "Fanyacarni Rimpi", "block.minecraft.banner.small_stripes.purple": "Luicarn<PERSON> rim<PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON><PERSON> tumna hyarnel<PERSON>", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> tumna hyarneltë", "block.minecraft.banner.square_bottom_left.brown": "Varnë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.cyan": "Fanyaluinë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.gray": "Sinda tumna hyarnel<PERSON>", "block.minecraft.banner.square_bottom_left.green": "Laica tumna hyar<PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "<PERSON><PERSON><PERSON> tumna hyar<PERSON>", "block.minecraft.banner.square_bottom_left.light_gray": "Mísë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON> tumna hyar<PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON> tumna hyarnel<PERSON>", "block.minecraft.banner.square_bottom_left.orange": "Culuina tumna hyarnel<PERSON>", "block.minecraft.banner.square_bottom_left.pink": "Fanyacarnë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.purple": "Luicarnë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.red": "Carnë tumna hyarneltë", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumna hyarneltë", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON> tumna h<PERSON>", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON><PERSON> tumna fornelt<PERSON>", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON> tumna forneltë", "block.minecraft.banner.square_bottom_right.brown": "Varnë tumna forneltë", "block.minecraft.banner.square_bottom_right.cyan": "Fanyaluinë tumna forneltë", "block.minecraft.banner.square_bottom_right.gray": "Sinda tumna fornelt<PERSON>", "block.minecraft.banner.square_bottom_right.green": "Laica tumna fornel<PERSON>ë", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON> tumna fornel<PERSON>", "block.minecraft.banner.square_bottom_right.light_gray": "Mísë tumna forneltë", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON>ya tumna fornel<PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "Mangenta tumna fornelt<PERSON>", "block.minecraft.banner.square_bottom_right.orange": "Culuina tumna forneltë", "block.minecraft.banner.square_bottom_right.pink": "Fanyacarnë tumna forneltë", "block.minecraft.banner.square_bottom_right.purple": "Luicarnë tumna forneltë", "block.minecraft.banner.square_bottom_right.red": "Carnë tumna forneltë", "block.minecraft.banner.square_bottom_right.white": "<PERSON>nq<PERSON>ë tumna forneltë", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON> tumna for<PERSON>", "block.minecraft.banner.square_top_left.black": "<PERSON><PERSON><PERSON> tára hyar<PERSON>", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> tára hyar<PERSON>", "block.minecraft.banner.square_top_left.brown": "Varnë tára hyar<PERSON>ë", "block.minecraft.banner.square_top_left.cyan": "Fanyaluinë tára hyarneltë", "block.minecraft.banner.square_top_left.gray": "Sinda tára hyar<PERSON>", "block.minecraft.banner.square_top_left.green": "Laica tára h<PERSON>", "block.minecraft.banner.square_top_left.light_blue": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON>", "block.minecraft.banner.square_top_left.light_gray": "Mísë tára hyarneltë", "block.minecraft.banner.square_top_left.lime": "<PERSON><PERSON>ya t<PERSON>ra h<PERSON>", "block.minecraft.banner.square_top_left.magenta": "Mangenta tára h<PERSON>", "block.minecraft.banner.square_top_left.orange": "Culuina tára hyar<PERSON>", "block.minecraft.banner.square_top_left.pink": "Fanyacarnë tára hyarneltë", "block.minecraft.banner.square_top_left.purple": "Luicarnë tára hyarneltë", "block.minecraft.banner.square_top_left.red": "Carnë tára hyar<PERSON>", "block.minecraft.banner.square_top_left.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> tára hyar<PERSON>ë", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON> tára for<PERSON>", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> tára fornelt<PERSON>", "block.minecraft.banner.square_top_right.brown": "Varnë tára forneltë", "block.minecraft.banner.square_top_right.cyan": "Fanyaluinë tára forneltë", "block.minecraft.banner.square_top_right.gray": "Sinda tára forneltë", "block.minecraft.banner.square_top_right.green": "Laica tára forneltë", "block.minecraft.banner.square_top_right.light_blue": "<PERSON><PERSON><PERSON> t<PERSON>ra fornel<PERSON>", "block.minecraft.banner.square_top_right.light_gray": "Mísë tára forneltë", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON>ya t<PERSON>ra fornel<PERSON>", "block.minecraft.banner.square_top_right.magenta": "Mangenta tára for<PERSON>", "block.minecraft.banner.square_top_right.orange": "Culuina tára forneltë", "block.minecraft.banner.square_top_right.pink": "Fanyacarnë tára forneltë", "block.minecraft.banner.square_top_right.purple": "Luicarnë tára forneltë", "block.minecraft.banner.square_top_right.red": "Carnë tára fornel<PERSON>ë", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> tára forneltë", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON> t<PERSON> for<PERSON>", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON><PERSON> tarwë", "block.minecraft.banner.straight_cross.blue": "Luinë tarwë", "block.minecraft.banner.straight_cross.brown": "Varnë tarwë", "block.minecraft.banner.straight_cross.cyan": "Fanyaluinë tarwë", "block.minecraft.banner.straight_cross.gray": "Sinda tarwë", "block.minecraft.banner.straight_cross.green": "Laica tarwë", "block.minecraft.banner.straight_cross.light_blue": "Helwa tarwë", "block.minecraft.banner.straight_cross.light_gray": "Mísë tarwë", "block.minecraft.banner.straight_cross.lime": "Venya tarwë", "block.minecraft.banner.straight_cross.magenta": "Mangenta tarwë", "block.minecraft.banner.straight_cross.orange": "Culuina tarwë", "block.minecraft.banner.straight_cross.pink": "Fanyacarnë tarwë", "block.minecraft.banner.straight_cross.purple": "Luicarnë tarwë", "block.minecraft.banner.straight_cross.red": "Carnë tarwë", "block.minecraft.banner.straight_cross.white": "Ninquë tarwë", "block.minecraft.banner.straight_cross.yellow": "<PERSON>na tarw<PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON> ta<PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.gray": "Sinda talma", "block.minecraft.banner.stripe_bottom.green": "Laica talma", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON>a talma", "block.minecraft.banner.stripe_bottom.orange": "Culuina talma", "block.minecraft.banner.stripe_bottom.pink": "Fanyacarnë talma", "block.minecraft.banner.stripe_bottom.purple": "Luicarnë talma", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> talma", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON> ta<PERSON>", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.cyan": "Fanyaluinë peltas", "block.minecraft.banner.stripe_center.gray": "Sinda peltas", "block.minecraft.banner.stripe_center.green": "Laica peltas", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON> peltas", "block.minecraft.banner.stripe_center.light_gray": "Mísë p<PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON> peltas", "block.minecraft.banner.stripe_center.magenta": "Mangenta peltas", "block.minecraft.banner.stripe_center.orange": "Culuina peltas", "block.minecraft.banner.stripe_center.pink": "Fanyacarnë peltas", "block.minecraft.banner.stripe_center.purple": "Luicarnë peltas", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> peltas", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_downleft.gray": "Sinda hyallanta", "block.minecraft.banner.stripe_downleft.green": "Laica hyallanta", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON>a hyallanta", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON><PERSON> hyallanta", "block.minecraft.banner.stripe_downleft.pink": "Fanyacarnë hyallanta", "block.minecraft.banner.stripe_downleft.purple": "Luicarnë hyallanta", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON><PERSON> foll<PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.cyan": "Fanyaluinë follanta", "block.minecraft.banner.stripe_downright.gray": "Sinda follanta", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.light_gray": "Mísë follanta", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.magenta": "Mangenta follanta", "block.minecraft.banner.stripe_downright.orange": "C<PERSON>ina follanta", "block.minecraft.banner.stripe_downright.pink": "Fanyacarnë follanta", "block.minecraft.banner.stripe_downright.purple": "Luicarnë follanta", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> follanta", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON> follanta", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON>n<PERSON>", "block.minecraft.banner.stripe_left.cyan": "Fanyaluinë h<PERSON>", "block.minecraft.banner.stripe_left.gray": "Sinda hyarpeltas", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.light_gray": "Mísë <PERSON>", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "Fanyacarnë hyarpeltas", "block.minecraft.banner.stripe_left.purple": "Luicarnë hyarpeltas", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "Varnë <PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Fanyaluinë rimpë", "block.minecraft.banner.stripe_middle.gray": "Sinda rimpë", "block.minecraft.banner.stripe_middle.green": "Laica rim<PERSON>ë", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "Mísë <PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON>enya <PERSON>", "block.minecraft.banner.stripe_middle.magenta": "Mangenta rim<PERSON>ë", "block.minecraft.banner.stripe_middle.orange": "Culuina rimpë", "block.minecraft.banner.stripe_middle.pink": "Fanyacarnë rimpë", "block.minecraft.banner.stripe_middle.purple": "Luicarnë rimpë", "block.minecraft.banner.stripe_middle.red": "Carnë <PERSON>", "block.minecraft.banner.stripe_middle.white": "Ninq<PERSON>ë rimpë", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.brown": "Varnë <PERSON>", "block.minecraft.banner.stripe_right.cyan": "Fanyaluinë forpeltas", "block.minecraft.banner.stripe_right.gray": "Sinda forpeltas", "block.minecraft.banner.stripe_right.green": "Laica <PERSON>pel<PERSON>", "block.minecraft.banner.stripe_right.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_gray": "Mísë forpeltas", "block.minecraft.banner.stripe_right.lime": "Venya forpel<PERSON>", "block.minecraft.banner.stripe_right.magenta": "Mangent<PERSON>", "block.minecraft.banner.stripe_right.orange": "Culuina forpeltas", "block.minecraft.banner.stripe_right.pink": "Fanyacarnë forpeltas", "block.minecraft.banner.stripe_right.purple": "Luicarnë forpeltas", "block.minecraft.banner.stripe_right.red": "Carnë forpel<PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON>nq<PERSON><PERSON> forpel<PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON> cas", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> cas", "block.minecraft.banner.stripe_top.brown": "<PERSON>arn<PERSON> cas", "block.minecraft.banner.stripe_top.cyan": "Fanyaluinë cas", "block.minecraft.banner.stripe_top.gray": "Sinda cas", "block.minecraft.banner.stripe_top.green": "Laica cas", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON> cas", "block.minecraft.banner.stripe_top.light_gray": "Mísë cas", "block.minecraft.banner.stripe_top.lime": "Venya cas", "block.minecraft.banner.stripe_top.magenta": "Mangenta cas", "block.minecraft.banner.stripe_top.orange": "Culuina cas", "block.minecraft.banner.stripe_top.pink": "Fanyacarnë cas", "block.minecraft.banner.stripe_top.purple": "Luicarnë cas", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> cas", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> cas", "block.minecraft.banner.stripe_top.yellow": "<PERSON>na cas", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> ne<PERSON>", "block.minecraft.banner.triangle_bottom.brown": "V<PERSON>në neltil", "block.minecraft.banner.triangle_bottom.cyan": "Fanyaluinë neltil", "block.minecraft.banner.triangle_bottom.gray": "Sinda neltil", "block.minecraft.banner.triangle_bottom.green": "Laica neltil", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON><PERSON> neltil", "block.minecraft.banner.triangle_bottom.light_gray": "Mísë ne<PERSON>", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON>ya neltil", "block.minecraft.banner.triangle_bottom.magenta": "Mangenta neltil", "block.minecraft.banner.triangle_bottom.orange": "Culuina neltil", "block.minecraft.banner.triangle_bottom.pink": "Fanyacarnë neltil", "block.minecraft.banner.triangle_bottom.purple": "Luicarnë neltil", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON> nelt<PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> neltil", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON><PERSON> ne<PERSON> n<PERSON>", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON>ë neltil nuquerna", "block.minecraft.banner.triangle_top.brown": "Varnë neltil nuquerna", "block.minecraft.banner.triangle_top.cyan": "Fanyaluinë neltil nuquerna", "block.minecraft.banner.triangle_top.gray": "Sinda neltil nuquerna", "block.minecraft.banner.triangle_top.green": "Laica neltil nuquerna", "block.minecraft.banner.triangle_top.light_blue": "<PERSON>lwa neltil nuquerna", "block.minecraft.banner.triangle_top.light_gray": "Mísë neltil nuquerna", "block.minecraft.banner.triangle_top.lime": "Venya neltil nuquerna", "block.minecraft.banner.triangle_top.magenta": "Mangenta neltil nuquerna", "block.minecraft.banner.triangle_top.orange": "Culuina neltil nuquerna", "block.minecraft.banner.triangle_top.pink": "Fanyacarnë neltil nuquerna", "block.minecraft.banner.triangle_top.purple": "Luicarnë neltil nuquerna", "block.minecraft.banner.triangle_top.red": "Carnë neltil nuquerna", "block.minecraft.banner.triangle_top.white": "<PERSON>nq<PERSON>ë neltil nuquerna", "block.minecraft.banner.triangle_top.yellow": "<PERSON>na neltil nuquerna", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.gray": "Sinda carcara talma", "block.minecraft.banner.triangles_bottom.green": "<PERSON>ca carcara talma", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.magenta": "Mangenta carcara talma", "block.minecraft.banner.triangles_bottom.orange": "C<PERSON>ina carcara talma", "block.minecraft.banner.triangles_bottom.pink": "Fanyacarnë carcara talma", "block.minecraft.banner.triangles_bottom.purple": "Luicarnë carcara talma", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> carcara talma", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.cyan": "Fanyaluinë carcara cas", "block.minecraft.banner.triangles_top.gray": "Sinda carcara cas", "block.minecraft.banner.triangles_top.green": "Laica carcara cas", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.light_gray": "Mísë carcara cas", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON>ya carcara cas", "block.minecraft.banner.triangles_top.magenta": "Mangenta carcara cas", "block.minecraft.banner.triangles_top.orange": "Culuina carcara cas", "block.minecraft.banner.triangles_top.pink": "Fanyacarnë carcara cas", "block.minecraft.banner.triangles_top.purple": "Luicarnë carcara cas", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> carcara cas", "block.minecraft.banner.triangles_top.yellow": "<PERSON>na carcara cas", "block.minecraft.barrel": "Corcolca", "block.minecraft.barrier": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.basalt": "Tornondo", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "Héra <PERSON>htë", "block.minecraft.beacon.secondary": "Attëa me<PERSON>htë", "block.minecraft.bed.no_sleep": "<PERSON>rë cárima lan lómë hya vangwë ná rië", "block.minecraft.bed.not_safe": "Sí serië lá cárima lyen – úvanor arië nár", "block.minecraft.bed.obstructed": "Si caima tapina", "block.minecraft.bed.occupied": "Quén caitëa sina caimassë", "block.minecraft.bed.too_far_away": "Serë lá cárima lyen sí – i caima haiya langë", "block.minecraft.bedrock": "<PERSON><PERSON>", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON> ni<PERSON>on", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "Nyellë", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON> lip<PERSON>", "block.minecraft.big_dripleaf_stem": "Telco hö<PERSON>", "block.minecraft.birch_button": "<PERSON><PERSON>", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.birch_fence": "<PERSON><PERSON> h<PERSON>o", "block.minecraft.birch_fence_gate": "<PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "Ling<PERSON><PERSON> hwind<PERSON>o", "block.minecraft.birch_leaves": "<PERSON><PERSON>", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON> hwindëo", "block.minecraft.birch_sign": "<PERSON><PERSON>", "block.minecraft.birch_slab": "Perronwa hwindëo", "block.minecraft.birch_stairs": "<PERSON><PERSON> h<PERSON>", "block.minecraft.birch_trapdoor": "<PERSON><PERSON> h<PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna h<PERSON>", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON> tanna h<PERSON>o", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.black_candle_cake": "<PERSON><PERSON> as mor<PERSON>", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON> mulo cem<PERSON>do", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON><PERSON> cili<PERSON> cem<PERSON>", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON><PERSON> calca", "block.minecraft.black_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Perronwa moriondo", "block.minecraft.blackstone_stairs": "<PERSON><PERSON> moriondo", "block.minecraft.blackstone_wall": "Ramba moriondo", "block.minecraft.blast_furnace": "Ticutaitë urna", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON> as l<PERSON><PERSON> l<PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON> farma", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON> mulo cemerrondo", "block.minecraft.blue_glazed_terracotta": "Luinë cilintaina cem<PERSON>wa", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> he<PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> colca hyul<PERSON>o", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> cilin", "block.minecraft.blue_stained_glass_pane": "Luinë calca", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON> tó", "block.minecraft.bone_block": "<PERSON><PERSON> a<PERSON>", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON> solva", "block.minecraft.brain_coral_block": "Ronwa nandoina solvo", "block.minecraft.brain_coral_fan": "Quasil nandoina solvo", "block.minecraft.brain_coral_wall_fan": "Rambava quasil nandoina solvo", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Perronwa tesarion", "block.minecraft.brick_stairs": "Tyeller tesarion", "block.minecraft.brick_wall": "Ramba tesarion", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>n<PERSON> caima", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> as varn<PERSON> l<PERSON>", "block.minecraft.brown_carpet": "<PERSON>arn<PERSON> farma", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON>ar<PERSON><PERSON> mulo cemerrondo", "block.minecraft.brown_glazed_terracotta": "Varnë cilintaina cem<PERSON>wa", "block.minecraft.brown_mushroom": "Varnë telumbë", "block.minecraft.brown_mushroom_block": "Ronwa varnë telum<PERSON>ëo", "block.minecraft.brown_shulker_box": "Varnë col<PERSON> hyul<PERSON>o", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON><PERSON> cilin", "block.minecraft.brown_stained_glass_pane": "Varnë calca", "block.minecraft.brown_terracotta": "<PERSON><PERSON>në cem<PERSON>", "block.minecraft.brown_wool": "<PERSON>ar<PERSON><PERSON> tó", "block.minecraft.bubble_column": "Tarma velvion", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON> solva", "block.minecraft.bubble_coral_block": "Ronwa velvina solvo", "block.minecraft.bubble_coral_fan": "Quasil velvina solvo", "block.minecraft.bubble_coral_wall_fan": "Rambava quasil velvina solvo", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Lótë eccaldava", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Ninquisincë", "block.minecraft.calibrated_sculk_sensor": "Calibrated Sculk Sensor", "block.minecraft.campfire": "R<PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> as l<PERSON><PERSON><PERSON>", "block.minecraft.carrots": "Rassulcar", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Cirinwa culvelyávë", "block.minecraft.cauldron": "Tambin", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chain": "Limil", "block.minecraft.chain_command_block": "Limilëa ronwa axanion", "block.minecraft.cherry_button": "<PERSON><PERSON>", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "<PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON>", "block.minecraft.cherry_slab": "Perronwa aipialdo", "block.minecraft.cherry_stairs": "<PERSON><PERSON> a<PERSON>", "block.minecraft.cherry_trapdoor": "Lat a<PERSON>", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> linga<PERSON>na <PERSON>", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON> tanna a<PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.chest": "Taucolca", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON> r<PERSON> onin", "block.minecraft.chiseled_bookshelf": "Cantaina palusta", "block.minecraft.chiseled_copper": "Cantaina urus", "block.minecraft.chiseled_deepslate": "Cantaina cirondo", "block.minecraft.chiseled_nether_bricks": "Cantainë tesari Nethero", "block.minecraft.chiseled_polished_blackstone": "Cantaina runda moriondo", "block.minecraft.chiseled_quartz_block": "Cantaina ronwa silmo", "block.minecraft.chiseled_red_sandstone": "Cantaina carnë lits<PERSON>on", "block.minecraft.chiseled_resin_bricks": "Cantainë tesari suhtio", "block.minecraft.chiseled_sandstone": "Cantaina <PERSON>ë<PERSON>", "block.minecraft.chiseled_stone_bricks": "Cantainë ondotesari", "block.minecraft.chiseled_tuff": "Chiseled <PERSON>", "block.minecraft.chiseled_tuff_bricks": "Chiseled Tuff Bricks", "block.minecraft.chorus_flower": "N<PERSON>llot", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.clay": "C<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON>", "block.minecraft.coal_block": "<PERSON><PERSON> h<PERSON>", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON> hyu<PERSON>o", "block.minecraft.coarse_dirt": "<PERSON><PERSON> cemen", "block.minecraft.cobbled_deepslate": "Raccirondo", "block.minecraft.cobbled_deepslate_slab": "Perronwa raccirondo", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON> rac<PERSON>do", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> rac<PERSON>do", "block.minecraft.cobblestone": "Ra<PERSON>", "block.minecraft.cobblestone_slab": "Perronwa racondo", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON> racondo", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> racondo", "block.minecraft.cobweb": "Hlínë", "block.minecraft.cocoa": "Cacáva", "block.minecraft.command_block": "Ronwa axanion", "block.minecraft.comparator": "Redstone comparma", "block.minecraft.composter": "Composter", "block.minecraft.conduit": "Tulwë", "block.minecraft.copper_block": "<PERSON><PERSON>", "block.minecraft.copper_bulb": "Calma urusto", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "Natsë u<PERSON>o", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Lat urusto", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Has<PERSON><PERSON> tesari cirondo", "block.minecraft.cracked_deepslate_tiles": "Cracked Deepslate Tiles", "block.minecraft.cracked_nether_bricks": "Hastainë tesari Nethero", "block.minecraft.cracked_polished_blackstone_bricks": "Has<PERSON>ë tesari runda moriondo", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.crafter": "Intamma", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON> tami<PERSON>o", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "Cas Creeperwa", "block.minecraft.creeper_wall_head": "Rambava cas Creepero", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON> tolma", "block.minecraft.crimson_door": "<PERSON>ni<PERSON><PERSON> fend<PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON> hwan", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> lingatanna", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Carnitoinë panor", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> sang<PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON> tanna", "block.minecraft.crimson_slab": "Perronwa carnituo", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyeller", "block.minecraft.crimson_stem": "Carnë telco", "block.minecraft.crimson_trapdoor": "Carnitoina lat", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> rambava lingatanna", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON> carnitoina tanna", "block.minecraft.crying_obsidian": "Nyenya moricalca", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON> urus", "block.minecraft.cut_copper_slab": "Perronwa cirina u<PERSON>o", "block.minecraft.cut_copper_stairs": "Ty<PERSON> cirina <PERSON>o", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "Perronwa cirina carn<PERSON> litsëon", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "Perronwa cirina lit<PERSON>", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.cyan_bed": "Fanyaluinë caima", "block.minecraft.cyan_candle": "Fanyaluinë lí<PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_carpet": "Fanyaluin<PERSON> farma", "block.minecraft.cyan_concrete": "Fanyaluinë cem<PERSON>da", "block.minecraft.cyan_concrete_powder": "Fan<PERSON>uin<PERSON> mulo cemerrondo", "block.minecraft.cyan_glazed_terracotta": "Fanyaluinë cilintaina cem<PERSON>wa", "block.minecraft.cyan_shulker_box": "Fanyaluinë colca hyulcero", "block.minecraft.cyan_stained_glass": "Fanyaluinë cilin", "block.minecraft.cyan_stained_glass_pane": "Fanyaluinë calca", "block.minecraft.cyan_terracotta": "Fanyaluinë cem<PERSON>wa", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON> tó", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON> m<PERSON>", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_oak_fence": "Hahta morinorno", "block.minecraft.dark_oak_fence_gate": "<PERSON>o morinorno", "block.minecraft.dark_oak_hanging_sign": "Lingata<PERSON> morinorno", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON>", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_oak_sapling": "Nessornë morinorno", "block.minecraft.dark_oak_sign": "<PERSON><PERSON>", "block.minecraft.dark_oak_slab": "Perronwa morinorno", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_oak_trapdoor": "Lat morinorno", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna mori<PERSON>", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON> tanna morinorno", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON> mori<PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Perronwa morë <PERSON>", "block.minecraft.dark_prismarine_stairs": "Ty<PERSON> mor<PERSON>", "block.minecraft.daylight_detector": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral": "Hessa nandoina solva", "block.minecraft.dead_brain_coral_block": "Ronwa hessa nandoina solvo", "block.minecraft.dead_brain_coral_fan": "Quasil hessa nandoina solvo", "block.minecraft.dead_brain_coral_wall_fan": "Rambava quasil hessa nandoina solvo", "block.minecraft.dead_bubble_coral": "Hessa velvina solva", "block.minecraft.dead_bubble_coral_block": "Ronwa hessa velvina solvo", "block.minecraft.dead_bubble_coral_fan": "Quasil hessa velvina solvo", "block.minecraft.dead_bubble_coral_wall_fan": "Rambava quasil hessa velvina solvo", "block.minecraft.dead_bush": "Pa<PERSON> tussa", "block.minecraft.dead_fire_coral": "Hessa ruina solva", "block.minecraft.dead_fire_coral_block": "Ronwa hessa ruina solvo", "block.minecraft.dead_fire_coral_fan": "Quasil hessa ruina solvo", "block.minecraft.dead_fire_coral_wall_fan": "Rambava quasil hessa ruina solvo", "block.minecraft.dead_horn_coral": "<PERSON><PERSON> rass<PERSON>a solva", "block.minecraft.dead_horn_coral_block": "Ronwa hessa rassëa solvo", "block.minecraft.dead_horn_coral_fan": "Quasil hessa rassëa solvo", "block.minecraft.dead_horn_coral_wall_fan": "Rambava quasil hessa rassëa solvo", "block.minecraft.dead_tube_coral": "<PERSON><PERSON> rotsina solva", "block.minecraft.dead_tube_coral_block": "Ronwa hessa rotsina solvo", "block.minecraft.dead_tube_coral_fan": "Quasil hessa rotsina solvo", "block.minecraft.dead_tube_coral_wall_fan": "Rambava quasil hessa rotsina solvo", "block.minecraft.decorated_pot": "Fintanwa tambë", "block.minecraft.deepslate": "Cirondo", "block.minecraft.deepslate_brick_slab": "Perronwa tesarion cirondo", "block.minecraft.deepslate_brick_stairs": "Tyeller tesarion cirondo", "block.minecraft.deepslate_brick_wall": "Ramba tesarion cirondo", "block.minecraft.deepslate_bricks": "<PERSON><PERSON><PERSON> cirondo", "block.minecraft.deepslate_coal_ore": "Sin<PERSON>ë hyulmo cirondossë", "block.minecraft.deepslate_copper_ore": "Sincë urusto cirondossë", "block.minecraft.deepslate_diamond_ore": "Sin<PERSON>ë tinwírëo cirondossë", "block.minecraft.deepslate_emerald_ore": "Sincë laicarilo cirondossë", "block.minecraft.deepslate_gold_ore": "Sincë malto cirondossë", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON> ango cirondo<PERSON>", "block.minecraft.deepslate_lapis_ore": "Sincë luiniondo cirondossë", "block.minecraft.deepslate_redstone_ore": "Sin<PERSON>ë <PERSON>ëo cirondossë", "block.minecraft.deepslate_tile_slab": "Deepslate Tile Slab", "block.minecraft.deepslate_tile_stairs": "Deepslate Tile Stairs", "block.minecraft.deepslate_tile_wall": "Deepslate Tile Wall", "block.minecraft.deepslate_tiles": "Deepslate Tiles", "block.minecraft.detector_rail": "Tirítë angatië", "block.minecraft.diamond_block": "<PERSON><PERSON>", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite": "Pelondo", "block.minecraft.diorite_slab": "Perronwa pelondo", "block.minecraft.diorite_stairs": "<PERSON><PERSON> pelondo", "block.minecraft.diorite_wall": "Ramba pelondo", "block.minecraft.dirt": "Cemen", "block.minecraft.dirt_path": "Tië", "block.minecraft.dispenser": "Estatar", "block.minecraft.dragon_egg": "Ohtë hlóceva", "block.minecraft.dragon_head": "Cas hlócëo", "block.minecraft.dragon_wall_head": "Rambava cas hlócëo", "block.minecraft.dried_ghast": "Tarquina Ñasto", "block.minecraft.dried_kelp_block": "Ronwa parahtanwa <PERSON>o", "block.minecraft.dripstone_block": "<PERSON><PERSON>", "block.minecraft.dropper": "<PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "<PERSON><PERSON>", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON><PERSON> luh<PERSON>", "block.minecraft.end_gateway": "<PERSON> fendass<PERSON>", "block.minecraft.end_portal": "<PERSON> fendass<PERSON>", "block.minecraft.end_portal_frame": "Canta End fendassëo", "block.minecraft.end_rod": "<PERSON><PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Perronwa tesarion ondo Endo", "block.minecraft.end_stone_brick_stairs": "Tyeller tesarion ondo <PERSON>o", "block.minecraft.end_stone_brick_wall": "Ramba tesarion ondo Endo", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Parna cantaina urus", "block.minecraft.exposed_copper": "Parna urus", "block.minecraft.exposed_copper_bulb": "<PERSON>rna urusta calma", "block.minecraft.exposed_copper_door": "Parna urusta fendë", "block.minecraft.exposed_copper_grate": "Parna urusta natsë", "block.minecraft.exposed_copper_trapdoor": "Parna urusta lat", "block.minecraft.exposed_cut_copper": "Parna cirina urus", "block.minecraft.exposed_cut_copper_slab": "Perronwa parna cirina u<PERSON>o", "block.minecraft.exposed_cut_copper_stairs": "Tyeller parna cirina u<PERSON>o", "block.minecraft.farmland": "Resta", "block.minecraft.fern": "Filquë", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON> solva", "block.minecraft.fire_coral_block": "Ronwa ruina solvo", "block.minecraft.fire_coral_fan": "Quasil ruina solvo", "block.minecraft.fire_coral_wall_fan": "Rambava quasil ruina solvo", "block.minecraft.firefly_bush": "<PERSON><PERSON>", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON> pili<PERSON>", "block.minecraft.flower_pot": "Tambë", "block.minecraft.flowering_azalea": "Lótëa as<PERSON>los", "block.minecraft.flowering_azalea_leaves": "Lassi l<PERSON>ëa astalloswë", "block.minecraft.frogspawn": "<PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON> he<PERSON>", "block.minecraft.furnace": "Urna", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON> moriondo", "block.minecraft.glass": "Cilin", "block.minecraft.glass_pane": "Calca", "block.minecraft.glow_lichen": "Glow Lichen", "block.minecraft.glowstone": "Calondo", "block.minecraft.gold_block": "Ronwa malto", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON> malto", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON>ron<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON>", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "Salquë", "block.minecraft.grass_block": "<PERSON><PERSON> salqu<PERSON>", "block.minecraft.gravel": "Virittë", "block.minecraft.gray_banner": "<PERSON><PERSON> lantanna", "block.minecraft.gray_bed": "Sinda caima", "block.minecraft.gray_candle": "Sinda Lícuma", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> as sinda lí<PERSON>a", "block.minecraft.gray_carpet": "Sinda farma", "block.minecraft.gray_concrete": "Sinda cemerronda", "block.minecraft.gray_concrete_powder": "Sinda mulo cemerrondo", "block.minecraft.gray_glazed_terracotta": "Sinda cilintaina cem<PERSON>", "block.minecraft.gray_shulker_box": "Sinda colca hyulcero", "block.minecraft.gray_stained_glass": "Sinda cilin", "block.minecraft.gray_stained_glass_pane": "Sinda calca", "block.minecraft.gray_terracotta": "Sinda cemmastanwa", "block.minecraft.gray_wool": "Sinda tó", "block.minecraft.green_banner": "<PERSON><PERSON> la<PERSON>", "block.minecraft.green_bed": "Laica caima", "block.minecraft.green_candle": "Laica lí<PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON> as laica lícuma", "block.minecraft.green_carpet": "Laica farma", "block.minecraft.green_concrete": "Laica cem<PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON> mulo cemerrondo", "block.minecraft.green_glazed_terracotta": "Laica cilintaina cem<PERSON>", "block.minecraft.green_shulker_box": "Laica colca hyulcero", "block.minecraft.green_stained_glass": "Laica cilin", "block.minecraft.green_stained_glass_pane": "Laica calca", "block.minecraft.green_terracotta": "Lai<PERSON> c<PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON> tó", "block.minecraft.grindstone": "<PERSON><PERSON>", "block.minecraft.hanging_roots": "Lingainë sulcar", "block.minecraft.hay_block": "Sanga salquë", "block.minecraft.heavy_core": "Heavy Core", "block.minecraft.heavy_weighted_pressure_plate": "Sang<PERSON><PERSON> ango", "block.minecraft.honey_block": "<PERSON><PERSON>", "block.minecraft.honeycomb_block": "<PERSON><PERSON>", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Rassëa solva", "block.minecraft.horn_coral_block": "Ronwa rassëa solvo", "block.minecraft.horn_coral_fan": "Quasil rassëa solvo", "block.minecraft.horn_coral_wall_fan": "Rambava quasil rassëa solvo", "block.minecraft.ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Vembië cantainë ondotesari", "block.minecraft.infested_cobblestone": "Vembëa racon", "block.minecraft.infested_cracked_stone_bricks": "Vembië hastainë ondotesari", "block.minecraft.infested_deepslate": "Vembëa cirondo", "block.minecraft.infested_mossy_stone_bricks": "Vembië lómavë ondotesari", "block.minecraft.infested_stone": "Vembëa ondo", "block.minecraft.infested_stone_bricks": "Vembië ondotesari", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON> sami", "block.minecraft.iron_block": "<PERSON><PERSON> ango", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON> ango", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON> ango", "block.minecraft.iron_trapdoor": "Lat ango", "block.minecraft.jack_o_lantern": "<PERSON>ar culve<PERSON>", "block.minecraft.jigsaw": "<PERSON><PERSON>", "block.minecraft.jukebox": "Colca lindalëo", "block.minecraft.jungle_button": "<PERSON><PERSON> t<PERSON>a r<PERSON>", "block.minecraft.jungle_door": "Fendë töa rostaur<PERSON>o", "block.minecraft.jungle_fence": "<PERSON><PERSON> töa rostaur<PERSON>o", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON> t<PERSON>a rostau<PERSON>", "block.minecraft.jungle_hanging_sign": "Lingatanna töa rostau<PERSON>o", "block.minecraft.jungle_leaves": "<PERSON><PERSON> r<PERSON>au<PERSON>", "block.minecraft.jungle_log": "Pulco rostaur<PERSON>", "block.minecraft.jungle_planks": "<PERSON><PERSON> t<PERSON>a r<PERSON>au<PERSON>", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON><PERSON> töa rostau<PERSON>o", "block.minecraft.jungle_sapling": "Nessornë rostaurëo", "block.minecraft.jungle_sign": "<PERSON><PERSON> t<PERSON>a r<PERSON>", "block.minecraft.jungle_slab": "Perronwa töa rostaurëo", "block.minecraft.jungle_stairs": "<PERSON><PERSON> t<PERSON>a r<PERSON>", "block.minecraft.jungle_trapdoor": "Lat töa rostaur<PERSON>o", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna t<PERSON>a r<PERSON>", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON><PERSON> tanna töa r<PERSON>", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.kelp": "Eäruilë", "block.minecraft.kelp_plant": "Eäruilë", "block.minecraft.ladder": "<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Calma", "block.minecraft.lapis_block": "<PERSON><PERSON> luinion<PERSON>", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON> luiniondo", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON><PERSON> tuima <PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON> filqu<PERSON>", "block.minecraft.lava": "<PERSON><PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "block.minecraft.leaf_litter": "Lantanwa olassië", "block.minecraft.lectern": "Parmatarma", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "Cálë", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON> caima", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> as he<PERSON><PERSON> l<PERSON>", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON> farma", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> cem<PERSON>", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON> mulo cemerrondo", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON> cilintaina cem<PERSON>wa", "block.minecraft.light_blue_shulker_box": "Helwa colca hyulcero", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> cilin", "block.minecraft.light_blue_stained_glass_pane": "Helwa calca", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON> cem<PERSON>", "block.minecraft.light_blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON>ísë <PERSON>", "block.minecraft.light_gray_candle": "<PERSON>ísë <PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> as m<PERSON><PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON> farma", "block.minecraft.light_gray_concrete": "Mísë c<PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON> mulo cemerrondo", "block.minecraft.light_gray_glazed_terracotta": "Mísë cilintaina cem<PERSON>wa", "block.minecraft.light_gray_shulker_box": "Mísë colca hyulcero", "block.minecraft.light_gray_stained_glass": "<PERSON>ísë cilin", "block.minecraft.light_gray_stained_glass_pane": "Mísë Cal<PERSON>", "block.minecraft.light_gray_terracotta": "Mísë c<PERSON>", "block.minecraft.light_gray_wool": "Mísë tó", "block.minecraft.light_weighted_pressure_plate": "Sangapalma malto", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.lilac": "<PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "Indyellótë", "block.minecraft.lily_pad": "Indil", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON> caima", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> as venya lí<PERSON>a", "block.minecraft.lime_carpet": "<PERSON><PERSON>ya farma", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON> cem<PERSON>da", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON>ya mulo cemerrondo", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON>ya cilintaina cem<PERSON>wa", "block.minecraft.lime_shulker_box": "Venya colca hyulcero", "block.minecraft.lime_stained_glass": "<PERSON><PERSON>ya cilin", "block.minecraft.lime_stained_glass_pane": "Venya calca", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON> cem<PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON> tó", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Lanwa", "block.minecraft.magenta_banner": "<PERSON><PERSON>a lantanna", "block.minecraft.magenta_bed": "Mangenta caima", "block.minecraft.magenta_candle": "Mangent<PERSON>ícum<PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> as mangenta lí<PERSON>a", "block.minecraft.magenta_carpet": "Mangenta farma", "block.minecraft.magenta_concrete": "Mangenta cemerronda", "block.minecraft.magenta_concrete_powder": "Mangenta mulo cemerrondo", "block.minecraft.magenta_glazed_terracotta": "Mangenta cilintaina cem<PERSON>wa", "block.minecraft.magenta_shulker_box": "Mangenta colca hyulcero", "block.minecraft.magenta_stained_glass": "Mangenta cilin", "block.minecraft.magenta_stained_glass_pane": "Mangenta calca", "block.minecraft.magenta_terracotta": "<PERSON>genta cem<PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON> tó", "block.minecraft.magma_block": "Tiquondo", "block.minecraft.mangrove_button": "<PERSON><PERSON>", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence": "<PERSON><PERSON> ne<PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON>", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_leaves": "<PERSON><PERSON>", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_planks": "<PERSON><PERSON>", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "<PERSON><PERSON>", "block.minecraft.mangrove_sign": "<PERSON><PERSON>", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_stairs": "<PERSON><PERSON>", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON> ne<PERSON><PERSON>", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna <PERSON>", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON><PERSON> tanna ne<PERSON>do", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON> h<PERSON>o tuima <PERSON>", "block.minecraft.melon": "Laivelyávë", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "<PERSON><PERSON>", "block.minecraft.moss_carpet": "Farma lómo", "block.minecraft.mossy_cobblestone": "Lómava racon", "block.minecraft.mossy_cobblestone_slab": "Perronwa lómava racondo", "block.minecraft.mossy_cobblestone_stairs": "Lómavë tyeller racondo", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON> ramba racondo", "block.minecraft.mossy_stone_brick_slab": "Lómava perronwa ondotesarion", "block.minecraft.mossy_stone_brick_stairs": "Lómavë tyeller ondotesarion", "block.minecraft.mossy_stone_brick_wall": "Lómava ramba ondotesarion", "block.minecraft.mossy_stone_bricks": "Lómavë ondotesari", "block.minecraft.moving_piston": "Moving <PERSON>ston", "block.minecraft.mud": "Loxo", "block.minecraft.mud_brick_slab": "Perronwa tesarion loxo", "block.minecraft.mud_brick_stairs": "Tyeller tesarion loxo", "block.minecraft.mud_brick_wall": "Ramba tesarion loxo", "block.minecraft.mud_bricks": "<PERSON><PERSON> lo<PERSON>o", "block.minecraft.muddy_mangrove_roots": "Loxoinë sulcar ne<PERSON>do", "block.minecraft.mushroom_stem": "Telco telumbëo", "block.minecraft.mycelium": "Telumnatsë", "block.minecraft.nether_brick_fence": "Hahta tesarion Nethero", "block.minecraft.nether_brick_slab": "Perronwa tesarion Nethero", "block.minecraft.nether_brick_stairs": "Tyeller tesarion Nethero", "block.minecraft.nether_brick_wall": "Ramba tesarion Nethero", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_gold_ore": "Sincë malto Nethero", "block.minecraft.nether_portal": "<PERSON><PERSON> fend<PERSON>", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON> silmo", "block.minecraft.nether_sprouts": "Tuimar Nethero", "block.minecraft.nether_wart": "Sirpë Nethero", "block.minecraft.nether_wart_block": "<PERSON><PERSON>", "block.minecraft.netherite_block": "<PERSON><PERSON>", "block.minecraft.netherrack": "Nether-ondo", "block.minecraft.note_block": "Lincolca", "block.minecraft.oak_button": "<PERSON><PERSON>", "block.minecraft.oak_door": "Fendë <PERSON>no", "block.minecraft.oak_fence": "<PERSON><PERSON> norno", "block.minecraft.oak_fence_gate": "<PERSON><PERSON> norno", "block.minecraft.oak_hanging_sign": "Lingatanna norno", "block.minecraft.oak_leaves": "<PERSON><PERSON>", "block.minecraft.oak_log": "Pulco norno", "block.minecraft.oak_planks": "<PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_sapling": "Nessornë norno", "block.minecraft.oak_sign": "<PERSON><PERSON>", "block.minecraft.oak_slab": "Perronwa norno", "block.minecraft.oak_stairs": "<PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Lat norno", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna norno", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON> tanna norno", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Tirronwa", "block.minecraft.obsidian": "Moricalca", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON> lantanna", "block.minecraft.orange_bed": "Culuina caima", "block.minecraft.orange_candle": "Culuina Lícuma", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> as culuina lí<PERSON>a", "block.minecraft.orange_carpet": "Culuina farma", "block.minecraft.orange_concrete": "C<PERSON>ina cem<PERSON>da", "block.minecraft.orange_concrete_powder": "Culuina mulo cemerrondo", "block.minecraft.orange_glazed_terracotta": "Culuina cilintaina cem<PERSON>wa", "block.minecraft.orange_shulker_box": "Culuina colca hyulcero", "block.minecraft.orange_stained_glass": "Culuina cilin", "block.minecraft.orange_stained_glass_pane": "Culuina calca", "block.minecraft.orange_terracotta": "C<PERSON>ina cem<PERSON>", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> car<PERSON>", "block.minecraft.orange_wool": "Culuina tó", "block.minecraft.oxeye_daisy": "Lairemírë", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> cantaina urus", "block.minecraft.oxidized_copper": "Hómat<PERSON> urus", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>a calma", "block.minecraft.oxidized_copper_door": "Hómatina urusta fendë", "block.minecraft.oxidized_copper_grate": "Hómatina urusta natsë", "block.minecraft.oxidized_copper_trapdoor": "Hómatina u<PERSON>a lat", "block.minecraft.oxidized_cut_copper": "<PERSON>ó<PERSON><PERSON> cirina urus", "block.minecraft.oxidized_cut_copper_slab": "Perronwa hómatina cirina <PERSON>o", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON> hómatina cirina u<PERSON>o", "block.minecraft.packed_ice": "<PERSON>a helce", "block.minecraft.packed_mud": "Sanga loxo", "block.minecraft.pale_hanging_moss": "Lingaitë néca-lóma", "block.minecraft.pale_moss_block": "Ronwa néca-lómo", "block.minecraft.pale_moss_carpet": "Farma néca-lómo", "block.minecraft.pale_oak_button": "<PERSON><PERSON> n<PERSON>", "block.minecraft.pale_oak_door": "Fendë néca-norno", "block.minecraft.pale_oak_fence": "Hahta néca-norno", "block.minecraft.pale_oak_fence_gate": "Ando néca-norno", "block.minecraft.pale_oak_hanging_sign": "Lingatanna néca-norno", "block.minecraft.pale_oak_leaves": "Lassi néca-nornovë", "block.minecraft.pale_oak_log": "Pulco néca-norno", "block.minecraft.pale_oak_planks": "Panor néca-norno", "block.minecraft.pale_oak_pressure_plate": "Sangapalma néca-norno", "block.minecraft.pale_oak_sapling": "Nessornë néca-norno", "block.minecraft.pale_oak_sign": "Tanna néca-norno", "block.minecraft.pale_oak_slab": "Perronwa néca-norno", "block.minecraft.pale_oak_stairs": "Tyeller néca-norno", "block.minecraft.pale_oak_trapdoor": "Lat néca-norno", "block.minecraft.pale_oak_wall_hanging_sign": "Ram<PERSON>va lingatanna n<PERSON>-norno", "block.minecraft.pale_oak_wall_sign": "Ram<PERSON>va tanna néca-norno", "block.minecraft.pale_oak_wood": "Töa néca-norno", "block.minecraft.pearlescent_froglight": "Luicarnë quácalma", "block.minecraft.peony": "<PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Sarda perronwa norno", "block.minecraft.piglin_head": "Cas pingilino", "block.minecraft.piglin_wall_head": "Rambava cas pingilino", "block.minecraft.pink_banner": "Fanyacarnë lantanna", "block.minecraft.pink_bed": "Fanyacarnë caima", "block.minecraft.pink_candle": "Fanyacarnë Lícuma", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> lí<PERSON>", "block.minecraft.pink_carpet": "Fanyacarnë farma", "block.minecraft.pink_concrete": "Fanyacarnë cemerronda", "block.minecraft.pink_concrete_powder": "Fanyacarnë mulo cemerrondo", "block.minecraft.pink_glazed_terracotta": "Fanyacarnë cilintaina cem<PERSON>wa", "block.minecraft.pink_petals": "Fanyacarnë tuilassë", "block.minecraft.pink_shulker_box": "Fanyacarnë colca hyulcero", "block.minecraft.pink_stained_glass": "Fanyacarnë cilin", "block.minecraft.pink_stained_glass_pane": "Fanyacarnë calca", "block.minecraft.pink_terracotta": "Fanyacarnë cemmastanwa", "block.minecraft.pink_tulip": "Fanyacarnë carpalos", "block.minecraft.pink_wool": "Fanyacarnë tó", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Cas nirmo", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "Pitcher Plant", "block.minecraft.player_head": "Cas tyalindo", "block.minecraft.player_head.named": "Cas o %s", "block.minecraft.player_wall_head": "Rambava cas tyalindo", "block.minecraft.podzol": "Pencemen", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "Runda andëondo", "block.minecraft.polished_andesite_slab": "Perronwa runda andëondo", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON> runda andë<PERSON><PERSON>", "block.minecraft.polished_basalt": "<PERSON><PERSON> torn<PERSON>o", "block.minecraft.polished_blackstone": "Runda moriondo", "block.minecraft.polished_blackstone_brick_slab": "Perronwa tesarion runda moriondo", "block.minecraft.polished_blackstone_brick_stairs": "Tyeller tesarion runda moriondo", "block.minecraft.polished_blackstone_brick_wall": "Ramba tesarion runda moriondo", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON> runda moriondo", "block.minecraft.polished_blackstone_button": "To<PERSON> runda moriondo", "block.minecraft.polished_blackstone_pressure_plate": "Sangapalma runda moriondo", "block.minecraft.polished_blackstone_slab": "Perronwa runda moriondo", "block.minecraft.polished_blackstone_stairs": "Tyeller runda moriondo", "block.minecraft.polished_blackstone_wall": "Ramba runda moriondo", "block.minecraft.polished_deepslate": "<PERSON><PERSON> cirondo", "block.minecraft.polished_deepslate_slab": "Perronwa runda cirondo", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON> runda cirondo", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> runda cirondo", "block.minecraft.polished_diorite": "Runda pelondo", "block.minecraft.polished_diorite_slab": "Perronwa runda pelondo", "block.minecraft.polished_diorite_stairs": "Ty<PERSON> runda pelondo", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "<PERSON>ron<PERSON> runda nellondo", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON> runda ne<PERSON>do", "block.minecraft.polished_tuff": "Polished <PERSON>", "block.minecraft.polished_tuff_slab": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "Polished <PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Nessornë nísimaldo tambissë", "block.minecraft.potted_allium": "Potted Allium", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_azure_bluet": "Potted Azure Bluet", "block.minecraft.potted_bamboo": "Vambu tambissë", "block.minecraft.potted_birch_sapling": "Nessornë hwindëo tambissë", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON><PERSON><PERSON> tamb<PERSON>", "block.minecraft.potted_brown_mushroom": "Varnë telumbë tambissë", "block.minecraft.potted_cactus": "<PERSON><PERSON>lda tambissë", "block.minecraft.potted_cherry_sapling": "Nessornë a<PERSON>ialdo tambissë", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON> hellótë tambissë", "block.minecraft.potted_cornflower": "<PERSON><PERSON><PERSON> ta<PERSON>", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON><PERSON> hwan tambissë", "block.minecraft.potted_crimson_roots": "Carni sulcar tamb<PERSON>", "block.minecraft.potted_dandelion": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_dark_oak_sapling": "Nessornë morinorno tambissë", "block.minecraft.potted_dead_bush": "Pa<PERSON> tussa tamb<PERSON>", "block.minecraft.potted_fern": "Filquë tambissë", "block.minecraft.potted_flowering_azalea_bush": "Lótëa astallos tamb<PERSON>", "block.minecraft.potted_jungle_sapling": "Nessornë rostaurëo tamb<PERSON>ë", "block.minecraft.potted_lily_of_the_valley": "Indyellótë tambissë", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON><PERSON> ne<PERSON> ta<PERSON>", "block.minecraft.potted_oak_sapling": "Nessornë norno tambissë", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON> tamb<PERSON>", "block.minecraft.potted_orange_tulip": "Culuina carpalos tambissë", "block.minecraft.potted_oxeye_daisy": "Lairemírë tambissë", "block.minecraft.potted_pale_oak_sapling": "Nessornë néca-norno tambissë", "block.minecraft.potted_pink_tulip": "Fanyacarnë carpalos tambissë", "block.minecraft.potted_poppy": "Lórelot tambissë", "block.minecraft.potted_red_mushroom": "Carnë telumbë tambissë", "block.minecraft.potted_red_tulip": "Carnë carpalos tambissë", "block.minecraft.potted_spruce_sapling": "Nessornë súcë tambissë", "block.minecraft.potted_torchflower": "Nallótë tambissë", "block.minecraft.potted_warped_fungus": "<PERSON><PERSON><PERSON> hwan tamb<PERSON>", "block.minecraft.potted_warped_roots": "Hwindë sulcar tambissë", "block.minecraft.potted_white_tulip": "<PERSON>nq<PERSON>ë carpalos tambissë", "block.minecraft.potted_wither_rose": "<PERSON>er <PERSON><PERSON> tamb<PERSON>", "block.minecraft.powder_snow": "<PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON> as mulo <PERSON><PERSON><PERSON>", "block.minecraft.powered_rail": "Ítacelmëa angatië", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Perronwa tesarion Prissimarino", "block.minecraft.prismarine_brick_stairs": "Tyeller tesar<PERSON>", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "Perronwa Prissimarino", "block.minecraft.prismarine_stairs": "<PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON>", "block.minecraft.pumpkin": "Culvelyávë", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON> culve<PERSON>", "block.minecraft.purple_banner": "Luicarnë lantanna", "block.minecraft.purple_bed": "Luicarnë caima", "block.minecraft.purple_candle": "Luicarnë lícuma", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> as l<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.purple_carpet": "Luicarnë farma", "block.minecraft.purple_concrete": "Luicarnë cemerronda", "block.minecraft.purple_concrete_powder": "Luicarnë mulo cemerrondo", "block.minecraft.purple_glazed_terracotta": "Luicarnë cilintaina cem<PERSON>wa", "block.minecraft.purple_shulker_box": "Luicarnë colca hyulcero", "block.minecraft.purple_stained_glass": "Luicarnë cilin", "block.minecraft.purple_stained_glass_pane": "Luicarnë calca", "block.minecraft.purple_terracotta": "Luicarnë cemmastanwa", "block.minecraft.purple_wool": "Luicarnë tó", "block.minecraft.purpur_block": "Ronwa Purpuro", "block.minecraft.purpur_pillar": "<PERSON>rma <PERSON>", "block.minecraft.purpur_slab": "Perronwa Purpuro", "block.minecraft.purpur_stairs": "Tyeller <PERSON>", "block.minecraft.quartz_block": "<PERSON><PERSON> silmo", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON> si<PERSON>o", "block.minecraft.quartz_pillar": "<PERSON><PERSON> silmo", "block.minecraft.quartz_slab": "Perronwa silmo", "block.minecraft.quartz_stairs": "<PERSON><PERSON> silmo", "block.minecraft.rail": "Angatië", "block.minecraft.raw_copper_block": "Ronwa altamna urusto", "block.minecraft.raw_gold_block": "Ronwa altamna malto", "block.minecraft.raw_iron_block": "Ronwa al<PERSON>' ango", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> farma", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON> mulo cemerrondo", "block.minecraft.red_glazed_terracotta": "Carnë cilintaina cem<PERSON>wa", "block.minecraft.red_mushroom": "Carnë telumbë", "block.minecraft.red_mushroom_block": "<PERSON><PERSON> carn<PERSON> telum<PERSON>o", "block.minecraft.red_nether_brick_slab": "Perronwa carni tesarion Nethero", "block.minecraft.red_nether_brick_stairs": "Tyeller carni tesarion Nethero", "block.minecraft.red_nether_brick_wall": "Ramba carni tesarion Nethero", "block.minecraft.red_nether_bricks": "Carni tesari Nethero", "block.minecraft.red_sand": "Carnë litsë", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Perronwa carnë litsëon", "block.minecraft.red_sandstone_stairs": "Ty<PERSON> carn<PERSON> lit<PERSON>", "block.minecraft.red_sandstone_wall": "Ramba carnë litsëondo", "block.minecraft.red_shulker_box": "Carnë colca hyulcero", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> cilin", "block.minecraft.red_stained_glass_pane": "Carnë calca", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON> cem<PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> car<PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> tó", "block.minecraft.redstone_block": "<PERSON><PERSON>", "block.minecraft.redstone_lamp": "Calma Redstonëo", "block.minecraft.redstone_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.redstone_torch": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.redstone_wall_torch": "Rambava narrundo <PERSON>o", "block.minecraft.redstone_wire": "Redstone nurda", "block.minecraft.reinforced_deepslate": "Turina cirondo", "block.minecraft.repeater": "Encarilma <PERSON>", "block.minecraft.repeating_command_block": "<PERSON><PERSON><PERSON> ronwa axanion", "block.minecraft.resin_block": "<PERSON><PERSON>", "block.minecraft.resin_brick_slab": "Perronwa tesarion suhtio", "block.minecraft.resin_brick_stairs": "Tyeller tesarion su<PERSON>io", "block.minecraft.resin_brick_wall": "Ramba tesarion suhtio", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "<PERSON><PERSON> su<PERSON>io", "block.minecraft.respawn_anchor": "Ampa en<PERSON>", "block.minecraft.rooted_dirt": "<PERSON><PERSON> as sulcar", "block.minecraft.rose_bush": "Tussa merillion", "block.minecraft.sand": "Litsë", "block.minecraft.sandstone": "Litsëon", "block.minecraft.sandstone_slab": "Perronwa litsëon", "block.minecraft.sandstone_stairs": "Tyeller litsëondo", "block.minecraft.sandstone_wall": "Ramba litsëondo", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Esculco", "block.minecraft.sculk_catalyst": "Hausta esculco", "block.minecraft.sculk_sensor": "Esculcova tuvima", "block.minecraft.sculk_shrieker": "<PERSON><PERSON>", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rotsë", "block.minecraft.sea_lantern": "Calma ëarwa", "block.minecraft.sea_pickle": "Tarquolos<PERSON>", "block.minecraft.seagrass": "Sal<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Nómë enontiëo tulcina", "block.minecraft.short_dry_grass": "<PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "Calatelumbë", "block.minecraft.shulker_box": "Colca hyulcero", "block.minecraft.skeleton_skull": "Coropë axoqueno", "block.minecraft.skeleton_wall_skull": "Rambava coropë axoqueno", "block.minecraft.slime_block": "<PERSON><PERSON> maxo", "block.minecraft.small_amethyst_bud": "Níca tuima <PERSON>", "block.minecraft.small_dripleaf": "Níca <PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Pasta tornondo", "block.minecraft.smooth_quartz": "Ronwa pasta silmo", "block.minecraft.smooth_quartz_slab": "Perronwa pasta silmo", "block.minecraft.smooth_quartz_stairs": "Tyeller pasta silmo", "block.minecraft.smooth_red_sandstone": "Pasta carnë litsëon", "block.minecraft.smooth_red_sandstone_slab": "Perronwa pasta carnë litsëondo", "block.minecraft.smooth_red_sandstone_stairs": "Tyeller pasta carnë litsëondo", "block.minecraft.smooth_sandstone": "Pasta litsëon", "block.minecraft.smooth_sandstone_slab": "Perronwa pasta litsëondo", "block.minecraft.smooth_sandstone_stairs": "Tyeller pasta litsëondo", "block.minecraft.smooth_stone": "Pasta ondo", "block.minecraft.smooth_stone_slab": "Perronwa pasta ondo", "block.minecraft.sniffer_egg": "Ohtë nustarwa", "block.minecraft.snow": "Lossë", "block.minecraft.snow_block": "<PERSON><PERSON>", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Calma fëaron", "block.minecraft.soul_sand": "Fëalitsë", "block.minecraft.soul_soil": "Fëacemen", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "Rambava narrundo fëaron", "block.minecraft.spawn.not_valid": "You have no home bed or charged respawn anchor, or it was obstructed", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Interact with Spawn Egg:", "block.minecraft.spawner.desc2": "Sets Mob Type", "block.minecraft.sponge": "<PERSON><PERSON>", "block.minecraft.spore_blossom": "Rellótë", "block.minecraft.spruce_button": "<PERSON><PERSON>", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.spruce_fence": "<PERSON><PERSON> sú<PERSON>ëo", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "Lingatanna súcëo", "block.minecraft.spruce_leaves": "<PERSON><PERSON>", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sapling": "Nessornë sú<PERSON>ëo", "block.minecraft.spruce_sign": "<PERSON><PERSON>", "block.minecraft.spruce_slab": "Perronwa súcëo", "block.minecraft.spruce_stairs": "<PERSON><PERSON> s<PERSON>", "block.minecraft.spruce_trapdoor": "Lat s<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> lingatanna <PERSON>", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON> tanna s<PERSON>", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON> nirma", "block.minecraft.stone": "Ondo", "block.minecraft.stone_brick_slab": "Perronwa ondotesarion", "block.minecraft.stone_brick_stairs": "Tyeller ondotesarion", "block.minecraft.stone_brick_wall": "Ramba ondotesarion", "block.minecraft.stone_bricks": "Ondotesari", "block.minecraft.stone_button": "<PERSON><PERSON> ondo", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON> ondo", "block.minecraft.stone_slab": "Perronwa ondo", "block.minecraft.stone_stairs": "<PERSON><PERSON> ondo", "block.minecraft.stonecutter": "On<PERSON>ci<PERSON>", "block.minecraft.stripped_acacia_log": "Heltina pulco nís<PERSON>ldo", "block.minecraft.stripped_acacia_wood": "Heltina töa nísimaldava", "block.minecraft.stripped_bamboo_block": "Ronwa heltina vambuo", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> pulco h<PERSON>o", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON> töa h<PERSON>o", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON> pulco a<PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON> t<PERSON>a a<PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> carnë telco", "block.minecraft.stripped_dark_oak_log": "Heltina pulco morinorno", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON> töa mori<PERSON>no", "block.minecraft.stripped_jungle_log": "Heltina pulco rostau<PERSON>o", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON> töa rostau<PERSON>o", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON> pulco ne<PERSON>do", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON> t<PERSON>a ne<PERSON>", "block.minecraft.stripped_oak_log": "Heltina pulco norno", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON> töa norno", "block.minecraft.stripped_pale_oak_log": "Heltina pulco néca-norno", "block.minecraft.stripped_pale_oak_wood": "Heltina töa néca-norno", "block.minecraft.stripped_spruce_log": "Heltina pulco s<PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON>lt<PERSON> töa s<PERSON>o", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON> hwinda hwant<PERSON>a", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON> hwinda telco", "block.minecraft.structure_block": "<PERSON><PERSON> carmion", "block.minecraft.structure_void": "Structure Void", "block.minecraft.sugar_cane": "Lisseliscë", "block.minecraft.sunflower": "Anarilótë", "block.minecraft.suspicious_gravel": "Suspicious Gravel", "block.minecraft.suspicious_sand": "Lasinwa litsë", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>", "block.minecraft.tall_dry_grass": "<PERSON>a sara", "block.minecraft.tall_grass": "<PERSON><PERSON> salquë", "block.minecraft.tall_seagrass": "Halla salqu<PERSON>", "block.minecraft.target": "Mehtë", "block.minecraft.terracotta": "Cemmastanwa", "block.minecraft.test_block": "<PERSON><PERSON>", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "Quilëa cilin", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "<PERSON><PERSON><PERSON> l<PERSON>anwë", "block.minecraft.torch": "Narrund<PERSON>", "block.minecraft.torchflower": "Nallótë", "block.minecraft.torchflower_crop": "Nallótë", "block.minecraft.trapped_chest": "Cuptaila taucolca", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "Remma", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON> solva", "block.minecraft.tube_coral_block": "Ron<PERSON> rotsina solvo", "block.minecraft.tube_coral_fan": "Quasil rotsina solvo", "block.minecraft.tube_coral_wall_fan": "Rambava quasil rotsina solvo", "block.minecraft.tuff": "Littondo", "block.minecraft.tuff_brick_slab": "Tuff Brick Slab", "block.minecraft.tuff_brick_stairs": "Tuff Brick Stairs", "block.minecraft.tuff_brick_wall": "Tuff Brick Wall", "block.minecraft.tuff_bricks": "<PERSON>ff Bricks", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON> St<PERSON>s", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "Locuiler", "block.minecraft.twisting_vines_plant": "Locuiler", "block.minecraft.vault": "<PERSON><PERSON>", "block.minecraft.verdant_froglight": "Lai<PERSON> q<PERSON>cal<PERSON>", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Cúmavista", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON> narrundo", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON> tolma", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON> fend<PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON> ha<PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON> and<PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON> hwan", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> linga<PERSON>na", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON> panor", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_roots": "Hwindë sulcar", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> tanna", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> tyeller", "block.minecraft.warped_stem": "Hwinda telco", "block.minecraft.warped_trapdoor": "Hwinda lat", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> rambava lingatanna", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> hwinda tanna", "block.minecraft.warped_wart_block": "<PERSON><PERSON> hwind<PERSON> sir<PERSON>", "block.minecraft.water": "Nén", "block.minecraft.water_cauldron": "<PERSON><PERSON> as n<PERSON>", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> cantaina urus", "block.minecraft.waxed_copper_block": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>wa <PERSON>", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>a calma", "block.minecraft.waxed_copper_door": "<PERSON>í<PERSON>na u<PERSON>a fend<PERSON>", "block.minecraft.waxed_copper_grate": "Lícuna urusta natsë", "block.minecraft.waxed_copper_trapdoor": "Lícuna u<PERSON>a lat", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON>na cirina urus", "block.minecraft.waxed_cut_copper_slab": "Perronwa lícuna cirina u<PERSON>o", "block.minecraft.waxed_cut_copper_stairs": "Tyeller lícuna cirina u<PERSON>o", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> parna cantaina urus", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON><PERSON> parna urus", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> parna urusta calma", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON> parna urusta fend<PERSON>", "block.minecraft.waxed_exposed_copper_grate": "Lí<PERSON>na parna u<PERSON>a natsë", "block.minecraft.waxed_exposed_copper_trapdoor": "Lícuna parna urusta lat", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON><PERSON> parna cirina urus", "block.minecraft.waxed_exposed_cut_copper_slab": "Perronwa lícuna parna cirina u<PERSON>o", "block.minecraft.waxed_exposed_cut_copper_stairs": "Tyeller lícuna parna cirina u<PERSON>o", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> hómatina cantaina urus", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> hó<PERSON> urus", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> hó<PERSON> u<PERSON>a calma", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON>na hómat<PERSON> urusta fendë", "block.minecraft.waxed_oxidized_copper_grate": "Lícuna hómat<PERSON> urusta natsë", "block.minecraft.waxed_oxidized_copper_trapdoor": "Lícuna hómatina u<PERSON>a lat", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON><PERSON> hómatina cirina urus", "block.minecraft.waxed_oxidized_cut_copper_slab": "Perronwa lícuna hómatina cirina u<PERSON>o", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Tyeller lícuna hómatina cirina u<PERSON>o", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> nwarina cantaina urus", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>na urus", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>na u<PERSON>a calma", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>na u<PERSON>a fend<PERSON>", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> u<PERSON> natsë", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>na u<PERSON>a lat", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON> nwarina cirina urus", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> lícuna nwarina cirina <PERSON>o", "block.minecraft.waxed_weathered_cut_copper_stairs": "Tyeller lí<PERSON>na n<PERSON>na cirina u<PERSON>o", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON> cantaina urus", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON> u<PERSON>", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON> calma", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON> u<PERSON>a fend<PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON>na urust<PERSON> natsë", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON> lat", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON> cirina urus", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> n<PERSON>na cirina <PERSON>o", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON> n<PERSON>na cirina u<PERSON>o", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON> <PERSON>wan", "block.minecraft.wheat": "Massalquë", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> caima", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON> as ninqu<PERSON> lí<PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> farma", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> cem<PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> mulo cemerrondo", "block.minecraft.white_glazed_terracotta": "Ninquë cilintaina cem<PERSON>wa", "block.minecraft.white_shulker_box": "<PERSON>nq<PERSON>ë colca hyulcero", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> cilin", "block.minecraft.white_stained_glass_pane": "Ninquë calca", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> cem<PERSON>wa", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON><PERSON><PERSON> carpalos", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> tó", "block.minecraft.wildflowers": "<PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Coropë Wither-axoqueno", "block.minecraft.wither_skeleton_wall_skull": "Rambava coropë Wither-axoqueno", "block.minecraft.yellow_banner": "<PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> as ma<PERSON> l<PERSON>", "block.minecraft.yellow_carpet": "<PERSON><PERSON> farma", "block.minecraft.yellow_concrete": "<PERSON><PERSON> c<PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON>na mulo cemerrondo", "block.minecraft.yellow_glazed_terracotta": "<PERSON>na cilintaina cem<PERSON>", "block.minecraft.yellow_shulker_box": "Malina colca hyulcero", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON> cilin", "block.minecraft.yellow_stained_glass_pane": "Malina calca", "block.minecraft.yellow_terracotta": "<PERSON><PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON> t<PERSON>", "block.minecraft.zombie_head": "Cas urco", "block.minecraft.zombie_wall_head": "Rambava cas urco", "book.byAuthor": "ló %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Tecë essë parmava:", "book.finalizeButton": "Tehta ar sacë", "book.finalizeWarning": "Ela! I parma lauva vistaima yá tehtuval<PERSON>.", "book.generation.0": "<PERSON><PERSON><PERSON>", "book.generation.1": "<PERSON><PERSON> quantemma", "book.generation.2": "Quantemma quantemmo", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* tag parmo lá mára *", "book.pageIndicator": "Lassë %1$s o %2$s", "book.page_button.next": "<PERSON><PERSON> las<PERSON>ë", "book.page_button.previous": "<PERSON><PERSON><PERSON>", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "<PERSON><PERSON>", "book.view.title": "Book View Screen", "build.tooHigh": "Pelma táriëo carastalëo: %s", "chat.cannotSend": "Tecë nyatilessë lacárima", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Appa <PERSON> palarrúma", "chat.copy": "Encarë <PERSON>", "chat.copy.click": "<PERSON><PERSON><PERSON> meter renë", "chat.deleted_marker": "Sina menta haitanwa ló i server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Nyatil lá lavina cilmissen i fëo. Á ennirë '%s' meter henta ambë.", "chat.disabled.profile.moreInfo": "Nyatil lá cavina cilmissen i fëo. Mentië yo apantië mentaron lacárima.", "chat.editBox": "n<PERSON><PERSON>", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": "Ma nalyë tanca i merilyë latya sina natsemen?", "chat.link.confirmTrusted": "Ma merilyë latya sina limë hya renitas?", "chat.link.open": "Open in Browser", "chat.link.warning": "<PERSON>va latya limi a<PERSON>llon!", "chat.queue": "[+%s menta(r)]", "chat.square_brackets": "[%s]", "chat.tag.error": "I server mentanë menta i lá mára.", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Menta servero.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s aquacárië i veryalë %s", "chat.type.advancement.goal": "%s ánië i menesta %s", "chat.type.advancement.task": "%s acárië empatyellë: %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Tecë yonávenna", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s quetë %s", "chat.validation_error": "<PERSON><PERSON> n<PERSON>", "chat_screen.message": "Message to send: %s", "chat_screen.title": "<PERSON><PERSON><PERSON> n<PERSON>", "chat_screen.usage": "Tecë menta ar nirë Enter meter mentaitas", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Low disk space!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "No items were found on %s players", "clear.failed.single": "Lana nat hirina t<PERSON>ë %s", "color.minecraft.black": "Morë", "color.minecraft.blue": "Luinë", "color.minecraft.brown": "Varnë", "color.minecraft.cyan": "Fanyaluinë", "color.minecraft.gray": "Sinda", "color.minecraft.green": "Laica", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "Mísë", "color.minecraft.lime": "Venya", "color.minecraft.magenta": "Mangent<PERSON>", "color.minecraft.orange": "Culuina", "color.minecraft.pink": "Fanyacarnë", "color.minecraft.purple": "Luicarnë", "color.minecraft.red": "Carnë", "color.minecraft.white": "Ninquë", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[SINOMË]", "command.context.parse_error": "%s tanomë, epë %s: %s", "command.exception": "Could not parse command: %s", "command.expected.separator": "Expected whitespace to end one argument, but found trailing data", "command.failed": "An unexpected error occurred trying to execute that command", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "Incorrect argument for command", "command.unknown.command": "Unknown or incomplete command, see below for error", "commands.advancement.criterionNotFound": "The advancement %1$s does not contain the criterion '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Couldn't grant criterion '%s' of advancement %s to %s players as they already have it", "commands.advancement.grant.criterion.to.many.success": "Granted criterion '%s' of advancement %s to %s players", "commands.advancement.grant.criterion.to.one.failure": "Couldn't grant criterion '%s' of advancement %s to %s as they already have it", "commands.advancement.grant.criterion.to.one.success": "Granted criterion '%s' of advancement %s to %s", "commands.advancement.grant.many.to.many.failure": "Couldn't grant %s advancements to %s players as they already have them", "commands.advancement.grant.many.to.many.success": "Granted %s advancements to %s players", "commands.advancement.grant.many.to.one.failure": "Couldn't grant %s advancements to %s as they already have them", "commands.advancement.grant.many.to.one.success": "Granted %s advancements to %s", "commands.advancement.grant.one.to.many.failure": "Couldn't grant advancement %s to %s players as they already have it", "commands.advancement.grant.one.to.many.success": "Granted the advancement %s to %s players", "commands.advancement.grant.one.to.one.failure": "Couldn't grant advancement %s to %s as they already have it", "commands.advancement.grant.one.to.one.success": "Granted the advancement %s to %s", "commands.advancement.revoke.criterion.to.many.failure": "Couldn't revoke criterion '%s' of advancement %s from %s players as they don't have it", "commands.advancement.revoke.criterion.to.many.success": "Revoked criterion '%s' of advancement %s from %s players", "commands.advancement.revoke.criterion.to.one.failure": "Couldn't revoke criterion '%s' of advancement %s from %s as they don't have it", "commands.advancement.revoke.criterion.to.one.success": "Revoked criterion '%s' of advancement %s from %s", "commands.advancement.revoke.many.to.many.failure": "Couldn't revoke %s advancements from %s players as they don't have them", "commands.advancement.revoke.many.to.many.success": "Revoked %s advancements from %s players", "commands.advancement.revoke.many.to.one.failure": "Couldn't revoke %s advancements from %s as they don't have them", "commands.advancement.revoke.many.to.one.success": "Revoked %s advancements from %s", "commands.advancement.revoke.one.to.many.failure": "Couldn't revoke advancement %s from %s players as they don't have it", "commands.advancement.revoke.one.to.many.success": "Revoked the advancement %s from %s players", "commands.advancement.revoke.one.to.one.failure": "Couldn't revoke advancement %s from %s as they don't have it", "commands.advancement.revoke.one.to.one.success": "Revoked the advancement %s from %s", "commands.attribute.base_value.get.success": "Base value of attribute %s for entity %s is %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Base value for attribute %s for entity %s set to %s", "commands.attribute.failed.entity": "%s is not a valid entity for this command", "commands.attribute.failed.modifier_already_present": "Modifier %s is already present on attribute %s for entity %s", "commands.attribute.failed.no_attribute": "Entity %s has no attribute %s", "commands.attribute.failed.no_modifier": "Attribute %s for entity %s has no modifier %s", "commands.attribute.modifier.add.success": "Added modifier %s to attribute %s for entity %s", "commands.attribute.modifier.remove.success": "Removed modifier %s from attribute %s for entity %s", "commands.attribute.modifier.value.get.success": "Value of modifier %s on attribute %s for entity %s is %s", "commands.attribute.value.get.success": "Value of attribute %s for entity %s is %s", "commands.ban.failed": "Lá vistë. I tyalindo avanwa tensi", "commands.ban.success": "%s avanwa: %s", "commands.banip.failed": "Lá vistë. Sana IP avanwa tensi", "commands.banip.info": "Sina avanwië apë tyalindo(r) %s: %s", "commands.banip.invalid": "Úmára tengessë IP, hya lasinwa tyalindo", "commands.banip.success": "Avanwa IP %s: %s", "commands.banlist.entry": "%s nánë avanwa ló %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON>)", "commands.banlist.list": "<PERSON><PERSON><PERSON> %s:", "commands.banlist.none": "<PERSON><PERSON> <PERSON><PERSON>", "commands.bossbar.create.failed": "<PERSON><PERSON><PERSON> tur<PERSON> as i nótë ID '%s' ná nanwa tensi", "commands.bossbar.create.success": "Véra téma turcotto %s ontaina", "commands.bossbar.get.max": "Custom bossbar %s has a maximum of %s", "commands.bossbar.get.players.none": "Véra téma turcotto %s lá sáma tyalindor i nár sinomë", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "Véra téma turcotto %s samë mirma %s", "commands.bossbar.get.visible.hidden": "Véra téma turcotto %s nurtaina ná", "commands.bossbar.get.visible.visible": "Véra téma turcotto %s apantina sí", "commands.bossbar.list.bars.none": "<PERSON><PERSON> <PERSON><PERSON> caraiti vér<PERSON> témar turcottor", "commands.bossbar.list.bars.some": "<PERSON>är caraiti vérë témar turcottoron %s: %s", "commands.bossbar.remove.success": "Véra téma turcotto %s haitaina", "commands.bossbar.set.color.success": "Quilë véra témo turcotto %s áhië", "commands.bossbar.set.color.unchanged": "Lá vistë. Tana i quilë sina témo turcotto tensi", "commands.bossbar.set.max.success": "Custom bossbar %s has changed maximum to %s", "commands.bossbar.set.max.unchanged": "Nothing changed. That's already the max of this bossbar", "commands.bossbar.set.name.success": "Véra téma turcotto %s anaië enestina", "commands.bossbar.set.name.unchanged": "Lá vistë. Tana i essë sina témo turcotto tensi", "commands.bossbar.set.players.success.none": "Véra téma turcotto %s lá sáma tyalindor", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Lá vistë. I tyalindor nár témassë turcotto tensi. Napanië hya haitië lá cárima", "commands.bossbar.set.style.success": "Lengië véra témo turcotto %s áhië", "commands.bossbar.set.style.unchanged": "Lá vistë. Tana tensi i lengië sina témo turcotto", "commands.bossbar.set.value.success": "Mirma véra témo turcotto %s áhië ana %s", "commands.bossbar.set.value.unchanged": "Lá vistë. Tana i mirma sina témo turcotto tensi", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON>á vistë. I téma turcotto nurtaina tensi", "commands.bossbar.set.visibility.unchanged.visible": "Lá vistë. I téma turcotto cénima tensi", "commands.bossbar.set.visible.success.hidden": "Véra téma turcotto %s nurtaina sí", "commands.bossbar.set.visible.success.visible": "Véra téma turcotto %s cénima sí", "commands.bossbar.unknown": "<PERSON><PERSON><PERSON> tur<PERSON> as i nótë ID '%s' lá nanwa", "commands.clear.success.multiple": "Removed %s item(s) from %s players", "commands.clear.success.single": "Removed %s item(s) from player %s", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "No blocks were cloned", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s on block %s, %s, %s after scale factor of %s is %s", "commands.data.block.invalid": "The target block is not a block entity", "commands.data.block.modified": "Istalë pá ronwa %s, %s, %s vistanwa", "commands.data.block.query": "Istalë pá ronwa %s, %s, %s ná: %s", "commands.data.entity.get": "%s on %s after scale factor of %s is %s", "commands.data.entity.invalid": "Unable to modify player data", "commands.data.entity.modified": "Modified entity data of %s", "commands.data.entity.query": "%s has the following entity data: %s", "commands.data.get.invalid": "Can't get %s; only numeric tags are allowed", "commands.data.get.multiple": "This argument accepts a single NBT value", "commands.data.get.unknown": "%s lá nétima; i tag lá nanwa", "commands.data.merge.failed": "Nothing changed. The specified properties already have these values", "commands.data.modify.expected_list": "Expected list, got: %s", "commands.data.modify.expected_object": "Expected object, got: %s", "commands.data.modify.expected_value": "Ho<PERSON><PERSON> mirma, nanwa: %s", "commands.data.modify.invalid_index": "Invalid list index: %s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s in storage %s after scale factor of %s is %s", "commands.data.storage.modified": "Modified storage %s", "commands.data.storage.query": "Storage %s has the following contents: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Essë vinya o<PERSON> '%s' lá mára", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Ocombë '%s' lá carait<PERSON>!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "Ocombë '%s' tensi caraitë!", "commands.datapack.enable.failed.no_flags": "Ocombë '%s' l<PERSON> caraitima, an iquisyainë talcar lár caraiti sina ambaressë: %s!", "commands.datapack.list.available.none": "Lá ëar ambë férimë ocombi", "commands.datapack.list.available.success": "Eär férimë ocombi %s: %s", "commands.datapack.list.enabled.none": "<PERSON><PERSON> <PERSON><PERSON> caraiti o<PERSON>i", "commands.datapack.list.enabled.success": "Eär caraiti ocombi %s: %s", "commands.datapack.modify.disable": "Luhtyalë ocombëo: '%s'", "commands.datapack.modify.enable": "<PERSON><PERSON>ë o<PERSON>ëo: '%s'", "commands.datapack.unknown": "<PERSON>in<PERSON> ocombë: '%s'", "commands.debug.alreadyRunning": "The tick profiler is already started", "commands.debug.function.noRecursion": "Can't trace from inside of function", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "Failed to trace function", "commands.debug.notRunning": "The tick profiler hasn't started", "commands.debug.started": "Started tick profiling", "commands.debug.stopped": "Stopped tick profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.defaultgamemode.success": "Sí i sanya tyalmelé %s ná", "commands.deop.failed": "Nothing changed. The player is not an operator", "commands.deop.success": "Made %s no longer a server operator", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Hrangwë lá vistaina; nás %s tensi", "commands.difficulty.query": "Hrangwë ná %s", "commands.difficulty.success": "I hrangwë martaina ve %s", "commands.drop.no_held_items": "Entity can't hold any items", "commands.drop.no_loot_table": "Engwë %s penë rapta-apantië", "commands.drop.no_loot_table.block": "I ronwa %s penë rapta-apantië", "commands.drop.success.multiple": "%s nati etehatinë", "commands.drop.success.multiple_with_table": "Nati %s etehatinë rapta-apantiello %s", "commands.drop.success.single": "%s %s etehatinë", "commands.drop.success.single_with_table": "%s %s etehatinë rapta-apantiello %s", "commands.effect.clear.everything.failed": "Target has no effects to remove", "commands.effect.clear.everything.success.multiple": "Removed every effect from %s targets", "commands.effect.clear.everything.success.single": "Removed every effect from %s", "commands.effect.clear.specific.failed": "Target doesn't have the requested effect", "commands.effect.clear.specific.success.multiple": "Removed effect %s from %s targets", "commands.effect.clear.specific.success.single": "Removed effect %s from %s", "commands.effect.give.failed": "Unable to apply this effect (target is either immune to effects, or has something stronger)", "commands.effect.give.success.multiple": "Applied effect %s to %s targets", "commands.effect.give.success.single": "Applied effect %s to %s", "commands.enchant.failed": "Lá vistë. I mehti atwa pénar nat málta<PERSON>, atwa i lúcë lánë antácima", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "%s lá cauva tana lúcë", "commands.enchant.failed.itemless": "%s is not holding any item", "commands.enchant.failed.level": "%s lahta i pelmëa tyellë sina lúcëo (%s)", "commands.enchant.success.multiple": "I lúcë %s antacinwa engwennar %s", "commands.enchant.success.single": "I lúcë %s antacinwa i engwenna tyalindova %s", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "Test failed", "commands.execute.conditional.fail_count": "Test failed, count: %s", "commands.execute.conditional.pass": "Test passed", "commands.execute.conditional.pass_count": "Test passed, count: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "Tyeller handëo %s antainë tyalindoin %s", "commands.experience.add.levels.success.single": "Tyeller handëo %s antainë tyalindon %s", "commands.experience.add.points.success.multiple": "Gave %s experience points to %s players", "commands.experience.add.points.success.single": "Gave %s experience points to %s", "commands.experience.query.levels": "%s samë tyeller handëo %s", "commands.experience.query.points": "%s has %s experience points", "commands.experience.set.levels.success.multiple": "Tyeller handëo %s antainë tyalindossen %s", "commands.experience.set.levels.success.single": "Tyeller handëo %s tulcinë tyalindossë %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> set experience points above the maximum points for the player's current level", "commands.experience.set.points.success.multiple": "Set %s experience points on %s players", "commands.experience.set.points.success.single": "Set %s experience points on %s", "commands.fill.failed": "No blocks were filled", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "<PERSON><PERSON> an<PERSON>ótimë sana latsessë (pelma: %s, yuhtinë: %s)", "commands.forceload.added.failure": "No chunks were marked for force loading", "commands.forceload.added.multiple": "Marked %s chunks in %s from %s to %s to be force loaded", "commands.forceload.added.none": "No force loaded chunks were found in %s", "commands.forceload.added.single": "Marked chunk %s in %s to be force loaded", "commands.forceload.list.multiple": "%s force loaded chunks were found in %s at: %s", "commands.forceload.list.single": "A force loaded chunk was found in %s at: %s", "commands.forceload.query.failure": "Chunk at %s in %s is not marked for force loading", "commands.forceload.query.success": "Chunk at %s in %s is marked for force loading", "commands.forceload.removed.all": "Unmarked all force loaded chunks in %s", "commands.forceload.removed.failure": "No chunks were removed from force loading", "commands.forceload.removed.multiple": "Unmarked %s chunks in %s from %s to %s for force loading", "commands.forceload.removed.single": "Unmarked chunk %s in %s for force loading", "commands.forceload.toobig": "Too many chunks in the specified area (maximum %s, specified %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "Tyalmelë %so tulcanwa ve %s", "commands.gamemode.success.self": "Tyalmelelya tulcanwa ve %s", "commands.gamerule.query": "Gamerule %s is currently set to: %s", "commands.gamerule.set": "Gamerule %s is now set to: %s", "commands.give.failed.toomanyitems": "Can't give more than %s of %s", "commands.give.success.multiple": "Gave %s %s to %s players", "commands.give.success.single": "Gave %s %s to %s", "commands.help.failed": "Unknown command or insufficient permissions", "commands.item.block.set.success": "Replaced a slot at %s, %s, %s with %s", "commands.item.entity.set.success.multiple": "Replaced a slot on %s entities with %s", "commands.item.entity.set.success.single": "Replaced a slot on %s with %s", "commands.item.source.no_such_slot": "I celu penë assa %s", "commands.item.source.not_a_container": "Source position %s, %s, %s is not a container", "commands.item.target.no_changed.known_item": "Lana mehtë cambë %s assanna %s", "commands.item.target.no_changes": "Lana mehtë cambë i nat assanna %s", "commands.item.target.no_such_slot": "I mehtë penë assa %s", "commands.item.target.not_a_container": "Target position %s, %s, %s is not a container", "commands.jfr.dump.failed": "Aucolië JFR-minatenco loitina: %s", "commands.jfr.start.failed": "\"JFR profiling\" yes<PERSON><PERSON> loitina", "commands.jfr.started": "\"JFR profiling\" yestaina", "commands.jfr.stopped": "\"JFR profiling\" pustin' ar aucolin' ana %s", "commands.kick.owner.failed": "I server-öamo úláhima tyalmissen LAN", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "%s lahina: %s", "commands.kill.success.multiple": "Nahtanwë engwi %s", "commands.kill.success.single": "%s nahtanwa", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "There are %s of a max of %s players online: %s", "commands.locate.biome.not_found": "Could not find a biome of type \"%s\" within reasonable distance", "commands.locate.biome.success": "Anarëa %s ilyaron se %s (ronwar %s silo)", "commands.locate.poi.not_found": "Could not find a point of interest of type \"%s\" within reasonable distance", "commands.locate.poi.success": "Anarëa %s ilyaron se %s (ronwar %s silo)", "commands.locate.structure.invalid": "I carmë as nostalë \"%s\" lá nanwa", "commands.locate.structure.not_found": "Could not find a structure of type \"%s\" nearby", "commands.locate.structure.success": "Anarëa %s ilyaron se %s (ronwar %s silo)", "commands.message.display.incoming": "%s hlussëa lyenna: %s", "commands.message.display.outgoing": "Hlussanelyë ana %s: %s", "commands.op.failed": "Nothing changed. The player already is an operator", "commands.op.success": "Made %s a server operator", "commands.pardon.failed": "Lá vistë. I tyalindo lá avanwa", "commands.pardon.success": "%s lá avanwa sí", "commands.pardonip.failed": "Lá vistë. Sana IP lá avanwa", "commands.pardonip.invalid": "Invalid IP address", "commands.pardonip.success": "%s (IP) lá avanwa sí", "commands.particle.failed": "The particle was not visible for anybody", "commands.particle.success": "Displaying particle %s", "commands.perf.alreadyRunning": "The performance profiler is already started", "commands.perf.notRunning": "The performance profiler hasn't started", "commands.perf.reportFailed": "Failed to create debug report", "commands.perf.reportSaved": "Debug-vesyalë ontanwa mi %s", "commands.perf.started": "Started 10 second performance profiling run (use '/perf stop' to stop early)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "Caitalë caricanto loitanwa", "commands.place.feature.invalid": "I caricanta as nostalë \"%s\" lá nanwa", "commands.place.feature.success": "\"%s\" caitanwa mi %s, %s, %s", "commands.place.jigsaw.failed": "Ontalë i tanwëo loitanwa", "commands.place.jigsaw.invalid": "I arcantier as nostalë \"%s\" lár nanwë", "commands.place.jigsaw.success": "Tanwë ontanwa mi %s, %s, %s", "commands.place.structure.failed": "Caitalë carmëo lo<PERSON>wa", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Carmë \"%s\" ontanwa mi %s, %s, %s", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "There is no template with id \"%s\"", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "I hlón haiya langë ar lás hlárima", "commands.playsound.success.multiple": "Played sound %s to %s players", "commands.playsound.success.single": "Played sound %s to %s", "commands.publish.alreadyPublished": "Multiplayer game is already hosted on port %s", "commands.publish.failed": "Lá pólan carë nómëa tyalmë", "commands.publish.started": "Nómëa tyalmë nasina mi %s", "commands.publish.success": "Multiplayer game is now hosted on port %s", "commands.random.error.range_too_large": "Anyaitien i rísima mirmo mauya ná tenna 2147483646", "commands.random.error.range_too_small": "Anyaitien i rísima mirmo mauya ná annún 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "<PERSON> vinya carmen parinwa", "commands.recipe.give.success.multiple": "Vinyë carmeni %s tyalindoin %s", "commands.recipe.give.success.single": "Vinyë carmeni %s o %s", "commands.recipe.take.failed": "<PERSON> carmen n<PERSON>", "commands.recipe.take.success.multiple": "Carmeni %s napinwë tyalindollon %s", "commands.recipe.take.success.single": "Carmeni %s napinwë ollo %s", "commands.reload.failure": "Reload failed; keeping old data", "commands.reload.success": "Enapantalë!", "commands.ride.already_riding": "%s tensi nortëa %s", "commands.ride.dismount.success": "%s tellë norta %s", "commands.ride.mount.failure.cant_ride_players": "Tyalindor l<PERSON>", "commands.ride.mount.failure.generic": "%s lá pollë norta %s", "commands.ride.mount.failure.loop": "I engwë lá caitaima pá insë yola pá lelyandoryar", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s yestanë norta %s", "commands.ride.not_riding": "%s lá <PERSON>a", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "<PERSON><PERSON><PERSON> tensi", "commands.save.alreadyOn": "Ren<PERSON>ë ná caraitë tensi", "commands.save.disabled": "<PERSON><PERSON><PERSON><PERSON> sí", "commands.save.enabled": "<PERSON><PERSON><PERSON><PERSON> car<PERSON> sí", "commands.save.failed": "Ren<PERSON>ë i tyalmëo lacárima (ma i nótar penquanta?)", "commands.save.saving": "I tyalmë renina s<PERSON> (quí anda ná!)", "commands.save.success": "I tyal<PERSON><PERSON> renina", "commands.schedule.cleared.failure": "No schedules with id %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Scheduled tag '%s' in %s tick(s) at gametime %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "Can't schedule for current tick", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON><PERSON> as sina essë ná nanwa tensi", "commands.scoreboard.objectives.add.success": "<PERSON><PERSON> indu<PERSON> '%s' ontaina", "commands.scoreboard.objectives.display.alreadyEmpty": "Lá vistë. Tana assa cumna tensi", "commands.scoreboard.objectives.display.alreadySet": "Lá vistë. Tana assa tensi apantëa i induinen", "commands.scoreboard.objectives.display.cleared": "Cleared any objectives in display slot %s", "commands.scoreboard.objectives.display.set": "Set display slot %s to show objective %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> <PERSON><PERSON> in<PERSON>", "commands.scoreboard.objectives.list.success": "Eär induineni %s: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Changed the display name of %s to %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "Changed the render type of objective %s", "commands.scoreboard.objectives.remove.success": "Induinen '%s' haitina", "commands.scoreboard.players.add.success.multiple": "%s napanina ana %s engwin %s", "commands.scoreboard.players.add.success.single": "%s napanina ana %s rá %s (sí %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "Nothing changed. That trigger is already enabled", "commands.scoreboard.players.enable.invalid": "Enable only works on trigger-objectives", "commands.scoreboard.players.enable.success.multiple": "Enabled trigger %s for %s entities", "commands.scoreboard.players.enable.success.single": "Enabled trigger %s for %s", "commands.scoreboard.players.get.null": "Can't get value of %s for %s; none is set", "commands.scoreboard.players.get.success": "%s samë %s %s", "commands.scoreboard.players.list.empty": "<PERSON><PERSON> <PERSON><PERSON> hilin<PERSON> engwi", "commands.scoreboard.players.list.entity.empty": "%s pen<PERSON> sari apan<PERSON>", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s samë sari (%s):", "commands.scoreboard.players.list.success": "<PERSON><PERSON><PERSON> hilinë engwi %s: %s", "commands.scoreboard.players.operation.success.multiple": "%s ceutina engwin %s", "commands.scoreboard.players.operation.success.single": "%s tulcina ve %s ana %s", "commands.scoreboard.players.remove.success.multiple": "%s haitina ollo %s engwin %s", "commands.scoreboard.players.remove.success.single": "%s haitina ollo %s rá %s (sí %s)", "commands.scoreboard.players.reset.all.multiple": "Entulca ilyë sari engwin %s", "commands.scoreboard.players.reset.all.single": "Ilyë sari entulcinë rá %s", "commands.scoreboard.players.reset.specific.multiple": "%s entulcina engwin %s", "commands.scoreboard.players.reset.specific.single": "%s entulcina rá %s", "commands.scoreboard.players.set.success.multiple": "%s tulcina engwin %s ana %s", "commands.scoreboard.players.set.success.single": "%s tulcina ve %s ana %s", "commands.seed.success": "Erdë: %s", "commands.setblock.failed": "Could not set the block", "commands.setblock.success": "Changed the block at %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "Ambarya nómë enontiëo tulcina mi %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Nómë enontiëo tulcina mi %s, %s, %s [%s], mi %s rá %s tyalindor", "commands.spawnpoint.success.single": "Nómë enontiëo tulcina mi %s, %s, %s [%s], mi %s rá %s", "commands.spectate.not_spectator": "%s lá tyal<PERSON> le<PERSON>", "commands.spectate.self": "<PERSON><PERSON> le<PERSON> lertirë imlë", "commands.spectate.success.started": "Lertíralyë %s sí", "commands.spectate.success.stopped": "<PERSON><PERSON><PERSON><PERSON> engwëo telina", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Invalid maxHeight %s; expected higher than world minimum %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "Pustalë servero", "commands.stopsound.success.source.any": "Stopped all '%s' sounds", "commands.stopsound.success.source.sound": "Stopped sound '%s' on source '%s'", "commands.stopsound.success.sourceless.any": "Stopped all sounds", "commands.stopsound.success.sourceless.sound": "Stopped sound '%s'", "commands.summon.failed": "Unable to summon entity", "commands.summon.failed.uuid": "Unable to summon entity due to duplicate UUIDs", "commands.summon.invalidPosition": "I nómë úmára yalien", "commands.summon.success": "Vinya %s yalina", "commands.tag.add.failed": "Target either already has the tag or has too many tags", "commands.tag.add.success.multiple": "I tag '%s' nap<PERSON>na engwinnar %s", "commands.tag.add.success.single": "I tag '%s' napanina ana %s", "commands.tag.list.multiple.empty": "Lá ëar tagi engwessen %s", "commands.tag.list.multiple.success": "Engwi %s samir tagi %s: %s", "commands.tag.list.single.empty": "%s penë tagi", "commands.tag.list.single.success": "%s samë tagi %s: %s", "commands.tag.remove.failed": "Target does not have this tag", "commands.tag.remove.success.multiple": "I tag '%s' haitina engwellon %s", "commands.tag.remove.success.single": "I tag '%s' haitina ollo %s", "commands.team.add.duplicate": "A team already exists by that name", "commands.team.add.success": "Yonávë %s ontanwa", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "Nothing changed. That team is already empty", "commands.team.join.success.multiple": "Astamor %s né napaninë %s yonávenna", "commands.team.join.success.single": "%s napanina %s yonávenna", "commands.team.leave.success.multiple": "%s astamor haitinë ilyë yonávillon", "commands.team.leave.success.single": "Removed %s from any team", "commands.team.list.members.empty": "Lá ëar astamor yo<PERSON>ë %s", "commands.team.list.members.success": "Yonávë %s samë astamo(r) %s: %s", "commands.team.list.teams.empty": "<PERSON><PERSON> <PERSON> yo<PERSON>", "commands.team.list.teams.success": "<PERSON><PERSON>r yonávi %s: %s", "commands.team.option.collisionRule.success": "Collision rule for team %s is now \"%s\"", "commands.team.option.collisionRule.unchanged": "Nothing changed. Collision rule is already that value", "commands.team.option.color.success": "Updated the color for team %s to %s", "commands.team.option.color.unchanged": "Nothing changed. That team already has that color", "commands.team.option.deathMessageVisibility.success": "Death message visibility for team %s is now \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothing changed. Death message visibility is already that value", "commands.team.option.friendlyfire.alreadyDisabled": "Nothing changed. Friendly fire is already disabled for that team", "commands.team.option.friendlyfire.alreadyEnabled": "Nothing changed. Friendly fire is already enabled for that team", "commands.team.option.friendlyfire.disabled": "Disabled friendly fire for team %s", "commands.team.option.friendlyfire.enabled": "Enabled friendly fire for team %s", "commands.team.option.name.success": "I essë yonáveva %s ceutaina", "commands.team.option.name.unchanged": "Nothing changed. That team already has that name", "commands.team.option.nametagVisibility.success": "Apantië ession yonávëo %s ná \"%s\" sí", "commands.team.option.nametagVisibility.unchanged": "Lá vistë. Apantië ession imya ná tensi", "commands.team.option.prefix.success": "Team prefix set to %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nothing changed. That team already can't see invisible teammates", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Lá vistë. Sana yonávë tensi lerta cenë alacén<PERSON> astamor", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s can no longer see invisible teammates", "commands.team.option.seeFriendlyInvisibles.enabled": "Silo yonávë %s lerta cenë alacénimë astamor", "commands.team.option.suffix.success": "Team suffix set to %s", "commands.team.remove.success": "Yonávë %s haitina", "commands.teammsg.failed.noteam": "You must be on a team to message your team", "commands.teleport.invalidPosition": "Nómë lamára p<PERSON>rrú<PERSON>en", "commands.teleport.success.entity.multiple": "%s engwi palarrúmaner %snna", "commands.teleport.success.entity.single": "%s palarrúmanë %snna", "commands.teleport.success.location.multiple": "%s engwi palarr<PERSON>er na %s, %s, %s", "commands.teleport.success.location.single": "%s palarrúmanë na %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON> meter renë", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "I tyalmë helina", "commands.tick.status.lagging": "The game is running, but can't keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "I lúmë ná %s", "commands.time.set": "I lúmë tulcanwa ve %s", "commands.title.cleared.multiple": "Cleared titles for %s players", "commands.title.cleared.single": "Cleared titles for %s", "commands.title.reset.multiple": "Reset title options for %s players", "commands.title.reset.single": "Reset title options for %s", "commands.title.show.actionbar.multiple": "Showing new actionbar title for %s players", "commands.title.show.actionbar.single": "Showing new actionbar title for %s", "commands.title.show.subtitle.multiple": "Apantëan vinya asya-quetta tyalindoin %s", "commands.title.show.subtitle.single": "Apantëan vinya asya-quetta ana %s", "commands.title.show.title.multiple": "Showing new title for %s players", "commands.title.show.title.single": "Showing new title for %s", "commands.title.times.multiple": "Changed title display times for %s players", "commands.title.times.single": "Changed title display times for %s", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "Triggered %s (added %s to value)", "commands.trigger.failed.invalid": "You can only trigger objectives that are 'trigger' type", "commands.trigger.failed.unprimed": "You cannot trigger this objective yet", "commands.trigger.set.success": "Triggered %s (set value to %s)", "commands.trigger.simple.success": "Triggered %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "istalë = %s", "commands.version.header": "Server version info:", "commands.version.id": "nótë Id = %s", "commands.version.name": "essë = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "téma = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Set the weather to clear", "commands.weather.set.rain": "Set the weather to rain", "commands.weather.set.thunder": "<PERSON><PERSON><PERSON> i vilwis ana ulo yo hundo", "commands.whitelist.add.failed": "Tyalindo ná ninquicombessë tensi", "commands.whitelist.add.success": "Napannë %s i ninquicombenna", "commands.whitelist.alreadyOff": "<PERSON>n<PERSON><PERSON>ë la<PERSON> tensi", "commands.whitelist.alreadyOn": "Ninquicombë ná caraitë tensi", "commands.whitelist.disabled": "<PERSON>n<PERSON>combë la<PERSON> sí", "commands.whitelist.enabled": "<PERSON><PERSON><PERSON><PERSON>ë carait<PERSON> sí", "commands.whitelist.list": "Eär tyalindor %s i ninquicombessë: %s", "commands.whitelist.none": "Lá ëar tyalindor i ninquicombessë", "commands.whitelist.reloaded": "Ninquicombë enapantaina", "commands.whitelist.remove.failed": "Tyalindo lá nin<PERSON>ë", "commands.whitelist.remove.success": "%s haitina i ninquicombello", "commands.worldborder.center.failed": "Lá vistë. I ambarríma tensi endentanwa tás", "commands.worldborder.center.success": "Endenta i ambarríma mi %s, %s", "commands.worldborder.damage.amount.failed": "Lá vistë. I harnalë i ambarrímo tensi tallë", "commands.worldborder.damage.amount.success": "Tulca i harnalë i ambarrímo ve %s ilya enquaista lúmincëo", "commands.worldborder.damage.buffer.failed": "Lá vistë. I nirwa harnalëo i ambarrímo tensi tás", "commands.worldborder.damage.buffer.success": "Tulca i nirwa harnalëo i ambarrímo ve ronwa(r) %s", "commands.worldborder.get": "I ambarríma sí palda ve ronwa(r) %s", "commands.worldborder.set.failed.big": "Ambarríma lá lerta ná höa lá ronwar %s", "commands.worldborder.set.failed.far": "Ambarríma lá lerta caita palla ronwar %s", "commands.worldborder.set.failed.nochange": "Lá vistë. I ambarríma tensi alta tallë", "commands.worldborder.set.failed.small": "World border cannot be smaller than 1 block wide", "commands.worldborder.set.grow": "Growing the world border to %s blocks wide over %s seconds", "commands.worldborder.set.immediate": "Tulca i paldassë i ambarrímo ve ronwa(r) %s", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "Nothing changed. The world border warning is already that distance", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "Nothing changed. The world border warning is already that amount of time", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "Tyálal ambë lá lúmi 24", "compliance.playtime.hours": "Tyálal lúmellon %s", "compliance.playtime.message": "Accatyalië quí peresta sanya coivië", "connect.aborted": "<PERSON><PERSON><PERSON>", "connect.authorizing": "Mittarë...", "connect.connecting": "Limyalë i serverenna...", "connect.encrypting": "Uruhanyatarë...", "connect.failed": "<PERSON><PERSON><PERSON> loitina", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "Limyalë ambarenna...", "connect.negotiating": "Ettirë...", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Entulcalë...", "connect.transferring": "Transferring to new server...", "container.barrel": "Corcolca", "container.beacon": "<PERSON><PERSON><PERSON>", "container.beehive.bees": "Nieri: %s/%s", "container.beehive.honey": "Nehtë: %s/%s", "container.blast_furnace": "Ticutaitë urna", "container.brewing": "<PERSON><PERSON><PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON>", "container.chest": "Taucolca", "container.chestDouble": "<PERSON>ö<PERSON> taucolca", "container.crafter": "Intamma", "container.crafting": "Tamië", "container.creative": "Cilië nation", "container.dispenser": "Estatar", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "minuldar %s luiniondo", "container.enchant.lapis.one": "1 luinion", "container.enchant.level.many": "Tyeller lúcëo %s", "container.enchant.level.one": "1 tyellë lúcëo", "container.enchant.level.requirement": "Iquisina tyellë: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Urna", "container.grindstone_title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Aurië", "container.isLocked": "%s tancataina!", "container.lectern": "Parmatarma", "container.loom": "Lanwa", "container.repair": "Envinyata ar esta", "container.repair.cost": "Valda: %1$s", "container.repair.expensive": "<PERSON><PERSON> langë!", "container.shulkerBox": "Colca hyulcero", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ar %s ambë...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Allátima. I rapta lá nanwa tensi.", "container.stonecutter": "On<PERSON>ci<PERSON>", "container.upgrade": "<PERSON><PERSON><PERSON> carmar", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Napanë cantië macalëo", "controls.keybinds": "Key Binds...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "Key Binds", "controls.reset": "Entulca", "controls.resetAll": "Entulca to<PERSON>", "controls.title": "Túrië", "createWorld.customize.buffet.biome": "<PERSON><PERSON><PERSON> yondë", "createWorld.customize.buffet.title": "Single Biome Customization", "createWorld.customize.flat.height": "Tárië", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Tál - %s", "createWorld.customize.flat.layer.top": "Palmë - %s", "createWorld.customize.flat.removeLayer": "Haita i palo", "createWorld.customize.flat.tile": "<PERSON><PERSON> palo", "createWorld.customize.flat.title": "Vératalë ampalwa ambaro", "createWorld.customize.presets": "Nótulcaler", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON><PERSON>, sinë nótulcaler yúhtimë yú!", "createWorld.customize.presets.select": "Yuhta nótulcalë", "createWorld.customize.presets.share": "Ma méralyë ósatë nótulcalelya? <PERSON><PERSON> i colca nún!", "createWorld.customize.presets.title": "Cilë nótulcalë", "createWorld.preparing": "Ambar-ontalë ferina sí...", "createWorld.tab.game.title": "Tyalmë", "createWorld.tab.more.title": "Ambë", "createWorld.tab.world.title": "Ambar", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Credits", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Credits and Attribution", "dataPack.bundle.description": "Antas rincëa nat – pocollë", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstonië nevier", "dataPack.title": "<PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Villager Trade Rebalance", "dataPack.update_1_20.description": "Vinyë engwi ana Minecraft 1.20", "dataPack.update_1_20.name": "Ceutassë 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Ceutassë 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "Ocombë-tyastal<PERSON> loitina!", "dataPack.validation.reset": "Encilë i sanya ocombë", "dataPack.validation.working": "Tyastalë cilinë ocombi...", "dataPack.vanilla.description": "I sanyë sattar Minecrafto", "dataPack.vanilla.name": "Sanya", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "<PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "Sina ambar samë urrë hya hastanwë istaler.", "datapackFailure.safeMode.failed.title": "Apantië i ambaro varna lessë loitina.", "datapackFailure.title": "Errors in currently selected data packs prevented the world from loading.\nYou can either try to load it with only the vanilla data pack (\"safe mode\"), or go back to the title screen and fix it manually.", "death.attack.anvil": "%1$s firnë nu lanta onin", "death.attack.anvil.player": "Mahtaila %2$s, %1$s firnë nu lantaila onin", "death.attack.arrow": "%2$s quihtanë %1$s", "death.attack.arrow.item": "%2$s quihtanë %1$s lé %3$s", "death.attack.badRespawnPoint.link": "Intentional Game Design", "death.attack.badRespawnPoint.message": "%1$s nánë nahtina ló %2$s", "death.attack.cactus": "%1$s nánë ercaina qualmenna", "death.attack.cactus.player": "Ricaila usë ollo %2$s, %1$s patanë eccaldanna", "death.attack.cramming": "%1$s was squished too much", "death.attack.cramming.player": "%1$s was squashed by %2$s", "death.attack.dragonBreath": "%1$s was roasted in dragon's breath", "death.attack.dragonBreath.player": "%1$s was roasted in dragon's breath by %2$s", "death.attack.drown": "%1$s quornë", "death.attack.drown.player": "Ricaila usë ollo %2$s, %1$s quornë", "death.attack.dryout": "%1$s died from dehydration", "death.attack.dryout.player": "Ricaila usë ollo %2$s, %1$s firnë parcessë", "death.attack.even_more_magic": "%1$s nánë nahtina ambë núlenen", "death.attack.explosion": "%1$s rúvë", "death.attack.explosion.player": "%2$s rúvë %1$s", "death.attack.explosion.player.item": "Yuhtaila %3$s, %2$s rúvë %1$s", "death.attack.fall": "%1$s hit the ground too hard", "death.attack.fall.player": "Ricaila usë ollo %2$s, %1$s lantanë anastorna palmessë", "death.attack.fallingBlock": "%1$s firnë nu lanta ronwa", "death.attack.fallingBlock.player": "Mahtaila %2$s, %1$s firnë nu lanta ronwa", "death.attack.fallingStalactite": "Undalasar lantanë ana %1$s ar ternesses", "death.attack.fallingStalactite.player": "Undalasar lantanë ana %1$s ar ternesses yá mahtanes %2$s", "death.attack.fireball": "%2$s nahtanë %1$s narcoronanen", "death.attack.fireball.item": "%1$s was fireballed by %2$s using %3$s", "death.attack.fireworks": "%1$s went off with a bang", "death.attack.fireworks.item": "%1$s went off with a bang due to a firework fired from %3$s by %2$s", "death.attack.fireworks.player": "%1$s went off with a bang while fighting %2$s", "death.attack.flyIntoWall": "%1$s vill<PERSON> ram<PERSON>na", "death.attack.flyIntoWall.player": "Ricaila usë ollo %2$s, %1$s villë rambanna", "death.attack.freeze": "%1$s hellë ar firnë", "death.attack.freeze.player": "%1$s n<PERSON>ë helina ló %2$s ar firnë", "death.attack.generic": "%1$s firnë", "death.attack.generic.player": "%1$s firnë an %2$s", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "Mahtaila %2$s, %1$s nahtanwa", "death.attack.hotFloor": "%1$s túvë i cemen sirruima ná", "death.attack.hotFloor.player": "%1$s walked into the danger zone due to %2$s", "death.attack.inFire": "%1$s fi<PERSON><PERSON>", "death.attack.inFire.player": "Mahtaila %2$s, %1$s patanë nárenna", "death.attack.inWall": "%1$s quorn<PERSON> rambassë", "death.attack.inWall.player": "Mahtaila %2$s, %1$s quornë rambassë", "death.attack.indirectMagic": "%1$s nánë nahtina ló %2$s núlenen", "death.attack.indirectMagic.item": "%1$s nánë nahtina ló %2$s %3$snen", "death.attack.lava": "%1$s rincë lutë sir<PERSON>ë", "death.attack.lava.player": "%1$s rincë lutë sirruimassë meter usë %2$sllo", "death.attack.lightningBolt": "%1$s nánë nambinwa ítanen", "death.attack.lightningBolt.player": "Mahtaila %2$s, %1$s nánë nambinwa ítanen", "death.attack.mace_smash": "%1$s nánë nambanwa ló %2$s", "death.attack.mace_smash.item": "%1$s nánë nambanwa ló %2$s lé %3$s", "death.attack.magic": "%1$s nahtanwa núlenen", "death.attack.magic.player": "Ricaila usë ollo %2$s, %1$s nánë nahtina núlenen", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here's a stripped version: %s", "death.attack.mob": "%1$s nahtanwa ló %2$s", "death.attack.mob.item": "%1$s nahtanwa ló %2$s lé %3$s", "death.attack.onFire": "%1$s firnë ruinissen", "death.attack.onFire.item": "%1$s was burned to a crisp while fighting %2$s wielding %3$s", "death.attack.onFire.player": "%1$s was burned to a crisp while fighting %2$s", "death.attack.outOfWorld": "%1$s lantanë i ambarello", "death.attack.outOfWorld.player": "%1$s lá mernë coita i ambaressë yassë %2$s ná", "death.attack.outsideBorder": "%1$s mern<PERSON> hir<PERSON>", "death.attack.outsideBorder.player": "Mahtaila %2$s, %1$s mern<PERSON> hir<PERSON>", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.sonic_boom": "%1$s was obliterated by a sonically-charged shriek", "death.attack.sonic_boom.item": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s wielding %3$s", "death.attack.sonic_boom.player": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s", "death.attack.stalagmite": "%1$s lantanë ambalasarnenna ar nánë terina", "death.attack.stalagmite.player": "Mahtaila %2$s, %1$s lantanë ambalasarnenna ar nánë terina", "death.attack.starve": "%1$s asaitië ar firnë", "death.attack.starve.player": "Mahtaila %2$s, %1$s asaitië ar firnë", "death.attack.sting": "%1$s nánë nastaina qualmenna", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": "%1$s nánë nastaina qualmenna ló %2$s", "death.attack.sweetBerryBush": "%1$s nánë ercaina qualmenna ló tussa lipsioron", "death.attack.sweetBerryBush.player": "Ricaila usë %2$sllo, %1$s nánë ercaina qualmenna ló tussa lipsioron", "death.attack.thorns": "%1$s nánë nahtina yá rinces harna %2$s", "death.attack.thorns.item": "%1$s nánë nahtina ló %3$s yá rinces harna %2$s", "death.attack.thrown": "%1$s was pummeled by %2$s", "death.attack.thrown.item": "%1$s was pummeled by %2$s using %3$s", "death.attack.trident": "%1$s nánë terina ló %2$s", "death.attack.trident.item": "%1$s nánë terina ló %2$s lé %3$s", "death.attack.wither": "%1$s he<PERSON><PERSON> qualmenna", "death.attack.wither.player": "Mahtaila %2$s, %1$s hestanë qualmenna", "death.attack.witherSkull": "%2$s quihtanë coropë ana %1$s", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "%1$s lantan<PERSON> tarm<PERSON>llo", "death.fell.accident.ladder": "%1$s lanta<PERSON><PERSON> ran<PERSON>", "death.fell.accident.other_climbable": "%1$s lantanë retiessë", "death.fell.accident.scaffolding": "%1$s lanta<PERSON><PERSON>", "death.fell.accident.twisting_vines": "%1$s lantan<PERSON> locuilello", "death.fell.accident.vines": "%1$s lanta<PERSON><PERSON> u<PERSON>", "death.fell.accident.weeping_vines": "%1$s lantanë nainala u<PERSON>", "death.fell.assist": "%1$s was doomed to fall by %2$s", "death.fell.assist.item": "%1$s was doomed to fall by %2$s using %3$s", "death.fell.finish": "%1$s fell too far and was finished by %2$s", "death.fell.finish.item": "%1$s fell too far and was finished by %2$s using %3$s", "death.fell.killer": "%1$s was doomed to fall", "deathScreen.quit.confirm": "Ma nalyë tanca i merilyë etele<PERSON>?", "deathScreen.respawn": "Enontië", "deathScreen.score": "<PERSON><PERSON><PERSON>", "deathScreen.score.value": "Sár: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON>", "deathScreen.title": "Firnelyë!", "deathScreen.title.hardcore": "Tyalmë ná telina!", "deathScreen.titleScreen": "Penteramba", "debug.advanced_tooltips.help": "F3 + H = tain<PERSON> istaler", "debug.advanced_tooltips.off": "Tainë istaler: nurtainë", "debug.advanced_tooltips.on": "Tainë istaler: apan<PERSON><PERSON>", "debug.chunk_boundaries.help": "F3 + G = Show chunk boundaries", "debug.chunk_boundaries.off": "Pelmar latsion: alacénimë", "debug.chunk_boundaries.on": "Pelmar latsion: c<PERSON><PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> i nyatil", "debug.copy_location.help": "F3 + C = renë i nómë ve axan /tp, unduhepë F3 + C meter qualta i tyalmë", "debug.copy_location.message": "Nómë renina", "debug.crash.message": "F3 + C is held down. This will crash the game unless released.", "debug.crash.warning": "Crashing in %s...", "debug.creative_spectator.error": "Unable to switch game mode; no permission", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Unable to open game mode switcher; no permission", "debug.gamemodes.help": "F3 + F4 = Open game mode switcher", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s – enta", "debug.help.help": "F3 + Q = Show this list", "debug.help.message": "Key bindings:", "debug.inspect.client.block": "Copied client-side block data to clipboard", "debug.inspect.client.entity": "Copied client-side entity data to clipboard", "debug.inspect.help": "F3 + I = Ren<PERSON> engwë hya ronwa-istalë", "debug.inspect.server.block": "Copied server-side block data to clipboard", "debug.inspect.server.entity": "Copied server-side entity data to clipboard", "debug.pause.help": "F3 + Esc = Pause without pause menu (if pausing is possible)", "debug.pause_focus.help": "F3 + P = Pause on lost focus", "debug.pause_focus.off": "Pause on lost focus: disabled", "debug.pause_focus.on": "Pause on lost focus: enabled", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = yesta/pusta \"profiling\"", "debug.profiling.start": "Profiling started for %s seconds. Use F3 + L to stop early", "debug.profiling.stop": "Profiling ended. Saved results to %s", "debug.reload_chunks.help": "F3 + A = Reload chunks", "debug.reload_chunks.message": "Reloading all chunks", "debug.reload_resourcepacks.help": "F3 + T = Reload resource packs", "debug.reload_resourcepacks.message": "Reloaded resource packs", "debug.show_hitboxes.help": "F3 + B = Apanta hitboxi", "debug.show_hitboxes.off": "Hitboxi: <PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.show_hitboxes.on": "Hitboxi: <PERSON><PERSON><PERSON><PERSON>", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Sina lanwassë termaruva rér lempë tyalmessë. Almien!", "demo.day.2": "Attëa ré", "demo.day.3": "Neldëa ré", "demo.day.4": "Cantëa ré", "demo.day.5": "<PERSON><PERSON>ra métima ré tyalielyo!", "demo.day.6": "Lempëa ré a<PERSON>. Á yuhta %s meter carë fanwemma mairelyo.", "demo.day.warning": "I metta tyalielyo <PERSON>!", "demo.demoExpired": "<PERSON><PERSON><PERSON><PERSON> lanwas<PERSON>o van<PERSON>!", "demo.help.buy": "Manca Sí!", "demo.help.fullWrapped": "This demo will last 5 in-game days (about 1 hour and 40 minutes of real time). Check the advancements for hints! Have fun!", "demo.help.inventory": "Yuhta %1$s meter latya aurielya", "demo.help.jump": "Capë lé tolma %1$s", "demo.help.later": "Tyatyalla!", "demo.help.movement": "À yuhta %1$s, %2$s, %3$s, %4$s ar i tenta lelya", "demo.help.movementMouse": "Cenda ambar tentamanen", "demo.help.movementShort": "Leva niriénen %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft Lanwa Lé", "demo.remainingTime": "Lemyaila lúmë: %s", "demo.reminder": "I nevië telina sí. Á homanca i tyalmë itan cacarë, hya yesta vinya ambar!", "difficulty.lock.question": "Ma nalyë tanca i merilyë tancata i hrangwë sina ambaro? Sinen nauvas %1$s tennoio ar lalyë lertauva envistaitas.", "difficulty.lock.title": "Tancata hrangwë ambaro", "disconnect.endOfStream": "<PERSON><PERSON> cel<PERSON>", "disconnect.exceeded_packet_rate": "<PERSON>hin<PERSON> – cahta: lin<PERSON> o<PERSON>i l<PERSON> i pelma", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "Mittarë loitaina: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Linquen lá férima. Á cenda cilmi Microsoft fëalyo, mecin.", "disconnect.loginFailedInfo.invalidSession": "I tyalië lá mára (á ricë atayesta tyalmelya ar i yestama)", "disconnect.loginFailedInfo.serversUnavailable": "The authentication servers are currently not reachable. Please try again.", "disconnect.loginFailedInfo.userBanned": "Nál avanwa linquenessë", "disconnect.lost": "<PERSON><PERSON><PERSON>", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "<PERSON><PERSON><PERSON> – cahta: spam", "disconnect.timeout": "Himyalë ifíri<PERSON>", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Lasinwa nastur", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Progress: %s (total size unknown)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Sanyavë: %s", "editGamerule.title": "Vista sanyer tyalm<PERSON>o", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Hlucë nahti", "effect.minecraft.bad_omen": "Úmara tengwë", "effect.minecraft.blindness": "Lacenítië", "effect.minecraft.conduit_power": "Yulunefítë", "effect.minecraft.darkness": "Huinë", "effect.minecraft.dolphins_grace": "Almë nostalingwion", "effect.minecraft.fire_resistance": "Narnornië", "effect.minecraft.glowing": "Nalta", "effect.minecraft.haste": "Ormë", "effect.minecraft.health_boost": "<PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON>o <PERSON>el<PERSON>", "effect.minecraft.hunger": "Maitië", "effect.minecraft.infested": "Vembëa", "effect.minecraft.instant_damage": "Férima nahtë", "effect.minecraft.instant_health": "Férima málë", "effect.minecraft.invisibility": "Úcénimië", "effect.minecraft.jump_boost": "<PERSON><PERSON> capanda", "effect.minecraft.levitation": "Vilië", "effect.minecraft.luck": "Almë", "effect.minecraft.mining_fatigue": "Lumbië rostalëo", "effect.minecraft.nausea": "Quámë", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Niétië", "effect.minecraft.poison": "<PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "Nestië", "effect.minecraft.resistance": "Nornië", "effect.minecraft.saturation": "Enquantalë", "effect.minecraft.slow_falling": "<PERSON><PERSON>", "effect.minecraft.slowness": "Lungumë", "effect.minecraft.speed": "Lintië", "effect.minecraft.strength": "Poldorë", "effect.minecraft.trial_omen": "Tengwë riciéo", "effect.minecraft.unluck": "Umbar", "effect.minecraft.water_breathing": "Néfitië <PERSON>ë", "effect.minecraft.weakness": "Lortalë", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Hwestië", "effect.minecraft.wither": "Hest<PERSON><PERSON>", "effect.none": "No Effects", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Nendilië", "enchantment.minecraft.bane_of_arthropods": "Liantendacil", "enchantment.minecraft.binding_curse": "Hapië", "enchantment.minecraft.blast_protection": "Varyalë anat ruvië", "enchantment.minecraft.breach": "Terruvië", "enchantment.minecraft.channeling": "Ítaquantalë", "enchantment.minecraft.density": "Nelcië", "enchantment.minecraft.depth_strider": "Nennúriendilië", "enchantment.minecraft.efficiency": "Tyaraitië", "enchantment.minecraft.feather_falling": "<PERSON><PERSON> lanta", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Varyalë anat ruinë", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "Almë", "enchantment.minecraft.frost_walker": "<PERSON><PERSON>-patar", "enchantment.minecraft.impaling": "Terië", "enchantment.minecraft.infinity": "Úlanwa", "enchantment.minecraft.knockback": "Nampalpalë", "enchantment.minecraft.looting": "Maptalë", "enchantment.minecraft.loyalty": "Voronwë", "enchantment.minecraft.luck_of_the_sea": "Almë <PERSON>", "enchantment.minecraft.lure": "Telyantassë", "enchantment.minecraft.mending": "Apterië", "enchantment.minecraft.multishot": "Linquihtëa", "enchantment.minecraft.piercing": "Térë", "enchantment.minecraft.power": "Melehtë", "enchantment.minecraft.projectile_protection": "Varyalë anat quihtar", "enchantment.minecraft.protection": "Varyalë", "enchantment.minecraft.punch": "Palpalë", "enchantment.minecraft.quick_charge": "<PERSON><PERSON> quihta", "enchantment.minecraft.respiration": "Súlë", "enchantment.minecraft.riptide": "Tucië", "enchantment.minecraft.sharpness": "Maicië", "enchantment.minecraft.silk_touch": "<PERSON><PERSON>", "enchantment.minecraft.smite": "Andambië", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON> f<PERSON>", "enchantment.minecraft.sweeping": "Palancíma", "enchantment.minecraft.sweeping_edge": "Palancíma", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Úrácimië", "enchantment.minecraft.vanishing_curse": "Picië", "enchantment.minecraft.wind_burst": "Oäsussë", "entity.minecraft.acacia_boat": "Luntë nís<PERSON>ldo", "entity.minecraft.acacia_chest_boat": "Luntë nísimaldo as tauc<PERSON><PERSON>", "entity.minecraft.allay": "Alyar", "entity.minecraft.area_effect_cloud": "Area Effect Cloud", "entity.minecraft.armadillo": "Nyelco", "entity.minecraft.armor_stand": "Varma tulco", "entity.minecraft.arrow": "<PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Luntë vambuo as tauc<PERSON>ca", "entity.minecraft.bamboo_raft": "Luntë vambuo", "entity.minecraft.bat": "Quildarë", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON> h<PERSON>", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> h<PERSON> as tauc<PERSON><PERSON>", "entity.minecraft.blaze": "<PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Luntë", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "Hwestar", "entity.minecraft.breeze_wind_charge": "Hwesta", "entity.minecraft.camel": "Ulumpë", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Felcoliantë", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> a<PERSON> as taucol<PERSON>", "entity.minecraft.chest_boat": "Luntë as taucolca", "entity.minecraft.chest_minecart": "Ra<PERSON> as taucolca", "entity.minecraft.chicken": "Porocë", "entity.minecraft.cod": "Cod", "entity.minecraft.command_block_minecart": "<PERSON><PERSON> as ronwa axanion", "entity.minecraft.cow": "Yaxë", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> mori<PERSON>", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> mori<PERSON> as tauc<PERSON><PERSON>", "entity.minecraft.dolphin": "Nostalingwë", "entity.minecraft.donkey": "Pellopë", "entity.minecraft.dragon_fireball": "Narcoron hlócëo", "entity.minecraft.drowned": "Quormo", "entity.minecraft.egg": "<PERSON><PERSON>", "entity.minecraft.elder_guardian": "Cundo <PERSON>", "entity.minecraft.end_crystal": "<PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON>ó<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermítë", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "<PERSON><PERSON>o", "entity.minecraft.experience_bottle": "<PERSON><PERSON> o<PERSON><PERSON> luhti<PERSON>o", "entity.minecraft.experience_orb": "<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "<PERSON><PERSON> la<PERSON>", "entity.minecraft.falling_block_type": "Lantala %s", "entity.minecraft.fireball": "Narcoron", "entity.minecraft.firework_rocket": "Nartanwë", "entity.minecraft.fishing_bobber": "Soltar", "entity.minecraft.fox": "Rusco", "entity.minecraft.frog": "Quácë", "entity.minecraft.furnace_minecart": "<PERSON><PERSON> as urna", "entity.minecraft.ghast": "Ñasto", "entity.minecraft.giant": "Nor<PERSON>", "entity.minecraft.glow_item_frame": "Calcanta", "entity.minecraft.glow_squid": "Calmórolingwë", "entity.minecraft.goat": "Naico", "entity.minecraft.guardian": "Cundo", "entity.minecraft.happy_ghast": "Alassëa Ñasto", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON> as rumma", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Erumëa urco", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Lancarë", "entity.minecraft.iron_golem": "Angaturco", "entity.minecraft.item": "Nat", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "Canta", "entity.minecraft.jungle_boat": "Luntë töa rostaurëo", "entity.minecraft.jungle_chest_boat": "Luntë töa rostaurëo as taucol<PERSON>", "entity.minecraft.killer_bunny": "The Killer Bunny", "entity.minecraft.leash_knot": "<PERSON><PERSON> tun<PERSON>", "entity.minecraft.lightning_bolt": "<PERSON><PERSON>", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON> lamo", "entity.minecraft.magma_cube": "Sílanávecolca", "entity.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON> as tauc<PERSON><PERSON>", "entity.minecraft.marker": "Me<PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON>", "entity.minecraft.mooshroom": "Músirum", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON>nt<PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> as taucol<PERSON>", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Lumna etyalma nation", "entity.minecraft.painting": "Tecemma", "entity.minecraft.pale_oak_boat": "Luntë néca-norno", "entity.minecraft.pale_oak_chest_boat": "Luntë néca-norno as taucolca", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Quetaiwë", "entity.minecraft.phantom": "Lórerauco", "entity.minecraft.pig": "Polca", "entity.minecraft.piglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "Pillager", "entity.minecraft.player": "Tyalindo", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON>", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "Ascan<PERSON>", "entity.minecraft.salmon": "Alaxë", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>", "entity.minecraft.silverfish": "Telpingwë", "entity.minecraft.skeleton": "Axoquen", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Maxomo", "entity.minecraft.small_fireball": "Níca narcoron", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snowball": "Loscoron", "entity.minecraft.spawner_minecart": "<PERSON><PERSON> as et<PERSON><PERSON>", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> pirin", "entity.minecraft.spider": "Liantë", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "<PERSON>nt<PERSON> s<PERSON>", "entity.minecraft.spruce_chest_boat": "<PERSON>ntë s<PERSON> as tauc<PERSON><PERSON>", "entity.minecraft.squid": "Mórolingwë", "entity.minecraft.stray": "<PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Telcontar", "entity.minecraft.tadpole": "Quaccas", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "Ferya TNT", "entity.minecraft.tnt_minecart": "Raxa as TNT", "entity.minecraft.trader_llama": "<PERSON> man<PERSON>", "entity.minecraft.trident": "Nelcarca", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Black Tang", "entity.minecraft.tropical_fish.predefined.10": "Moorish Idol", "entity.minecraft.tropical_fish.predefined.11": "Ornate Butterflyfish", "entity.minecraft.tropical_fish.predefined.12": "Queltingwë", "entity.minecraft.tropical_fish.predefined.13": "Queen Angelfish", "entity.minecraft.tropical_fish.predefined.14": "Red Cichlid", "entity.minecraft.tropical_fish.predefined.15": "Red Lipped Blenny", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Tomato Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Triggerfish", "entity.minecraft.tropical_fish.predefined.2": "Blue Tang", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON> quelt<PERSON>", "entity.minecraft.tropical_fish.predefined.21": "Yellow Tang", "entity.minecraft.tropical_fish.predefined.3": "Butterflyfish", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "Clownfish", "entity.minecraft.tropical_fish.predefined.6": "Cotton Candy <PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Emperor <PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Goatfish", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Ronwalingwë", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Rilcë", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "Spotty", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Sand<PERSON><PERSON>", "entity.minecraft.vex": "Tarastar", "entity.minecraft.villager": "Opelemo", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Noremmatan", "entity.minecraft.villager.cleric": "Cordamo", "entity.minecraft.villager.farmer": "Cemendur", "entity.minecraft.villager.fisherman": "Ling<PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Alumo", "entity.minecraft.villager.librarian": "<PERSON><PERSON>", "entity.minecraft.villager.mason": "Ontamo", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Opelemo", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Carmatan", "entity.minecraft.villager.weaponsmith": "Maicatan", "entity.minecraft.vindicator": "Vindicator", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON> man<PERSON>o", "entity.minecraft.warden": "Ortirmo", "entity.minecraft.wind_charge": "Hwesta", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Wither-axo<PERSON>n", "entity.minecraft.wither_skull": "Coropë <PERSON>", "entity.minecraft.wolf": "Ráca", "entity.minecraft.zoglin": "Ron<PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> rocco", "entity.minecraft.zombie_villager": "Urcoya opelemo", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON>", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "Várë", "event.minecraft.raid.defeat.full": "Nalanta – várë", "event.minecraft.raid.raiders_remaining": "<PERSON><PERSON><PERSON><PERSON> termarila: %s", "event.minecraft.raid.victory": "Túrë", "event.minecraft.raid.victory.full": "Nalanta – túrë", "filled_map.buried_treasure": "Buried Treasure Map", "filled_map.explorer_jungle": "<PERSON><PERSON><PERSON> r<PERSON>-hirmo", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON> h<PERSON>-hirmo", "filled_map.id": "Nótë ID #%s", "filled_map.level": "(Tyellë %s/%s)", "filled_map.locked": "<PERSON><PERSON>", "filled_map.mansion": "<PERSON><PERSON><PERSON> ta<PERSON>-hirmo", "filled_map.monument": "<PERSON><PERSON><PERSON>-hi<PERSON>o", "filled_map.scale": "Scaling at 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON><PERSON>", "filled_map.unknown": "<PERSON><PERSON><PERSON>", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON><PERSON> ambar", "flat_world_preset.minecraft.desert": "Erumë", "flat_world_preset.minecraft.overworld": "Ambanórë", "flat_world_preset.minecraft.redstone_ready": "M<PERSON>ra meter finya <PERSON>nen", "flat_world_preset.minecraft.snowy_kingdom": "Lossëa aranië", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON>", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON> ambar", "flat_world_preset.unknown": "???", "gameMode.adventure": "Veryandelë", "gameMode.changed": "Tyalmelya anaië ceutaina ve %s", "gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "Anastornalë", "gameMode.spectator": "Lertirmolë", "gameMode.survival": "Vorielë", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "Announce advancements", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "<PERSON><PERSON><PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Hyanë", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "Tyalindo", "gamerule.category.spawning": "Etyalië", "gamerule.category.updates": "World Updates", "gamerule.commandBlockOutput": "Broadcast command block output", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Nótë ronwaron i vistaimë ú pusto axananen ve fill (quanta) hya clone (tatya).", "gamerule.disableElytraMovementCheck": "Disable elytra movement check", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Disable raids", "gamerule.doDaylightCycle": "Advance time of day", "gamerule.doEntityDrops": "Drop entity equipment", "gamerule.doEntityDrops.description": "Controls drops from minecarts (including inventories), item frames, boats, etc.", "gamerule.doFireTick": "Update fire", "gamerule.doImmediateRespawn": "Respawn immediately", "gamerule.doInsomnia": "Spawn phantoms", "gamerule.doLimitedCrafting": "Require recipe for crafting", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": "Drop mob loot", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": "Spawn mobs", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": "Spawn pillager patrols", "gamerule.doTileDrops": "Drop blocks", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "Spawn Wandering Traders", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "Spawn Wardens", "gamerule.doWeatherCycle": "Update weather", "gamerule.drowningDamage": "<PERSON><PERSON> quort<PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "<PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Forgive dead players", "gamerule.forgiveDeadPlayers.description": "Raiqui sendë vëor apsenir yá i mehtaina Tyalindo firë harivë.", "gamerule.freezeDamage": "<PERSON><PERSON>", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": "Keep inventory after death", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Broadcast admin commands", "gamerule.maxCommandChainLength": "Command chain size limit", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "Entity cramming threshold", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Allow destructive mob actions", "gamerule.naturalRegeneration": "Regenerate health", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "gamerule.playersSleepingPercentage.description": "Nótë tyalindoron maurina itan i ambarónë tulë.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "Random tick speed rate", "gamerule.reducedDebugInfo": "Reduce debug info", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": "Send command feedback", "gamerule.showDeathMessages": "Show death messages", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "Latsë enontië-nómëo", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "Allow spectators to generate terrain", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "Universal anger", "gamerule.universalAnger.description": "Angered neutral mobs attack any nearby player, not just the player that angered them. Works best if forgiveDead<PERSON><PERSON><PERSON> is disabled.", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "Véra", "generator.customized": "Yára vérataina", "generator.minecraft.amplified": "MELETYAINA", "generator.minecraft.amplified.info": "Aiya, alassen rië! Mára nótar iquisyaina.", "generator.minecraft.debug_all_block_states": "Debug", "generator.minecraft.flat": "<PERSON>pal<PERSON>", "generator.minecraft.large_biomes": "<PERSON>ë yondi", "generator.minecraft.normal": "Sanya", "generator.minecraft.single_biome_surface": "Erinqua yondë", "generator.single_biome_caves": "Caves", "generator.single_biome_floating_islands": "<PERSON><PERSON>", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "<PERSON><PERSON> hrangwë mentaless<PERSON> ve<PERSON>o", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drugs or alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Ostecië:", "gui.abuseReport.reason.false_reporting": "Laitë <PERSON>alë", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "Harassment or bullying", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON> pahta", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism or violent extremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Sending your report...", "gui.abuseReport.sent.title": "Vesyarë mentaina", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "Asanyë", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON>", "gui.all": "Ilyë", "gui.back": "<PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Fëalya a<PERSON>ialë, san lá ecë lyen tyalë linquenessë hya mitta Rëalmi.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "Nótë: %s", "gui.banned.description.reason_id_message": "Nótë: %s – %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Fëalya avanwa lúmëavë. Nauvas caraitë mi %s.", "gui.banned.description.unknownreason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified that it goes against the Minecraft Community Standards.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "<PERSON>m hya cacannal<PERSON>", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Fëa avanwa oialë", "gui.banned.title.temporary": "Fëalya avanwa lúmëavë", "gui.cancel": "<PERSON><PERSON>", "gui.chatReport.comments": "T<PERSON><PERSON>", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "Leave and Discard Report", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Cacarë tecië", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Avarta", "gui.chatReport.draft.edit": "Cacarë tecië", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Á ostecë i autas:", "gui.chatReport.observed_what": "<PERSON><PERSON>?", "gui.chatReport.read_info": "Learn About Reporting", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "Cilë nostalë vesyalëo", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "Menta vesyalë", "gui.chatReport.send.comments_too_long": "Á senta i menta, mecin", "gui.chatReport.send.no_reason": "Á cilë nostalë vesyalëo, mecin", "gui.chatReport.send.no_reported_messages": "Á cilë menta min annún vesyalenna", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Vesya tyalindo nyatilessë", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s quentë: %s mi %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Cacarë", "gui.copy_link_to_clipboard": "Renë i limë", "gui.days": "Ré(r) %s", "gui.done": "Carina", "gui.down": "Undu", "gui.entity_tooltip.type": "Nostalë: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "Lúmë(-i) %s", "gui.loadingMinecraft": "Feryalë <PERSON>o", "gui.minutes": "Lúmincë(-i) %s", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s tolma", "gui.narrate.editBox": "Colca %s: %s", "gui.narrate.slider": "licil %s", "gui.narrate.tab": "%s tab", "gui.no": "Lá", "gui.none": "<PERSON><PERSON>", "gui.ok": "Sá", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "Cacarë", "gui.recipebook.moreRecipes": "<PERSON><PERSON><PERSON> i forya itan cenë ambë", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cesta...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> il<PERSON>", "gui.recipebook.toggleRecipes.blastable": "Ticutaimë engwi", "gui.recipebook.toggleRecipes.craftable": "Támimë engwi", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON><PERSON> en<PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON>ai<PERSON><PERSON> engwi", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Mahta i fëanen Microsoft", "gui.socialInteractions.empty_blocked": "Alquen tapina nyatilessë", "gui.socialInteractions.empty_hidden": "Alquen nurtana nyatilessë", "gui.socialInteractions.hidden_in_chat": "Mentar nyatilessë tecinë ló %s nauvar nurtainë", "gui.socialInteractions.hide": "Nurta nyatilessë", "gui.socialInteractions.narration.hide": "Nurta mentar o %s", "gui.socialInteractions.narration.report": "Vesya tyalindo %s", "gui.socialInteractions.narration.show": "Apanta mentar o %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON> as i essë lac<PERSON>rima", "gui.socialInteractions.search_hint": "Cesta...", "gui.socialInteractions.server_label.multiple": "%s - %s tyalindor", "gui.socialInteractions.server_label.single": "%s - %s tyalindo", "gui.socialInteractions.show": "<PERSON><PERSON><PERSON> n<PERSON>tiless<PERSON>", "gui.socialInteractions.shown_in_chat": "Mentar nyatilessë tecinë ló %s nauvar apantinë", "gui.socialInteractions.status_blocked": "Tapinë", "gui.socialInteractions.status_blocked_offline": "<PERSON><PERSON> <PERSON> l<PERSON>", "gui.socialInteractions.status_hidden": "Nurtainë", "gui.socialInteractions.status_hidden_offline": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l<PERSON>", "gui.socialInteractions.status_offline": "<PERSON><PERSON>", "gui.socialInteractions.tab_all": "Ilyë", "gui.socialInteractions.tab_blocked": "Tapinë", "gui.socialInteractions.tab_hidden": "Nurtainë", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON> la<PERSON>", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> mentar", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> t<PERSON>", "gui.socialInteractions.tooltip.report.disabled": "Vesyalë al<PERSON>", "gui.socialInteractions.tooltip.report.no_messages": "Lana vesyaima menta tyalindollo %s", "gui.socialInteractions.tooltip.report.not_reportable": "Sina tyalindo l<PERSON>, an mentaryar nyatilessë lár tyastaimë sina serveressë", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON><PERSON> mentar", "gui.stats": "<PERSON><PERSON><PERSON><PERSON>", "gui.toMenu": "Nanwenë serverion combenna", "gui.toRealms": "Nanwenë Rëalmion combenna", "gui.toTitle": "Nanwenë Penterambanna", "gui.toWorld": "<PERSON><PERSON><PERSON> ambaron combenna", "gui.togglable_slot": "<PERSON><PERSON><PERSON> <PERSON> luhtya i assa", "gui.up": "Amba", "gui.waitingForResponse.button.inactive": "Entulë (%ss)", "gui.waitingForResponse.title": "I server horaina", "gui.yes": "Ná", "hanging_sign.edit": "Edit Hanging Sign Message", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON>rust<PERSON>", "instrument.minecraft.yearn_goat_horn": "Xarasta", "inventory.binSlot": "Nancarë nat", "inventory.hotbarInfo": "Renë fertéma lé %1$s+%2$s", "inventory.hotbarSaved": "Fert<PERSON><PERSON> renina (tulta lé %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON> r<PERSON>:", "item.canPlace": "Can be placed on:", "item.canUse.unknown": "<PERSON><PERSON><PERSON>", "item.color": "Quilë: %s", "item.components": "%s component(s)", "item.disabled": "Lacaraitë nat", "item.durability": "Coloitië: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Luntë nís<PERSON>ldo", "item.minecraft.acacia_chest_boat": "Luntë nísimaldo as tauc<PERSON><PERSON>", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON>ë <PERSON>", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as anwa", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as anwa", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as c<PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as c<PERSON>", "item.minecraft.armadillo_scute": "Satsë nyelcova", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.armor_stand": "Varma tulco", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.arrow": "<PERSON><PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> as ax<PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "Oh<PERSON>ë a<PERSON>", "item.minecraft.baked_potato": "Mastanwa cemorva", "item.minecraft.bamboo_chest_raft": "Luntë vambuo as tauc<PERSON>ca", "item.minecraft.bamboo_raft": "Luntë vambuo", "item.minecraft.bat_spawn_egg": "<PERSON>të quild<PERSON>", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.beef": "Hrávë yax<PERSON>o", "item.minecraft.beetroot": "Russulca", "item.minecraft.beetroot_seeds": "<PERSON><PERSON> r<PERSON>", "item.minecraft.beetroot_soup": "Sulpa russulco", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON> h<PERSON>", "item.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> h<PERSON> as tauc<PERSON><PERSON>", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON> quil<PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as mac<PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as mac<PERSON>", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Ohtë u<PERSON>óva", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON> quilë", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON> la<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Turyaitë netwë", "item.minecraft.bone": "Axo", "item.minecraft.bone_meal": "Axomulë", "item.minecraft.book": "Parma", "item.minecraft.bordure_indented_banner_pattern": "Ocantië: carcara p<PERSON>", "item.minecraft.bow": "<PERSON><PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON>ë h<PERSON>", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as yulda", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as yulda", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "item.minecraft.brick": "Tesar", "item.minecraft.brown_bundle": "Varnë pocoll<PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON><PERSON> quilë", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON><PERSON> latta<PERSON>", "item.minecraft.brush": "Au<PERSON>arma", "item.minecraft.bucket": "Calpa", "item.minecraft.bundle": "Pocollë", "item.minecraft.bundle.empty": "C<PERSON>na", "item.minecraft.bundle.empty.description": "Colis nati 64", "item.minecraft.bundle.full": "Quanta", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as ruinë", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as ruinë", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot": "Rassulca", "item.minecraft.carrot_on_a_stick": "Rassulca olbassë", "item.minecraft.cat_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.cauldron": "Tambin", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON><PERSON> f<PERSON>", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "Amborcauma angarem<PERSON>ëo", "item.minecraft.chainmail_helmet": "Castol angarembëo", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> a<PERSON> as taucol<PERSON>", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> as taucol<PERSON>", "item.minecraft.chicken": "Hrávë porocëo", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON><PERSON> nyello", "item.minecraft.clay_ball": "<PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "Falasseva netwë", "item.minecraft.cocoa_beans": "<PERSON><PERSON>", "item.minecraft.cod": "Raw Cod", "item.minecraft.cod_bucket": "Bucket of Cod", "item.minecraft.cod_spawn_egg": "Cod Spawn Egg", "item.minecraft.command_block_minecart": "<PERSON><PERSON> as ronwa axanion", "item.minecraft.compass": "Mententar", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON> ya<PERSON>", "item.minecraft.cooked_chicken": "Apsa porocëo", "item.minecraft.cooked_cod": "Cooked Cod", "item.minecraft.cooked_mutton": "Apsa mámo", "item.minecraft.cooked_porkchop": "Apsa polco", "item.minecraft.cooked_rabbit": "Apsa lopo", "item.minecraft.cooked_salmon": "Apsa al<PERSON>", "item.minecraft.cookie": "Mastaincë", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.creeper_banner_pattern.desc": "Cendelë <PERSON>", "item.minecraft.creeper_banner_pattern.new": "Ocantië: c<PERSON><PERSON><PERSON> Cree<PERSON>wa", "item.minecraft.creeper_spawn_egg": "Oh<PERSON>ë <PERSON>", "item.minecraft.crossbow": "Lanquinga", "item.minecraft.crossbow.projectile": "<PERSON><PERSON><PERSON>:", "item.minecraft.crossbow.projectile.multiple": "Quihta: %s x %s", "item.minecraft.crossbow.projectile.single": "Quihta: %s", "item.minecraft.cyan_bundle": "Fanyaluinë pocollë", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON> quilë", "item.minecraft.cyan_harness": "Fanyaluinë lattasta", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> mori<PERSON>", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> mori<PERSON> as tauc<PERSON><PERSON>", "item.minecraft.debug_stick": "Debug Stick", "item.minecraft.debug_stick.empty": "%s has no properties", "item.minecraft.debug_stick.select": "cilina \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" ana %s", "item.minecraft.diamond": "Tinwírë", "item.minecraft.diamond_axe": "<PERSON><PERSON>cco <PERSON>", "item.minecraft.diamond_boots": "Hyapa<PERSON>", "item.minecraft.diamond_chestplate": "Amborcauma tin<PERSON>írëo", "item.minecraft.diamond_helmet": "Castol tinwírëo", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Tinw<PERSON><PERSON> cauma rocco", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Ondopelet tinwírëo", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON>", "item.minecraft.disc_fragment_5": "<PERSON><PERSON> r<PERSON>", "item.minecraft.disc_fragment_5.desc": "Rindë lindalëo - 5", "item.minecraft.dolphin_spawn_egg": "Ohtë no<PERSON>ingwe<PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.dragon_breath": "Föa hlócëo", "item.minecraft.dried_kelp": "Parahtanwa ëaruilë", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON> quorm<PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "Litseva netwë", "item.minecraft.echo_shard": "Rimpë nallámo", "item.minecraft.egg": "Ohtë", "item.minecraft.elder_guardian_spawn_egg": "Ohtë cundo anyá<PERSON>va", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Luhtaina Parma", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> malt<PERSON>va", "item.minecraft.end_crystal": "Met<PERSON><PERSON>l", "item.minecraft.ender_dragon_spawn_egg": "Ender Dragon Spawn Egg", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "Endermite Spawn Egg", "item.minecraft.evoker_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON><PERSON> luhti<PERSON>o", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as nor<PERSON><PERSON>", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as nor<PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "Hendeva netwë", "item.minecraft.feather": "Quessë", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON> hen l<PERSON>", "item.minecraft.field_masoned_banner_pattern": "Ocantië: tesari", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Nartanwë", "item.minecraft.firework_rocket.flight": "Lúmë viliëo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Firework Star", "item.minecraft.firework_star.black": "Morë", "item.minecraft.firework_star.blue": "Luinë", "item.minecraft.firework_star.brown": "Varnë", "item.minecraft.firework_star.custom_color": "Véra", "item.minecraft.firework_star.cyan": "Fanyaluinë", "item.minecraft.firework_star.fade_to": "<PERSON>ta ana", "item.minecraft.firework_star.flicker": "Tintilië", "item.minecraft.firework_star.gray": "Sinda", "item.minecraft.firework_star.green": "Laica", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "Mísë", "item.minecraft.firework_star.lime": "Venya", "item.minecraft.firework_star.magenta": "Mangent<PERSON>", "item.minecraft.firework_star.orange": "Culuina", "item.minecraft.firework_star.pink": "Fanyacarnë", "item.minecraft.firework_star.purple": "Luicarnë", "item.minecraft.firework_star.red": "Carnë", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON> canta", "item.minecraft.firework_star.shape.burst": "Ruvië", "item.minecraft.firework_star.shape.creeper": "Creepercanta", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON><PERSON> coron", "item.minecraft.firework_star.shape.small_ball": "Níca coron", "item.minecraft.firework_star.shape.star": "Elencanta", "item.minecraft.firework_star.trail": "Pimpë", "item.minecraft.firework_star.white": "Ninquë", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Lingwëhimya", "item.minecraft.flint": "Sinca", "item.minecraft.flint_and_steel": "Nartar", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Hwinda netwë", "item.minecraft.flow_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.flow_banner_pattern.desc": "Hwindë", "item.minecraft.flow_banner_pattern.new": "Ocantië: hwindë", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.flower_banner_pattern.desc": "Canta almo", "item.minecraft.flower_banner_pattern.new": "Ocantië: canta almo", "item.minecraft.flower_pot": "Tambë", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON><PERSON> qu<PERSON>", "item.minecraft.furnace_minecart": "<PERSON><PERSON> as urna", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.ghast_tear": "<PERSON><PERSON>", "item.minecraft.glass_bottle": "Olpë cilino", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "item.minecraft.globe_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Ocantië: coron", "item.minecraft.glow_berries": "Calpior", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "Calcanta", "item.minecraft.glow_squid_spawn_egg": "Ohtë calmórolingweva", "item.minecraft.glowstone_dust": "<PERSON><PERSON> calondo", "item.minecraft.goat_horn": "Rassë naico", "item.minecraft.goat_spawn_egg": "Ohtë <PERSON>ico<PERSON>", "item.minecraft.gold_ingot": "<PERSON><PERSON>a malto", "item.minecraft.gold_nugget": "Lupsë malto", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "Pelecco malto", "item.minecraft.golden_boots": "Hyapatu malto", "item.minecraft.golden_carrot": "Culsulca", "item.minecraft.golden_chestplate": "Amborcauma malto", "item.minecraft.golden_helmet": "Castol malto", "item.minecraft.golden_hoe": "Hyarincë malto", "item.minecraft.golden_horse_armor": "Maltaina cauma rocco", "item.minecraft.golden_leggings": "Telcohan malto", "item.minecraft.golden_pickaxe": "Ondopelet malto", "item.minecraft.golden_shovel": "Sampa malto", "item.minecraft.golden_sword": "<PERSON><PERSON> malto", "item.minecraft.gray_bundle": "Sinda pocollë", "item.minecraft.gray_dye": "Sinda quilë", "item.minecraft.gray_harness": "Sinda lattasta", "item.minecraft.green_bundle": "Lai<PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON> quil<PERSON>", "item.minecraft.green_harness": "Laica latta<PERSON>", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON> cund<PERSON>", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.guster_banner_pattern.desc": "Hwesta", "item.minecraft.guster_banner_pattern.new": "Ocantië: hwesta", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.harness": "Lattasta", "item.minecraft.heart_of_the_sea": "<PERSON>", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as h<PERSON>", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as h<PERSON>", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.hoglin_spawn_egg": "Ohtë <PERSON>", "item.minecraft.honey_bottle": "Olpë as nehtë", "item.minecraft.honeycomb": "Nehtelë", "item.minecraft.hopper_minecart": "<PERSON><PERSON> as rumma", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Nasturwa netwë", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as r<PERSON><PERSON>", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as r<PERSON><PERSON>", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON><PERSON> erum<PERSON><PERSON> u<PERSON>", "item.minecraft.ink_sac": "<PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON>", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> ango", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON> ango", "item.minecraft.iron_chestplate": "Amborcauma ango", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Egg", "item.minecraft.iron_helmet": "Castol ango", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> ango", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON> cauma rocco", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON> ango", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON> ango", "item.minecraft.iron_nugget": "Lu<PERSON><PERSON> ango", "item.minecraft.iron_pickaxe": "Ondopelet ango", "item.minecraft.iron_shovel": "<PERSON><PERSON> ango", "item.minecraft.iron_sword": "<PERSON><PERSON> ango", "item.minecraft.item_frame": "Canta", "item.minecraft.jungle_boat": "Luntë töa rostaurëo", "item.minecraft.jungle_chest_boat": "Luntë töa rostaurëo as taucol<PERSON>", "item.minecraft.knowledge_book": "Nóleparma", "item.minecraft.lapis_lazuli": "Luinion", "item.minecraft.lava_bucket": "<PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "<PERSON><PERSON>a cauma rocco", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON> al<PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON> quil<PERSON>", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON> latta<PERSON>", "item.minecraft.light_gray_bundle": "Mísë p<PERSON>oll<PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON> quilë", "item.minecraft.light_gray_harness": "Mísë la<PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON> quil<PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON> latta<PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON> yulda", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON> lat<PERSON> yulda", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> yulda na<PERSON>", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> yulda harn<PERSON>", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> yulda m<PERSON>", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON> yulda ve<PERSON>o", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> yulda <PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> yulda capi<PERSON>o", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> yulda vili<PERSON>o", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON> yulda al<PERSON>", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON> senya yulda", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON> yulda moriceno", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON> yulda <PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON> hloima", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> yulda nest<PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> yulda moica lanti<PERSON>o", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> yulda <PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> yulda p<PERSON>o", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> yulda linti<PERSON>o", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON> nelca yulda", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON> yulda sand<PERSON>wa", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON> as n<PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON> yulda néfiti<PERSON>", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> yulda lo<PERSON>", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON> yulda h<PERSON>", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "<PERSON><PERSON><PERSON> rantondo", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Mangenta pocoll<PERSON>", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON> quilë", "item.minecraft.magenta_harness": "<PERSON>genta latta<PERSON>", "item.minecraft.magma_cream": "<PERSON><PERSON>", "item.minecraft.magma_cube_spawn_egg": "Magma Cube Spawn Egg", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON> as tauc<PERSON><PERSON>", "item.minecraft.map": "<PERSON><PERSON><PERSON>", "item.minecraft.melon_seeds": "<PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON> as ilin", "item.minecraft.minecart": "<PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as on<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as on<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.mojang_banner_pattern.desc": "Nat", "item.minecraft.mojang_banner_pattern.new": "Ocantië: nat", "item.minecraft.mooshroom_spawn_egg": "Mooshroom Spawn Egg", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as naitar", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as naitar", "item.minecraft.mule_spawn_egg": "Mule Spawn Egg", "item.minecraft.mushroom_stew": "Sulpa telumbion", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> – <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON> linda<PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON>á<PERSON>ë má<PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_brick": "<PERSON><PERSON>", "item.minecraft.nether_star": "<PERSON>en <PERSON>o", "item.minecraft.nether_wart": "Sirpë Nethero", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_chestplate": "Amborcauma neserito", "item.minecraft.netherite_helmet": "Castol neserito", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "Ondopelet neserito", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_shovel": "<PERSON><PERSON>", "item.minecraft.netherite_sword": "<PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Neseritwa aryatalë", "item.minecraft.oak_boat": "<PERSON>nt<PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> as taucol<PERSON>", "item.minecraft.ocelot_spawn_egg": "Ocelot Spawn Egg", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON> la<PERSON> rici<PERSON>o", "item.minecraft.orange_bundle": "Culuina pocollë", "item.minecraft.orange_dye": "Culuina quilë", "item.minecraft.orange_harness": "Culuina lattasta", "item.minecraft.painting": "Tecemma", "item.minecraft.pale_oak_boat": "Luntë néca-norno", "item.minecraft.pale_oak_chest_boat": "Luntë néca-norno as taucolca", "item.minecraft.panda_spawn_egg": "<PERSON><PERSON>ë pandava", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_membrane": "<PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Ohtë polca<PERSON>", "item.minecraft.piglin_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.piglin_banner_pattern.desc": "Mundo", "item.minecraft.piglin_banner_pattern.new": "Ocantië: mundo", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON><PERSON> ascar<PERSON>", "item.minecraft.piglin_spawn_egg": "Ohtë <PERSON>", "item.minecraft.pillager_spawn_egg": "Pillager Spawn Egg", "item.minecraft.pink_bundle": "Fanyacarnë pocollë", "item.minecraft.pink_dye": "Fanyacarnë quilë", "item.minecraft.pink_harness": "Fanyacarnë lattasta", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as taucol<PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> nihta as taucol<PERSON>", "item.minecraft.poisonous_potato": "Hloirë<PERSON> cemorva", "item.minecraft.polar_bear_spawn_egg": "<PERSON><PERSON><PERSON> m<PERSON>", "item.minecraft.popped_chorus_fruit": "Sisinwa yávë nyello", "item.minecraft.porkchop": "Hrávë polco", "item.minecraft.potato": "Cemorva", "item.minecraft.potion": "<PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>a", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> v<PERSON>", "item.minecraft.potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON> y<PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON> m<PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON> moica lanti<PERSON>o", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON> l<PERSON>", "item.minecraft.potion.effect.thick": "Nelca yulda", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON>", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON> as n<PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON> n<PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.pottery_shard_archer": "<PERSON><PERSON><PERSON><PERSON><PERSON> as c<PERSON>", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.powder_snow_bucket": "<PERSON><PERSON> as mulo <PERSON><PERSON><PERSON>", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as m<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish": "Lingwë tiuyana", "item.minecraft.pufferfish_bucket": "Bucket of Pufferfish", "item.minecraft.pufferfish_spawn_egg": "Pufferfish Spawn Egg", "item.minecraft.pumpkin_pie": "<PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON>", "item.minecraft.purple_bundle": "Luicarnë pocollë", "item.minecraft.purple_dye": "Luicarnë quilë", "item.minecraft.purple_harness": "Luicarnë lattasta", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "Hrávë lopo", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON> lopo", "item.minecraft.rabbit_hide": "<PERSON><PERSON> lopo", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_stew": "Sulpa lopo", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON>rova netwë", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_copper": "Altamna urus", "item.minecraft.raw_gold": "Altamna malta", "item.minecraft.raw_iron": "Altamn' anga", "item.minecraft.recovery_compass": "Mententar entulessëo", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> pocoll<PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON> quilë", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> la<PERSON>", "item.minecraft.redstone": "<PERSON><PERSON>", "item.minecraft.resin_brick": "<PERSON><PERSON>", "item.minecraft.resin_clump": "<PERSON><PERSON> su<PERSON>io", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> netwë", "item.minecraft.rotten_flesh": "Saura hrávë", "item.minecraft.saddle": "Hanwa", "item.minecraft.salmon": "Alaxë", "item.minecraft.salmon_bucket": "<PERSON><PERSON> as al<PERSON><PERSON>", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "Tirmova netwë", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "Cemnarwa netwë", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as mass<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as mass<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shears": "Aucirma", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as alda", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as alda", "item.minecraft.shield": "<PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON>a", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON> sanda", "item.minecraft.shield.gray": "Sinda sanda", "item.minecraft.shield.green": "<PERSON><PERSON> sanda", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> sanda", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON> sanda", "item.minecraft.shield.magenta": "<PERSON><PERSON>a sanda", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON> sanda", "item.minecraft.shield.pink": "Fanyacarnë sanda", "item.minecraft.shield.purple": "Luica<PERSON><PERSON> sanda", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>a", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> sanda", "item.minecraft.shield.yellow": "<PERSON><PERSON> sanda", "item.minecraft.shulker_shell": "Hyalma hyul<PERSON>o", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>ë <PERSON>", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Quilda netwë", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON><PERSON> te<PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.skeleton_spawn_egg": "Ohtë axoquenwa", "item.minecraft.skull_banner_pattern": "Ocantië la<PERSON>nno", "item.minecraft.skull_banner_pattern.desc": "Canta coropëo", "item.minecraft.skull_banner_pattern.new": "Ocantië: canta coropëo", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "item.minecraft.slime_ball": "Mehelecoron", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON>ë ma<PERSON>", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Napánima se:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Na<PERSON><PERSON> nihta hya maril", "item.minecraft.smithing_template.armor_trim.applies_to": "Varma", "item.minecraft.smithing_template.armor_trim.base_slot_description": "<PERSON><PERSON><PERSON> satta varmo", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON><PERSON> ar mariler", "item.minecraft.smithing_template.ingredients": "Sundi:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Napanë minulda ne<PERSON>ito", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Napanë varma hya carma tin<PERSON>o", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.upgrade": "Aryatalë: ", "item.minecraft.sniffer_spawn_egg": "Ohtë nustarwa", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as nustar", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON> as nustar", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Nengweva netwë", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Spawn Egg", "item.minecraft.snowball": "Loscoron", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> pirin", "item.minecraft.spider_eye": "<PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "Nelmava netwë", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> y<PERSON>a", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> yulda na<PERSON>", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> yulda ha<PERSON>", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a m<PERSON>", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON> yulda ve<PERSON>", "item.minecraft.splash_potion.effect.invisibility": "Hátima yulda úcénimiëo", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> yulda capi<PERSON>o", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> yulda vili<PERSON>o", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a al<PERSON>", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON> senya yulda", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON> yulda moriceno", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a <PERSON>", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a nest<PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "H<PERSON><PERSON>ma yulda moica lanti<PERSON>o", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a <PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a p<PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> yulda linti<PERSON>o", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON> nelca y<PERSON>a", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON> yulda sand<PERSON>", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON> as n<PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON>ma yulda néfitiëo <PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a lo<PERSON>", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a h<PERSON>", "item.minecraft.spruce_boat": "<PERSON>nt<PERSON> s<PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON>ntë s<PERSON> as tauc<PERSON><PERSON>", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.stick": "Olba", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON><PERSON> ondo", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> ondo", "item.minecraft.stone_pickaxe": "Ondopelet ondo", "item.minecraft.stone_shovel": "<PERSON><PERSON> ondo", "item.minecraft.stone_sword": "<PERSON><PERSON> ondo", "item.minecraft.stray_spawn_egg": "Stray Spawn Egg", "item.minecraft.strider_spawn_egg": "Ohtë telcontarwa", "item.minecraft.string": "Lia", "item.minecraft.sugar": "Lís", "item.minecraft.suspicious_stew": "Suspicious Stew", "item.minecraft.sweet_berries": "Lipsior", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> as quaccas", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>ë quaccaswa", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> netwë", "item.minecraft.tipped_arrow": "Tumyanwa pilin", "item.minecraft.tipped_arrow.effect.awkward": "Tumyanwa pilin", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> as h<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON>rnorn<PERSON>", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON> ha<PERSON>", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.infested": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON> cap<PERSON>", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON> vili<PERSON>", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Tumyanwa pilin", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON> m<PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON> hloimo", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON>lin moica lanti<PERSON>o", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON> lint<PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Tumyanwa pilin", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON>lin ungolion", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.tnt_minecart": "Raxa as TNT", "item.minecraft.torchflower_seeds": "<PERSON><PERSON>lló<PERSON>", "item.minecraft.totem_of_undying": "Totem of Undying", "item.minecraft.trader_llama_spawn_egg": "Ohtë mancarwa lamava", "item.minecraft.trial_key": "<PERSON><PERSON> r<PERSON>", "item.minecraft.trident": "Nelcarca", "item.minecraft.tropical_fish": "Tropical Fish", "item.minecraft.tropical_fish_bucket": "Bucket of Tropical Fish", "item.minecraft.tropical_fish_spawn_egg": "Tropical Fish Spawn Egg", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.turtle_scute": "<PERSON>", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON>ó<PERSON>", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Tarastarwa netwë", "item.minecraft.vex_spawn_egg": "<PERSON>të <PERSON>", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.vindicator_spawn_egg": "Vindicator Spawn Egg", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON> ran<PERSON> man<PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "Ortirmova netwë", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON><PERSON> hwan o<PERSON>", "item.minecraft.water_bucket": "<PERSON><PERSON> as n<PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Telcontarwa netwë", "item.minecraft.wheat": "Massalquë", "item.minecraft.wheat_seeds": "<PERSON><PERSON>", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pocollë", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON> quilë", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> latta<PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Lómava netwë", "item.minecraft.wind_charge": "Hwesta", "item.minecraft.witch_spawn_egg": "Ohtë curuniva", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON><PERSON>axoquen<PERSON>", "item.minecraft.wither_spawn_egg": "Wither Spawn Egg", "item.minecraft.wolf_armor": "<PERSON><PERSON> r<PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON>ë <PERSON>", "item.minecraft.wooden_axe": "<PERSON><PERSON> p<PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON> sa<PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON>", "item.minecraft.writable_book": "Parma ar tecil", "item.minecraft.written_book": "<PERSON><PERSON><PERSON> parma", "item.minecraft.yellow_bundle": "<PERSON><PERSON>", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Ohtë urcoya roccóva", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON><PERSON> u<PERSON>ó<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Ohtë urcoya opelemóva", "item.minecraft.zombified_piglin_spawn_egg": "Ohtë urcoya Pingilinwa", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "Amboressë:", "item.modifiers.feet": "Talussë:", "item.modifiers.hand": "When held:", "item.modifiers.head": "Caressë:", "item.modifiers.legs": "Telcutsë:", "item.modifiers.mainhand": "When in Main Hand:", "item.modifiers.offhand": "When in Off Hand:", "item.modifiers.saddle": "<PERSON><PERSON> han<PERSON>:", "item.nbt_tags": "NBT: tag(i) %s", "item.op_block_warning.line1": "Yé:", "item.op_block_warning.line2": "Yuhtië sina nato quí tulya axan-carienna", "item.op_block_warning.line3": "<PERSON>va yuhta sa laqui istalyë i imissë!", "item.unbreakable": "Úrácima", "itemGroup.buildingBlocks": "<PERSON><PERSON>", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON><PERSON> ronwar", "itemGroup.combat": "Mahtië", "itemGroup.consumables": "<PERSON><PERSON>", "itemGroup.crafting": "Tamië", "itemGroup.foodAndDrink": "<PERSON><PERSON> ar yuldar", "itemGroup.functional": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "itemGroup.hotbar": "<PERSON><PERSON><PERSON> f<PERSON>", "itemGroup.ingredients": "<PERSON><PERSON>", "itemGroup.inventory": "Aurië", "itemGroup.natural": "<PERSON><PERSON><PERSON>", "itemGroup.op": "<PERSON><PERSON>", "itemGroup.redstone": "<PERSON><PERSON>", "itemGroup.search": "Cesë nati", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Tam<PERSON>", "item_modifier.unknown": "Unknown item modifier: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON><PERSON> ana:", "jigsaw_block.generate": "Onta", "jigsaw_block.joint.aligned": "Voronda", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Nostalë limëo:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON>", "jigsaw_block.levels": "Tyeller: %s", "jigsaw_block.name": "Essë:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "Celwi:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "Essë i <PERSON>htëo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "Nalanta/nancarë", "key.back": "<PERSON><PERSON>", "key.categories.creative": "Creative Mode", "key.categories.gameplay": "Tyalessë", "key.categories.inventory": "Aurië", "key.categories.misc": "Hyanë", "key.categories.movement": "Levë", "key.categories.multiplayer": "Linquen", "key.categories.ui": "Turfanwa", "key.chat": "<PERSON><PERSON><PERSON> <PERSON>", "key.command": "<PERSON><PERSON><PERSON> axan", "key.drop": "Etehatë cilina nat", "key.forward": "Menë ompa", "key.fullscreen": "Toggle Fullscreen", "key.hotbar.1": "1ya estatië fertémo", "key.hotbar.2": "2ëa estatië fertémo", "key.hotbar.3": "3ëa estatië fertémo", "key.hotbar.4": "4ëa estatië fertémo", "key.hotbar.5": "5ëa estatië fertémo", "key.hotbar.6": "6ëa estatië fertémo", "key.hotbar.7": "7ëa estatië fertémo", "key.hotbar.8": "8ëa estatië fertémo", "key.hotbar.9": "9ëa estatië fertémo", "key.inventory": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "key.jump": "Capë", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Notessëa 0", "key.keyboard.keypad.1": "Notessëa 1", "key.keyboard.keypad.2": "Notessëa 2", "key.keyboard.keypad.3": "Notessëa 3", "key.keyboard.keypad.4": "Notessëa 4", "key.keyboard.keypad.5": "Notessëa 5", "key.keyboard.keypad.6": "Notessëa 6", "key.keyboard.keypad.7": "Notessëa 7", "key.keyboard.keypad.8": "Notessëa 8", "key.keyboard.keypad.9": "Notessëa 9", "key.keyboard.keypad.add": "Notessëa +", "key.keyboard.keypad.decimal": "Notessëa tixë", "key.keyboard.keypad.divide": "Notessëa /", "key.keyboard.keypad.enter": "Notessëa Enter", "key.keyboard.keypad.equal": "Notessëa =", "key.keyboard.keypad.multiply": "Notessëa *", "key.keyboard.keypad.subtract": "Notessëa -", "key.keyboard.left": "Hyar<PERSON>lin", "key.keyboard.left.alt": "Hyarya Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Hyarya Control", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.left.win": "<PERSON><PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "Forpilin", "key.keyboard.right.alt": "Forya Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Forya Control", "key.keyboard.right.shift": "Forya Shift", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Space", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Lamartain<PERSON>", "key.keyboard.up": "Ampilin", "key.keyboard.world.1": "Ambar 1", "key.keyboard.world.2": "Ambar 2", "key.left": "<PERSON><PERSON>", "key.loadToolbarActivator": "<PERSON><PERSON><PERSON> ferté<PERSON>", "key.mouse": "Tolma %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> to<PERSON>", "key.mouse.middle": "<PERSON><PERSON><PERSON> tolma", "key.mouse.right": "<PERSON><PERSON> tolma", "key.pickItem": "<PERSON><PERSON><PERSON> ronwa", "key.playerlist": "Combë tyalindoron", "key.quickActions": "<PERSON><PERSON><PERSON>", "key.right": "<PERSON><PERSON>", "key.saveToolbarActivator": "<PERSON><PERSON> f<PERSON>", "key.screenshot": "Take Screenshot", "key.smoothCamera": "Toggle Cinematic Camera", "key.sneak": "Hlicë", "key.socialInteractions": "Fanwa ó<PERSON>ë lancarion", "key.spectatorOutlines": "Highlight Players (Spectators)", "key.sprint": "Norë", "key.swapOffhand": "Swap Item With Off Hand", "key.togglePerspective": "Toggle Perspective", "key.use": "Yu<PERSON> nat/Sasta ronwa", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "<PERSON><PERSON>", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Tarmë", "known_server_link.support": "Support", "known_server_link.website": "Natsemen", "lanServer.otherPlayers": "<PERSON><PERSON><PERSON>", "lanServer.port": "Port Number", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Sina londë lá mára.\nÁ hehta i cumna colca, hya á tecë nótë, imbë %s ar %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Sina londë lá férima.\nÁ hehta i cumna colca, hya á tecë hyana nótë, imbë %s ar %s.", "lanServer.scanning": "Cestëan t<PERSON>mi <PERSON>", "lanServer.start": "<PERSON><PERSON> ambar <PERSON>", "lanServer.title": "Ambar <PERSON>", "language.code": "qya", "language.name": "Quenya", "language.region": "<PERSON>rda", "lectern.take_book": "Mapa parma", "loading.progress": "%s%%", "mco.account.privacy.info": "Á henta ambë pá Mojang ar axani aquapahtiëo", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.\n\nIf you have an older Minecraft account (you log in with your username), you need to migrate the account to a Mojang account in order to access Realms.", "mco.account.update": "Ceuta i fëa", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "Caraitië tyalindoron", "mco.backup.button.download": "Cavë i métima", "mco.backup.button.reset": "Entulca i ambar", "mco.backup.button.restore": "Nancavë", "mco.backup.button.upload": "Menta ambar", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Rehtaren (%s)", "mco.backup.entry.description": "Ostecië", "mco.backup.entry.enabledPack": "Caraiti ocombi", "mco.backup.entry.gameDifficulty": "Hrangië", "mco.backup.entry.gameMode": "Tyalmelë", "mco.backup.entry.gameServerVersion": "<PERSON><PERSON><PERSON> t<PERSON>o serveress<PERSON>", "mco.backup.entry.name": "Essë", "mco.backup.entry.seed": "Erdë", "mco.backup.entry.templateName": "Essë can<PERSON>", "mco.backup.entry.undefined": "Lamartanwa vistë", "mco.backup.entry.uploaded": "Mentanwa", "mco.backup.entry.worldType": "Nostalë ambaro", "mco.backup.generate.world": "Ont' ambar", "mco.backup.info.title": "Visti i nöa rehtarénello", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "Sina Rëalm penë rehtareni sí.", "mco.backup.restoring": "Nantulië Rëalmelyo", "mco.backup.unknown": "LASINWA", "mco.brokenworld.download": "Cavë", "mco.brokenworld.downloaded": "C<PERSON><PERSON>", "mco.brokenworld.message.line1": "Á entulca hya cilë hyana ambar, mecin.", "mco.brokenworld.message.line2": "Lertal cavë i ambar erquenna.", "mco.brokenworld.minigame.title": "Sina tyalmellë lá tuluhtina", "mco.brokenworld.nonowner.error": "Á hora i entulcalë i rëalmeva ambaro", "mco.brokenworld.nonowner.title": "<PERSON><PERSON> y<PERSON> lang<PERSON>", "mco.brokenworld.play": "Tyalë", "mco.brokenworld.reset": "Entulca", "mco.brokenworld.title": "Silúmëa ambarelya lá tuluhtina", "mco.client.incompatible.msg.line1": "Ce<PERSON>elya l<PERSON> má<PERSON>min.", "mco.client.incompatible.msg.line2": "<PERSON> yuhta silúmëa le<PERSON>, mecin.", "mco.client.incompatible.msg.line3": "<PERSON><PERSON> \"snapshot\" l<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>.", "mco.client.incompatible.title": "Celient lá mára sís!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "Silúmëa", "mco.configure.world.activityfeed.disabled": "Player feed temporarily disabled", "mco.configure.world.backup": "Rehtareni i ambaro", "mco.configure.world.buttons.activity": "Caraitië tyalindoron", "mco.configure.world.buttons.close": "Sacë i rëalm lúmëavë", "mco.configure.world.buttons.delete": "<PERSON><PERSON>", "mco.configure.world.buttons.done": "Carina", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "Minasë tyalindo", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON> cilmi", "mco.configure.world.buttons.newworld": "<PERSON><PERSON> ambar", "mco.configure.world.buttons.open": "<PERSON><PERSON><PERSON> i rëalm", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>o", "mco.configure.world.buttons.players": "Tyalindor", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Entulca i ambar", "mco.configure.world.buttons.save": "Renë", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Hilië", "mco.configure.world.buttons.switchminigame": "Vista tyalmellë", "mco.configure.world.close.question.line1": "R<PERSON><PERSON><PERSON><PERSON>.", "mco.configure.world.close.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Lúmëa sacië i Rëalmo...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>", "mco.configure.world.delete.button": "Haita i rëalm", "mco.configure.world.delete.question.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON> nauva haitina tenno<PERSON>", "mco.configure.world.delete.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.description": "Ostecië i rëalmo", "mco.configure.world.edit.slot.name": "I essë ambaro", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>i an silúmëa ambarelya veryandë ná", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>i an silúmëa ambarelya tuvië ná", "mco.configure.world.edit.subscreen.inspiration": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>i an silúmëa ambarelya súnalë ná", "mco.configure.world.forceGameMode": "Sahta i tyalmelë", "mco.configure.world.invite.narration": "<PERSON>l vinyë minasie(r) %s", "mco.configure.world.invite.profile.name": "Essë", "mco.configure.world.invited": "Minasinwa", "mco.configure.world.invited.number": "Minasinwa (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON> y<PERSON>", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Qui etelelyal sina Rëalmello, en lá cenuvalyes laqui mo emminasë lye", "mco.configure.world.leave.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.loading": "Ferië i rëalmo", "mco.configure.world.location": "Nómë", "mco.configure.world.minigame": "Current: %s", "mco.configure.world.name": "Essë i rëalmo", "mco.configure.world.opening": "Latië i Rëalmo...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON> as sin' essë lá nanwa", "mco.configure.world.players.inviting": "Minasië i tyalindo...", "mco.configure.world.players.title": "Tyalindor", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Ambarelya nauva ontanwa ar i silúmëa ambar nauva vanwa", "mco.configure.world.reset.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "Mauya lyen samë véra colca mairion meter tyalë sina Rëalmessë", "mco.configure.world.resourcepack.question.line2": "Ma merilyë cavitas ar tyalë?", "mco.configure.world.restore.download.question.line1": "I ambar nauva cavina ar napanina erquenwë ambarinnar.", "mco.configure.world.restore.download.question.line2": "Ma me<PERSON> cacar<PERSON>?", "mco.configure.world.restore.question.line1": "<PERSON><PERSON><PERSON><PERSON> nan<PERSON>va tarmenna ollo '%s' (%s)", "mco.configure.world.restore.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Ambar %s", "mco.configure.world.slot.empty": "C<PERSON>na", "mco.configure.world.slot.switch.question.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON> nauva vistaina hyana ambarenna", "mco.configure.world.slot.switch.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.configure.world.slot.tooltip": "Vista ambarenna", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Vista tyalmellenna", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON><PERSON> celvar", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Etyalë vëor", "mco.configure.world.spawnProtection": "Cauma nómessë enontiëo", "mco.configure.world.spawn_toggle.message": "Turning this option off will remove all existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Turning this option off will remove all existing entities of that type, like Villagers", "mco.configure.world.spawn_toggle.title": "Yé!", "mco.configure.world.status": "Tarmë", "mco.configure.world.subscription.day": "ré", "mco.configure.world.subscription.days": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.expired": "Firinwa", "mco.configure.world.subscription.extend": "Taita i hilië", "mco.configure.world.subscription.less_than_a_day": "Less than a day", "mco.configure.world.subscription.month": "rán<PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON><PERSON><PERSON><PERSON> mi", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.start": "<PERSON><PERSON>", "mco.configure.world.subscription.tab": "Hilië", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "mco.configure.world.subscription.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "Onta ambar", "mco.configure.world.switch.slot.subtitle": "<PERSON>a ambar cumna, á cilë yanen ontauval ambarelya", "mco.configure.world.title": "Catë rëalm:", "mco.configure.world.uninvite.player": "Ma nalyë tanca i meril laminasë '%s'?", "mco.configure.world.uninvite.question": "Ma nalyë tanca i meril laminasë", "mco.configure.worlds.title": "Ambari", "mco.connect.authorizing": "Mittarë...", "mco.connect.connecting": "Limyalë i Rëalmenna...", "mco.connect.failed": "Limyalë i Rëalmenna loitina", "mco.connect.region": "Server region: %s", "mco.connect.success": "Carina", "mco.create.world": "Onta", "mco.create.world.error": "Mauya lyen tecë esselya!", "mco.create.world.failed": "Ambar-ontalë loitanë!", "mco.create.world.reset.title": "Ontalë ambaro...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON> meril, á cilë i ambar i caituval vinya Rëalmelyassë", "mco.create.world.wait": "Ontalë i Rëalmo...", "mco.download.cancelled": "<PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "I ambar ya meril cavë höa lá %s", "mco.download.confirmation.line2": "Lá ecuva lyen emmenta sina ambar Rëalmelyanna", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "<PERSON><PERSON><PERSON> carina", "mco.download.downloading": "Cavië", "mco.download.extracting": "Ettultië", "mco.download.failed": "Cavië loitina", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON> ca<PERSON>", "mco.download.resourcePack.fail": "Cavië i mairion colco loitina!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Cavië i métima ambaro", "mco.error.invalid.session.message": "Á ricë atayesta Minecraft, mecin", "mco.error.invalid.session.title": "I tyalië lá mára", "mco.errorMessage.6001": "Celient yára", "mco.errorMessage.6002": "<PERSON><PERSON><PERSON> y<PERSON> l<PERSON> ca<PERSON>", "mco.errorMessage.6003": "<PERSON><PERSON><PERSON> cavi<PERSON>a", "mco.errorMessage.6004": "<PERSON><PERSON><PERSON> mentalion anyaina", "mco.errorMessage.6005": "<PERSON><PERSON> tap<PERSON>", "mco.errorMessage.6006": "<PERSON><PERSON> y<PERSON> lang<PERSON>", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON> <PERSON><PERSON> r<PERSON><PERSON> nótimë langë", "mco.errorMessage.6008": "I essë i rëalmo lá mára", "mco.errorMessage.6009": "I ostecië i rëalmo lá mára", "mco.errorMessage.connectionFailure": "Loima. Á ataricë cato, mecin.", "mco.errorMessage.generic": "Loima: ", "mco.errorMessage.initialize.failed": "Yestië i rëalmo loitina", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "Loima (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Rëalmi (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "<PERSON><PERSON><PERSON><PERSON> caraiti n<PERSON> si<PERSON>.\nÁ ricë atalimya R<PERSON><PERSON><PERSON> rongo, mecin.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Sá", "mco.info": "Istalë!", "mco.invited.player.narration": "%s minasinwa", "mco.invites.button.accept": "Cavë", "mco.invites.button.reject": "Auquerë", "mco.invites.nopending": "No pending invites!", "mco.invites.pending": "Vinyë minasier!", "mco.invites.title": "Pending Invites", "mco.minigame.world.changeButton": "<PERSON><PERSON><PERSON> hyana t<PERSON>", "mco.minigame.world.info.line1": "Sina vistuva ambarelya tyal<PERSON>na lúmëavë!", "mco.minigame.world.info.line2": "<PERSON><PERSON><PERSON><PERSON> nan<PERSON><PERSON> celuva ambar<PERSON>, l<PERSON> pentaila aima.", "mco.minigame.world.noSelection": "<PERSON>yë cilë", "mco.minigame.world.restore": "Telië t<PERSON>...", "mco.minigame.world.restore.question.line1": "I tyalmellë teluva ar Rëalmelya nanwenuva.", "mco.minigame.world.restore.question.line2": "Ma nalyë tanca i merilyë cacarë?", "mco.minigame.world.selected": "<PERSON><PERSON><PERSON>:", "mco.minigame.world.slot.screen.title": "Vistalë ambaro...", "mco.minigame.world.startButton": "Vista", "mco.minigame.world.starting.screen.title": "<PERSON>ta t<PERSON>...", "mco.minigame.world.stopButton": "Telya i tyalmellë", "mco.minigame.world.switch.new": "<PERSON><PERSON><PERSON> hyana tyal<PERSON>?", "mco.minigame.world.switch.title": "Vista tyalmellë", "mco.minigame.world.title": "Vista rëalmello t<PERSON>na", "mco.news": "<PERSON><PERSON>", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Latya i limë", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "I r<PERSON><PERSON>m sa<PERSON>wa", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Tuvië", "mco.reset.world.generate": "<PERSON><PERSON> ambar", "mco.reset.world.inspiration": "Súnalë", "mco.reset.world.resetting.screen.title": "Entulcalë ambaro...", "mco.reset.world.seed": "Erdë (nírima)", "mco.reset.world.template": "Ambarcantier", "mco.reset.world.title": "Entulca i ambar", "mco.reset.world.upload": "Menta ambar", "mco.reset.world.warning": "Sinen i silúmëa ambar Rëalmelyo nauva haitina", "mco.selectServer.buy": "Homanca rëalm!", "mco.selectServer.close": "Sacë", "mco.selectServer.closed": "Sacinwa Rëalm", "mco.selectServer.closeserver": "Sacë i Rëalm", "mco.selectServer.configure": "<PERSON><PERSON> rëalm", "mco.selectServer.configureRealm": "Catë i Rëalm", "mco.selectServer.create": "Onta rëalm", "mco.selectServer.create.subtitle": "Á cilë i ambar i caituval vinya Rëalmelyassë", "mco.selectServer.expired": "Firinwa <PERSON>", "mco.selectServer.expiredList": "Hilielya ifírië", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON>", "mco.selectServer.expiredTrial": "Ricielya <PERSON>í<PERSON>", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON> enar", "mco.selectServer.expires.days": "Firuva apa rér %s", "mco.selectServer.expires.soon": "<PERSON>ruva rongo", "mco.selectServer.leave": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.loading": "<PERSON><PERSON><PERSON> rëalmio<PERSON>", "mco.selectServer.mapOnlySupportedForVersion": "Sin' ambar lá tuluhtina mi %s", "mco.selectServer.minigame": "Tyalmellë:", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Sina tyalmellë lá tyálima mi %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "Sartë:", "mco.selectServer.open": "Latya i Rëalm", "mco.selectServer.openserver": "Latya i Rëalm", "mco.selectServer.play": "Tyalë", "mco.selectServer.popup": "Rëalmi – varna ar asamahtë lé i carë cárima 'online' tyalië ambaressë mi Minecraft ó málor quëan. Linë tyalmeller ar vérë ambari tuluhtinë! Rië mauya i öamon rëalmo anta telpë.", "mco.selectServer.purchase": "Napanë rëalm", "mco.selectServer.trial": "Ricë!", "mco.selectServer.uninitialized": "<PERSON><PERSON><PERSON> meter yesta vinya Rëalmelya!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Rëalmi sí férimë mi Snapshots apa i Snapshot 23w41a. Ilya hilië Rëalmion anta lyen Snapshot rëalm ya satya ara i sanya Java rëalm!", "mco.snapshotRealmsPopup.title": "Rëalmi sí férimë mi Snapshots", "mco.snapshotRealmsPopup.urlText": "Ambë", "mco.template.button.publisher": "Ontar", "mco.template.button.select": "Cilë", "mco.template.button.trailer": "<PERSON><PERSON><PERSON>", "mco.template.default.name": "Ambarcantië", "mco.template.info.tooltip": "Natsemen <PERSON>", "mco.template.name": "Cantië", "mco.template.select.failure": "We couldn't retrieve the list of content for this category.\nPlease check your internet connection, or try again later.", "mco.template.select.narrate.authors": "Ontari: %s", "mco.template.select.narrate.version": "lerië %s", "mco.template.select.none": "Oops, it looks like this content category is currently empty.\nPlease check back later for new content, or if you're a creator,\n%s.", "mco.template.select.none.linkTitle": "consider submitting something yourself", "mco.template.title": "Ambarcantier", "mco.template.title.minigame": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "Sáquétanyë", "mco.terms.buttons.disagree": "<PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON><PERSON><PERSON><PERSON> axani y<PERSON>o", "mco.terms.sentence.2": "Minecraft Rëalmion", "mco.terms.title": "<PERSON><PERSON><PERSON>", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "silumë", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "Ma meril samë Rëalmelya?", "mco.trial.message.line2": "<PERSON><PERSON><PERSON> sís meter henta ambë!", "mco.upload.button.name": "Menta", "mco.upload.cancelled": "<PERSON>ë <PERSON>", "mco.upload.close.failure": "Sacië R<PERSON> loiti<PERSON>, á ataricë cato, mecin", "mco.upload.done": "<PERSON><PERSON> carinwa", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Mentalë loitina! (%s)", "mco.upload.failed.too_big.description": "I cilinwa ambar höa langë. I antára höass<PERSON> %s ná.", "mco.upload.failed.too_big.title": "<PERSON><PERSON> höa lang<PERSON>", "mco.upload.hardcore": "Mentalë anastornë ambaron lacárima!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON>", "mco.upload.select.world.none": "<PERSON> erquenwa ambar hirina!", "mco.upload.select.world.subtitle": "Á cilë erquenwa ambar mentalen, mecin", "mco.upload.select.world.title": "Menta ambar", "mco.upload.size.failure.line1": "'%s' höa langë ná!", "mco.upload.size.failure.line2": "Nás %s. I ant<PERSON>ra h<PERSON> %s ná.", "mco.upload.uploading": "Mentalë '%s'", "mco.upload.verifying": "Tyastalë ambarelyo", "mco.version": "Lerië: %s", "mco.warning": "Yé!", "mco.worldSlot.minigame": "Tyalmellë", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "<PERSON><PERSON><PERSON>", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Fanwa <PERSON>", "menu.modded": " (Tyalmë vistaina)", "menu.multiplayer": "Linquen", "menu.online": "Rëalmi Minecraft", "menu.options": "<PERSON><PERSON><PERSON>...", "menu.paused": "Tyalmë pustina", "menu.playdemo": "<PERSON><PERSON><PERSON> lanwassëa ambaressë", "menu.playerReporting": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "menu.preparingSpawn": "Ferië nostanómëo: %s%%", "menu.quick_actions": "Lintë cardar...", "menu.quick_actions.title": "<PERSON><PERSON><PERSON>", "menu.quit": "<PERSON><PERSON><PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON><PERSON><PERSON> loima<PERSON>", "menu.resetdemo": "Entulca lanwas<PERSON>ëa ambar", "menu.returnToGame": "<PERSON><PERSON><PERSON> tyal<PERSON>na", "menu.returnToMenu": "Renë ar etelelya <PERSON>", "menu.savingChunks": "<PERSON><PERSON><PERSON>", "menu.savingLevel": "<PERSON><PERSON><PERSON> ambaro", "menu.sendFeedback": "Ósanwë-menta", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "<PERSON><PERSON><PERSON>", "menu.singleplayer": "Erquen", "menu.working": "Mólë...", "merchant.deprecated": "Opelemo cé enquanta hauralta yullumë auressë.", "merchant.level.1": "Yes<PERSON>o", "merchant.level.2": "Parmo", "merchant.level.3": "Mótamo", "merchant.level.4": "<PERSON>lm<PERSON>", "merchant.level.5": "Ingolmo", "merchant.title": "%s – %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Nirë %1$s meter núya", "multiplayer.applyingPack": "Yuhtië i colco mairion", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Authentication servers are down. Please try again later, sorry!", "multiplayer.disconnect.bad_chat_index": "<PERSON><PERSON> vanwa hya empaninwa menta i <PERSON>", "multiplayer.disconnect.banned": "Nál avanwa sina serveressë", "multiplayer.disconnect.banned.expiration": "\nAvanwië nauva aucolinwa fëalyallo mi %s", "multiplayer.disconnect.banned.reason": "Nál avanwa sina serveressë.\nCahta: %s", "multiplayer.disconnect.banned_ip.expiration": "\nAvanwië nauva aucolinwa fëalyallo mi %s", "multiplayer.disconnect.banned_ip.reason": "IP tengesselya avanwa sina serveressë.\nCahta: %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "Mittanelyë hyana nó<PERSON>o", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "Vilië lá caraitë sina serveressë", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.idling": "Lacaraitielya n<PERSON> anda langë!", "multiplayer.disconnect.illegal_characters": "Illegal characters in chat", "multiplayer.disconnect.incompatible": "Celient lá mára sís! Á yuhta %s, mecin", "multiplayer.disconnect.invalid_entity_attacked": "Attempting to attack an invalid entity", "multiplayer.disconnect.invalid_packet": "I server mentanë ocombë i lá mára", "multiplayer.disconnect.invalid_player_data": "Istaler pá tyalindo lár már<PERSON>", "multiplayer.disconnect.invalid_player_movement": "Ocombë levion tyalindovë i lá mára cavina", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Ocombë levion menwavë i lá mára cavina", "multiplayer.disconnect.ip_banned": "IP tengesselya avanwa sina serveressë", "multiplayer.disconnect.kicked": "<PERSON><PERSON><PERSON> lye", "multiplayer.disconnect.missing_tags": "Incomplete set of tags received from server.\nPlease contact server operator.", "multiplayer.disconnect.name_taken": "<PERSON>a es<PERSON><PERSON> yuh<PERSON> tensi", "multiplayer.disconnect.not_whitelisted": "Esselya lá ninquicombessë sina servero!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "I server penquanta!", "multiplayer.disconnect.server_shutdown": "Server sacina", "multiplayer.disconnect.slow_login": "<PERSON><PERSON><PERSON><PERSON> nánë anda langë", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Unexpected custom data from client", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Tyastalë esselyo loitina!", "multiplayer.downloadingStats": "Retrieving statistics...", "multiplayer.downloadingTerrain": "Feryalë norcanto...", "multiplayer.lan.server_found": "Vinya server hirinwa: %s", "multiplayer.message_not_delivered": "Can't deliver chat message, check server logs: %s", "multiplayer.player.joined": "%s utúlië", "multiplayer.player.joined.renamed": "%s (yára %s) utúlië", "multiplayer.player.left": "%s lenwentë", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Caraiti tyalindor: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server <PERSON><PERSON><PERSON> y<PERSON> col<PERSON>to mairion", "multiplayer.requiredTexturePrompt.line1": "Sina server i<PERSON><PERSON> y<PERSON>ya colcalta mairion.", "multiplayer.requiredTexturePrompt.line2": "Auquerië i véra colco mairion auyantuva lye i serverello.", "multiplayer.socialInteractions.not_available": "<PERSON><PERSON><PERSON> lancari férimë nár linquenyë ambarissen rië", "multiplayer.status.and_more": "... ar %s ambë ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Yantalë serverenna l<PERSON> cárima", "multiplayer.status.cannot_resolve": "Atsintalë nasturessëo lá cárima", "multiplayer.status.finished": "Carinwa", "multiplayer.status.incompatible": "I lerië lá mára sís!", "multiplayer.status.motd.narration": "Menta servero: %s", "multiplayer.status.no_connection": "(<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Caraitë", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Ping carina sí...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s tyalindoron %s sís", "multiplayer.status.quitting": "Etelelië", "multiplayer.status.request_handled": "Iquista tarmëo mahtina", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Aliquiryaina tarm<PERSON> ca<PERSON>wa", "multiplayer.status.version.narration": "Lerië serverwa: %s", "multiplayer.stopSleeping": "<PERSON><PERSON> ca<PERSON>", "multiplayer.texturePrompt.failure.line1": "Server resource pack couldn't be applied", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "Sina server quet<PERSON> yuhtaitalya colcalta mairion nauva mára.", "multiplayer.texturePrompt.line2": "Ma merilyë cavë ar tulca sa luhtunen?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMenta serverello:\n%s", "multiplayer.title": "Tyalë linquenessë", "multiplayer.unsecureserver.toast": "I mentar sina serveressë cé vistainë nár, celuvë mentar quí lavélë", "multiplayer.unsecureserver.toast.title": "Chat messages can't be verified", "multiplayerWarning.check": "Áva apantaitas ata", "multiplayerWarning.header": "Caution: Third-Party Online Play", "multiplayerWarning.message": "Caution: Online play is offered by third-party servers that are not owned, operated, or supervised by Mojang Studios or Microsoft. During online play, you may be exposed to unmoderated chat messages or other types of user-generated content that may not be suitable for everyone.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - <PERSON>", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Tolma: %s", "narration.button.usage.focused": "<PERSON><PERSON><PERSON>ter meter vehta", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON> i hyarya meter vehta", "narration.checkbox": "Colca cilmëo: %s", "narration.checkbox.usage.focused": "Press Enter to toggle", "narration.checkbox.usage.hovered": "Left click to toggle", "narration.component_list.usage": "Press Tab to navigate to next element", "narration.cycle_button.usage.focused": "Press Enter to switch to %s", "narration.cycle_button.usage.hovered": "Left click to switch to %s", "narration.edit_box": "Teccolca: %s", "narration.item": "Nat: %s", "narration.recipe": "Carmen o %s", "narration.recipe.usage": "<PERSON>r<PERSON> i hyarya itan cilë", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON> i forya itan cenë ambë carmeni", "narration.selection.usage": "Press up and down buttons to move to another entry", "narration.slider.usage.focused": "<PERSON><PERSON><PERSON> hyarya hya forya tolma meter vista mirma", "narration.slider.usage.hovered": "Lucë i licil meter vista mirma", "narration.suggestion": "Cilinwa %s o %s: %s", "narration.suggestion.tooltip": "Cilinwa %s o %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "Asyassë", "narrator.button.difficulty_lock": "Tampë hrangwëo", "narrator.button.difficulty_lock.locked": "Alalehta", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON>", "narrator.button.language": "Lambë", "narrator.controls.bound": "%s ná martaina ve %s", "narrator.controls.reset": "Entulca tolma %s", "narrator.controls.unbound": "%s lá martaina", "narrator.joining": "Limyalë", "narrator.loading": "Feryalë: %s", "narrator.loading.done": "Carina", "narrator.position.list": "Cilina %s o %s", "narrator.position.object_list": "Selected row element %s out of %s", "narrator.position.screen": "<PERSON> fanwassë: %s o %s", "narrator.position.tab": "Cilina %s o %s", "narrator.ready_to_play": "Férima tyalien", "narrator.screen.title": "Penteramba", "narrator.screen.usage": "Use mouse cursor or Tab button to select element", "narrator.select": "Cilina: %s", "narrator.select.world": "Cilina %s, métima tyalmë: %s, %s, %s, lerië: %s", "narrator.select.world_info": "%s cilin<PERSON>, métima tyalmë: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON>", "optimizeWorld.confirm.description": "Sinen ricuvan aryata ambarelya. <PERSON>é ambarelya ná höa, i ricië nauva anda. Tyalë ambarelyassë cé nauva analinta yá ricië nauva carina, mal tyalë noë le<PERSON> céla carina. Ma nalyë tanca?", "optimizeWorld.confirm.proceed": "Onta rehtaren ar aryata", "optimizeWorld.confirm.title": "Aryata i ambar", "optimizeWorld.info.converted": "Aryatainë latsi: %s", "optimizeWorld.info.skipped": "Hehtainë latsi: %s", "optimizeWorld.info.total": "Notwë latsion: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Notië latsion...", "optimizeWorld.stage.failed": "Loitaina! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Aryatalë ambaro '%s'", "options.accessibility": "<PERSON><PERSON><PERSON>...", "options.accessibility.high_contrast": "High Contrast", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Accessibility Guide", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "Text Background", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Illomë", "options.accessibility.text_background_opacity": "Text Background Opacity", "options.accessibility.title": "<PERSON><PERSON><PERSON>", "options.allowServerListing": "Combi serverion lavinë", "options.allowServerListing.tooltip": "Essi tyal<PERSON>oron combissen serverion apantinë cé nauvar.\nQui sina cilmë lacaraitë, en esselya lauva apantina i combissen.", "options.ao": "<PERSON><PERSON>ë cali<PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "LÁ", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "<PERSON><PERSON><PERSON><PERSON>", "options.attackIndicator": "<PERSON><PERSON>", "options.audioDevice": "<PERSON><PERSON>", "options.audioDevice.default": "Sanya", "options.autoJump": "Auto-Jump", "options.autoSuggestCommands": "Asyaquetië axanion", "options.autosaveIndicator": "<PERSON><PERSON>", "options.biomeBlendRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON> yondion", "options.biomeBlendRadius.1": "Lá (analinta)", "options.biomeBlendRadius.11": "11x11 (maira)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (analta)", "options.biomeBlendRadius.3": "3x3 (linta)", "options.biomeBlendRadius.5": "5x5 (sanya)", "options.biomeBlendRadius.7": "7x7 (tára)", "options.biomeBlendRadius.9": "9x9 (ant<PERSON><PERSON>)", "options.chat": "<PERSON><PERSON><PERSON>...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "<PERSON>t <PERSON>: %s second(s)", "options.chat.delay_none": "<PERSON><PERSON>: None", "options.chat.height.focused": "Focused Height", "options.chat.height.unfocused": "Unfocused Height", "options.chat.line_spacing": "Line Spacing", "options.chat.links": "<PERSON>ir <PERSON>", "options.chat.links.prompt": "<PERSON><PERSON><PERSON> yá limi latinë", "options.chat.opacity": "Tercénim. tengw. mi nyatil", "options.chat.scale": "Chat Text Size", "options.chat.title": "<PERSON><PERSON><PERSON>", "options.chat.visibility": "<PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "Axani rië", "options.chat.width": "Paldassë", "options.chunks": "latsi %s", "options.clouds.fancy": "<PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON>", "options.controls": "Turmar...", "options.credits_and_attribution": "Credits & Attribution...", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Morininquë catafanwa", "options.darkMojangStudiosBackgroundColor.tooltip": "Vista i quilë Mojang Studios fanwassë, carnillo morinna.", "options.darknessEffectScale": "Darkness Pulsing", "options.darknessEffectScale.tooltip": "Controls how much the Darkness effect pulses when a Warden or Sculk Shrieker gives it to you.", "options.difficulty": "Hrangwë", "options.difficulty.easy": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Anastorna", "options.difficulty.normal": "Sanya", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "Hrangwë servero", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Directional Audio", "options.directionalAudio.off.tooltip": "Classic Stereo sound.", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Discret<PERSON>ing", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON> en<PERSON>", "options.entityShadows": "Entity Shadows", "options.font": "<PERSON><PERSON><PERSON>...", "options.font.title": "<PERSON><PERSON><PERSON>", "options.forceUnicodeFont": "Sahta Unicode", "options.fov": "Paldassë ceno", "options.fov.max": "Ampalda", "options.fov.min": "Sanya", "options.fovEffectScale": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> ceno", "options.fovEffectScale.tooltip": "Controls how much the field of view can change with gameplay effects.", "options.framerate": "FPS %s", "options.framerateLimit": "Anhalla FPS", "options.framerateLimit.max": "Úlanwa", "options.fullscreen": "<PERSON>uanta fanwa", "options.fullscreen.current": "Silúmëa", "options.fullscreen.entry": "%sx%s@%s (%sbiti)", "options.fullscreen.resolution": "Fullscreen Resolution", "options.fullscreen.unavailable": "Cilmë alaférima", "options.gamma": "Calimië", "options.gamma.default": "Sanya", "options.gamma.max": "Calima", "options.gamma.min": "Lilómëa", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Elmendëa!", "options.graphics.fabulous.tooltip": "%s graphics uses screen shaders for drawing weather, clouds, and particles behind translucent blocks and water.\nThis may severely impact performance for portable devices and 4K displays.", "options.graphics.fancy": "<PERSON><PERSON>", "options.graphics.fancy.tooltip": "Fancy graphics balances performance and quality for the majority of machines.\nWeather, clouds, and particles may not appear behind translucent blocks or water.", "options.graphics.fast": "<PERSON><PERSON>", "options.graphics.fast.tooltip": "Fast graphics reduces the amount of visible rain and snow.\nTransparency effects are disabled for various blocks such as leaves.", "options.graphics.warning.accept": "Cacarë ú tuluhtarëo", "options.graphics.warning.cancel": "<PERSON><PERSON>", "options.graphics.warning.message": "Your graphics device is detected as unsupported for the %s graphics option.\n\nYou may ignore this and continue, however support will not be provided for your device if you choose to use %s graphics.", "options.graphics.warning.renderer": "Renderma tuvina: [%s]", "options.graphics.warning.title": "Graphics Device Unsupported", "options.graphics.warning.vendor": "Carindo tuvina: [%s]", "options.graphics.warning.version": "Lerië OpenGL tuvina: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON><PERSON>", "options.guiScale.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON> tin<PERSON>i<PERSON> ar ítar", "options.hideLightningFlashes.tooltip": "Nuhtas i alcandë ítaron i menelessë. Ítar nauvar c<PERSON>im<PERSON> enera.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> es<PERSON>", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Nihta FPS yá", "options.inactivityFpsLimit.afk": "Lacaraitë", "options.inactivityFpsLimit.afk.tooltip": "Lanyas i emnotasta mi 30 yá i tyalmë lá tunta i súlë i tyalindo ter min lúmincë. Tá lanyasses mi 10 apa lúminci nertë.", "options.inactivityFpsLimit.minimized": "Pihtanwa", "options.inactivityFpsLimit.minimized.tooltip": "Lanyas i emnotasta rië yá i lattin pihtanwa ná.", "options.invertMouse": "<PERSON>tama nuquerna", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "<PERSON><PERSON><PERSON> sari (CJK) sanya sallessë.", "options.key.hold": "Mahtë", "options.key.toggle": "Toggle", "options.language": "Lambë...", "options.language.title": "Lambë", "options.languageAccuracyWarning": "(<PERSON><PERSON><PERSON> tér<PERSON> cé lauvar)", "options.languageWarning": "Lancolier altérë cé nauvar", "options.mainHand": "Héra má", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "Forma", "options.mipmapLevels": "<PERSON><PERSON> h<PERSON>", "options.modelPart.cape": "Collo", "options.modelPart.hat": "Carpë", "options.modelPart.jacket": "Vacco", "options.modelPart.left_pants_leg": "Hyarya telco", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "Forya telco", "options.modelPart.right_sleeve": "Forma", "options.mouseWheelSensitivity": "Scroll Sensitivity", "options.mouse_settings": "<PERSON><PERSON><PERSON> tentamo...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON>amo", "options.multiplayer.title": "<PERSON><PERSON><PERSON> lin<PERSON>...", "options.multiplier": "%sx", "options.music_frequency": "Rimbië lindalëo", "options.music_frequency.constant": "Vórima", "options.music_frequency.default": "Sanya", "options.music_frequency.frequent": "Hrímina", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Quentaro", "options.narrator.all": "Ethenta ilqua", "options.narrator.chat": "<PERSON>rra<PERSON>", "options.narrator.notavailable": "Alaférima", "options.narrator.off": "LÁ", "options.narrator.system": "Narrates System", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "LÁ", "options.off.composed": "%s: <PERSON><PERSON>", "options.on": "NÁ", "options.on.composed": "%s: <PERSON><PERSON>", "options.online": "Natsë...", "options.online.title": "<PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON> varna nyatil rië", "options.onlyShowSecureChat.tooltip": "Apanta rië mentar hyanë tyalindollon, i mentië i tyalindollo ná tyastaima, ar mentar lá vistainë.", "options.operatorItemsTab": "<PERSON><PERSON>", "options.particles": "<PERSON><PERSON><PERSON>", "options.particles.all": "All", "options.particles.decreased": "Nuhtainë", "options.particles.minimal": "Nótimë", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Carastar latsion", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Some actions within a chunk will recompile the chunk immediately. This includes block placing & destroying.", "options.prioritizeChunkUpdates.nearby": "Tapila aqua", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby chunks are always compiled immediately. This may impact game performance when blocks are placed or destroyed.", "options.prioritizeChunkUpdates.none": "Lanyaina", "options.prioritizeChunkUpdates.none.tooltip": "Nearby chunks are compiled in parallel threads. This may result in brief visual holes when blocks are destroyed.", "options.rawMouseInput": "Terya atamentië", "options.realmsNotifications": "<PERSON><PERSON> ar min<PERSON>er", "options.realmsNotifications.tooltip": "<PERSON><PERSON><PERSON> sinyar Rëalmion ar minasier i penterambassë, ar apantas emmantar tolmassë 'Rëalmi'.", "options.reducedDebugInfo": "Reduced Debug Info", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "Fanya-cénimië", "options.renderDistance": "<PERSON><PERSON><PERSON> ceno", "options.resourcepack": "Colcar mairion...", "options.rotateWithMinecart": "<PERSON><PERSON><PERSON> as raxar", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Distortion Effects", "options.screenEffectScale.tooltip": "Strength of nausea and Nether portal screen distortion effects.\nAt lower values, the nausea effect is replaced with a green overlay.", "options.sensitivity": "<PERSON><PERSON><PERSON>", "options.sensitivity.max": "AMLINTIË!!!", "options.sensitivity.min": "*yanga*", "options.showNowPlayingToast": "<PERSON><PERSON>ta essi lindalion", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.simulationDistance": "Hairië ovëantiëo", "options.skinCustomisation": "Vératalë helmo...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON> helmo", "options.sounds": "Linda<PERSON><PERSON> ar lámar...", "options.sounds.title": "<PERSON><PERSON><PERSON> l<PERSON> ar l<PERSON>", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "<PERSON><PERSON><PERSON> is<PERSON>", "options.telemetry.button.tooltip": "\"%s\" yorë i maurina istalë rië.\n\"%s\" yorë i nírima ar i maurina istalë.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "Il<PERSON>", "options.telemetry.state.minimal": "<PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "<PERSON><PERSON><PERSON>", "options.touchscreen": "Appafanwa Lé", "options.video": "<PERSON><PERSON><PERSON> ceno...", "options.videoTitle": "<PERSON><PERSON><PERSON> ceno", "options.viewBobbing": "<PERSON><PERSON> caro", "options.visible": "<PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent world corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "Ú rénëo!", "pack.available.title": "Férimë", "pack.copyFailure": "Renë ocombion loitina", "pack.dropConfirm": "Do you want to add the following packs to Minecraft?", "pack.dropInfo": "Lucë ar lerya cómi sina lattinna meter napanë ocombi", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(sasta cómi ocombion sís)", "pack.incompatible": "<PERSON><PERSON> mára s<PERSON>", "pack.incompatible.confirm.new": "This pack was made for a newer version of Minecraft and may not work correctly.", "pack.incompatible.confirm.old": "This pack was made for an older version of Minecraft and may no longer work correctly.", "pack.incompatible.confirm.title": "Are you sure you want to load this pack?", "pack.incompatible.new": "(Made for a newer version of Minecraft)", "pack.incompatible.old": "(Made for an older version of Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON> ha<PERSON> o<PERSON>ion", "pack.selected.title": "Cilinë", "pack.source.builtin": "built-in", "pack.source.feature": "caricanta", "pack.source.local": "nómëa", "pack.source.server": "server", "pack.source.world": "ambar", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albamo", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Tarwa", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Varocco", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON> mai nancarina", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Urwa coropë", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Amboremma", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Hrótaiwë", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Vistë", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Omentië", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Cemen", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON> cotto", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Filquë", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Hiri<PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "R<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Nucumnië", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Hísilómë", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Hómalmar", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Lango", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Polcemma", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Lún", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Lónë", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Onortanen rocco", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Falassë", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Fírimárë", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Coropë ar Meriller", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Ilqua <PERSON>", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "andúnë", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Erumë", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Nén", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Súrë", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Expected boolean", "parsing.bool.invalid": "Invalid boolean, expected 'true' or 'false' but found '%s'", "parsing.double.expected": "Expected double", "parsing.double.invalid": "Invalid double '%s'", "parsing.expected": "Expected '%s'", "parsing.float.expected": "Expected float", "parsing.float.invalid": "Invalid float '%s'", "parsing.int.expected": "Expected integer", "parsing.int.invalid": "Invalid integer '%s'", "parsing.long.expected": "Expected long", "parsing.long.invalid": "Invalid long '%s'", "parsing.quote.escape": "Invalid escape sequence '\\%s' in quoted string", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Lasinwa astaincë: %s", "permissions.requires.entity": "An entity is required to run this command here", "permissions.requires.player": "A player is required to run this command here", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Apa yulië:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unknown predicate: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Limyalë i rëalmenna loitina", "quickplay.error.realm_permission": "Limyalë i rëalmenna lánë lavina", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "<PERSON><PERSON> \"snapshot\" l<PERSON><PERSON> <PERSON><PERSON>", "recipe.notFound": "Lasinwa carmen: %s", "recipe.toast.description": "Cesë i tentaparma", "recipe.toast.title": "Vinyë carmeni!", "record.nowPlaying": "Tyalina sí: %s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "lasinwa", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "BROKEN ASSETS DETECTED", "resourcePack.high_contrast.name": "High Contrast", "resourcePack.load_fail": "Resource reload failed", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "<PERSON><PERSON> lo<PERSON> co<PERSON> mairion", "resourcePack.server.name": "World Specific Resources", "resourcePack.title": "<PERSON><PERSON><PERSON> colcar mairion", "resourcePack.vanilla.description": "I sanya nemesta Minecrafto", "resourcePack.vanilla.name": "Sanya", "resourcepack.downloading": "Downloading Resource Pack", "resourcepack.progress": "Cómë cavina ná (%s MB)...", "resourcepack.requesting": "Iquista carina...", "screenshot.failure": "Fanwemma lá rénima: %s", "screenshot.success": "Fanwemma renina ve %s", "selectServer.add": "Napanë server", "selectServer.defaultName": "Server Minecrafto", "selectServer.delete": "<PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON>", "selectServer.deleteQuestion": "Ma nalyë tanca i merilyë haita sina server?", "selectServer.deleteWarning": "'%s' nauva vanwa tennoio! (Yéni únótimë!)", "selectServer.direct": "<PERSON><PERSON>", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "(muina)", "selectServer.refresh": "<PERSON><PERSON>", "selectServer.select": "<PERSON><PERSON>", "selectWorld.access_failure": "Ambar-mit<PERSON><PERSON> lo<PERSON>", "selectWorld.allowCommands": "Cuptassë cárima", "selectWorld.allowCommands.info": "A<PERSON>ni ve /gamemode, /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "<PERSON>se Cached Data", "selectWorld.backupJoinConfirmButton": "Onta rehtaren ar apanta", "selectWorld.backupJoinSkipButton": "Nanyë tanca!", "selectWorld.backupQuestion.customized": "Vératainë ambari lár tulu<PERSON>ë", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": "Ambari i yuhtar rincië cilmi lár tulu<PERSON>ë", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": "Unfortunately, we do not support customized worlds in this version of Minecraft. We can still load this world and keep everything the way it was, but any newly generated terrain will no longer be customized. We're sorry for the inconvenience!", "selectWorld.backupWarning.downgrade": "Sina ambar nánë tyalina mi %s; si lerië %s ná. Tyalë nöa leriessen quí tyarë ulco. Lá ecë men quetë tancavë i ambar itas nauva tyálima. <PERSON><PERSON> merily<PERSON> caritas, á onta rehtaren, mecin.", "selectWorld.backupWarning.experimental": "This world uses experimental settings that could stop working at any time. We cannot guarantee it will load or work. Here be dragons!", "selectWorld.backupWarning.snapshot": "Sina ambar nánë tyalina mi %s; silúmëa lerië %s ná. Á onta rehtaren itan hastalë ambaro lá tuluva.", "selectWorld.bonusItems": "Na<PERSON>na ta<PERSON>", "selectWorld.cheats": "Cuptaler", "selectWorld.commands": "Commands", "selectWorld.conversion": "Envinyatalenna!", "selectWorld.conversion.tooltip": "This world must be opened in an older version (like 1.6.4) to be safely converted", "selectWorld.create": "Onta vinya ambar", "selectWorld.customizeType": "Vérata", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON>", "selectWorld.data_read": "<PERSON><PERSON><PERSON> istal<PERSON>o pá ambar...", "selectWorld.delete": "<PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON>", "selectWorld.deleteQuestion": "Ma nalyë tanca i merilyë haita sina ambar?", "selectWorld.deleteWarning": "'%s' nauva vanwa tennoio! (Yéni únótimë!)", "selectWorld.delete_failure": "Ambar-ha<PERSON><PERSON> loita<PERSON>", "selectWorld.edit": "Vista", "selectWorld.edit.backup": "<PERSON><PERSON> rehtaren", "selectWorld.edit.backupCreated": "Rehtarenina: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON><PERSON>", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON> haura rehtarenion", "selectWorld.edit.backupSize": "hoässë: %s MB", "selectWorld.edit.export_worldgen_settings": "<PERSON><PERSON><PERSON><PERSON> cilmi ambaro", "selectWorld.edit.export_worldgen_settings.failure": "<PERSON><PERSON><PERSON><PERSON> loitina", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON> haura ambaro", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "En<PERSON>l<PERSON> emma", "selectWorld.edit.save": "Renë", "selectWorld.edit.title": "Vista ambar", "selectWorld.enterName": "Essë ambaro", "selectWorld.enterSeed": "Erdë sina ambaro", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Iquisyainë rincië caricantar: %s", "selectWorld.experimental.details.title": "Iquisti rincëa caricanto", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Something went wrong while trying to load a world from a future version. This was a risky operation to begin with; sorry it didn't work.", "selectWorld.futureworld.error.title": "<PERSON><PERSON>!", "selectWorld.gameMode": "Tyalmelë", "selectWorld.gameMode.adventure": "Veryandë", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON><PERSON>, mal ronwar l<PERSON>r <PERSON> hya aucólim<PERSON>.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON><PERSON>, mal r<PERSON><PERSON> l<PERSON>r", "selectWorld.gameMode.adventure.line2": "napánimë hya rácimë", "selectWorld.gameMode.creative": "Maitië", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, car<PERSON><PERSON>, ar ratë ú pelmo. <PERSON><PERSON><PERSON><PERSON> vilë, sam<PERSON> aurar, ar <PERSON><PERSON> lá polir harna lye.", "selectWorld.gameMode.creative.line1": "Úlan<PERSON><PERSON> aurar, cárima vilë,", "selectWorld.gameMode.creative.line2": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore": "Anastorna", "selectWorld.gameMode.hardcore.info": "Vorielë pelinwa urda hrangwessë. Lá ecë lyen enonta apa firië.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON><PERSON>, tan<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.line2": "hrangwessë ar coivië min rië", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "Ecë lyen tirë, mal lá appa.", "selectWorld.gameMode.spectator.line1": "Ecë lyen tirë, mal lá appa", "selectWorld.gameMode.survival": "Vorië", "selectWorld.gameMode.survival.info": "<PERSON><PERSON> núla ambar yassë lyé carasta, comya, tam<PERSON>, ar <PERSON><PERSON>.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON>, tam<PERSON>, netë", "selectWorld.gameMode.survival.line2": "tyeller, tirë m<PERSON>lya ar maiti<PERSON>a", "selectWorld.gameRules": "<PERSON><PERSON> t<PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON><PERSON> cilmi", "selectWorld.import_worldgen_settings.failure": "Micolië cilmion loitina", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON> cómë cilmion (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "<PERSON><PERSON><PERSON>", "selectWorld.load_folder_access": "I haura ambaron lá apantima!", "selectWorld.loading_list": "<PERSON><PERSON><PERSON> comb<PERSON>o ambaron", "selectWorld.locked": "Hyana Minecraft tapë sina tyalië", "selectWorld.mapFeatures": "Onta carmi", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> cir<PERSON>, arta", "selectWorld.mapType": "Nostalë ambaro", "selectWorld.mapType.normal": "Sanya", "selectWorld.moreWorldOptions": "Ambë cilmi ambaro...", "selectWorld.newWorld": "<PERSON><PERSON> ambar", "selectWorld.recreate": "<PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Vératainë ambari lár tuluhtinë sina leriessë Minecrafto. Polin ricë enontaitas itan i ambar haryuva imya erdë, arta, mal ilya vératalë nauva vanwa. Ánin apsenë i tarastië!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "<PERSON>ima omenina lan rincen enonta ambar.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Nauvas renina mi:", "selectWorld.search": "cesë ambari", "selectWorld.seedInfo": "<PERSON><PERSON>, vinya erd<PERSON> nauva carina", "selectWorld.select": "Tyalë cilina ambar<PERSON>", "selectWorld.targetFolder": "I haura: %s", "selectWorld.title": "<PERSON><PERSON><PERSON> ambar", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON> renina an<PERSON>,", "selectWorld.tooltip.fromNewerVersion2": "feryalë sina ambaro quí tyarë hrangwi!", "selectWorld.tooltip.snapshot1": "<PERSON><PERSON> et<PERSON> carë rehtaren sina ambaro", "selectWorld.tooltip.snapshot2": "nó apantalyes si snapshotessë.", "selectWorld.unable_to_load": "Apantalë ambaron lá cárima", "selectWorld.version": "Lerië:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON>, á carë sa", "selectWorld.versionQuestion": "Ma merilyë tancavë apanta sina ambar?", "selectWorld.versionUnknown": "úsinwa", "selectWorld.versionWarning": "Sina ambar nánë tyalina mi %s ar apantaitas sinomë qui tyarë ulco!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Yé! Sinë cilmi yuhtar caricantar i yárë langë", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Yé! Sinë cilmi yuhtar rincië caricantar", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Ambar", "sign.edit": "Vista sarmë tannassë", "sleep.not_possible": "Lorië lá tuluva i ambarónë", "sleep.players_sleeping": "%s/%s tyalindor lórar", "sleep.skipping_night": "Lorië tuluva i ambarónë", "slot.only_single_allowed": "Rië assar lavinë rië, '%s' cavina", "slot.unknown": "Lasinwa assa '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "I mirma i Unicode tengwo lá mára: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "Lasinwa carië: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Catamen / oscaitië", "soundCategory.block": "<PERSON><PERSON>", "soundCategory.hostile": "Co<PERSON>ë cuima<PERSON>", "soundCategory.master": "Ilúvë<PERSON> r<PERSON>", "soundCategory.music": "Lindalë", "soundCategory.neutral": "<PERSON><PERSON><PERSON>", "soundCategory.player": "Tyalindor", "soundCategory.record": "<PERSON><PERSON> l<PERSON>", "soundCategory.ui": "Mapalmë", "soundCategory.voice": "Ó<PERSON> / pahta", "soundCategory.weather": "Vilwistë", "spectatorMenu.close": "Sacë i fanwa", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "<PERSON>r<PERSON> tolma meter cilë axan, atanir<PERSON> meter yuhtaitas.", "spectatorMenu.team_teleport": "<PERSON><PERSON><PERSON><PERSON><PERSON> yo<PERSON> as<PERSON>na", "spectatorMenu.team_teleport.prompt": "Cilë yonávë meter palarrúma tenna", "spectatorMenu.teleport": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyalind<PERSON>na", "spectatorMenu.teleport.prompt": "Cilë tyalindo meter palarrúma senna", "stat.generalButton": "Ilquárië", "stat.itemsButton": "<PERSON><PERSON>", "stat.minecraft.animals_bred": "Litanwë celvar", "stat.minecraft.aviate_one_cm": "Hlapunwa hairië", "stat.minecraft.bell_ring": "Nyeltanwë nyeller", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON> le<PERSON> luntenen", "stat.minecraft.clean_armor": "Poitin<PERSON><PERSON> varmar", "stat.minecraft.clean_banner": "Poitinwë lantannar", "stat.minecraft.clean_shulker_box": "Poitinwë colcar hyulcerion", "stat.minecraft.climb_one_cm": "Retinwa hairië", "stat.minecraft.crouch_one_cm": "Hlicinwa hairië", "stat.minecraft.damage_absorbed": "Termarinwa harnalë", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> tapinwa sand<PERSON>n", "stat.minecraft.damage_dealt": "Tyarinwa harnalë", "stat.minecraft.damage_dealt_absorbed": "Tyarinwa harnalë (cavinwa)", "stat.minecraft.damage_dealt_resisted": "Tyarinwa harnalë (nanyanwa)", "stat.minecraft.damage_resisted": "Nanyanwa harnalë", "stat.minecraft.damage_taken": "Cavinwa harnalë", "stat.minecraft.deaths": "Nótë qualmion", "stat.minecraft.drop": "Etehatinwë nati", "stat.minecraft.eat_cake_slice": "Matinwë hócirdar mastaron", "stat.minecraft.enchant_item": "Luhtanwë nati", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Quantanwë tambini", "stat.minecraft.fish_caught": "Atinwë lingwi", "stat.minecraft.fly_one_cm": "Hlapunwa hairië", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON> le<PERSON> r<PERSON>", "stat.minecraft.inspect_dispenser": "Cendar estataron", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON> t<PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON> rummaron", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON> as onin", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON><PERSON> as na<PERSON><PERSON>", "stat.minecraft.interact_with_blast_furnace": "<PERSON><PERSON><PERSON> as tic<PERSON><PERSON><PERSON> urna", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON> as velma", "stat.minecraft.interact_with_campfire": "Lancari as ruinë", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON><PERSON> as pal<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON><PERSON> as pal<PERSON><PERSON> ta<PERSON>o", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON> as urna", "stat.minecraft.interact_with_grindstone": "<PERSON><PERSON><PERSON> as runwa", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON> as par<PERSON><PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON> as lanwa", "stat.minecraft.interact_with_smithing_table": "<PERSON><PERSON><PERSON> as pal<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON><PERSON> as us<PERSON>ar", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON> as on<PERSON><PERSON><PERSON>", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON> t<PERSON>", "stat.minecraft.minecart_one_cm": "<PERSON><PERSON><PERSON> le<PERSON> r<PERSON>anen", "stat.minecraft.mob_kills": "Nahtanë vëor", "stat.minecraft.open_barrel": "Latyanwë corcolcar", "stat.minecraft.open_chest": "Latyanwë taucolcar", "stat.minecraft.open_enderchest": "Latyanwë taucolcar Endevë", "stat.minecraft.open_shulker_box": "Latyanwë colcar hyulcerion", "stat.minecraft.pig_one_cm": "<PERSON><PERSON>ë le<PERSON> pol<PERSON>n", "stat.minecraft.play_noteblock": "Tyalinwë lincolcar", "stat.minecraft.play_record": "Tyalinwë rindi lindal<PERSON>o", "stat.minecraft.play_time": "Lúmë tyaliëo", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> tyal<PERSON>", "stat.minecraft.pot_flower": "<PERSON><PERSON> sast<PERSON> tambissen", "stat.minecraft.raid_trigger": "Yestanwë nalantar", "stat.minecraft.raid_win": "Turunwë nalantar", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON>", "stat.minecraft.sneak_time": "Lúmë h<PERSON>iëo", "stat.minecraft.sprint_one_cm": "Noronwa hairië", "stat.minecraft.strider_one_cm": "Hairië lelyanwa telcontaranen", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> luti<PERSON>", "stat.minecraft.talked_to_villager": "Lanquettar ó opelemor", "stat.minecraft.target_hit": "Mehti tambanwë pilindenen", "stat.minecraft.time_since_death": "Lúmë nöa qualmello", "stat.minecraft.time_since_rest": "Lúmë nöa s<PERSON>", "stat.minecraft.total_world_time": "Lúmë latina ambaro", "stat.minecraft.traded_with_villager": "Mancaler ó opelemor", "stat.minecraft.trigger_trapped_chest": "Trapped Chests Triggered", "stat.minecraft.tune_noteblock": "Lampananwë lincolcar", "stat.minecraft.use_cauldron": "<PERSON>én ca<PERSON> tamb<PERSON>", "stat.minecraft.walk_on_water_one_cm": "Hairië patanwa pá nén", "stat.minecraft.walk_one_cm": "Patanwa hairië", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON>ë patanwa nu nén", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON>", "stat_type.minecraft.dropped": "Etehatinë", "stat_type.minecraft.killed": "Nahtanel %s %s", "stat_type.minecraft.killed.none": "Allúmë nahtanel %s", "stat_type.minecraft.killed_by": "%s nahtanë lye %s-llumë", "stat_type.minecraft.killed_by.none": "%s nahtanë lye allumë", "stat_type.minecraft.mined": "Rostina", "stat_type.minecraft.picked_up": "Picked Up", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "HIRË", "structure_block.button.load": "APANTA", "structure_block.button.save": "RENË", "structure_block.custom_data": "Custom Data Tag Name", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Neltë: %s", "structure_block.hover.data": "Istalë: %s", "structure_block.hover.load": "Apanta: %s", "structure_block.hover.save": "Renë: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Structure Integrity and Seed", "structure_block.integrity.integrity": "Structure Integrity", "structure_block.integrity.seed": "<PERSON><PERSON><PERSON>", "structure_block.invalid_structure_name": "Essë carmeva '%s' lá mára", "structure_block.load_not_found": "Carmë '%s' lá férima", "structure_block.load_prepare": "Structure '%s' position prepared", "structure_block.load_success": "Carmë apantina ollo '%s'", "structure_block.mode.corner": "Neltë", "structure_block.mode.data": "Istalë", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "Renë", "structure_block.mode_info.corner": "Corner Mode - Placement and size marker", "structure_block.mode_info.data": "Data Mode - Game logic marker", "structure_block.mode_info.load": "Apantië – apanta c<PERSON>o", "structure_block.mode_info.save": "Renië – minatecë cómenna", "structure_block.position": "Relative Position", "structure_block.position.x": "relative Position x", "structure_block.position.y": "relative position y", "structure_block.position.z": "relative position z", "structure_block.save_failure": "<PERSON><PERSON><PERSON> '%s' lacárima", "structure_block.save_success": "Carmë renina ve '%s'", "structure_block.show_air": "Apanta úcenimë ronwar:", "structure_block.show_boundingbox": "Show Bounding Box:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.size.x": "<PERSON><PERSON><PERSON><PERSON> car<PERSON> x", "structure_block.size.y": "<PERSON><PERSON><PERSON><PERSON> y", "structure_block.size.z": "<PERSON>öassë car<PERSON> z", "structure_block.size_failure": "Unable to detect structure size. Add corners with matching structure names", "structure_block.size_success": "<PERSON><PERSON> successfully detected for '%s'", "structure_block.strict": "Nahtëa caitalë:", "structure_block.structure_name": "<PERSON><PERSON><PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "Mirumírë nyéla", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.anvil.destroy": "<PERSON><PERSON> na<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON> yuh<PERSON>a", "subtitles.block.barrel.close": "Corcolca sáca", "subtitles.block.barrel.open": "Corcolca latyëa", "subtitles.block.beacon.activate": "Naltama alcëa", "subtitles.block.beacon.ambient": "Naltama óma", "subtitles.block.beacon.deactivate": "Naltama núra", "subtitles.block.beacon.power_select": "Melehtë naltamo cilinwa", "subtitles.block.beehive.drip": "Nehtë liptëa", "subtitles.block.beehive.enter": "<PERSON><PERSON> mitt<PERSON><PERSON> ni<PERSON>", "subtitles.block.beehive.exit": "<PERSON><PERSON> aut<PERSON><PERSON>", "subtitles.block.beehive.shear": "Aucirma nyása", "subtitles.block.beehive.work": "<PERSON><PERSON>", "subtitles.block.bell.resonate": "<PERSON><PERSON>llë to<PERSON>", "subtitles.block.bell.use": "Nyellë nyéla", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON><PERSON> cainua", "subtitles.block.big_dripleaf.tilt_up": "Liptalas tératë<PERSON>", "subtitles.block.blastfurnace.fire_crackle": "Hloni ticutaitë urno", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "Bubbles whirl", "subtitles.block.bubble_column.whirlpool_inside": "Bubbles zoom", "subtitles.block.button.click": "<PERSON><PERSON>", "subtitles.block.cake.add_candle": "Cake squishes", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "Taucolca sáca", "subtitles.block.chest.locked": "Chest locked", "subtitles.block.chest.open": "Taucolca latyëa", "subtitles.block.chorus_flower.death": "Nyellot he<PERSON>ëa", "subtitles.block.chorus_flower.grow": "Nyellot álëa", "subtitles.block.comparator.click": "Comp<PERSON>", "subtitles.block.composter.empty": "Composter emptied", "subtitles.block.composter.fill": "Composter filled", "subtitles.block.composter.ready": "Composter composts", "subtitles.block.conduit.activate": "Tulwë caltëa", "subtitles.block.conduit.ambient": "Tulwë tompëa", "subtitles.block.conduit.attack.target": "Tulwë nalantëa", "subtitles.block.conduit.deactivate": "Tulwë lórua", "subtitles.block.copper_bulb.turn_off": "Urusta calma lórua", "subtitles.block.copper_bulb.turn_on": "Urusta calma eccuinua", "subtitles.block.copper_trapdoor.close": "Lat sáca", "subtitles.block.copper_trapdoor.open": "Lat latyëa", "subtitles.block.crafter.craft": "Intamma táma", "subtitles.block.crafter.fail": "Intamma lá táma", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "Fintanwa tambë quantanwa", "subtitles.block.decorated_pot.insert_fail": "Fintanwa tambë taltëa", "subtitles.block.decorated_pot.shatter": "Fintanwa tambë ráca", "subtitles.block.dispenser.dispense": "Nat estatina", "subtitles.block.dispenser.fail": "Estatar loitanë", "subtitles.block.door.toggle": "Door creaks", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON> luhti<PERSON>o yuh<PERSON>a", "subtitles.block.end_portal.spawn": "<PERSON> fend<PERSON><PERSON> la<PERSON>a", "subtitles.block.end_portal_frame.fill": "Hen <PERSON><PERSON>", "subtitles.block.eyeblossom.close": "Hellótë sáca", "subtitles.block.eyeblossom.idle": "Hellótë hlussëa", "subtitles.block.eyeblossom.open": "Hellótë latyëa", "subtitles.block.fence_gate.toggle": "Fence Gate creaks", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Elenitser vúrëar", "subtitles.block.frogspawn.hatch": "Quaccas nóna", "subtitles.block.furnace.fire_crackle": "Hloni urno", "subtitles.block.generic.break": "<PERSON><PERSON>", "subtitles.block.generic.fall": "<PERSON> lant<PERSON> ronwas<PERSON>", "subtitles.block.generic.footsteps": "<PERSON><PERSON>", "subtitles.block.generic.hit": "<PERSON><PERSON> racina", "subtitles.block.generic.place": "Ronwa caitaina", "subtitles.block.grindstone.use": "<PERSON><PERSON> yuh<PERSON>a", "subtitles.block.growing_plant.crop": "<PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON>", "subtitles.block.honey_block.slide": "Taltalë ne<PERSON>essë", "subtitles.block.iron_trapdoor.close": "Lat sáca", "subtitles.block.iron_trapdoor.open": "Lat latyëa", "subtitles.block.lava.ambient": "Sirruima nóca", "subtitles.block.lava.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON>", "subtitles.block.note_block.note": "Lin<PERSON><PERSON><PERSON> la<PERSON>", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON>", "subtitles.block.piston.move": "<PERSON><PERSON> moves", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON><PERSON> lip<PERSON>a tambinna", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON> tambinna", "subtitles.block.pointed_dripstone.land": "Undalasar taltëa", "subtitles.block.portal.ambient": "Fendassë lamyëa", "subtitles.block.portal.travel": "<PERSON><PERSON>óna fendasso quéla", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON><PERSON> fendasso ant<PERSON>", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON>", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "Narrundo u<PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "Ampa enontiëo <PERSON>", "subtitles.block.respawn_anchor.deplete": "Ampa enonti<PERSON>", "subtitles.block.respawn_anchor.set_spawn": "Ampa enontiëo tulcëa i nómë enontiëo", "subtitles.block.sand.idle": "Litsië hlóni", "subtitles.block.sand.wind": "Vailimë hlóni", "subtitles.block.sculk.charge": "Esculco véla", "subtitles.block.sculk.spread": "Esculco palyëa", "subtitles.block.sculk_catalyst.bloom": "Hausta esculco lost<PERSON>a", "subtitles.block.sculk_sensor.clicking": "Esculcova tuvima yestëa lamië", "subtitles.block.sculk_sensor.clicking_stop": "Esculcova tuvima pustëa lamië", "subtitles.block.sculk_shrieker.shriek": "Esculcova holtur holtua", "subtitles.block.shulker_box.close": "<PERSON><PERSON> hyul<PERSON>o s<PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON> hyu<PERSON> la<PERSON>", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON>", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON> macal<PERSON>o yuh<PERSON>a", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "Ohtë nustarwa ráca", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON><PERSON> nustar rúva ohtë", "subtitles.block.sniffer_egg.plop": "Nustar caita ohtë", "subtitles.block.sponge.absorb": "<PERSON><PERSON>", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON>", "subtitles.block.trapdoor.close": "Lat sáca", "subtitles.block.trapdoor.open": "Lat latyëa", "subtitles.block.trapdoor.toggle": "Trapdoor creaks", "subtitles.block.trial_spawner.about_to_spawn_item": "Lumna nat feryëa insa", "subtitles.block.trial_spawner.ambient": "Hloni etyal<PERSON> r<PERSON>", "subtitles.block.trial_spawner.ambient_charged": "Lumnë hloni", "subtitles.block.trial_spawner.ambient_ominous": "Lumnë hloni", "subtitles.block.trial_spawner.charge_activate": "Tengwë unduláva etyalma riciéron", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.block.trial_spawner.detect_player": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> fery<PERSON>a", "subtitles.block.trial_spawner.eject_item": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> haptëa nat", "subtitles.block.trial_spawner.ominous_activate": "Tengwë unduláva etyalma riciéron", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> laty<PERSON>a", "subtitles.block.trial_spawner.spawn_item": "Lumna nat lantëa", "subtitles.block.trial_spawner.spawn_item_begin": "Lumna nat néma", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON>alma r<PERSON> etyála vëo", "subtitles.block.tripwire.attach": "<PERSON><PERSON> him<PERSON>a", "subtitles.block.tripwire.click": "<PERSON><PERSON> nirina", "subtitles.block.tripwire.detach": "<PERSON><PERSON>a", "subtitles.block.vault.activate": "<PERSON><PERSON>", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON> sampo", "subtitles.block.vault.close_shutter": "<PERSON><PERSON>", "subtitles.block.vault.deactivate": "Sampo nancaltëa", "subtitles.block.vault.eject_item": "Sampo haptëa nat", "subtitles.block.vault.insert_item": "Sampo latyë<PERSON> insa", "subtitles.block.vault.insert_item_fail": "Sampo auquéra nat", "subtitles.block.vault.open_shutter": "<PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> au<PERSON> tyalindo", "subtitles.block.water.ambient": "<PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "<PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert": "Parma caitanwa", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON> parma caitanwa", "subtitles.chiseled_bookshelf.take": "Parma napanwa", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON><PERSON> parma napina", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON>", "subtitles.entity.allay.ambient_with_item": "Alyar c<PERSON>ë<PERSON>", "subtitles.entity.allay.ambient_without_item": "<PERSON>yar <PERSON>", "subtitles.entity.allay.death": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.allay.hurt": "<PERSON><PERSON> ha<PERSON>", "subtitles.entity.allay.item_given": "<PERSON>yar lálë<PERSON>", "subtitles.entity.allay.item_taken": "<PERSON><PERSON>", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON>", "subtitles.entity.armadillo.ambient": "Nyelco hwestëa", "subtitles.entity.armadillo.brush": "Satsë hwecinwa öar", "subtitles.entity.armadillo.death": "Nyelco fíra", "subtitles.entity.armadillo.eat": "Nyelco <PERSON>", "subtitles.entity.armadillo.hurt": "Nyelco harnaina", "subtitles.entity.armadillo.hurt_reduced": "Nyelco alatyëa", "subtitles.entity.armadillo.land": "Nyelco andoryëa", "subtitles.entity.armadillo.peek": "Nyelco tihtëa", "subtitles.entity.armadillo.roll": "Nyelco cortëa", "subtitles.entity.armadillo.scute_drop": "Nyelco pentëa sat<PERSON>ë", "subtitles.entity.armadillo.unroll_finish": "Nyelco vélua", "subtitles.entity.armadillo.unroll_start": "Nyelco tihtëa", "subtitles.entity.armor_stand.fall": "<PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON>ndo harna<PERSON>", "subtitles.entity.arrow.shoot": "<PERSON><PERSON> qui<PERSON>", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>ra", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.splash": "Axolotl splashes", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.bat.ambient": "Quildarë quína", "subtitles.entity.bat.death": "Quildarë fíra", "subtitles.entity.bat.hurt": "Quildarë harnaina", "subtitles.entity.bat.takeoff": "Quildarë auvíla", "subtitles.entity.bee.ambient": "<PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON>", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> vú<PERSON><PERSON> raiqua", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> v<PERSON><PERSON><PERSON> al<PERSON>", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Uryamo néfa", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON> qui<PERSON>", "subtitles.entity.boat.paddle_land": "Rowing", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Hwestar feryëa", "subtitles.entity.breeze.death": "Hwestar fíra", "subtitles.entity.breeze.deflect": "Hwestar nanháta", "subtitles.entity.breeze.hurt": "Hwestar harnaina", "subtitles.entity.breeze.idle_air": "Hwestar víla", "subtitles.entity.breeze.idle_ground": "Hwestar lamyëa", "subtitles.entity.breeze.inhale": "Hwestar néfa", "subtitles.entity.breeze.jump": "Hwestar cápa", "subtitles.entity.breeze.land": "Hwestar andoryëa", "subtitles.entity.breeze.shoot": "Hwestar quihtëa", "subtitles.entity.breeze.slide": "Hwestar líca", "subtitles.entity.breeze.whirl": "Hwestar hwinyëa", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.camel.ambient": "Ulumpë hwestëa", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> c<PERSON>", "subtitles.entity.camel.dash_ready": "Ulumpë turyëa insë", "subtitles.entity.camel.death": "Ulumpë fíra", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.camel.hurt": "Ulumpë harna<PERSON>", "subtitles.entity.camel.saddle": "Hanwa colinwa", "subtitles.entity.camel.sit": "<PERSON>lump<PERSON> h<PERSON>a", "subtitles.entity.camel.stand": "Ulumpë tólua", "subtitles.entity.camel.step": "<PERSON>lumpë p<PERSON>ta", "subtitles.entity.camel.step_sand": "Ulumpë páta litsessë", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON>", "subtitles.entity.cat.eat": "<PERSON><PERSON>", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON>", "subtitles.entity.cat.purr": "Cat purrs", "subtitles.entity.chicken.ambient": "Porocë cehtecehtëa", "subtitles.entity.chicken.death": "Porocë fíra", "subtitles.entity.chicken.egg": "Porocë tup<PERSON>co", "subtitles.entity.chicken.hurt": "Porocë harnaina", "subtitles.entity.cod.death": "Cod dies", "subtitles.entity.cod.flop": "Cod flops", "subtitles.entity.cod.hurt": "Cod hurts", "subtitles.entity.cow.ambient": "Cow moos", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON> f<PERSON>ra", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.cow.milk": "Cow gets milked", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "Creeper fíra", "subtitles.entity.creeper.hurt": "Creeper harnaina", "subtitles.entity.creeper.primed": "Creeper suryëa", "subtitles.entity.dolphin.ambient": "Nostalingwë sípa", "subtitles.entity.dolphin.ambient_water": "Nostalingwë sípa", "subtitles.entity.dolphin.attack": "Nostalingwë nalantëa", "subtitles.entity.dolphin.death": "Nostalingwë fíra", "subtitles.entity.dolphin.eat": "Nostalingwë máta", "subtitles.entity.dolphin.hurt": "Nostalingwë harnaina", "subtitles.entity.dolphin.jump": "Nostaling<PERSON><PERSON> c<PERSON>", "subtitles.entity.dolphin.play": "Nostalingwë tyála", "subtitles.entity.dolphin.splash": "Nostalingwë falaryëa", "subtitles.entity.dolphin.swim": "Nostalingwë lútëa", "subtitles.entity.donkey.ambient": "Donkey hee-haws", "subtitles.entity.donkey.angry": "Donkey neighs", "subtitles.entity.donkey.chest": "Donkey Chest equips", "subtitles.entity.donkey.death": "Pellopë fíra", "subtitles.entity.donkey.eat": "Pellopë máta", "subtitles.entity.donkey.hurt": "Pellopë harnaina", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Drowned gurgles", "subtitles.entity.drowned.ambient_water": "Quormo nurrua", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>ra", "subtitles.entity.drowned.hurt": "<PERSON>uo<PERSON><PERSON> ha<PERSON>", "subtitles.entity.drowned.shoot": "<PERSON>uormo h<PERSON>", "subtitles.entity.drowned.step": "Drowned steps", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.egg.throw": "Ohtë etehatina", "subtitles.entity.elder_guardian.ambient": "Elder Guardian moans", "subtitles.entity.elder_guardian.ambient_land": "Elder Guardian flaps", "subtitles.entity.elder_guardian.curse": "Cundo anyá<PERSON> h<PERSON>", "subtitles.entity.elder_guardian.death": "Cundo anyá<PERSON> f<PERSON>ra", "subtitles.entity.elder_guardian.flop": "Elder Guardian flops", "subtitles.entity.elder_guardian.hurt": "Cundo <PERSON> harna<PERSON>", "subtitles.entity.ender_dragon.ambient": "Hlócë ráva", "subtitles.entity.ender_dragon.death": "Hlócë fíra", "subtitles.entity.ender_dragon.flap": "Hlócë quása rámaryar", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON><PERSON> yarr<PERSON>", "subtitles.entity.ender_dragon.hurt": "Hlócë harna<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> quihtëa", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> <PERSON><PERSON> qui<PERSON>", "subtitles.entity.ender_pearl.throw": "Eässar <PERSON> ví<PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> f<PERSON><PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> harna<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON> holtua", "subtitles.entity.enderman.teleport": "<PERSON><PERSON>", "subtitles.entity.endermite.ambient": "Endermite scuttles", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.endermite.hurt": "Endermite hurts", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON> n<PERSON><PERSON>a", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evoker prepares attack", "subtitles.entity.evoker.prepare_summon": "Evoker prepares summoning", "subtitles.entity.evoker.prepare_wololo": "Evoker prepares charming", "subtitles.entity.evoker_fangs.attack": "Fangs snap", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON> cavina", "subtitles.entity.firework_rocket.blast": "Nartanwë rúva", "subtitles.entity.firework_rocket.launch": "Firework launches", "subtitles.entity.firework_rocket.twinkle": "Firework twinkles", "subtitles.entity.fish.swim": "Falarië", "subtitles.entity.fishing_bobber.retrieve": "Soltar ettucinwa", "subtitles.entity.fishing_bobber.splash": "Soltar falaryëa", "subtitles.entity.fishing_bobber.throw": "Soltar hatinwa", "subtitles.entity.fox.aggro": "Rusco u<PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> qu<PERSON>", "subtitles.entity.fox.bite": "Rusco náca", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON>co ha<PERSON>", "subtitles.entity.fox.screech": "<PERSON> screeches", "subtitles.entity.fox.sleep": "<PERSON> snores", "subtitles.entity.fox.sniff": "Rusco nustëa", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.teleport": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> quaquëa", "subtitles.entity.frog.death": "Quácë fíra", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> máta", "subtitles.entity.frog.hurt": "Quácë harnaina", "subtitles.entity.frog.lay_spawn": "Quácë caitëa ohti", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Burning", "subtitles.entity.generic.death": "Firië", "subtitles.entity.generic.drink": "Sipping", "subtitles.entity.generic.eat": "Matië", "subtitles.entity.generic.explode": "Ruvië", "subtitles.entity.generic.extinguish_fire": "Fire extinguishes", "subtitles.entity.generic.hurt": "<PERSON>", "subtitles.entity.generic.small_fall": "Something trips", "subtitles.entity.generic.splash": "Falarië", "subtitles.entity.generic.swim": "Lutië", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "Ñasto fíra", "subtitles.entity.ghast.hurt": "Ñasto <PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> shoots", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Calcanta quantanwa", "subtitles.entity.glow_item_frame.break": "Calcanta rá<PERSON>", "subtitles.entity.glow_item_frame.place": "Calcanta caitanwa", "subtitles.entity.glow_item_frame.remove_item": "Calcanta cum<PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "Glow Item Frame clicks", "subtitles.entity.glow_squid.ambient": "Calmórolingwë lútëa", "subtitles.entity.glow_squid.death": "Calmórolingwë fíra", "subtitles.entity.glow_squid.hurt": "Calmórolingwë harnaina", "subtitles.entity.glow_squid.squirt": "Glow Squid shoots ink", "subtitles.entity.goat.ambient": "Naico nít<PERSON>", "subtitles.entity.goat.death": "<PERSON>ico <PERSON>", "subtitles.entity.goat.eat": "Goat eats", "subtitles.entity.goat.horn_break": "Rassë naico auráca", "subtitles.entity.goat.hurt": "Na<PERSON> ha<PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> leaps", "subtitles.entity.goat.milk": "<PERSON><PERSON> gets milked", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomps", "subtitles.entity.goat.ram_impact": "Goat rams", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> holtua", "subtitles.entity.goat.step": "Goat steps", "subtitles.entity.guardian.ambient": "Guardian moans", "subtitles.entity.guardian.ambient_land": "Guardian flaps", "subtitles.entity.guardian.attack": "Guardian shoots", "subtitles.entity.guardian.death": "Cundo fíra", "subtitles.entity.guardian.flop": "Guardian flops", "subtitles.entity.guardian.hurt": "Cundo harnaina", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "Alassëa Ñasto fíra", "subtitles.entity.happy_ghast.equip": "Lattasta colaina ná", "subtitles.entity.happy_ghast.harness_goggles_down": "Alassëa Ñasto ferya ná", "subtitles.entity.happy_ghast.harness_goggles_up": "Alassëa Ñasto pustëa", "subtitles.entity.happy_ghast.hurt": "Alassëa Ñasto harnaina", "subtitles.entity.happy_ghast.unequip": "Lattasta avacolaina ná", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> yarr<PERSON>a raiqua<PERSON>", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> converts to <PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.hoglin.retreat": "Hoglin retreats", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "Horse neighs", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Cauma rocco colinwa", "subtitles.entity.horse.breathe": "Horse breathes", "subtitles.entity.horse.death": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON>", "subtitles.entity.horse.gallop": "Horse gallops", "subtitles.entity.horse.hurt": "<PERSON><PERSON> harna<PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON>", "subtitles.entity.horse.saddle": "Hanwa colinwa", "subtitles.entity.husk.ambient": "Erumëa urco nurrua", "subtitles.entity.husk.converted_to_zombie": "Erumë<PERSON> urco ollë urco", "subtitles.entity.husk.death": "Erumëa urco fíra", "subtitles.entity.husk.hurt": "Erumëa urco harna<PERSON>", "subtitles.entity.illusioner.ambient": "Illusioner murmurs", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON> casts spell", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.illusioner.hurt": "Il<PERSON><PERSON> hurts", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> displaces", "subtitles.entity.illusioner.prepare_blindness": "Il<PERSON><PERSON> prepares blindness", "subtitles.entity.illusioner.prepare_mirror": "Il<PERSON><PERSON> prepares mirror image", "subtitles.entity.iron_golem.attack": "Angaturco nalantëa", "subtitles.entity.iron_golem.damage": "Angaturco racina", "subtitles.entity.iron_golem.death": "Angaturco fíra", "subtitles.entity.iron_golem.hurt": "Angaturco harnaina", "subtitles.entity.iron_golem.repair": "Angaturco envinyan<PERSON>", "subtitles.entity.item.break": "Nat ráca", "subtitles.entity.item.pickup": "Item plops", "subtitles.entity.item_frame.add_item": "Canta quantanwa", "subtitles.entity.item_frame.break": "Canta ráca", "subtitles.entity.item_frame.place": "Canta caitanwa", "subtitles.entity.item_frame.remove_item": "Canta cumyanwa", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON> clicks", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tuncas<PERSON> nutinwa", "subtitles.entity.lightning_bolt.impact": "Íta nambëa", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON> rá<PERSON>", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON> n<PERSON><PERSON> r<PERSON>", "subtitles.entity.llama.chest": "<PERSON><PERSON>", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON>", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Magma Cube dies", "subtitles.entity.magma_cube.hurt": "Magma Cube hurts", "subtitles.entity.magma_cube.squish": "Magma Cube squishes", "subtitles.entity.minecart.inside": "<PERSON><PERSON>", "subtitles.entity.minecart.inside_underwater": "<PERSON><PERSON>", "subtitles.entity.minecart.riding": "<PERSON><PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom transforms", "subtitles.entity.mooshroom.eat": "Mooshroom eats", "subtitles.entity.mooshroom.milk": "Mooshroom gets milked", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom gets milked suspiciously", "subtitles.entity.mule.ambient": "Mule hee-haws", "subtitles.entity.mule.angry": "<PERSON><PERSON> neighs", "subtitles.entity.mule.chest": "Mule Chest equips", "subtitles.entity.mule.death": "<PERSON><PERSON> dies", "subtitles.entity.mule.eat": "<PERSON><PERSON> eats", "subtitles.entity.mule.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Tecemma ráca", "subtitles.entity.painting.place": "Tecemma caitanwa", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda hwestëa", "subtitles.entity.panda.bite": "Panda náca", "subtitles.entity.panda.cant_breed": "Panda nítëa", "subtitles.entity.panda.death": "Panda fíra", "subtitles.entity.panda.eat": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.panda.hurt": "Panda harnaina", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "Panda hotyëa", "subtitles.entity.panda.step": "Panda pátëa", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON>", "subtitles.entity.parrot.ambient": "Quetaiwë quéta", "subtitles.entity.parrot.death": "Quetaiwë fíra", "subtitles.entity.parrot.eats": "Quetaiwë máta", "subtitles.entity.parrot.fly": "Quetaiwë víla", "subtitles.entity.parrot.hurts": "Quetaiwë harnaina", "subtitles.entity.parrot.imitate.blaze": "Quetaiwë néfa", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "Quetaiwë suryëa", "subtitles.entity.parrot.imitate.drowned": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.elder_guardian": "Quetaiwë <PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "Quetaiwë ráva", "subtitles.entity.parrot.imitate.endermite": "Quetaiwë pátëa", "subtitles.entity.parrot.imitate.evoker": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.ghast": "Quetaiwë <PERSON>", "subtitles.entity.parrot.imitate.guardian": "Quetaiwë <PERSON>", "subtitles.entity.parrot.imitate.hoglin": "Quetaiwë yarrëa", "subtitles.entity.parrot.imitate.husk": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.illusioner": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.magma_cube": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.phantom": "Quetaiwë yaiyëa", "subtitles.entity.parrot.imitate.piglin": "Quetaiwë hwestëa", "subtitles.entity.parrot.imitate.piglin_brute": "Quetaiwë hwestëa", "subtitles.entity.parrot.imitate.pillager": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.ravager": "Quetaiwë hwestëa", "subtitles.entity.parrot.imitate.shulker": "Quetaiwë hlícëa", "subtitles.entity.parrot.imitate.silverfish": "Quetaiwë suryëa", "subtitles.entity.parrot.imitate.skeleton": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.slime": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.spider": "Quetaiwë suryëa", "subtitles.entity.parrot.imitate.stray": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.vex": "Quetaiwë tarastëa", "subtitles.entity.parrot.imitate.vindicator": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.warden": "Quetaiwë <PERSON>", "subtitles.entity.parrot.imitate.witch": "Quetaiwë lálëa", "subtitles.entity.parrot.imitate.wither": "Quetaiwë ursëa", "subtitles.entity.parrot.imitate.wither_skeleton": "Quetaiwë lamyëa", "subtitles.entity.parrot.imitate.zoglin": "Quetaiwë yarrëa", "subtitles.entity.parrot.imitate.zombie": "Quetaiwë nurrua", "subtitles.entity.parrot.imitate.zombie_villager": "Quetaiwë nurrua", "subtitles.entity.phantom.ambient": "Phantom screeches", "subtitles.entity.phantom.bite": "Lórerauco náca", "subtitles.entity.phantom.death": "Lórerauco fíra", "subtitles.entity.phantom.flap": "Phantom flaps", "subtitles.entity.phantom.hurt": "Lórerauco harnaina", "subtitles.entity.phantom.swoop": "Phantom swoops", "subtitles.entity.pig.ambient": "Pig oinks", "subtitles.entity.pig.death": "Polca fíra", "subtitles.entity.pig.hurt": "Polca harnaina", "subtitles.entity.pig.saddle": "Hanwa colinwa", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON><PERSON> el<PERSON> ma", "subtitles.entity.piglin.ambient": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON>n hwestëa raiquavë", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON><PERSON> oll<PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.piglin.jealous": "Pingilin hwestëa hrúcenië", "subtitles.entity.piglin.retreat": "<PERSON><PERSON><PERSON> nan<PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON> pingilin hwest<PERSON>a", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON> pingilin hwestëa raiquavë", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON><PERSON> pingilin ó<PERSON><PERSON>a urcoya pingilin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON> pin<PERSON> p<PERSON>", "subtitles.entity.pillager.ambient": "Pillager murmurs", "subtitles.entity.pillager.celebrate": "Pillager cheers", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.pillager.hurt": "Pillager hurts", "subtitles.entity.player.attack.crit": "Critical attack", "subtitles.entity.player.attack.knockback": "Knockback attack", "subtitles.entity.player.attack.strong": "Turnalantalë", "subtitles.entity.player.attack.sweep": "Sweeping attack", "subtitles.entity.player.attack.weak": "Weak attack", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON>ndo harna<PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON>ndo quóra", "subtitles.entity.player.hurt_on_fire": "Player burns", "subtitles.entity.player.levelup": "Player dings", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "Olpë ráca", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON><PERSON> hatinwa", "subtitles.entity.puffer_fish.blow_out": "Pufferfish deflates", "subtitles.entity.puffer_fish.blow_up": "Pufferfish inflates", "subtitles.entity.puffer_fish.death": "Pufferfish dies", "subtitles.entity.puffer_fish.flop": "Pufferfish flops", "subtitles.entity.puffer_fish.hurt": "Pufferfish hurts", "subtitles.entity.puffer_fish.sting": "Pufferfish stings", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Rabbit hops", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON> cheers", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> stunned", "subtitles.entity.salmon.death": "Alaxë fíra", "subtitles.entity.salmon.flop": "Alaxë c<PERSON>", "subtitles.entity.salmon.hurt": "Alaxë harnaina", "subtitles.entity.sheep.ambient": "Sheep baahs", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON><PERSON> q<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hit": "Quihta<PERSON>ë hyulcero rúva", "subtitles.entity.shulker_bullet.hurt": "Quihtalë hyulcero ráca", "subtitles.entity.silverfish.ambient": "Telpingwë suryëa", "subtitles.entity.silverfish.death": "Telpingwë fíra", "subtitles.entity.silverfish.hurt": "Telpingwë harnaina", "subtitles.entity.skeleton.ambient": "Skeleton rattles", "subtitles.entity.skeleton.converted_to_stray": "Skeleton converts to Stray", "subtitles.entity.skeleton.death": "Axoquen fíra", "subtitles.entity.skeleton.hurt": "Axoquen harnaina", "subtitles.entity.skeleton.shoot": "Axoquen quihtëa", "subtitles.entity.skeleton_horse.ambient": "Návarocco <PERSON>", "subtitles.entity.skeleton_horse.death": "Návarocco fíra", "subtitles.entity.skeleton_horse.hurt": "Návarocco harna<PERSON>", "subtitles.entity.skeleton_horse.jump_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton_horse.swim": "Návarocco lútëa", "subtitles.entity.slime.attack": "Maxomo nalanta", "subtitles.entity.slime.death": "Maxomo fíra", "subtitles.entity.slime.hurt": "Maxomo ha<PERSON>", "subtitles.entity.slime.squish": "Slime squishes", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON><PERSON> erdi", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.egg_crack": "Ohtë nustarwa ráca", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON> nustar rúva ohtë", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> nust<PERSON>", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON> nust<PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.death": "<PERSON><PERSON> dies", "subtitles.entity.snow_golem.hurt": "Snow Golem hurts", "subtitles.entity.snowball.throw": "Loscoron víla", "subtitles.entity.spider.ambient": "Liantë suryëa", "subtitles.entity.spider.death": "Liantë fíra", "subtitles.entity.spider.hurt": "Liantë harna<PERSON>", "subtitles.entity.squid.ambient": "Mórolingwë lútëa", "subtitles.entity.squid.death": "Mórolingwë fíra", "subtitles.entity.squid.hurt": "Mórolingwë harnaina", "subtitles.entity.squid.squirt": "Squid shoots ink", "subtitles.entity.stray.ambient": "Stray rattles", "subtitles.entity.stray.death": "<PERSON><PERSON> dies", "subtitles.entity.stray.hurt": "Stray hurts", "subtitles.entity.strider.death": "Telcontar fíra", "subtitles.entity.strider.eat": "Telcontar máta", "subtitles.entity.strider.happy": "Telcontar alaryëa", "subtitles.entity.strider.hurt": "Telcontar harnaina", "subtitles.entity.strider.idle": "Telcontar sípa", "subtitles.entity.strider.retreat": "Telcontar nána", "subtitles.entity.tadpole.death": "Quaccas fíra", "subtitles.entity.tadpole.flop": "Quaccas cápa", "subtitles.entity.tadpole.grow_up": "Quaccas óla olanwa", "subtitles.entity.tadpole.hurt": "Quaccas harnaina", "subtitles.entity.tnt.primed": "TNT urtëa", "subtitles.entity.tropical_fish.death": "Tropical Fish dies", "subtitles.entity.tropical_fish.flop": "Tropical Fish flops", "subtitles.entity.tropical_fish.hurt": "Tropical Fish hurts", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "Sandanasto fíra", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON> f<PERSON>ra", "subtitles.entity.turtle.egg_break": "Ohtë sand<PERSON> ráca", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON> sand<PERSON> ruxëa", "subtitles.entity.turtle.egg_hatch": "<PERSON><PERSON><PERSON> rúva ohtë", "subtitles.entity.turtle.hurt": "Sand<PERSON><PERSON> harna<PERSON>", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.turtle.lay_egg": "Sandanasto caitëa ohtë", "subtitles.entity.turtle.shamble": "Sand<PERSON><PERSON> ména", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON> ména", "subtitles.entity.turtle.swim": "Sandanasto lútëa", "subtitles.entity.vex.ambient": "Tarastar tarastëa", "subtitles.entity.vex.charge": "Tarastar holtua", "subtitles.entity.vex.death": "Tarastar fíra", "subtitles.entity.vex.hurt": "Tarastar harnaina", "subtitles.entity.villager.ambient": "Opelemo nurrua", "subtitles.entity.villager.celebrate": "Villager cheers", "subtitles.entity.villager.death": "Opelemo fíra", "subtitles.entity.villager.hurt": "Opelemo harna<PERSON>", "subtitles.entity.villager.no": "Opelemo l<PERSON>", "subtitles.entity.villager.trade": "Opelemo <PERSON>", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cartographer": "Noremmatan mótëa", "subtitles.entity.villager.work_cleric": "Cordamo m<PERSON>ë<PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON>o m<PERSON>", "subtitles.entity.villager.work_librarian": "<PERSON><PERSON> mótëa", "subtitles.entity.villager.work_mason": "Ontamo mótëa", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_toolsmith": "Carmatan mótëa", "subtitles.entity.villager.work_weaponsmith": "Maicatan mótëa", "subtitles.entity.villager.yes": "Opelemo ná<PERSON>", "subtitles.entity.vindicator.ambient": "Vindicator mutters", "subtitles.entity.vindicator.celebrate": "Vindicator cheers", "subtitles.entity.vindicator.death": "Vindicator dies", "subtitles.entity.vindicator.hurt": "Vindicator hurts", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> mancaro nur<PERSON>a", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON> mancaro f<PERSON>ra", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON> mancaro na<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON> mancaro y<PERSON><PERSON> ilin", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON> mancaro y<PERSON>a yulda", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> mancaro ha<PERSON>", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON> man<PERSON>", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON> man<PERSON>o <PERSON>", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON> mancaro man<PERSON>", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON> man<PERSON>", "subtitles.entity.warden.agitated": "Ortirmo nurrua raiquavë", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.angry": "Ortirmo ursëa", "subtitles.entity.warden.attack_impact": "Ortirmo pal<PERSON>ëa", "subtitles.entity.warden.death": "Ortirmo f<PERSON>", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.emerge": "Or<PERSON>rm<PERSON>", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON>ova palpëa", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON>rmo tunt<PERSON>", "subtitles.entity.warden.listening_angry": "Ortirmo tuntëa raiquavë", "subtitles.entity.warden.nearby_close": "Ortirmo analelyëa", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>a", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.roar": "Ortirmo rá<PERSON>", "subtitles.entity.warden.sniff": "Ortirmo nustëa", "subtitles.entity.warden.sonic_boom": "Or<PERSON>rmo h<PERSON>", "subtitles.entity.warden.sonic_charge": "Ortirmo f<PERSON>", "subtitles.entity.warden.step": "Ortirmo p<PERSON>", "subtitles.entity.warden.tendril_clicks": "Lepsiler ortirmovë rihtëar", "subtitles.entity.wind_charge.throw": "Hwesta víla", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.witch.ambient": "Witch giggles", "subtitles.entity.witch.celebrate": "Witch cheers", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "Witch drinks", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.witch.throw": "Witch throws", "subtitles.entity.wither.ambient": "<PERSON><PERSON> <PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wither.hurt": "Wither harna<PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON> released", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleton rattles", "subtitles.entity.wither_skeleton.death": "Wither-axoquen fíra", "subtitles.entity.wither_skeleton.hurt": "Wither-axoquen harnaina", "subtitles.entity.wolf.ambient": "Ráca h<PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "Ráca ha<PERSON>", "subtitles.entity.wolf.pant": "Ráca h<PERSON>", "subtitles.entity.wolf.shake": "Ráca quása", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> ya<PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> yarr<PERSON>a raiquav<PERSON>", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> f<PERSON>ra", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> harna<PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> steps", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> nur<PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "Fendë quása", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Zombie converts to Drowned", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.destroy_egg": "<PERSON><PERSON><PERSON> nancarina", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie_horse.ambient": "Urcoya rocco ya<PERSON>a", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON>oya rocco fíra", "subtitles.entity.zombie_horse.hurt": "Urcoya rocco harnaina", "subtitles.entity.zombie_villager.ambient": "Urcoya opelemo nurrua", "subtitles.entity.zombie_villager.converted": "Zombie Villager vociferates", "subtitles.entity.zombie_villager.cure": "Zombie Villager snuffles", "subtitles.entity.zombie_villager.death": "Urcoya opelemo fíra", "subtitles.entity.zombie_villager.hurt": "Urcoya opelemo harnaina", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON> pingilin hwest<PERSON>", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON> pingilin hwestëa raiquavë", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "<PERSON><PERSON><PERSON> var<PERSON>a harivë", "subtitles.event.mob_effect.trial_omen": "Lumna ricië varastëa harivë", "subtitles.event.raid.horn": "<PERSON><PERSON>na ró<PERSON>", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON> colinwa", "subtitles.item.armor.equip_chain": "Chain armor jingles", "subtitles.item.armor.equip_diamond": "Diamond armor clangs", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_gold": "Gold armor clinks", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON> varma hlontëa", "subtitles.item.armor.equip_leather": "Leather armor rustles", "subtitles.item.armor.equip_netherite": "Varma neserito h<PERSON>tëa", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_wolf": "Varma rácava tacinwa", "subtitles.item.armor.unequip_wolf": "Varma rácava autucinwa", "subtitles.item.axe.scrape": "Pelecco nyása", "subtitles.item.axe.strip": "Axe strips", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON>", "subtitles.item.bone_meal.use": "Bone Meal crinkles", "subtitles.item.book.page_turn": "Lassë hyastëa", "subtitles.item.book.put": "Parma hlondëa", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON>ë <PERSON>", "subtitles.item.bottle.fill": "Olpë quantanwa", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brushing Gravel", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel completed", "subtitles.item.brush.brushing.sand": "Brushing Sand", "subtitles.item.brush.brushing.sand.complete": "Brushing Sand completed", "subtitles.item.bucket.empty": "Calpa cumyanwa", "subtitles.item.bucket.fill": "Calpa quantanwa", "subtitles.item.bucket.fill_axolotl": "A<PERSON><PERSON><PERSON> atina", "subtitles.item.bucket.fill_fish": "Lingwë atina", "subtitles.item.bucket.fill_tadpole": "Quaccas atina", "subtitles.item.bundle.drop_contents": "Pocollë <PERSON>", "subtitles.item.bundle.insert": "<PERSON>rta<PERSON>", "subtitles.item.bundle.insert_fail": "Pocollë quanta", "subtitles.item.bundle.remove_one": "Nat <PERSON>", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON>", "subtitles.item.crop.plant": "Crop planted", "subtitles.item.crossbow.charge": "Serma lanquingo taina", "subtitles.item.crossbow.hit": "<PERSON><PERSON>", "subtitles.item.crossbow.load": "Lanquinga <PERSON>", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON> quiht<PERSON>a", "subtitles.item.dye.use": "Dye stains", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Fireball whooshes", "subtitles.item.flintandsteel.use": "Flint and Steel click", "subtitles.item.glow_ink_sac.use": "Glow Ink Sac splotches", "subtitles.item.goat_horn.play": "Rassë naico lamyëa", "subtitles.item.hoe.till": "Hoe tills", "subtitles.item.honey_bottle.drink": "Yulië", "subtitles.item.honeycomb.wax_on": "Tupië lí<PERSON>", "subtitles.item.horse_armor.unequip": "Cauma rocco avatalt<PERSON>a", "subtitles.item.ink_sac.use": "Ink Sac splotches", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON><PERSON> nutinwa", "subtitles.item.lead.untied": "<PERSON><PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "Farma avataltëa", "subtitles.item.lodestone_compass.lock": "Mententar rantondo tancat<PERSON>a ranton<PERSON>", "subtitles.item.mace.smash_air": "Runda nambëa", "subtitles.item.mace.smash_ground": "Runda nambëa", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Hanwa avataltëa", "subtitles.item.shears.shear": "Aucirma <PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON>", "subtitles.item.shield.block": "<PERSON><PERSON> t<PERSON>", "subtitles.item.shovel.flatten": "Sampa lárëa", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON> na<PERSON>", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON>", "subtitles.item.totem.use": "Totem activates", "subtitles.item.trident.hit": "Nelcarca <PERSON>ë<PERSON>", "subtitles.item.trident.hit_ground": "Nelcarca pápa", "subtitles.item.trident.return": "Nelcarca entúla", "subtitles.item.trident.riptide": "Nelcarca omyëa", "subtitles.item.trident.throw": "Nelcarca to<PERSON>", "subtitles.item.trident.thunder": "Nelcarca hundua", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Varma rácava harnaina", "subtitles.item.wolf_armor.repair": "Varma rácava apteryanwa", "subtitles.particle.soul_escape": "Soul escapes", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON> carina", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "<PERSON><PERSON>wa yuh<PERSON>a", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>a", "subtitles.weather.rain": "<PERSON><PERSON>", "symlink_warning.message": "Feryalë ambaron ha<PERSON>lon i samir satinwë limi cé nauva raxëa qui lá istal ita caril. Á cenë %s meter parë pá sa.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "Ambë", "symlink_warning.title": "I haura i ambaro samë satinwë limi", "symlink_warning.title.pack": "<PERSON><PERSON><PERSON> o<PERSON>ë samë satinwë limi", "symlink_warning.title.world": "I haura i ambaro samë satinwë limi", "team.collision.always": "Illumë", "team.collision.never": "Allumë", "team.collision.pushOtherTeams": "Push other teams", "team.collision.pushOwnTeam": "<PERSON><PERSON> own team", "team.notFound": "Lasinwa yonávë '%s'", "team.visibility.always": "Oio", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON> hyan<PERSON> yo<PERSON>", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON>", "team.visibility.never": "Úoio", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (nírima)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (iquis)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "Lúmë ambar-apantalëo", "telemetry.event.world_loaded.description": "Istalë pá lér tyali<PERSON> (ve tyalmelë, vistalë celiento hya servero, ar le<PERSON><PERSON> tyal<PERSON>) lavë men ceuta Minecraft itan tama, i tyalindor cimbar ambë, aryatanwa nauva.\nI tulmat ambar-apantiëo ar alapantiëo yorit i lúmë tyaliëo.", "telemetry.event.world_loaded.title": "<PERSON><PERSON>", "telemetry.event.world_unloaded.description": "I tulma yo ambar-alapantiëo yorit i lúmë tyaliëo.\nI lúmë (lúmenihtainen ar ticínen) lestina ná yá tyalië ambaressë telë (eteleliessë, auyantiessë serverello).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON>ú<PERSON>ë <PERSON> (ticínen)", "telemetry.property.advancement_id.title": "Nótë ID empatyellëo", "telemetry.property.client_id.title": "Nótë ID celiento", "telemetry.property.client_modded.title": "Celient vistanwa", "telemetry.property.dedicated_memory_kb.title": "Hapina rénë (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Rimbassë emmaron (FPS)", "telemetry.property.game_mode.title": "Tyalmelë", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON>", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Lúmë nó i lattin la<PERSON>wa (ms)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Nótë ID tyaliëo", "telemetry.property.new_world.title": "<PERSON><PERSON> ambar", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "<PERSON><PERSON>", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "<PERSON><PERSON><PERSON> ceno", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Lúmë ambar-apantal<PERSON>o (s)", "telemetry.property.server_modded.title": "Server vistanwa", "telemetry.property.server_type.title": "Nostalë servero", "telemetry.property.ticks_since_load.title": "Lúmë ambar-apantal<PERSON> (ticínen)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "Nótë ID yuhtaro", "telemetry.property.world_load_time_ms.title": "Lúmë ambar-apantal<PERSON> (ms)", "telemetry.property.world_session_id.title": "Nótë ID ambar-tyaliëo", "telemetry_info.button.give_feedback": "Menta ahtarië", "telemetry_info.button.privacy_statement": "Eccanië aquapahtiëo", "telemetry_info.button.show_data": "<PERSON><PERSON><PERSON> is<PERSON>", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "<PERSON><PERSON>", "telemetry_info.screen.description": "Comië sana istalëo asya me ceuta Minecraft itan tama, i tyalindor cimbar ambë, aryatanwa nauva.\nPolil menta men ahtarië meter asya me aryata Minecraft.", "telemetry_info.screen.title": "<PERSON><PERSON><PERSON> istal<PERSON> p<PERSON>", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Caitalë tyasta-carmëo loitanwa: %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "<PERSON>inwa mitya loima: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Lúpelma lá mára (%s) – nótë ticion lá ambamuntëa", "test_block.message": "Menta:", "test_block.mode.accept": "Cavë", "test_block.mode.fail": "Lo<PERSON>", "test_block.mode.log": "Log", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Loitina: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Carmë: %s", "test_instance.description.type": "Nostalë: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Engwi:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[lá mára]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Querë:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "<PERSON>va enapanta sina fanwa", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.credits": "Copyright Mojang AB. Áva ósatë!", "title.multiplayer.disabled": "Linquen lá férima. Á cenda cilmi Microsoft fëalyo, mecin.", "title.multiplayer.disabled.banned.name": "Mauya lyen vista esselya nó tyaluval linquenessë", "title.multiplayer.disabled.banned.permanent": "Fëalya avanwa linquenello oialë", "title.multiplayer.disabled.banned.temporary": "Fëalya avanwa linquenello lúmëavë", "title.multiplayer.lan": "<PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "Linquen (ava server)", "title.multiplayer.realms": "Linquen (Rëalmi)", "title.singleplayer": "Erquen", "translation.test.args": "%s %s", "translation.test.complex": "Noquetta, %s%2$s ata %s ar %1$s teldavë %s %1$s ata!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "aia %", "translation.test.invalid2": "ala %s", "translation.test.none": "Alla ambar!", "translation.test.world": "ambar", "trim_material.minecraft.amethyst": "Mirumírë", "trim_material.minecraft.copper": "Urus", "trim_material.minecraft.diamond": "Tinwírë", "trim_material.minecraft.emerald": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "Malta", "trim_material.minecraft.iron": "<PERSON><PERSON>", "trim_material.minecraft.lapis": "Luinion", "trim_material.minecraft.netherite": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstonë", "trim_material.minecraft.resin": "Suhtë", "trim_pattern.minecraft.bolt": "Turyaitë netwë", "trim_pattern.minecraft.coast": "Falasseva netwë", "trim_pattern.minecraft.dune": "Litseva netwë", "trim_pattern.minecraft.eye": "Hendeva netwë", "trim_pattern.minecraft.flow": "Hwinda netwë", "trim_pattern.minecraft.host": "Nasturwa netwë", "trim_pattern.minecraft.raiser": "<PERSON><PERSON>rova netwë", "trim_pattern.minecraft.rib": "<PERSON><PERSON><PERSON> netwë", "trim_pattern.minecraft.sentry": "Tirmova netwë", "trim_pattern.minecraft.shaper": "Cemnarwa netwë", "trim_pattern.minecraft.silence": "Quilda netwë", "trim_pattern.minecraft.snout": "Nengweva netwë", "trim_pattern.minecraft.spire": "Nelmava netwë", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON> netwë", "trim_pattern.minecraft.vex": "Tarastarwa netwë", "trim_pattern.minecraft.ward": "Ortirmova netwë", "trim_pattern.minecraft.wayfinder": "Telcontarwa netwë", "trim_pattern.minecraft.wild": "Lómava netwë", "tutorial.bundleInsert.description": "<PERSON><PERSON><PERSON> i forya meter napanë nati", "tutorial.bundleInsert.title": "<PERSON><PERSON> pocollë", "tutorial.craft_planks.description": "I tentaparma quí alya", "tutorial.craft_planks.title": "<PERSON><PERSON> toinë panor", "tutorial.find_tree.description": "Palpa sa ar comya töa", "tutorial.find_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.look.description": "<PERSON>hta tentamalya meter querë", "tutorial.look.title": "Oscenë", "tutorial.move.description": "Nirë %s meter capë", "tutorial.move.title": "Nirë %s, %s, %s, ar %s meter menë", "tutorial.open_inventory.description": "Nirë %s", "tutorial.open_inventory.title": "<PERSON><PERSON><PERSON>", "tutorial.punch_tree.description": "Nirë %s andavë", "tutorial.punch_tree.title": "Nancarë i alda", "tutorial.socialInteractions.description": "Nirë %s meter latya", "tutorial.socialInteractions.title": "<PERSON><PERSON><PERSON> la<PERSON>", "upgrade.minecraft.netherite_upgrade": "Neseritwa aryatalë"}