{"accessibility.onboarding.accessibility.button": "Tilgjenge...", "accessibility.onboarding.screen.narrator": "Kyv Enter fyr’ å slå på forteljaren", "accessibility.onboarding.screen.title": "Velkomen til Minecraft!\n\nYnskjer du å slå på forteljaren elder vitja vali fyre tilgjenge?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Tenartilskrift", "addServer.enterName": "<PERSON><PERSON> på tenar", "addServer.resourcePack": "Tilfangspakkar på tenar", "addServer.resourcePack.disabled": "Avslege", "addServer.resourcePack.enabled": "Åslege", "addServer.resourcePack.prompt": "Spyr", "addServer.title": "<PERSON>", "advMode.command": "Ko<PERSON>lls<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode": "Stòda", "advMode.mode.auto": "Taka uppatt", "advMode.mode.autoexec.bat": "Allstødt vyrk", "advMode.mode.conditional": "På vilkòr", "advMode.mode.redstone": "Tildriv", "advMode.mode.redstoneTriggered": "Treng redstone", "advMode.mode.sequence": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "<PERSON><PERSON> v<PERSON>", "advMode.notAllowed": "Må vera ein røktar i skapestòda", "advMode.notEnabled": "Styrebòdsblekker ero ’kje <PERSON> på denne tenaren", "advMode.previousOutput": "<PERSON><PERSON><PERSON>", "advMode.setCommand": "Set konsoll<PERSON><PERSON><PERSON><PERSON>d fyre blokk", "advMode.setCommand.success": "Styrebòd sett: %s", "advMode.trackOutput": "Spò<PERSON> u<PERSON>", "advMode.triggering": "Utløysande", "advMode.type": "Slag", "advancement.advancementNotFound": "«%s» er ei ukjend bragd", "advancements.adventure.adventuring_time.description": "<PERSON> alle lende", "advancements.adventure.adventuring_time.title": "Æventyr<PERSON>d", "advancements.adventure.arbalistic.description": "Drep fem ulike kvìkende med eitt låsbogeskot", "advancements.adventure.arbalistic.title": "Låsbogeskjotar", "advancements.adventure.avoid_vibration.description": "Smjug nær ein sculkegaumar elder vord fyr’ å hindra at han går deg", "advancements.adventure.avoid_vibration.title": "Smjug 100", "advancements.adventure.blowback.description": "Drep ei floga med ei bægd floge-vindlading", "advancements.adventure.blowback.title": "Atterblåster", "advancements.adventure.brush_armadillo.description": "<PERSON><PERSON> skjoldar frå eit beldedyr med ein kost", "advancements.adventure.brush_armadillo.title": "Skjoldfengd vòra", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON> midten av ei skotskiva frå minst 30 meter undan", "advancements.adventure.bullseye.title": "Blìkskot", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Gjer ei prydd potta av 4 skålbrot", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON>ars<PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Ver nær ein emnar nær han emnar ein emnar", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON> som emna emnarar", "advancements.adventure.fall_from_world_height.description": "Fall frå toppen av heimen (byggjegrensa) til botnen utan å døya", "advancements.adventure.fall_from_world_height.title": "Hòlor og hamrar", "advancements.adventure.heart_transplanter.description": "Set ein knerkadal med rette uppsetnaden millom tvei bleikeikestomnar", "advancements.adventure.heart_transplanter.title": "<PERSON><PERSON>", "advancements.adventure.hero_of_the_village.description": "Verna ei bygd imot ei herjing", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Hoppa på ei huningblokk fyr’ å døyva fallet", "advancements.adventure.honey_block_slide.title": "<PERSON> klistret", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON> e<PERSON>t fiendslege skræmslet", "advancements.adventure.kill_a_mob.title": "Skræmsleveidar", "advancements.adventure.kill_all_mobs.description": "<PERSON>ep eitt av kvart fiendslegt skræmsl", "advancements.adventure.kill_all_mobs.title": "Skræmsl veidde", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Drep eit kvìkende nær ein sculkeskundar", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Det breidest", "advancements.adventure.lighten_up.description": "Skrapa ei koparpera med ei øks fyr’ å gjera ’nne l<PERSON>are", "advancements.adventure.lighten_up.title": "Upplysande", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Verna ei bygd imot ein uynskt støyt utan å kveikja brand", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Yverspennsvern", "advancements.adventure.minecraft_trials_edition.description": "Tak deg inn i eit røynerom", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "Skjot med låsboge", "advancements.adventure.ol_betsy.title": "Skjotar’n", "advancements.adventure.overoverkill.description": "Tak skade på 50 hjarto med eitt einskilt stridsklubbeslag", "advancements.adventure.overoverkill.title": "Yver-<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Lat engjarne livna til med ljodet av toneleik frå ein skivespelar", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Les kraftsignalet frå ei uthoggi bokhylla med samanliknar", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Krafti av bokom", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON> upp ein illspåen kvelv med illspåen lykel", "advancements.adventure.revaulting.title": "Kvelv or kvelven", "advancements.adventure.root.description": "Ævent<PERSON>, grensking og strid", "advancements.adventure.root.title": "Æventyr", "advancements.adventure.salvage_sherd.description": "Kosta ei mistenkjeleg blokk fyr’ å få tak i skålbrot", "advancements.adventure.salvage_sherd.title": "Age fyre leivom", "advancements.adventure.shoot_arrow.description": "Skjot nokot med kolv", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "Sov i ei seng fyr’ å skifta uppstòdestad", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Drep ei beingrind frå minst 50 meter undan", "advancements.adventure.sniper_duel.title": "Snikskjotartevling", "advancements.adventure.spyglass_at_dragon.description": "Sj<PERSON> på enderdraken gjenom ein handkikar", "advancements.adventure.spyglass_at_dragon.title": "Er det eit flyge?", "advancements.adventure.spyglass_at_ghast.description": "Sj<PERSON> på eit ghast gjenom ein handkikar", "advancements.adventure.spyglass_at_ghast.title": "Er det ei blå<PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> på ein pavegauk gjenom ein handkikar", "advancements.adventure.spyglass_at_parrot.title": "Er det ein fugl?", "advancements.adventure.summon_iron_golem.description": "Kveik ein jarnkall til å verja ei bygd", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON> hjø<PERSON>", "advancements.adventure.throw_trident.description": "Kasta ei ljoster åt nokot.\nHugsa: Å kasta burt det einaste våpnet ditt er ’kje serlega klokt.", "advancements.adventure.throw_trident.title": "Eingongsvits", "advancements.adventure.totem_of_undying.description": "Nytta eit totem mot døying til å lura dauden", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> dauden", "advancements.adventure.trade.description": "Byt med ein bygdarbue", "advancements.adventure.trade.title": "For eit kaup!", "advancements.adventure.trade_at_world_height.description": "Byt med ein bygdarbue ved hæddargrensa fyre byggjing", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> prisar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Nytta kvar av desse smideskantarne minst éin gong: spir, tryne, riv<PERSON><PERSON>, vord, togn, mødar, tid<PERSON>n, vegvise", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Stilfull smiding", "advancements.adventure.trim_with_any_armor_pattern.description": "Laga ein prydd herbunad på eit smidjebord", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON> stil", "advancements.adventure.two_birds_one_arrow.description": "Drep tvau tankefoster med ein gjenomtrengjande kolv", "advancements.adventure.two_birds_one_arrow.title": "Tvei fuglar med éin kolv", "advancements.adventure.under_lock_and_key.description": "Nytta ein rø<PERSON> på ein kvelv", "advancements.adventure.under_lock_and_key.title": "Bak lås og slå", "advancements.adventure.use_lodestone.description": "Nytta ein kompås på ein leidarstein", "advancements.adventure.use_lodestone.title": "Var det den Leidi eg skulde?", "advancements.adventure.very_very_frightening.description": "Slå ned ein bygdarbue med ljonet", "advancements.adventure.very_very_frightening.title": "«Very very frightening»", "advancements.adventure.voluntary_exile.description": "Drep ein herjeleidar.\nDet kann vera lurt å halda seg undan bygder ei stund...", "advancements.adventure.voluntary_exile.title": "Friviljug utlægd", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Gakk på mjøll... utan å søkka ned i ’nne", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lett som ein hjase", "advancements.adventure.who_needs_rockets.description": "Nytta ei vindlading til å skjota deg upp 7 blekker", "advancements.adventure.who_needs_rockets.title": "<PERSON><PERSON> treng glø<PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "Svara ein ransbue med same mynt", "advancements.adventure.whos_the_pillager_now.title": "<PERSON>ven er ransbue no?", "advancements.empty": "Det tykkjest ikkje vera nokot her ...", "advancements.end.dragon_breath.description": "San<PERSON> drakeande på ei glasflaska", "advancements.end.dragon_breath.title": "Du treng munnvatn", "advancements.end.dragon_egg.description": "<PERSON><PERSON> drakeegget", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.description": "Finn skalvenger", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON> er grensa", "advancements.end.enter_end_gateway.description": "Fly øyi", "advancements.end.enter_end_gateway.title": "<PERSON>rd åt ein fjerr stad", "advancements.end.find_end_city.description": "Gakk inn; kvat kann vel henda?", "advancements.end.find_end_city.title": "Byen ved enden av spelet", "advancements.end.kill_dragon.description": "<PERSON>kka til", "advancements.end.kill_dragon.title": "<PERSON><PERSON>", "advancements.end.levitate.description": "Sviv yver 50 blekker frå eit shulkeråtak", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> utsyn her uppe", "advancements.end.respawn_dragon.description": "Al endedraken å nyo", "advancements.end.respawn_dragon.title": "Enden... endå ein gong...", "advancements.end.root.description": "Elder by<PERSON><PERSON>gi?", "advancements.end.root.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Få ei hjelpeònd til å sleppa ei kaka på ei noteblokk", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "F<PERSON>dslardagssong", "advancements.husbandry.allay_deliver_item_to_player.description": "Få ei hjelpeònd til å heimta ting åt deg", "advancements.husbandry.allay_deliver_item_to_player.title": "Eg er vìnen din", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>a ein axolotl i eit spann", "advancements.husbandry.axolotl_in_a_bucket.title": "Det søtaste rædyret", "advancements.husbandry.balanced_diet.description": "Et alt som du kann eta, jamvel um det ikkje er godt fyre deg", "advancements.husbandry.balanced_diet.title": "Eit jamvegtugt kosthald", "advancements.husbandry.breed_all_animals.description": "Avla alle dyri!", "advancements.husbandry.breed_all_animals.title": "Tvau og tvau", "advancements.husbandry.breed_an_animal.description": "Avla tvau dyr med kvarandre", "advancements.husbandry.breed_an_animal.title": "Pavegaukarne og skjåvengjorna", "advancements.husbandry.complete_catalogue.description": "Tem alle katteslag!", "advancements.husbandry.complete_catalogue.title": "Full kattalog", "advancements.husbandry.feed_snifflet.description": "<PERSON> ein tevung", "advancements.husbandry.feed_snifflet.title": "Småtev", "advancements.husbandry.fishy_business.description": "<PERSON>a ein fisk", "advancements.husbandry.fishy_business.title": "Skit fiske!", "advancements.husbandry.froglights.description": "Hav alle froskeljosi i skreppa di", "advancements.husbandry.froglights.title": "Med alle krafter i hop!", "advancements.husbandry.kill_axolotl_target.description": "Gakk i lag med ein axolotl og vinn ein kamp", "advancements.husbandry.kill_axolotl_target.title": "Ein vìn i nauden!", "advancements.husbandry.leash_all_frog_variants.description": "Hav kvart froskeslag i band", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON><PERSON> gjengen hoppar til bys", "advancements.husbandry.make_a_sign_glow.description": "Få teksti på eit skilt til å gløda", "advancements.husbandry.make_a_sign_glow.title": "Strålande!", "advancements.husbandry.netherite_hoe.description": "Nytta ein netherittbarre til å betra ein ljå, og tenk å nyo yver livsvali dine", "advancements.husbandry.netherite_hoe.title": "Ålvorsa<PERSON> umhug", "advancements.husbandry.obtain_sniffer_egg.description": "Få tak i eit tevaregg", "advancements.husbandry.obtain_sniffer_egg.title": "Spanande teft", "advancements.husbandry.place_dried_ghast_in_water.description": "Set eit innturkat ghast i vatn", "advancements.husbandry.place_dried_ghast_in_water.title": "Sløkk torsten!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON> eit tevarfrjo", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> slær rot", "advancements.husbandry.plant_seed.description": "<PERSON>å eit frjo og sjå det veksa", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Tak bort brynja åt ein ulv med soks", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON> klypp!", "advancements.husbandry.repair_wolf_armor.description": "<PERSON>øt ei broti ulvebrynja med beltedyr-sk<PERSON>ldar", "advancements.husbandry.repair_wolf_armor.title": "God som ny", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Ro med ei geit", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.root.description": "Heimen er full av vìner og mat", "advancements.husbandry.root.title": "Jordbruk", "advancements.husbandry.safely_harvest_honey.description": "Nytta eit bål til å sanka huning frå ein biekube med flaska utan å eggja upp biorna", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> vår gjest", "advancements.husbandry.silk_touch_nest.description": "Nytta varsemd til å flytja eit biebol med 3 bior inni utan at dei fljuga ut", "advancements.husbandry.silk_touch_nest.title": "Ingen biverknad", "advancements.husbandry.tactical_fishing.description": "Fang ein fisk... utan fiskestong!", "advancements.husbandry.tactical_fishing.title": "Taktiskt fiske", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON>a eit rovetroll i eit spann", "advancements.husbandry.tadpole_in_a_bucket.title": "Byttedyr", "advancements.husbandry.tame_an_animal.description": "Tem eit dyr", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_off.description": "Skrapa burt vaks frå ei koparblokk!", "advancements.husbandry.wax_off.title": "Vaks av", "advancements.husbandry.wax_on.description": "Smỳr ei vakskaka på ei koparblokk!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON> på", "advancements.husbandry.whole_pack.description": "Tem ein ulv av korjo slag", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> flokken", "advancements.nether.all_effects.description": "Hav alle verknaderne på samstundes", "advancements.nether.all_effects.title": "Korso komo med hegat?", "advancements.nether.all_potions.description": "Hav alle trolldrykkverknaderne på samstundes", "advancements.nether.all_potions.title": "<PERSON>it ill<PERSON>gt brygg", "advancements.nether.brew_potion.description": "Brygg<PERSON> ein trolldrykk", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Lad eit uppstòdeakkjer til det er fullt", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON><PERSON><PERSON> heilt «nio» liv", "advancements.nether.create_beacon.description": "<PERSON>gg og set ned ein vìte", "advancements.nether.create_beacon.title": "<PERSON>kte varde", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON>v ein vìte full styrke", "advancements.nether.create_full_beacon.title": "Vìtebyggjar", "advancements.nether.distract_piglin.description": "Avleid ein piglin med gull", "advancements.nether.distract_piglin.title": "Ooo, skinande", "advancements.nether.explore_nether.description": "Grenska alle lendi i Nether", "advancements.nether.explore_nether.title": "He<PERSON> f<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "Nytta nether til å fara 7 km i yverheimen", "advancements.nether.fast_travel.title": "Underromsbubla", "advancements.nether.find_bastion.description": "Gakk inn i ei borgleiv", "advancements.nether.find_bastion.title": "<PERSON><PERSON> gamle dagar", "advancements.nether.find_fortress.description": "Brjot deg inn i eit Nether-varstøde", "advancements.nether.find_fortress.title": "<PERSON><PERSON> fæ<PERSON> varstøde", "advancements.nether.get_wither_skull.description": "Få tak i hovudet av ei witherbeingrind", "advancements.nether.get_wither_skull.title": "Nefst utriveleg beingrind", "advancements.nether.loot_bastion.description": "Tøm ei kista i ei borgleiv", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Få tak i full netherittbrynja", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON><PERSON> meg i leiver", "advancements.nether.obtain_ancient_debris.description": "Få tak i forne leiver", "advancements.nether.obtain_ancient_debris.title": "<PERSON><PERSON> i dypti", "advancements.nether.obtain_blaze_rod.description": "Tak ein logestav frå ein loge", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.description": "Få tak i gråtande ramntinna", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON> skjer lauk?", "advancements.nether.return_to_sender.description": "<PERSON>ep eit ghast med ei eldkula", "advancements.nether.return_to_sender.title": "Send attende", "advancements.nether.ride_strider.description": "Rid på ein stìgar med ein vrìdsopp på stong", "advancements.nether.ride_strider.title": "<PERSON><PERSON> b<PERSON>ten heve føter", "advancements.nether.ride_strider_in_overworld_lava.description": "Tak ein stìgar på ei lòòòòng ferd i ei raunt<PERSON>ørn i yverheimen", "advancements.nether.ride_strider_in_overworld_lava.title": "Nett som heime", "advancements.nether.root.description": "Tak med sumarklæde", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>n", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON> hæ<PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON>a eit ghast frå nether, tak det trygt med heim til yverheimen... og so drep det", "advancements.nether.uneasy_alliance.title": "Urolegt samband", "advancements.nether.use_lodestone.description": "Nytta ein kompås på ein leidarstein", "advancements.nether.use_lodestone.title": "Var det den Leidi eg skulde?", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Veik og so læk ein bygdarnåe", "advancements.story.cure_zombie_villager.title": "Daudelækjar", "advancements.story.deflect_arrow.description": "Verna deg frå ein kolv med ein skjold", "advancements.story.deflect_arrow.title": "Ikkje i dag, takk", "advancements.story.enchant_item.description": "Galdra nokot med eit galdrebord", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Gakk inn i endelìdet", "advancements.story.enter_the_end.title": "Enden?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, k<PERSON>ik, og gakk inn i eit netherlìd", "advancements.story.enter_the_nether.title": "Me ljota fara djupare", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON>g eit enderauga", "advancements.story.follow_ender_eye.title": "Augnaleite", "advancements.story.form_obsidian.description": "Få tak i ei blokk av ramntinna", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "<PERSON>ra hakka di", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> galen", "advancements.story.lava_bucket.description": "<PERSON>yll eit spann med raun", "advancements.story.lava_bucket.title": "Heite saker", "advancements.story.mine_diamond.description": "Få tak i demantar", "advancements.story.mine_diamond.title": "Demantar!", "advancements.story.mine_stone.description": "Hakka stein med n<PERSON>hakka di", "advancements.story.mine_stone.title": "Steinalderen", "advancements.story.obtain_armor.description": "Verna deg med ein lùt av jarnbrynja", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON><PERSON> på deg", "advancements.story.root.description": "Hjartat og soga åt spelet", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Demantb<PERSON>ja bjergar liv", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON>d meg i demantar", "advancements.story.smelt_iron.description": "S<PERSON>ta eit jarnstykke", "advancements.story.smelt_iron.title": "Få tak i jarnvòra", "advancements.story.upgrade_tools.description": "Laga ei betre hakka", "advancements.story.upgrade_tools.title": "Betring", "advancements.toast.challenge": "Utbjoding fullgjord!", "advancements.toast.goal": "M<PERSON>l nått!", "advancements.toast.task": "Bragd gjord!", "argument.anchor.invalid": "«%s» er ein ugild akkjerstad fyr’ eining", "argument.angle.incomplete": "Uheilsleg (venta 1 vinkel)", "argument.angle.invalid": "<PERSON><PERSON><PERSON> v<PERSON>", "argument.block.id.invalid": "«%s» er eit ukjent blokkslag", "argument.block.property.duplicate": "<PERSON><PERSON><PERSON> «%s» kann berre verda sett éin gong fyre blokki %s", "argument.block.property.invalid": "Blokki %s godtek ikkje «%s» fyre gjerdi %s", "argument.block.property.novalue": "Verde fyre gjerdi «%s» på blokki %s var ventat", "argument.block.property.unclosed": "Attlatande ] var ventat fyre gjerderna åt blokktilhøve", "argument.block.property.unknown": "Blokki %s heve ’kje gjerdi «%s»", "argument.block.tag.disallowed": "Merk<PERSON><PERSON><PERSON> ero ’kje lø<PERSON>vde her. <PERSON><PERSON> blekker", "argument.color.invalid": "«%s» er ein ukjend lìt", "argument.component.invalid": "«%s» er ein ugild svall-lùt", "argument.criteria.invalid": "«%s» er eit ukjent krav", "argument.dimension.invalid": "«%s» er ein ukjend heim", "argument.double.big": "Tvitalet kann ’kje vera større en %s. Fann %s", "argument.double.low": "Tvitalet kann ’kje vera mindre en %s. Fann %s", "argument.entity.invalid": "<PERSON><PERSON><PERSON><PERSON> namn elder U<PERSON><PERSON>", "argument.entity.notfound.entity": "<PERSON><PERSON> eining vart fundi", "argument.entity.notfound.player": "Ingen leikar vardt funden", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON> med bragder", "argument.entity.options.distance.description": "Fråstand til eining", "argument.entity.options.distance.negative": "Fråstanden kann ’kje vera neitande", "argument.entity.options.dx.description": "Einingar millom x og x + dx", "argument.entity.options.dy.description": "Ein<PERSON>r millom y og y + dy", "argument.entity.options.dz.description": "Einingar millom z og z + dz", "argument.entity.options.gamemode.description": "Le<PERSON>rar med spelstòda", "argument.entity.options.inapplicable": "<PERSON>t «%s» er ’kje nyttande her", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.level.negative": "Stìget lyt ikkje vera neitande", "argument.entity.options.limit.description": "Høgste tal på einingar til å koma attende", "argument.entity.options.limit.toosmall": "Grensa må vera minst 1", "argument.entity.options.mode.invalid": "«%s» er ei ugild elder ukjend spelstòda", "argument.entity.options.name.description": "Einingnamn", "argument.entity.options.nbt.description": "Einingar med NTB", "argument.entity.options.predicate.description": "Tilmåtat predikat", "argument.entity.options.scores.description": "Einingar med skòretal", "argument.entity.options.sort.description": "<PERSON><PERSON> <PERSON>", "argument.entity.options.sort.irreversible": "«%s» er eit ugildt elder ukjent skiljingslag", "argument.entity.options.tag.description": "Einingar med merkjelapp", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON> på laget", "argument.entity.options.type.description": "Einingar av slaget", "argument.entity.options.type.invalid": "Ugildt elder ukjent e<PERSON> «%s»", "argument.entity.options.unknown": "«%s» er eit ukjent val", "argument.entity.options.unterminated": "<PERSON>e på val var ventat", "argument.entity.options.valueless": "Verdet fyre valet «%s» var ventat", "argument.entity.options.x.description": "x-stad", "argument.entity.options.x_rotation.description": "X-ridingi åt e<PERSON>i", "argument.entity.options.y.description": "y-stad", "argument.entity.options.y_rotation.description": "Y-ridingi åt e<PERSON>i", "argument.entity.options.z.description": "z-stad", "argument.entity.selector.allEntities": "Alle einingar", "argument.entity.selector.allPlayers": "<PERSON>e leika<PERSON>", "argument.entity.selector.missing": "Veljarslag er saknat", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON>", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> <PERSON>kje lø<PERSON>t", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON> leikar", "argument.entity.selector.self": "Gjeldande eining", "argument.entity.selector.unknown": "«%s» er eit ukjent veljarslag", "argument.entity.toomany": "Berre éi eining er løyvd, men den uppgjevne veljaren gjev løyve til meir’ en éi", "argument.enum.invalid": "«%s» er eit ugildt verde", "argument.float.big": "Fljottalet kann ’kje vera større en %s; fann %s", "argument.float.low": "Fljo<PERSON>let kann ’kje vera mindre en %s; fann %s", "argument.gamemode.invalid": "Ukjend spelstòda: %s", "argument.hexcolor.invalid": "<PERSON><PERSON>ld hex-lìt<PERSON> «%s»", "argument.id.invalid": "Ugild ID", "argument.id.unknown": "«%s» er ein ukjend ID", "argument.integer.big": "Heiltalet kann ’kje vera større en %s. Fann %s", "argument.integer.low": "Heiltalet kann ’kje vera mindre en %s. Fann %s", "argument.item.id.invalid": "«%s» er ein ukjend ting", "argument.item.tag.disallowed": "Merk<PERSON><PERSON><PERSON> ero ’kje løyvde her. <PERSON><PERSON> rø<PERSON> ting", "argument.literal.incorrect": "Strengkonstanten %s var ventad", "argument.long.big": "Long-verde kann ’kje vera større en %s; fann %s", "argument.long.low": "Long-verde kann ’kje vera mindre en %s; fann %s", "argument.message.too_long": "Svallbòd var for langt (%s > høgst %s teikn)", "argument.nbt.array.invalid": "«%s» er eit ugildt uppsetsslag", "argument.nbt.array.mixed": "<PERSON>nn <PERSON>kje setja inn %s i %s", "argument.nbt.expected.compound": "Samansett merke var ventat", "argument.nbt.expected.key": "Lykel var ventad", "argument.nbt.expected.value": "Verde var ventat", "argument.nbt.list.mixed": "<PERSON>nn <PERSON>kje setja inn %s i lista yver %s", "argument.nbt.trailing": "Fleire data våro ’kje ventade", "argument.player.entities": "<PERSON><PERSON> le<PERSON> kunn<PERSON> verda påverka av detta styrebòdet, men den uppgjevne veljaren femner òg einingar", "argument.player.toomany": "<PERSON><PERSON> éin leikar er løyvd, men den uppgjevne veljaren gjev løyve til meir’ en éin", "argument.player.unknown": "<PERSON><PERSON> le<PERSON> finst ikkje", "argument.pos.missing.double": "Ein koordinat var ventad", "argument.pos.missing.int": "Ein blokkstad var ventad", "argument.pos.mixed": "<PERSON>nn <PERSON>kje blanda heimskoordinatar og heimlege koordinatar (alt må anten nytta ^ elder ’kje nokot)", "argument.pos.outofbounds": "Denne staden er utanfyre dei løyvde grensorna.", "argument.pos.outofworld": "Denne staden er utanfyr’ heimen!", "argument.pos.unloaded": "Denne staden er ’kje ladd inn", "argument.pos2d.incomplete": "Uheilsleg (2 koordinatar var ventade)", "argument.pos3d.incomplete": "Uheilsleg (3 koordinatar var ventade)", "argument.range.empty": "Eit verde elder ei verderekkja var ventat/-d", "argument.range.ints": "<PERSON><PERSON> heile tal er løyvde, ikk<PERSON> desima<PERSON>l", "argument.range.swapped": "<PERSON>st kann ’kje vera større en høgst", "argument.resource.invalid_type": "Elementet «%s» heve det range slaget «%s» (ventade «%s»)", "argument.resource.not_found": "Kann ’kje finna elementet «%s» av slaget «%s»", "argument.resource_or_id.failed_to_parse": "<PERSON><PERSON> ’kje tyda bygnad: %s", "argument.resource_or_id.invalid": "<PERSON>gild <PERSON> elder merke", "argument.resource_or_id.no_such_element": "Kann ’kje finna elementet «%s» i samnet «%s»", "argument.resource_selector.not_found": "<PERSON><PERSON> samsvòr fyre veljar «%s» av slaget «%s»", "argument.resource_tag.invalid_type": "Merkjelappen «%s» heve det range slaget «%s» (ventade «%s»)", "argument.resource_tag.not_found": "<PERSON>nn ’kje finna merkjelappen «%s» av slaget «%s»", "argument.rotation.incomplete": "Uheilsleg (2 koordinatar var ventade)", "argument.scoreHolder.empty": "Ingi relevante skòretalshaldarar vordo fundne", "argument.scoreboardDisplaySlot.invalid": "«%s» er ein ukjend visingstad", "argument.style.invalid": "Ugild stil: %s", "argument.time.invalid_tick_count": "Tikketal må vera ikkje-neitande", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON>", "argument.time.tick_count_too_low": "Tikketalet må ’kje vera lægre en %s; fann %s", "argument.uuid.invalid": "Ugild UUID", "argument.waypoint.invalid": "Vald eining er ikkje eit leidarmerke", "arguments.block.tag.unknown": "«%s» er ein ukjend blokkmerkjelapp", "arguments.function.tag.unknown": "«%s» er ein ukjend verkendemerkjelapp", "arguments.function.unknown": "«%s» er eit ukjent verkende", "arguments.item.component.expected": "Ventade tingkomponent", "arguments.item.component.malformed": "Vanskapad «%s»-komponent: «%s»", "arguments.item.component.repeated": "Tingkomponenten «%s» vardt tviteken, men berre eitt verde lèt seg skilja ut", "arguments.item.component.unknown": "Ukjend tingkomponent: «%s»", "arguments.item.malformed": "Vanskapad ting: «%s»", "arguments.item.overstacked": "%s kann berre verda lødd upp til %s", "arguments.item.predicate.malformed": "Vanskapad «%s»-predikat: «%s»", "arguments.item.predicate.unknown": "«%s» er eit ukjent tingpredikat", "arguments.item.tag.unknown": "«%s» er ein ukjend tingmerkjelapp", "arguments.nbtpath.node.invalid": "Ugild NBT-baneelement", "arguments.nbtpath.nothing_found": "Fann ingi samsvarande element %s", "arguments.nbtpath.too_deep": "Fylgjande NBT for djupt nystad", "arguments.nbtpath.too_large": "Fylgjande NBT for stor", "arguments.objective.notFound": "«%s» er eit ukjent mål på skòretavla", "arguments.objective.readonly": "Målet «%s» på skòretavla er skrivevernat", "arguments.operation.div0": "<PERSON><PERSON> <PERSON>kje kluva null", "arguments.operation.invalid": "<PERSON><PERSON><PERSON>", "arguments.swizzle.invalid": "Ugild akselsamanstelling. Ei samanstelling av «x», «y» og «z» var ventad", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "+%s%% %s", "attribute.name.armor": "<PERSON><PERSON>", "attribute.name.armor_toughness": "Brynjestyrke", "attribute.name.attack_damage": "Åtaksskade", "attribute.name.attack_knockback": "Atterslag ved åtak", "attribute.name.attack_speed": "Åtakssnarleike", "attribute.name.block_break_speed": "Blokkbrots-snarleike", "attribute.name.block_interaction_range": "Fråstand fyre blokksamhandling", "attribute.name.burning_time": "<PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kamerafråstand", "attribute.name.entity_interaction_range": "Fråstand fyr’ einingsamhandling", "attribute.name.explosion_knockback_resistance": "Motstand mot atterslag frå sprengnader", "attribute.name.fall_damage_multiplier": "Mangfaldar fyre fallskade", "attribute.name.flying_speed": "Flògsnarleike", "attribute.name.follow_range": "Fylgjefråstand fyre kvìkende", "attribute.name.generic.armor": "<PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Styrke på brynja", "attribute.name.generic.attack_damage": "Åtaksskade", "attribute.name.generic.attack_knockback": "Atterslag ved åtak", "attribute.name.generic.attack_speed": "Åtakssnarleike", "attribute.name.generic.block_interaction_range": "Fråstand fyre blokksamhandling", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Fråstand fyr’ einingsamhandling", "attribute.name.generic.explosion_knockback_resistance": "Motstand mot atterslag frå sprengnader", "attribute.name.generic.fall_damage_multiplier": "Mangfaldar fyre fallskade", "attribute.name.generic.flying_speed": "Flògsnarleike", "attribute.name.generic.follow_range": "Fråstand som skræmsl vil fylgja deg frå", "attribute.name.generic.gravity": "Tyngdarkraft", "attribute.name.generic.jump_strength": "Hoppstyrke", "attribute.name.generic.knockback_resistance": "Motstand mot atterslag", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Høgste uppsog", "attribute.name.generic.max_health": "Høgst helsa", "attribute.name.generic.movement_efficiency": "R<PERSON><PERSON>ledugleike", "attribute.name.generic.movement_speed": "<PERSON>narleike", "attribute.name.generic.oxygen_bonus": "attpånøre", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.generic.scale": "Storleike", "attribute.name.generic.step_height": "St<PERSON><PERSON><PERSON>dd", "attribute.name.generic.water_movement_efficiency": "Rørsledugleike i vatn", "attribute.name.gravity": "Tyngdarkraft", "attribute.name.horse.jump_strength": "Øykjehoppstyrke", "attribute.name.jump_strength": "Hoppstyrke", "attribute.name.knockback_resistance": "Motstand mot atterslag", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Høgst uppsog", "attribute.name.max_health": "Høgst helsa", "attribute.name.mining_efficiency": "Gravedugle<PERSON>", "attribute.name.movement_efficiency": "R<PERSON><PERSON>ledugleike", "attribute.name.movement_speed": "<PERSON>narleike", "attribute.name.oxygen_bonus": "Attpånøre", "attribute.name.player.block_break_speed": "Blokkbrots-snarleike", "attribute.name.player.block_interaction_range": "Fråstand fyre blokksamhandling", "attribute.name.player.entity_interaction_range": "Fråstand fyr’ einingsamhandling", "attribute.name.player.mining_efficiency": "Gravedugle<PERSON>", "attribute.name.player.sneaking_speed": "Smjugesnarleike", "attribute.name.player.submerged_mining_speed": "Gravesnarleike under vatn", "attribute.name.player.sweeping_damage_ratio": "Strjukan<PERSON> skade-mùn", "attribute.name.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.scale": "Storleike", "attribute.name.sneaking_speed": "Smjugesnarleike", "attribute.name.spawn_reinforcements": "Nåestyrkjingar", "attribute.name.step_height": "St<PERSON><PERSON><PERSON>dd", "attribute.name.submerged_mining_speed": "Gravesnarleike under vatn", "attribute.name.sweeping_damage_ratio": "Strjukan<PERSON> skade-mùn", "attribute.name.tempt_range": "Freistefråstand fyre kvìkende", "attribute.name.water_movement_efficiency": "Rørsledugleike i vatn", "attribute.name.waypoint_receive_range": "Mottakefråstand åt le<PERSON>rmerke", "attribute.name.waypoint_transmit_range": "Sendefråstand åt le<PERSON>", "attribute.name.zombie.spawn_reinforcements": "Nåestyrkjingar", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bambusskog", "biome.minecraft.basalt_deltas": "Basaltøyr", "biome.minecraft.beach": "Strond", "biome.minecraft.birch_forest": "Byrke", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "Kaldt hav", "biome.minecraft.crimson_forest": "Blodskog", "biome.minecraft.dark_forest": "Myrkskog", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON> kaldt hav", "biome.minecraft.deep_dark": "<PERSON> djupe myrke", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON> froset hav", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON> lunket hav", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON> hav", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Dropsteinsholor", "biome.minecraft.end_barrens": "Endeø<PERSON>a", "biome.minecraft.end_highlands": "Endehøglende", "biome.minecraft.end_midlands": "Endemidlende", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "Blomskog", "biome.minecraft.forest": "Skog", "biome.minecraft.frozen_ocean": "Frose hav", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON>", "biome.minecraft.ice_spikes": "<PERSON><PERSON><PERSON>", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "Regnskog", "biome.minecraft.lukewarm_ocean": "Lunket hav", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON><PERSON> holor", "biome.minecraft.mangrove_swamp": "Mangrovemyr", "biome.minecraft.meadow": "Eng", "biome.minecraft.mushroom_fields": "Soppslettor", "biome.minecraft.nether_wastes": "Netherøydor", "biome.minecraft.ocean": "Hav", "biome.minecraft.old_growth_birch_forest": "Gamalt byrke", "biome.minecraft.old_growth_pine_taiga": "Gamalt fyre", "biome.minecraft.old_growth_spruce_taiga": "Gamalt grene", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Slettor", "biome.minecraft.river": "Å", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Grasmohøgsletta", "biome.minecraft.small_end_islands": "Små endeøyar", "biome.minecraft.snowy_beach": "Snjotekt strond", "biome.minecraft.snowy_plains": "Snjotekte vidder", "biome.minecraft.snowy_slopes": "Snjotekte lider", "biome.minecraft.snowy_taiga": "Snjotekt barskog", "biome.minecraft.soul_sand_valley": "Sålarsandsdal", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON> re<PERSON>", "biome.minecraft.stony_peaks": "<PERSON><PERSON> tindar", "biome.minecraft.stony_shore": "Svadberg", "biome.minecraft.sunflower_plains": "Solvendeleng", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Barskog", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "Tomrome<PERSON>", "biome.minecraft.warm_ocean": "Heitt hav", "biome.minecraft.warped_forest": "Vrìdskog", "biome.minecraft.windswept_forest": "Vindsliten skog", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_hills": "Vindslitne åsar", "biome.minecraft.windswept_savanna": "Vindsliten grasmo", "biome.minecraft.wooded_badlands": "Skogvaks<PERSON> s<PERSON>", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "Akasiegrind", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Akasiestomn", "block.minecraft.acacia_planks": "Akasiebord", "block.minecraft.acacia_pressure_plate": "Akasie-tyng<PERSON>fjøl", "block.minecraft.acacia_sapling": "A<PERSON><PERSON><PERSON>ning", "block.minecraft.acacia_sign": "Akasieskilt", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Akasietropp", "block.minecraft.acacia_trapdoor": "Akasielem", "block.minecraft.acacia_wall_hanging_sign": "<PERSON>ande akasieskilt på vegg", "block.minecraft.acacia_wall_sign": "Akasieveggskilt", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.activator_rail": "Åslagarskjena", "block.minecraft.air": "Vind", "block.minecraft.allium": "<PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametystblokk", "block.minecraft.amethyst_cluster": "Ametystklyngja", "block.minecraft.ancient_debris": "<PERSON><PERSON> leiver", "block.minecraft.andesite": "<PERSON><PERSON>", "block.minecraft.andesite_slab": "Andesitthella", "block.minecraft.andesite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite_wall": "Andesittvegg", "block.minecraft.anvil": "Sted", "block.minecraft.attached_melon_stem": "Fest melonstylk", "block.minecraft.attached_pumpkin_stem": "Fest graskjerstylk", "block.minecraft.azalea": "Lyn<PERSON>sa", "block.minecraft.azalea_leaves": "Lauv av lyngrosetre", "block.minecraft.azure_bluet": "Houstonia", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblokk", "block.minecraft.bamboo_button": "Bambusknapp", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_fence": "Bambusgard", "block.minecraft.bamboo_fence_gate": "Bambusgrind", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_mosaic": "Bambusmosaikk", "block.minecraft.bamboo_mosaic_slab": "Hella av bambusmosaikk", "block.minecraft.bamboo_mosaic_stairs": "Tropp av bambusmosaikk", "block.minecraft.bamboo_planks": "Bambusbord", "block.minecraft.bamboo_pressure_plate": "Bambus-tyngdarfjøl", "block.minecraft.bamboo_sapling": "Bambusskot", "block.minecraft.bamboo_sign": "Bambusskilt", "block.minecraft.bamboo_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_stairs": "Bambustropp", "block.minecraft.bamboo_trapdoor": "Bambuslem", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> bambusskilt på vegg", "block.minecraft.bamboo_wall_sign": "Bambusveggskilt", "block.minecraft.banner.base.black": "<PERSON><PERSON>t botn", "block.minecraft.banner.base.blue": "Blå botn", "block.minecraft.banner.base.brown": "<PERSON><PERSON> botn", "block.minecraft.banner.base.cyan": "Blågrøn botn", "block.minecraft.banner.base.gray": "Grå botn", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.light_blue": "Ljosblå botn", "block.minecraft.banner.base.light_gray": "Ljosgrå botn", "block.minecraft.banner.base.lime": "Limegr<PERSON><PERSON> botn", "block.minecraft.banner.base.magenta": "Ljosblåraud botn", "block.minecraft.banner.base.orange": "Ljosbrandgul botn", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.red": "<PERSON><PERSON> botn", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.yellow": "<PERSON>ul botn", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.light_blue": "Ljosbl<PERSON><PERSON> bord", "block.minecraft.banner.border.light_gray": "L<PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.magenta": "Ljosblåraudt bord", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.purple": "Blå<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.yellow": "<PERSON><PERSON> bord", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>ster", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "Ljosblått tiglmynster", "block.minecraft.banner.bricks.light_gray": "Ljosgr<PERSON>tt t<PERSON>lm<PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.magenta": "Ljosblåraudt tiglmynster", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON> tiglmynster", "block.minecraft.banner.bricks.pink": "<PERSON>jos<PERSON><PERSON><PERSON> tiglmynster", "block.minecraft.banner.bricks.purple": "Blåraudt tiglmynster", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON> skiva", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Blågr<PERSON><PERSON> skiva", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON> skiva", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Ljosblå skiva", "block.minecraft.banner.circle.light_gray": "Ljosgrå skiva", "block.minecraft.banner.circle.lime": "Ljosgr<PERSON><PERSON> skiva", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON>va", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "Gul skiva", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.blue": "B<PERSON>å creeper", "block.minecraft.banner.creeper.brown": "<PERSON>run creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_blue": "Ljosblå creeper", "block.minecraft.banner.creeper.light_gray": "Ljosgrå creeper", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.orange": "Brandgul creeper", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.red": "Raud creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.yellow": "Gul creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON><PERSON> andreskross", "block.minecraft.banner.cross.blue": "Blå andreskross", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Blågrø<PERSON> and<PERSON>", "block.minecraft.banner.cross.gray": "Grå andreskross", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "Ljosblå andreskross", "block.minecraft.banner.cross.light_gray": "Ljosgrå andreskross", "block.minecraft.banner.cross.lime": "Ljosgrø<PERSON>", "block.minecraft.banner.cross.magenta": "Ljos<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> andreskross", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON>t bord med taggar", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>t bord med taggar", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.light_blue": "Ljosblått bord med taggar", "block.minecraft.banner.curly_border.light_gray": "Ljosgr<PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.magenta": "Ljosblåraudt bord med taggar", "block.minecraft.banner.curly_border.orange": "Brand<PERSON>lt bord med taggar", "block.minecraft.banner.curly_border.pink": "Ljos<PERSON><PERSON>t bord med taggar", "block.minecraft.banner.curly_border.purple": "Blåraudt bord med taggar", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> bord med taggar", "block.minecraft.banner.curly_border.yellow": "Gult bord med taggar", "block.minecraft.banner.diagonal_left.black": "Svòrt sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.blue": "Blå sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON> s<PERSON>lovi frå vinstre", "block.minecraft.banner.diagonal_left.cyan": "Blågrøn sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.gray": "Grå sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>idklovi frå vinstre", "block.minecraft.banner.diagonal_left.light_blue": "Ljosblå sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.light_gray": "Ljosgrå sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.lime": "Ljosgrøn sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.magenta": "Ljosblåraud sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.orange": "Brandgul sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON><PERSON> sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.purple": "Blåraud sneidklovi frå vinstre", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON> frå vinstre", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> s<PERSON>idklovi frå vinstre", "block.minecraft.banner.diagonal_left.yellow": "Gul sneidklovi frå vinstre", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.blue": "Blå sneidklovi", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "Blågrø<PERSON>", "block.minecraft.banner.diagonal_right.gray": "Grå sneidklovi", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Ljosblå sneidklovi", "block.minecraft.banner.diagonal_right.light_gray": "Ljosgrå sneidklovi", "block.minecraft.banner.diagonal_right.lime": "Ljosgrø<PERSON>", "block.minecraft.banner.diagonal_right.magenta": "Ljosb<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.yellow": "Gul sneidklovi", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON><PERSON> umvend s<PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "Blå umvend s<PERSON>i", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON> um<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "Blågrø<PERSON> umvend <PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON> umvend s<PERSON>", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Ljosblå umvend s<PERSON>lovi", "block.minecraft.banner.diagonal_up_left.light_gray": "Ljosgrå umvend s<PERSON>i", "block.minecraft.banner.diagonal_up_left.lime": "Ljosgrø<PERSON> umvend <PERSON>", "block.minecraft.banner.diagonal_up_left.magenta": "Ljosblåra<PERSON> umvend <PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON> um<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> umvend <PERSON>", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.black": "Svòrt umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.blue": "Blå umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON> umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.cyan": "Blågrøn umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.gray": "Grå umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON> umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.light_blue": "Ljosblå umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.light_gray": "Ljosgrå umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.lime": "Ljosgrøn umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.magenta": "Ljosblåraud umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.orange": "Brand<PERSON>l umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON><PERSON> umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.purple": "Blåraud umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON> umvend s<PERSON>lovi frå vinstre", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> umvend sneidklovi frå vinstre", "block.minecraft.banner.diagonal_up_right.yellow": "Gul umvend sneidklovi frå vinstre", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.blue": "Blå straum", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> straum", "block.minecraft.banner.flow.cyan": "Blågrø<PERSON> straum", "block.minecraft.banner.flow.gray": "Grå straum", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.light_blue": "Ljosblå straum", "block.minecraft.banner.flow.light_gray": "Ljosgrå straum", "block.minecraft.banner.flow.lime": "Ljosgrø<PERSON> straum", "block.minecraft.banner.flow.magenta": "L<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.red": "<PERSON><PERSON> straum", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> straum", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> straum", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.blue": "Blå blom", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> blom", "block.minecraft.banner.flower.cyan": "Blågr<PERSON><PERSON> blom", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.light_blue": "Ljosblå blom", "block.minecraft.banner.flower.light_gray": "Ljosgrå blom", "block.minecraft.banner.flower.lime": "Ljosgrø<PERSON> blom", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.red": "<PERSON><PERSON> blom", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON> blom", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> blom", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.blue": "Blå klote", "block.minecraft.banner.globe.brown": "<PERSON><PERSON> k<PERSON>e", "block.minecraft.banner.globe.cyan": "Blågrøn klote", "block.minecraft.banner.globe.gray": "Grå klote", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.light_blue": "Ljosblå klote", "block.minecraft.banner.globe.light_gray": "Ljosgrå klote", "block.minecraft.banner.globe.lime": "Ljosgrøn klote", "block.minecraft.banner.globe.magenta": "Ljosblåraud klote", "block.minecraft.banner.globe.orange": "<PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.globe.purple": "Blå<PERSON><PERSON> klote", "block.minecraft.banner.globe.red": "<PERSON><PERSON>", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON> klote", "block.minecraft.banner.gradient.black": "Svart yvergang", "block.minecraft.banner.gradient.blue": "Blå yvergang", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON> y<PERSON>gang", "block.minecraft.banner.gradient.cyan": "Blågrøn yvergang", "block.minecraft.banner.gradient.gray": "Grå yvergang", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>g", "block.minecraft.banner.gradient.light_blue": "Ljosblå yvergang", "block.minecraft.banner.gradient.light_gray": "Ljosgrå yvergang", "block.minecraft.banner.gradient.lime": "Ljosgrøn yvergang", "block.minecraft.banner.gradient.magenta": "Ljosblåraud yvergang", "block.minecraft.banner.gradient.orange": "Brandgul yvergang", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>", "block.minecraft.banner.gradient.purple": "B<PERSON><PERSON><PERSON>ud yvergang", "block.minecraft.banner.gradient.red": "<PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON> y<PERSON>gang", "block.minecraft.banner.gradient.yellow": "Gul yvergang", "block.minecraft.banner.gradient_up.black": "Svart yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.blue": "Blå yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.brown": "<PERSON>run yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.cyan": "Blågrøn yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.gray": "Grå yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.green": "Grøn yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.light_blue": "Ljosblå yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.light_gray": "Ljosgrå yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.lime": "Ljosgrøn yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.magenta": "Ljosblåraud yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.orange": "Brandgul yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.pink": "Ljosraud yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.purple": "Blåraud yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> y<PERSON>gang frå skjeldfot", "block.minecraft.banner.gradient_up.white": "Kvit yvergang frå skjeldfot", "block.minecraft.banner.gradient_up.yellow": "Gul yvergang frå skjeldfot", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.blue": "Blå flagar", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>ar", "block.minecraft.banner.guster.cyan": "Blågr<PERSON><PERSON> flagar", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.light_blue": "Ljosblå flagar", "block.minecraft.banner.guster.light_gray": "Ljosgrå flagar", "block.minecraft.banner.guster.lime": "Ljosgrø<PERSON> flagar", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.red": "<PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> flagar", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON>ar", "block.minecraft.banner.half_horizontal.black": "Svòrt tverklovi", "block.minecraft.banner.half_horizontal.blue": "Blå tverklovi", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Blågrøn tverklovi", "block.minecraft.banner.half_horizontal.gray": "Grå tverklovi", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Ljosblå tverklovi", "block.minecraft.banner.half_horizontal.light_gray": "Ljosgrå tverklovi", "block.minecraft.banner.half_horizontal.lime": "Ljosgrøn tverklovi", "block.minecraft.banner.half_horizontal.magenta": "Ljosblåraud tverklovi", "block.minecraft.banner.half_horizontal.orange": "Brandgul tverklovi", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "block.minecraft.banner.half_horizontal.purple": "Blåraud tverklovi", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "K<PERSON>t tverklovi", "block.minecraft.banner.half_horizontal.yellow": "Gul tverklovi", "block.minecraft.banner.half_horizontal_bottom.black": "Svòrt umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.blue": "Blå umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON> umvend t<PERSON><PERSON>i", "block.minecraft.banner.half_horizontal_bottom.cyan": "Blågrøn umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.gray": "Grå umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON><PERSON> umvend t<PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Ljosblå umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Ljosgrå umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.lime": "Ljosgrøn umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.magenta": "Blåraud umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.orange": "Brandgul umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> umvend t<PERSON>klovi", "block.minecraft.banner.half_horizontal_bottom.purple": "Blåraud umvend tverklovi", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON> um<PERSON> t<PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> umvend t<PERSON>klovi", "block.minecraft.banner.half_horizontal_bottom.yellow": "Gul umvend tverklovi", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON><PERSON> klovi", "block.minecraft.banner.half_vertical.blue": "Blå klovi", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Blågrø<PERSON> k<PERSON>i", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON><PERSON> klovi", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Ljosblå klovi", "block.minecraft.banner.half_vertical.light_gray": "Ljosgrå klovi", "block.minecraft.banner.half_vertical.lime": "Ljosgrø<PERSON> klovi", "block.minecraft.banner.half_vertical.magenta": "Ljosblå<PERSON><PERSON>", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON> klovi", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "Gul klovi", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON><PERSON><PERSON> umvend klovi", "block.minecraft.banner.half_vertical_right.blue": "Blå umvend klovi", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON> um<PERSON>d <PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Blågrø<PERSON> umvend klovi", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON> umvend klovi", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>d k<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Ljosblå umvend klovi", "block.minecraft.banner.half_vertical_right.light_gray": "Ljosgrå umvend klovi", "block.minecraft.banner.half_vertical_right.lime": "Ljosgrø<PERSON> umvend klovi", "block.minecraft.banner.half_vertical_right.magenta": "Ljosblåraud umvend klovi", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON> umvend klovi", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> umvend k<PERSON>i", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> umvend klovi", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.light_blue": "Ljosblå ting", "block.minecraft.banner.mojang.light_gray": "Ljosgrå ting", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.magenta": "Ljosb<PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.red": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> ting", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON>ne", "block.minecraft.banner.piglin.light_blue": "Ljosblått tryne", "block.minecraft.banner.piglin.light_gray": "Ljos<PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON>lt tryne", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.purple": "B<PERSON>å<PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> tryne", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON>ne", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON> tryne", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> rombe", "block.minecraft.banner.rhombus.blue": "Blå rombe", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON> rombe", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON>gr<PERSON><PERSON> r<PERSON>e", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> r<PERSON>e", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "Ljosblå rombe", "block.minecraft.banner.rhombus.light_gray": "Ljosgrå rombe", "block.minecraft.banner.rhombus.lime": "<PERSON>josgr<PERSON><PERSON> r<PERSON>e", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON> rombe", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON> rombe", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON> skalle", "block.minecraft.banner.skull.blue": "Blå skalle", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> skalle", "block.minecraft.banner.skull.cyan": "Blågrøn skalle", "block.minecraft.banner.skull.gray": "Grå skalle", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON> skalle", "block.minecraft.banner.skull.light_blue": "Ljosblå skalle", "block.minecraft.banner.skull.light_gray": "Ljosgrå skalle", "block.minecraft.banner.skull.lime": "Ljosgrøn skalle", "block.minecraft.banner.skull.magenta": "Ljosblåraud skalle", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON> skalle", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> skalle", "block.minecraft.banner.skull.red": "<PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> skalle", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.blue": "Blå stolpar", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> stol<PERSON>", "block.minecraft.banner.small_stripes.cyan": "Blågr<PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.gray": "Gr<PERSON> stolpar", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Ljosblå stolpar", "block.minecraft.banner.small_stripes.light_gray": "Ljosgrå stolpar", "block.minecraft.banner.small_stripes.lime": "Ljosgr<PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.magenta": "Ljosblåraude stolpar", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.purple": "Blåraude stolpar", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.yellow": "Gule stolpar", "block.minecraft.banner.square_bottom_left.black": "Svart høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.blue": "Blå høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.brown": "Brun høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.cyan": "Blågrøn høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.gray": "Grå høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.green": "Grøn høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.light_blue": "Ljosblå høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.light_gray": "Ljosgrå høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.lime": "Ljosgrøn høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.magenta": "Ljosblåraud høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.orange": "Brandgul høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.pink": "Ljosraud høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.purple": "Blåraud høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON> høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.white": "Kvit høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_left.yellow": "Gul høgreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.black": "Svart vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.blue": "Blå vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.brown": "Brun vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.cyan": "Blågrøn vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.gray": "Grå vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.green": "Grøn vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.light_blue": "Ljosblå vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.light_gray": "Ljosgrå vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.lime": "Ljosgrøn vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.magenta": "Ljosblåraud vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.orange": "Brandgul vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.pink": "Ljosraud vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.purple": "Blåraud vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON> vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.white": "Kvit vinstreteig i skjeldfot", "block.minecraft.banner.square_bottom_right.yellow": "Gul vinstreteig i skjeldfot", "block.minecraft.banner.square_top_left.black": "Svart høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.blue": "Blå høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.brown": "Brun høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.cyan": "Blågrøn høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.gray": "Grå høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.green": "Grøn høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.light_blue": "Ljosblå høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.light_gray": "Ljosgrå høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.lime": "Ljosgrøn høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.magenta": "Ljosblåraud høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.orange": "Brandgul høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.pink": "Ljosraud høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.purple": "Blåraud høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON> høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.white": "Kvit høgreteig i skjeldhovud", "block.minecraft.banner.square_top_left.yellow": "Gul høgreteig i skjeldhovud", "block.minecraft.banner.square_top_right.black": "Svart vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.blue": "Blå vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.brown": "Brun vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.cyan": "Blågrøn vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.gray": "Grå vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.green": "Grøn vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.light_blue": "Ljosblå vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.light_gray": "Ljosgrå vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.lime": "Ljosgrøn vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.magenta": "Ljosblåraud vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.orange": "Brandgul vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.pink": "Ljosraud vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.purple": "Blåraud vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON> vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.white": "Kvit vinstreteig i skjeldhovud", "block.minecraft.banner.square_top_right.yellow": "Gul vinstreteig i skjeldhovud", "block.minecraft.banner.straight_cross.black": "<PERSON>vart kross", "block.minecraft.banner.straight_cross.blue": "Blå kross", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Blågrøn kross", "block.minecraft.banner.straight_cross.gray": "Grå kross", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Ljosblå kross", "block.minecraft.banner.straight_cross.light_gray": "Ljosgrå kross", "block.minecraft.banner.straight_cross.lime": "Ljosgrøn kross", "block.minecraft.banner.straight_cross.magenta": "Ljosbl<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "Gul kross", "block.minecraft.banner.stripe_bottom.black": "Svart skjeldfot", "block.minecraft.banner.stripe_bottom.blue": "Blå skjeldfot", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON> skje<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Blågrøn skjeldfot", "block.minecraft.banner.stripe_bottom.gray": "Grå skjeldfot", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON> skje<PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Ljosblå skjeldfot", "block.minecraft.banner.stripe_bottom.light_gray": "Ljosgrå skjeldfot", "block.minecraft.banner.stripe_bottom.lime": "Ljosgrøn skjeldfot", "block.minecraft.banner.stripe_bottom.magenta": "Ljosblåraud skjeldfot", "block.minecraft.banner.stripe_bottom.orange": "Brandgul skjeldfot", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>je<PERSON>", "block.minecraft.banner.stripe_bottom.purple": "Blåraud skjeldfot", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON> skjeld<PERSON>t", "block.minecraft.banner.stripe_bottom.yellow": "Gul skjeldfot", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.blue": "Blå stolpe", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.cyan": "Blågr<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.gray": "Gr<PERSON> stolpe", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.light_blue": "Ljosblå stolpe", "block.minecraft.banner.stripe_center.light_gray": "Ljosgrå stolpe", "block.minecraft.banner.stripe_center.lime": "Ljosgrø<PERSON> stolpe", "block.minecraft.banner.stripe_center.magenta": "Ljosbl<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON><PERSON> sto<PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_downleft.black": "Svart vinstretverbjelke", "block.minecraft.banner.stripe_downleft.blue": "Blå vinstretverbjelke", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON> vinstretverbjelke", "block.minecraft.banner.stripe_downleft.cyan": "Blågrøn vinstretverbjelke", "block.minecraft.banner.stripe_downleft.gray": "Grå vinstretverbjelke", "block.minecraft.banner.stripe_downleft.green": "G<PERSON><PERSON><PERSON> vinstretverbjelke", "block.minecraft.banner.stripe_downleft.light_blue": "Ljosblå vinstretverbjelke", "block.minecraft.banner.stripe_downleft.light_gray": "Ljosgrå vinstretverbjelke", "block.minecraft.banner.stripe_downleft.lime": "Ljosgrøn vinstretverbjelke", "block.minecraft.banner.stripe_downleft.magenta": "Ljosblåraud vinstretverbjelke", "block.minecraft.banner.stripe_downleft.orange": "Brandgul vinstretverbjelke", "block.minecraft.banner.stripe_downleft.pink": "Ljos<PERSON>ud vinstretverbjelke", "block.minecraft.banner.stripe_downleft.purple": "Blåraud vinstretverbjelke", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "Kvit vinstretverbjelke", "block.minecraft.banner.stripe_downleft.yellow": "Gul vinstretverbjelke", "block.minecraft.banner.stripe_downright.black": "Svart tverbjelke", "block.minecraft.banner.stripe_downright.blue": "Blå tverbjelke", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON> tverb<PERSON><PERSON>e", "block.minecraft.banner.stripe_downright.cyan": "Blågrøn tverbjelke", "block.minecraft.banner.stripe_downright.gray": "Grå tverbjelke", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON> tver<PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Ljosblå tverbjelke", "block.minecraft.banner.stripe_downright.light_gray": "Ljosgrå tverbjelke", "block.minecraft.banner.stripe_downright.lime": "Ljosgrøn tverbjelke", "block.minecraft.banner.stripe_downright.magenta": "Ljosblåraud tverbjelke", "block.minecraft.banner.stripe_downright.orange": "Brandgul tverbjelke", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON><PERSON> tver<PERSON>je<PERSON>", "block.minecraft.banner.stripe_downright.purple": "Blåraud tverbjelke", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "Gul tverbjelke", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON> høgrestol<PERSON>", "block.minecraft.banner.stripe_left.blue": "Blå høgrestolpe", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "Blågrøn hø<PERSON>ol<PERSON>", "block.minecraft.banner.stripe_left.gray": "Grå høgrestolpe", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Ljosblå høgrestolpe", "block.minecraft.banner.stripe_left.light_gray": "Ljosgrå høgrestolpe", "block.minecraft.banner.stripe_left.lime": "Ljosgrøn hø<PERSON>ol<PERSON>", "block.minecraft.banner.stripe_left.magenta": "Ljosbl<PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON> hø<PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> høgrestolpe", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "Blå bjelke", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bjelk<PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> bjelke", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Ljosblå bjelke", "block.minecraft.banner.stripe_middle.light_gray": "Ljosgrå bjelke", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bjelk<PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON> vins<PERSON><PERSON><PERSON>pe", "block.minecraft.banner.stripe_right.blue": "Blå vinstrestolpe", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "Blågrø<PERSON> vins<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "Grå vinstrestolpe", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Ljosblå vinstrestolpe", "block.minecraft.banner.stripe_right.light_gray": "Ljosgrå vinstrestolpe", "block.minecraft.banner.stripe_right.lime": "Ljosgrø<PERSON> vins<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "Ljosbl<PERSON><PERSON><PERSON> vins<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON><PERSON> vinstres<PERSON><PERSON>pe", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> vinstres<PERSON>lpe", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON> skje<PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Ljosblått skje<PERSON>", "block.minecraft.banner.stripe_top.light_gray": "Ljosgrått skje<PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "Ljosblåraudt skjeldhovud", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON><PERSON> skje<PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "Blåraudt skje<PERSON>hovud", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.blue": "Blå sparre", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.cyan": "Blågr<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.light_blue": "Ljosblå sparre", "block.minecraft.banner.triangle_bottom.light_gray": "Ljosgrå sparre", "block.minecraft.banner.triangle_bottom.lime": "Ljosgrø<PERSON> sparre", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON>jos<PERSON><PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON> spa<PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.yellow": "G<PERSON> sparre", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.blue": "B<PERSON>å umvend sparre", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.cyan": "<PERSON>lågr<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.light_blue": "Ljosblå umvend sparre", "block.minecraft.banner.triangle_top.light_gray": "Ljosgrå umvend sparre", "block.minecraft.banner.triangle_top.lime": "Ljosgr<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.magenta": "Ljosblå<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> umvend sparre", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_bottom.blue": "B<PERSON>å taggar nedre", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON> tag<PERSON> nedre", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON> <PERSON>gar nedre", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON> nedre", "block.minecraft.banner.triangles_bottom.light_blue": "Ljosblå taggar nedre", "block.minecraft.banner.triangles_bottom.light_gray": "Ljosgrå taggar nedre", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_bottom.magenta": "Ljosblåraude taggar nedre", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_bottom.purple": "Blåraude taggar nedre", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON> tag<PERSON> nedre", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> nedre", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> taggar nedre", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.blue": "Blå taggar uppe", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.cyan": "Blågr<PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.light_blue": "Ljosblå taggar uppe", "block.minecraft.banner.triangles_top.light_gray": "Ljosgrå taggar uppe", "block.minecraft.banner.triangles_top.lime": "Ljosgr<PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.magenta": "Ljosblåraude taggar uppe", "block.minecraft.banner.triangles_top.orange": "Brand<PERSON>le taggar uppe", "block.minecraft.banner.triangles_top.pink": "Ljos<PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.purple": "Blåraude taggar uppe", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> taggar uppe", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> tag<PERSON> uppe", "block.minecraft.banner.triangles_top.yellow": "Gule taggar uppe", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "V<PERSON><PERSON>", "block.minecraft.beacon.primary": "Hovudkraft", "block.minecraft.beacon.secondary": "Attåtkraft", "block.minecraft.bed.no_sleep": "Du kann berre sova um notti elder i toreveder", "block.minecraft.bed.not_safe": "Du kann ’kje sova no; skræ<PERSON><PERSON> ero i nærleiken", "block.minecraft.bed.obstructed": "<PERSON>ne sengi er hindrad", "block.minecraft.bed.occupied": "<PERSON>ne sengi er uppteki", "block.minecraft.bed.too_far_away": "Du kann ’kje sova no; sengi er for langt undan", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "Biebol", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Stort droplauv", "block.minecraft.big_dripleaf_stem": "Stylk av stort droplauv", "block.minecraft.birch_button": "Bjørkarknapp", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Bjørkargrind", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON> bj<PERSON>", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Bjørkarstomn", "block.minecraft.birch_planks": "Bjørkarbord", "block.minecraft.birch_pressure_plate": "Bjørkar-tyngdarfjøl", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Bjørkarskilt", "block.minecraft.birch_slab": "Bjørkarhella", "block.minecraft.birch_stairs": "Bjørkartropp", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> bjørkarskilt på vegg", "block.minecraft.birch_wall_sign": "Bjørkarveggskilt", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.black_candle": "<PERSON>var<PERSON> vaksljos", "block.minecraft.black_candle_cake": "Kaka med svart v<PERSON>ljos", "block.minecraft.black_carpet": "Svòrt rya", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON> st<PERSON>", "block.minecraft.black_concrete_powder": "S<PERSON><PERSON>rt stø<PERSON>sdumba", "block.minecraft.black_glazed_terracotta": "Svòrt glasad terrakotta", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON> shul<PERSON>", "block.minecraft.black_stained_glass": "Svartlìtat glas", "block.minecraft.black_stained_glass_pane": "Svartlì<PERSON> g<PERSON>", "block.minecraft.black_terracotta": "Svòrt terrakotta", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_stairs": "Svartsteinstropp", "block.minecraft.blackstone_wall": "Svartsteinsvegg", "block.minecraft.blast_furnace": "Malmomn", "block.minecraft.blue_banner": "Blå fana", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.blue_candle": "Blått vaksljos", "block.minecraft.blue_candle_cake": "<PERSON>ka med blått v<PERSON>", "block.minecraft.blue_carpet": "Blå rya", "block.minecraft.blue_concrete": "Blå støypestein", "block.minecraft.blue_concrete_powder": "Blå støypesteinsdumba", "block.minecraft.blue_glazed_terracotta": "Blå glasa terrakotta", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "Blå orkidé", "block.minecraft.blue_shulker_box": "B<PERSON>ått shulkerhus", "block.minecraft.blue_stained_glass": "Blålìtat glas", "block.minecraft.blue_stained_glass_pane": "Blålìtad glasruta", "block.minecraft.blue_terracotta": "Blå terrakotta", "block.minecraft.blue_wool": "Blå ull", "block.minecraft.bone_block": "Beinblokk", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Heilekorallblokk", "block.minecraft.brain_coral_fan": "Heilekorallveifta", "block.minecraft.brain_coral_wall_fan": "Heilekorallveggveifta", "block.minecraft.brewing_stand": "Bryggjestod", "block.minecraft.brick_slab": "Tiglhella", "block.minecraft.brick_stairs": "Tigltropp", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Tigl", "block.minecraft.brown_banner": "<PERSON><PERSON>a", "block.minecraft.brown_bed": "<PERSON><PERSON> seng", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Kaka med brunt v<PERSON><PERSON><PERSON>", "block.minecraft.brown_carpet": "<PERSON><PERSON> rya", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON> glas<PERSON> terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON> sopp", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Brunlìtat glas", "block.minecraft.brown_stained_glass_pane": "Brunlìtad g<PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON> terra<PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>l", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON>korall", "block.minecraft.bubble_coral_block": "Bublekorallblokk", "block.minecraft.bubble_coral_fan": "Bublekorallveifta", "block.minecraft.bubble_coral_wall_fan": "Bublekorallveggveifta", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON> ametyst", "block.minecraft.bush": "<PERSON>n", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Ka<PERSON>usblom", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Kalkspat", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> sculk<PERSON><PERSON><PERSON>", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "Kaka med v<PERSON>ljos", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Kartteiknarbord", "block.minecraft.carved_pumpkin": "Utskoret graskjer", "block.minecraft.cauldron": "G<PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.chain": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Kjedje-styrebòdsblokk", "block.minecraft.cherry_button": "<PERSON>eb<PERSON>k<PERSON><PERSON>", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "Kissebergrind", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "Kisseberstomn", "block.minecraft.cherry_planks": "Kisseberbord", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sapling": "Kisseb<PERSON><PERSON>ning", "block.minecraft.cherry_sign": "Kisseberskilt", "block.minecraft.cherry_slab": "Kisseb<PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "Kissebertropp", "block.minecraft.cherry_trapdoor": "Kisseberlem", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> kisseb<PERSON>kilt på vegg", "block.minecraft.cherry_wall_sign": "Kisseberveggskilt", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "Sprokket sted", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "Meitlat nethertigl", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_quartz_block": "Meitlad kattsteinsblokk", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON> r<PERSON>stein", "block.minecraft.chiseled_resin_bricks": "Meitlat kvådetigl", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "Meitlat steintigl", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON> tuff", "block.minecraft.chiseled_tuff_bricks": "Meitlat tufftigl", "block.minecraft.chorus_flower": "Korblom", "block.minecraft.chorus_plant": "Korvokster", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Attlaten augblom", "block.minecraft.coal_block": "Kolblokk", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Grov mold", "block.minecraft.cobbled_deepslate": "Djupskifer-brustein", "block.minecraft.cobbled_deepslate_slab": "Hella av djups<PERSON>fer-brustein", "block.minecraft.cobbled_deepslate_stairs": "Tropp av djupskifer-brustein", "block.minecraft.cobbled_deepslate_wall": "Vegg av djups<PERSON>fer-brustein", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_wall": "Brusteinsvegg", "block.minecraft.cobweb": "Kongurvev", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Styrebòdsblokk", "block.minecraft.comparator": "Redstone-samanliknar", "block.minecraft.composter": "Bòstadbing", "block.minecraft.conduit": "Flødar", "block.minecraft.copper_block": "Koparblokk", "block.minecraft.copper_bulb": "Koparpera", "block.minecraft.copper_door": "<PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Koparmalm", "block.minecraft.copper_trapdoor": "Koparlem", "block.minecraft.cornflower": "Kornblom", "block.minecraft.cracked_deepslate_bricks": "Sprokket djupskifertigl", "block.minecraft.cracked_deepslate_tiles": "Sprokkne djupskiferflisar", "block.minecraft.cracked_nether_bricks": "Sprokket nethertigl", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> fins<PERSON> s<PERSON>", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON> stein<PERSON>gl", "block.minecraft.crafter": "<PERSON><PERSON>", "block.minecraft.crafting_table": "Emnebord", "block.minecraft.creaking_heart": "Knerkadal", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_button": "Blodknapp", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "B<PERSON>dgard", "block.minecraft.crimson_fence_gate": "Blodgrind", "block.minecraft.crimson_fungus": "Blodsopp", "block.minecraft.crimson_hanging_sign": "Hangande blodskilt", "block.minecraft.crimson_hyphae": "Blodhyfor", "block.minecraft.crimson_nylium": "Blodsoppvev", "block.minecraft.crimson_planks": "Blodbord", "block.minecraft.crimson_pressure_plate": "Blod-tyngdarfjøl", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Blodskilt", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Blodtropp", "block.minecraft.crimson_stem": "Blodstomn", "block.minecraft.crimson_trapdoor": "Blodlem", "block.minecraft.crimson_wall_hanging_sign": "Hangande blodskilt på vegg", "block.minecraft.crimson_wall_sign": "Blodveggskilt", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON> kopar", "block.minecraft.cut_copper_slab": "Hella av skoren kopar", "block.minecraft.cut_copper_stairs": "Tropp av skoren kopar", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON> raud sandstein", "block.minecraft.cut_red_sandstone_slab": "Hella av skoren raud sandstein", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON> sandstein", "block.minecraft.cut_sandstone_slab": "Hella av skoren sandstein", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON>gr<PERSON><PERSON> fana", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON>ka med bl<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.cyan_carpet": "Blågrøn rya", "block.minecraft.cyan_concrete": "Blågrø<PERSON> stø<PERSON>", "block.minecraft.cyan_concrete_powder": "Blågrøn støypesteinsdumba", "block.minecraft.cyan_glazed_terracotta": "Blågrøn glasad terrakotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "Blågrønlìtat glas", "block.minecraft.cyan_stained_glass_pane": "Blågrønlìtad glasruta", "block.minecraft.cyan_terracotta": "Blågrøn terrakotta", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.damaged_anvil": "Skadt sted", "block.minecraft.dandelion": "Gullboste", "block.minecraft.dark_oak_button": "Myrkeikeknapp", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_fence": "Myrkeikegard", "block.minecraft.dark_oak_fence_gate": "Myrkeikegrind", "block.minecraft.dark_oak_hanging_sign": "Hangande myrkeikeskilt på vegg", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "Myrkeikestomn", "block.minecraft.dark_oak_planks": "Myrkeikebord", "block.minecraft.dark_oak_pressure_plate": "Myrkeike-tyngdarfjøl", "block.minecraft.dark_oak_sapling": "Myrkeiker<PERSON>ning", "block.minecraft.dark_oak_sign": "Myrkeikeskilt", "block.minecraft.dark_oak_slab": "Myrkeikehelle", "block.minecraft.dark_oak_stairs": "Myrkeiketropp", "block.minecraft.dark_oak_trapdoor": "Myrkeikelem", "block.minecraft.dark_oak_wall_hanging_sign": "Hangande myrkeikeskilt på vegg", "block.minecraft.dark_oak_wall_sign": "Myrkeikeveggskilt", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>e", "block.minecraft.dark_prismarine_stairs": "Myrkprismarin-tropp", "block.minecraft.daylight_detector": "Dagsljosmålar", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON> he<PERSON>", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>gg<PERSON>", "block.minecraft.dead_bush": "<PERSON><PERSON> runn", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON>", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON>ta", "block.minecraft.deepslate": "D<PERSON>ps<PERSON>fer", "block.minecraft.deepslate_brick_slab": "Hella av djupskifertigl", "block.minecraft.deepslate_brick_stairs": "Tropp av djupskifertigl", "block.minecraft.deepslate_brick_wall": "Vegg av djupskifertigl", "block.minecraft.deepslate_bricks": "Djupskifertigl", "block.minecraft.deepslate_coal_ore": "Kolmalm i djupskifer", "block.minecraft.deepslate_copper_ore": "Koparmalm i djupskifer", "block.minecraft.deepslate_diamond_ore": "Demantår i djupskifer", "block.minecraft.deepslate_emerald_ore": "Smaragdår i djupskifer", "block.minecraft.deepslate_gold_ore": "Gullmalm i djupskifer", "block.minecraft.deepslate_iron_ore": "Jarnmalm i djupskifer", "block.minecraft.deepslate_lapis_ore": "Lasursteinår i djupskifer", "block.minecraft.deepslate_redstone_ore": "Redstonemalm i djupskifer", "block.minecraft.deepslate_tile_slab": "Hella av djupskiferflisar", "block.minecraft.deepslate_tile_stairs": "Trapp av djupskiferflisar", "block.minecraft.deepslate_tile_wall": "Vegg av djupskiferflisar", "block.minecraft.deepslate_tiles": "Djups<PERSON>fer<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Merkarskjena", "block.minecraft.diamond_block": "Demantblokk", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioritthella", "block.minecraft.diorite_stairs": "Diorittropp", "block.minecraft.diorite_wall": "Diorittvegg", "block.minecraft.dirt": "Mold", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Utskjotar", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "Innturkat ghast", "block.minecraft.dried_kelp_block": "Blokk av turkad tare", "block.minecraft.dripstone_block": "Dropsteinsblokk", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragdblokk", "block.minecraft.emerald_ore": "Smaragdår", "block.minecraft.enchanting_table": "Galdrebord", "block.minecraft.end_gateway": "Endegat", "block.minecraft.end_portal": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_portal_frame": "Endelìdsrå<PERSON>", "block.minecraft.end_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Hella av endesteinstigl", "block.minecraft.end_stone_brick_stairs": "Tropp av endesteinstigl", "block.minecraft.end_stone_brick_wall": "Vegg av endesteinstigl", "block.minecraft.end_stone_bricks": "Endesteinstigl", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON> meitlad kopar", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON> kopar", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON> kop<PERSON>a", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.exposed_copper_trapdoor": "Utsett koparlem", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON> skoren kopar", "block.minecraft.exposed_cut_copper_slab": "Utsett hella av skoren kopar", "block.minecraft.exposed_cut_copper_stairs": "Utsett tropp av skoren kopar", "block.minecraft.farmland": "<PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON>d", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Eldkorallblokk", "block.minecraft.fire_coral_fan": "Eldkorallveifta", "block.minecraft.fire_coral_wall_fan": "Eldkorallveggveifta", "block.minecraft.firefly_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fletching_table": "Pilmakarbord", "block.minecraft.flower_pot": "Blompotta", "block.minecraft.flowering_azalea": "Blømande lyngrosa", "block.minecraft.flowering_azalea_leaves": "Blømande lyngroselauv", "block.minecraft.frogspawn": "Fr<PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Rimlagd is", "block.minecraft.furnace": "Omn", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Gullblokk", "block.minecraft.gold_ore": "Gullmalm", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Granitthella", "block.minecraft.granite_stairs": "Granittropp", "block.minecraft.granite_wall": "Granittvegg", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblokk", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>a", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>ljos", "block.minecraft.gray_candle_cake": "Kaka med gr<PERSON> v<PERSON>", "block.minecraft.gray_carpet": "Grå rya", "block.minecraft.gray_concrete": "Gr<PERSON> støypestein", "block.minecraft.gray_concrete_powder": "Grå støypesteinsdumba", "block.minecraft.gray_glazed_terracotta": "Grå glasad terrakotta", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "Grålìtat glas", "block.minecraft.gray_stained_glass_pane": "Gr<PERSON><PERSON>ì<PERSON> g<PERSON>", "block.minecraft.gray_terracotta": "Grå terrakotta", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>l", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "Kaka med grønt v<PERSON>", "block.minecraft.green_carpet": "Grøn rya", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ø<PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> stø<PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glasad terracotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "Grønlìtat glas", "block.minecraft.green_stained_glass_pane": "Grøn<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakot<PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> rø<PERSON>", "block.minecraft.hay_block": "Høystakk", "block.minecraft.heavy_core": "<PERSON>ng kjerne", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.honey_block": "Huningblokk", "block.minecraft.honeycomb_block": "Vakskakeblokk", "block.minecraft.hopper": "<PERSON><PERSON>", "block.minecraft.horn_coral": "Hornkorall", "block.minecraft.horn_coral_block": "Hornkorallblokk", "block.minecraft.horn_coral_fan": "Hornkorallveifta", "block.minecraft.horn_coral_wall_fan": "Hornkorallveggveifta", "block.minecraft.ice": "Is", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON> meitlat stein<PERSON>gl", "block.minecraft.infested_cobblestone": "<PERSON><PERSON> brustein", "block.minecraft.infested_cracked_stone_bricks": "<PERSON>d sprokken steintigl", "block.minecraft.infested_deepslate": "<PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON> mose<PERSON> stein<PERSON>l", "block.minecraft.infested_stone": "Fengd stein", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON> s<PERSON>l", "block.minecraft.iron_bars": "Jarngrind", "block.minecraft.iron_block": "Jarnblokk", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "Jarnmalm", "block.minecraft.iron_trapdoor": "Jarnlem", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jigsaw": "Pusleblokk", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Regnskogsknapp", "block.minecraft.jungle_door": "Regnskogsdỳr", "block.minecraft.jungle_fence": "Regnskogsgard", "block.minecraft.jungle_fence_gate": "Regnskogsgrind", "block.minecraft.jungle_hanging_sign": "Hangande regnskogsskilt", "block.minecraft.jungle_leaves": "Regnskogslauv", "block.minecraft.jungle_log": "Regnskogsstomn", "block.minecraft.jungle_planks": "Regnskogsbord", "block.minecraft.jungle_pressure_plate": "Regnskogs-tyngdarfjøl", "block.minecraft.jungle_sapling": "Regnskogsrenning", "block.minecraft.jungle_sign": "Regnskogsskilt", "block.minecraft.jungle_slab": "Regnskogshella", "block.minecraft.jungle_stairs": "Regnskogstropp", "block.minecraft.jungle_trapdoor": "Regnskogslem", "block.minecraft.jungle_wall_hanging_sign": "Hangande regnskogsskilt på vegg", "block.minecraft.jungle_wall_sign": "Regnskogsveggskilt", "block.minecraft.jungle_wood": "Regnskogsvìd", "block.minecraft.kelp": "<PERSON><PERSON>", "block.minecraft.kelp_plant": "Tarevokster", "block.minecraft.ladder": "Stige", "block.minecraft.lantern": "Lykt", "block.minecraft.lapis_block": "Lasursteinsblokk", "block.minecraft.lapis_ore": "Lasursteinsår", "block.minecraft.large_amethyst_bud": "Stor ametystknupp", "block.minecraft.large_fern": "<PERSON><PERSON> burkne", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.leaf_litter": "Lauvstrøy", "block.minecraft.lectern": "Bokstol", "block.minecraft.lever": "Handtak", "block.minecraft.light": "Ljos", "block.minecraft.light_blue_banner": "Ljosblå fana", "block.minecraft.light_blue_bed": "Ljosblå seng", "block.minecraft.light_blue_candle": "Ljosblått vaksljos", "block.minecraft.light_blue_candle_cake": "Kaka med ljosblått vaksljos", "block.minecraft.light_blue_carpet": "Ljosblå rya", "block.minecraft.light_blue_concrete": "Ljosblå støypestein", "block.minecraft.light_blue_concrete_powder": "Ljosblå støypesteinsdumba", "block.minecraft.light_blue_glazed_terracotta": "Ljosblå glasad terrakotta", "block.minecraft.light_blue_shulker_box": "Ljosblått shulkerhus", "block.minecraft.light_blue_stained_glass": "Ljosblålìtat glas", "block.minecraft.light_blue_stained_glass_pane": "Ljosblålìtad glasruta", "block.minecraft.light_blue_terracotta": "Ljosblå terrakotta", "block.minecraft.light_blue_wool": "Ljosblå ull", "block.minecraft.light_gray_banner": "Ljosgrå fana", "block.minecraft.light_gray_bed": "Ljosgrå seng", "block.minecraft.light_gray_candle": "Ljosgrått vaksljos", "block.minecraft.light_gray_candle_cake": "Kaka med ljosgrått vaksljos", "block.minecraft.light_gray_carpet": "Ljosgrå rya", "block.minecraft.light_gray_concrete": "Ljosgrå støypestein", "block.minecraft.light_gray_concrete_powder": "Ljosgrå støypesteinsdumba", "block.minecraft.light_gray_glazed_terracotta": "Ljosgrå glasad terrakotta", "block.minecraft.light_gray_shulker_box": "Ljosgrått shulkerhus", "block.minecraft.light_gray_stained_glass": "Ljosgrålìtat glas", "block.minecraft.light_gray_stained_glass_pane": "Ljosgrålìtad g<PERSON>ruta", "block.minecraft.light_gray_terracotta": "Ljosgrå terrakotta", "block.minecraft.light_gray_wool": "Ljosgrå ull", "block.minecraft.light_weighted_pressure_plate": "Lett-t<PERSON>g<PERSON><PERSON>j<PERSON>l", "block.minecraft.lightning_rod": "Eldingavleidar", "block.minecraft.lilac": "Syrin", "block.minecraft.lily_of_the_valley": "Rams", "block.minecraft.lily_pad": "Nykkjeblomblad", "block.minecraft.lime_banner": "<PERSON>josg<PERSON><PERSON><PERSON> fana", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.lime_candle": "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "Kaka med ljosg<PERSON><PERSON>nt v<PERSON>", "block.minecraft.lime_carpet": "Ljosgrøn rya", "block.minecraft.lime_concrete": "Ljosgrø<PERSON> stø<PERSON>", "block.minecraft.lime_concrete_powder": "Ljosgrøn støypesteinsdumba", "block.minecraft.lime_glazed_terracotta": "Ljosgrøn glasad terrakotta", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "Ljosgrønlìtat glas", "block.minecraft.lime_stained_glass_pane": "Ljosgrønlìtad g<PERSON>ruta", "block.minecraft.lime_terracotta": "Ljosgrøn terrakotta", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Vevstol", "block.minecraft.magenta_banner": "Ljosb<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.magenta_candle": "Ljosblåraudt vaksljos", "block.minecraft.magenta_candle_cake": "Kaka med ljosblåraudt vaksljos", "block.minecraft.magenta_carpet": "Ljosblåraud rya", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Ljosblå<PERSON><PERSON> s<PERSON>ø<PERSON>du<PERSON>", "block.minecraft.magenta_glazed_terracotta": "Ljosblåraud g<PERSON>ad te<PERSON>", "block.minecraft.magenta_shulker_box": "Ljosblåraudt shulkerhus", "block.minecraft.magenta_stained_glass": "Ljosblåraudlìtat glas", "block.minecraft.magenta_stained_glass_pane": "Ljosblåraudlìtad glasruta", "block.minecraft.magenta_terracotta": "Ljosblåraud terrakot<PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.magma_block": "Raunblokk", "block.minecraft.mangrove_button": "Mangroveknapp", "block.minecraft.mangrove_door": "Mangrovedỳr", "block.minecraft.mangrove_fence": "Mangrovegard", "block.minecraft.mangrove_fence_gate": "Mangrovegrind", "block.minecraft.mangrove_hanging_sign": "Hangande mangroveskilt", "block.minecraft.mangrove_leaves": "Mangrovelauv", "block.minecraft.mangrove_log": "Mangrovestomn", "block.minecraft.mangrove_planks": "Mangrovebord", "block.minecraft.mangrove_pressure_plate": "Mangrove-tyngdarfjøl", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "Man<PERSON>r<PERSON><PERSON>", "block.minecraft.mangrove_sign": "Mangroveskilt", "block.minecraft.mangrove_slab": "Mangrovehella", "block.minecraft.mangrove_stairs": "Mangrovetropp", "block.minecraft.mangrove_trapdoor": "Mangrovelem", "block.minecraft.mangrove_wall_hanging_sign": "Hangande mangroveskilt på vegg", "block.minecraft.mangrove_wall_sign": "Mangroveveggskilt", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Millomstor ametystknupp", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "Melonstylk", "block.minecraft.moss_block": "Moseblokk", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON>dd brustein", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON> brustein<PERSON><PERSON>", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON> brustein<PERSON>pp", "block.minecraft.mossy_cobblestone_wall": "Mosegrodd brusteinsvegg", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON>dd steintigltropp", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON>tt stein<PERSON>gl", "block.minecraft.moving_piston": "Stempel i rørsla", "block.minecraft.mud": "G<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Gyrmetiglhella", "block.minecraft.mud_brick_stairs": "Gyrmetigltropp", "block.minecraft.mud_brick_wall": "Gyr<PERSON>iglvegg", "block.minecraft.mud_bricks": "G<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "Gyrmute mangroverøter", "block.minecraft.mushroom_stem": "Soppstylk", "block.minecraft.mycelium": "Soppvev", "block.minecraft.nether_brick_fence": "Nethertiglgard", "block.minecraft.nether_brick_slab": "Nethertiglhella", "block.minecraft.nether_brick_stairs": "Nethertigltropp", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON>glvegg", "block.minecraft.nether_bricks": "Nethertigl", "block.minecraft.nether_gold_ore": "Nethergullmalm", "block.minecraft.nether_portal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "Netherkattsteins-år", "block.minecraft.nether_sprouts": "Netherspiror", "block.minecraft.nether_wart": "Nethervòrta", "block.minecraft.nether_wart_block": "Nethervòrteblokk", "block.minecraft.netherite_block": "Netherittblokk", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Noteblokk", "block.minecraft.oak_button": "<PERSON><PERSON>knap<PERSON>", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "Eikegrind", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Eikestomn", "block.minecraft.oak_planks": "Eikebord", "block.minecraft.oak_pressure_plate": "Eike-t<PERSON>g<PERSON>fj<PERSON>l", "block.minecraft.oak_sapling": "Eikerenning", "block.minecraft.oak_sign": "Eikeskilt", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Eikelem", "block.minecraft.oak_wall_hanging_sign": "Hangande eikeskilt på vegg", "block.minecraft.oak_wall_sign": "Eikeveggskilt", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.open_eyeblossom": "Open augblom", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON> fana", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.orange_candle": "Brandgult vaksljos", "block.minecraft.orange_candle_cake": "Kaka med brandgult vaksljos", "block.minecraft.orange_carpet": "Brandgul rya", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>t<PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON> støypesteinsdumba", "block.minecraft.orange_glazed_terracotta": "Brandgul glasad terrakotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON> shulkerhus", "block.minecraft.orange_stained_glass": "Brandgullìtat glas", "block.minecraft.orange_stained_glass_pane": "Brandgullìtad g<PERSON>", "block.minecraft.orange_terracotta": "Brandgul terrakotta", "block.minecraft.orange_tulip": "Brandgul tulipan", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.oxeye_daisy": "Balderbrå", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON> meit<PERSON> kopar", "block.minecraft.oxidized_copper": "Eirad koparblokk", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Eirad koparlem", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON> skoren kopar", "block.minecraft.oxidized_cut_copper_slab": "Eirad hella av skoren kopar", "block.minecraft.oxidized_cut_copper_stairs": "Eirad tropp av skoren kopar", "block.minecraft.packed_ice": "<PERSON><PERSON>", "block.minecraft.packed_mud": "Pakkgyrma", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.pale_moss_block": "Bleikmoseblokk", "block.minecraft.pale_moss_carpet": "Bleikmoserya", "block.minecraft.pale_oak_button": "Bleikeikeknapp", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence": "Bleikeikegard", "block.minecraft.pale_oak_fence_gate": "Bleikeikegrind", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON> bleikeike<PERSON>lt", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON>lauv", "block.minecraft.pale_oak_log": "Bleikeikestomn", "block.minecraft.pale_oak_planks": "Bleikeikebord", "block.minecraft.pale_oak_pressure_plate": "Bleikeike-tyngdarfjøl", "block.minecraft.pale_oak_sapling": "Bleikeikerenning", "block.minecraft.pale_oak_sign": "Bleikeikeskilt", "block.minecraft.pale_oak_slab": "Bleikeikehella", "block.minecraft.pale_oak_stairs": "Bleikeiketropp", "block.minecraft.pale_oak_trapdoor": "Bleikeikelem", "block.minecraft.pale_oak_wall_hanging_sign": "Hang<PERSON>e bleikeikeskilt på vegg", "block.minecraft.pale_oak_wall_sign": "Bleikeikeveggskilt", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.peony": "Bonderosa", "block.minecraft.petrified_oak_slab": "Steingjengi e<PERSON>a", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "Piglinvegg<PERSON>ud", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.pink_candle": "Ljosraudt vaksljos", "block.minecraft.pink_candle_cake": "Kaka med ljos<PERSON>udt v<PERSON>ljos", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON><PERSON> rya", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> te<PERSON>", "block.minecraft.pink_petals": "Ljosraude kronblad", "block.minecraft.pink_shulker_box": "Ljos<PERSON><PERSON><PERSON> shul<PERSON>", "block.minecraft.pink_stained_glass": "Ljosraudlìtat glas", "block.minecraft.pink_stained_glass_pane": "Ljosraudlìtad g<PERSON>ru<PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON><PERSON> tulipan", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.piston": "Stempel", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Hovudet åt %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "Fins<PERSON><PERSON> and<PERSON>tt", "block.minecraft.polished_andesite_slab": "Hella av finslipen andesitt", "block.minecraft.polished_andesite_stairs": "Tropp av finslipen andesitt", "block.minecraft.polished_basalt": "Finslipen basalt", "block.minecraft.polished_blackstone": "Finslip<PERSON> s<PERSON>tein", "block.minecraft.polished_blackstone_brick_slab": "Hella av finslipet svartsteinstigl", "block.minecraft.polished_blackstone_brick_stairs": "Tropp av finslipet svartsteinstigl", "block.minecraft.polished_blackstone_brick_wall": "Vegg av finslipet svartsteinstigl", "block.minecraft.polished_blackstone_bricks": "Finslipet svartsteinstigl", "block.minecraft.polished_blackstone_button": "Knapp av finslipen svartstein", "block.minecraft.polished_blackstone_pressure_plate": "Tyngdarfjøl av finslipen svartstein", "block.minecraft.polished_blackstone_slab": "Hella av fins<PERSON><PERSON> svar<PERSON>tein", "block.minecraft.polished_blackstone_stairs": "Tropp av finslipen svartstein", "block.minecraft.polished_blackstone_wall": "Vegg av fins<PERSON>en svartstein", "block.minecraft.polished_deepslate": "Finslip<PERSON>", "block.minecraft.polished_deepslate_slab": "Hella av finslipen d<PERSON>", "block.minecraft.polished_deepslate_stairs": "Tropp av finslipen djupskifer", "block.minecraft.polished_deepslate_wall": "Vegg av finslipen d<PERSON>fer", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "block.minecraft.polished_diorite_slab": "Hella av finslipen dioritt", "block.minecraft.polished_diorite_stairs": "Tropp av finslipen dioritt", "block.minecraft.polished_granite": "<PERSON>s<PERSON><PERSON> granitt", "block.minecraft.polished_granite_slab": "Hella av finslipen granitt", "block.minecraft.polished_granite_stairs": "Tropp av finslipen granitt", "block.minecraft.polished_tuff": "Finslipen tuff", "block.minecraft.polished_tuff_slab": "Hella av finslipen tuff", "block.minecraft.polished_tuff_stairs": "Tropp av finslipen tuff", "block.minecraft.polished_tuff_wall": "Vegg av finslipen tuff", "block.minecraft.poppy": "Valmoe", "block.minecraft.potatoes": "Jorde<PERSON>", "block.minecraft.potted_acacia_sapling": "Akasierenning i potta", "block.minecraft.potted_allium": "Villauk i potta", "block.minecraft.potted_azalea_bush": "Lyngrosa i potta", "block.minecraft.potted_azure_bluet": "Houstonia i potta", "block.minecraft.potted_bamboo": "Bambus i potta", "block.minecraft.potted_birch_sapling": "Bjørkarrenning i potta", "block.minecraft.potted_blue_orchid": "Blå orkidé i potta", "block.minecraft.potted_brown_mushroom": "<PERSON>run sopp i potta", "block.minecraft.potted_cactus": "<PERSON>kt<PERSON> i potta", "block.minecraft.potted_cherry_sapling": "Kisseberrenning i potta", "block.minecraft.potted_closed_eyeblossom": "Attlaten augblom i potta", "block.minecraft.potted_cornflower": "Kornblom i potta", "block.minecraft.potted_crimson_fungus": "Blodsopp i potta", "block.minecraft.potted_crimson_roots": "Blodrøter i potta", "block.minecraft.potted_dandelion": "Gullboste i potta", "block.minecraft.potted_dark_oak_sapling": "Myrkeikerenning i potta", "block.minecraft.potted_dead_bush": "<PERSON><PERSON> runn i potta", "block.minecraft.potted_fern": "Burkne i potta", "block.minecraft.potted_flowering_azalea_bush": "Blømande lyngrosa i potta", "block.minecraft.potted_jungle_sapling": "Regnskogsrenning i potta", "block.minecraft.potted_lily_of_the_valley": "Rams i potta", "block.minecraft.potted_mangrove_propagule": "Mangrovefrjo i potta", "block.minecraft.potted_oak_sapling": "Eikerenning i potta", "block.minecraft.potted_open_eyeblossom": "Open augblom i potta", "block.minecraft.potted_orange_tulip": "Brandgul tulipan i potta", "block.minecraft.potted_oxeye_daisy": "Balderbrå i potta", "block.minecraft.potted_pale_oak_sapling": "Bleikeik i potta", "block.minecraft.potted_pink_tulip": "Ljosraud tulipan i potta", "block.minecraft.potted_poppy": "Valmoe i potta", "block.minecraft.potted_red_mushroom": "Flugesopp i potta", "block.minecraft.potted_red_tulip": "<PERSON><PERSON> tulipan i potta", "block.minecraft.potted_spruce_sapling": "Granrenning i potta", "block.minecraft.potted_torchflower": "Kyndelblom i potta", "block.minecraft.potted_warped_fungus": "Vrìdsopp i potta", "block.minecraft.potted_warped_roots": "Vrìdrøter i potta", "block.minecraft.potted_white_tulip": "Kvit tulipan i potta", "block.minecraft.potted_wither_rose": "Witherrosa i potta", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "block.minecraft.powered_rail": "Straumskjena", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarintiglhella", "block.minecraft.prismarine_brick_stairs": "Prismarintigltropp", "block.minecraft.prismarine_bricks": "Prismarintigl", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "Prismarintropp", "block.minecraft.prismarine_wall": "Prismarinvegg", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Graskjerstylk", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.purple_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.purple_candle": "Blåraudt vaksljos", "block.minecraft.purple_candle_cake": "Kaka med blå<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON><PERSON>ud rya", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> glasad terra<PERSON>", "block.minecraft.purple_shulker_box": "Blåraudt shulkerhus", "block.minecraft.purple_stained_glass": "Blåraudlìtat glas", "block.minecraft.purple_stained_glass_pane": "Blåraudlìtad glasruta", "block.minecraft.purple_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.purpur_block": "Purpurblokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "P<PERSON>purhella", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_block": "Kattsteinsblokk", "block.minecraft.quartz_bricks": "Kattsteinst<PERSON>l", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_stairs": "Kattsteinstropp", "block.minecraft.rail": "Skjena", "block.minecraft.raw_copper_block": "Råkoparblokk", "block.minecraft.raw_gold_block": "Rågullsblokk", "block.minecraft.raw_iron_block": "Råjarnsblokk", "block.minecraft.red_banner": "<PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON> seng", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle_cake": "Kaka med raudt v<PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON> rya", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "Flugesoppblokk", "block.minecraft.red_nether_brick_slab": "Hella av raudt nethertigl", "block.minecraft.red_nether_brick_stairs": "Tropp av raudt nethertigl", "block.minecraft.red_nether_brick_wall": "Vegg av raudt nethertigl", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "Raud sand", "block.minecraft.red_sandstone": "<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Hella av raud sandstein", "block.minecraft.red_sandstone_stairs": "Tropp av raud sandstein", "block.minecraft.red_sandstone_wall": "Vegg av raud sandstein", "block.minecraft.red_shulker_box": "<PERSON><PERSON>", "block.minecraft.red_stained_glass": "Raudlìtat glas", "block.minecraft.red_stained_glass_pane": "Raud<PERSON>ìtad g<PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON> tulipan", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Redstoneblokk", "block.minecraft.redstone_lamp": "Redstonelampe", "block.minecraft.redstone_ore": "Redstonemalm", "block.minecraft.redstone_torch": "Redstonekyndel", "block.minecraft.redstone_wall_torch": "Veggredstonekyndel", "block.minecraft.redstone_wire": "Redstoneleidning", "block.minecraft.reinforced_deepslate": "Styrkt djupskifer", "block.minecraft.repeater": "<PERSON><PERSON>au<PERSON>", "block.minecraft.repeating_command_block": "Uppattakande styrebòdsblokk", "block.minecraft.resin_block": "Kvådeblokk", "block.minecraft.resin_brick_slab": "Kvådetiglhella", "block.minecraft.resin_brick_stairs": "Kvådetigltropp", "block.minecraft.resin_brick_wall": "Kvådetiglvegg", "block.minecraft.resin_bricks": "Kvådetigl", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Uppstòdeakkjer", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_wall": "Sandsteinsvegg", "block.minecraft.scaffolding": "Stelling", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculkeskundar", "block.minecraft.sculk_sensor": "Sculkegaumar", "block.minecraft.sculk_shrieker": "Sculkeskrikar", "block.minecraft.sculk_vein": "Sculkeår", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "<PERSON><PERSON>", "block.minecraft.short_dry_grass": "Stutt turt gras", "block.minecraft.short_grass": "Stutt gras", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON>er<PERSON>", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_wall_skull": "Veggskalle", "block.minecraft.slime_block": "Slimblokk", "block.minecraft.small_amethyst_bud": "<PERSON>ten ametystknupp", "block.minecraft.small_dripleaf": "Litet droplauv", "block.minecraft.smithing_table": "Smidjebord", "block.minecraft.smoker": "Røykjaromn", "block.minecraft.smooth_basalt": "Jamn basalt", "block.minecraft.smooth_quartz": "Jòmn kattsteinsblokk", "block.minecraft.smooth_quartz_slab": "<PERSON>a av jamn kattstein", "block.minecraft.smooth_quartz_stairs": "Tropp av jamn kattstein", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> raud <PERSON>stein", "block.minecraft.smooth_red_sandstone_slab": "<PERSON>a av jamn raud sandstein", "block.minecraft.smooth_red_sandstone_stairs": "Tropp av jamn raud sandstein", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON>a av jamn sandstein", "block.minecraft.smooth_sandstone_stairs": "Tropp av jamn sandstein", "block.minecraft.smooth_stone": "<PERSON><PERSON> stein", "block.minecraft.smooth_stone_slab": "Hella av jamn stein", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "Snjoblokk", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "Sålarmold", "block.minecraft.soul_torch": "Sålarkyndel", "block.minecraft.soul_wall_torch": "Veggsålarkyndel", "block.minecraft.spawn.not_valid": "Du hev’ ingi seng elder a<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, elder so vorto dei stengde", "block.minecraft.spawner": "Skræmsleskapar", "block.minecraft.spawner.desc1": "<PERSON><PERSON><PERSON>:", "block.minecraft.spawner.desc2": "Set kvìkendeslag", "block.minecraft.sponge": "Svamp", "block.minecraft.spore_blossom": "Moseblom", "block.minecraft.spruce_button": "Granknapp", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence": "Gran<PERSON>", "block.minecraft.spruce_fence_gate": "Grangrind", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.spruce_leaves": "Granbar", "block.minecraft.spruce_log": "Granstomn", "block.minecraft.spruce_planks": "Granbord", "block.minecraft.spruce_pressure_plate": "Gran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "Granskilt", "block.minecraft.spruce_slab": "Granhella", "block.minecraft.spruce_stairs": "Grantrop<PERSON>", "block.minecraft.spruce_trapdoor": "Granlem", "block.minecraft.spruce_wall_hanging_sign": "Hangande granskilt på vegg", "block.minecraft.spruce_wall_sign": "Granveggskilt", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Klisterstempel", "block.minecraft.stone": "<PERSON>", "block.minecraft.stone_brick_slab": "Steintiglhell<PERSON>", "block.minecraft.stone_brick_stairs": "Steintigltropp", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_slab": "Steinhell<PERSON>", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Steinskjere", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON>ad bambusblokk", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> bjørkarstomn", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON> blo<PERSON>for", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> blods<PERSON>n", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_log": "Slindad regnskogsstomn", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "Slindad mangrovestomn", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON> man<PERSON>d", "block.minecraft.stripped_oak_log": "Slindad eikestomn", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON> bleikeikestomn", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_log": "Slindad gran<PERSON>n", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.stripped_warped_hyphae": "Slindade vrìdhyfor", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON> v<PERSON>ìds<PERSON>n", "block.minecraft.structure_block": "Bygnadsblokk", "block.minecraft.structure_void": "Bygnadstomrom", "block.minecraft.sugar_cane": "Sukkerrøyr", "block.minecraft.sunflower": "Solvendel", "block.minecraft.suspicious_gravel": "Mistenkje<PERSON> aur", "block.minecraft.suspicious_sand": "Mistenkjeleg sand", "block.minecraft.sweet_berry_bush": "Søtberjalyng", "block.minecraft.tall_dry_grass": "<PERSON><PERSON>gt turt gras", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON> gras", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.target": "Skotskiva", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Ryneblokk", "block.minecraft.test_instance_block": "Rynehøves-blokk", "block.minecraft.tinted_glass": "Svertat glas", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-sprengnad er avsleget", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>mgrø<PERSON>", "block.minecraft.trapped_chest": "Fellekista", "block.minecraft.trial_spawner": "Røyneskapar", "block.minecraft.tripwire": "Snåvetråd", "block.minecraft.tripwire_hook": "Snåvetrådfeste", "block.minecraft.tube_coral": "Orgekorall", "block.minecraft.tube_coral_block": "Orgekorallblokk", "block.minecraft.tube_coral_fan": "Orgekorallveifta", "block.minecraft.tube_coral_wall_fan": "Orgekorallveggveifta", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tufftiglhella", "block.minecraft.tuff_brick_stairs": "Tufftigltropp", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "Tufftigl", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "Tuffvegg", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "Krokande klivevokster", "block.minecraft.twisting_vines_plant": "Krokande klivevokster", "block.minecraft.vault": "Kvelv", "block.minecraft.verdant_froglight": "<PERSON>r<PERSON>ns<PERSON><PERSON> f<PERSON>", "block.minecraft.vine": "Klivevokster", "block.minecraft.void_air": "Tomromsvind", "block.minecraft.wall_torch": "Veggkyndel", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON>ds<PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "Hang<PERSON><PERSON> vrìds<PERSON>lt", "block.minecraft.warped_hyphae": "Vrìdhyfor", "block.minecraft.warped_nylium": "Vrìdsoppvev", "block.minecraft.warped_planks": "Vrì<PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Vrì<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "Vrìdskilt", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "Vrìdtropp", "block.minecraft.warped_stem": "Vrìdstomn", "block.minecraft.warped_trapdoor": "Vrìdlem", "block.minecraft.warped_wall_hanging_sign": "Hangande vrìdskilt på vegg", "block.minecraft.warped_wall_sign": "Vrìdveggskilt", "block.minecraft.warped_wart_block": "Vrìdvòrteblokk", "block.minecraft.water": "Vatn", "block.minecraft.water_cauldron": "Vatsgryta", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON> meitlad kopar", "block.minecraft.waxed_copper_block": "Vaksad koparblokk", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_trapdoor": "Vaksad koparlem", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON> skoren kopar", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON><PERSON> hella av skoren kopar", "block.minecraft.waxed_cut_copper_stairs": "Vaksad tropp av skoren kopar", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON> utsett meitlad kopar", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON> uts<PERSON> kopar", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON> uts<PERSON> kopar<PERSON>a", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON> u<PERSON> kop<PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "Vaksad utsett koparlem", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON> utsett skoren kopar", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON>aks<PERSON> utsett hella av skoren kopar", "block.minecraft.waxed_exposed_cut_copper_stairs": "Vaksad utsett tropp av skoren kopar", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON> eirad meitlad kopar", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON> e<PERSON> k<PERSON>ar", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON> e<PERSON> k<PERSON>a", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON> e<PERSON>d koparlem", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON> eirad skoren kopar", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON> eirad hella av skoren kopar", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Vaksad eirad tropp av skoren kopar", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON> vederbiten meitlad kopar", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON> vederbiten kopar", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON> vederbiti kopar<PERSON>a", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON> ve<PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON> ve<PERSON>biti kop<PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "Vaksad vederbiten koparlem", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON>ad vederbiten skoren kopar", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> vederbiti hella av skoren kopar", "block.minecraft.waxed_weathered_cut_copper_stairs": "Vaksad vederbiti tropp av skoren kopar", "block.minecraft.weathered_chiseled_copper": "Vederbiten meitlad kopar", "block.minecraft.weathered_copper": "Vederbiten kopar", "block.minecraft.weathered_copper_bulb": "Vederbiti koparpera", "block.minecraft.weathered_copper_door": "Vederbiti kopardỳr", "block.minecraft.weathered_copper_grate": "Vederbiti koparrist", "block.minecraft.weathered_copper_trapdoor": "Vêrbiten koparlem", "block.minecraft.weathered_cut_copper": "Vederbiten skoren kopar", "block.minecraft.weathered_cut_copper_slab": "Vederbiti hella av skoren kopar", "block.minecraft.weathered_cut_copper_stairs": "Vederbiti tropp av skoren kopar", "block.minecraft.weeping_vines": "Gråtande klivevokster", "block.minecraft.weeping_vines_plant": "Gråtande klivevokster", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON> svamp", "block.minecraft.wheat": "Kveitegrøda", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON>a", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.white_candle_cake": "Kaka med kvitt v<PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON> rya", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON> terra<PERSON>", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass": "Kvitlìtat glas", "block.minecraft.white_stained_glass_pane": "Kvitlìtad glasru<PERSON>", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> tulipan", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Witherveggskalle", "block.minecraft.yellow_banner": "<PERSON><PERSON> <PERSON>a", "block.minecraft.yellow_bed": "<PERSON><PERSON> seng", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "Kaka med gult v<PERSON>l<PERSON>", "block.minecraft.yellow_carpet": "Gul rya", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "Gul støypesteinsdumba", "block.minecraft.yellow_glazed_terracotta": "Gul glasad terrakotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "Gullìtat glas", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.yellow_terracotta": "Gul terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON> ull", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "av %1$s", "book.edit.title": "Bokbrigde", "book.editTitle": "Skriv inn namnet på boki:", "book.finalizeButton": "Set innsigle", "book.finalizeWarning": "Åthuga! Set du innsigle på boki kann du ’kje lenger reida ’nne.", "book.generation.0": "Upphavleg", "book.generation.1": "Avrit av upphavleg", "book.generation.2": "Avrit av avrit", "book.generation.3": "Fillut", "book.invalid.tag": "* <PERSON><PERSON><PERSON> b<PERSON> *", "book.pageIndicator": "Sida %1$s av %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON> sida", "book.page_button.previous": "<PERSON><PERSON><PERSON> sida", "book.sign.title": "Underskriving av bok", "book.sign.titlebox": "<PERSON><PERSON>", "book.signButton": "Skriv under", "book.view.title": "<PERSON><PERSON><PERSON><PERSON>", "build.tooHigh": "Hæddargrensa fyre byggjing er %s", "chat.cannotSend": "<PERSON>nn <PERSON>kje senda bòd", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Trykk fyr’ å fjerrflytja", "chat.copy": "Rita av", "chat.copy.click": "Kyv fyr’ å rita av til utklyppstavla", "chat.deleted_marker": "<PERSON>ta bòdet er vordet slettat av tenaren.", "chat.disabled.chain_broken": "Svall avsleget på grunn av ei broti kjedja. Freista å kopla til å nyo.", "chat.disabled.expiredProfileKey": "Svall avsleget på grunn av ein utgjengen ålmenninglykel. Freista å kopla til å nyo.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON><PERSON><PERSON><PERSON> hadde uventade elder van<PERSON>de styrebòdsargument-segl.", "chat.disabled.invalid_signature": "Svall hadde ugildt innsigle. Freista å kopla til å nyo.", "chat.disabled.launcher": "Svall er avsleget av opnarval. Kann <PERSON>kje senda bòd.", "chat.disabled.missingProfileKey": "Svall er avsleget på grunn av vantande ålmenneleg lykel til profil. Freista å få samband å nyo.", "chat.disabled.options": "Svall er avsleget i klientval.", "chat.disabled.out_of_order_chat": "Svall motteket i gali rekkjefylgja. Heve systemtidi di skift?", "chat.disabled.profile": "Svall er ’kje løyvt av kontovali. Kyv «%s» att fyre fleire upplysingar.", "chat.disabled.profile.moreInfo": "Sval<PERSON> er sperrat av kontovali. Ka<PERSON> <PERSON>kje senda elder sj<PERSON> bòd.", "chat.editBox": "svall", "chat.filtered": "Silat av tenaren.", "chat.filtered_full": "Tenaren heve gø<PERSON>t bòdet di fyre sume leikarar.", "chat.link.confirm": "Er du trygg på at du vil opna fylgjande vevgard?", "chat.link.confirmTrusted": "Vil du å opna elder taka avrit av denne lekken?", "chat.link.open": "Opna i netlesar", "chat.link.warning": "<PERSON>na aldri netlekker frå folk du ikkje lit på!", "chat.queue": "[+%s ventande ròd/rader]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON>ar sende ugildt bòd.", "chat.tag.modified": "Bòd brigdt av tenaren: Upphavleg:", "chat.tag.not_secure": "<PERSON>kk<PERSON>-sannk<PERSON><PERSON> bòd. Kann <PERSON>kje segja ifrå um det.", "chat.tag.system": "Tenarbòd. Kann ’kje segja ifrå um det.", "chat.tag.system_single_player": "Ten<PERSON><PERSON><PERSON><PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s heve fullgjort utbjodingi %s", "chat.type.advancement.goal": "%s heve nått må<PERSON> %s", "chat.type.advancement.task": "%s heve gjort bragdi %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Send bòd til laget", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s segjer %s", "chat.validation_error": "<PERSON><PERSON> <PERSON>kje <PERSON>era svall", "chat_screen.message": "Bòd til å senda: %s", "chat_screen.title": "Svallskjerm", "chat_screen.usage": "Skriv bòd og kyv Enter fyr’ å senda", "chunk.toast.checkLog": "Sjå loggen fyr’ utgreiding", "chunk.toast.loadFailure": "<PERSON><PERSON> ’kje lada heimsbit ved %s", "chunk.toast.lowDiskSpace": "Litet diskrom!", "chunk.toast.lowDiskSpace.description": "<PERSON><PERSON> kann henda ikkje i stand til å spara heimen.", "chunk.toast.saveFailure": "Ku<PERSON> ’kje spara heimsbit ved %s", "clear.failed.multiple": "<PERSON>gi ting vordo fundne hjå %s leikarar", "clear.failed.single": "Ingi ting vordo fundne hjå leikaren %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blå", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "Blågrøn", "color.minecraft.gray": "Grå", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Ljosblå", "color.minecraft.light_gray": "Ljosgrå", "color.minecraft.lime": "Ljosgrøn", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "Brandgul", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Gul", "command.context.here": "<--[HER]", "command.context.parse_error": "%s på stad %s: %s", "command.exception": "<PERSON>nde ’kje tyda styrebòd: %s", "command.expected.separator": "Millomromsteikn var ventat fyr’ å enda argumentet, men fleire data vordo fundne", "command.failed": "Eit uventat lyte ovra seg medan du freista å setja styrebòdet i verk", "command.forkLimit": "Høgste tal på samanhangen (%s) nått", "command.unknown.argument": "<PERSON>rett argument fyre styre<PERSON><PERSON>d", "command.unknown.command": "<PERSON><PERSON><PERSON><PERSON> elder <PERSON><PERSON><PERSON><PERSON> s<PERSON>; sj<PERSON> under fyre lyte", "commands.advancement.criterionNotFound": "Bragdi %1$s heve ’kje kravet «%2$s»", "commands.advancement.grant.criterion.to.many.failure": "Kunde ’kje gjeva kravet «%s» fyre bragdi %s til %s leikarar, av di dei alt hava det", "commands.advancement.grant.criterion.to.many.success": "<PERSON><PERSON> kravet «%s» fyre bragdi %s til %s leikarar", "commands.advancement.grant.criterion.to.one.failure": "<PERSON><PERSON> ’kje gjeva kravet «%s» fyre bragdi %s til %s, av di han/ho alt heve det", "commands.advancement.grant.criterion.to.one.success": "<PERSON><PERSON> kravet «%s» fyre bragdi %s til %s", "commands.advancement.grant.many.to.many.failure": "Ku<PERSON> ’kje gjeva %s bragder til %s leikarar, av dei alt hava deim", "commands.advancement.grant.many.to.many.success": "Gav %s bragder til %s leikarar", "commands.advancement.grant.many.to.one.failure": "<PERSON><PERSON> ’kje gjeva %s bragder til %s, av han/ho alt heve deim", "commands.advancement.grant.many.to.one.success": "Gav %s bragder til %s", "commands.advancement.grant.one.to.many.failure": "<PERSON><PERSON> ’kje gjeva bragdi %s til %s leikarar, av di dei alt hava ’nne", "commands.advancement.grant.one.to.many.success": "Gav bragdi %s til %s leikarar", "commands.advancement.grant.one.to.one.failure": "<PERSON><PERSON> ’kje gjeva bragdi %s til %s, som alt hava ’nne", "commands.advancement.grant.one.to.one.success": "Gav bragdi %s til %s", "commands.advancement.revoke.criterion.to.many.failure": "Ku<PERSON> ’kje taka burt kravet «%s» fyre bragdi %s frå %s leikarar, av di dei ikkje hava det", "commands.advancement.revoke.criterion.to.many.success": "Tok burt kravet «%s» fyre bragdi %s frå %s leikarar", "commands.advancement.revoke.criterion.to.one.failure": "<PERSON><PERSON> ’kje taka burt kravet «%s» fyre bragdi %s frå %s, av di han/ho ikkje heve det", "commands.advancement.revoke.criterion.to.one.success": "Tok burt kravet «%s» fyre bragdi %s frå %s", "commands.advancement.revoke.many.to.many.failure": "<PERSON><PERSON> <PERSON>kje taka burt %s bragder frå %s leika<PERSON>, av di dei ikkje hava deim", "commands.advancement.revoke.many.to.many.success": "Tok burt %s bragder frå %s leikarar", "commands.advancement.revoke.many.to.one.failure": "<PERSON><PERSON> <PERSON>kje taka burt %s bragder frå %s, av di han/ho ikkje heve deim", "commands.advancement.revoke.many.to.one.success": "Tok burt %s bragder frå %s", "commands.advancement.revoke.one.to.many.failure": "<PERSON><PERSON> ’kje taka burt bragdi %s frå %s leika<PERSON>, av di dei ikkje hava ’nne", "commands.advancement.revoke.one.to.many.success": "Tok burt bragdi %s frå %s leikarar", "commands.advancement.revoke.one.to.one.failure": "<PERSON><PERSON> ’kje taka burt bragdi %s frå %s, av di han/ho ikkje heve ’nne", "commands.advancement.revoke.one.to.one.success": "Tok burt bragdi %s frå %s", "commands.attribute.base_value.get.success": "Grunnverdet åt gjerdi %s fyre einingi %s er %s", "commands.attribute.base_value.reset.success": "Grunnverdet åt gjerdi %s fyre einingi %s er attrat til fyrevalet %s", "commands.attribute.base_value.set.success": "Grunnverdet åt gjerdi %s fyre einingi %s er %s", "commands.attribute.failed.entity": "%s er ’kje ei gild eining fyre detta styrebòdet", "commands.attribute.failed.modifier_already_present": "Tilmåtaren %s er alt til stades i gjerdi %s til einingi %s", "commands.attribute.failed.no_attribute": "Einingi %s heve ’kje gjerdi %s", "commands.attribute.failed.no_modifier": "Gjerdi %s til einingi %s heve ’kje tilmåtingi %s", "commands.attribute.modifier.add.success": "Lagde til tilmåtingi %s i gjerdi %s til einingi %s", "commands.attribute.modifier.remove.success": "Tok burt tilmåtingi %s i gjerdi %s til einingi %s", "commands.attribute.modifier.value.get.success": "Verdet åt tilmåtingi %s i gjerdi %s til einingi %s er %s", "commands.attribute.value.get.success": "Verdet åt gjerdi %s til einingi %s er %s", "commands.ban.failed": "<PERSON><PERSON><PERSON> brig<PERSON>; leikaren er alt bannlyst", "commands.ban.success": "Bannlyste %s: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON> brig<PERSON>; <PERSON>en er alt bannlyst", "commands.banip.info": "<PERSON>ne bannlysingi påverkar %s leikar(ar): %s", "commands.banip.invalid": "Ugild <PERSON>-tilskrift elder uk<PERSON><PERSON> leikar", "commands.banip.success": "Bannlyste IP-tilskrifti %s: %s", "commands.banlist.entry": "%s vardt bannlyst av %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON><PERSON>)", "commands.banlist.list": "D’er(o) %s bannlysing(ar):", "commands.banlist.none": "D’ero ingi bann<PERSON>ar", "commands.bossbar.create.failed": "Ein fiendsmålar med IDen «%s» finst alt", "commands.bossbar.create.success": "Lagade fiendsmålaren %s", "commands.bossbar.get.max": "Den eigenlagade fiendsmålaren %s hev’ ein høgste på %s", "commands.bossbar.get.players.none": "Den eigenlagad fiendsmålaren %s hev’ ingi leikarar ånet<PERSON> fyre tidi", "commands.bossbar.get.players.some": "Den eigenlagade fiendsmålaren %s heve %s leikar(ar) ånetes fyre tidi: %s", "commands.bossbar.get.value": "Den eigenlagade fiendsmålaren %s hev’ eit verde av %s", "commands.bossbar.get.visible.hidden": "Den eigenlagad fiendsmålaren %s er fyre tidi løynd", "commands.bossbar.get.visible.visible": "Den eigenlagad fiendsmålaren %s er fyre tidi synt", "commands.bossbar.list.bars.none": "D’ero ingi eigenlagade fiendsmålarar åslegne", "commands.bossbar.list.bars.some": "D’er(o) %s eigenlagad(e) fiendsmålar(ar) åslegen/-ne: %s", "commands.bossbar.remove.success": "Tok burt den eigenlagade fiendsmålaren %s", "commands.bossbar.set.color.success": "Den eigenlagade fiendsmålaren %s heve skift lìt", "commands.bossbar.set.color.unchanged": "Inkje brigdt. Detta er alt lìten på denne fi<PERSON>må<PERSON>en", "commands.bossbar.set.max.success": "Den eigenlagade fiendsmålaren %s heve skift høgst til %s", "commands.bossbar.set.max.unchanged": "Inkje brigdt. Detta er alt det høgste fyre denne fi<PERSON>en", "commands.bossbar.set.name.success": "Den eigenlagade fiendsmålaren %s heve fenge nytt namn", "commands.bossbar.set.name.unchanged": "Inkje brigdt. Detta er alt namnet på denne fiendsmålaren", "commands.bossbar.set.players.success.none": "Den eigenlagade fiendsmålaren %s heve ’kje leikarar lenger", "commands.bossbar.set.players.success.some": "Den eigenlagade fiendsmålaren %s heve no %s leikar(ar): %s", "commands.bossbar.set.players.unchanged": "Inkje brigdt. Desse leikararne ero alt på fiendsmålaren med ingen å leggja til elder taka burt", "commands.bossbar.set.style.success": "Den eigenlagade fiendsmålaren %s heve skift stil", "commands.bossbar.set.style.unchanged": "Inkje brigdt. Detta er alt stilen på denne fi<PERSON>målaren", "commands.bossbar.set.value.success": "Den eigenlagade fiendsmålaren %s heve skift verde til %s", "commands.bossbar.set.value.unchanged": "Inkje brigdt. Detta er alt verdet på denne fiendsmålaren", "commands.bossbar.set.visibility.unchanged.hidden": "Inkje brigdt. Fiendsmålaren er alt løynd", "commands.bossbar.set.visibility.unchanged.visible": "Inkje brigdt. Fiendsmålaren er alt synt", "commands.bossbar.set.visible.success.hidden": "Den eigenlagade fiendsmålaren %s er no løynd", "commands.bossbar.set.visible.success.visible": "Den eigenlagade fiendsmålaren %s er no synt", "commands.bossbar.unknown": "Ingen fiendsmålar finst med IDen «%s»", "commands.clear.success.multiple": "Tok burt %s ting frå %s leikarar", "commands.clear.success.single": "Tok burt %s ting frå leikaren %s", "commands.clear.test.multiple": "Fann %s sams<PERSON><PERSON><PERSON> ting hjå %s leikarar", "commands.clear.test.single": "Fann %s samsvarande ting hjå leika<PERSON> %s", "commands.clone.failed": "<PERSON><PERSON> blekker vordo klonade", "commands.clone.overlap": "Kjelde- og målumkvervi kann ’kje skara kvarandre", "commands.clone.success": "Klonade %s blokk/blekker", "commands.clone.toobig": "For mange blekker i det uppgjevne umkvervet (%s er høgst. %s er uppgjevet)", "commands.damage.invulnerable": "Skade av detta slaget heve ingen verknad på målet", "commands.damage.success": "Gav %s skade til %s", "commands.data.block.get": "%s ved %s, %s, %s etter skalafaktoren %s er %s", "commands.data.block.invalid": "Målblokki er ikkje ei blokkeining", "commands.data.block.modified": "Tilmåtade blokkdatai til %s, %s, %s", "commands.data.block.query": "%s, %s, %s heve desse blokkdatai: %s", "commands.data.entity.get": "%s hjå %s etter skalafaktoren %s er %s", "commands.data.entity.invalid": "<PERSON><PERSON> <PERSON>kje <PERSON> le<PERSON>", "commands.data.entity.modified": "Tilmåtade einingdatai til %s", "commands.data.entity.query": "%s heve desse einingdatai: %s", "commands.data.get.invalid": "<PERSON>nn <PERSON>kje heimta %s; berre tal-merkjelappar ero løyvde", "commands.data.get.multiple": "Detta argumentet godtek eit einskilt NBT-verde", "commands.data.get.unknown": "Kann <PERSON>kje heimta %s; merkjelappen finst ikkje", "commands.data.merge.failed": "Inkje brigdt. Dei uppgjevne gjerderna hava alt desse verdi", "commands.data.modify.expected_list": "Ventade lista; fann: %s", "commands.data.modify.expected_object": "Ventade ting; fann: %s", "commands.data.modify.expected_value": "Verde var ventat; fekk: %s", "commands.data.modify.invalid_index": "<PERSON><PERSON><PERSON> listeindeks: %s", "commands.data.modify.invalid_substring": "Ugilde understreng-indeksar: %s til %s", "commands.data.storage.get": "%s i sparingi %s etter skalafaktoren av %s er %s", "commands.data.storage.modified": "Tilmåtade sparingi %s", "commands.data.storage.query": "Sparingi %s heve detta innehaldet: %s", "commands.datapack.create.already_exists": "Pakke med namnet «%s» finst alt", "commands.datapack.create.invalid_full_name": "«%s» er eit ugildt nytt pakkenamn", "commands.datapack.create.invalid_name": "Ugilde teikn i det nye pakkenamnet «%s»", "commands.datapack.create.io_failure": "<PERSON>nn <PERSON>kje laga pakke med namnet «%s»; sj<PERSON> loggane", "commands.datapack.create.metadata_encode_failure": "Kunde ’kje koda metadata fyre pakke med namnet «%s»: %s", "commands.datapack.create.success": "Laga ny tom pakke med namnet «%s»", "commands.datapack.disable.failed": "Pakken «%s» er ’kje åslegen!", "commands.datapack.disable.failed.feature": "<PERSON>nn <PERSON>kje slå av pakken «%s», av di han høyrer med eit åsleget flagg!", "commands.datapack.enable.failed": "Pakken «%s» er alt åslegen!", "commands.datapack.enable.failed.no_flags": "Pakken «%s» kann ikkje verda åslegi, av di dei kravde flaggi ikkje ero åslegne i denne heimen: %s!", "commands.datapack.list.available.none": "D’ero ingi fleire datapakkar tilg<PERSON>de", "commands.datapack.list.available.success": "D’er(o) %s datapakke/ar tilgjengd(e): %s", "commands.datapack.list.enabled.none": "D’ero ingi datapakkar å<PERSON>gne", "commands.datapack.list.enabled.success": "D’er(o) %s datapakke/ar åslegen/-ne: %s", "commands.datapack.modify.disable": "Slær av datapakken %s", "commands.datapack.modify.enable": "Slær på datapakken %s", "commands.datapack.unknown": "«%s» er ein ukjend datapakke", "commands.debug.alreadyRunning": "Tikkprofileraren er alt sett i gang", "commands.debug.function.noRecursion": "<PERSON>nn <PERSON>kje spòra innan verkendet", "commands.debug.function.noReturnRun": "<PERSON>nn <PERSON>kje nytta spòring med /return run", "commands.debug.function.success.multiple": "Spòrade %s styrebòd frå %s verkende til utgjøv-skilnet %s", "commands.debug.function.success.single": "Spòrade %s styrebòd frå verkendet «%s» til utgjøv-skilnet %s", "commands.debug.function.traceFailed": "<PERSON><PERSON> <PERSON>kje spòra verkende", "commands.debug.notRunning": "Tikkprofileraren er ’kje sett i gang", "commands.debug.started": "Sette i gang tikkprofilering", "commands.debug.stopped": "Stodgade tikkprofilering etter %s sekund(ar) og %s tikk (%s tikk i sekunden)", "commands.defaultgamemode.success": "Fyrevald spelstòda er no %s", "commands.deop.failed": "Inkje brigdt. <PERSON><PERSON><PERSON> er <PERSON>kje ein røktar", "commands.deop.success": "Tok burt %s som tenarrøktar", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Vandleiken vardt ’kje brigd; han er alt sett til %s", "commands.difficulty.query": "Vandleiken er %s", "commands.difficulty.success": "Vandleiken er sett til %s", "commands.drop.no_held_items": "<PERSON><PERSON><PERSON> kann ’kje halda ting", "commands.drop.no_loot_table": "Einingi %s hev’ ingi fengdartavla", "commands.drop.no_loot_table.block": "Blokki %s hev’ ingi fengdartavla", "commands.drop.success.multiple": "Sleppte %s ting", "commands.drop.success.multiple_with_table": "Sleppte %s ting frå fengdartavla %s", "commands.drop.success.single": "Sleppte %s %s", "commands.drop.success.single_with_table": "Sleppte %s %s frå fengdartavla %s", "commands.effect.clear.everything.failed": "Målet hev’ ingen verknader å taka burt", "commands.effect.clear.everything.success.multiple": "Tok burt alle verknaderne frå %s mål", "commands.effect.clear.everything.success.single": "Tok burt alle verknaderne frå %s", "commands.effect.clear.specific.failed": "Målet heve ’kje dei kravde verknaderne", "commands.effect.clear.specific.success.multiple": "Tok burt verknaden %s frå %s mål", "commands.effect.clear.specific.success.single": "Tok burt verknaden %s frå %s", "commands.effect.give.failed": "<PERSON><PERSON> <PERSON>kje nytta denne verknaden (målet er anten unæmt fyre verknader, elder heve no<PERSON> ster<PERSON>e)", "commands.effect.give.success.multiple": "Gav verknaden %s til %s mål", "commands.effect.give.success.single": "Gav verknaden %s til %s", "commands.enchant.failed": "Inkje brig<PERSON>. <PERSON>ten hava måli ingi ting i henderna, elder trolldomen kunde ’kje verta gjeven", "commands.enchant.failed.entity": "%s er ’kje ei gild eining fyre detta styrebòdet", "commands.enchant.failed.incompatible": "%s kann ’kje stydja den trolldomen", "commands.enchant.failed.itemless": "%s held ingen ting", "commands.enchant.failed.level": "%s er høgre enn det høgste stìget av %s studd av trolldomen", "commands.enchant.success.multiple": "Gav trolldomen %s til %s ting", "commands.enchant.success.single": "Gav trolldomen %s til tingen åt %s", "commands.execute.blocks.toobig": "For mange blekker i det uppgjevne umkvervet (%s er høgst. %s er uppgjevet)", "commands.execute.conditional.fail": "Utrøyning var mislukkad", "commands.execute.conditional.fail_count": "Utrøyning var mislukkad; tal: %s", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON><PERSON><PERSON> stod", "commands.execute.conditional.pass_count": "Utrø<PERSON>ing stod; tal: %s", "commands.execute.function.instantiationFailure": "Kunde ’kje instansiera verkendet %s: %s", "commands.experience.add.levels.success.multiple": "Gav %s røynslestìg til %s leikarar", "commands.experience.add.levels.success.single": "Gav %s røynslestìg til %s", "commands.experience.add.points.success.multiple": "Gav %s røynslepoeng til %s leikarar", "commands.experience.add.points.success.single": "Gav %s røynslepoeng til %s", "commands.experience.query.levels": "%s have %s røynslestìg", "commands.experience.query.points": "%s heve %s r<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.experience.set.levels.success.multiple": "Sette %s røynslestìg hjå %s leikarar", "commands.experience.set.levels.success.single": "Sette %s røynslestìg hjå %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> <PERSON>kje <PERSON>ja rø<PERSON> yver høgste mengdi poeng fyre det gjeldande stìget hjå leikaren", "commands.experience.set.points.success.multiple": "Sette %s røynsle<PERSON>eng hjå %s leikarar", "commands.experience.set.points.success.single": "Sette %s røynslepoeng hjå %s", "commands.fill.failed": "<PERSON><PERSON> blekker vordo fyllte", "commands.fill.success": "Fyllte %s blokk/blekker", "commands.fill.toobig": "For mange blekker i det uppgjevne umkvervet (%s er høgst. %s er uppgjevet)", "commands.fillbiome.success": "Lende sette millom %s, %s, %s og %s, %s, %s", "commands.fillbiome.success.count": "Sette %s lende-uppføring(ar) millom %s, %s, %s og %s, %s, %s", "commands.fillbiome.toobig": "For mange blekker i det uppgjevne romet (%s er høgst. %s er uppgjevet)", "commands.forceload.added.failure": "<PERSON><PERSON> heims<PERSON>ar vardt merkte fyre tvingad innlading", "commands.forceload.added.multiple": "Merkte %s heimsbitar i %s frå %s til %s til tvingad innlading", "commands.forceload.added.none": "Ingi tvingat innladde heimsbitar vordo fundne i %s", "commands.forceload.added.single": "Merkte heimsbit %s i %s til tvingad innlading", "commands.forceload.list.multiple": "%s tvingat innladde heimsbitar vordo fundne i %s ved: %s", "commands.forceload.list.single": "Ein tvingat innladd heimsbit vardt funden i %s ved: %s", "commands.forceload.query.failure": "Heimsbiten ved %s i %s er ’kje merkt fyre tvingad innlading", "commands.forceload.query.success": "Heimsbiten ved %s i %s er merkt fyre tvingad innlading", "commands.forceload.removed.all": "Avmerkte alle tvingat innladde heimsbitar i %s", "commands.forceload.removed.failure": "<PERSON><PERSON> heimsbitar vordo tekne burt frå tvingad innlading", "commands.forceload.removed.multiple": "Avmerkte %s heimsbitar fyre tvingad innlading i %s frå %s til %s", "commands.forceload.removed.single": "Avmerkte heimsiten %s i %s fyre tvingad innlading", "commands.forceload.toobig": "For mange heimsbitar i det uppgjevne umkvervet (%s er høgst. %s er uppgjevet)", "commands.function.error.argument_not_compound": "Ugildt argumentslag: %s; samansetjing var ventat", "commands.function.error.missing_argument": "Vantar argument %2$s til verkendet %1$s", "commands.function.error.missing_arguments": "Vantar argument til verkendet %s", "commands.function.error.parse": "Medan du instansierade makroen %s: Styrebòdet «%s» volde lyte: %s", "commands.function.instantiationFailure": "Kunde ’kje instansiera verkendet %s: %s", "commands.function.result": "Verkendet %s gav %s attende", "commands.function.scheduled.multiple": "Køyrande verkende %s", "commands.function.scheduled.no_functions": "Kann ’kje finna nokon verkende fyre namnet %s", "commands.function.scheduled.single": "Køyrer verkende %s", "commands.function.success.multiple": "Utførde %s styrebòd frå %s verkende", "commands.function.success.multiple.result": "Utførde %s verkende", "commands.function.success.single": "Utførde %s styrebòd frå verkendet «%s»", "commands.function.success.single.result": "Verkendet «%2$s» gav %1$s attende", "commands.gamemode.success.other": "Sette spelstòda hjå %s til %s", "commands.gamemode.success.self": "Sette eigi spelstòda til %s", "commands.gamerule.query": "Speltaksteren %s er fyre tidi sett til: %s", "commands.gamerule.set": "Speltaksteren %s er no sett til: %s", "commands.give.failed.toomanyitems": "<PERSON>nn ’kje gjeva meir en %s av %s", "commands.give.success.multiple": "Gav %s %s til %s leikarar", "commands.give.success.single": "Gav %s %s til %s", "commands.help.failed": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> elder van<PERSON><PERSON> l<PERSON>", "commands.item.block.set.success": "Bytte ut eit òp ved %s, %s, %s med %s", "commands.item.entity.set.success.multiple": "Bytte ut eit òp hjå %s einingar med %s", "commands.item.entity.set.success.single": "Bytte ut eit òp hjå %s med %s", "commands.item.source.no_such_slot": "K<PERSON><PERSON> heve ’kje <PERSON>pet %s", "commands.item.source.not_a_container": "Kjeldestaden %s, %s, %s er ’kje eit ilåt", "commands.item.target.no_changed.known_item": "Ingi mål godtoko tingen %s til òpet %s", "commands.item.target.no_changes": "<PERSON>gi mål godtoko tingen i òpet %s", "commands.item.target.no_such_slot": "Målet heve ’kje <PERSON> %s", "commands.item.target.not_a_container": "Målstaden %s, %s, %s er ikkje eit ilåt", "commands.jfr.dump.failed": "<PERSON>nde ’kje dumpa JFR-upptak: %s", "commands.jfr.start.failed": "<PERSON><PERSON> ’kje setja i gang JFR-profilering", "commands.jfr.started": "Sette i gang JFR-profilering", "commands.jfr.stopped": "JFR-profilering stodgade og dumpade i %s", "commands.kick.owner.failed": "Kann ’kje kasta ut tenareigaren i LAN-spel", "commands.kick.singleplayer.failed": "<PERSON>nn <PERSON>kje kasta ut leikarar i eit avnetes serleikarspel", "commands.kick.success": "Kastade ut %s: %s", "commands.kill.success.multiple": "Drap %s einingar", "commands.kill.success.single": "Drap %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "D’er %s av høgst %s leikarar ånetes: %s", "commands.locate.biome.not_found": "Ku<PERSON> ’kje finna lende av slaget «%s» innanfyr’ ein rimeleg fråstand", "commands.locate.biome.success": "Næste %s er ved %s (%s blekker undan)", "commands.locate.poi.not_found": "Kunde ’kje finna nokon forvitnestad av slaget «%s» innanfyre rimeleg fråstand", "commands.locate.poi.success": "Næste %s er ved %s (%s blekker undan)", "commands.locate.structure.invalid": "D’er ingen bygnad av slaget «%s»", "commands.locate.structure.not_found": "Kunde ’kje finna bygnad av slaget «%s» i nærleiken", "commands.locate.structure.success": "Næste %s er ved %s (%s blekker undan)", "commands.message.display.incoming": "%s kviskrar åt deg: %s", "commands.message.display.outgoing": "Du kviskrar åt %s: %s", "commands.op.failed": "Inkje brigdt. Leikaren er alt røktar", "commands.op.success": "Gjorde %s til tenarrøktar", "commands.pardon.failed": "Inkje brigdt. Le<PERSON>ren er ikkje bannlyst", "commands.pardon.success": "Tok burt bannlysing av %s", "commands.pardonip.failed": "<PERSON><PERSON><PERSON> brig<PERSON>; <PERSON>en er <PERSON>kje bann<PERSON>", "commands.pardonip.invalid": "Ugild IP-tilskrift", "commands.pardonip.success": "Tok burt bannlysing av IPen %s", "commands.particle.failed": "Smålùten var ikkje synleg fyre nokon", "commands.particle.success": "Syner smålùten %s", "commands.perf.alreadyRunning": "Dygdarprofileraren er alt sett i gang", "commands.perf.notRunning": "Dygdarprofileraren heve ’kje <PERSON>t", "commands.perf.reportFailed": "Ku<PERSON> ’kje laga lyskefråsegn", "commands.perf.reportSaved": "Lagade lyskefråsegn i %s", "commands.perf.started": "Byrjade ei dygdarprofilering på 10 sekundar (nytta «/perf stop» fyr’ å stodga tidlege)", "commands.perf.stopped": "Stodgade dygdarprofilering etter %s sekund(ar) og %s tikk (%s tikk kvar sekund)", "commands.place.feature.failed": "<PERSON><PERSON> <PERSON>kje setja ned verkendet", "commands.place.feature.invalid": "D’er inkje verkende av slaget «%s»", "commands.place.feature.success": "Sette ned «%s» ved %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON> <PERSON>kje skapa puslebrikka", "commands.place.jigsaw.invalid": "D’er ingen skant av slaget «%s»", "commands.place.jigsaw.success": "Skapade puslebrikka ved %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON> <PERSON>kje setja ned bygnad", "commands.place.structure.invalid": "D’er ingen bygnad av slaget «%s»", "commands.place.structure.success": "Skapade bygnaden «%s» ved %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON> <PERSON>kje setja ned skant", "commands.place.template.invalid": "D’er ingen skant med IDen «%s»", "commands.place.template.success": "Ladde inn skanten «%s» ved %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON> er for langt burte til å verda høyrt", "commands.playsound.success.multiple": "Spelade ljodet %s til %s leikarar", "commands.playsound.success.single": "Spelade ljodet %s til %s", "commands.publish.alreadyPublished": "Samleikar-spel er alt hyst på porten %s", "commands.publish.failed": "Ku<PERSON> <PERSON>kje <PERSON>a heim<PERSON>t spel", "commands.publish.started": "He<PERSON>legt spel er hyst på porten %s", "commands.publish.success": "Samleikar-spel er no hyst på porten %s", "commands.random.error.range_too_large": "Fråstanden åt det slumpevalde verdet kann høgst vera 2147483646", "commands.random.error.range_too_small": "Fråstanden åt det slumpevalde verdet kann minst vera 2", "commands.random.reset.all.success": "Attrade %s tilfellelege fylgd(er)", "commands.random.reset.success": "Attrade den tilfellelega fylgdi %s", "commands.random.roll": "%s rullade %s (frå %s til %s)", "commands.random.sample.success": "Slumpevalt verde: %s", "commands.recipe.give.failed": "<PERSON>gi nye uppskrifter lærde", "commands.recipe.give.success.multiple": "Læste upp %s uppskrift(er) fyre %s leikarar", "commands.recipe.give.success.single": "Læste upp %s uppskrift(er) fyre %s", "commands.recipe.take.failed": "<PERSON><PERSON> uppskrifter kunde verda glø<PERSON>", "commands.recipe.take.success.multiple": "Tok burt %s uppskrift(er) frå %s leikarar", "commands.recipe.take.success.single": "Tok burt %s uppskrift(er) frå %s", "commands.reload.failure": "<PERSON><PERSON> ’kje lada inn å nyo; held på gamle data", "commands.reload.success": "Lader inn å nyo!", "commands.ride.already_riding": "%s rid alt %s", "commands.ride.dismount.success": "%s stodgade å rida %s", "commands.ride.mount.failure.cant_ride_players": "<PERSON> kan ’kje rida leikarar", "commands.ride.mount.failure.generic": "%s kunde ’kje byrja å rida %s", "commands.ride.mount.failure.loop": "<PERSON><PERSON><PERSON> kann ’kje rida seg sjø<PERSON>v elder <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sine", "commands.ride.mount.failure.wrong_dimension": "<PERSON>nn <PERSON>kje rida eining i ein annan heim", "commands.ride.mount.success": "%s tok til å rida %s", "commands.ride.not_riding": "%s rid ingi farkost", "commands.rotate.success": "Reid %s", "commands.save.alreadyOff": "Sparing er alt avsleget", "commands.save.alreadyOn": "Sparing er alt åsleget", "commands.save.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt sparing er no avsleget", "commands.save.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt sparing er no åsleget", "commands.save.failed": "<PERSON>nde ’kje spara spelet (er det nog rom på harddisken?)", "commands.save.saving": "Sparer spelet (detta kann taka ei stund!)", "commands.save.success": "Sparde spelet", "commands.schedule.cleared.failure": "Ingi ætlaner med IDen %s", "commands.schedule.cleared.success": "Tok burt %s ætlan(er) med serkjennet %s", "commands.schedule.created.function": "Ætlade verkendet «%s» um %s tikk på speltid %s", "commands.schedule.created.tag": "Ætlade merkjelappen «%s» um %s tikk ved speltid %s", "commands.schedule.macro": "<PERSON>nn <PERSON>kje ætla ein makro", "commands.schedule.same_tick": "<PERSON>nn <PERSON>kje ætla gjeldande tikk", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON> målsetjing med detta namnet finst alt", "commands.scoreboard.objectives.add.success": "Skipade ny målsetjing %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Inkje brigdt. Denne synestaden er alt tom", "commands.scoreboard.objectives.display.alreadySet": "Inkje brigdt. Denne synestaden syner alt denne målsetjingi", "commands.scoreboard.objectives.display.cleared": "Tok burt alle målsetjingar i synestad %s", "commands.scoreboard.objectives.display.set": "Sette synestad %s til å syna målsetjing %s", "commands.scoreboard.objectives.list.empty": "D’ero ingi m<PERSON>", "commands.scoreboard.objectives.list.success": "D’er(o) %s målsetjing(ar): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Slo av sjølvverkande etterførsla av syning fyre målsetjingi %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Slo på sjølvminnt etterførsla av syning fyre målsetjingi %s", "commands.scoreboard.objectives.modify.displayname": "Brigde synenamnet fyre %s til %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Tok burt det fyrevalde talsnìdet fyre målsetjingi %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Brigde det fyrevalde talsnìdet fyre målsetjingi %s", "commands.scoreboard.objectives.modify.rendertype": "Brigde attgjevingslag fyre målsetjing %s", "commands.scoreboard.objectives.remove.success": "Tok burt målsetjing %s", "commands.scoreboard.players.add.success.multiple": "Lagde %s til %s fyre %s einingar", "commands.scoreboard.players.add.success.single": "Lagde %s til %s fyre %s (no %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Tok burt synenamnet til %s einingar i %s", "commands.scoreboard.players.display.name.clear.success.single": "Tok burt synenamnet til %s i %s", "commands.scoreboard.players.display.name.set.success.multiple": "Brigde synenamnet til %s fyre %s einingar i %s", "commands.scoreboard.players.display.name.set.success.single": "Brigde synenamnet til %s fyre %s i %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Tok burt talsnìdet til %s einingar i %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Tok burt talsnìdet til %s i %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Brigde talsnìdet til %s einingar i %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Brigde talsnìdet til %s i %s", "commands.scoreboard.players.enable.failed": "Inkje brigdt. Denne utløysaren er alt åslegen", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON> berre på utl<PERSON>ysarm<PERSON>", "commands.scoreboard.players.enable.success.multiple": "Slo på utløysar %s fyre %s einingar", "commands.scoreboard.players.enable.success.single": "Slo på utløysar %s fyre %s", "commands.scoreboard.players.get.null": "<PERSON>nn ’kje heimta verdet %s fyre %s; inkje er sett", "commands.scoreboard.players.get.success": "%s heve %s %s", "commands.scoreboard.players.list.empty": "D’ero ingi spòrade e<PERSON>ar", "commands.scoreboard.players.list.entity.empty": "%s hev’ ingi skòretal å syna", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s heve %s skòretal:", "commands.scoreboard.players.list.success": "D’er(o) %s spòrad(e) eining(ar): %s", "commands.scoreboard.players.operation.success.multiple": "Etterførde %s fyre %s einingar", "commands.scoreboard.players.operation.success.single": "Sette %s fyre %s til %s", "commands.scoreboard.players.remove.success.multiple": "Tok burt %s frå %s fyre %s einingar", "commands.scoreboard.players.remove.success.single": "Tok burt %s frå %s fyre %s (no %s)", "commands.scoreboard.players.reset.all.multiple": "Attrade alle skòretal fyre %s einingar", "commands.scoreboard.players.reset.all.single": "Attrade alle skòretal fyre %s", "commands.scoreboard.players.reset.specific.multiple": "Attrade %s fyre %s einingar", "commands.scoreboard.players.reset.specific.single": "Attrade %s fyre %s", "commands.scoreboard.players.set.success.multiple": "Sette %s fyre %s einingar til %s", "commands.scoreboard.players.set.success.single": "Sette %s fyre %s til %s", "commands.seed.success": "Frjo: %s", "commands.setblock.failed": "<PERSON><PERSON> <PERSON>kje <PERSON>ja blokki", "commands.setblock.success": "Brigde blokki ved %s, %s, %s", "commands.setidletimeout.success": "Tidavbrotet fyre gjerandsløysa er no %s minutt(ar)", "commands.setidletimeout.success.disabled": "Tidavbrot fyre gjerandslause leikarar er no avslege", "commands.setworldspawn.failure.not_overworld": "Kann berre setja <PERSON><PERSON><PERSON><PERSON> fyr’ yverheimen", "commands.setworldspawn.success": "Set byrjestaden i heimen til %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Set byrjestaden til %s, %s, %s [%s] i %s fyre %s leikarar", "commands.spawnpoint.success.single": "Sette byrjestad til %s, %s, %s [%s] i %s fyre %s", "commands.spectate.not_spectator": "%s er ikkje i tilskòdarstòda", "commands.spectate.self": "Du kann ’kje vera tilskòdar av deg sjølv", "commands.spectate.success.started": "Ser no på %s", "commands.spectate.success.stopped": "Ser ikkje lenger på nokor eining", "commands.spreadplayers.failed.entities": "Kunde ’kje breida %s eining(ar) kring %s, %s (for mange einingar fyre romet – freista å breida med minst %s)", "commands.spreadplayers.failed.invalid.height": "Ugild maxHeight %s; ventade høgre en heimsminst %s", "commands.spreadplayers.failed.teams": "Kunde ’kje breida %s lag kring %s, %s (for mange einingar fyre romet – freista å breida med minst %s)", "commands.spreadplayers.success.entities": "Breidde %s eining(ar) kring %s, %s med ein medels fråstand på %s blokk/blekker ifrå kvarandre", "commands.spreadplayers.success.teams": "Breidde %s lag kring %s, %s med ein medels fråstand på %s blokk/blekker ifrå kvarandre", "commands.stop.stopping": "<PERSON><PERSON><PERSON> tenaren", "commands.stopsound.success.source.any": "Stodgade alle «%s»-ljo<PERSON>", "commands.stopsound.success.source.sound": "Stodgade ljodet «%s» frå «%s»", "commands.stopsound.success.sourceless.any": "Stod<PERSON> alle ljodi", "commands.stopsound.success.sourceless.sound": "Stodgade ljodet «%s»", "commands.summon.failed": "Ku<PERSON> ’kje kveikja e<PERSON>", "commands.summon.failed.uuid": "Ku<PERSON> ’kje kveikja eining på grunn av like UUIDar", "commands.summon.invalidPosition": "Ugild stad fyre kveikjing", "commands.summon.success": "Ol ny %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> hev<PERSON> anten alt merk<PERSON><PERSON><PERSON>, elder for mange merkjelappar", "commands.tag.add.success.multiple": "Lagde merkjelappen «%s» til %s einingar", "commands.tag.add.success.single": "Lagde merkjelappen «%s» til %s", "commands.tag.list.multiple.empty": "D’ero ingi merkjelappar hjå dei %s einingarne", "commands.tag.list.multiple.success": "Dei %s einingarne hava %s merkjelappar til saman: %s", "commands.tag.list.single.empty": "%s hev’ ingi me<PERSON>", "commands.tag.list.single.success": "%s heve %s merkjelappar: %s", "commands.tag.remove.failed": "Målet hev<PERSON> ikkje denne me<PERSON>pen", "commands.tag.remove.success.multiple": "Tok burt merkjelappen «%s» frå %s einingar", "commands.tag.remove.success.single": "Tok burt merkjelappen «%s» frå %s", "commands.team.add.duplicate": "Eit lag med detta namnet finst alt", "commands.team.add.success": "Skipade lag %s", "commands.team.empty.success": "Tok burt %s medlìm(er) frå lag %s", "commands.team.empty.unchanged": "Inkje brigt. <PERSON><PERSON> laget er alt tomt", "commands.team.join.success.multiple": "Lagde %s medlìmer til lag %s", "commands.team.join.success.single": "Lagde %s til laget %s", "commands.team.leave.success.multiple": "Tok bort %s medlìmer frå alle lag", "commands.team.leave.success.single": "Tok burt %s frå alle lag", "commands.team.list.members.empty": "D’ero ingi medlìmer på lag %s", "commands.team.list.members.success": "Lag %s heve %s medlìm(er): %s", "commands.team.list.teams.empty": "D’ero ingi lag", "commands.team.list.teams.success": "D’er(o) %s lag: %s", "commands.team.option.collisionRule.success": "Samanstøyts-takster fyre laget %s er no «%s»", "commands.team.option.collisionRule.unchanged": "Inkje brigdt. Samanstøyts-taksteren hev’ alt detta verdet", "commands.team.option.color.success": "Etterførde lìten på lag %s til %s", "commands.team.option.color.unchanged": "Inkje brigdt. Detta laget hev’ alt den lìten", "commands.team.option.deathMessageVisibility.success": "Synleiken på daudevitringar fyre lag %s er no «%s»", "commands.team.option.deathMessageVisibility.unchanged": "Inkje brigdt. Synleiken på daudevitringi hev’ alt detta verdet", "commands.team.option.friendlyfire.alreadyDisabled": "Inkje brigdt. Vìneskading er alt avsleget på detta laget", "commands.team.option.friendlyfire.alreadyEnabled": "Inkje brigdt. Vìneskading er alt åsleget på detta laget", "commands.team.option.friendlyfire.disabled": "Slo av vìneskading på laget %s", "commands.team.option.friendlyfire.enabled": "Slo på vìneskading på laget %s", "commands.team.option.name.success": "Etterførde namnet åt laget %s", "commands.team.option.name.unchanged": "Inkje brigdt. Detta laget hev’ alt det namnet", "commands.team.option.nametagVisibility.success": "Synleik på namneskilt hjå laget %s er no «%s»", "commands.team.option.nametagVisibility.unchanged": "Inkje brigdt. Synleiken på namneskilt hev’ alt detta verdet", "commands.team.option.prefix.success": "Lags-fyrefeste sett til %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Inkje brigdt. <PERSON>ta laget kann ’kje sjå usynlege lagslìmer alt", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Inkje brigdt. <PERSON>ta laget kann alt sjå usynlege lagslìmer", "commands.team.option.seeFriendlyInvisibles.disabled": "Lag %s kann ’kje lenger sjå usynlege lagslìmer", "commands.team.option.seeFriendlyInvisibles.enabled": "Lag %s kann no sjå usynlege lagslìmer", "commands.team.option.suffix.success": "Lags-etterfeste sett til %s", "commands.team.remove.success": "Tok burt lag %s", "commands.teammsg.failed.noteam": "Du må vera på eit lag fyr’ å senda bòd til laget ditt", "commands.teleport.invalidPosition": "Ugild stad fyre fjerrflytjing", "commands.teleport.success.entity.multiple": "Fjerrflutte %s einingar til %s", "commands.teleport.success.entity.single": "Fjerrflutte %s til %s", "commands.teleport.success.location.multiple": "Fjerrflutte %s einingar til %s, %s, %s", "commands.teleport.success.location.single": "Fjerrflutte %s til %s, %s, %s", "commands.test.batch.starting": "By<PERSON>jar um<PERSON> %s, lading %s", "commands.test.clear.error.no_tests": "<PERSON><PERSON> ’kje finna ryne å reinska", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Kyv fyr’ å rita av til utklyppstavla", "commands.test.create.success": "Lagade ryneuppset fyre ryne %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "Bygnads-storleiken må vera mindre en %s langs kvar ås", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "<PERSON><PERSON> ryne å køyra", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Ku<PERSON> ’kje finna ryne å attra", "commands.test.reset.success": "Attrade %s bygnad(er)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Prosentar: P50: %sms P95: %sms P99: %sms, sjon: %s", "commands.tick.query.rate.running": "Målverde fyre tikksnarleike: %s kvar sekund.\nMedels tid kvar tikk: %sms (Mål: %sms)", "commands.tick.query.rate.sprinting": "Målverde fyre tikksnarleike: %s kvar sekund (unyttat; einast til upplysing).\nMedels tid kvart tikk: %s ms", "commands.tick.rate.success": "Sette målverdet fyre tikksnarleike til %s kvar sekund", "commands.tick.sprint.report": "<PERSON>prang fullgjort med %s tikk kvar sekund, elder %s ms kvart tikk", "commands.tick.sprint.stop.fail": "Inkje tikksprang er på gang", "commands.tick.sprint.stop.success": "<PERSON>it tikksprang vardt avbrotet", "commands.tick.status.frozen": "Spelet er froset", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON>ø<PERSON>, men kann ikkje nå målverdet fyre tikksnargleike", "commands.tick.status.running": "S<PERSON><PERSON> køyrer <PERSON>", "commands.tick.status.sprinting": "Spelet spring", "commands.tick.step.fail": "<PERSON><PERSON> ’kje st<PERSON>ga spelet – spelet må verda froset fyrst", "commands.tick.step.stop.fail": "Inkje tikkstìg er på gang", "commands.tick.step.stop.success": "<PERSON>it tikkst<PERSON>g vardt avbrotet", "commands.tick.step.success": "Stìgar %s tikk", "commands.time.query": "Tidi er %s", "commands.time.set": "Sette tidi til %s", "commands.title.cleared.multiple": "Tok burt titlar frå %s leikarar", "commands.title.cleared.single": "Tok burt titlar frå %s", "commands.title.reset.multiple": "Attrade titelval fyre %s leikarar", "commands.title.reset.single": "Attrade titelval fyre %s", "commands.title.show.actionbar.multiple": "Syner ny titel i åtgjerdbolken fyre %s leikarar", "commands.title.show.actionbar.single": "Syner ny titel i åtgjerdbolk fyre %s", "commands.title.show.subtitle.multiple": "Syner ny undertekst fyre %s leikarar", "commands.title.show.subtitle.single": "Syner ny undertekst fyre %s", "commands.title.show.title.multiple": "Syner ny titel fyre %s leikarar", "commands.title.show.title.single": "Syner ny titel fyre %s", "commands.title.times.multiple": "Brigde titelsynetid fyre %s leikarar", "commands.title.times.single": "Brigde titelsynetid fyre %s", "commands.transfer.error.no_players": "Må peika ut minst éin leikar å yverføra", "commands.transfer.success.multiple": "Yverfører %s leikarar til %s:%s", "commands.transfer.success.single": "Yverfører %s til %s:%s", "commands.trigger.add.success": "Utløyste %s (lagde til %s på verdet)", "commands.trigger.failed.invalid": "Du kann berre utløysa målsetjingar av slaget «trigger»", "commands.trigger.failed.unprimed": "<PERSON> kann ’kje utlø<PERSON>a denne målsetjingi enno", "commands.trigger.set.success": "Utløyste %s (sette verde til %s)", "commands.trigger.simple.success": "Utløyste %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Utsjånad på leidarmerke brigd", "commands.weather.set.clear": "Sette vedret til klårt", "commands.weather.set.rain": "Sette vedret til regn", "commands.weather.set.thunder": "Sette vedret til regn og tora", "commands.whitelist.add.failed": "Le<PERSON><PERSON> er alt kvitelistad", "commands.whitelist.add.success": "Lagde %s til på kvitelista", "commands.whitelist.alreadyOff": "Kvitelista er alt avslegi", "commands.whitelist.alreadyOn": "Kvitelista er alt åslegi", "commands.whitelist.disabled": "Kvitelista er no avslegi", "commands.whitelist.enabled": "Kvitelista er no åslegi", "commands.whitelist.list": "D’er(o) %s kvitelistad(e) leikar(ar): %s", "commands.whitelist.none": "D’ero ingi kvitelistade leikarar", "commands.whitelist.reloaded": "Ladde inn kvitelista å nyo", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON> er <PERSON>kje k<PERSON>telistad", "commands.whitelist.remove.success": "Tok burt %s frå kvitelista", "commands.worldborder.center.failed": "Inkje brigdt. Heimsgrensa hev’ alt midstad der", "commands.worldborder.center.success": "Sette midstad av heimsgrensa til %s, %s", "commands.worldborder.damage.amount.failed": "Inkje brigdt. Skaden ved heimsgrensa er alt den mengdi", "commands.worldborder.damage.amount.success": "Set skaden ved heimsgrensa til %s fyre kór blokk kvar sekund", "commands.worldborder.damage.buffer.failed": "Inkje brigdt. Skadeumkvervet ved heimsgrensa er alt den fråstanden", "commands.worldborder.damage.buffer.success": "Sette skadeumkvervet åt heimsgrensa til %s blokk/blekker", "commands.worldborder.get": "Heimsgrensa er fyre tidi %s blokk/blekker breid", "commands.worldborder.set.failed.big": "Heimsgrensa kann ’kje vera vidare en %s blekker", "commands.worldborder.set.failed.far": "Heimsgrensa kann ’kje vera lengre ute en %s blekker", "commands.worldborder.set.failed.nochange": "Inkje brigdt. Heimsgrensa hev’ alt den storleiken", "commands.worldborder.set.failed.small": "Heimsgrensa kann ’kje vera mindre en 1 blokk breid", "commands.worldborder.set.grow": "Aukar heimsgrensa til %s blekker vid yver %s sekundar", "commands.worldborder.set.immediate": "Sette heimsgrensa til %s blokk/blekker breid", "commands.worldborder.set.shrink": "Minskar heimsgrensa til %s blokk/blekker breid på %s sekund", "commands.worldborder.warning.distance.failed": "Inkje brigdt. Åtvaringi fyre skade ved heimsgrensa er alt den fråstanden", "commands.worldborder.warning.distance.success": "Sette åtvarefråstanden fyr’ heimsgrensa til %s blokk/blekker", "commands.worldborder.warning.time.failed": "Inkje brigdt. Åtvaringi fyre skade ved heimsgrensa er alt den tidmengdi", "commands.worldborder.warning.time.success": "Sette åtvaretidi åt heimsgrensa til %s sekund(ar)", "compliance.playtime.greaterThan24Hours": "Du heve spelat i meir en 24 timar", "compliance.playtime.hours": "Du heve spelat i %s time/-ar", "compliance.playtime.message": "For myket speling kann skipla k<PERSON>n din", "connect.aborted": "Avbrotet", "connect.authorizing": "Skriv deg inn...", "connect.connecting": "Ko<PERSON><PERSON> til tenaren...", "connect.encrypting": "Krypterar...", "connect.failed": "Fekk ikkje samband med tenaren", "connect.failed.transfer": "Fekk ikkje samband medan du yverførde til tenaren", "connect.joining": "Fer inn i heim...", "connect.negotiating": "Samrådest...", "connect.reconfiging": "Umreider...", "connect.reconfiguring": "Umreider...", "connect.transferring": "<PERSON><PERSON><PERSON> yver til ny tenar...", "container.barrel": "<PERSON><PERSON>", "container.beacon": "V<PERSON><PERSON>", "container.beehive.bees": "Bior: %s / %s", "container.beehive.honey": "Huning: %s / %s", "container.blast_furnace": "Malmomn", "container.brewing": "Bryggjestod", "container.cartography_table": "Kortteiknarbord", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "Stor kista", "container.crafter": "<PERSON><PERSON>", "container.crafting": "<PERSON><PERSON>", "container.creative": "Vel ting", "container.dispenser": "Utskjotar", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "Galdra", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s <PERSON><PERSON>tein", "container.enchant.lapis.one": "1 <PERSON><PERSON><PERSON>", "container.enchant.level.many": "%s galdrestìg", "container.enchant.level.one": "1 galdrestìg", "container.enchant.level.requirement": "Stìgkrav: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Omn", "container.grindstone_title": "Bøt og avgaldra", "container.hopper": "Tingtrekt", "container.inventory": "Skreppa", "container.isLocked": "%s er læst!", "container.lectern": "Bokstol", "container.loom": "Vevstol", "container.repair": "Bøt og gjev namn", "container.repair.cost": "Galdrekostnad: %1$s", "container.repair.expensive": "For dyrt!", "container.shulkerBox": "<PERSON><PERSON>er<PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "og %s til...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Røykjaromn", "container.spectatorCantOpen": "<PERSON><PERSON> <PERSON>kje opna. <PERSON>d er ’kje skapad enn-no.", "container.stonecutter": "Steinskjere", "container.upgrade": "Betra utbunad", "container.upgrade.error_tooltip": "<PERSON>nn <PERSON>kje betra utbunaden på denne visi", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON> til smideskant", "controls.keybinds": "Knappebindingar...", "controls.keybinds.duplicateKeybinds": "<PERSON>ne knappen er òg nytta til:\n%s", "controls.keybinds.title": "Knappebindingar", "controls.reset": "Attra", "controls.resetAll": "Attra knappar", "controls.title": "Styring", "createWorld.customize.buffet.biome": "Vel eit lende", "createWorld.customize.buffet.title": "Kakebords-tilmåting", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Botn – %s", "createWorld.customize.flat.layer.top": "Topp – %s", "createWorld.customize.flat.removeLayer": "Tak burt lag", "createWorld.customize.flat.tile": "Lagemne", "createWorld.customize.flat.title": "Vatsrett-tilmåting", "createWorld.customize.presets": "Fyrelaging", "createWorld.customize.presets.list": "Um ikkje so er det nokre her som me hava lagat tidlegare!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON> fyre<PERSON>", "createWorld.customize.presets.share": "Ynskjer du å deila fyrelagingi med nokon? Nytta teigen under!", "createWorld.customize.presets.title": "Vel ei fyrelaging", "createWorld.preparing": "<PERSON><PERSON><PERSON>...", "createWorld.tab.game.title": "Spel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON>", "credits_and_attribution.button.attribution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.credits": "Medverkande", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "Medverkande og tilleggjingar", "dataPack.bundle.description": "S<PERSON><PERSON><PERSON> på utrøyneleg sekk-ting", "dataPack.bundle.name": "<PERSON><PERSON>", "dataPack.locator_bar.description": "<PERSON>yn leidi åt andre leika<PERSON> i samleikar", "dataPack.locator_bar.name": "Leidarstong", "dataPack.minecart_improvements.description": "<PERSON><PERSON> rø<PERSON> fyre malm<PERSON>gner", "dataPack.minecart_improvements.name": "Betring av malm<PERSON>gner", "dataPack.redstone_experiments.description": "Utrøynelege redstoneskifte", "dataPack.redstone_experiments.name": "Redstoneutrøyne", "dataPack.title": "Vel datapakkar", "dataPack.trade_rebalance.description": "Umgjorde byte hjå bygdarfolk", "dataPack.trade_rebalance.name": "Umvegne bygdarbuebyte", "dataPack.update_1_20.description": "Nye verkende og innehald i Minecraft 1.20", "dataPack.update_1_20.name": "Etterførsla 1.20", "dataPack.update_1_21.description": "Nye verkende og innehald til Minecraft 1.21", "dataPack.update_1_21.name": "Etterførsla 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON> attende", "dataPack.validation.failed": "Validering av datapakke var mislukkad!", "dataPack.validation.reset": "Attra til fyreval", "dataPack.validation.working": "Validerar valde datapakkar...", "dataPack.vanilla.description": "Dei fyrevalde datai fyre Minecraft", "dataPack.vanilla.name": "Fyrevalt", "dataPack.winter_drop.description": "Nye verkande og innehald til vettersleppet", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Trygg-stòda", "datapackFailure.safeMode.failed.description": "<PERSON><PERSON> heimen inneheld ugilde elder brotne sparingdata.", "datapackFailure.safeMode.failed.title": "<PERSON><PERSON> ’kje lada inn heimen i trygg-stòda.", "datapackFailure.title": "Lyte i dei valde datapòkkom hindrade heimen frå å lada inn.\nDu kann anten freista å lada inn med vanleg datapakke («trygg-stòda») elder ganga atter åt hovudskråi og retta det upp fyr’ hond.", "death.attack.anvil": "%1$s vardt klemrad av eit fallande sted", "death.attack.anvil.player": "%1$s vardt klemrad av eit fallande sted i strid med %2$s", "death.attack.arrow": "%1$s vardt skoten/-i av %2$s", "death.attack.arrow.item": "%1$s vardt skoten/-i av %2$s med %3$s", "death.attack.badRespawnPoint.link": "Tiltenkt speldesign", "death.attack.badRespawnPoint.message": "%1$s vardt drepen/-i av %2$s", "death.attack.cactus": "%1$s stakk seg i hel", "death.attack.cactus.player": "%1$s gjekk inn i ein kaktus i flog frå %2$s", "death.attack.cramming": "%1$s vardt klemrad for myket", "death.attack.cramming.player": "%1$s vardt klemrad av %2$s", "death.attack.dragonBreath": "%1$s vardt steikt av drakeande", "death.attack.dragonBreath.player": "%1$s vart steikt av drakeande av %2$s", "death.attack.drown": "%1$s druknade", "death.attack.drown.player": "%1$s druknade i flog frå %2$s", "death.attack.dryout": "%1$s tyrstade i hel", "death.attack.dryout.player": "%1$s tyrstade i hel i flog frå %2$s", "death.attack.even_more_magic": "%1$s vardt drepen/-i av enn-då meire trolldom", "death.attack.explosion": "%1$s vardt sprengd i fillor", "death.attack.explosion.player": "%1$s vardt sprengd i fillor av %2$s", "death.attack.explosion.player.item": "%1$s vardt sprengd i fillor av %2$s med %3$s", "death.attack.fall": "%1$s møtte bakken ei smòla for hardt", "death.attack.fall.player": "%1$s møtte bakken for hardt i flog frå %2$s", "death.attack.fallingBlock": "%1$s vardt klemrad av ei fallande blokk", "death.attack.fallingBlock.player": "%1$s vardt klemrad av ei fallande blokk i strid med %2$s", "death.attack.fallingStalactite": "%1$s vardt spitad av fallande stalaktitt", "death.attack.fallingStalactite.player": "%1$s vardt spitad av fallande stalaktitt i strid med %2$s", "death.attack.fireball": "%1$s vardt eldkuleskoten/-i av %2$s", "death.attack.fireball.item": "%1$s vardt eldkuleskoten/-i av %2$s med %3$s", "death.attack.fireworks": "%1$s gjekk av med eit pang", "death.attack.fireworks.item": "%1$s gjekk av med eit pang frå eit fyrverk skotet frå %3$s av %2$s", "death.attack.fireworks.player": "%1$s gjekk av med eit pang i strid med %2$s", "death.attack.flyIntoWall": "%1$s røynde rørslekraft", "death.attack.flyIntoWall.player": "%1$s røynde rørslekraft i flog frå %2$s", "death.attack.freeze": "%1$s fraus i hel", "death.attack.freeze.player": "%1$s vardt frøyst i hel av %2$s", "death.attack.generic": "%1$s do", "death.attack.generic.player": "%1$s do på grunn av %2$s", "death.attack.genericKill": "%1$s vardt drepen/-i", "death.attack.genericKill.player": "%1$s vardt drepen/-i i strid med %2$s", "death.attack.hotFloor": "%1$s fann ut at golvet var raun", "death.attack.hotFloor.player": "%1$s gjekk inn i fårestròket på grunn av %2$s", "death.attack.inFire": "%1$s brann upp", "death.attack.inFire.player": "%1$s vardt ei smòla for heit i strid med %2$s", "death.attack.inWall": "%1$s kjøvdest i ein vegg", "death.attack.inWall.player": "%1$s kjøvdest i ein vegg i strid med %2$s", "death.attack.indirectMagic": "%1$s vardt drepen/-i av %2$s med trolldom", "death.attack.indirectMagic.item": "%1$s vardt drepen/-i av %2$s med %3$s", "death.attack.lava": "%1$s freistade symja i raun", "death.attack.lava.player": "%1$s freistade symja i raun i flog frå %2$s", "death.attack.lightningBolt": "%1$s vardt slegen/-i av elding", "death.attack.lightningBolt.player": "%1$s vardt slegen/-i av elding i strid med %2$s", "death.attack.mace_smash": "%1$s vardt krasad av %2$s", "death.attack.mace_smash.item": "%1$s vardt krasad av %2$s med %3$s", "death.attack.magic": "%1$s vardt drepen/-i av trolldom", "death.attack.magic.player": "%1$s vardt drepen av trolldom i flog frå %2$s", "death.attack.message_too_long": "Bòdet var diverre for langt til å verda sendt. Orsaka! Her er ei nedstytt utgåva: %s", "death.attack.mob": "%1$s vardt drepen/-i av %2$s", "death.attack.mob.item": "%1$s vardt drepen/-i av %2$s med %3$s", "death.attack.onFire": "%1$s brann i hel", "death.attack.onFire.item": "%1$s brann til oska i strid med %2$s som nyttade %3$s", "death.attack.onFire.player": "%1$s brann til oska i strid med %2$s", "death.attack.outOfWorld": "%1$s fill ut av heimen", "death.attack.outOfWorld.player": "%1$s ynskte ikkje lìva i den same heimen som %2$s", "death.attack.outsideBorder": "%1$s hamnade utanfyr’ heimsgrensa", "death.attack.outsideBorder.player": "%1$s hamnade utanfyr’ heimsgrensa i strid med %2$s", "death.attack.player": "%1$s vardt drepen/-i av %2$s", "death.attack.player.item": "%1$s vardt drepen/-i av %2$s med %3$s", "death.attack.sonic_boom": "%1$s vardt utslettad av eit supersoniskt skrik", "death.attack.sonic_boom.item": "%1$s vardt utslettad av eit supersoniskt skrik i flog frå %2$s som nyttade %3$s", "death.attack.sonic_boom.player": "%1$s vardt utslettad av eit supersoniskt skrik i flog frå %2$s", "death.attack.stalagmite": "%1$s vardt spitad på ein stalagmitt", "death.attack.stalagmite.player": "%1$s vardt spitad på ein stalagmitt i strid med %2$s", "death.attack.starve": "%1$s svalt i hel", "death.attack.starve.player": "%1$s svalt i hel i strid med %2$s", "death.attack.sting": "%1$s vardt stungen i hel", "death.attack.sting.item": "%1$s vardt stungen i hel av %2$s med %3$s", "death.attack.sting.player": "%1$s vardt stungen i hel av %2$s", "death.attack.sweetBerryBush": "%1$s vardt stungen/-i i hel av søtberjalyng", "death.attack.sweetBerryBush.player": "%1$s vardt stungen/-i i hel av søtberjalyng i flog frå %2$s", "death.attack.thorns": "%1$s vardt drepen/-i i ein freistnad på å skada %2$s", "death.attack.thorns.item": "%1$s vart drepen av %3$s i ein freistnad på å skada %2$s", "death.attack.thrown": "%1$s vardt bombarderad av %2$s", "death.attack.thrown.item": "%1$s vardt bombarderad av %2$s med %3$s", "death.attack.trident": "%1$s vardt spitad av %2$s", "death.attack.trident.item": "%1$s vardt spitad av %2$s med %3$s", "death.attack.wither": "%1$s visnade burt", "death.attack.wither.player": "%1$s visnade burt i strid med %2$s", "death.attack.witherSkull": "%1$s vardt skoten/-i av ein skalle frå %2$s", "death.attack.witherSkull.item": "%1$s vardt skoten/-i av ein skalle frå %2$s med %3$s", "death.fell.accident.generic": "%1$s fill frå ein høg stad", "death.fell.accident.ladder": "%1$s fill frå ein stige", "death.fell.accident.other_climbable": "%1$s kleiv og fill", "death.fell.accident.scaffolding": "%1$s fill ned frå ei stelling", "death.fell.accident.twisting_vines": "%1$s fill av nokre krokande klivevokstrar", "death.fell.accident.vines": "%1$s fill av ein klivevokster", "death.fell.accident.weeping_vines": "%1$s fill av nokre gråtande klivevokstrar", "death.fell.assist": "%1$s vardt dømd til å falla av %2$s", "death.fell.assist.item": "%1$s vardt dømd til å falla av %2$s med %3$s", "death.fell.finish": "%1$s fill for langt og vardt drepen/-i av %2$s", "death.fell.finish.item": "%1$s fill for langt og vardt drepen/-i av %2$s med %3$s", "death.fell.killer": "%1$s vardt dømd til å falla", "deathScreen.quit.confirm": "Er du trygg på at du vil enda spelet?", "deathScreen.respawn": "Statt upp att", "deathScreen.score": "Skòretal", "deathScreen.score.value": "Skòretal: %s", "deathScreen.spectate": "<PERSON><PERSON>", "deathScreen.title": "Du do!", "deathScreen.title.hardcore": "Du tapade!", "deathScreen.titleScreen": "Hovudskrå", "debug.advanced_tooltips.help": "F3 + H = Vidkade tølevitringar", "debug.advanced_tooltips.off": "Vidkade tølevitringar: løynde", "debug.advanced_tooltips.on": "Vidkade tølevitringar: synte", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON> he<PERSON>", "debug.chunk_boundaries.off": "Heimsbitgrensor: g<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "Heimsbitgrensor: synte", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON> svall", "debug.copy_location.help": "F3 + C = Rita av stadsetjing som /tp kommando. Haldt F3 + C fyr’ å å valda at spelet ryn i hop", "debug.copy_location.message": "Ritade av stadsetjing til utklyppstavla", "debug.crash.message": "F3 + C er haldet nedre. <PERSON><PERSON> veld at spelet ryn i hop um ikkje sleppt.", "debug.crash.warning": "Ryn i hop um %s...", "debug.creative_spectator.error": "Ku<PERSON> ’kje skifta spelstòda; inkje løyve", "debug.creative_spectator.help": "F3 + N = Skift millom fyrre spelstòda <-> tilskòdar", "debug.dump_dynamic_textures": "Spar rørande vevnader til %s", "debug.dump_dynamic_textures.help": "F3 + S = dumpa rørande vevnader", "debug.gamemodes.error": "Ku<PERSON> ’kje opna spelstòdeskiftar; vantar løyve", "debug.gamemodes.help": "F3 + F4 = Opna spelstòdeskiftar", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Næste", "debug.help.help": "F3 + Q = <PERSON>yn denne lista", "debug.help.message": "Knappebindingar:", "debug.inspect.client.block": "Ritade av klient-blokkdata til utklyppstavla", "debug.inspect.client.entity": "Ritade av klient-einingdata til utklyppstavla", "debug.inspect.help": "F3 + I = <PERSON> av e<PERSON>- elder blokk<PERSON> til utklyppstavla", "debug.inspect.server.block": "Ritade av tenar-blokkdata til utklyppstavla", "debug.inspect.server.entity": "Ritade av tenar-einingdata til utklyppstavla", "debug.pause.help": "F3 + Esc = Stodga spelet utan stodgeskrå (um stodging er mogelegt)", "debug.pause_focus.help": "F3 + P = Avbrot um fokuset er mist", "debug.pause_focus.off": "Avbrot um fokuset er mist: avsleget", "debug.pause_focus.on": "Avbrot um fokuset er mist: åsleget", "debug.prefix": "[Lysking]:", "debug.profiling.help": "F3 + L = Set i gang/enda profilering", "debug.profiling.start": "Sette i gang profilering i %s sekundar. Nytta F3 + L fyr’ å stodga tidlege", "debug.profiling.stop": "Profilering endade. Sparde utfall til %s", "debug.reload_chunks.help": "F3 + A = Lader inn heimsbitar å nyo", "debug.reload_chunks.message": "Lader inn alle heimsbitar å nyo", "debug.reload_resourcepacks.help": "F3 + T = Lad inn tilfangspakkar å nyo", "debug.reload_resourcepacks.message": "Lad inn tilfangspakkar å nyo", "debug.show_hitboxes.help": "F3 + B = <PERSON>yn s<PERSON>", "debug.show_hitboxes.off": "Skotråmor: gø<PERSON><PERSON>", "debug.show_hitboxes.on": "Skotråmor: synte", "debug.version.header": "Upplysingar um k<PERSON>gåva:", "debug.version.help": "F3 + V = Upplysingar um k<PERSON>utgåva", "demo.day.1": "Denne freisteutgåva kjem til å vara i fem speldagar. Gjer ditt beste!", "demo.day.2": "Dag tvei", "demo.day.3": "Dag tri", "demo.day.4": "<PERSON>g fjore", "demo.day.5": "Detta er sidste dagen!", "demo.day.6": "Den sidste dagen din er yver. Trykk på %s fyr’ å taka eit skjermbilæte av byggverket ditt.", "demo.day.warning": "Tidi di er snart ute!", "demo.demoExpired": "Freistetidi er yver!", "demo.help.buy": "Kaup no!", "demo.help.fullWrapped": "Denne freisteutgåva varer 5 speldagar (kring 1 time og 40 minutt). Sjå bragderna fyre råder! Hav det kjekt!", "demo.help.inventory": "Kyv %1$s fyr’ å opna skreppa di", "demo.help.jump": "Hoppa med %1$s", "demo.help.later": "Haldt fram å spela!", "demo.help.movement": "Nytta %1$s, %2$s, %3$s, %4$s og musi til å røyva deg", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> kring deg med musi", "demo.help.movementShort": "Røyv deg ved å kyva %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft-freisteutgåve", "demo.remainingTime": "Tid att: %s", "demo.reminder": "Freistetidi er yver. <PERSON><PERSON> spelet fyr’ å halda fram, elder skapa ny heim!", "difficulty.lock.question": "Er du trygg på at du vil læsa vandleiken på denne heimen? Dette kjem til å setja vandleiken til %1$s, og du kann ’kje skifta ’nom att.", "difficulty.lock.title": "<PERSON><PERSON><PERSON> til heimen", "disconnect.endOfStream": "Strøymingi endade", "disconnect.exceeded_packet_rate": "Kastad ut fyr’ å ganga yver talgrensa fyre datapakkar", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ser burt ifr<PERSON> stòde-fyrespurnad", "disconnect.loginFailedInfo": "Ku<PERSON> ’kje skriva deg inn: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Samleikar er avsleget. Sjå på vali fyre Microsoft-kontoen din.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> (freista å køyra spelet og opnaren å nyo)", "disconnect.loginFailedInfo.serversUnavailable": "Kunde ’kje nå stadfestartenararne fyre tidi. Freista å nyo.", "disconnect.loginFailedInfo.userBanned": "Du er bannlyst frå å spela ånetes", "disconnect.lost": "Miste sambandet", "disconnect.packetError": "Lyte i netverksprotokollen", "disconnect.spam": "Kastad ut på grunn av spam", "disconnect.timeout": "Tidavbrot", "disconnect.transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON> til ein annan tenar", "disconnect.unknownHost": "<PERSON><PERSON><PERSON><PERSON> hysar", "download.pack.failed": "<PERSON><PERSON> ’kje lada ned %s av %s pakke/-ar", "download.pack.progress.bytes": "Framgang: %s (full storleike ukjend)", "download.pack.progress.percent": "Framgang: %s%%", "download.pack.title": "Lader ned tilfangspakke %s/%s", "editGamerule.default": "Fyreval: %s", "editGamerule.title": "Reid spel<PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Upptòka", "effect.minecraft.bad_omen": "<PERSON>l jar<PERSON><PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON>", "effect.minecraft.conduit_power": "Flødarkraft", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Tumlardygd", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON>", "effect.minecraft.glowing": "Glod", "effect.minecraft.haste": "Skunding", "effect.minecraft.health_boost": "Helseauke", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "Hunger", "effect.minecraft.infested": "<PERSON><PERSON>", "effect.minecraft.instant_damage": "Brådskade", "effect.minecraft.instant_health": "Brådhelsa", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Sviving", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Nåmsmøda", "effect.minecraft.nausea": "Øgjing", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Ty<PERSON>", "effect.minecraft.poison": "<PERSON><PERSON>", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Atterskaping", "effect.minecraft.resistance": "Vern", "effect.minecraft.saturation": "Metting", "effect.minecraft.slow_falling": "Seint fall", "effect.minecraft.slowness": "Tråleike", "effect.minecraft.speed": "<PERSON>narleike", "effect.minecraft.strength": "Styrke", "effect.minecraft.trial_omen": "Røynejarteign", "effect.minecraft.unluck": "<PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "Vatsanding", "effect.minecraft.weakness": "Veikskap", "effect.minecraft.weaving": "Veving", "effect.minecraft.wind_charged": "Vindladd", "effect.minecraft.wither": "V<PERSON><PERSON>", "effect.none": "<PERSON><PERSON> verk<PERSON>der", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Vatsvyrk", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.binding_curse": "Bindingsgand", "enchantment.minecraft.blast_protection": "Sprengnadsvern", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Leidande", "enchantment.minecraft.density": "Tettleike", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_aspect": "Eldkraft", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "Loge", "enchantment.minecraft.fortune": "<PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Frost<PERSON>gar", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Atterslag", "enchantment.minecraft.looting": "<PERSON><PERSON>", "enchantment.minecraft.loyalty": "Trygd", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.lure": "Agn", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Margskot", "enchantment.minecraft.piercing": "Gjenomtrengjande", "enchantment.minecraft.power": "Kraft", "enchantment.minecraft.projectile_protection": "Skotvern", "enchantment.minecraft.protection": "Vern", "enchantment.minecraft.punch": "Slag", "enchantment.minecraft.quick_charge": "Snarlading", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sharpness": "Kvassleike", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "Vandaudbane", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "Strjukande sverdsegg", "enchantment.minecraft.sweeping_edge": "Strjukande sverdsegg", "enchantment.minecraft.swift_sneak": "Snarsmjuging", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Herd", "enchantment.minecraft.vanishing_curse": "Kvervingsgand", "enchantment.minecraft.wind_burst": "Vindsprengnad", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "Akasiebåt med kista", "entity.minecraft.allay": "H<PERSON>l<PERSON>ònd", "entity.minecraft.area_effect_cloud": "Umkverves-verknadssky", "entity.minecraft.armadillo": "Beltedyr", "entity.minecraft.armor_stand": "Brynjestod", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "Aks<PERSON>tl", "entity.minecraft.bamboo_chest_raft": "Bambusflòte med kista", "entity.minecraft.bamboo_raft": "Bambusflòte", "entity.minecraft.bat": "Skjåvengja", "entity.minecraft.bee": "Bia", "entity.minecraft.birch_boat": "Bjørkarb<PERSON>t", "entity.minecraft.birch_chest_boat": "Bjørkarbåt med kista", "entity.minecraft.blaze": "Loge", "entity.minecraft.block_display": "Blokksyning", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Floga", "entity.minecraft.breeze_wind_charge": "Vindlading", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "Kisseberbåt med kista", "entity.minecraft.chest_boat": "<PERSON><PERSON>t med kista", "entity.minecraft.chest_minecart": "Malmvogn med kista", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Torsk", "entity.minecraft.command_block_minecart": "Malmvogn med styrebòdsblokk", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Kner<PERSON>", "entity.minecraft.creaking_transient": "Kner<PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Myrkeikebåt", "entity.minecraft.dark_oak_chest_boat": "Myrkeikebåt med kista", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Eldkula frå drake", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Kastat egg", "entity.minecraft.elder_guardian": "<PERSON><PERSON>", "entity.minecraft.end_crystal": "Endedvergsmide", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON> end<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Ender<PERSON>", "entity.minecraft.evoker": "Andevekkjar", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Kastad galdreflaske", "entity.minecraft.experience_orb": "R<PERSON><PERSON>sle<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "Enderauga", "entity.minecraft.falling_block": "<PERSON>ande blokk", "entity.minecraft.falling_block_type": "Fallande %s", "entity.minecraft.fireball": "<PERSON>d<PERSON><PERSON>", "entity.minecraft.firework_rocket": "Fyrverk", "entity.minecraft.fishing_bobber": "<PERSON><PERSON>", "entity.minecraft.fox": "Rev", "entity.minecraft.frog": "Frosk", "entity.minecraft.furnace_minecart": "Malmvogn med omn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Gløderåma", "entity.minecraft.glow_squid": "Glødespruta", "entity.minecraft.goat": "Geit", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON> ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Malmvogn med trekt", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "<PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "Sjonkvervar", "entity.minecraft.interaction": "Samhandling", "entity.minecraft.iron_golem": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item": "<PERSON>g", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON>g<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Regnskogsbåt", "entity.minecraft.jungle_chest_boat": "Regnskogsbåt med kista", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Tygelknut", "entity.minecraft.lightning_bolt": "<PERSON><PERSON>", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_boat": "Mangrovebåt", "entity.minecraft.mangrove_chest_boat": "Mangrovebåt med kista", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Malmvogn", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON>ld<PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "Eikebåt med kista", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Illspåen tingskapar", "entity.minecraft.painting": "Målarstykke", "entity.minecraft.pale_oak_boat": "Bleikeikebå<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Bleikeikebåt med kista", "entity.minecraft.panda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.parrot": "Pavegauk", "entity.minecraft.phantom": "Tankefoster", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "Ransbue", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "Igulfisk", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON>", "entity.minecraft.salmon": "Laks", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Sk<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Sylvkrek", "entity.minecraft.skeleton": "Beingrind", "entity.minecraft.skeleton_horse": "Øykjebeingrind", "entity.minecraft.slime": "Sliming", "entity.minecraft.small_fireball": "<PERSON><PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON>", "entity.minecraft.snow_golem": "Snjokall", "entity.minecraft.snowball": "Snjoball", "entity.minecraft.spawner_minecart": "Malmvogn med skapar", "entity.minecraft.spectral_arrow": "Merkjekolv", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "Granbåt med kista", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Villegrind", "entity.minecraft.strider": "St<PERSON>gar", "entity.minecraft.tadpole": "Rovetroll", "entity.minecraft.text_display": "Tekstsyning", "entity.minecraft.tnt": "Tendrad TNT", "entity.minecraft.tnt_minecart": "Malmvogn med TNT", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Ljoster", "entity.minecraft.tropical_fish": "Sudhavsfisk", "entity.minecraft.tropical_fish.predefined.0": "Sjorosefisk", "entity.minecraft.tropical_fish.predefined.1": "Svart kirurgfisk", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> cornutus", "entity.minecraft.tropical_fish.predefined.11": "P<PERSON>dd s<PERSON>lfinnefisk", "entity.minecraft.tropical_fish.predefined.12": "Pavegaukfisk", "entity.minecraft.tropical_fish.predefined.13": "Drottningskalar", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Trådugge", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Avtrekkjarfisk", "entity.minecraft.tropical_fish.predefined.2": "Blå kirurg", "entity.minecraft.tropical_fish.predefined.20": "Gulrovad pavegaukfisk", "entity.minecraft.tropical_fish.predefined.21": "Gul kirurg", "entity.minecraft.tropical_fish.predefined.3": "Skjelfinnefisk", "entity.minecraft.tropical_fish.predefined.4": "Siklid", "entity.minecraft.tropical_fish.predefined.5": "Klovnefisk", "entity.minecraft.tropical_fish.predefined.6": "Blå-ljos<PERSON>ud kamp<PERSON>", "entity.minecraft.tropical_fish.predefined.7": "Tvilìting", "entity.minecraft.tropical_fish.predefined.8": "Keisarsnappar", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "Kampfisk", "entity.minecraft.tropical_fish.type.blockfish": "Blokkfisk", "entity.minecraft.tropical_fish.type.brinely": "Saltfisk", "entity.minecraft.tropical_fish.type.clayfish": "Leirfisk", "entity.minecraft.tropical_fish.type.dasher": "Strìkfisk", "entity.minecraft.tropical_fish.type.flopper": "Skvalpar", "entity.minecraft.tropical_fish.type.glitter": "Glitterfisk", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snokefisk", "entity.minecraft.tropical_fish.type.spotty": "Flekkefisk", "entity.minecraft.tropical_fish.type.stripey": "Striping", "entity.minecraft.tropical_fish.type.sunstreak": "Solstripefisk", "entity.minecraft.turtle": "Skjelpodda", "entity.minecraft.vex": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager": "Bygdarbue", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "Slagtar", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON>st", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Lederverkar", "entity.minecraft.villager.librarian": "Bokverja", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Styving", "entity.minecraft.villager.none": "Bygdarbue", "entity.minecraft.villager.shepherd": "Hyrding", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON>nsmì<PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Vord", "entity.minecraft.wind_charge": "Vindlading", "entity.minecraft.witch": "Trollkjerring", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherbeingrind", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Ulv", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Øykjenå<PERSON>", "entity.minecraft.zombie_villager": "Bygdar<PERSON>å<PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Kann ’kje kveikja eining av slaget %s", "event.minecraft.raid": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "<PERSON><PERSON>", "event.minecraft.raid.defeat.full": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Herjarar att: %s", "event.minecraft.raid.victory": "Si<PERSON>", "event.minecraft.raid.victory.full": "Herjing - Siger", "filled_map.buried_treasure": "Skattekort", "filled_map.explorer_jungle": "Regnskogs-grenskekort", "filled_map.explorer_swamp": "Myrgrenskekort", "filled_map.id": "Id #%s", "filled_map.level": "(Stìg %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON>", "filled_map.mansion": "Skoggrenskingkort", "filled_map.monument": "Sjogrenskekort", "filled_map.scale": "Skalering på 1:%s", "filled_map.trial_chambers": "Kort til røynerom", "filled_map.unknown": "<PERSON><PERSON><PERSON><PERSON> kort", "filled_map.village_desert": "<PERSON>rt yver sand<PERSON>d", "filled_map.village_plains": "<PERSON>rt yver slettebygd", "filled_map.village_savanna": "<PERSON>rt yver savan<PERSON>d", "filled_map.village_snowy": "<PERSON>rt yver snjobygd", "filled_map.village_taiga": "Kort yver barskogsbygd", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON> hol", "flat_world_preset.minecraft.classic_flat": "Klassisk flat", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Yverheim", "flat_world_preset.minecraft.redstone_ready": "Reidug fyre redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "Tomrome<PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Drau<PERSON>t ein hòlegangsgravar", "flat_world_preset.minecraft.water_world": "Vatsheim", "flat_world_preset.unknown": "???", "gameMode.adventure": "Grenskarstòda", "gameMode.changed": "Spelstòda di er vordi brigd til %s", "gameMode.creative": "Skapestòda", "gameMode.hardcore": "hardhausstòda", "gameMode.spectator": "Tilskòdarstòda", "gameMode.survival": "Attlìvnadsstòda", "gamerule.allowFireTicksAwayFromPlayer": "Lat eld breida seg burte frå leikarar", "gamerule.allowFireTicksAwayFromPlayer.description": "Avg<PERSON>er um eld kann breida seg meir en 8 heimsbitar burte frå ein leikar", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON><PERSON> bragder", "gamerule.blockExplosionDropDecay": "I sprengnader frå blokksamhandling sleppa nokre blekker ikkje allstødt fengdi si", "gamerule.blockExplosionDropDecay.description": "Nokot av sleppet frå blekker brotna av sprengnader valdne av blokkpåverknad kverv i sprengnaden.", "gamerule.category.chat": "Svall", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON>", "gamerule.category.mobs": "Kvìkende", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "<PERSON><PERSON><PERSON>", "gamerule.category.updates": "He<PERSON>etterfø<PERSON><PERSON>", "gamerule.commandBlockOutput": "Kringkasta utgjøv frå styrebòdsblekker", "gamerule.commandModificationBlockLimit": "Blokkgrensa fyre styrebòdsbrigde", "gamerule.commandModificationBlockLimit.description": "<PERSON>gd av blekker som verda brigde samstundes av eitt styrebog, t.d. fill elder clone.", "gamerule.disableElytraMovementCheck": "Slå av røkjing av skalvengje-rørsla", "gamerule.disablePlayerMovementCheck": "Slå av rørslerøknad fyre leikarar", "gamerule.disableRaids": "Slå av herjingar", "gamerule.doDaylightCycle": "Set tidi fram", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON> reidnad frå e<PERSON>", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON> slepp frå ma<PERSON> (gjeld <PERSON>g skre<PERSON>), tin<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, o.s.fr.", "gamerule.doFireTick": "Lat elden skifta", "gamerule.doImmediateRespawn": "Statt upp att på augneblinken", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON> fantom", "gamerule.doLimitedCrafting": "Krev uppskrift til emning", "gamerule.doLimitedCrafting.description": "Um åsleget kunna leikarar berre emna med upplæste uppskrifter.", "gamerule.doMobLoot": "Frå fengd frå kvìkende", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> til<PERSON> frå kvìkende; gjeld òg fyre galdrekulor.", "gamerule.doMobSpawning": "Skapa kvìkende", "gamerule.doMobSpawning.description": "<PERSON><PERSON> e<PERSON>ar kunna hava serskilde takstrar.", "gamerule.doPatrolSpawning": "Skapa ransbue-kringferder", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON> b<PERSON>", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> til<PERSON> frå ble<PERSON>, som òg gjeld fyre galdrekulor.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON> far<PERSON><PERSON>", "gamerule.doVinesSpread": "Klivevokster breider seg", "gamerule.doVinesSpread.description": "Avgjerer um klivevokstrar breida seg på slump til granneblekker. Påverkar ikkje andre slag vokstrar som gråtande elder krokande klivevokstrar, o.s.fr.", "gamerule.doWardenSpawning": "Skapa vordar", "gamerule.doWeatherCycle": "Lat vedret skifta", "gamerule.drowningDamage": "Tak skade frå drukning", "gamerule.enderPearlsVanishOnDeath": "Ka<PERSON><PERSON> enderperlor kverva nær ein døyr", "gamerule.enderPearlsVanishOnDeath.description": "Um enderperlor kastade av ein leikar kverva nær leikaren døyr.", "gamerule.entitiesWithPassengersCanUsePortals": "Einingar med å<PERSON>ar kann nytta he<PERSON>lìd", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON><PERSON> løyve til at einingar med åsitjarar kann fjerrflytja seg gjenom netherlìd, endelìd og endegat.", "gamerule.fallDamage": "Tak skade frå fall", "gamerule.fireDamage": "Tak skade frå eld", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON> daude leikarar", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON>le kvìkende stansa å vera arge nær leikaren dei ero etter døyr i nærleiken.", "gamerule.freezeDamage": "Tak skade frå frost", "gamerule.globalSoundEvents": "Ålheimleg<PERSON> l<PERSON>ar", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> he<PERSON>, t.d. at ein boss verd skapad, er ljodet høyrt yver alt.", "gamerule.keepInventory": "Haldt på skreppa etter dauden", "gamerule.lavaSourceConversion": "<PERSON><PERSON> verd til raunkjelda", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON><PERSON> rennande raun er umringat av raunkjeldor på tvæ sidor verd det òg ei kjelda.", "gamerule.locatorBar": "Slå på leidarstong fyre leikarar", "gamerule.locatorBar.description": "<PERSON><PERSON><PERSON> å<PERSON>get kjem det ei stong på skjermen som syner i kvat leid andre leikarar halda seg.", "gamerule.logAdminCommands": "Kringkasta stjornar-s<PERSON><PERSON><PERSON><PERSON>d", "gamerule.maxCommandChainLength": "Grensa fyre kjedjelengd i styrebòd", "gamerule.maxCommandChainLength.description": "<PERSON><PERSON><PERSON> kjedjor av styrebòdsblekker og verkende.", "gamerule.maxCommandForkCount": "<PERSON>rensa fyre styre<PERSON><PERSON><PERSON>-saman<PERSON>", "gamerule.maxCommandForkCount.description": "Høgste tal på samanhangar som styrebòd kann nytta som «execute as».", "gamerule.maxEntityCramming": "Grensa fyre stappade e<PERSON>ar", "gamerule.minecartMaxSpeed": "Høgste snarleike på malm<PERSON>gner", "gamerule.minecartMaxSpeed.description": "Høgste snarleike på køyrande malmvogner på land.", "gamerule.mobExplosionDropDecay": "I kvìkende-sprengnader sleppa nokre blekker ikkje allstødt fengdi si", "gamerule.mobExplosionDropDecay.description": "Nokot av sleppet frå blekker brotna av sprengnader valdne av kvìkende kverv i sprengnaden.", "gamerule.mobGriefing": "Løyv brjoting frå kvìkende", "gamerule.naturalRegeneration": "Få att liv", "gamerule.playersNetherPortalCreativeDelay": "Leikardrygjing i netherlìd i skapestòda", "gamerule.playersNetherPortalCreativeDelay.description": "Tid (i tikk) ein leikar i skapestòda må standa i eit netherlìd fyrr heimen skifter.", "gamerule.playersNetherPortalDefaultDelay": "Leikardrygjing i netherlìd utanfyre skapestòda", "gamerule.playersNetherPortalDefaultDelay.description": "Tid (i tikk) ein leikar som ikkje er i skapestòda må standa i eit netherlìd fyrr heimen skifter.", "gamerule.playersSleepingPercentage": "Svevnprosent", "gamerule.playersSleepingPercentage.description": "Hundradlùten av leikarar som må sova fyr’ å hoppa yver notti.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON> kunna br<PERSON><PERSON> blekker", "gamerule.projectilesCanBreakBlocks.description": "Avgjerer um skot koma til å brjota blekker som dei kunna brjota.", "gamerule.randomTickSpeed": "Tilfelleleg tillsnarleike", "gamerule.reducedDebugInfo": "<PERSON><PERSON> l<PERSON>upply<PERSON>ar", "gamerule.reducedDebugInfo.description": "Avgrensar innehaldet på lyskeskjermen.", "gamerule.sendCommandFeedback": "Syn utgjøv fyre styrebòdet", "gamerule.showDeathMessages": "<PERSON><PERSON>", "gamerule.snowAccumulationHeight": "Høgste mengd snjolag på mòrki", "gamerule.snowAccumulationHeight.description": "Den høgsta mengdi snjolag som legg seg på mòrki nær det snjovar.", "gamerule.spawnChunkRadius": "Heimsbit-umkrins ved <PERSON><PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "Tal på heimsbitar som halda seg ladde kring byrjestaden i yverheimen.", "gamerule.spawnRadius": "Heimsbit-umkrins ved uppstòdestad", "gamerule.spawnRadius.description": "St<PERSON>r storleiken på umkvervet kring byrjestaden som leikarar verda skapade i.", "gamerule.spectatorsGenerateChunks": "Lat tilskòdarar skapa landskap", "gamerule.tntExplodes": "Løyv TNT til å verda utløyst og sprengja", "gamerule.tntExplosionDropDecay": "I TNT-sprengnader sleppa nokre blekker ikkje allstødt fengdi si", "gamerule.tntExplosionDropDecay.description": "Nokot av sleppet frå blekker brotna av sprengnader valdne av TNT kverv i sprengnaden.", "gamerule.universalAnger": "Ålmenn argskap", "gamerule.universalAnger.description": "<PERSON><PERSON>le kvìkende ganga til åtak på alle leikarar i nærleiken, ikk<PERSON> berre leikaren som egde deim upp. <PERSON><PERSON><PERSON> best um forgiveDeadPlayers er sleget av.", "gamerule.waterSourceConversion": "Lat vatn skapa kjeldeblokk", "gamerule.waterSourceConversion.description": "<PERSON><PERSON><PERSON> rennande vatn er umringat av vatskjeldor på tvæ sidor verd det òg ei kjelda.", "generator.custom": "Tilmåtad", "generator.customized": "<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "STYRKT", "generator.minecraft.amplified.info": "Merknad: <PERSON><PERSON> fyre moro skuld, krev ei sterk telja.", "generator.minecraft.debug_all_block_states": "Lyskestòda", "generator.minecraft.flat": "<PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON> lende", "generator.minecraft.normal": "<PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Einskilt lende", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "Sender du denne fråsegni stadfester du at upplysingarne du heve uppgjevet er rettuge og heilslege etter det du veit.", "gui.abuseReport.comments": "Utgreiding", "gui.abuseReport.describe": "Ei nøgji utgreiding hjelper oss å gjera ei velgrunna avgjerd.", "gui.abuseReport.discard.content": "Om du fer misser du denne fråsegni og utgreidingi di.\nEr du trygg på at du vil fara?", "gui.abuseReport.discard.discard": "Fara frå og kasta fråsegn", "gui.abuseReport.discard.draft": "Spar som utkast", "gui.abuseReport.discard.return": "Haldt fram med å reida", "gui.abuseReport.discard.title": "Kasta fråsegn og utgreiding?", "gui.abuseReport.draft.content": "<PERSON>il du halda fram med å reida den gjeldande fr<PERSON><PERSON><PERSON>, elder kasta henne og skriva ny?", "gui.abuseReport.draft.discard": "<PERSON><PERSON>", "gui.abuseReport.draft.edit": "Haldt fram med å reida", "gui.abuseReport.draft.quittotitle.content": "Vil du halda fram med å reida, elder kasta henne?", "gui.abuseReport.draft.quittotitle.title": "Du heve eit utkast av ei svallfråsegn som kverv um du fer", "gui.abuseReport.draft.title": "<PERSON><PERSON> utkas<PERSON>?", "gui.abuseReport.error.title": "<PERSON><PERSON> <PERSON>kje senda fråsegni di", "gui.abuseReport.message": "<PERSON><PERSON> såg du den vonda framferdi?\nDetta hjelper oss med å undersøkja saki di.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON> ut um kvat som hende:", "gui.abuseReport.name.comment_box_label": "<PERSON>re<PERSON> ut um kvifor du vil segja ifrå um detta namnet:", "gui.abuseReport.name.reporting": "<PERSON> segjer ifrå um «%s».", "gui.abuseReport.name.title": "Seg ifrå um leikarnamn", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON>r segjer du ifrå um detta?", "gui.abuseReport.read_info": "<PERSON><PERSON>r meir um fråsegn", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Ruseråder elder alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Nokon uppmodar andre til å vera med på ulovleg rusverksemd elder uppmodar mindreårige til å drikka alkohol.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON> utnytting av elder y<PERSON><PERSON><PERSON><PERSON><PERSON> mot born", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> talar um, elder på onnor vis fremjar, <PERSON><PERSON><PERSON><PERSON> framferd som hev’ å gjera med born.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Ærekrenkjing", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Nokon skader umdømet ditt elder umdømet åt ein annan, lyg um å vera ein annan, elder breider usanningar med fyremålet å villeida andre.", "gui.abuseReport.reason.description": "Utgreiding:", "gui.abuseReport.reason.false_reporting": "Usònn frå<PERSON>gn", "gui.abuseReport.reason.generic": "Eg vil segja ifrå um leikaren", "gui.abuseReport.reason.generic.description": "<PERSON><PERSON><PERSON> plågar meg / heve gjort nokot eg ikkje likar.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON> (trak<PERSON><PERSON>) elder mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, åtek elder mobbar deg elder andre. <PERSON><PERSON> gjeld òg um nokon gong på gong freistar å kontakta deg elder andre utan samty<PERSON>, elder legg ut private upplysingar um deg elder andre utan samtykke («doxing»).", "gui.abuseReport.reason.hate_speech": "Hat mot folkehop", "gui.abuseReport.reason.hate_speech.description": "Nokon åtek deg elder ein annan leikar på grunn av trudom, folkeslag, kynsleg leggning, elder di<PERSON><PERSON>.", "gui.abuseReport.reason.imminent_harm": "Trugar med å skada andre", "gui.abuseReport.reason.imminent_harm.description": "Nokon trugar med å skada deg elder ein annan i røyndi.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intime bilæte utan sam<PERSON>kke", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> talar um, brei<PERSON>, elder på onnor vis fremjar, private og intime bilæte.", "gui.abuseReport.reason.self_harm_or_suicide": "Sjølvskading elder s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.self_harm_or_suicide.description": "Nokon trugar med å skada seg sjølv i røyndi, elder talar um å skada seg sjølv i røyndi.", "gui.abuseReport.reason.sexually_inappropriate": "Kynslega uhøvande", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON>ar som spela på kynsleg umgjenge, kyn<PERSON>l, og kynsleg vald.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme elder v<PERSON><PERSON>e", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> talar um, <PERSON><PERSON><PERSON>, elder trugar med å utføra terrorisme elder valdleg ekstremisme av politiske, religi<PERSON><PERSON>, ideologiske, elder andre grun<PERSON>.", "gui.abuseReport.reason.title": "Vel fråsegnbolk", "gui.abuseReport.report_sent_msg": "Me hava teket imot fråsegni di. Takk!\n\nLaget vårt ser på ’nne so snart som råd.", "gui.abuseReport.select_reason": "Vel fråsegnbolk", "gui.abuseReport.send": "Send fråsegn", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON> utgreidingi stuttare", "gui.abuseReport.send.error_message": "Eit lyte ovrade seg då du freistade å senda fråsegni di:\n«%s»", "gui.abuseReport.send.generic_error": "Støytte på eit uventat lyte då du freistade å senda fråsegni di.", "gui.abuseReport.send.http_error": "Eit uventat HTTP-lyte ovrade seg medan du sende fråsegni di.", "gui.abuseReport.send.json_error": "Støytte på vanskapat nyttelass då du skulde senda fråsegni di.", "gui.abuseReport.send.no_reason": "Vel ein fråsegnbolk", "gui.abuseReport.send.not_attested": "Les teksti yver og krossa av i øskja fyr’ å kunna senda fråsegni", "gui.abuseReport.send.service_unavailable": "<PERSON>nde ’kje nå tenesta fyre misbruks-fråsegner. Sjå um du heve samband med internet og freista å nyo.", "gui.abuseReport.sending.title": "Sender fråsegni di...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON><PERSON> send", "gui.abuseReport.skin.title": "Seg ifrå um leikarham", "gui.abuseReport.title": "Seg ifrå um leikar", "gui.abuseReport.type.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.name": "Leikarnamn", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON><PERSON>", "gui.acknowledge": "Stadfest", "gui.advancements": "<PERSON><PERSON><PERSON>", "gui.all": "Alle", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON>r meir med den fylgjande lekken: %s", "gui.banned.description.permanent": "<PERSON><PERSON><PERSON> din er bannlyst fyr’ alltid, som tyder at du ikkje kann spela ånetes elder verda med i Realms.", "gui.banned.description.reason": "Me hava nylega motteket ei fråsegn um dåleg framferd av kontoen din. Umsynsfolki våre hava no sét igjenom sòki og kjent det att som %s, som bryt med samlagsstandardarne til Minecraft.", "gui.banned.description.reason_id": "Kode: %s", "gui.banned.description.reason_id_message": "Kode: %s – %s", "gui.banned.description.temporary": "%s <PERSON>am til då kann du ’kje spela ånetes elder verda med i Realms.", "gui.banned.description.temporary.duration": "Kontoen din er millombìls utestengd og fær tilgjenge att um %s.", "gui.banned.description.unknownreason": "Me hava nylega motteket ei fråsegn um dåleg framferd av kontoen din. Umsynsfolki våre hava no sét igjenom sòki og fundet ut at det bryt med samlagsstandardarne til Minecraft.", "gui.banned.name.description": "Det gjeldande namnet ditt – «%s» – strider imot samlagsstandardarne våre. Du kann spela på serleikar, men må gjera um på namnet ditt um du vil spela ånetes.\n\n<PERSON><PERSON><PERSON> meir elder send sòki til gjenomsyn med den fylgjande lekken: %s", "gui.banned.name.title": "Namnet er ikkje løyvt i samleikar", "gui.banned.reason.defamation_impersonation_false_information": "<PERSON> gjeva seg ut fyr’ å vera ein annan, elder brei<PERSON> upp<PERSON><PERSON>ar fyr’ å utnytta elder ville<PERSON> andre", "gui.banned.reason.drugs": "Tilvising til uloglege ruseråder", "gui.banned.reason.extreme_violence_or_gore": "Skildring av grovt vald elder gòr i rø<PERSON>di", "gui.banned.reason.false_reporting": "<PERSON><PERSON> mengd usanne elder galne fr<PERSON><PERSON><PERSON>r", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON><PERSON> tileigning elder n<PERSON><PERSON> av inn<PERSON>ald", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON>dardarne", "gui.banned.reason.harassment_or_bullying": "Krenkjande ordbruk på ei utpeikande og sårande vis", "gui.banned.reason.hate_speech": "<PERSON><PERSON><PERSON> utsegner elder man<PERSON><PERSON><PERSON><PERSON>", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON> til hat<PERSON>, <PERSON><PERSON><PERSON>, elder <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> menne", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON>ve som fyremål å skada folk elder eigedom i røyndi", "gui.banned.reason.nudity_or_pornography": "<PERSON><PERSON><PERSON> elder pornografiskt emne", "gui.banned.reason.sexually_inappropriate": "Kynslege emne elder innehald", "gui.banned.reason.spam_or_advertising": "Bòspost elder lysing (reklame)", "gui.banned.skin.description": "Den gjeldande hamen din strider imot samlagsstandardarne våre. Du kan enno spela med ein fyrevalsham, elder velja ein ny.\n\n<PERSON><PERSON><PERSON> meir elder send s<PERSON>ki til gjenomsyn med den fylgjande lekken: %s", "gui.banned.skin.title": "<PERSON>en er ikkje løyvd", "gui.banned.title.permanent": "<PERSON><PERSON><PERSON> din er bannlyst fyr’ alltid", "gui.banned.title.temporary": "<PERSON><PERSON><PERSON> din er millombìls utestengd", "gui.cancel": "Brjot av", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Ei nøgji utgreiding hjelper oss å gjera ei velgrunna avgjerd.", "gui.chatReport.discard.content": "Om du fer misser du denne fråsegni og utgreidingi di.\nEr du trygg på at du vil fara?", "gui.chatReport.discard.discard": "Fara frå og kasta fråsegn", "gui.chatReport.discard.draft": "Spar som utkast", "gui.chatReport.discard.return": "Haldt fram med å reida", "gui.chatReport.discard.title": "Kasta fråsegn og utgreiding?", "gui.chatReport.draft.content": "<PERSON>il du halda fram med å reida den gjeldande fr<PERSON><PERSON><PERSON>, elder kasta henne og skriva ny?", "gui.chatReport.draft.discard": "<PERSON><PERSON>", "gui.chatReport.draft.edit": "Haldt fram med å reida", "gui.chatReport.draft.quittotitle.content": "Vil du halda fram med å reida, elder kasta henne?", "gui.chatReport.draft.quittotitle.title": "Du heve eit utkast av ei svallfråsegn som kverv um du fer", "gui.chatReport.draft.title": "<PERSON><PERSON> utkas<PERSON>?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON> ut um kvat som hende:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON>r segjer du ifrå um detta?", "gui.chatReport.read_info": "<PERSON>ær um fråsegn", "gui.chatReport.report_sent_msg": "Me hava motteket fråsegni di. Takk!\n\nLaget vårt skal sjå på ’nne so snart som råd.", "gui.chatReport.select_chat": "<PERSON>el svallbòd å segja ifrå um", "gui.chatReport.select_reason": "Vel fråsegnbolk", "gui.chatReport.selected_chat": "%s svallbòd valde til fråsegn", "gui.chatReport.send": "Send fråsegn", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> utgreidingi stuttare", "gui.chatReport.send.no_reason": "Vel ein fråsegnbolk", "gui.chatReport.send.no_reported_messages": "Vel minst eitt svallbòd å segja ifrå um", "gui.chatReport.send.too_many_messages": "For mange bòd er tekne med i fråsegni", "gui.chatReport.title": "Seg ifrå um leikarsvall", "gui.chatSelection.context": "<PERSON><PERSON>d fyrr og etter dei valde kjem med i fråsegni fyr’ å gjeva betre innsyn i samanhangen", "gui.chatSelection.fold": "%s bòd lø<PERSON>t/-de", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s vardt med i svallet", "gui.chatSelection.message.narrate": "%s sagde: %s kl. %s", "gui.chatSelection.selected": "%s/%s bòd valde", "gui.chatSelection.title": "<PERSON>el svallbòd å segja ifrå um", "gui.continue": "Haldt fram", "gui.copy_link_to_clipboard": "Rita av lekk", "gui.days": "%s dag(ar)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON>", "gui.entity_tooltip.type": "Slag: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Vandade %s skiln", "gui.fileDropFailure.title": "Kunde inkje leggja til skiln", "gui.hours": "%s time/-ar", "gui.loadingMinecraft": "Lader inn Minecraft", "gui.minutes": "%s minutt", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s-knapp", "gui.narrate.editBox": "Brigdeteig %s: %s", "gui.narrate.slider": "%s-glidebrjotar", "gui.narrate.tab": "%s-flìpe", "gui.no": "<PERSON><PERSON>", "gui.none": "Ingen", "gui.ok": "<PERSON><PERSON><PERSON><PERSON>", "gui.open_report_dir": "Opna falden med rapportar", "gui.proceed": "Haldt fram", "gui.recipebook.moreRecipes": "Høgreklikka fyre meire", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "<PERSON><PERSON>...", "gui.recipebook.toggleRecipes.all": "Syner alt", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON> smeltelege", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON> em<PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON> smeltelege", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON>", "gui.report_to_server": "<PERSON>d frå til tenar", "gui.socialInteractions.blocking_hint": "Handsama med Microsoft-konto", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON> bægde leikarar i svall", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> løynde leika<PERSON> i svall", "gui.socialInteractions.hidden_in_chat": "Svallbòd frå %s verda løynde", "gui.socialInteractions.hide": "Løyn i svall", "gui.socialInteractions.narration.hide": "<PERSON><PERSON><PERSON> bòd frå %s", "gui.socialInteractions.narration.report": "Seg ifrå um leikaren %s", "gui.socialInteractions.narration.show": "Syn bòd frå %s", "gui.socialInteractions.report": "<PERSON><PERSON> if<PERSON>", "gui.socialInteractions.search_empty": "Finn ingen leikar med det namnet", "gui.socialInteractions.search_hint": "<PERSON><PERSON>...", "gui.socialInteractions.server_label.multiple": "%s – %s leikarar", "gui.socialInteractions.server_label.single": "%s – %s leikar", "gui.socialInteractions.show": "Syn i svall", "gui.socialInteractions.shown_in_chat": "Svallbòd frå %s verda synte", "gui.socialInteractions.status_blocked": "Bægd", "gui.socialInteractions.status_blocked_offline": "Bægd – fråkoplad", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Løynd – fråkoplad", "gui.socialInteractions.status_offline": "Fråkoplad", "gui.socialInteractions.tab_all": "Alle", "gui.socialInteractions.tab_blocked": "Bægde", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Samkvæme", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> b<PERSON>", "gui.socialInteractions.tooltip.report": "Seg ifrå um leikar", "gui.socialInteractions.tooltip.report.disabled": "Fråsegn-tenesta er utilgjengd", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON><PERSON> bòd du kann segja ifrå um frå leikaren %s", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON> kann ’kje segja ifrå um denne le<PERSON>, av di bòdi hans/henna<PERSON> ikkje kann verda sannkjende på denne tenaren", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> bòd", "gui.stats": "Statistikk", "gui.toMenu": "Atter til tenarlista", "gui.toRealms": "Atter åt Realms-lista", "gui.toTitle": "Atter til hovudskrå", "gui.toWorld": "<PERSON><PERSON> <PERSON>t he<PERSON>a", "gui.togglable_slot": "Klikka fyr’ å slå av òp", "gui.up": "Upp", "gui.waitingForResponse.button.inactive": "Atter (%ss)", "gui.waitingForResponse.title": "<PERSON><PERSON><PERSON> på tenar", "gui.yes": "<PERSON>a", "hanging_sign.edit": "<PERSON><PERSON><PERSON> bòd på hangande skilt", "instrument.minecraft.admire_goat_horn": "Ovundring", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Grunding", "instrument.minecraft.seek_goat_horn": "Leit", "instrument.minecraft.sing_goat_horn": "Song", "instrument.minecraft.yearn_goat_horn": "Trå", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON> ting", "inventory.hotbarInfo": "Spar tøleròd med %1$s+%2$s", "inventory.hotbarSaved": "Tøleròd spard (heimta upp att med %1$s+%2$s)", "item.canBreak": "Kann brotna:", "item.canPlace": "<PERSON>nn setjast ned på:", "item.canUse.unknown": "Ukjend", "item.color": "Lìt: %s", "item.components": "%s komponent(ar)", "item.disabled": "Av<PERSON>gen ting", "item.durability": "Varing: %s / %s", "item.dyed": "Lì<PERSON>", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "Akasiebåt med kista", "item.minecraft.allay_spawn_egg": "Kveikjeegg fyre hjelpeònd", "item.minecraft.amethyst_shard": "Ametystbrot", "item.minecraft.angler_pottery_shard": "Skålbrot med fiskestong", "item.minecraft.angler_pottery_sherd": "Skålbrot med fiskestong", "item.minecraft.apple": "Eple", "item.minecraft.archer_pottery_shard": "Skålbrot med bogeskyttar", "item.minecraft.archer_pottery_sherd": "Skålbrot med bogeskyttar", "item.minecraft.armadillo_scute": "Beltedyrskjold", "item.minecraft.armadillo_spawn_egg": "K<PERSON>ik<PERSON>gg fyre beltedyr", "item.minecraft.armor_stand": "Brynjestod", "item.minecraft.arms_up_pottery_shard": "Skålbrot med armar i vedret", "item.minecraft.arms_up_pottery_sherd": "Skålbrot med armar i vedret", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Spann med aksolotl", "item.minecraft.axolotl_spawn_egg": "Kveikjeegg fyr’ aksolotl", "item.minecraft.baked_potato": "<PERSON><PERSON> j<PERSON>", "item.minecraft.bamboo_chest_raft": "Bambusflòte med kista", "item.minecraft.bamboo_raft": "Bambusflòte", "item.minecraft.bat_spawn_egg": "Kveikjeegg fyre skjåvengja", "item.minecraft.bee_spawn_egg": "Kveikjeegg fyre bia", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Bjørkarb<PERSON>t", "item.minecraft.birch_chest_boat": "Bjørkarbåt med kista", "item.minecraft.black_bundle": "Svart sekk", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON> l<PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> se<PERSON>", "item.minecraft.blade_pottery_shard": "Skålbrot med sverd", "item.minecraft.blade_pottery_sherd": "Skålbrot med sverd", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "Logestav", "item.minecraft.blaze_spawn_egg": "Kveikjeegg fyre loge", "item.minecraft.blue_bundle": "Blå sekk", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>tt <PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>tt egg", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre myrgrind", "item.minecraft.bolt_armor_trim_smithing_template": "Smideskant", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt-br<PERSON>jep<PERSON>d", "item.minecraft.bone": "Bein", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "Bok", "item.minecraft.bordure_indented_banner_pattern": "Fanemynster fyre bord", "item.minecraft.bow": "Boge", "item.minecraft.bowl": "Sk<PERSON>l", "item.minecraft.bread": "<PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "Flogestav", "item.minecraft.breeze_spawn_egg": "Kveikjeegg fyre floga", "item.minecraft.brewer_pottery_shard": "Skålbrot med brygg", "item.minecraft.brewer_pottery_sherd": "Skålbrot med brygg", "item.minecraft.brewing_stand": "Bryggjereidskap", "item.minecraft.brick": "Tigl", "item.minecraft.brown_bundle": "<PERSON><PERSON> sekk", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "Brunt egg", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON>", "item.minecraft.bucket": "<PERSON>nn", "item.minecraft.bundle": "Sekk", "item.minecraft.bundle.empty": "<PERSON>", "item.minecraft.bundle.empty.description": "Kann halda eit blandat lad av ting", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Skålbrot med eldtunga", "item.minecraft.burn_pottery_sherd": "Skålbrot med eldtunga", "item.minecraft.camel_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyr’ <PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> på stong", "item.minecraft.cat_spawn_egg": "Kveik<PERSON>gg fyre katt", "item.minecraft.cauldron": "G<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Kveikjeegg fyr’ hòlekongurvåva", "item.minecraft.chainmail_boots": "Ringbrynjeskor", "item.minecraft.chainmail_chestplate": "Ringbrynja", "item.minecraft.chainmail_helmet": "Ring<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>ò<PERSON>", "item.minecraft.charcoal": "Trekol", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "Kisseberbåt med kista", "item.minecraft.chest_minecart": "Malmvogn med kista", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Kveik<PERSON>gg fyr’ høna", "item.minecraft.chorus_fruit": "Koralda", "item.minecraft.clay_ball": "Leirball", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Smideskant", "item.minecraft.coast_armor_trim_smithing_template.new": "Strand-brynjepryd", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON>rsk", "item.minecraft.cod_bucket": "Torskespann", "item.minecraft.cod_spawn_egg": "Kveik<PERSON>gg fyre torsk", "item.minecraft.command_block_minecart": "Malmvogn med styrebòdsblokk", "item.minecraft.compass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Uksesteik", "item.minecraft.cooked_chicken": "Steikt høna", "item.minecraft.cooked_cod": "Steikt torsk", "item.minecraft.cooked_mutton": "Saudesteik", "item.minecraft.cooked_porkchop": "Svinesteik", "item.minecraft.cooked_rabbit": "St<PERSON><PERSON> ha<PERSON>", "item.minecraft.cooked_salmon": "Steikt laks", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Koparstykke", "item.minecraft.cow_spawn_egg": "K<PERSON><PERSON><PERSON>gg fyre naut", "item.minecraft.creaking_spawn_egg": "Kveikjeegg fyre knerk", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Fanemynster fyre creeper", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre creeper", "item.minecraft.crossbow": "Låsboge", "item.minecraft.crossbow.projectile": "Skot:", "item.minecraft.crossbow.projectile.multiple": "Skot: %s x %s", "item.minecraft.crossbow.projectile.single": "Skot: %s", "item.minecraft.cyan_bundle": "Blågrøn sekk", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Sk<PERSON><PERSON>brot med creeper", "item.minecraft.danger_pottery_sherd": "Sk<PERSON><PERSON>brot med creeper", "item.minecraft.dark_oak_boat": "Myrkeikebåt", "item.minecraft.dark_oak_chest_boat": "Myrkeikebåt med kista", "item.minecraft.debug_stick": "Lyskepinne", "item.minecraft.debug_stick.empty": "%s hev’ ingi gjerder", "item.minecraft.debug_stick.select": "valde «%s» (%s)", "item.minecraft.debug_stick.update": "«%s» til %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Demantstyvlar", "item.minecraft.diamond_chestplate": "Demantbrynja", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Demantbrynja til øyk", "item.minecraft.diamond_leggings": "Demantbrok", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Demantsverd", "item.minecraft.disc_fragment_5": "Skivebrot", "item.minecraft.disc_fragment_5.desc": "Skiva – 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre tumlar", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr’ asne", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON>d tare", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre sjodraug", "item.minecraft.dune_armor_trim_smithing_template": "Smideskant", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.echo_shard": "Atterljomsbrot", "item.minecraft.egg": "Egg", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr<PERSON> eldre verjar", "item.minecraft.elytra": "Skalvenger", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Runebok", "item.minecraft.enchanted_golden_apple": "Trollbundet gulleple", "item.minecraft.end_crystal": "Endedvergsmide", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr’ end<PERSON>rake", "item.minecraft.ender_eye": "Enderauga", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr<PERSON> endermann", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr’ endermit", "item.minecraft.evoker_spawn_egg": "Kveik<PERSON>gg fyr’ andevekkjar", "item.minecraft.experience_bottle": "Galdreflaska", "item.minecraft.explorer_pottery_shard": "Skålbrot med kort", "item.minecraft.explorer_pottery_sherd": "Skålbrot med kort", "item.minecraft.eye_armor_trim_smithing_template": "Smideskant", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON>et auga av kongurvåva", "item.minecraft.field_masoned_banner_pattern": "Fanemynster fyre tigl", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "Eldlading", "item.minecraft.firework_rocket": "Fyrverk", "item.minecraft.firework_rocket.flight": "Flogtid:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Fyrverksstjørna", "item.minecraft.firework_star.black": "Svòrt", "item.minecraft.firework_star.blue": "Blå", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Tilmåtad", "item.minecraft.firework_star.cyan": "Blågrøn", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.flicker": "Gnistring", "item.minecraft.firework_star.gray": "Grå", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Ljosblå", "item.minecraft.firework_star.light_gray": "Ljosgrå", "item.minecraft.firework_star.lime": "Ljosgrøn", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Brandgul", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "Ukjend form", "item.minecraft.firework_star.shape.burst": "Sprengjing", "item.minecraft.firework_star.shape.creeper": "Creeperformad", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> kula", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON> k<PERSON>", "item.minecraft.firework_star.shape.star": "Stjørneformad", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Gul", "item.minecraft.fishing_rod": "Fiskestong", "item.minecraft.flint": "<PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "Smideskant", "item.minecraft.flow_armor_trim_smithing_template.new": "Kvervel-brynjepryd", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "Fanemynster fyre kvervel", "item.minecraft.flow_pottery_sherd": "Skålbrot med kvervel", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "Blom", "item.minecraft.flower_banner_pattern.new": "Fanemynster fyre blom", "item.minecraft.flower_pot": "Blompotta", "item.minecraft.fox_spawn_egg": "K<PERSON><PERSON><PERSON>gg fyre rev", "item.minecraft.friend_pottery_shard": "Skålbrot med vìnsamt andlìt", "item.minecraft.friend_pottery_sherd": "Skålbrot med vìnsamt andlìt", "item.minecraft.frog_spawn_egg": "Kveikjeegg fyre frosk", "item.minecraft.furnace_minecart": "Malmvogn med omn", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre ghast", "item.minecraft.ghast_tear": "Ghasttår", "item.minecraft.glass_bottle": "Glasflaska", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Fanemynster fyre klote", "item.minecraft.glow_berries": "Gl<PERSON><PERSON>ber", "item.minecraft.glow_ink_sac": "Glødeblèksekk", "item.minecraft.glow_item_frame": "Glødande råma", "item.minecraft.glow_squid_spawn_egg": "Kveikjeegg fyre glødespruta", "item.minecraft.glowstone_dust": "Glødesteinsdumba", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Kveikjeegg fyre geit", "item.minecraft.gold_ingot": "Gullstykke", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Gullstyvlar", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Gullbrynja", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Gullbrynja til øyk", "item.minecraft.golden_leggings": "Gullbrok", "item.minecraft.golden_pickaxe": "Gullhakka", "item.minecraft.golden_shovel": "Gullspade", "item.minecraft.golden_sword": "Gullsverd", "item.minecraft.gray_bundle": "Grå sekk", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> sekk", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre verjar", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "Floga", "item.minecraft.guster_banner_pattern.new": "Fanemynster fyre floga", "item.minecraft.guster_pottery_sherd": "Skålbrot med floga", "item.minecraft.happy_ghast_spawn_egg": "Kveikjeegg fyre gladvært ghast", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON>t havet", "item.minecraft.heart_pottery_shard": "Skålbrot med hjarta", "item.minecraft.heart_pottery_sherd": "Skålbrot med hjarta", "item.minecraft.heartbreak_pottery_shard": "Skålbrot med brotet hjarta", "item.minecraft.heartbreak_pottery_sherd": "Skålbrot med brotet hjarta", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr’ hoglin", "item.minecraft.honey_bottle": "Huningflaska", "item.minecraft.honeycomb": "Vakskaka", "item.minecraft.hopper_minecart": "Malmvogn med trekt", "item.minecraft.horse_spawn_egg": "Kveikjeegg fyr’ øyk", "item.minecraft.host_armor_trim_smithing_template": "Smideskant", "item.minecraft.host_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Skålbrot med ulv", "item.minecraft.howl_pottery_sherd": "Skålbrot med ulv", "item.minecraft.husk_spawn_egg": "Kveikjeegg fyre skolm", "item.minecraft.ink_sac": "Blèksekk", "item.minecraft.iron_axe": "J<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Jarnstyvlar", "item.minecraft.iron_chestplate": "Jarnbrynja", "item.minecraft.iron_golem_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre jarnkall", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Jarnbrynja til øyk", "item.minecraft.iron_ingot": "Jarnstykke", "item.minecraft.iron_leggings": "Jarnbrok", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Jarnhakka", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Jarnsverd", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Regnskogsbåt", "item.minecraft.jungle_chest_boat": "Regnskogsbåt med kista", "item.minecraft.knowledge_book": "Kunnebok", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Raunspann", "item.minecraft.lead": "Band", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "Lederstyvlar", "item.minecraft.leather_chestplate": "Ledertrøya", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Lederbrynja til øyk", "item.minecraft.leather_leggings": "Lederbrok", "item.minecraft.light_blue_bundle": "Ljosblå sekk", "item.minecraft.light_blue_dye": "Ljosblått l<PERSON>", "item.minecraft.light_blue_harness": "Ljosblått selety", "item.minecraft.light_gray_bundle": "Ljosgrå sekk", "item.minecraft.light_gray_dye": "Ljosgr<PERSON><PERSON>", "item.minecraft.light_gray_harness": "Ljosgr<PERSON><PERSON>", "item.minecraft.lime_bundle": "Ljosgrøn sekk", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> br<PERSON>gg", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> br<PERSON>gg", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON>rygg", "item.minecraft.lingering_potion.effect.harming": "Dveljande skadebrygg", "item.minecraft.lingering_potion.effect.healing": "Dveljan<PERSON> læ<PERSON>", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.leaping": "Dveljande by<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.levitation": "Dveljande sviveb<PERSON>gg", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.mundane": "Kvardagslegt dveljande brygg", "item.minecraft.lingering_potion.effect.night_vision": "Dveljan<PERSON> na<PERSON><PERSON>gg", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Dveljan<PERSON> atterskapebrygg", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.slowness": "Dveljande trå<PERSON>brygg", "item.minecraft.lingering_potion.effect.strength": "Dveljan<PERSON> s<PERSON>rk<PERSON>gg", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.thick": "Dveljande tjukt brygg", "item.minecraft.lingering_potion.effect.turtle_master": "Dveljebrygg frå skjelpoddemeistaren", "item.minecraft.lingering_potion.effect.water": "Dveljande vatsflaska", "item.minecraft.lingering_potion.effect.water_breathing": "Dveljande vatsandebrygg", "item.minecraft.lingering_potion.effect.weakness": "Dveljan<PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre lama", "item.minecraft.lodestone_compass": "Kompås fyre le<PERSON>", "item.minecraft.mace": "Stridsklubba", "item.minecraft.magenta_bundle": "Ljosblåraud sekk", "item.minecraft.magenta_dye": "Ljosblåraudt lìtemne", "item.minecraft.magenta_harness": "Ljosblåraudt selety", "item.minecraft.magma_cream": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre raunkubb", "item.minecraft.mangrove_boat": "Mangrovebåt", "item.minecraft.mangrove_chest_boat": "Mangrovebåt med kista", "item.minecraft.map": "<PERSON><PERSON> kort", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "Mjølkespann", "item.minecraft.minecart": "Malmvogn", "item.minecraft.miner_pottery_shard": "Skålbrot med hakka", "item.minecraft.miner_pottery_sherd": "Skålbrot med hakka", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "<PERSON>g", "item.minecraft.mojang_banner_pattern.new": "Fanemynster fyre ting", "item.minecraft.mooshroom_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyre mooshroom", "item.minecraft.mourner_pottery_shard": "Skålbrot med syrgjande", "item.minecraft.mourner_pottery_sherd": "Skålbrot med syrgjande", "item.minecraft.mule_spawn_egg": "Kveikjeegg fyre muldyr", "item.minecraft.mushroom_stew": "Soppg<PERSON><PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 – 11", "item.minecraft.music_disc_13": "<PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 – 13", "item.minecraft.music_disc_5": "<PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> – 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 – blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 – cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 – chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> – <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> – C<PERSON> (speldåsa)", "item.minecraft.music_disc_far": "<PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 – far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions – Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON>", "item.minecraft.music_disc_mall.desc": "C418 – mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 – mellohi", "item.minecraft.music_disc_otherside": "<PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> – <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> – Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 – stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 – strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 – wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 – ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Namneskilt", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON><PERSON>tkuvung", "item.minecraft.nether_brick": "Nethertigl", "item.minecraft.nether_star": "Netherstjørna", "item.minecraft.nether_wart": "Nethervòrta", "item.minecraft.netherite_axe": "Netherittøks", "item.minecraft.netherite_boots": "Netherittstyvlar", "item.minecraft.netherite_chestplate": "Netherittbrynja", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Netherittgrev", "item.minecraft.netherite_ingot": "Netherittstykke", "item.minecraft.netherite_leggings": "Netherittbrok", "item.minecraft.netherite_pickaxe": "Netheritthakka", "item.minecraft.netherite_scrap": "Netherittskrap", "item.minecraft.netherite_shovel": "Netherittspade", "item.minecraft.netherite_sword": "Netherittsverd", "item.minecraft.netherite_upgrade_smithing_template": "Smideskant", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherittbetring", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "Eikebåt med kista", "item.minecraft.ocelot_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyr’ ocelott", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON> flaska", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "item.minecraft.orange_bundle": "Brandgul sekk", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON> lì<PERSON>ne", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON> selety", "item.minecraft.painting": "Målarstykke", "item.minecraft.pale_oak_boat": "Bleikeikebå<PERSON>", "item.minecraft.pale_oak_chest_boat": "Bleikeikebåt med kista", "item.minecraft.panda_spawn_egg": "Kveikjeegg fyre panda<PERSON>j<PERSON><PERSON>", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Kveikjeegg fyre pavegauk", "item.minecraft.phantom_membrane": "Tankefosterskjå", "item.minecraft.phantom_spawn_egg": "Kveikjeegg fyre tankefoster", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre svin", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Fanemynster fyre tryne", "item.minecraft.piglin_brute_spawn_egg": "Kveik<PERSON>gg fyre pig<PERSON>", "item.minecraft.piglin_spawn_egg": "Kveik<PERSON>gg fyre piglin", "item.minecraft.pillager_spawn_egg": "Kveikjeegg fyre ransbue", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON><PERSON> sekk", "item.minecraft.pink_dye": "Ljosra<PERSON><PERSON> lì<PERSON>", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.plenty_pottery_shard": "Skålbrot med kista", "item.minecraft.plenty_pottery_sherd": "Skålbrot med kista", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON>le", "item.minecraft.polar_bear_spawn_egg": "Kveikjeegg fyre kvitebjørn", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>da", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "Jorde<PERSON>", "item.minecraft.potion": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> brygg", "item.minecraft.potion.effect.empty": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>gg", "item.minecraft.potion.effect.fire_resistance": "Eldvernsbrygg", "item.minecraft.potion.effect.harming": "Ska<PERSON><PERSON><PERSON>gg", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "Svivebrygg", "item.minecraft.potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "Kvardagslegt brygg", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON>terska<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "Styrkjebrygg", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.thick": "Tjukt brygg", "item.minecraft.potion.effect.turtle_master": "Brygg frå skjelpoddemeistaren", "item.minecraft.potion.effect.water": "Vatsflaska", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_archer": "Skålbrot med bogeskyttar", "item.minecraft.pottery_shard_arms_up": "Skålbrot med armar i vedret", "item.minecraft.pottery_shard_prize": "Skålbrot med glimestein", "item.minecraft.pottery_shard_skull": "Skålbrot med skalle", "item.minecraft.powder_snow_bucket": "Mjøllspann", "item.minecraft.prismarine_crystals": "Prismarindvergsmide", "item.minecraft.prismarine_shard": "Prismarinbrot", "item.minecraft.prize_pottery_shard": "Skålbrot med glimestein", "item.minecraft.prize_pottery_sherd": "Skålbrot med glimestein", "item.minecraft.pufferfish": "Igulfisk", "item.minecraft.pufferfish_bucket": "Spann med igulfisk", "item.minecraft.pufferfish_spawn_egg": "Kveikjeegg fyre igulfisk", "item.minecraft.pumpkin_pie": "Graskjerpai", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "Blåraud sekk", "item.minecraft.purple_dye": "Blåraudt lìtemne", "item.minecraft.purple_harness": "Blåraudt selety", "item.minecraft.quartz": "Nether<PERSON>tstein", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre h<PERSON>e", "item.minecraft.rabbit_stew": "Hjaseg<PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Smideskant", "item.minecraft.raiser_armor_trim_smithing_template.new": "Lyftar-brynjepryd", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyr’ herjar", "item.minecraft.raw_copper": "Råkopar", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Kompås til atterfinning", "item.minecraft.red_bundle": "<PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Redstonedumba", "item.minecraft.resin_brick": "Kvådetigl", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Smideskant", "item.minecraft.rib_armor_trim_smithing_template.new": "Rivbeins-brynjepryd", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON> k<PERSON>t", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "Rå laks", "item.minecraft.salmon_bucket": "Spann med laks", "item.minecraft.salmon_spawn_egg": "Kveikjeegg fyre laks", "item.minecraft.scrape_pottery_sherd": "Skålbrot med øks", "item.minecraft.scute": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Smideskant", "item.minecraft.sentry_armor_trim_smithing_template.new": "Vaktar-brynjepryd", "item.minecraft.shaper_armor_trim_smithing_template": "Smideskant", "item.minecraft.shaper_armor_trim_smithing_template.new": "Formar-brynjepryd", "item.minecraft.sheaf_pottery_shard": "Skålbrot med kveite", "item.minecraft.sheaf_pottery_sherd": "Skålbrot med kveite", "item.minecraft.shears": "Sòks", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre saud", "item.minecraft.shelter_pottery_shard": "Skålbrot med tre", "item.minecraft.shelter_pottery_sherd": "Skålbrot med tre", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.blue": "Blå skjold", "item.minecraft.shield.brown": "<PERSON><PERSON> skjold", "item.minecraft.shield.cyan": "Blågrøn skjold", "item.minecraft.shield.gray": "Grå skjold", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.light_blue": "Ljosblå skjold", "item.minecraft.shield.light_gray": "Ljosgrå skjold", "item.minecraft.shield.lime": "Ljosgrøn skjold", "item.minecraft.shield.magenta": "Ljosblå<PERSON><PERSON> skjold", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON>jold", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.red": "<PERSON><PERSON>jo<PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>jold", "item.minecraft.shield.yellow": "<PERSON><PERSON> skjold", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre shulker", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Smideskant", "item.minecraft.silence_armor_trim_smithing_template.new": "Togn-brynjep<PERSON>d", "item.minecraft.silverfish_spawn_egg": "Kveikjeegg fyre sylvkrek", "item.minecraft.skeleton_horse_spawn_egg": "Kveikjeegg fyr’ øykjebeingrind", "item.minecraft.skeleton_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyre beingrind", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Fanemynster fyre skalle", "item.minecraft.skull_pottery_shard": "Skålbrot med skalle", "item.minecraft.skull_pottery_sherd": "Skålbrot med skalle", "item.minecraft.slime_ball": "Slimball", "item.minecraft.slime_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyre sliming", "item.minecraft.smithing_template": "Smideskant", "item.minecraft.smithing_template.applies_to": "Verd nyttat på:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Legg til malmstykke elder dvergsmide", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Legg til brynjestykke", "item.minecraft.smithing_template.armor_trim.ingredients": "Malmstykke og dvergsmide", "item.minecraft.smithing_template.ingredients": "Innehald:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Legg til netherittstykke", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Demantreidnad", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON> til bryn<PERSON>, våpn elder ambòd av demant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherittstykke", "item.minecraft.smithing_template.upgrade": "Betring: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre tevar", "item.minecraft.snort_pottery_shard": "Skålbrot med moldnase", "item.minecraft.snort_pottery_sherd": "Skålbrot med moldnase", "item.minecraft.snout_armor_trim_smithing_template": "Smideskant", "item.minecraft.snout_armor_trim_smithing_template.new": "Tryne-brynjepryd", "item.minecraft.snow_golem_spawn_egg": "Kveikjeegg fyre snjokall", "item.minecraft.snowball": "Snjoball", "item.minecraft.spectral_arrow": "Merkjekolv", "item.minecraft.spider_eye": "Auga av kongurvåva", "item.minecraft.spider_spawn_egg": "Kveikjeegg fyre kongurvåva", "item.minecraft.spire_armor_trim_smithing_template": "Smideskant", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "Ka<PERSON><PERSON><PERSON> ink<PERSON>-<PERSON><PERSON><PERSON> br<PERSON>gg", "item.minecraft.splash_potion.effect.fire_resistance": "Kastelegt eldvernsbrygg", "item.minecraft.splash_potion.effect.harming": "Kastelegt skadebrygg", "item.minecraft.splash_potion.effect.healing": "Kastelegt lækjebrygg", "item.minecraft.splash_potion.effect.infested": "Kastelegt fengjebrygg", "item.minecraft.splash_potion.effect.invisibility": "Kastelegt usynleikebrygg", "item.minecraft.splash_potion.effect.leaping": "Kastelegt byksebrygg", "item.minecraft.splash_potion.effect.levitation": "Kastelegt svivebrygg", "item.minecraft.splash_potion.effect.luck": "Kaste<PERSON><PERSON>gg", "item.minecraft.splash_potion.effect.mundane": "Kvardag<PERSON><PERSON> ka<PERSON>", "item.minecraft.splash_potion.effect.night_vision": "Kastelegt nattarsynsbrygg", "item.minecraft.splash_potion.effect.oozing": "Kasteleg<PERSON> tytebrygg", "item.minecraft.splash_potion.effect.poison": "Kastelegt eiterbrygg", "item.minecraft.splash_potion.effect.regeneration": "Kastelegt atterskapebrygg", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> se<PERSON><PERSON>", "item.minecraft.splash_potion.effect.slowness": "Kastelegt tråleikebrygg", "item.minecraft.splash_potion.effect.strength": "Kastelegt styrkjebrygg", "item.minecraft.splash_potion.effect.swiftness": "Ka<PERSON><PERSON><PERSON> snarl<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Kastebrygg frå skjelpoddemeistaren", "item.minecraft.splash_potion.effect.water": "Kasteleg vatsflaska", "item.minecraft.splash_potion.effect.water_breathing": "Kastelegt vatsandebrygg", "item.minecraft.splash_potion.effect.weakness": "Kastelegt veikjebrygg", "item.minecraft.splash_potion.effect.weaving": "Kastelegt vevebrygg", "item.minecraft.splash_potion.effect.wind_charged": "Kastelegt vindladebrygg", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "Granbåt med kista", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "K<PERSON>ik<PERSON>gg fyre spruta", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "Steinhak<PERSON>", "item.minecraft.stone_shovel": "Steinspade", "item.minecraft.stone_sword": "Steinsverd", "item.minecraft.stray_spawn_egg": "Kveikjeegg fyre villegrind", "item.minecraft.strider_spawn_egg": "Kveik<PERSON>gg fyre stìgar", "item.minecraft.string": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "Tvilsam gry<PERSON>ett", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_bucket": "Rovetrollspann", "item.minecraft.tadpole_spawn_egg": "Kveikjeegg fyre rovetroll", "item.minecraft.tide_armor_trim_smithing_template": "Smideskant", "item.minecraft.tide_armor_trim_smithing_template.new": "Tidvats-brynjepryd", "item.minecraft.tipped_arrow": "Duvad kolv", "item.minecraft.tipped_arrow.effect.awkward": "Duvad kolv", "item.minecraft.tipped_arrow.effect.empty": "Ikk<PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>d kolv", "item.minecraft.tipped_arrow.effect.fire_resistance": "Eldvernskolv", "item.minecraft.tipped_arrow.effect.harming": "Skadekolv", "item.minecraft.tipped_arrow.effect.healing": "Lækjekolv", "item.minecraft.tipped_arrow.effect.infested": "Fengjekolv", "item.minecraft.tipped_arrow.effect.invisibility": "Usynleikekolv", "item.minecraft.tipped_arrow.effect.leaping": "Byksekolv", "item.minecraft.tipped_arrow.effect.levitation": "Svivekolv", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON>kolv", "item.minecraft.tipped_arrow.effect.mundane": "Duvad kolv", "item.minecraft.tipped_arrow.effect.night_vision": "Nattarsynskolv", "item.minecraft.tipped_arrow.effect.oozing": "Tytekolv", "item.minecraft.tipped_arrow.effect.poison": "Eiterkolv", "item.minecraft.tipped_arrow.effect.regeneration": "Atterskapekolv", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON>falls<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.slowness": "Tråleikekolv", "item.minecraft.tipped_arrow.effect.strength": "Styrkjekolv", "item.minecraft.tipped_arrow.effect.swiftness": "Snarleikekolv", "item.minecraft.tipped_arrow.effect.thick": "Duvad kolv", "item.minecraft.tipped_arrow.effect.turtle_master": "Pil frå skjelpoddemeistaren", "item.minecraft.tipped_arrow.effect.water": "Pil med kasteleg-verknad", "item.minecraft.tipped_arrow.effect.water_breathing": "Vatsandekolv", "item.minecraft.tipped_arrow.effect.weakness": "Veikjekolv", "item.minecraft.tipped_arrow.effect.weaving": "Vevekolv", "item.minecraft.tipped_arrow.effect.wind_charged": "Vindladekolv", "item.minecraft.tnt_minecart": "Malmvogn med TNT", "item.minecraft.torchflower_seeds": "Frjo av kyndel<PERSON>lom", "item.minecraft.totem_of_undying": "Totem mot døying", "item.minecraft.trader_llama_spawn_egg": "Kveik<PERSON><PERSON> fyre ka<PERSON>", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "Ljoster", "item.minecraft.tropical_fish": "Sudhavsfisk", "item.minecraft.tropical_fish_bucket": "Sudhavsfiskespann", "item.minecraft.tropical_fish_spawn_egg": "Kveikjeegg fyre sudhavsfisk", "item.minecraft.turtle_helmet": "Skjelpoddeskal", "item.minecraft.turtle_scute": "Skjelpoddeskjold", "item.minecraft.turtle_spawn_egg": "Kveikjeegg fyre skjelpodda", "item.minecraft.vex_armor_trim_smithing_template": "Smideskant", "item.minecraft.vex_armor_trim_smithing_template.new": "Mødar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.vex_spawn_egg": "Kveikjeegg fyre mødar", "item.minecraft.villager_spawn_egg": "Kveikjeegg fyre bygdarbue", "item.minecraft.vindicator_spawn_egg": "Kveikjeegg fyr’ hævdar", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre farande ka<PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "Smideskant", "item.minecraft.ward_armor_trim_smithing_template.new": "Vord-brynjepryd", "item.minecraft.warden_spawn_egg": "Kveikjeegg fyre vord", "item.minecraft.warped_fungus_on_a_stick": "Vrìdsopp på stong", "item.minecraft.water_bucket": "Vatsspann", "item.minecraft.wayfinder_armor_trim_smithing_template": "Smideskant", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Vegfinnar-brynjepryd", "item.minecraft.wheat": "Kveite", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_bundle": "Kvit sekk", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Smideskant", "item.minecraft.wild_armor_trim_smithing_template.new": "Vill bryn<PERSON>d", "item.minecraft.wind_charge": "Vindlading", "item.minecraft.witch_spawn_egg": "Kveikjeegg fyre trollkjerring", "item.minecraft.wither_skeleton_spawn_egg": "Kveikjeegg fyre witherbeingrind", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyre wither", "item.minecraft.wolf_armor": "Ulvebrynja", "item.minecraft.wolf_spawn_egg": "K<PERSON><PERSON><PERSON>gg fyr’ ulv", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON>grev", "item.minecraft.wooden_pickaxe": "Trehakka", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "Tresverd", "item.minecraft.writable_book": "Bok og fjøderpenn", "item.minecraft.written_book": "<PERSON><PERSON><PERSON><PERSON> bok", "item.minecraft.yellow_bundle": "Gul sekk", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "Kveik<PERSON>gg fyre zoglin", "item.minecraft.zombie_horse_spawn_egg": "Kveik<PERSON>gg fyr’ ø<PERSON>", "item.minecraft.zombie_spawn_egg": "Kveik<PERSON>gg fyre nåe", "item.minecraft.zombie_villager_spawn_egg": "Kveik<PERSON>gg fyre bygdarn<PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "K<PERSON><PERSON><PERSON><PERSON> fyre pig<PERSON>", "item.modifiers.any": "<PERSON><PERSON>r på:", "item.modifiers.armor": "<PERSON><PERSON><PERSON>:", "item.modifiers.body": "<PERSON><PERSON>r på:", "item.modifiers.chest": "<PERSON><PERSON><PERSON> på b<PERSON>len:", "item.modifiers.feet": "<PERSON><PERSON>r på føterne:", "item.modifiers.hand": "<PERSON><PERSON><PERSON> halde:", "item.modifiers.head": "<PERSON><PERSON>r på hovudet:", "item.modifiers.legs": "<PERSON><PERSON>r på føterne:", "item.modifiers.mainhand": "<PERSON><PERSON><PERSON> i hovudhondi:", "item.modifiers.offhand": "<PERSON><PERSON><PERSON> i hi hondi:", "item.modifiers.saddle": "<PERSON><PERSON><PERSON>:", "item.nbt_tags": "NBT: %s merkjelapp(ar)", "item.op_block_warning.line1": "Å<PERSON>varing:", "item.op_block_warning.line2": "Å nytta denne tingen kann leida til at styrebòd verda iverksette", "item.op_block_warning.line3": "Ik<PERSON><PERSON> nytta minder du kjenner innehaldet vel!", "item.unbreakable": "Ubrjoteleg", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "itemGroup.combat": "Strid", "itemGroup.consumables": "<PERSON> og dry<PERSON>k", "itemGroup.crafting": "<PERSON><PERSON>", "itemGroup.foodAndDrink": "<PERSON> og dry<PERSON>k", "itemGroup.functional": "Tenesteblekker", "itemGroup.hotbar": "<PERSON><PERSON> tø<PERSON>", "itemGroup.ingredients": "<PERSON><PERSON><PERSON>", "itemGroup.inventory": "Attlìvnadsskreppa", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Røktartol", "itemGroup.redstone": "Redstoneblekker", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Tølor og ambòd", "item_modifier.unknown": "Ukjend tilmåtar fyre ting: %s", "jigsaw_block.final_state": "Verd til:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "På lina", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Skøyteslag:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON> på pusleb.", "jigsaw_block.levels": "Stìg: %s", "jigsaw_block.name": "Namn:", "jigsaw_block.placement_priority": "Nedsetje-prioritet:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON><PERSON> denne pusle<PERSON> bind seg til ein puslebit er i den rekkjefylgja biten er handsamad i den breidare bygnaden.\n\nBitar verda handsamade i fallande prioritet der innsetje-rekkjefylgja tek yver um uavgjort.", "jigsaw_block.pool": "Målumkverve:", "jigsaw_block.selection_priority": "Valfyrerett:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON>r den yverstadde biten verd handsamad fyre samband er det i den rekkjefylgja puslebiten freistar på binda seg til målbiten sin.\n\nBitar verda handsamade i fallande prioritet der tilfelleleg rekkjefylgja tek yver um uavgjort.", "jigsaw_block.target": "Målnamn:", "jukebox_song.minecraft.11": "C418 – 11", "jukebox_song.minecraft.13": "C418 – 13", "jukebox_song.minecraft.5": "<PERSON> – 5", "jukebox_song.minecraft.blocks": "C418 – blocks", "jukebox_song.minecraft.cat": "C418 – cat", "jukebox_song.minecraft.chirp": "C418 – chirp", "jukebox_song.minecraft.creator": "<PERSON> – <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> – C<PERSON> (speldåsa)", "jukebox_song.minecraft.far": "C418 – far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions – Lava Chicken", "jukebox_song.minecraft.mall": "C418 – mall", "jukebox_song.minecraft.mellohi": "C418 – mellohi", "jukebox_song.minecraft.otherside": "<PERSON> – <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> – Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 – stal", "jukebox_song.minecraft.strad": "C418 – strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 – wait", "jukebox_song.minecraft.ward": "C418 – ward", "key.advancements": "<PERSON><PERSON><PERSON>", "key.attack": "Åtak/Brjot", "key.back": "Gakk attle<PERSON>", "key.categories.creative": "Skapestòda", "key.categories.gameplay": "Spelgong", "key.categories.inventory": "Skreppa", "key.categories.misc": "<PERSON><PERSON>", "key.categories.movement": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Spel-grenseflata", "key.chat": "<PERSON><PERSON> svall", "key.command": "<PERSON><PERSON> s<PERSON>", "key.drop": "<PERSON><PERSON><PERSON> vald ting", "key.forward": "Gakk fram<PERSON>", "key.fullscreen": "Fullskjerm", "key.hotbar.1": "Fyr’ hondi 1", "key.hotbar.2": "Fyr’ hondi 2", "key.hotbar.3": "Fyr’ hondi 3", "key.hotbar.4": "Fyr’ hondi 4", "key.hotbar.5": "Fyr’ hondi 5", "key.hotbar.6": "Fyr<PERSON> hondi 6", "key.hotbar.7": "Fyr’ hondi 7", "key.hotbar.8": "Fyr’ hondi 8", "key.hotbar.9": "Fyr’ hondi 9", "key.inventory": "Opna/lat att skreppa", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Sletta", "key.keyboard.down": "Pil ned", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Talfjøl 0", "key.keyboard.keypad.1": "Talfjøl 1", "key.keyboard.keypad.2": "Talfjøl 2", "key.keyboard.keypad.3": "Talfjøl 3", "key.keyboard.keypad.4": "Talfjøl 4", "key.keyboard.keypad.5": "Talfjøl 5", "key.keyboard.keypad.6": "Talfjøl 6", "key.keyboard.keypad.7": "Talfjøl 7", "key.keyboard.keypad.8": "Talfjøl 8", "key.keyboard.keypad.9": "Talfjøl 9", "key.keyboard.keypad.add": "Talfjøl +", "key.keyboard.keypad.decimal": "<PERSON><PERSON><PERSON><PERSON><PERSON> ,", "key.keyboard.keypad.divide": "<PERSON><PERSON><PERSON><PERSON><PERSON> /", "key.keyboard.keypad.enter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.keypad.equal": "Ta<PERSON><PERSON><PERSON><PERSON> =", "key.keyboard.keypad.multiply": "<PERSON><PERSON><PERSON><PERSON><PERSON> *", "key.keyboard.keypad.subtract": "<PERSON><PERSON><PERSON><PERSON><PERSON> -", "key.keyboard.left": "Vinstre pil", "key.keyboard.left.alt": "Vinstre Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Vinstre Ctrl", "key.keyboard.left.shift": "Vinstre Shift", "key.keyboard.left.win": "Vinstre Windows-knapp", "key.keyboard.menu": "Skrå", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "PrintScr", "key.keyboard.right": "<PERSON><PERSON>gre<PERSON><PERSON>", "key.keyboard.right.alt": "Høgre Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Høgre Ctrl", "key.keyboard.right.shift": "Høgre Shift", "key.keyboard.right.win": "Høgre Windows-knapp", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Ikkje bunden", "key.keyboard.up": "Pil upp", "key.keyboard.world.1": "Heim 1", "key.keyboard.world.2": "<PERSON><PERSON> 2", "key.left": "Gakk til vinstre", "key.loadToolbarActivator": "Lad inn «fyr’ hondi»-åslagar", "key.mouse": "Knapp %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> knapp", "key.mouse.middle": "Midknapp", "key.mouse.right": "<PERSON><PERSON><PERSON><PERSON> knapp", "key.pickItem": "Vel blokk", "key.playerlist": "Leikarl<PERSON>", "key.quickActions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.right": "Gakk til høgre", "key.saveToolbarActivator": "Spar «fyr’ hondi»-åslagar", "key.screenshot": "Tak skjermbilæte", "key.smoothCamera": "Filmatiskt kamera", "key.sneak": "Smjug", "key.socialInteractions": "Samkvæmeskjerm", "key.spectatorOutlines": "Merk av leika<PERSON> (tilskòdarar)", "key.sprint": "Spring", "key.swapOffhand": "Byt tingen med hi hondi", "key.togglePerspective": "Skift synstad", "key.use": "<PERSON><PERSON><PERSON> ting/<PERSON><PERSON> ned blokk", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Samlag", "known_server_link.community_guidelines": "Samlagsreglar", "known_server_link.feedback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.forums": "Forum", "known_server_link.news": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.report_bug": "<PERSON>d ifr<PERSON> um tenarlus", "known_server_link.status": "Stòda", "known_server_link.support": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.website": "<PERSON><PERSON><PERSON>", "lanServer.otherPlayers": "Val fyr’ andre leikarar", "lanServer.port": "Port-tal", "lanServer.port.invalid": "Ikkje ein gild port.\nLat reideteigen vera tom elder legg inn eit tal millom 1024 og 65535.", "lanServer.port.invalid.new": "Ikkje ein gild port.\nLat reideteigen vera tom elder legg inn eit tal millom %s og %s.", "lanServer.port.unavailable": "Porten er ikkje tilgjengd.\nLat reideteigen vera tom elder legg inn eit annat tal millom 1024 og 65535.", "lanServer.port.unavailable.new": "Porten er ikkje tilgjengd.\nLat reideteigen vera tom elder legg inn eit annat tal millom %s og %s.", "lanServer.scanning": "Leitar etter spel på heimenetverket ditt", "lanServer.start": "Opna LAN-heim", "lanServer.title": "LA<PERSON>-heim", "language.code": "qho", "language.name": "Høgnorsk", "language.region": "<PERSON><PERSON>", "lectern.take_book": "<PERSON>k bok", "loading.progress": "%s%%", "mco.account.privacy.info": "Les meir um Mojang og mannevernslòger", "mco.account.privacy.info.button": "Les meir um GDPR", "mco.account.privacy.information": "Mojang set i verk visse framgangsmåtar fyr’ å hjelpa born og mannevernet deira. Detta samsvarar med Children’s Online Privacy Protection Act (COPPA) og mannevernspåbòdet åt EU (GDPR).\n\nDet kann henda du må få løyve av foreldre/verja fyrr du fær tilgjenge til Realms-kontoen din.", "mco.account.privacyinfo": "Mojang set i verk visse framgangsmåtar fyr’ å hjelpa born og mannevernet deira. Detta samsvarar med Children’s Online Privacy Protection Act (COPPA) og mannevernspåbòdet åt EU (GDPR).\n\nDet kann henda du må få løyve av foreldre/verja fyrr du fær tilgjenge til Realms-kontoen din.\n\nUm du heve ein eldre Minecraft-konto (du skriv deg inn med nytarnamnet ditt), må du flytja kontoen din yver til ein Mojang-konto fyr’ å få tilgjenge til Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON><PERSON> konto", "mco.activity.noactivity": "Ingi verksemd på %s døger", "mco.activity.title": "Leikarverksemd", "mco.backup.button.download": "Lad ned nyaste u<PERSON>", "mco.backup.button.reset": "Attra heim", "mco.backup.button.restore": "He<PERSON><PERSON> upp att", "mco.backup.button.upload": "Lad upp heim", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "<PERSON><PERSON><PERSON><PERSON><PERSON> (%s)", "mco.backup.entry.description": "Utgreiding", "mco.backup.entry.enabledPack": "Åslegne pakkar", "mco.backup.entry.gameDifficulty": "Spelvandleik", "mco.backup.entry.gameMode": "Spelstòda", "mco.backup.entry.gameServerVersion": "Speltenarutgåva", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "Skantnamn", "mco.backup.entry.undefined": "Ikkje-fastsett brigde", "mco.backup.entry.uploaded": "Uppladd", "mco.backup.entry.worldType": "Heimsslag", "mco.backup.generate.world": "<PERSON><PERSON><PERSON> heim", "mco.backup.info.title": "Brigde frå siste avrit", "mco.backup.narration": "Trygdaravrit frå %s", "mco.backup.nobackups": "Denne Realmen hev’ ingi trygdar<PERSON>rit nett no.", "mco.backup.restoring": "<PERSON><PERSON><PERSON> upp att Realm<PERSON> din", "mco.backup.unknown": "UKJENT", "mco.brokenworld.download": "Lad ned", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Attra elder vel ein annan heim.", "mco.brokenworld.message.line2": "Du kann òg velja å lada ned heimen til serleikar.", "mco.brokenworld.minigame.title": "Detta minispelet er ’kje lenger studt", "mco.brokenworld.nonowner.error": "Venta til eigaren av Realmen attrar heimen", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON> er <PERSON>kje etter<PERSON>ø<PERSON>", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Attra", "mco.brokenworld.title": "Den gjeldande heimen din er ’kje lenger studd", "mco.client.incompatible.msg.line1": "Klienten din høver ikkje til Realms.", "mco.client.incompatible.msg.line2": "Nytta den sidsta utgåva av Minecraft.", "mco.client.incompatible.msg.line3": "Realms høver ikkje til røyneutgåvor.", "mco.client.incompatible.title": "Klienten høver ikkje!", "mco.client.outdated.stable.version": "Klientutgåva di (%s) høver ikkje med Realms.\n\nNytta den seinsta utgåva av Minecraft.", "mco.client.unsupported.snapshot.version": "Klientutgåva di (%s) høver ’kje med Realms.\n\nRealmar er ’kje tilgjengde i denne røyneutgåva.", "mco.compatibility.downgrade": "<PERSON><PERSON>", "mco.compatibility.downgrade.description": "Denne heimen var sidst spelad i utgåva %s; du nyttar utgåva %s. <PERSON> setja atter ein heim kann føra til øydeleggjing. D’er uvisst um han kjem til å lada inn elder verka som han skal.\n\nEit trygdaravrit av heimen din verd spard i «World Backups». Du kann atterskapa heimen um nøydd.", "mco.compatibility.incompatible.popup.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.incompatible.releaseType.popup.message": "Heimen du freistar å vera med i høver ikkje med utgåva du er på.", "mco.compatibility.incompatible.series.popup.message": "Denne heimen vardt sidst spelad i utgåva %s; du nyttar utgåva %s.\n\nDesse seriarne høva ikkje med kvarandre. Du treng ein ny heim fyr’ å spela på denne utgåva.", "mco.compatibility.unverifiable.message": "Utgåva denne heimen sidst var spelad på kunde ’kje verda sannkjent. Um heimen verd aukad elder minkad verd eit trygdaravrit skapat av seg sjølv og spard i falden «World Backups».", "mco.compatibility.unverifiable.title": "<PERSON><PERSON><PERSON><PERSON> kann <PERSON>kje verda sannkjent", "mco.compatibility.upgrade": "<PERSON><PERSON>", "mco.compatibility.upgrade.description": "<PERSON>ne heimen var sidst spelad i utgåva %s; du nyttar utgåva %s.\n\nEit trygdaravrit av heimen din verd spard i «World Backups».\n\nHeimta heimen din upp um nøydd.", "mco.compatibility.upgrade.friend.description": "<PERSON>ne heimen var sidst spelad i utgåva %s; du nyttar utgåva %s.\n\nEit trygdaravrit av heimen din verd spart i «World backups».\n\nEigaren av Realmen kann atterskapa heimen um nøydd.", "mco.compatibility.upgrade.title": "Vil du røynlege auka heimen din?", "mco.configure.current.minigame": "Gjeldande", "mco.configure.world.activityfeed.disabled": "Leikarverksemd er millombils avleget", "mco.configure.world.backup": "Trygdarav<PERSON> av heim", "mco.configure.world.buttons.activity": "Leikarverksemd", "mco.configure.world.buttons.close": "Steng Realm millombils", "mco.configure.world.buttons.delete": "Sletta", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Val", "mco.configure.world.buttons.invite": "Byd inn leikar", "mco.configure.world.buttons.moreoptions": "Fleire val", "mco.configure.world.buttons.newworld": "<PERSON><PERSON> heim", "mco.configure.world.buttons.open": "Opna Realm att", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "<PERSON>el stròk...", "mco.configure.world.buttons.resetworld": "Attra heim", "mco.configure.world.buttons.save": "Spar", "mco.configure.world.buttons.settings": "Val", "mco.configure.world.buttons.subscription": "Tinging", "mco.configure.world.buttons.switchminigame": "Skift minispel", "mco.configure.world.close.question.line1": "Realmen din kjem til å verda utilgjengd.", "mco.configure.world.close.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.close.question.title": "Treng du gjera brigde utan å verda avbroten?", "mco.configure.world.closing": "<PERSON><PERSON><PERSON> att Realmen...", "mco.configure.world.commandBlocks": "Styrebòdsblekker", "mco.configure.world.delete.button": "Sletta Realm", "mco.configure.world.delete.question.line1": "Realmen din kjem til å verda slettad i òll æva", "mco.configure.world.delete.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.description": "Realms-skildring", "mco.configure.world.edit.slot.name": "<PERSON><PERSON>nam<PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON> val er avslegne sidan den gjeldande heimen din er eit æventyr", "mco.configure.world.edit.subscreen.experience": "No<PERSON>re val er avslegne sidan den gjeldande heimen din er ei røynsla", "mco.configure.world.edit.subscreen.inspiration": "No<PERSON>re val er avslegne sidan den gjeldande heimen din er ein inngjevnad", "mco.configure.world.forceGameMode": "Tvinga s<PERSON>ò<PERSON>", "mco.configure.world.invite.narration": "Du heve %s nye innbòd", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Innboden/-i", "mco.configure.world.invited.number": "Innbodne (%s)", "mco.configure.world.invites.normal.tooltip": "Ålmenn nytar", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "Tak burt", "mco.configure.world.leave.question.line1": "Um du fer frå denne Realmen kjem du ikkje til å sjå ’nom, minder du verd innboden att", "mco.configure.world.leave.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.loading": "Lader inn Realm", "mco.configure.world.location": "Stadsetjing", "mco.configure.world.minigame": "Gjeldande: %s", "mco.configure.world.name": "Realms-namn", "mco.configure.world.opening": "<PERSON>nar <PERSON>en...", "mco.configure.world.players.error": "Ein leikar med det uppgjevne namnet finst ikkje", "mco.configure.world.players.inviting": "Byd inn leikar...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Fyretrekt stròk", "mco.configure.world.region_preference.title": "<PERSON><PERSON>tr<PERSON>", "mco.configure.world.reset.question.line1": "Heimen din kjem til å verda atterskapad og den gjeldande heimen kjem til å kverva", "mco.configure.world.reset.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.resourcepack.question": "Du treng ein tilmåtad tilfangspakke fyr’ å spela på denne Realmen\n\nVil du lada ’nom ned og spela?", "mco.configure.world.resourcepack.question.line1": "Denne Realmen krev ein tilmåtad tilfangspakke", "mco.configure.world.resourcepack.question.line2": "Vil du lada det ned og spela?", "mco.configure.world.restore.download.question.line1": "Heimen verd nedladd og spard i serleikarheimarne dine.", "mco.configure.world.restore.download.question.line2": "Vil du halda fram?", "mco.configure.world.restore.question.line1": "Heimen din kjem til å verda atterskipad til dagtalet «%s» (%s)", "mco.configure.world.restore.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.settings.expired": "Du kann ikkje brigda vali til ein utgjengen Realm", "mco.configure.world.settings.title": "Val", "mco.configure.world.slot": "Heim %s", "mco.configure.world.slot.empty": "<PERSON>", "mco.configure.world.slot.switch.question.line1": "Realmen din kjem til å verda skift til ein annan heim", "mco.configure.world.slot.switch.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.slot.tooltip": "Skift til heim", "mco.configure.world.slot.tooltip.active": "Ver med", "mco.configure.world.slot.tooltip.minigame": "Skift til minispel", "mco.configure.world.spawnAnimals": "Skapa dyr", "mco.configure.world.spawnMonsters": "Skapa skræmsl", "mco.configure.world.spawnNPCs": "Skapa NPCar", "mco.configure.world.spawnProtection": "Byrjestadsvern", "mco.configure.world.spawn_toggle.message": "Å slå av detta kjem til å taka burt alle einingar som finnast av dette slaget", "mco.configure.world.spawn_toggle.message.npc": "Å slå av detta TEK BURT ALLE einingar som finnast av detta slaget, m. a. bygdarfolk", "mco.configure.world.spawn_toggle.title": "Åtvaring!", "mco.configure.world.status": "Stòda", "mco.configure.world.subscription.day": "dag", "mco.configure.world.subscription.days": "dagar", "mco.configure.world.subscription.expired": "Utgjengen", "mco.configure.world.subscription.extend": "Leng tinging", "mco.configure.world.subscription.less_than_a_day": "<PERSON>re en ein dag", "mco.configure.world.subscription.month": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON> uppnyad av seg sjølv um", "mco.configure.world.subscription.recurring.info": "Brigde gjorde på Realms-tingingi di, m. a. å lengja tidi elder slå av uppatt-ta<PERSON><PERSON> fakture<PERSON>, gjeld ikkje fyrr næste fakturadag.", "mco.configure.world.subscription.remaining.days": "%1$s dag(ar)", "mco.configure.world.subscription.remaining.months": "%1$s månad(er)", "mco.configure.world.subscription.remaining.months.days": "%1$s månad(er), %2$s dag(ar)", "mco.configure.world.subscription.start": "Byrjedag", "mco.configure.world.subscription.tab": "Tinging", "mco.configure.world.subscription.timeleft": "Tid att", "mco.configure.world.subscription.title": "Tingingi di", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> heim", "mco.configure.world.switch.slot.subtitle": "<PERSON>ne heimen er tom; vel korleides du vil skapa ’nom", "mco.configure.world.title": "Reid Realm:", "mco.configure.world.uninvite.player": "<PERSON>r du trygg på at du vil attra innbòdet åt «%s»?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> <PERSON> trygg på at du vil attra innbòdet", "mco.configure.worlds.title": "Heimar", "mco.connect.authorizing": "Skriv deg inn...", "mco.connect.connecting": "Knyter samband med Realmen...", "mco.connect.failed": "Fekk ikkje samband med Realmen", "mco.connect.region": "Server region: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "Du må gjeva eit namn!", "mco.create.world.failed": "<PERSON><PERSON> <PERSON>kje skapa heim!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON> heim...", "mco.create.world.skip": "<PERSON><PERSON> yver", "mco.create.world.subtitle": "Du kann òg velja å leggja inn ein heim på Realmen", "mco.create.world.wait": "Skipar ny Realm...", "mco.download.cancelled": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "Heimen du er i ferd med å lada ned er større en %s", "mco.download.confirmation.line2": "Du kjem ikkje til å kunna lada upp denne heimen til Realmen din att", "mco.download.confirmation.oversized": "Heimen du skal lada ned er større en %s\n\nDu kann inkje lada heimen upp til Realmen din att", "mco.download.done": "Ned<PERSON><PERSON>", "mco.download.downloading": "Lader ned", "mco.download.extracting": "<PERSON><PERSON> ut", "mco.download.failed": "Nedladingi var mislukkad", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON> ne<PERSON>", "mco.download.resourcePack.fail": "<PERSON><PERSON> <PERSON>kje lada ned tilfangs<PERSON>!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Lader ned den nyaste heimen", "mco.error.invalid.session.message": "Freista å opna Minecraft å nyo", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6001": "Klienten er ikkje-et<PERSON>førd", "mco.errorMessage.6002": "Kravi ero <PERSON>kje god<PERSON>ne", "mco.errorMessage.6003": "<PERSON>lade-g<PERSON><PERSON> er n<PERSON>dd", "mco.errorMessage.6004": "Upplade-gren<PERSON> er n<PERSON>dd", "mco.errorMessage.6005": "<PERSON><PERSON> læ<PERSON>", "mco.errorMessage.6006": "<PERSON><PERSON><PERSON> er <PERSON>kje etter<PERSON>ø<PERSON>", "mco.errorMessage.6007": "Nytar er i for mange Realmar", "mco.errorMessage.6008": "Ugildt Realms-namn", "mco.errorMessage.6009": "Ugild Realms-utgreiding", "mco.errorMessage.connectionFailure": "Nokot gjekk galet; freista å nyo seinre.", "mco.errorMessage.generic": "Nokot gjekk galet: ", "mco.errorMessage.initialize.failed": "Kunde ’kje k<PERSON>rgjera Realm", "mco.errorMessage.noDetails": "Ingi lyte-utgreidingar gjevne", "mco.errorMessage.realmsService": "Nokot gjekk galet (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Fekk ikkje samband med Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON> <PERSON>kje røkja semjande utgåva; fekk svar: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> nyo", "mco.errorMessage.serviceBusy": "Realms heve mange ånetes nett no.\nFreista å få samband med Realmen å nyo um nokre minuttar.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "<PERSON><PERSON><PERSON><PERSON>", "mco.info": "Opplysingar!", "mco.invited.player.narration": "Baud inn leikaren %s", "mco.invites.button.accept": "Godtak", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "Ingi innbòd!", "mco.invites.pending": "Nye innbòd!", "mco.invites.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.changeButton": "Vel eit annat minispel", "mco.minigame.world.info.line1": "<PERSON>te skifter millombìls heimen din ut med eit minispel!", "mco.minigame.world.info.line2": "Du kann seinre fara atter til den upphavlege heimen din utan å missa nokot.", "mco.minigame.world.noSelection": "Vel nokot", "mco.minigame.world.restore": "Stengjer minispel...", "mco.minigame.world.restore.question.line1": "Minisp<PERSON>t endar, og <PERSON>en din verd heimtad upp att.", "mco.minigame.world.restore.question.line2": "Er du trygg på at du vil halda fram?", "mco.minigame.world.selected": "Valt minispel:", "mco.minigame.world.slot.screen.title": "Skifter heim...", "mco.minigame.world.startButton": "Skift", "mco.minigame.world.starting.screen.title": "Opnar minispel...", "mco.minigame.world.stopButton": "Enda minispel", "mco.minigame.world.switch.new": "Vel eit annat minispel?", "mco.minigame.world.switch.title": "Skift minispel", "mco.minigame.world.title": "Skift Realm til minispel", "mco.news": "Realms-nytt", "mco.notification.dismiss": "Lat fara", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON>r yver no", "mco.notification.transferSubscription.message": "Realms-tingingar til Java flytja til Microsoft Store. Ikkje lat tingingi di ganga ut!\nYverfør no og få 30 dagar med Realms ukostat.\nGakk åt Profil på minecraft.net fyr’ å yverføra tingingi di.", "mco.notification.visitUrl.buttonText.default": "Opna lekk", "mco.notification.visitUrl.message.default": "<PERSON><PERSON><PERSON> le<PERSON> under", "mco.onlinePlayers": "<PERSON><PERSON><PERSON>", "mco.play.button.realm.closed": "Realm er stengd", "mco.question": "Spurning", "mco.reset.world.adventure": "Æventyr", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "<PERSON><PERSON> heim", "mco.reset.world.inspiration": "Inngjevnad", "mco.reset.world.resetting.screen.title": "Attrar heim...", "mco.reset.world.seed": "<PERSON><PERSON> (valfritt)", "mco.reset.world.template": "Heimsskantar", "mco.reset.world.title": "Attra heim", "mco.reset.world.upload": "Lad upp heim", "mco.reset.world.warning": "<PERSON><PERSON> skifter ut den gjeldande Realms-heimen din", "mco.selectServer.buy": "<PERSON><PERSON> ein <PERSON>!", "mco.selectServer.close": "Lat att", "mco.selectServer.closed": "Stengd Realm", "mco.selectServer.closeserver": "Steng Realm", "mco.selectServer.configure": "<PERSON>", "mco.selectServer.configureRealm": "<PERSON>", "mco.selectServer.create": "Skipa Realm", "mco.selectServer.create.subtitle": "Vel kva for ein heim å leggja inn i den nye Realmen din", "mco.selectServer.expired": "Utgjengen Realm", "mco.selectServer.expiredList": "Tingingi di er utgjengi", "mco.selectServer.expiredRenew": "Uppnya", "mco.selectServer.expiredSubscribe": "Tinga", "mco.selectServer.expiredTrial": "Freisteutgåva di er utgjengi", "mco.selectServer.expires.day": "Gjeng ut um <PERSON>in dag", "mco.selectServer.expires.days": "G<PERSON>ng ut um %s dagar", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON> ut snart", "mco.selectServer.leave": "Far frå Realm", "mco.selectServer.loading": "Lader inn Realms-lista", "mco.selectServer.mapOnlySupportedForVersion": "Detta kortet er ikkje studt i %s", "mco.selectServer.minigame": "Minispel:", "mco.selectServer.minigameName": "Minispel: %s", "mco.selectServer.minigameNotSupportedInVersion": "<PERSON>nn ’kje spela detta minispelet i %s", "mco.selectServer.noRealms": "Du tykkjest ikkje hava ein Realm. Legg til ein Realm fyr’ å spela med venerne dine.", "mco.selectServer.note": "Åthuga:", "mco.selectServer.open": "Open Realm", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms er ein trygg og einfald måte å njota ein ånetes Minecraft-heim på, med upptil 10 vìner på éin gong. Han styd ei mengd med minispel og mange tilmåtade heimar! Berre eigaren av Realmen må svara pengar.", "mco.selectServer.purchase": "<PERSON><PERSON> til <PERSON>", "mco.selectServer.trial": "Få ei freisteutgåva!", "mco.selectServer.uninitialized": "Klikka fyr’ å skipa Realm!", "mco.snapshot.createSnapshotPopup.text": "Du er i ferd med å skipa ein ukostad røyneutgåve-Realm som kjem til å verda bunden saman med den kostada Realms-tingingi. Denne nye røyneutgåve-Realmen kjem til å vera tilgjengd so lenge den kostada tingingi er verksòm. Den kostade Realmen verd ikkje påverkad.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON> r<PERSON>å<PERSON>-Realm?", "mco.snapshot.creating": "<PERSON><PERSON>-Realm...", "mco.snapshot.description": "Samanbunden med «%s»", "mco.snapshot.friendsRealm.downgrade": "Du må vera på utgåva %s fyr’ å verda med i denne <PERSON>en", "mco.snapshot.friendsRealm.upgrade": "%s må auka Realmen sin fyrr du kann spela frå denne utg<PERSON>va", "mco.snapshot.paired": "<PERSON><PERSON> rø<PERSON>utgåve-Realmen er samanbunden med «%s»", "mco.snapshot.parent.tooltip": "Nytta den nyasta heilutgåva av Minecraft fyr’ å spela på denne Realmen", "mco.snapshot.start": "Set upp ukostad røyneutgåve-Realm", "mco.snapshot.subscription.info": "Detta er ein røyneutgåve-Realm som er bunden saman med tingingi «%s». Han kjem til å vera verksam so lenge den samanbundne Realmen er verksam.", "mco.snapshot.tooltip": "Nytta røyneutgåve-Realmar fyr’ å få ein glytt av komande utgåvor av Minecraft, som kunna hava nytt innehald elder andre brigde.\n\nDu kann finna dei vanlege Realmarne dine i den heilslega utgåva av spelet.", "mco.snapshotRealmsPopup.message": "Realms er no tilgjengt i røyneutgåvor frå og med 23w41a. Kór Realms-tinging kjem med ein ukostad røyneutgåve-Realm som er skild frå den vanlege Java-Realmen!", "mco.snapshotRealmsPopup.title": "Realms er no tilgjengt i røyneutgåvor", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON> meir", "mco.template.button.publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.select": "Vel", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "Heimsskant", "mco.template.info.tooltip": "Vevgard<PERSON> åt utgjevaren", "mco.template.name": "<PERSON><PERSON><PERSON>", "mco.template.select.failure": "Me kunde ’kje heimta lista yver innehald fyre denne bolken.\nSjå på netsambandet ditt elder freista å nyo seinre.", "mco.template.select.narrate.authors": "Upphavsmenn: %s", "mco.template.select.narrate.version": "utgåva %s", "mco.template.select.none": "Oi sann! Det sér ut som at denne bolken er tom fyre tidi.\nSj<PERSON> att seinre fyre nytt innehald, elder %s um du er ein skapar.", "mco.template.select.none.linkTitle": "tenk på å senda inn nokot sjølv", "mco.template.title": "Heimsskantar", "mco.template.title.minigame": "Minispel", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "Samd", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON> samd", "mco.terms.sentence.1": "<PERSON><PERSON> godk<PERSON> vilkòri åt Minecraft Realms", "mco.terms.sentence.2": "Tenestevilkòr", "mco.terms.title": "Tenestevilkòr fyre Realms", "mco.time.daysAgo": "%1$s dag(ar) sidan", "mco.time.hoursAgo": "%1$s time/-ar sidan", "mco.time.minutesAgo": "%1$s minutt(ar) sidan", "mco.time.now": "nett no", "mco.time.secondsAgo": "%1$s sekund(ar) sidan", "mco.trial.message.line1": "Vil du hava din eigen Realm?", "mco.trial.message.line2": "<PERSON><PERSON><PERSON> her fyre meir utgreiding!", "mco.upload.button.name": "Lad upp", "mco.upload.cancelled": "Upplading <PERSON><PERSON><PERSON><PERSON>", "mco.upload.close.failure": "<PERSON><PERSON> ’kje stengja Realmen din; freista å nyo seinre", "mco.upload.done": "Upplading ferdug", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Upplading var mislukkad! (%s)", "mco.upload.failed.too_big.description": "Den valde heimen er for stor. Høgste løyvde storleike er %s.", "mco.upload.failed.too_big.title": "Heim for stor", "mco.upload.hardcore": "Hardhausheimar kunna ’kje verda uppladde!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON> heimen din", "mco.upload.select.world.none": "Ingi se<PERSON>ar fundne!", "mco.upload.select.world.subtitle": "Vel ein serleikarheim å lada upp", "mco.upload.select.world.title": "Lad upp heim", "mco.upload.size.failure.line1": "«%s» er for stor!", "mco.upload.size.failure.line2": "Han er %s. Høgste løyvde storleike er %s.", "mco.upload.uploading": "Lader upp «%s»", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON> heimen ditt", "mco.version": "Utgåva: %s", "mco.warning": "Åtvaring!", "mco.worldSlot.minigame": "Minispel", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Sløkk samband", "menu.feedback": "Atterbòd...", "menu.feedback.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.game": "Spelskrå", "menu.modded": " (Moddad)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Val...", "menu.paused": "Spel stodgat", "menu.playdemo": "S<PERSON><PERSON> f<PERSON>", "menu.playerReporting": "Seg ifrå um leikar", "menu.preparingSpawn": "Fyrebur byrjestad: %s%%", "menu.quick_actions": "Snaråtgjerder...", "menu.quick_actions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.quit": "Enda spel", "menu.reportBugs": "Seg ifrå um lyser", "menu.resetdemo": "Attra freisteheim", "menu.returnToGame": "Attende til spelet", "menu.returnToMenu": "Spar og far åt hovudskråi", "menu.savingChunks": "Sparer heimsbitar", "menu.savingLevel": "Sparer heim", "menu.sendFeedback": "<PERSON><PERSON><PERSON>", "menu.server_links": "Tenarlekker...", "menu.server_links.title": "<PERSON><PERSON><PERSON><PERSON>", "menu.shareToLan": "Opna til LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.working": "<PERSON><PERSON><PERSON>...", "merchant.deprecated": "Bygdarfolk fylla upplaget tvei gonger dagen.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "Svein", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s — %s", "merchant.trades": "Byter", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Kyv %1$s fyr’ å stiga av", "multiplayer.applyingPack": "Legg inn tilfangspakke", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Stadfest køyring av styrebòd", "multiplayer.disconnect.authservers_down": "Stadfestartenararne ero nedre. Freista å nyo seinre; orsaka!", "multiplayer.disconnect.bad_chat_index": "Fekk svallbòd som var saknat elder komet i rang rekkjefylgja frå tenaren", "multiplayer.disconnect.banned": "Du er bannlyst frå denne tenaren", "multiplayer.disconnect.banned.expiration": "\nBannlysingi di verd teki burt %s", "multiplayer.disconnect.banned.reason": "Du er bannlyst frå denne tenaren.\nGrunn: %s", "multiplayer.disconnect.banned_ip.expiration": "\nBannlysingi di verd teki burt %s", "multiplayer.disconnect.banned_ip.reason": "IP-tilskrifti di er bannlyst frå denne tenaren.\nGrunn: %s", "multiplayer.disconnect.chat_validation_failed": "<PERSON><PERSON> <PERSON>kje <PERSON> s<PERSON>d", "multiplayer.disconnect.duplicate_login": "Du skreiv deg inn frå ein annan stad", "multiplayer.disconnect.expired_public_key": "Den ålmennelege lykelen til profilen er utgjengen. Sjå til at systemet ditt er synkroniserat, og freista opna spelet å nyo.", "multiplayer.disconnect.flying": "Fljuging er ’kje åsleget på denne tenaren", "multiplayer.disconnect.generic": "Sløkt samband", "multiplayer.disconnect.idling": "Du heve voret gjerand<PERSON>us for lenge!", "multiplayer.disconnect.illegal_characters": "Ugilde teikn i svall", "multiplayer.disconnect.incompatible": "Usemjande klient. Nytta %s", "multiplayer.disconnect.invalid_entity_attacked": "Freistar å åtaka ei ugild eining", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON><PERSON> sende ugild pakke", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON>", "multiplayer.disconnect.invalid_player_movement": "Ugild pakke fyre flytjing av leikar teken imot", "multiplayer.disconnect.invalid_public_key_signature": "Ugildt segl fyre den ålmennelege lykelen til profilen. Freista å opna spelet å nyo.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ugildt segl fyre den ålmennelege lykelen til profilen.\nFreista å opna spelet å nyo.", "multiplayer.disconnect.invalid_vehicle_movement": "Ugild pakke fyre flytjing av køyrety teken imot", "multiplayer.disconnect.ip_banned": "Du er vorden/-i IP-bannlyst frå denne tenaren", "multiplayer.disconnect.kicked": "Kastad ut av røktar", "multiplayer.disconnect.missing_tags": "Eit uheilslegt uppset av merkjelappar vardt heimtat frå tenaren.\nNå ut til tenarrøktaren.", "multiplayer.disconnect.name_taken": "Detta namnet er alt teket", "multiplayer.disconnect.not_whitelisted": "Du er ’kje kvitelistad på denne tenaren!", "multiplayer.disconnect.out_of_order_chat": "Svallpakke vardt motteken i gali rekkjefylgja. Er systemtidi di brigd?", "multiplayer.disconnect.outdated_client": "Usemjande klient! Nytta %s", "multiplayer.disconnect.outdated_server": "Usemjande klient! Nytta %s", "multiplayer.disconnect.server_full": "Tenaren er full!", "multiplayer.disconnect.server_shutdown": "<PERSON>ar stengd", "multiplayer.disconnect.slow_login": "Tok for lòng tid å skriva seg inn", "multiplayer.disconnect.too_many_pending_chats": "For mange ustadfeste svallbòd", "multiplayer.disconnect.transfers_disabled": "Tenaren godtek ikkje yverføringar", "multiplayer.disconnect.unexpected_query_response": "Uventade eigenlagade data frå klient", "multiplayer.disconnect.unsigned_chat": "<PERSON>k imot svallpakke med vantande elder ugildt segl.", "multiplayer.disconnect.unverified_username": "<PERSON>nde ’kje sannkjenna nytarnamn!", "multiplayer.downloadingStats": "Heimtar statistikk...", "multiplayer.downloadingTerrain": "Lader inn landskap...", "multiplayer.lan.server_found": "Ny tenar funden: %s", "multiplayer.message_not_delivered": "<PERSON>nn <PERSON>kje senda svallbòd. Sjå tenarloggen: %s", "multiplayer.player.joined": "%s vardt med i spelet", "multiplayer.player.joined.renamed": "%s (tidlegare kjend som %s) vardt med i spelet", "multiplayer.player.left": "%s fór or spelet", "multiplayer.player.list.hp": "%s liv", "multiplayer.player.list.narration": "<PERSON><PERSON><PERSON>: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON>n krev ein tilm<PERSON>tad tilfangspakke", "multiplayer.requiredTexturePrompt.line1": "<PERSON>ne tenaren krev bruk av ein tilmåtad tilfangspakke.", "multiplayer.requiredTexturePrompt.line2": "Avviser du denne tilmåtade tilfangspakken sløkk sambandet med tenaren.", "multiplayer.socialInteractions.not_available": "Samkvæme er berre mogelegt i samleikar-heimar", "multiplayer.status.and_more": "... og %s fleire ...", "multiplayer.status.cancelled": "Avbrotet", "multiplayer.status.cannot_connect": "<PERSON><PERSON><PERSON> <PERSON>kje samband med tenaren", "multiplayer.status.cannot_resolve": "<PERSON><PERSON> <PERSON>kje løysa hysarnamn", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Usemjande utgåva!", "multiplayer.status.motd.narration": "Bòd fyre dagen: %s", "multiplayer.status.no_connection": "(inkje samband)", "multiplayer.status.old": "G<PERSON><PERSON>", "multiplayer.status.online": "Ånetes", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisekundar", "multiplayer.status.pinging": "Pingar...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s av %s leikarar <PERSON>es", "multiplayer.status.quitting": "<PERSON><PERSON>", "multiplayer.status.request_handled": "Førespurnaden um stòda er vorden handsamad", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Tok imot uynskt stòda", "multiplayer.status.version.narration": "Tenarutgåva: %s", "multiplayer.stopSleeping": "Gakk ifrå sengi", "multiplayer.texturePrompt.failure.line1": "Ku<PERSON> ’kje slå på tilfangspakke fyre tenaren", "multiplayer.texturePrompt.failure.line2": "Verkende som krevja tilmåtade tilfang verka kann-henda ikkje som ventat", "multiplayer.texturePrompt.line1": "<PERSON>ne tenaren tilråder bruk av ein viss tilfangspakke.", "multiplayer.texturePrompt.line2": "Ynskjer du å lada ned og leggja det inn sjølvgandande?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nBòd frå tenaren:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON>", "multiplayer.unsecureserver.toast": "B<PERSON>d sende på denne tenaren kunna vera brigde og spegla kann henda ikkje det upphavlege bòdet", "multiplayer.unsecureserver.toast.title": "S<PERSON><PERSON>b<PERSON><PERSON> kunna ’kje verda sannkjende", "multiplayerWarning.check": "Ikkje syn denne vitringi att", "multiplayerWarning.header": "Åtvaring: <PERSON>d<PERSON><PERSON>s-<PERSON>pel", "multiplayerWarning.message": "Åtvaring: Netspel er tilbòdet av tridjemanns-tenarar som Mojang Studios elder Microsoft korkje eiga, styra, elder sjå til. Nær du spelar ånetes kann du møta på utilsét svall elder andre former fyre nytarskapat innehald som ikkje allstødt høva fyr’ alle.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 – Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 – <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – <PERSON>’s Piano", "music.game.oxygene": "C418 – Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labyrinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Knapp: %s", "narration.button.usage.focused": "Kyv Enter fyr’ å slå på", "narration.button.usage.hovered": "Vinstreklikka fyr’ å slå på", "narration.checkbox": "Avkrossingteig: %s", "narration.checkbox.usage.focused": "Kyv Enter fyr’ å skifta", "narration.checkbox.usage.hovered": "Vinstreklikka fyr’ å skifta", "narration.component_list.usage": "Kyv Tab fyr’ å koma deg til næste element", "narration.cycle_button.usage.focused": "Kyv Enter fyr’ å skifta til %s", "narration.cycle_button.usage.hovered": "Vinstreklikka fyr’ å skifta til %s", "narration.edit_box": "Reideteig: %s", "narration.item": "Ting: %s", "narration.recipe": "Uppskrift til %s", "narration.recipe.usage": "Vinstreklikka fyr’ å velja", "narration.recipe.usage.more": "Høgreklikka fyr’ å syna fleire uppskrifter", "narration.selection.usage": "Kyv upp- og ned-knapparne fyr’ å flytja deg til ei onnor uppføring", "narration.slider.usage.focused": "Kyv vinstre- elder hø<PERSON><PERSON><PERSON><PERSON> på knappef<PERSON><PERSON><PERSON> fyr’ å brigda verde", "narration.slider.usage.hovered": "Drag glidebrjotaren fyr’ å gjera um på verde", "narration.suggestion": "Valde framlegg %s av %s: %s", "narration.suggestion.tooltip": "Valde framlegg %s av %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Kyv Tab fyr’ å koma til næste framlegg", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON><PERSON> Tab fyr’ å koma til næste framleg<PERSON>, elder <PERSON><PERSON><PERSON> fyr’ å fara frå framleggi", "narration.suggestion.usage.fill.fixed": "Kyv Tab fyr’ å nytta framlegg", "narration.suggestion.usage.fill.hidable": "K<PERSON>v Tab fyr<PERSON> å nyt<PERSON> fram<PERSON>, elder <PERSON><PERSON><PERSON> fyr<PERSON> å fara frå framleggi", "narration.tab_navigation.usage": "Kyv Ctrl og Tab fyr’ å skifta millom flìpar", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Vandleikslås", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s er bundet til %s", "narrator.controls.reset": "Set attende %s-knapp", "narrator.controls.unbound": "%s er ’kje bundet", "narrator.joining": "<PERSON>erd med", "narrator.loading": "Lader inn: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Valde listeròd %s utav %s", "narrator.position.object_list": "Valde radelement %s utav %s", "narrator.position.screen": "Skjermelement %s utav %s", "narrator.position.tab": "Valde flìpe %s utav %s", "narrator.ready_to_play": "Klår til å spela", "narrator.screen.title": "Hovudskrå", "narrator.screen.usage": "Nytta musepe<PERSON>ren elder Tab fyr’ å velja element", "narrator.select": "Valt: %s", "narrator.select.world": "Valt: %s, sidst spelad: %s, %s, %s, utgåva: %s", "narrator.select.world_info": "Valde %s, sidst spelat %s,%s", "narrator.toast.disabled": "Forteljar avslegen", "narrator.toast.enabled": "<PERSON><PERSON>jar <PERSON>", "optimizeWorld.confirm.description": "Detta mun betra heimen din ved å sjå til at alle data er sparde i det nyaste spelsnìdet. Detta kann taka ei lòng stund, bundet av heimen din. Nær detta er klårt kjem heimen din til å køyra snarare, men kjem ikkje lenger til å stydja eldre utgåvor av spelet. Er du trygg på at du vil halda fram?", "optimizeWorld.confirm.proceed": "Laga trygdaravrit og betra", "optimizeWorld.confirm.title": "<PERSON><PERSON> heim", "optimizeWorld.info.converted": "Aukade heimsbitar: %s", "optimizeWorld.info.skipped": "Yverhoppade heimsbitar: %s", "optimizeWorld.info.total": "Heimsbitar til saman: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Tel heimsbitar...", "optimizeWorld.stage.failed": "Kunde ’kje! :(", "optimizeWorld.stage.finished": "Fullgjerer...", "optimizeWorld.stage.finished.chunks": "Fullgjerer betringi av heimsbitar...", "optimizeWorld.stage.finished.entities": "Fullgjerer betringi av einingar...", "optimizeWorld.stage.finished.poi": "Fullgjerer betringi av forvitnestader...", "optimizeWorld.stage.upgrading": "Betrar alle heimsbitar...", "optimizeWorld.stage.upgrading.chunks": "Betrar alle heimsbitar...", "optimizeWorld.stage.upgrading.entities": "Betrar alle einingar...", "optimizeWorld.stage.upgrading.poi": "Betrar alle forvitnestader...", "optimizeWorld.title": "<PERSON><PERSON> heimen «%s»", "options.accessibility": "Tilgjengeval...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON> mots<PERSON>d", "options.accessibility.high_contrast.error.tooltip": "Tilfangspakke til høg motsetnad er ’kje tilgjengd.", "options.accessibility.high_contrast.tooltip": "Styrkjer motsetnaden til element i grenseflata.", "options.accessibility.high_contrast_block_outline": "Tydelege blo<PERSON>kjadrar", "options.accessibility.high_contrast_block_outline.tooltip": "Styrkjer motsetnaden på jadaren av målblokki.", "options.accessibility.link": "Vegvisar fyre tilg<PERSON>nge", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON> attum skråi", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON>jerer um på tòka attum skråi.", "options.accessibility.narrator_hotkey": "Forteljar-snark<PERSON>p", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON><PERSON> lø<PERSON>ve til at forteljaren verd åslegen og avslegen med «Cmd+B».", "options.accessibility.narrator_hotkey.tooltip": "<PERSON><PERSON><PERSON> lø<PERSON>ve til at forteljaren verd åslegen og avslegen med «Ctrl+B».", "options.accessibility.panorama_speed": "Panorama-snarl<PERSON><PERSON>", "options.accessibility.text_background": "Tekstbakgrunn", "options.accessibility.text_background.chat": "Svall", "options.accessibility.text_background.everywhere": "Allstad", "options.accessibility.text_background_opacity": "Tòka attum tekst", "options.accessibility.title": "Tilgjengeval", "options.allowServerListing": "<PERSON><PERSON><PERSON> løyve til tenarlistingar", "options.allowServerListing.tooltip": "Tenarar kann lista ånetes leikarar i den ålmennelega stòda si.\nMed detta valet avsleget kjem ikkje namnet ditt upp på slike listor.", "options.ao": "Jamt ljos", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minst", "options.ao.off": "AV", "options.attack.crosshair": "Trådkross", "options.attack.hotbar": "<PERSON><PERSON><PERSON> hondi", "options.attackIndicator": "Åtaksmålar", "options.audioDevice": "Eining", "options.audioDevice.default": "Systemfyreval", "options.autoJump": "Autohopp", "options.autoSuggestCommands": "Styrebòdsframlegg", "options.autosaveIndicator": "Sparevitring", "options.biomeBlendRadius": "Lendeblanding", "options.biomeBlendRadius.1": "AV (Snarast)", "options.biomeBlendRadius.11": "11x11 (Ofselegt)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (Høgst)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON> h<PERSON>)", "options.chat": "<PERSON><PERSON><PERSON><PERSON>...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Svalldrygjing: %s sek.", "options.chat.delay_none": "Svalldrygjing: Ingi", "options.chat.height.focused": "Fokuserad hædd", "options.chat.height.unfocused": "Ufokuserad hædd", "options.chat.line_spacing": "Radfråstand", "options.chat.links": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.links.prompt": "<PERSON>r um lekker", "options.chat.opacity": "Svallgjenomsyn", "options.chat.scale": "Skriftstorleike i svall", "options.chat.title": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility": "Svall", "options.chat.visibility.full": "Synt", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "Einast styre<PERSON>òd", "options.chat.width": "Breidd", "options.chunks": "%s heimsbt.", "options.clouds.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.clouds.fast": "Snare", "options.controls": "Styring...", "options.credits_and_attribution": "Medverkande...", "options.damageTiltStrength": "Skadeskaking", "options.damageTiltStrength.tooltip": "Mengdi kameraskaking orsakad av å verda skadd.", "options.darkMojangStudiosBackgroundColor": "Einlìtt logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON><PERSON> bakgrunnen på Mojang Studios-ladeskjermen til svart.", "options.darknessEffectScale": "Myrkerbløkting", "options.darknessEffectScale.tooltip": "Avgjerer kor myket myrkerverknaden bløktar nær ein vord elder sculkeskrikar gjev deg honom.", "options.difficulty": "<PERSON><PERSON><PERSON>", "options.difficulty.easy": "<PERSON>t", "options.difficulty.easy.info": "Fiendslege kvìkende koma fram men gjera mindre skade. Hungermålaren verd tømd og dreg helsa ned til 5 hjarto.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "Fiendslege kvìkende koma fram og gjera meire skade. Hungermålaren verd tømd og òll helsa fer.", "options.difficulty.hardcore": "<PERSON><PERSON>", "options.difficulty.normal": "<PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "Fiendslege kvìkende koma fram og gjerer vanleg skade. Hungermålaren verd tømd og dreg helsa ned til eit halvt hjarta.", "options.difficulty.online": "Tenarvandleik", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON> j<PERSON>", "options.difficulty.peaceful.info": "<PERSON>gi fiendslege kvìkende. Berre sume likesæle kvìkende ovra seg. Du verd ’kje svolten og helsa verd fyllt upp yver tid.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klassiskt stereoljod.", "options.directionalAudio.on.tooltip": "Nyttar HRTF-baserad ljoddusj til å betra simuleringi av 3D-ljod. Krev ljod-maskinvòra som gjev løyve til HRTF. HRTF er best notet med øyretelefonar.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON><PERSON> bledjing", "options.entityDistanceScaling": "Einingfråstand", "options.entityShadows": "Einingskuggar", "options.font": "<PERSON><PERSON><PERSON><PERSON>...", "options.font.title": "<PERSON><PERSON><PERSON><PERSON>", "options.forceUnicodeFont": "Tvinga Unicode-skrift", "options.fov": "Skòdsvidd", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON>", "options.fovEffectScale": "Skòdsverknader", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON> kor myket syni kann skifta med spelverknader.", "options.framerate": "%s fps", "options.framerateLimit": "Høgst FPS", "options.framerateLimit.max": "Uavgren<PERSON>", "options.fullscreen": "Fullskjerm", "options.fullscreen.current": "Gjeldande", "options.fullscreen.entry": "%s×%s@%s (%sbit)", "options.fullscreen.resolution": "Fullskjermsuppløysing", "options.fullscreen.unavailable": "<PERSON><PERSON> er <PERSON>kje tilgje<PERSON>t", "options.gamma": "Ljosstyrke", "options.gamma.default": "Fyrevalt", "options.gamma.max": "Ljost", "options.gamma.min": "Dimt", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON>narleike", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON> kor skjott den visuelle glimten skimrar yver galdrade ting.", "options.glintStrength": "Glimtstyrke", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON> kor gjenomsynleg den visuelle glimten på galdrade ting er.", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Framifrå!", "options.graphics.fabulous.tooltip": "%s grafikk nyttar shaders til å teikna veder, skyer og smålùter attum gjenomskinlege blekker og vatn.\nDetta kann påverka dygdi i stor grad fyre berelege einingar og 4K-skjermar.", "options.graphics.fancy": "<PERSON><PERSON>del<PERSON>", "options.graphics.fancy.tooltip": "Prydeleg grafikk balanserar dygd og godleike fyre flestalle maskinor.\nDet kann henda at veder, skyer og smålùter ikkje ovra seg attum gjenomskinlege blekker elder vatn.", "options.graphics.fast": "<PERSON><PERSON>", "options.graphics.fast.tooltip": "Snar grafikk minkar mengdi synleg regn og snjo.\nGjenomsyn-verknader er slegne av fyr’ ymse blekker, som t.d. trelauv.", "options.graphics.warning.accept": "Haldt fram utan studnad", "options.graphics.warning.cancel": "Tak meg atter", "options.graphics.warning.message": "Grafikkeiningi di vantar studnad fyre grafikkvalet %s.\n\n<PERSON> kann sjå burt ifrå detta og halda fram, men einingi di verd ’kje studd um du vel å nytta grafikkvalet %s.", "options.graphics.warning.renderer": "Attgjevar funden: [%s]", "options.graphics.warning.title": "Grafikkeiningi er ustudd", "options.graphics.warning.vendor": "Se<PERSON><PERSON> funden: [%s]", "options.graphics.warning.version": "Utgåva av OpenGL fundi: [%s]", "options.guiScale": "GUI-storleike", "options.guiScale.auto": "S<PERSON><PERSON><PERSON>vminnt", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON> elding frå å lysa upp himelen. Sjølve nedslaget kjem enndå til å vera synlegt.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON> namn som ero like", "options.hideMatchedNames.tooltip": "Tridjemanns-tenarar kunna senda svallbòd som ikkje ero i standardsnìd.\nMed detta valet på verda gøymde leikarar bundne i hop etter svallsendarnamn.", "options.hideSplashTexts": "<PERSON><PERSON><PERSON>", "options.hideSplashTexts.tooltip": "Løyner den gula teksti på hovudskråi.", "options.inactivityFpsLimit": "Set ned FPS nær", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Avgrensar FPS til 30 nær spelet ikkje fær leikargjøv i meir en éin minutt. Avgrensar det dinæst til 10 etter 9 fleire minuttar.", "options.inactivityFpsLimit.minimized": "<PERSON>gt ned", "options.inactivityFpsLimit.minimized.tooltip": "Avgrensar FPS einast nær spelvindaugat er lagt ned.", "options.invertMouse": "Spegelvend musi", "options.japaneseGlyphVariants": "Japanske teiknslag", "options.japaneseGlyphVariants.tooltip": "Nyttar japanske slag av CJK-teikn i det fyrevalde bokstavslaget.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Skift", "options.language": "Mål...", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(<PERSON> hender at umsetjingarne ikkje ero 100%% rette)", "options.languageWarning": "Det hender at umsetjingarne ikkje ero 100%% rette", "options.mainHand": "<PERSON><PERSON><PERSON>ond", "options.mainHand.left": "Vinstre", "options.mainHand.right": "<PERSON><PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap-stìg", "options.modelPart.cape": "Sløda", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Vinstre brokfot", "options.modelPart.left_sleeve": "Vinstre erm", "options.modelPart.right_pants_leg": "Høgre brokfot", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON><PERSON> erm", "options.mouseWheelSensitivity": "Bledjevarsemd", "options.mouse_settings": "Musarval...", "options.mouse_settings.title": "Musarval", "options.multiplayer.title": "Samleikarval...", "options.multiplier": "%sx", "options.music_frequency": "Musikktidleike", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "Fyrevalt", "options.music_frequency.frequent": "Ofte", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON> kor tidt toneleik verd spelad av i ein spelheim.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Les upp alt", "options.narrator.chat": "<PERSON> upp svall", "options.narrator.notavailable": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.off": "AV", "options.narrator.system": "Les upp system", "options.notifications.display_time": "Synetid fyre vitringar", "options.notifications.display_time.tooltip": "Påverkar kor lenge vitringar ero synlege på skjermen.", "options.off": "AV", "options.off.composed": "%s: AV", "options.on": "PÅ", "options.on.composed": "%s: P<PERSON>", "options.online": "Ånetes...", "options.online.title": "Ånetes-val", "options.onlyShowSecureChat": "<PERSON>yn berre trygt svall", "options.onlyShowSecureChat.tooltip": "<PERSON>yn berre bòd frå andre leikarar nær det er sannkjent at sende deim, og utan å vera brigde.", "options.operatorItemsTab": "Røktartol-flìpe", "options.particles": "Smålùter", "options.particles.all": "Alle", "options.particles.decreased": "<PERSON><PERSON><PERSON>", "options.particles.minimal": "Minst", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Heimsbitbyggjar", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "<PERSON><PERSON><PERSON>gjerder inni ein heimsbit etterfører blokki på augneblinken. <PERSON>ta gjeld setjing og brjoting av blekker.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Heimsbitar i nærleiken verda allstødt etterførde på augneblinken. Detta kann påverka speldygdi nær blekker verda sette ned elder brotna.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Heimsbitar i nærleiken verda etterførde i parallelle træder. Detta kann føra til stutte visuelle hol nær blekker brotna.", "options.rawMouseInput": "<PERSON><PERSON>øv", "options.realmsNotifications": "Realms-nytt og innbòd", "options.realmsNotifications.tooltip": "Heimtar Realms-nytt og innbòd til hovudskråi, og syner bilætet deira på Realms-knappen.", "options.reducedDebugInfo": "<PERSON><PERSON><PERSON> lys<PERSON>upp<PERSON><PERSON>ar", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "Skyfråstand", "options.renderDistance": "Tò<PERSON>", "options.resourcepack": "Tilfangspakkar...", "options.rotateWithMinecart": "Rid deg med malmvogn", "options.rotateWithMinecart.tooltip": "Um synet åt leikaren skal rida seg etter malmvogn i svingar. Berre tilgjengt i heimar der utrøynevalet «Betring av malmvogner» er slege på.", "options.screenEffectScale": "Rengjeverknader", "options.screenEffectScale.tooltip": "Styrken av skjermrengjing frå svimring og netherlìd.\nPå lægre stìg er svimreverknaden bytt ut med grønt yverlegg.", "options.sensitivity": "Musarvarsemd", "options.sensitivity.max": "OVSNARLEIKE!!!", "options.sensitivity.min": "*geisp*", "options.showNowPlayingToast": "Syn toneleiksvitring", "options.showNowPlayingToast.tooltip": "Syner ei vitring nær ein song verd spelad. Den same vitringi verd synt i avbrotsskråi jamt medan ein song spelar.", "options.showSubtitles": "<PERSON>yn under<PERSON>", "options.simulationDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.skinCustomisation": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "Toneleik og ljod...", "options.sounds.title": "Toneleiks- og ljodval", "options.telemetry": "Telemetridata...", "options.telemetry.button": "Data-innsamning", "options.telemetry.button.tooltip": "«%s» inneheld berre naudsynte data.\n«%s» inneheld både valfrie og naudsynte data.", "options.telemetry.disabled": "Fjerrmåling er avsleget.", "options.telemetry.state.all": "Alt", "options.telemetry.state.minimal": "Minst", "options.telemetry.state.none": "Inkje", "options.title": "Val", "options.touchscreen": "Peikeskjerm-stòda", "options.video": "B<PERSON><PERSON><PERSON><PERSON>...", "options.videoTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.viewBobbing": "<PERSON><PERSON> gongelag", "options.visible": "Synt", "options.vsync": "VSync", "outOfMemory.message": "Minecraft er tom for minne.\n\n<PERSON><PERSON><PERSON><PERSON> kann vera ei lus i spelet, elder at Java Virtual Machine ikkje er løyvt nog minne.\n\nFyr’ å hindra at spelskiln verda skadde vardt spelet latet att. Me hava freistat å frigjera nog minne til at du skal kunna ganga atter åt hovudskråi og halda fram med spelingi, men d’er uvisst um det verkar.\nOpna spelet å nyo um du ser denna vitringi att.", "outOfMemory.title": "Tom fyre minne!", "pack.available.title": "Tilgjengde", "pack.copyFailure": "<PERSON><PERSON> <PERSON>kje rita av pakkar", "pack.dropConfirm": "Vil du leggja til fylgjande pakkar i Minecraft?", "pack.dropInfo": "Drag og slepp skiln i detta vindaugat fyr’ å leggja til pakkar", "pack.dropRejected.message": "Fylgjande uppføringar våro ugilde pakkar og vordo ’kje avritade:\n %s", "pack.dropRejected.title": "Uppføringar som ikkje ero pakkar", "pack.folderInfo": "(<PERSON><PERSON> p<PERSON>kes<PERSON><PERSON> her)", "pack.incompatible": "<PERSON><PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "Denne pakken var lagad fyr’ ei nyare utgåva av Minecraft og kjem kann henda ikkje til å verka.", "pack.incompatible.confirm.old": "Denne pakken var lagad fyr’ ei eldre utgåva av Minecraft og kjem kann henda ikkje til å verka.", "pack.incompatible.confirm.title": "<PERSON>r du trygg på at du vil lada denne pakken?", "pack.incompatible.new": "(La<PERSON>d fyr’ ei nyare utgåva av Minecraft)", "pack.incompatible.old": "(La<PERSON>d fyr’ ei eldre utgåva av Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON> p<PERSON>n", "pack.selected.title": "Valde", "pack.source.builtin": "innbygt", "pack.source.feature": "verkende", "pack.source.local": "heim<PERSON>", "pack.source.server": "tenar", "pack.source.world": "heim", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albansk", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Bakgard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barokk", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON> sprengt med heppa", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Blomebundel", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skalle i brand", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Holefugl", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Skiftande", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Endebossen", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Stridsfolk", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Leitande", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON>d", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Audmjuk", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tri pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON> s<PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Eldstikka", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Tankemyken", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Ljosklot", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Ula med sitronar", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Gjenomgang", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Dam", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Slettelandsrid", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Havstrondi", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Døyeleg kveil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skalle og rosor", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON><PERSON> er sett", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sol<PERSON>dl<PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tidvatn", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Uppakkat", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Tomrome<PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Ferdar", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Øydemark", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Vatn", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Slumpevalt avbrigde", "parsing.bool.expected": "Boolsk verde var ventat", "parsing.bool.invalid": "Ugildt boolsk verde. «True» elder «false» var ventat, men «%s» vardt fundet", "parsing.double.expected": "Tvi<PERSON> var ventat", "parsing.double.invalid": "«%s» er eit ugildt tvital", "parsing.expected": "«%s» var ventat", "parsing.float.expected": "Fljottal var ventat", "parsing.float.invalid": "«%s» er eit ugildt fljottal", "parsing.int.expected": "Heiltal var ventat", "parsing.int.invalid": "«%s» er eit ugildt heiltal", "parsing.long.expected": "Lang-verde var ventat", "parsing.long.invalid": "«%s» er eit ugildt lang-verde", "parsing.quote.escape": "«\\%s» er ei ugild skiftefylgd i den hermde strengen", "parsing.quote.expected.end": "Strengen hev’ inkje attlatande gåsauga", "parsing.quote.expected.start": "Gåsauga var ventat fyr’ å byrja ein streng", "particle.invalidOptions": "<PERSON>nn <PERSON>kje tolka partik<PERSON>: %s", "particle.notFound": "«%s» er ein ukjend sm<PERSON>t", "permissions.requires.entity": "Ei eining er kravd fyr’ å køyra detta styrebòdet", "permissions.requires.player": "Ein leikar er kravd fyr’ å køyra detta styrebòdet", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Nær drukket:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "<PERSON>k<PERSON><PERSON> predikat: %s", "quickplay.error.invalid_identifier": "<PERSON><PERSON> <PERSON>kje finna heimen med det gjevne serkjennet", "quickplay.error.realm_connect": "Fekk ikkje samband med Realm", "quickplay.error.realm_permission": "Vantar løyve til å få samband med denne Realmen", "quickplay.error.title": "<PERSON><PERSON> <PERSON>kje snar<PERSON>", "realms.configuration.region.australia_east": "Ny-Sud-Wales i Australia", "realms.configuration.region.australia_southeast": "Viktoria i Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "Indland", "realms.configuration.region.central_us": "Iowa i Sambandsstatom", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia i Sambandsstatom", "realms.configuration.region.east_us_2": "Nord-Karolina i Sambandsstatom", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Aust-Japan", "realms.configuration.region.japan_west": "Vest-Japan", "realms.configuration.region.korea_central": "Sud-Korea", "realms.configuration.region.north_central_us": "Illinois i Sambandsstatom", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Teksas i Sambandsstatom", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sverike", "realms.configuration.region.uae_north": "Sameinte Arabiske Fyrstedøme (UAE)", "realms.configuration.region.uk_south": "Sud-England", "realms.configuration.region.west_central_us": "Utah i Sambandsstatom", "realms.configuration.region.west_europe": "Nìderlond", "realms.configuration.region.west_us": "Kalifornia i Sambandsstatom", "realms.configuration.region.west_us_2": "Washington i Sambandsstatom", "realms.configuration.region_preference.automatic_owner": "Sjølvverkande (realmseigaren)", "realms.configuration.region_preference.automatic_player": "Sjølvverkande (fyrste som verd med i øykt)", "realms.missing.snapshot.error.text": "Realms er fyre tidi ikkje studd i røyneutgåvor", "recipe.notFound": "Ukjend uppskrift: %s", "recipe.toast.description": "Sjå i emneboki di", "recipe.toast.title": "Nye uppskrifter upplæste!", "record.nowPlaying": "Spela nett no: %s", "recover_world.bug_tracker": "Seg ifrå um lus", "recover_world.button": "Freista å heimta upp att", "recover_world.done.failed": "<PERSON><PERSON> <PERSON>kje heimta upp att frå fyrre tilstòda.", "recover_world.done.success": "Uppatt-heimting var vellukkat!", "recover_world.done.title": "Uppatt-he<PERSON>ting fullgjord", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON> skiln", "recover_world.issue.none": "Ingi lyte", "recover_world.message": "Fylgjande lyte ovrade seg medan spelet freistade å lesa heimsfalden «%s».\nDet kann vera mogeleg å heimta heimen upp att frå ei eldre til<PERSON>ò<PERSON>, elder so kann du segja ifrå um lytet i lusespòraren.", "recover_world.no_fallback": "<PERSON><PERSON> tilst<PERSON> å heimta upp att frå er tilgjengd", "recover_world.restore": "Freista å heimta upp att", "recover_world.restoring": "Freistar å heimta heimen upp att...", "recover_world.state_entry": "Tilstòda frå %s: ", "recover_world.state_entry.unknown": "ukjend", "recover_world.title": "<PERSON><PERSON> ’kje lada inn heim", "recover_world.warning": "Kunde ’kje lada inn heimssamandrag", "resourcePack.broken_assets": "FANN BROTNE TILFANG", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON> mots<PERSON>d", "resourcePack.load_fail": "<PERSON><PERSON> <PERSON>kje lada tilfang å nyo", "resourcePack.programmer_art.name": "Programmerarkunst", "resourcePack.runtime_failure": "Støytte på lyte med tilfangspakke", "resourcePack.server.name": "Heimsstyrte tilfang", "resourcePack.title": "<PERSON><PERSON>", "resourcePack.vanilla.description": "Den fyrevalde utsjånaden og kjensla av Minecraft", "resourcePack.vanilla.name": "Fyrevalt", "resourcepack.downloading": "Lader ned tilfangs<PERSON>ke", "resourcepack.progress": "Lader ned skiln (%s MB)...", "resourcepack.requesting": "Sender fyrespurnad...", "screenshot.failure": "Kunde ’kje spara skjermbilæte: %s", "screenshot.success": "Sparde skjermbilæte som %s", "selectServer.add": "Legg til tenar", "selectServer.defaultName": "Minecraft-tenar", "selectServer.delete": "Sletta", "selectServer.deleteButton": "Sletta", "selectServer.deleteQuestion": "Er du trygg på at du vil taka burt denne tenaren?", "selectServer.deleteWarning": "«%s» kjem til å vera tapad i òll æva! (<PERSON><PERSON> lenge!)", "selectServer.direct": "Beint samband", "selectServer.edit": "<PERSON>", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON><PERSON>)", "selectServer.refresh": "Lad inn att", "selectServer.select": "Ver med i tenar", "selectWorld.access_failure": "Fekk ikkje tilgjenge til heim", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> løyve til fusk", "selectWorld.allowCommands.info": "Styrebòd som /gamemode, /xp", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON> løyve til fusk", "selectWorld.backupEraseCache": "Sletta data i snarminnet", "selectWorld.backupJoinConfirmButton": "Tak trygdaravrit og lad inn", "selectWorld.backupJoinSkipButton": "Eg veit kvat eg gjerer!", "selectWorld.backupQuestion.customized": "Tilmåtade heimar ero ’kje lenger studde", "selectWorld.backupQuestion.downgrade": "Å nedgradera heimen er ’kje studt", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON>r som nytta utrøynelege val ero ’kje studde", "selectWorld.backupQuestion.snapshot": "Vil du røynlega lada inn denne heimen?", "selectWorld.backupWarning.customized": "Me stydja diverre ikkje tilmåtade heimar i denne utgåva av Minecraft. Me kunna enn-no lada inn denne heimen, og halda på alt som det var, men alle nye tilemnade landskap verda ikkje lenger tilmåtade. Me orsaka fyre vanden!", "selectWorld.backupWarning.downgrade": "<PERSON>ne heimen var sidst spelad i utgåva %s; du nyttar utgåva %s. <PERSON> setja atter ein heim kann valda øydeleggjing. D’er uvisst um han kjem til å lada inn elder verka som han skal. Um du, trass i detta, ynsk<PERSON> å halda fram gjerer du klokt i å taka eit avrit fyrst.", "selectWorld.backupWarning.experimental": "Denne heimen nyttar utrøyningelege val som kunna stansa å verka nær som helst. D’er ’kje visst um ho kjem til å lada inn elder verka. Ver varsam!", "selectWorld.backupWarning.snapshot": "Denne heimen vardt sidst spelad i utgåva %s; du nyttar utgåva %s. Tak eit trygdaravrit i tilfelle heimen brotnar.", "selectWorld.bonusItems": "Attpåkista", "selectWorld.cheats": "Fusk", "selectWorld.commands": "Fusk", "selectWorld.conversion": "Må verda umskapad!", "selectWorld.conversion.tooltip": "Du må opna denne heimen i ei eldre utgåva (som 1.6.4) fyr’ å kunna skapa ’nne um trygt", "selectWorld.create": "Ska<PERSON> ny heim", "selectWorld.customizeType": "Tilmåta", "selectWorld.dataPacks": "Datapakkar", "selectWorld.data_read": "Les heimsdata...", "selectWorld.delete": "Sletta", "selectWorld.deleteButton": "Sletta", "selectWorld.deleteQuestion": "Er du trygg på at du vil sletta denne heimen?", "selectWorld.deleteWarning": "«%s» kjem til å vera tapad i òll æva! (<PERSON><PERSON> lenge!)", "selectWorld.delete_failure": "<PERSON><PERSON> <PERSON>kje sletta heim", "selectWorld.edit": "<PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON>", "selectWorld.edit.backupCreated": "Tok trygdaravrit: %s", "selectWorld.edit.backupFailed": "Trygdaravrit var mislukkat", "selectWorld.edit.backupFolder": "<PERSON>na falden fyre trygdaravrit", "selectWorld.edit.backupSize": "storleike: %s MB", "selectWorld.edit.export_worldgen_settings": "Flyt ut val fyr’ he<PERSON>skaping", "selectWorld.edit.export_worldgen_settings.failure": "Utflutnad var mislukkad", "selectWorld.edit.export_worldgen_settings.success": "Utflutt", "selectWorld.edit.openFolder": "<PERSON><PERSON>", "selectWorld.edit.optimize": "<PERSON><PERSON> heim", "selectWorld.edit.resetIcon": "Set attende bilæte", "selectWorld.edit.save": "Spar", "selectWorld.edit.title": "<PERSON> heim", "selectWorld.enterName": "<PERSON><PERSON>nam<PERSON>", "selectWorld.enterSeed": "Frjo til heimsskaparen", "selectWorld.experimental": "Utrøyneleg", "selectWorld.experimental.details": "Utgreiding", "selectWorld.experimental.details.entry": "Kravde utrøynelege verkende: %s", "selectWorld.experimental.details.title": "Krav fyr’ utrøynelege verkende", "selectWorld.experimental.message": "Ver varsam!\nDenne reidingi krev verkende som me framleides halda på å laga til. Heimen din kjem kann henda til å rynja i hop, brotna, elder stodga å verka med seinre utg<PERSON>.", "selectWorld.experimental.title": "Åtvaring um utrøynelege verkende", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Utrøyningar ero mogelege nye verkende. <PERSON>g kunna brotna, so ver varsam. Du kann ’kje slå av utrøyningar etter at du heve skapat heimen.", "selectWorld.futureworld.error.text": "Nokot gjekk galet medan du freistade å lada ein heim frå ei framtidug utgåva. Detta var ei vågal åtgjerd til å byrja med; or<PERSON><PERSON> at det ikkje gjekk.", "selectWorld.futureworld.error.title": "Nokot gjekk galet!", "selectWorld.gameMode": "Spelstòda", "selectWorld.gameMode.adventure": "Æventyr", "selectWorld.gameMode.adventure.info": "Same som attlivingst<PERSON>da, men ein kann ’kje leggja ned elder taka burt blekker.", "selectWorld.gameMode.adventure.line1": "Same som attlìvna<PERSON>ò<PERSON>, men blekker kunna ’kje", "selectWorld.gameMode.adventure.line2": "verda lagde ned elder tekne burt", "selectWorld.gameMode.creative": "Skapande", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bygg og grenska utan grensor. <PERSON> kann fljuga, hava uendelege bygg<PERSON><PERSON>ne, og skræmsl kunna ’kje skada deg.", "selectWorld.gameMode.creative.line1": "Uav<PERSON><PERSON> tilfang, fri fljuging og", "selectWorld.gameMode.creative.line2": "br<PERSON>t blekker på augneblinken", "selectWorld.gameMode.hardcore": "<PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Attlìvnadsstòda læst til vandleiken «vandt». Du kann ’kje standa upp att um du døyr.", "selectWorld.gameMode.hardcore.line1": "Same som attlìvnadsstòda, læst på vandaste", "selectWorld.gameMode.hardcore.line2": "<PERSON><PERSON><PERSON>, og med berre eitt liv", "selectWorld.gameMode.spectator": "Tilskòdar", "selectWorld.gameMode.spectator.info": "<PERSON> kann sjå, men ikk<PERSON> røra.", "selectWorld.gameMode.spectator.line1": "<PERSON> kann sjå, men ikk<PERSON> røra", "selectWorld.gameMode.survival": "Attlìvnad", "selectWorld.gameMode.survival.info": "Grenska ein løyndomsfull heim der du byggjer, samnar, emnar, og slåst mot skræmsl.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON> etter til<PERSON>, emna, auka i", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON><PERSON>, st<PERSON><PERSON>, helsa og hunger", "selectWorld.gameRules": "Speltakstrar", "selectWorld.import_worldgen_settings": "Flytja inn val", "selectWorld.import_worldgen_settings.failure": "Ku<PERSON> ’kje flytja inn val", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "<PERSON>nn <PERSON>kje opna denne heimen i denne utgåva.\nHan var sidst spelad i utgåva %s.", "selectWorld.incompatible.info": "Usemjande utgåva: %s", "selectWorld.incompatible.title": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.incompatible.tooltip": "<PERSON>nn <PERSON>kje opna denne heimen av di han var skapad av ei usemjande utgåva.", "selectWorld.incompatible_series": "Skapad av ei usemjande utgåva", "selectWorld.load_folder_access": "<PERSON><PERSON> ’kje lesa elder opna falden der spelheimarne ero sparde!", "selectWorld.loading_list": "Lader inn heimslista", "selectWorld.locked": "Læst av eit annat køyrande høve av Minecraft", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, skips<PERSON><PERSON> o. s. fr.", "selectWorld.mapType": "Heimsslag", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "Fleire heimsval...", "selectWorld.newWorld": "<PERSON><PERSON> heim", "selectWorld.recreate": "Atterskapa", "selectWorld.recreate.customized.text": "Tilmåtade heimar ero ’kje lenger studde i denne utgåva av Minecraft. Me kunna freista å atterskapa honom med det same frjoet og same gjerder, men alle landskapstilmåtingar verda tapade. Me orsaka fyre vanden!", "selectWorld.recreate.customized.title": "Tilmåtade heimar ero ’kje lenger studde", "selectWorld.recreate.error.text": "Nokot gjekk galet medan du freistade å atterskapa heim.", "selectWorld.recreate.error.title": "Nokot gjekk galet!", "selectWorld.resource_load": "<PERSON><PERSON><PERSON> tilfang...", "selectWorld.resultFolder": "Verd spard i:", "selectWorld.search": "leita etter heimar", "selectWorld.seedInfo": "Lat standa tomt fyre slumpevalt frjo", "selectWorld.select": "<PERSON><PERSON><PERSON> vald heim", "selectWorld.targetFolder": "Sparingfalde: %s", "selectWorld.title": "<PERSON><PERSON> heim", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON><PERSON> vardt spard i ei nyare utgåva.", "selectWorld.tooltip.fromNewerVersion2": "Å lada inn denne heimen kann føra med seg vandar!", "selectWorld.tooltip.snapshot1": "Ikkje gløym å taka trygdaravrit av denne heimen", "selectWorld.tooltip.snapshot2": "fyrr du lader ’om inn i denne røyneutgåva.", "selectWorld.unable_to_load": "Ku<PERSON> ’kje lada inn heimar", "selectWorld.version": "Utgåva:", "selectWorld.versionJoinButton": "Lad inn same kvat", "selectWorld.versionQuestion": "Vil du røynlega lada inn denne heimen?", "selectWorld.versionUnknown": "ukjent", "selectWorld.versionWarning": "Denne heimen vardt sidst spelad i utgåva %s og å lada ’nom inn i denne utgåva kan valda øydeleggjing!", "selectWorld.warning.deprecated.question": "Nokre verkende ero ut-elde og koma ikkje til å verka i framtidi. Vil du halda fram?", "selectWorld.warning.deprecated.title": "Åtvaring! Desse vali nytta ut-elde verkende", "selectWorld.warning.experimental.question": "Desse vali ero utrø<PERSON>lege og kann stodga å verka ein dag. Vil du halda fram?", "selectWorld.warning.experimental.title": "Åtvaring! Desse vali nytta utrøynelege verkende", "selectWorld.warning.lowDiskSpace.description": "<PERSON>’er ’kje myket rom att på einingi di. Å ganga tom fyre diskrom medan du spelar kann føra til at heimen di verd skadd.", "selectWorld.warning.lowDiskSpace.title": "Åtvaring! Litet diskrom!", "selectWorld.world": "<PERSON><PERSON>", "sign.edit": "<PERSON> skiltbòd", "sleep.not_possible": "<PERSON>gi mengd med svevn kann få deg gjenom denne notti", "sleep.players_sleeping": "%s/%s leikarar søv", "sleep.skipping_night": "<PERSON><PERSON><PERSON> gjenom denne notti", "slot.only_single_allowed": "Berre einskilde òp løyvde; fekk «%s»", "slot.unknown": "«%s» er eit ukjent òp", "snbt.parser.empty_key": "<PERSON><PERSON>l kann ’kje vera tom", "snbt.parser.expected_binary_numeral": "Binær-tal var ventat", "snbt.parser.expected_decimal_numeral": "Desimaltal var ventat", "snbt.parser.expected_float_type": "Flòt-tal var ventat", "snbt.parser.expected_hex_escape": "Ei teiknrekkja med med lengdi %s var ventat", "snbt.parser.expected_hex_numeral": "Heksidesimalt tal var ventat", "snbt.parser.expected_integer_type": "Talrekkja med heiltal var ventat", "snbt.parser.expected_non_negative_number": "Ikkje-negativt tal var ventat", "snbt.parser.expected_number_or_boolean": "Tal elder boolsk verde var ventat", "snbt.parser.expected_string_uuid": "Streng som stend fyre ein gild UUID var ventad", "snbt.parser.expected_unquoted_string": "Gild streng utan hermeteikn var ventat", "snbt.parser.infinity_not_allowed": "Ikkje-endelege tal er ’kje løyvde", "snbt.parser.invalid_array_element_type": "Ugildt slag av uppsets-element", "snbt.parser.invalid_character_name": "Ugildt namn på Unicode-teikn", "snbt.parser.invalid_codepoint": "Ugildt verde på Unicode-teikn: %s", "snbt.parser.invalid_string_contents": "<PERSON><PERSON><PERSON><PERSON> strenginnehald", "snbt.parser.invalid_unquoted_start": "<PERSON><PERSON><PERSON> utan hermet<PERSON>kn kunna ’kje byrja med taltei<PERSON> 0–9, +, elder -", "snbt.parser.leading_zero_not_allowed": "Desimaltal kunna ’kje byrja med 0", "snbt.parser.no_such_operation": "<PERSON><PERSON> sovor<PERSON>g<PERSON>: %s", "snbt.parser.number_parse_failure": "<PERSON><PERSON> ’kje tyda talet: %s", "snbt.parser.undescore_not_allowed": "Tòl få ’kje byrja elder enda på understrìk", "soundCategory.ambient": "Umgjevnad", "soundCategory.block": "<PERSON><PERSON><PERSON>", "soundCategory.hostile": "Fiendslege kvìkende", "soundCategory.master": "Hovudljodstyrke", "soundCategory.music": "Toneleik", "soundCategory.neutral": "Vìnsame kvìkende", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Skivespelar/notebl.", "soundCategory.ui": "Nytar<PERSON>lat<PERSON>", "soundCategory.voice": "Røyst/tala", "soundCategory.weather": "<PERSON><PERSON><PERSON>", "spectatorMenu.close": "Lat att skrå", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON> sida", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON> sida", "spectatorMenu.root.prompt": "Kyv ein knapp fyr’ å velja eit styrebòd, og atter fyr’ å nytta det.", "spectatorMenu.team_teleport": "Fjerrflyt deg til lagslem", "spectatorMenu.team_teleport.prompt": "Vel eit lag å fjerrflytja til", "spectatorMenu.teleport": "Fjerrflyt deg til leikar", "spectatorMenu.teleport.prompt": "Vel ein leikar å fjerrflytja til", "stat.generalButton": "<PERSON><PERSON><PERSON>", "stat.itemsButton": "<PERSON>g", "stat.minecraft.animals_bred": "Dyr alne", "stat.minecraft.aviate_one_cm": "Veglengd flogi med skalvenger", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON><PERSON><PERSON> ringde", "stat.minecraft.boat_one_cm": "Veglengd i båt", "stat.minecraft.clean_armor": "Brynjestykke reinsat", "stat.minecraft.clean_banner": "<PERSON><PERSON> v<PERSON>", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON> laugade", "stat.minecraft.climb_one_cm": "Veglengd klivi", "stat.minecraft.crouch_one_cm": "Veglengd kropi", "stat.minecraft.damage_absorbed": "Skade uppteken", "stat.minecraft.damage_blocked_by_shield": "S<PERSON><PERSON> hindrad med skjold", "stat.minecraft.damage_dealt": "Skade gjord", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> gjeven (uppteken)", "stat.minecraft.damage_dealt_resisted": "Skade gjord (motstaden)", "stat.minecraft.damage_resisted": "Skade motstaden", "stat.minecraft.damage_taken": "Skade teken", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "Ting sleppte", "stat.minecraft.eat_cake_slice": "Kakestykke etne", "stat.minecraft.enchant_item": "Ting galdrade", "stat.minecraft.fall_one_cm": "Veglengd falli", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON> fyllte", "stat.minecraft.fish_caught": "Fisk fangad", "stat.minecraft.fly_one_cm": "Veglengd flogi", "stat.minecraft.happy_ghast_one_cm": "Veglengd på gladvært ghast", "stat.minecraft.horse_one_cm": "Veglengd på øyk", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON> ranns<PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON> ran<PERSON>", "stat.minecraft.interact_with_anvil": "Verksemd med sted", "stat.minecraft.interact_with_beacon": "Verksemd med vìte", "stat.minecraft.interact_with_blast_furnace": "Verksemd med smelteomn", "stat.minecraft.interact_with_brewingstand": "Verksemd med bryggjestod", "stat.minecraft.interact_with_campfire": "Verksemd med bål", "stat.minecraft.interact_with_cartography_table": "Verksemd med kortteiknarbord", "stat.minecraft.interact_with_crafting_table": "Verksemd med emnebord", "stat.minecraft.interact_with_furnace": "Verksemd med omn", "stat.minecraft.interact_with_grindstone": "Verksemd med slipestein", "stat.minecraft.interact_with_lectern": "Verksemd med bokstol", "stat.minecraft.interact_with_loom": "Verksemd med vevstol", "stat.minecraft.interact_with_smithing_table": "Verksemd med smidjebord", "stat.minecraft.interact_with_smoker": "Verksemd med røykjaromn", "stat.minecraft.interact_with_stonecutter": "Verksemd med steinskjere", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "Spel endade", "stat.minecraft.minecart_one_cm": "Veglengd med malmvogn", "stat.minecraft.mob_kills": "Kvìkende drepne", "stat.minecraft.open_barrel": "Tunner opnade", "stat.minecraft.open_chest": "Kistor opnade", "stat.minecraft.open_enderchest": "Enderkistor opnade", "stat.minecraft.open_shulker_box": "Shulkerhus opnade", "stat.minecraft.pig_one_cm": "Veglengd på svin", "stat.minecraft.play_noteblock": "Noteblekker spelade", "stat.minecraft.play_record": "<PERSON><PERSON> spelade", "stat.minecraft.play_time": "Tid spelat", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> dre<PERSON>ne", "stat.minecraft.pot_flower": "Vokstrar sette i potta", "stat.minecraft.raid_trigger": "<PERSON><PERSON><PERSON> utløys<PERSON>", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON> vunne", "stat.minecraft.sleep_in_bed": "<PERSON>er sovet i seng", "stat.minecraft.sneak_time": "Tid smoget", "stat.minecraft.sprint_one_cm": "Vegleng<PERSON>", "stat.minecraft.strider_one_cm": "Veglengd på stìgar", "stat.minecraft.swim_one_cm": "Veglengd sumd", "stat.minecraft.talked_to_villager": "<PERSON><PERSON> med bygdarfolk", "stat.minecraft.target_hit": "Skotskivor råkade", "stat.minecraft.time_since_death": "Tid sidan sidste daude", "stat.minecraft.time_since_rest": "Tid sidan sidste svevn", "stat.minecraft.total_world_time": "Tid med heimen open", "stat.minecraft.traded_with_villager": "Bytt med bygdarfolk", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.tune_noteblock": "Toneleiksblekker stillte", "stat.minecraft.use_cauldron": "Vatn teket frå grytor", "stat.minecraft.walk_on_water_one_cm": "Veglengd gjengi på vatn", "stat.minecraft.walk_one_cm": "Veglengd gje<PERSON>i", "stat.minecraft.walk_under_water_one_cm": "Veglengd gjengi under vatn", "stat.mobsButton": "Kvìkende", "stat_type.minecraft.broken": "<PERSON><PERSON> brot<PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON>", "stat_type.minecraft.dropped": "Sleppt", "stat_type.minecraft.killed": "Du drap %s %s", "stat_type.minecraft.killed.none": "Du hev’ aldri drepet %s", "stat_type.minecraft.killed_by": "%s drap deg %s gong(er)", "stat_type.minecraft.killed_by.none": "Du hev’ aldri voret drepen/-i av %s", "stat_type.minecraft.mined": "<PERSON><PERSON> gravet", "stat_type.minecraft.picked_up": "Teket upp", "stat_type.minecraft.used": "<PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "FINN", "structure_block.button.load": "LAD INN", "structure_block.button.save": "SPAR", "structure_block.custom_data": "Namn på eigenlagad datamerkjelapp", "structure_block.detect_size": "Finn bygnadstorleike og stad:", "structure_block.hover.corner": "Hyrna: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Lad inn: %s", "structure_block.hover.save": "Spar: %s", "structure_block.include_entities": "Hav med einingar:", "structure_block.integrity": "Bygnadsheilskap og frjo", "structure_block.integrity.integrity": "Bygnadsheilskap", "structure_block.integrity.seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.invalid_structure_name": "«%s» er eit ugildt bygnadsnamn", "structure_block.load_not_found": "Bygnaden «%s» er ’kje tilgjengd", "structure_block.load_prepare": "Staden til bygnad «%s» gjord klår", "structure_block.load_success": "Bygnad ladd inn frå «%s»", "structure_block.mode.corner": "Hyrna", "structure_block.mode.data": "Data", "structure_block.mode.load": "Lad inn", "structure_block.mode.save": "Spar", "structure_block.mode_info.corner": "Hyrnestòda – merke fyre stad og storleike", "structure_block.mode_info.data": "Datastòda – merke fyre spellogikk", "structure_block.mode_info.load": "Ladestòda – lad inn frå skiln", "structure_block.mode_info.save": "Spar stòda - skriv til skiln", "structure_block.position": "Relativ stad", "structure_block.position.x": "relativ stad x", "structure_block.position.y": "relativ stad y", "structure_block.position.z": "relativ stad z", "structure_block.save_failure": "Kunde ’kje spara bygnaden «%s»", "structure_block.save_success": "Bygnad spard som «%s»", "structure_block.show_air": "<PERSON><PERSON> usyn<PERSON>e blekker:", "structure_block.show_boundingbox": "<PERSON><PERSON> um<PERSON>:", "structure_block.size": "Bygnadsstorleike", "structure_block.size.x": "bygnadsstorleike x", "structure_block.size.y": "bygnadsstorle<PERSON> y", "structure_block.size.z": "bygnadsstorleike z", "structure_block.size_failure": "<PERSON>nde ’kje finna storleiken åt bygnaden. Legg til hyrna med samsvarande bygnadsnamn", "structure_block.size_success": "Storleike er funden fyre «%s»", "structure_block.strict": "Streng nedsetjing:", "structure_block.structure_name": "Bygnadsnamn", "subtitles.ambient.cave": "Nefst ljod", "subtitles.ambient.sound": "Nefst ljod", "subtitles.block.amethyst_block.chime": "Ametyst kling", "subtitles.block.amethyst_block.resonate": "Ametysk kling", "subtitles.block.anvil.destroy": "Sted brotnade", "subtitles.block.anvil.land": "Sted landade", "subtitles.block.anvil.use": "Sted nyttat", "subtitles.block.barrel.close": "Tunna verd stengd", "subtitles.block.barrel.open": "Tunna verd opnad", "subtitles.block.beacon.activate": "<PERSON><PERSON><PERSON> verd slegen på", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON> verd slegen av", "subtitles.block.beacon.power_select": "Vìtekraft vald", "subtitles.block.beehive.drip": "Huning dryp", "subtitles.block.beehive.enter": "Bia fer inn i bol", "subtitles.block.beehive.exit": "Bia fer or bol", "subtitles.block.beehive.shear": "Sòks skrapar", "subtitles.block.beehive.work": "<PERSON>ior verka", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON><PERSON> gjev <PERSON>", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Droplauv kvelv nedyver", "subtitles.block.big_dripleaf.tilt_up": "Droplauv kvelv uppyver", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bublar", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON> s<PERSON>ka", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> fl<PERSON>", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON> k<PERSON>v<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "<PERSON><PERSON>", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON> verd stengd", "subtitles.block.chest.locked": "<PERSON><PERSON> læ<PERSON>", "subtitles.block.chest.open": "<PERSON>sta verd opnad", "subtitles.block.chorus_flower.death": "Korblom visnar", "subtitles.block.chorus_flower.grow": "Korblom veks", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.empty": "Bòstadbing tømd", "subtitles.block.composter.fill": "Bòstadbing fyllt", "subtitles.block.composter.ready": "Bòstadbing gjerer b<PERSON>stad", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON><PERSON> verd slegen på", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON> bl<PERSON><PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON>l<PERSON><PERSON>", "subtitles.block.conduit.deactivate": "Flødar verd slegen av", "subtitles.block.copper_bulb.turn_off": "Koparpera verd slegi av", "subtitles.block.copper_bulb.turn_on": "Kopar<PERSON>a verd slegi på", "subtitles.block.copper_trapdoor.close": "Lem verd stengd", "subtitles.block.copper_trapdoor.open": "Lem verd opnad", "subtitles.block.crafter.craft": "<PERSON><PERSON> emnar", "subtitles.block.crafter.fail": "<PERSON><PERSON> kunde ’kje emna", "subtitles.block.creaking_heart.hurt": "K<PERSON><PERSON><PERSON> brakar", "subtitles.block.creaking_heart.idle": "Nefst ljod", "subtitles.block.creaking_heart.spawn": "Knerkadal vaknar", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON> potta verd fyllt", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON> potta vinglar", "subtitles.block.decorated_pot.shatter": "<PERSON>tta brotnar", "subtitles.block.dispenser.dispense": "<PERSON>g vardt skoten ut", "subtitles.block.dispenser.fail": "Utskjotar var mislukkad", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Turrleiksljod", "subtitles.block.dried_ghast.ambient_water": "Storknat ghast fær att væta", "subtitles.block.dried_ghast.place_in_water": "Storknat ghast verd bløytt", "subtitles.block.dried_ghast.transition": "Storknat ghast kjenner seg beter", "subtitles.block.dry_grass.ambient": "Vindl<PERSON>d", "subtitles.block.enchantment_table.use": "Galdrebord vardt nyttat", "subtitles.block.end_portal.spawn": "<PERSON><PERSON><PERSON><PERSON> verd opnat", "subtitles.block.end_portal_frame.fill": "Enderauga verd fest", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON><PERSON> lèt seg att", "subtitles.block.eyeblossom.idle": "Aug<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.eyeblossom.open": "Augblom opnar seg", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON>d knirkar", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON> vardt sløk<PERSON>", "subtitles.block.firefly_bush.idle": "Eldflugor svirra", "subtitles.block.frogspawn.hatch": "Rovetroll klekkjer", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.generic.break": "Blokk broti", "subtitles.block.generic.fall": "Nokot fell på ei blokk", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Blokk brotnar", "subtitles.block.generic.place": "Blokk verd sett ned", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON> verd skoren", "subtitles.block.hanging_sign.waxed_interact_fail": "Skilt flakrar", "subtitles.block.honey_block.slide": "Glid ned ei huningblokk", "subtitles.block.iron_trapdoor.close": "Lem verd stengd", "subtitles.block.iron_trapdoor.open": "Lem verd opnad", "subtitles.block.lava.ambient": "<PERSON><PERSON> bublar", "subtitles.block.lava.extinguish": "<PERSON><PERSON> fræ<PERSON>", "subtitles.block.lever.click": "Handtak k<PERSON>", "subtitles.block.note_block.note": "Toneblokk spelar", "subtitles.block.pale_hanging_moss.idle": "Nefst ljod", "subtitles.block.piston.move": "Stempel rører seg", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> ned i g<PERSON>ta", "subtitles.block.pointed_dripstone.drip_water": "Vatn dryp", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Vatn dryp ned i g<PERSON>ta", "subtitles.block.pointed_dripstone.land": "Stalaktitt fyk ned", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.portal.travel": "Lìdståk veiknar", "subtitles.block.portal.trigger": "Lìdståk verd sterkare", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.pumpkin.carve": "Sòks skjer", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.ambient": "Uppstòdeakkjer svirrar", "subtitles.block.respawn_anchor.charge": "Uppstòdeakkjer er ladd", "subtitles.block.respawn_anchor.deplete": "Uppstòdeakkjer verd tømd", "subtitles.block.respawn_anchor.set_spawn": "Uppstòdeakkjer set uppstòdestad", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sand.wind": "Vindl<PERSON>d", "subtitles.block.sculk.charge": "Sculk bublar", "subtitles.block.sculk.spread": "Sculk breider seg", "subtitles.block.sculk_catalyst.bloom": "Sculkeskundar blømer", "subtitles.block.sculk_sensor.clicking": "Sculkegaumar <PERSON>", "subtitles.block.sculk_sensor.clicking_stop": "Sculkegaumar stodgar å klikka", "subtitles.block.sculk_shrieker.shriek": "Sculkeskrikar skrik", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON><PERSON> verd stengt", "subtitles.block.shulker_box.open": "<PERSON>lker<PERSON> verd opnat", "subtitles.block.sign.waxed_interact_fail": "Skilt flakrar", "subtitles.block.smithing_table.use": "Smidjebord nyttat", "subtitles.block.smoker.smoke": "Røykjaromn ryk", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON><PERSON><PERSON> sprekk", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON> verp", "subtitles.block.sponge.absorb": "Svamp syg", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON>r verd pilad", "subtitles.block.trapdoor.close": "Lem verd stengd", "subtitles.block.trapdoor.open": "Lem verd opnad", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Illspåen ting fyrebur seg", "subtitles.block.trial_spawner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.trial_spawner.ambient_charged": "<PERSON><PERSON>p<PERSON><PERSON> rø<PERSON> sprakar", "subtitles.block.trial_spawner.ambient_ominous": "<PERSON><PERSON>p<PERSON><PERSON> rø<PERSON> sprakar", "subtitles.block.trial_spawner.charge_activate": "Jarteign gløyper røyneskapar", "subtitles.block.trial_spawner.close_shutter": "Røyneskapar stengjer", "subtitles.block.trial_spawner.detect_player": "Røyneskapar lader upp", "subtitles.block.trial_spawner.eject_item": "Røyneskapar slepper ut ting", "subtitles.block.trial_spawner.ominous_activate": "Jarteign gløyper røyneskapar", "subtitles.block.trial_spawner.open_shutter": "Røyneskapar opnar seg", "subtitles.block.trial_spawner.spawn_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> ting verd slept", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON>lspå<PERSON> ting ovrar seg", "subtitles.block.trial_spawner.spawn_mob": "Røyneskapar skapar kvìkende", "subtitles.block.tripwire.attach": "Snåvetråd verd fest", "subtitles.block.tripwire.click": "Sn<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.activate": "Kvelv kveikjer seg", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON> sprakar", "subtitles.block.vault.close_shutter": "Kvelv stengjer", "subtitles.block.vault.deactivate": "Kvelv sløkk", "subtitles.block.vault.eject_item": "Kvelv slepp ting", "subtitles.block.vault.insert_item": "Kvelv verd læst upp", "subtitles.block.vault.insert_item_fail": "Kvelvupplæsing var mislukkat", "subtitles.block.vault.open_shutter": "Kvelv opnar seg", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON> vandar leikar", "subtitles.block.water.ambient": "Vatn renn", "subtitles.block.wet_sponge.dries": "Svamp turkar", "subtitles.chiseled_bookshelf.insert": "Bok sett inn", "subtitles.chiseled_bookshelf.insert_enchanted": "Runebok sett inn", "subtitles.chiseled_bookshelf.take": "Bok teki ut", "subtitles.chiseled_bookshelf.take_enchanted": "Runebok teki", "subtitles.enchant.thorns.hit": "<PERSON><PERSON>a", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON>l<PERSON><PERSON>nd leitar", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lengtar", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> døyr", "subtitles.entity.allay.hurt": "Hjelpeònd verkjer", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> knisar", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hjel<PERSON>", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kastar", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> gryler", "subtitles.entity.armadillo.brush": "S<PERSON><PERSON>ld verd kostad av", "subtitles.entity.armadillo.death": "Beltedyr døyr", "subtitles.entity.armadillo.eat": "Beltedyr et", "subtitles.entity.armadillo.hurt": "Beltedyr verkjer", "subtitles.entity.armadillo.hurt_reduced": "Beltedyr vernar seg", "subtitles.entity.armadillo.land": "Beltedyr landar", "subtitles.entity.armadillo.peek": "Beltedyr glytter", "subtitles.entity.armadillo.roll": "Beltedyr rullar i hop", "subtitles.entity.armadillo.scute_drop": "Beltedyr feller skjold", "subtitles.entity.armadillo.unroll_finish": "<PERSON>ed<PERSON> retter seg ut", "subtitles.entity.armadillo.unroll_start": "Beltedyr glytter", "subtitles.entity.armor_stand.fall": "Nokot fill", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> høvd", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> skoten", "subtitles.entity.axolotl.attack": "Aksolotl åtek", "subtitles.entity.axolotl.death": "<PERSON>ks<PERSON><PERSON> dø<PERSON>", "subtitles.entity.axolotl.hurt": "Aksolotl verkjer", "subtitles.entity.axolotl.idle_air": "Aksolotl kvittrar", "subtitles.entity.axolotl.idle_water": "Aksolotl kvittrar", "subtitles.entity.axolotl.splash": "Aksolotl skvalpar", "subtitles.entity.axolotl.swim": "Aks<PERSON>tl sym", "subtitles.entity.bat.ambient": "Skjåvengja skrik", "subtitles.entity.bat.death": "Skjåvengja døyr", "subtitles.entity.bat.hurt": "Skjåvengja verkjer", "subtitles.entity.bat.takeoff": "Skjåvengja flyg av garde", "subtitles.entity.bee.ambient": "<PERSON><PERSON> surrar", "subtitles.entity.bee.death": "Bia døyr", "subtitles.entity.bee.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON> surrar", "subtitles.entity.bee.loop_aggressive": "Bia surrar argt", "subtitles.entity.bee.pollinate": "Bia surrar feget", "subtitles.entity.bee.sting": "Bia sting", "subtitles.entity.blaze.ambient": "Loge andar", "subtitles.entity.blaze.burn": "<PERSON><PERSON> sprakar", "subtitles.entity.blaze.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.blaze.hurt": "Loge verkjer", "subtitles.entity.blaze.shoot": "Loge skyt", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON><PERSON> rang<PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.charge": "Floga lader upp", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> bæ<PERSON>", "subtitles.entity.breeze.hurt": "<PERSON>log<PERSON> ve<PERSON>", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON> flyg", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.breeze.inhale": "Floga andar inn", "subtitles.entity.breeze.jump": "<PERSON>loga hoppar", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON> landar", "subtitles.entity.breeze.shoot": "Floga skyt", "subtitles.entity.breeze.slide": "Floga glid", "subtitles.entity.breeze.whirl": "Floga kvervlar", "subtitles.entity.breeze.wind_burst": "Vindlading brest", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON><PERSON> gryl<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON><PERSON> kjem seg", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON><PERSON> et", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON> verd teken på", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON><PERSON> set seg ned", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON><PERSON> stend upp", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON><PERSON> trøder", "subtitles.entity.camel.step_sand": "Ulvalde trøder i sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> tigg", "subtitles.entity.cat.death": "<PERSON><PERSON>", "subtitles.entity.cat.eat": "<PERSON><PERSON> et", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON>", "subtitles.entity.cat.purr": "<PERSON>t mel", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> verp", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.death": "Torsk døyr", "subtitles.entity.cod.flop": "Torsk spralar", "subtitles.entity.cod.hurt": "Torsk verkjer", "subtitles.entity.cow.ambient": "<PERSON><PERSON> r<PERSON>ar", "subtitles.entity.cow.death": "<PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.cow.milk": "Naut verd mylkt", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON> ser", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.attack": "Knerk åtek", "subtitles.entity.creaking.deactivate": "Knerk roar seg", "subtitles.entity.creaking.death": "Knerk dett i hop", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON> stodgar", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON> syner seg", "subtitles.entity.creaking.sway": "<PERSON><PERSON>k verd slegen", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON> kipper", "subtitles.entity.creaking.unfreeze": "Knerk røyver seg", "subtitles.entity.creeper.death": "Creeper døyr", "subtitles.entity.creeper.hurt": "Creeper verkjer", "subtitles.entity.creeper.primed": "Creeper kvæser", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> blistrar", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> et", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.dolphin.jump": "Tumlar hoppar", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON> sym", "subtitles.entity.donkey.ambient": "<PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON>", "subtitles.entity.donkey.chest": "<PERSON><PERSON> fær på kistor", "subtitles.entity.donkey.death": "<PERSON><PERSON>", "subtitles.entity.donkey.eat": "Asne et", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> hop<PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON> kastar l<PERSON>ter", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON> tred", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON> sym", "subtitles.entity.egg.throw": "Egg flyg", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON> verjar styn", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON> ver<PERSON> spralar", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON> verjar bannar", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON> verjar dø<PERSON>", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON> ver<PERSON> spralar", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON> verjar verk<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON> d<PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON> flagsar", "subtitles.entity.ender_dragon.growl": "<PERSON> knurrar", "subtitles.entity.ender_dragon.hurt": "<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON> skyt", "subtitles.entity.ender_eye.death": "Enderauga fell", "subtitles.entity.ender_eye.launch": "Enderauga verd kastat", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> verd <PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON> vopar", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.enderman.teleport": "<PERSON><PERSON><PERSON> fjer<PERSON>t seg", "subtitles.entity.endermite.ambient": "Endermit spring", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.ambient": "Andevek<PERSON><PERSON> <PERSON>", "subtitles.entity.evoker.cast_spell": "Andevekkjar kastar gand", "subtitles.entity.evoker.celebrate": "Andevekkjar fagnar", "subtitles.entity.evoker.death": "Andevekkjar døyr", "subtitles.entity.evoker.hurt": "Andevekkjar verkjer", "subtitles.entity.evoker.prepare_attack": "Andevekkjar fyrebur <PERSON>", "subtitles.entity.evoker.prepare_summon": "Andevekkjar fyrebur andevekkjing", "subtitles.entity.evoker.prepare_wololo": "Andevekkjar fyrebur ynde", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON> bita saman", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON>d", "subtitles.entity.firework_rocket.blast": "Fyrverk smell", "subtitles.entity.firework_rocket.launch": "Fyrverk verd fyrt av", "subtitles.entity.firework_rocket.twinkle": "Fyrverk sprakar", "subtitles.entity.fish.swim": "Skvalp", "subtitles.entity.fishing_bobber.retrieve": "Dupp dregen inn", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON> sk<PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON>", "subtitles.entity.fox.aggro": "<PERSON>", "subtitles.entity.fox.ambient": "Rev pip", "subtitles.entity.fox.bite": "Rev bit", "subtitles.entity.fox.death": "<PERSON>", "subtitles.entity.fox.eat": "Rev et", "subtitles.entity.fox.hurt": "<PERSON>", "subtitles.entity.fox.screech": "Rev kvin", "subtitles.entity.fox.sleep": "<PERSON> snorkar", "subtitles.entity.fox.sniff": "<PERSON> vedrar", "subtitles.entity.fox.spit": "Rev sputtar", "subtitles.entity.fox.teleport": "<PERSON> f<PERSON><PERSON><PERSON>t seg", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.frog.death": "Frosk døyr", "subtitles.entity.frog.eat": "Fros<PERSON> et", "subtitles.entity.frog.hurt": "Frosk verkjer", "subtitles.entity.frog.lay_spawn": "Frosk gyt", "subtitles.entity.frog.long_jump": "Frosk hoppar", "subtitles.entity.generic.big_fall": "Nokot fill", "subtitles.entity.generic.burn": "Brenn", "subtitles.entity.generic.death": "D<PERSON><PERSON>", "subtitles.entity.generic.drink": "Nokon drikk", "subtitles.entity.generic.eat": "Nokon et", "subtitles.entity.generic.explode": "Sprengnad", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON> slo<PERSON>nar", "subtitles.entity.generic.hurt": "Nokot verkjer", "subtitles.entity.generic.small_fall": "Nokot snåvar", "subtitles.entity.generic.splash": "Nokot skvalpar", "subtitles.entity.generic.swim": "Nokot sym", "subtitles.entity.generic.wind_burst": "Vindlading brest", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "G<PERSON><PERSON> skyt", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> kurrar", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> ovrar seg", "subtitles.entity.glow_item_frame.add_item": "Gløderåma verd fyllt", "subtitles.entity.glow_item_frame.break": "Gløderåma brotnar", "subtitles.entity.glow_item_frame.place": "Gløderåma verd sett ned", "subtitles.entity.glow_item_frame.remove_item": "Gløderåma verd tømd", "subtitles.entity.glow_item_frame.rotate_item": "Gløderåma k<PERSON>", "subtitles.entity.glow_squid.ambient": "Glødespruta sym", "subtitles.entity.glow_squid.death": "Glødesp<PERSON><PERSON> dø<PERSON>", "subtitles.entity.glow_squid.hurt": "Glødespru<PERSON> verkjer", "subtitles.entity.glow_squid.squirt": "Glødespruta skyt blèk", "subtitles.entity.goat.ambient": "<PERSON><PERSON> mæ<PERSON>r", "subtitles.entity.goat.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.goat.eat": "Geit et", "subtitles.entity.goat.horn_break": "Bukkehorn brotnar av", "subtitles.entity.goat.hurt": "Geit verkjer", "subtitles.entity.goat.long_jump": "Geit spring", "subtitles.entity.goat.milk": "Geit verd mylkt", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> trappar", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON> stangar", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.goat.step": "Geit tred", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> styn", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> spralar", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON> skyt", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> spralar", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON><PERSON> ghast nynnar", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON><PERSON> ghast døyr", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON> verd stramat", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON><PERSON> ghast er klårt", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON><PERSON> ghast stodgar", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON><PERSON> ghast verkjer", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> verd lø<PERSON>t", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> knurrar", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> knurrar argt", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON>n verd umskapad til zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON>n dreg seg attende", "subtitles.entity.hoglin.step": "<PERSON><PERSON>n tred", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON> fær på brynja", "subtitles.entity.horse.breathe": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON> <PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON> tvis<PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON> fær på sadel", "subtitles.entity.husk.ambient": "S<PERSON>lm styn", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON> verd umskapad til nåe", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.husk.hurt": "S<PERSON>lm ve<PERSON>jer", "subtitles.entity.illusioner.ambient": "Andevek<PERSON><PERSON> <PERSON>", "subtitles.entity.illusioner.cast_spell": "Andevekkjar kastar gand", "subtitles.entity.illusioner.death": "Andevekkjar døyr", "subtitles.entity.illusioner.hurt": "Andevekkjar verkjer", "subtitles.entity.illusioner.mirror_move": "Sjonkvervar flyt på seg", "subtitles.entity.illusioner.prepare_blindness": "Sjonkvervar fyrebur <PERSON>a", "subtitles.entity.illusioner.prepare_mirror": "Sjonkvervar fyrebur spegelbilæte", "subtitles.entity.iron_golem.attack": "Jarnkall åtek", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON><PERSON><PERSON> brotnar", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.iron_golem.repair": "<PERSON><PERSON><PERSON><PERSON> bø<PERSON>", "subtitles.entity.item.break": "Ting brotnar", "subtitles.entity.item.pickup": "Ting verd teken upp", "subtitles.entity.item_frame.add_item": "Tingrå<PERSON> verd fyllt", "subtitles.entity.item_frame.break": "Ting<PERSON><PERSON><PERSON> brotnar", "subtitles.entity.item_frame.place": "<PERSON>g<PERSON><PERSON><PERSON> verd sett ned", "subtitles.entity.item_frame.remove_item": "<PERSON>g<PERSON><PERSON><PERSON> verd tømd", "subtitles.entity.item_frame.rotate_item": "<PERSON>g<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "Tygelknut brotnar", "subtitles.entity.leash_knot.place": "Tygelknut verd knytt", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON> slær ned", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON> br<PERSON> a<PERSON>t", "subtitles.entity.llama.chest": "<PERSON> fær på kistor", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> et", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON> sputtar", "subtitles.entity.llama.step": "<PERSON> trø<PERSON>", "subtitles.entity.llama.swag": "<PERSON> verd prydd", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.squish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside": "Malmvogn ranglar", "subtitles.entity.minecart.inside_underwater": "Malmvogn ranglar under vatn", "subtitles.entity.minecart.riding": "Malmvogn rullar", "subtitles.entity.mooshroom.convert": "Mooshroom skifter ham", "subtitles.entity.mooshroom.eat": "Mooshroom et", "subtitles.entity.mooshroom.milk": "Mooshroom verd mylkt", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom verd mylkt tvilsamt", "subtitles.entity.mule.ambient": "Muldyr skryter", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> kneggjar", "subtitles.entity.mule.chest": "Muld<PERSON> fær på kistor", "subtitles.entity.mule.death": "Muldyr døyr", "subtitles.entity.mule.eat": "Muldyr et", "subtitles.entity.mule.hurt": "Muldyr verkjer", "subtitles.entity.mule.jump": "Muldyr hoppar", "subtitles.entity.painting.break": "Målarstykke brotnar", "subtitles.entity.painting.place": "Målarstykke verd hengt upp", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ar", "subtitles.entity.panda.bite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bit", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et", "subtitles.entity.panda.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda<PERSON><PERSON><PERSON><PERSON> verd <PERSON>", "subtitles.entity.panda.sneeze": "Pandabjørn nys", "subtitles.entity.panda.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tred", "subtitles.entity.panda.worried_ambient": "Pandabjørn <PERSON>", "subtitles.entity.parrot.ambient": "Pavegauk talar", "subtitles.entity.parrot.death": "Pavegauk døyr", "subtitles.entity.parrot.eats": "Pavegauk et", "subtitles.entity.parrot.fly": "Pavegauk flagsar", "subtitles.entity.parrot.hurts": "Pavegauk verkjer", "subtitles.entity.parrot.imitate.blaze": "Pavegauk andar", "subtitles.entity.parrot.imitate.bogged": "Pavegauk ranglar", "subtitles.entity.parrot.imitate.breeze": "Pavegauk svirrar", "subtitles.entity.parrot.imitate.creaking": "Pave<PERSON><PERSON>erkar", "subtitles.entity.parrot.imitate.creeper": "Pavegauk kvæser", "subtitles.entity.parrot.imitate.drowned": "Pavegauk surklar", "subtitles.entity.parrot.imitate.elder_guardian": "Pavegauk styn", "subtitles.entity.parrot.imitate.ender_dragon": "Pavegauk brølar", "subtitles.entity.parrot.imitate.endermite": "Pavegauk spring", "subtitles.entity.parrot.imitate.evoker": "Pavegauk mumlar", "subtitles.entity.parrot.imitate.ghast": "Pavegauk skrik", "subtitles.entity.parrot.imitate.guardian": "Pavegauk styn", "subtitles.entity.parrot.imitate.hoglin": "Pavegauk knurrar", "subtitles.entity.parrot.imitate.husk": "Pavegauk styn", "subtitles.entity.parrot.imitate.illusioner": "Pavegauk mumlar", "subtitles.entity.parrot.imitate.magma_cube": "Pavegauk klaskar", "subtitles.entity.parrot.imitate.phantom": "Pavegauk skrik", "subtitles.entity.parrot.imitate.piglin": "Pavegauk gryntar", "subtitles.entity.parrot.imitate.piglin_brute": "Pavegauk gryntar", "subtitles.entity.parrot.imitate.pillager": "Pavegauk mumlar", "subtitles.entity.parrot.imitate.ravager": "Pavegauk gryler", "subtitles.entity.parrot.imitate.shulker": "Pavegauk lurkar", "subtitles.entity.parrot.imitate.silverfish": "Pavegauk kvæser", "subtitles.entity.parrot.imitate.skeleton": "Pavegauk ranglar", "subtitles.entity.parrot.imitate.slime": "Pavegauk klaskar", "subtitles.entity.parrot.imitate.spider": "Pavegauk kvæser", "subtitles.entity.parrot.imitate.stray": "Pavegauk ranglar", "subtitles.entity.parrot.imitate.vex": "Pavegauk møder", "subtitles.entity.parrot.imitate.vindicator": "Pave<PERSON><PERSON> murrar", "subtitles.entity.parrot.imitate.warden": "Pavegauk kvin", "subtitles.entity.parrot.imitate.witch": "Pavegauk knisar", "subtitles.entity.parrot.imitate.wither": "Pavegauk argast", "subtitles.entity.parrot.imitate.wither_skeleton": "Pavegauk ranglar", "subtitles.entity.parrot.imitate.zoglin": "Pavegauk knurrar", "subtitles.entity.parrot.imitate.zombie": "Pavegauk styn", "subtitles.entity.parrot.imitate.zombie_villager": "Pavegauk styn", "subtitles.entity.phantom.ambient": "Tankefoster skrik", "subtitles.entity.phantom.bite": "Tankefoster bit", "subtitles.entity.phantom.death": "Tankefoster døyr", "subtitles.entity.phantom.flap": "Tankefoster flaksar", "subtitles.entity.phantom.hurt": "Tankefoster verkjer", "subtitles.entity.phantom.swoop": "Tankefoster høgg", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON> fær på sadel", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> høg<PERSON><PERSON>r ting", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> frø<PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> frøser argt", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> verd umskapad til piglinnåe", "subtitles.entity.piglin.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.piglin.jealous": "Piglin fnøser av ovund", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> dreg seg attende", "subtitles.entity.piglin.step": "<PERSON><PERSON> tred", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> styn", "subtitles.entity.piglin_brute.angry": "Piglinr<PERSON><PERSON>n s<PERSON> a<PERSON>t", "subtitles.entity.piglin_brute.converted_to_zombified": "Pig<PERSON>r<PERSON><PERSON>n verd skapa om til piglinnåe", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.piglin_brute.hurt": "Piglinråskinn verk<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tred", "subtitles.entity.pillager.ambient": "<PERSON>ns<PERSON><PERSON> mumlar", "subtitles.entity.pillager.celebrate": "Ransbue fagnar", "subtitles.entity.pillager.death": "Ransbue døyr", "subtitles.entity.pillager.hurt": "Ransbue verkjer", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Atterslagsåtak", "subtitles.entity.player.attack.strong": "Sterkt åtak", "subtitles.entity.player.attack.sweep": "Strjukande <PERSON>", "subtitles.entity.player.attack.weak": "Veikt åtak", "subtitles.entity.player.burp": "Rap", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> frys", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> brenn", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> plingar", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON> f<PERSON><PERSON> seg", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brummar", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "Flaska brotnar", "subtitles.entity.potion.throw": "Flaska verd kastad", "subtitles.entity.puffer_fish.blow_out": "Igulfisk krepp", "subtitles.entity.puffer_fish.blow_up": "Igulfisk blæs seg upp", "subtitles.entity.puffer_fish.death": "Igulfisk døyr", "subtitles.entity.puffer_fish.flop": "Igulfisk spralar", "subtitles.entity.puffer_fish.hurt": "Igulfisk verkjer", "subtitles.entity.puffer_fish.sting": "Igulfisk sting", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON> pip", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> hoppar", "subtitles.entity.ravager.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.ravager.attack": "Herjar bit", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON> fagnar", "subtitles.entity.ravager.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON> tred", "subtitles.entity.ravager.stunned": "<PERSON><PERSON> verd døyvd", "subtitles.entity.salmon.death": "Laks døyr", "subtitles.entity.salmon.flop": "<PERSON><PERSON> spralar", "subtitles.entity.salmon.hurt": "Laks verkjer", "subtitles.entity.sheep.ambient": "<PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON>", "subtitles.entity.shulker.ambient": "Shulker lurer", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> lèt seg att", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opnar seg", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> skyt", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON> seg", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON> sp<PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> brotnar", "subtitles.entity.silverfish.ambient": "Sylvk<PERSON> k<PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.silverfish.hurt": "Sylvkrek verkjer", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON>d ranglar", "subtitles.entity.skeleton.converted_to_stray": "Beingrind verd umskapad til villegrind", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.skeleton.shoot": "Being<PERSON>d skyt", "subtitles.entity.skeleton_horse.ambient": "Øykjebeingrind skrik", "subtitles.entity.skeleton_horse.death": "Øykjebeingrind dø<PERSON>", "subtitles.entity.skeleton_horse.hurt": "Øykjebeingrind verkjer", "subtitles.entity.skeleton_horse.jump_water": "Øykjebeingrind hoppar", "subtitles.entity.skeleton_horse.swim": "Øykjebeingrind sym", "subtitles.entity.slime.attack": "Sliming åtek", "subtitles.entity.slime.death": "Sliming døyr", "subtitles.entity.slime.hurt": "Sliming verkjer", "subtitles.entity.slime.squish": "Sliming klaskar", "subtitles.entity.sniffer.death": "<PERSON><PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON> grev", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON> stend upp", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON> slepp frjo", "subtitles.entity.sniffer.eat": "<PERSON><PERSON> et", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON><PERSON><PERSON> brotnar", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.sniffer.happy": "<PERSON><PERSON> er glad", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON> tevar", "subtitles.entity.sniffer.searching": "<PERSON><PERSON> le<PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON> ve<PERSON>r", "subtitles.entity.sniffer.step": "<PERSON><PERSON> tred", "subtitles.entity.snow_golem.death": "Snjokall døyr", "subtitles.entity.snow_golem.hurt": "Snjokall verkjer", "subtitles.entity.snowball.throw": "Snjoball flyg", "subtitles.entity.spider.ambient": "Kongurvåva kvæser", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.spider.hurt": "Kongurvåva verkjer", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON><PERSON> sym", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON><PERSON> skyt blekk", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON> ranglar", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.stray.hurt": "Villeg<PERSON><PERSON> verkjer", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.strider.eat": "Stìgar et", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON> trallar", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON> dreg seg attende", "subtitles.entity.tadpole.death": "Rovetroll døyr", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON> spralar", "subtitles.entity.tadpole.grow_up": "Rovetroll veks upp", "subtitles.entity.tadpole.hurt": "Rovetroll verkjer", "subtitles.entity.tnt.primed": "TNT fræser", "subtitles.entity.tropical_fish.death": "Sudhavsfisk døyr", "subtitles.entity.tropical_fish.flop": "Sudhavsfisk spralar", "subtitles.entity.tropical_fish.hurt": "Sudhavsfisk verkjer", "subtitles.entity.turtle.ambient_land": "Skjelpodda kvittrar", "subtitles.entity.turtle.death": "Sk<PERSON>lpodda døyr", "subtitles.entity.turtle.death_baby": "Skjelpoddunge døyr", "subtitles.entity.turtle.egg_break": "Skjelpoddeegg brotnar", "subtitles.entity.turtle.egg_crack": "Skjelpoddeegg sprekk", "subtitles.entity.turtle.egg_hatch": "Skjelpoddeegg klekkjer", "subtitles.entity.turtle.hurt": "Skjelpodda verkjer", "subtitles.entity.turtle.hurt_baby": "Skjelpoddunge verkjer", "subtitles.entity.turtle.lay_egg": "Skjelpodda verp", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruggar", "subtitles.entity.turtle.shamble_baby": "Skjelpoddunge ruggar", "subtitles.entity.turtle.swim": "Skjelpodda sym", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> mullar", "subtitles.entity.villager.celebrate": "<PERSON>g<PERSON><PERSON><PERSON> fagnar", "subtitles.entity.villager.death": "Bygdarbue døyr", "subtitles.entity.villager.hurt": "Bygdarbue verkjer", "subtitles.entity.villager.no": "Bygdarbue er usamd", "subtitles.entity.villager.trade": "Bygdarbue byter", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_cartographer": "Kortteik<PERSON> verkar", "subtitles.entity.villager.work_cleric": "Prest verkar", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> verkar", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_leatherworker": "Lederverkar verkar", "subtitles.entity.villager.work_librarian": "Bokverja verkar", "subtitles.entity.villager.work_mason": "<PERSON><PERSON> verkar", "subtitles.entity.villager.work_shepherd": "<PERSON>yr<PERSON> verkar", "subtitles.entity.villager.work_toolsmith": "Ambo<PERSON><PERSON><PERSON><PERSON> ve<PERSON>r", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.villager.yes": "Bygdarbue er samd", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON> murrar", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON> fagnar", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> mum<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON> kverv", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON> drikk mjølk", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON> drikk brygg", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON> ka<PERSON>mann er usamd", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON> ovrar seg", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON> byter", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON> ka<PERSON> er samd", "subtitles.entity.warden.agitated": "Vord styn argt", "subtitles.entity.warden.ambient": "Vord kvin", "subtitles.entity.warden.angry": "<PERSON>ord rasar", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON> slær", "subtitles.entity.warden.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.warden.dig": "Vord grev", "subtitles.entity.warden.emerge": "Vord grev seg upp", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> vord slær", "subtitles.entity.warden.hurt": "Vord verkjer", "subtitles.entity.warden.listening": "Vord merkar", "subtitles.entity.warden.listening_angry": "Vord merkar argt", "subtitles.entity.warden.nearby_close": "<PERSON>ord kjem", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON> kjem nærre", "subtitles.entity.warden.nearby_closest": "<PERSON>ord er nær", "subtitles.entity.warden.roar": "<PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON> vedrar", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON> buld<PERSON>", "subtitles.entity.warden.sonic_charge": "Vord lader upp", "subtitles.entity.warden.step": "Vord tred", "subtitles.entity.warden.tendril_clicks": "Veidehorni åt vord klikka", "subtitles.entity.wind_charge.throw": "Vindlading fyk", "subtitles.entity.wind_charge.wind_burst": "Vindlading brest", "subtitles.entity.witch.ambient": "Trollkjerring knisar", "subtitles.entity.witch.celebrate": "Trollkjerring fagnar", "subtitles.entity.witch.death": "Trollkjerring døyr", "subtitles.entity.witch.drink": "Trollkjerring drikk", "subtitles.entity.witch.hurt": "Trollkjerring verkjer", "subtitles.entity.witch.throw": "Trollkjerring kastar", "subtitles.entity.wither.ambient": "<PERSON><PERSON> a<PERSON>st", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "Wither verd sett laus", "subtitles.entity.wither_skeleton.ambient": "Witherbeing<PERSON>d ranglar", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.wither_skeleton.hurt": "Witherbeingrind verkjer", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> pæsar", "subtitles.entity.wolf.bark": "<PERSON><PERSON>v gøyr", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON> knurrar", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON> pæsar", "subtitles.entity.wolf.shake": "Ulv skjek", "subtitles.entity.wolf.whine": "Ulv kvin", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> knurrar", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> knurrar argt", "subtitles.entity.zoglin.attack": "Zoglin åtek", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> verkjer", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> tred", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> verd umskapad til sjodraug", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.zombie.destroy_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verd tradkat på", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.zombie_horse.ambient": "Øykjenåe skrik", "subtitles.entity.zombie_horse.death": "Øykjen<PERSON>e døyr", "subtitles.entity.zombie_horse.hurt": "Øykjenåe verkjer", "subtitles.entity.zombie_villager.ambient": "Bygdarn<PERSON><PERSON> styn", "subtitles.entity.zombie_villager.converted": "Bygdarn<PERSON><PERSON> klagar", "subtitles.entity.zombie_villager.cure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> snuvlar", "subtitles.entity.zombie_villager.death": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.zombie_villager.hurt": "Bygdarn<PERSON><PERSON> verkjer", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON> gryler argt", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.zombified_piglin.hurt": "Piglinn<PERSON><PERSON> verkjer", "subtitles.event.mob_effect.bad_omen": "Jarteign veks seg sterkare", "subtitles.event.mob_effect.raid_omen": "Herjing trugar i nærleiken", "subtitles.event.mob_effect.trial_omen": "Illspåe røyne trugar i nærleiken", "subtitles.event.raid.horn": "<PERSON>lspåe horn ljomar", "subtitles.item.armor.equip": "Utbunad verd åteken", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON>man<PERSON>b<PERSON><PERSON> kling", "subtitles.item.armor.equip_elytra": "Skalvenger skråva", "subtitles.item.armor.equip_gold": "Gullb<PERSON><PERSON> kling", "subtitles.item.armor.equip_iron": "Jarnbrynja kling", "subtitles.item.armor.equip_leather": "Lederbrynja skråvar", "subtitles.item.armor.equip_netherite": "Netherittbrynja kling", "subtitles.item.armor.equip_turtle": "Skjelpoddeskal støyter", "subtitles.item.armor.equip_wolf": "Ulvebrynja verd stramad", "subtitles.item.armor.unequip_wolf": "Ulvebrynja glid av", "subtitles.item.axe.scrape": "Øks skrapar", "subtitles.item.axe.strip": "<PERSON><PERSON> s<PERSON>", "subtitles.item.axe.wax_off": "Vaks av", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.book.page_turn": "<PERSON><PERSON> s<PERSON>", "subtitles.item.book.put": "Bok støyter", "subtitles.item.bottle.empty": "<PERSON><PERSON> verd tømd", "subtitles.item.bottle.fill": "Flaska vert fyllt", "subtitles.item.brush.brushing.generic": "Kostar", "subtitles.item.brush.brushing.gravel": "Kostar aur", "subtitles.item.brush.brushing.gravel.complete": "Kosting av aur fullgjort", "subtitles.item.brush.brushing.sand": "Kostar sand", "subtitles.item.brush.brushing.sand.complete": "Kosting av sand fullgjort", "subtitles.item.bucket.empty": "<PERSON>nn verd tømt", "subtitles.item.bucket.fill": "Spann verd fyllt", "subtitles.item.bucket.fill_axolotl": "Aksolotl verd aust upp", "subtitles.item.bucket.fill_fish": "Fisk fangad", "subtitles.item.bucket.fill_tadpole": "Rovetroll fangad", "subtitles.item.bundle.drop_contents": "Sekk verd tømd", "subtitles.item.bundle.insert": "Ting verd pakkad", "subtitles.item.bundle.insert_fail": "Sekk er full", "subtitles.item.bundle.remove_one": "Ting verd pakkad ut", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON> f<PERSON><PERSON> seg", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON><PERSON> verd sådd", "subtitles.item.crossbow.charge": "Låsboge verd spent", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.item.crossbow.load": "Låsboge verd ladd", "subtitles.item.crossbow.shoot": "Låsboge skyt", "subtitles.item.dye.use": "<PERSON>ì<PERSON><PERSON> l<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON>", "subtitles.item.firecharge.use": "Eldladning fræser", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Glødeblèksekk klattar", "subtitles.item.goat_horn.play": "Bukkehorn verd spelat", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON> dyr<PERSON> upp", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON> på", "subtitles.item.horse_armor.unequip": "Øykjebrynja verd teki av", "subtitles.item.ink_sac.use": "Blèksekk klattar", "subtitles.item.lead.break": "Band ryk", "subtitles.item.lead.tied": "Band verd knytt", "subtitles.item.lead.untied": "Band verd knytt upp", "subtitles.item.llama_carpet.unequip": "<PERSON>ya verd teki av", "subtitles.item.lodestone_compass.lock": "Kompås fyre leidarstein fester seg til leidarstein", "subtitles.item.mace.smash_air": "Stridsklubba verd slengd", "subtitles.item.mace.smash_ground": "Stridsklubba slær ned", "subtitles.item.nether_wart.plant": "<PERSON><PERSON><PERSON><PERSON> verd sådd", "subtitles.item.ominous_bottle.dispose": "Flaska brotnar", "subtitles.item.saddle.unequip": "<PERSON>el verd teken av", "subtitles.item.shears.shear": "Soks klikker", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON> vernar", "subtitles.item.shovel.flatten": "Spade flatar ut", "subtitles.item.spyglass.stop_using": "Hand<PERSON><PERSON> verd stuttare", "subtitles.item.spyglass.use": "Hand<PERSON>kar verd lengre", "subtitles.item.totem.use": "Totem verd sleget på", "subtitles.item.trident.hit": "Ljoster sting", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON> kjem attende", "subtitles.item.trident.riptide": "Ljoster fyk", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON> kling", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON><PERSON><PERSON> brakar", "subtitles.item.wolf_armor.break": "Ulvebryngja brotnar", "subtitles.item.wolf_armor.crack": "Ulvebrynja slær sprekkar", "subtitles.item.wolf_armor.damage": "Ulvebrynja tek skade", "subtitles.item.wolf_armor.repair": "Ulvebrynja er bøtt", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> flyr", "subtitles.ui.cartography_table.take_result": "<PERSON>rt verd teiknat", "subtitles.ui.hud.bubble_pop": "<PERSON><PERSON><PERSON><PERSON> søkk", "subtitles.ui.loom.take_result": "<PERSON>ev<PERSON><PERSON> verd nyttad", "subtitles.ui.stonecutter.take_result": "Steinskjere verd nyttad", "subtitles.weather.rain": "<PERSON>n fell", "symlink_warning.message": "Å lada inn heimar frå faldar med symbolske lekker kann vera utrygt um du ikkje veit kvat du gjerer. Vitja %s fyr’ å læra meir.", "symlink_warning.message.pack": "Å lada inn pakkar symbolske lekker kann vera utrygt um du ikkje veit kvat du gjerer. Vitja %s fyr’ å læra meir.", "symlink_warning.message.world": "Å lada inn heimar frå faldar med symbolske lekker kann vera utrygt um du ikkje veit kvat du gjerer. Vitja %s fyr’ å læra meir.", "symlink_warning.more_info": "<PERSON>le<PERSON> upplysingar", "symlink_warning.title": "Heimsfalden inneheld <PERSON>ke lekker", "symlink_warning.title.pack": "D’ero <PERSON>ke lekker i den tillagde pakken/dei tillagde pòkkom", "symlink_warning.title.world": "Heimsfalden inneheld <PERSON>ke lekker", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "Skumpa andre lag", "team.collision.pushOwnTeam": "Skumpa eiget lag", "team.notFound": "«%s» er eit ukjent lag", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON> fyr<PERSON> andre lag", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON> fyr’ eiget lag", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "Ved å skyna samanhangen attum tileigningi av bragder kann me lettare skyna og betra framgangen i spelet.", "telemetry.event.advancement_made.title": "Bragd gjord", "telemetry.event.game_load_times.description": "<PERSON>ne hendingi mæler kor lang tid igongsetje-vendi nytta, og kann hjelpa oss å finna ut kvar me ljota betra igongsetje-dygdi.", "telemetry.event.game_load_times.title": "Spelladetider", "telemetry.event.optional": "%s (valfri)", "telemetry.event.optional.disabled": "%s (valfritt) – Avsleget", "telemetry.event.performance_metrics.description": "Å kjenna til den ålmenne dygdarprofilen åt Minecraft hjelpa oss med å tilmåta og betra spelet fyr’ eit breidt utval maskinspesifikasjonar og operativsystem. \nSpelutgåva fylgjer med fyr’ å hjelpa oss å jamføra dygdarprofilarne åt nyare utgåvor av Minecraft.", "telemetry.event.performance_metrics.title": "Dygdarmål", "telemetry.event.required": "%s (kravd)", "telemetry.event.world_load_times.description": "D’er vegtugt fyr’ oss å skyna kor lòng tid det tek å verda med i ein heim, og korleides det skifter yver tid. <PERSON><PERSON> dø<PERSON>, nær me leggja inn nye verkende elder gjera store tekniske umgjerder trenga me sjå kvat påverknad det heve på ladetidi.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Å vita korleides leikarar spela Minecraft (so som spelstòda, klient elder moddad tenar, og spelutgåva) lèt oss føra spel-etterførslor i ei leid som legg vegt på å betra ting leikarar vyrda på mest. Heim innladd-hendingi er bunden til Heim avladd-hendingi fyr’ å rekna ut kor lang tid speleøykti heve vart.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> inn<PERSON>d", "telemetry.event.world_unloaded.description": "Denne hendingi er bundi til Heim innladd-hendingi fyr’ å rekna ut kor lenge heimsøykti heve vart.\nVaringi (i sekundar og tikk) er mælt nær øykti åt heimen heve endat (fer åt hovudskråi, sløkkjer samband med tenar).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Speltid (tikk)", "telemetry.property.advancement_id.title": "Bragdar-ID", "telemetry.property.client_id.title": "Klient-ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON> klient", "telemetry.property.dedicated_memory_kb.title": "Tileignat minne (kB)", "telemetry.property.event_timestamp_utc.title": "Tidstempel fyr’ hending (UTC)", "telemetry.property.frame_rate_samples.title": "Sjoner av bilætfrekvens (FPS)", "telemetry.property.game_mode.title": "Spelstòda", "telemetry.property.game_version.title": "S<PERSON>utgåva", "telemetry.property.launcher_name.title": "Opnarnamn", "telemetry.property.load_time_bootstrap_ms.title": "Opnetid (millisekundar)", "telemetry.property.load_time_loading_overlay_ms.title": "Tid på ladeskjerm (millisekundar)", "telemetry.property.load_time_pre_window_ms.title": "Tid fyre vindaugat opnar (millisekundar)", "telemetry.property.load_time_total_time_ms.title": "Full ladetid (millisekundar)", "telemetry.property.minecraft_session_id.title": "Minecraft-øykt-ID", "telemetry.property.new_world.title": "<PERSON><PERSON> heim", "telemetry.property.number_of_samples.title": "Sjonetal", "telemetry.property.operating_system.title": "Operativsystem", "telemetry.property.opt_in.title": "Samtykke", "telemetry.property.platform.title": "Flata", "telemetry.property.realms_map_content.title": "Realmskort-innehald (minispelnamn)", "telemetry.property.render_distance.title": "Tò<PERSON>", "telemetry.property.render_time_samples.title": "Sjoner av attgjevetid", "telemetry.property.seconds_since_load.title": "Tid sidan innlading (sekundar)", "telemetry.property.server_modded.title": "<PERSON><PERSON><PERSON> tenar", "telemetry.property.server_type.title": "Tenarslag", "telemetry.property.ticks_since_load.title": "Tid sidan innlading (tikk)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON> minne (RAM)", "telemetry.property.user_id.title": "Nytar-ID", "telemetry.property.world_load_time_ms.title": "Innladetid fyr<PERSON> heim (millisekundar)", "telemetry.property.world_session_id.title": "Øyktar-ID fyr’ heim", "telemetry_info.button.give_feedback": "<PERSON><PERSON><PERSON>", "telemetry_info.button.privacy_statement": "Mannevernsfråsegn", "telemetry_info.button.show_data": "Syn datai mine", "telemetry_info.opt_in.description": "Eg samtykkjer til at valfrie telemetridata verda sende", "telemetry_info.property_title": "Inneheldt data", "telemetry_info.screen.description": "Å samna desse datai hjelpa oss med å betra Minecraft ved å føra oss i dei leiderna som høva fyre leikararne våre.\nDu kann òg senda inn serskilt atterbòd fyr’ å hjelpa oss betra Minecraft enn-då meir.", "telemetry_info.screen.title": "Innsamning av telemetridata", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Uventat blokkslag fundet: %s", "test.error.missing_block_entity": "Vantar blokkeining", "test.error.position": "%s ved %s, %s, %s (relativt: %s, %s, %s) på tikk %s", "test.error.sequence.condition_already_triggered": "Vilkòr alt utløyst ved %s", "test.error.sequence.condition_not_triggered": "Vilkòr ikkje utlø<PERSON>t", "test.error.sequence.invalid_tick": "Fullgjorde på ugildt tikk: ventade %s", "test.error.sequence.not_completed": "<PERSON><PERSON>i rann ut fyrr fylgdi endade", "test.error.set_biome": "<PERSON><PERSON> <PERSON>kje setja lende til ryne", "test.error.spawn_failure": "Kunde ’kje skapa einingi %s", "test.error.state_not_equal": "Rang stòda. Ventade %s; var %s", "test.error.structure.failure": "Ku<PERSON> <PERSON>kje setja ned rynebygnad fyre %s", "test.error.tick": "%s på tikk %s", "test.error.ticking_without_structure": "<PERSON><PERSON><PERSON><PERSON><PERSON> fyrr bygnad verd sett ned", "test.error.timeout.no_result": "Fullgjorde ’kje elder kunde ’kje innan %s tikk", "test.error.timeout.no_sequences_finished": "Ingi sekvensar fullgjorde innan %s tikk", "test.error.too_many_entities": "Ventade berre éin %s til å finnast kring %s, %s, %s, men fann %s", "test.error.unexpected_block": "Ventade ikkje at blokki var %s", "test.error.unexpected_entity": "Ventade ikkje at %s fanst", "test.error.unexpected_item": "Ventade ikkje ting av slaget %s", "test.error.unknown": "Ukjent indre lyte: %s", "test.error.value_not_equal": "Ventade at %s vøre %s; var %s", "test.error.wrong_block_entity": "Rangt blokkeiningslag: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Bòd:", "test_block.mode.accept": "Godtaka", "test_block.mode.fail": "Mislukka", "test_block.mode.log": "<PERSON><PERSON><PERSON> i logg", "test_block.mode.start": "<PERSON><PERSON><PERSON>", "test_block.mode_info.accept": "God<PERSON>stòda – god<PERSON><PERSON> at eit ryne (i sumt) verd lukkat", "test_block.mode_info.fail": "Mislukkestòda – gjer at rynet verd mislukkat", "test_block.mode_info.log": "Loggstòda – loggfør eit bòd", "test_block.mode_info.start": "Byrjestòda – byrjepunktet fyre eit ryne", "test_instance.action.reset": "Attra og køyr", "test_instance.action.run": "Lad og køyr", "test_instance.action.save": "Spar bygnad", "test_instance.description.batch": "Slump: %s", "test_instance.description.failed": "Mislukkat: %s", "test_instance.description.function": "Verkende: %s", "test_instance.description.invalid_id": "<PERSON>gild ryne-<PERSON>", "test_instance.description.no_test": "Inkje sovordet ryne", "test_instance.description.structure": "Bygnad: %s", "test_instance.description.type": "Slag: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Einingar:", "test_instance_block.error.no_test": "Kunde ’kje køyra rynehøve ved %s, %s, %s av di det hev eit ikkje-fastsett ryne", "test_instance_block.error.no_test_structure": "Kunde ’kje køyra rynehøve ved %s, %s, %s av di det ikkje hev rynebygnad", "test_instance_block.error.unable_to_save": "Ku<PERSON> ’kje spara rynebygnads-skant fyre rynetilfelle ved %s, %s, %s", "test_instance_block.invalid": "[ugild]", "test_instance_block.reset_success": "Attring var vellukkad fyre ryne: %s", "test_instance_block.rotation": "Riding:", "test_instance_block.size": "Storleike på rynebygnad", "test_instance_block.starting": "<PERSON><PERSON>jar ryne %s", "test_instance_block.test_id": "Rynehøves-ID", "title.32bit.deprecation": "Fann 32-bitssystem. <PERSON><PERSON> kann hindra deg ifrå å spela i framtidi av di spelet kjem til å krevja 64-bitssystem!", "title.32bit.deprecation.realms": "Minecraft kjem snart til å krevja 64-bitssystem, som kjem til å hindra deg ifrå å spela elder nytta Realms på denne einingi. Du må stansa Realms-tingingar på eigi hond.", "title.32bit.deprecation.realms.check": "Ikkje syn denne vitringi att", "title.32bit.deprecation.realms.header": "Fann 32-bitssystem", "title.credits": "Upphavsrett Mojang AB. Ikkje lov å breida!", "title.multiplayer.disabled": "Samleikar er avsleget. Sjå på vali fyre Microsoft-kontoen din.", "title.multiplayer.disabled.banned.name": "Du må skifta namn fyrr du kann spela ånetes", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON><PERSON> din er utestengd frå netspel fyr’ alltid", "title.multiplayer.disabled.banned.temporary": "Ko<PERSON>en din er millombìls utestengd frå netspel", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON> (tridjemannstenar)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Fyrefeste, %s%2$s att %s og %1$s til sidst %s og so %1$s att!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hei %", "translation.test.invalid2": "hei %s", "translation.test.none": "Hei, heim!", "translation.test.world": "heim", "trim_material.minecraft.amethyst": "Ametystemne", "trim_material.minecraft.copper": "Koparemne", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragdemne", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "Jarnemne", "trim_material.minecraft.lapis": "Lasursteinsemne", "trim_material.minecraft.netherite": "Netherittemne", "trim_material.minecraft.quartz": "Kattsteinsemne", "trim_material.minecraft.redstone": "Restoneemne", "trim_material.minecraft.resin": "Kvådemne", "trim_pattern.minecraft.bolt": "Bolt-br<PERSON>jep<PERSON>d", "trim_pattern.minecraft.coast": "Strand-brynjepryd", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "Kvervel-brynjepryd", "trim_pattern.minecraft.host": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.raiser": "Lyftar-brynjepryd", "trim_pattern.minecraft.rib": "Rivbeins-brynjepryd", "trim_pattern.minecraft.sentry": "Vaktar-brynjepryd", "trim_pattern.minecraft.shaper": "Formar-brynjepryd", "trim_pattern.minecraft.silence": "Togn-brynjep<PERSON>d", "trim_pattern.minecraft.snout": "Tryne-brynjepryd", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "Tidvats-brynjepryd", "trim_pattern.minecraft.vex": "Mødar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.ward": "Vord-brynjepryd", "trim_pattern.minecraft.wayfinder": "Vegfinnar-brynjepryd", "trim_pattern.minecraft.wild": "Vill bryn<PERSON>d", "tutorial.bundleInsert.description": "Høgreklikk fyr’ å leggja til ting", "tutorial.bundleInsert.title": "Nytta sekk", "tutorial.craft_planks.description": "<PERSON>ne<PERSON><PERSON> kann hjelpa", "tutorial.craft_planks.title": "<PERSON><PERSON> trebord", "tutorial.find_tree.description": "Sl<PERSON> det fyr’ å samna vìd", "tutorial.find_tree.title": "Finn eit tre", "tutorial.look.description": "Nytta musi til å venda deg", "tutorial.look.title": "Sj<PERSON> deg umkring", "tutorial.move.description": "Hoppa med %s", "tutorial.move.title": "<PERSON><PERSON><PERSON> deg med %s, %s, %s og %s", "tutorial.open_inventory.description": "Kyv %s", "tutorial.open_inventory.title": "Opna skreppa di", "tutorial.punch_tree.description": "Haldt nedre %s", "tutorial.punch_tree.title": "Ø<PERSON><PERSON><PERSON> treet", "tutorial.socialInteractions.description": "Kyv %s fyr’ å opna", "tutorial.socialInteractions.title": "Samkvæme", "upgrade.minecraft.netherite_upgrade": "Netherittbetring"}