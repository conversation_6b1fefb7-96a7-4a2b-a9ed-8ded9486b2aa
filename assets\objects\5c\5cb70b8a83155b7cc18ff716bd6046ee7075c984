{"accessibility.onboarding.accessibility.button": "Tilgjenge...", "accessibility.onboarding.screen.narrator": "Trykk Enter for å slå på forteljaren", "accessibility.onboarding.screen.title": "Velkomen til Minecraft!\n\nYnskjer du å slå på forteljaren eller vitja innstillingane for tilgjenge?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Tenaradresse", "addServer.enterName": "<PERSON><PERSON> på tenar", "addServer.resourcePack": "Tilfangspakkar på tenar", "addServer.resourcePack.disabled": "Avslege", "addServer.resourcePack.enabled": "Påslege", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Gjer om tenarinfo", "advMode.command": "Konsollkommando", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Alltid verksam", "advMode.mode.conditional": "Føresetnad", "advMode.mode.redstone": "Tildriv", "advMode.mode.redstoneTriggered": "Treng redstone", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Uføresetnad", "advMode.notAllowed": "Må vera ein operatør i skaparmodus", "advMode.notEnabled": "Kommandoblokker er ikkje slegne på på denne tenaren", "advMode.previousOutput": "<PERSON><PERSON><PERSON>", "advMode.setCommand": "Set konsollkommando for blokk", "advMode.setCommand.success": "Kommando vart sett: %s", "advMode.trackOutput": "Spora utgjæv", "advMode.triggering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.type": "Slag", "advancement.advancementNotFound": "«%s» er ei ukjend bragd", "advancements.adventure.adventuring_time.description": "<PERSON> alle lende", "advancements.adventure.adventuring_time.title": "Eventyrtid", "advancements.adventure.arbalistic.description": "Drep fem sæ<PERSON><PERSON> skapningar med eitt låsbogeskot", "advancements.adventure.arbalistic.title": "Låsbogeskytar", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON>g nær ein sculk<PERSON><PERSON><PERSON> eller vord for hindra at han legg merke til deg", "advancements.adventure.avoid_vibration.title": "Smyg 100", "advancements.adventure.blowback.description": "Drep eit vindskrømt med ei bægd vindkule frå eit vindskrømt", "advancements.adventure.blowback.title": "Atterblåster", "advancements.adventure.brush_armadillo.description": "<PERSON><PERSON> skjoldplater frå eit beltedyr ved å bruke ei børste", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> vare", "advancements.adventure.bullseye.description": "Råka ei skotskive midt på frå minst 30 meter undan", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "G<PERSON> ei prydd potte med 4 skålbrot", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Ver nær ein emnar når han emnar ein emnar", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON> emnar emnarar", "advancements.adventure.fall_from_world_height.description": "Fall frå toppen av verda (byggjehøgda) til botnen av verda utan å døy", "advancements.adventure.fall_from_world_height.title": "H<PERSON>ler og hamrar", "advancements.adventure.heart_transplanter.description": "Set ned eit knirkehjarta med den rette oppsetnaden mellom to bleikeikestomnar", "advancements.adventure.heart_transplanter.title": "Med hjarta på rette staden", "advancements.adventure.hero_of_the_village.description": "Vern ei bygd frå ei herjing", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON> på ei honningblokk for å døyva fallet", "advancements.adventure.honey_block_slide.title": "<PERSON> klisteret", "advancements.adventure.kill_a_mob.description": "Drep eit fiendsleg monster av kva som helst slag", "advancements.adventure.kill_a_mob.title": "Monsterveidar", "advancements.adventure.kill_all_mobs.description": "Drep eit av kvart fiendslege monster", "advancements.adventure.kill_all_mobs.title": "<PERSON> veidde", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Drep ein skapning nær ein sculkeskundar", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON> breier seg", "advancements.adventure.lighten_up.description": "Skrap ei koparpære med ei øks for å gjera henne ljosare", "advancements.adventure.lighten_up.title": "Opplysande", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Verna ei bygd imot eit uynskt sjokk utan å kveikja ein brann", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Overspenningsvern", "advancements.adventure.minecraft_trials_edition.description": "Ta deg inn i eit røynerom", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "Skyt med låsboge", "advancements.adventure.ol_betsy.title": "Skytaren", "advancements.adventure.overoverkill.description": "Ta skade på 50 hjarto med eitt einskilt stridsklubbeslag", "advancements.adventure.overoverkill.title": "Over-overmål", "advancements.adventure.play_jukebox_in_meadows.description": "Lat engene livna til med ljoden av musikk frå ein platespelar", "advancements.adventure.play_jukebox_in_meadows.title": "Sound of Music", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Les kraftsignalet frå ei forma bokhylle med sa<PERSON>liknar", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Makta i bøker", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON> opp eit illevarande kvelv med ein illevarande rø<PERSON>", "advancements.adventure.revaulting.title": "Ikkje så kvelvmeint", "advancements.adventure.root.description": "<PERSON>yr, utforsking, og strid", "advancements.adventure.root.title": "Eventyr", "advancements.adventure.salvage_sherd.description": "Kosta ei mistenkjeleg blokk for å få tak i skålbrot", "advancements.adventure.salvage_sherd.title": "Vyrdnad for leivdene", "advancements.adventure.shoot_arrow.description": "Skyt noko med ei pil", "advancements.adventure.shoot_arrow.title": "Sikt", "advancements.adventure.sleep_in_bed.description": "Sov i ei seng for å gjera om byrjestaden din", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Drep eit bein<PERSON>el frå minst 50 meter undan", "advancements.adventure.sniper_duel.title": "Snikskyttartevling", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON><PERSON> på <PERSON>raken med kikert", "advancements.adventure.spyglass_at_dragon.title": "Er det eit fly?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON>j<PERSON> på eit ghast med kikert", "advancements.adventure.spyglass_at_ghast.title": "Er det ein ballong?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> på ein papegøye med kikert", "advancements.adventure.spyglass_at_parrot.title": "Er det ein fugl?", "advancements.adventure.summon_iron_golem.description": "Framkall ei jarnkjempe for å verja ei bygd", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON> h<PERSON>", "advancements.adventure.throw_trident.description": "Kasta ei ljoster åt noko.\nHugsa: Å kasta bort det einaste våpenet ditt er ikkje særleg klokt.", "advancements.adventure.throw_trident.title": "Eingongsvits", "advancements.adventure.totem_of_undying.description": "Bruk eit totem mot døying for å lura dauden", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> dauden", "advancements.adventure.trade.description": "Byt med ein bygdebu", "advancements.adventure.trade.title": "For eit kaup!", "advancements.adventure.trade_at_world_height.description": "Byt med ein bygdebu ved grensa for byggjehøgd", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> prisar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON><PERSON> kvar av desse smideskantane minst éin gong: spir, snut, riv<PERSON>n, vord, togn, p<PERSON><PERSON><PERSON>, tid<PERSON>n, vegfinnar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Stilfull smiding", "advancements.adventure.trim_with_any_armor_pattern.description": "Laga ei prydd rustning på eit smiebord", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON> stil", "advancements.adventure.two_birds_one_arrow.description": "Drep to fantom med ei spiddande pil", "advancements.adventure.two_birds_one_arrow.title": "Skyta gullfuglen", "advancements.adventure.under_lock_and_key.description": "Bruka ei røynelykel på eit kvelv", "advancements.adventure.under_lock_and_key.title": "Bak lås og slå", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON>a eit kompass på ein leidarstein", "advancements.adventure.use_lodestone.title": "Var det den leidi eg skulde?", "advancements.adventure.very_very_frightening.description": "Slå ned ein bygdebu med lynet", "advancements.adventure.very_very_frightening.title": "«Very very frightening»", "advancements.adventure.voluntary_exile.description": "Drep ein herje<PERSON>.\nDet kan vera klokt å halda seg undan bygder ei stund...", "advancements.adventure.voluntary_exile.title": "Friviljug utlegd", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Gå på nysnø... utan å søkka ned i honom", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lett som ein hare", "advancements.adventure.who_needs_rockets.description": "<PERSON>ruka ei vindlading til å skyta deg opp 7 blokker", "advancements.adventure.who_needs_rockets.title": "<PERSON><PERSON> treng rakettar?", "advancements.adventure.whos_the_pillager_now.description": "Lat ein plyndrar smaka sin eigen medisin", "advancements.adventure.whos_the_pillager_now.title": "Kven er plyndrar no?", "advancements.empty": "Der ser ikkje ut til å vera noko her...", "advancements.end.dragon_breath.description": "Samla drakepust i ei glasflaske", "advancements.end.dragon_breath.title": "Du treng munnvatn", "advancements.end.dragon_egg.description": "<PERSON><PERSON> drakeegget", "advancements.end.dragon_egg.title": "Neste ættleden", "advancements.end.elytra.description": "Finn venger", "advancements.end.elytra.title": "Himmelen er grensa", "advancements.end.enter_end_gateway.description": "Fly øya", "advancements.end.enter_end_gateway.title": "<PERSON>rd til ein fjern stad", "advancements.end.find_end_city.description": "Gå inn. Kva kan vel henda?", "advancements.end.find_end_city.title": "Byen ved enden av spelet", "advancements.end.kill_dragon.description": "Lukke til", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Sviv over 50 blokker frå eit shulkeråtak", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> utsyn her oppe", "advancements.end.respawn_dragon.description": "<PERSON>am<PERSON><PERSON> endedraken å nyo", "advancements.end.respawn_dragon.title": "Enden... omatt...", "advancements.end.root.description": "Eller by<PERSON><PERSON><PERSON>?", "advancements.end.root.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON>å ein hjel<PERSON>nd til å sleppa ei kake på ei noteblokk", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Fødselsdagssong", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON>å ein hjel<PERSON>nd til å henta ting til deg", "advancements.husbandry.allay_deliver_item_to_player.title": "Eg er venen din", "advancements.husbandry.axolotl_in_a_bucket.description": "Fanga ein axolotl i ei bytte", "advancements.husbandry.axolotl_in_a_bucket.title": "Det søtaste rovdyret", "advancements.husbandry.balanced_diet.description": "Et alt som du kan eta, jamvel det ikkje er godt for deg", "advancements.husbandry.balanced_diet.title": "Eit jamvektig kosthald", "advancements.husbandry.breed_all_animals.description": "Avl alle dyra!", "advancements.husbandry.breed_all_animals.title": "To og to", "advancements.husbandry.breed_an_animal.description": "Al to dyr med kvarandre", "advancements.husbandry.breed_an_animal.title": "Papegøyane og skinnvengjene", "advancements.husbandry.complete_catalogue.description": "Tem alle katteslag!", "advancements.husbandry.complete_catalogue.title": "Ein full kattalog", "advancements.husbandry.feed_snifflet.description": "<PERSON> ein mold<PERSON>e", "advancements.husbandry.feed_snifflet.title": "Smånasing", "advancements.husbandry.fishy_business.description": "Fang ein fisk", "advancements.husbandry.fishy_business.title": "Skit fiske!", "advancements.husbandry.froglights.description": "Ha alle froskeljosa i inventaret ditt", "advancements.husbandry.froglights.title": "Med alle krefter i hop!", "advancements.husbandry.kill_axolotl_target.description": "G<PERSON> i lag med ein axolotl og vinn ein kamp", "advancements.husbandry.kill_axolotl_target.title": "Ein ven i nauden!", "advancements.husbandry.leash_all_frog_variants.description": "Fest kvart froskeslag til eit band", "advancements.husbandry.leash_all_frog_variants.title": "Når gjengen hoppar inn i byen", "advancements.husbandry.make_a_sign_glow.description": "Få teksta på alle slag skilt til å gløda", "advancements.husbandry.make_a_sign_glow.title": "Strålande!", "advancements.husbandry.netherite_hoe.description": "Bruk ein netherittbarre til å betra ein ljå, og tenk på ny over livsvala dine", "advancements.husbandry.netherite_hoe.title": "Ålvorleg deltaking", "advancements.husbandry.obtain_sniffer_egg.description": "Få tak i eit moldnas<PERSON>gg", "advancements.husbandry.obtain_sniffer_egg.title": "Spennande teft", "advancements.husbandry.place_dried_ghast_in_water.description": "Plasser ei uttørka ghast-blokk i vatn", "advancements.husbandry.place_dried_ghast_in_water.title": "Sløkk tørsten!", "advancements.husbandry.plant_any_sniffer_seed.description": "Så eit frø frå ein moldnase", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON> sl<PERSON>r rot", "advancements.husbandry.plant_seed.description": "Så eit frø og sjå det veksa", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON><PERSON> ulver<PERSON>ning frå ein ulv med ei saks", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON><PERSON> genialt", "advancements.husbandry.repair_wolf_armor.description": "Reparer skadd ulverustning med beltedyrskjoldplater", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> god som ny", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Kom deg i båten og ro med ei geit", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.root.description": "Verda er full av vener og mat", "advancements.husbandry.root.title": "Landbruk", "advancements.husbandry.safely_harvest_honey.description": "Bruk eit bål til å sanka honning frå ein biekube med flaske utan å hissa opp biene", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> vår gjest", "advancements.husbandry.silk_touch_nest.description": "Bruk varsemd til å flytta eit biebol med 3 bier utan at dei flyg ut", "advancements.husbandry.silk_touch_nest.title": "Ingen biverknad", "advancements.husbandry.tactical_fishing.description": "Fang ein fisk... utan fiskestong!", "advancements.husbandry.tactical_fishing.title": "Taktisk fiske", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON>a eit rumpetroll i ei bytte", "advancements.husbandry.tadpole_in_a_bucket.title": "Froskedam av jarn", "advancements.husbandry.tame_an_animal.description": "Tem eit dyr", "advancements.husbandry.tame_an_animal.title": "Ævelege bestevener", "advancements.husbandry.wax_off.description": "Skrapa bort voks frå ei koparblokk!", "advancements.husbandry.wax_off.title": "Voks av", "advancements.husbandry.wax_on.description": "Smør ei vokskake på ei koparblokk!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON>s på", "advancements.husbandry.whole_pack.description": "Tem ein varg av kvart slag", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> flokken", "advancements.nether.all_effects.description": "Ha alle verknadene på samstundes", "advancements.nether.all_effects.title": "<PERSON><PERSON><PERSON> kom me hit?", "advancements.nether.all_potions.description": "Ha alle trolldrykkverknadene på samstundes", "advancements.nether.all_potions.title": "Ein rasande drykk", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> ein trolldr<PERSON>k", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Lad eit oppstodeanker til det er fullt", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON><PERSON><PERSON> heilt «ni» liv", "advancements.nether.create_beacon.description": "Bygg og set ned ein varde", "advancements.nether.create_beacon.title": "<PERSON>kte varde", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON> ein varde full styrke", "advancements.nether.create_full_beacon.title": "Vardeleggjar", "advancements.nether.distract_piglin.description": "Di<PERSON><PERSON> piglinar med gull", "advancements.nether.distract_piglin.title": "Å, skinande", "advancements.nether.explore_nether.description": "Granska alle lende i Nether", "advancements.nether.explore_nether.title": "Heite turist<PERSON><PERSON>l", "advancements.nether.fast_travel.description": "Bruk Nether til å fara 7 km i oververda", "advancements.nether.fast_travel.title": "Underromsboble", "advancements.nether.find_bastion.description": "Gå inn i leivdene av ei festning", "advancements.nether.find_bastion.title": "<PERSON><PERSON> gamle dagar", "advancements.nether.find_fortress.description": "Bryt deg inn i ei Nether-festning", "advancements.nether.find_fortress.title": "Ei fælsleg festning", "advancements.nether.get_wither_skull.description": "Få tak i hovudet av eit witherbeinrangel", "advancements.nether.get_wither_skull.title": "Nifst utriveleg beinrangel", "advancements.nether.loot_bastion.description": "Plyndra ei kiste i leivdene av ei festning", "advancements.nether.loot_bastion.title": "Stridsgrisar", "advancements.nether.netherite_armor.description": "Få tak i ei full drakt med netherittrustning", "advancements.nether.netherite_armor.title": "Dekk meg med leivder", "advancements.nether.obtain_ancient_debris.description": "Få tak i forne leivder", "advancements.nether.obtain_ancient_debris.title": "Gøymde i djupa", "advancements.nether.obtain_blaze_rod.description": "Skaff ein eldstav frå eit eldskrømt", "advancements.nether.obtain_blaze_rod.title": "Inn i elden", "advancements.nether.obtain_crying_obsidian.description": "Få tak i gråtande obsidian", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON> skjer lauk?", "advancements.nether.return_to_sender.description": "<PERSON>ep eit ghast med ei eldkule", "advancements.nether.return_to_sender.title": "Send attende til sendar", "advancements.nether.ride_strider.description": "Ri på ein stegar med ein vridesopp på stong", "advancements.nether.ride_strider.title": "<PERSON><PERSON> b<PERSON>ten har bein", "advancements.nether.ride_strider_in_overworld_lava.description": "Tak ein stegar på ein laaaang tur i ei lavatjørn i Oververda", "advancements.nether.ride_strider_in_overworld_lava.title": "Nett som heime", "advancements.nether.root.description": "Tak med sumarklede", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON> witheren", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON> eit ghast fr<PERSON>, ta det trygt med heim til oververda... og so drep det", "advancements.nether.uneasy_alliance.title": "Uroleg samband", "advancements.nether.use_lodestone.description": "Bruk eit kompass på ein leidarstein", "advancements.nether.use_lodestone.title": "Var det den leidi eg skulde?", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Veik og deretter læk ein bygdedauding", "advancements.story.cure_zombie_villager.title": "Daudinglæk<PERSON>", "advancements.story.deflect_arrow.description": "Verna deg frå ei pil med eit skjold", "advancements.story.deflect_arrow.title": "Ikkje i dag, takk", "advancements.story.enchant_item.description": "Gald<PERSON> ein ting med eit galdrebord", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Gå inn i endeportalen", "advancements.story.enter_the_end.title": "Enden?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, kveik, og gå inn i ein netherportal", "advancements.story.enter_the_nether.title": "Me lyt gå djupare", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON>g eit enderauga", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.form_obsidian.description": "Få tak i ei blokk av obsidian", "advancements.story.form_obsidian.title": "Is<PERSON><PERSON><PERSON><PERSON>ring", "advancements.story.iron_tools.description": "<PERSON>ra hakka di", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galen", "advancements.story.lava_bucket.description": "Fyll ei bytte med lava", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON> saker", "advancements.story.mine_diamond.description": "Få tak i diamantar", "advancements.story.mine_diamond.title": "Diamantar!", "advancements.story.mine_stone.description": "Hakk stein med n<PERSON>hakka di", "advancements.story.mine_stone.title": "Steinalderen", "advancements.story.obtain_armor.description": "Vern deg med ein lùt av jarnrustninga", "advancements.story.obtain_armor.title": "<PERSON>led på deg", "advancements.story.root.description": "Hjarta og soga åt spelet", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrustning bergar liv", "advancements.story.shiny_gear.title": "Kled meg i diamantar", "advancements.story.smelt_iron.description": "Smelt ein jarnbarre", "advancements.story.smelt_iron.title": "Få tak i jarnvare", "advancements.story.upgrade_tools.description": "Laga ei betre hakke", "advancements.story.upgrade_tools.title": "Betring", "advancements.toast.challenge": "Utfordring fullførd!", "advancements.toast.goal": "M<PERSON>l nått!", "advancements.toast.task": "Bragd gjord!", "argument.anchor.invalid": "«%s» er ein ugild an<PERSON> for eining", "argument.angle.incomplete": "Uheilsleg (venta 1 vinkel)", "argument.angle.invalid": "<PERSON><PERSON><PERSON> v<PERSON>", "argument.block.id.invalid": "«%s» er eit ukjent blokkslag", "argument.block.property.duplicate": "Eigenskapen «%s» kan berre verta sett éin gong for blokka %s", "argument.block.property.invalid": "Blokka %s godtek ikkje «%s» for eigenskapen %s", "argument.block.property.novalue": "Verde for eigenskapen «%s» på blokka %s var venta", "argument.block.property.unclosed": "Attlatande ] var venta for eigenskapane åt blokkstanden", "argument.block.property.unknown": "Blokka %s har ikkje eigenskapen «%s»", "argument.block.tag.disallowed": "<PERSON><PERSON> er ikkje tillatne her. <PERSON><PERSON> verklege blokker", "argument.color.invalid": "«%s» er ein ukjend let", "argument.component.invalid": "«%s» er ein ugild nettpratskomponent", "argument.criteria.invalid": "«%s» er eit ukjent krav", "argument.dimension.invalid": "«%s» er ein ukjend heim", "argument.double.big": "Dobbeltalet kan ikkje vera større enn %s. Fann %s", "argument.double.low": "Dobbeltalet kan ikkje vera mindre enn %s. Fann %s", "argument.entity.invalid": "Ugildt namn eller UUID", "argument.entity.notfound.entity": "Inga eining var funnen", "argument.entity.notfound.player": "Ingen spelar var funnen", "argument.entity.options.advancements.description": "Spelarar med bragder", "argument.entity.options.distance.description": "Fråstand til eining", "argument.entity.options.distance.negative": "Fråstanden kan ikkje vera negativ", "argument.entity.options.dx.description": "Einingar mellom x og x + dx", "argument.entity.options.dy.description": "Ein<PERSON>r mellom y og y + dy", "argument.entity.options.dz.description": "Einingar mellom z og z + dz", "argument.entity.options.gamemode.description": "Spelarar med spelmodus", "argument.entity.options.inapplicable": "Valet «%s» er ikkje brukande her", "argument.entity.options.level.description": "R<PERSON><PERSON>slesteg", "argument.entity.options.level.negative": "Steget lyt ikkje vera negativt", "argument.entity.options.limit.description": "Høgste tal på einingar til å koma attende", "argument.entity.options.limit.toosmall": "Grensa må vera minst 1", "argument.entity.options.mode.invalid": "«%s» er ein ugild eller ukjend spelmodus", "argument.entity.options.name.description": "Einingsnamn", "argument.entity.options.nbt.description": "Einingar med NTB", "argument.entity.options.predicate.description": "<PERSON>il<PERSON><PERSON><PERSON> predikat", "argument.entity.options.scores.description": "Einingar med skòretal", "argument.entity.options.sort.description": "<PERSON><PERSON>", "argument.entity.options.sort.irreversible": "«%s» er eit ugildt eller ukjent sorteringslag", "argument.entity.options.tag.description": "Einingar med tagg", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON> på laget", "argument.entity.options.type.description": "Einingar av typen", "argument.entity.options.type.invalid": "Ugildt eller ukjent einingsslag «%s»", "argument.entity.options.unknown": "«%s» er eit ukjent val", "argument.entity.options.unterminated": "<PERSON><PERSON> på val var venta", "argument.entity.options.valueless": "Verdet for valet «%s» var venta", "argument.entity.options.x.description": "x-stad", "argument.entity.options.x_rotation.description": "X-rotasjonen åt e<PERSON>a", "argument.entity.options.y.description": "y-stad", "argument.entity.options.y_rotation.description": "Y-rotasjonen åt e<PERSON>a", "argument.entity.options.z.description": "z-stad", "argument.entity.selector.allEntities": "Alle einingar", "argument.entity.selector.allPlayers": "<PERSON>e spelarar", "argument.entity.selector.missing": "<PERSON><PERSON>jarslag er sakna", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON> spelar", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> er ikkje tillaten", "argument.entity.selector.randomPlayer": "Tilfelleleg spelar", "argument.entity.selector.self": "Gjeldande eining", "argument.entity.selector.unknown": "«%s» er eit ukjent veljarslag", "argument.entity.toomany": "<PERSON>rre éi eining er tillaten, men den oppgjevne veljaren tillèt meir enn éi", "argument.enum.invalid": "«%s» er eit ugildt verde", "argument.float.big": "Flyttalet kan ikkje vera større enn %s. Fann %s", "argument.float.low": "Flyttalet kan ikkje vera mindre enn %s. Fann %s", "argument.gamemode.invalid": "Ukjend spelmodus: %s", "argument.hexcolor.invalid": "Ugyldig hex-fargekode '%s'", "argument.id.invalid": "Ugild ID", "argument.id.unknown": "«%s» er ein ukjend ID", "argument.integer.big": "Heiltalet kan ikkje vera større enn %s. Fann %s", "argument.integer.low": "Heiltalet kan ikkje vera mindre enn %s. Fann %s", "argument.item.id.invalid": "«%s» er ein ukjend gjenstand", "argument.item.tag.disallowed": "<PERSON>gar er ikkje tillatne her. Berre verkelege gjenstandar", "argument.literal.incorrect": "Strengkonstanten %s var venta", "argument.long.big": "Long-verde kan ikkje vera større enn %s. Fann %s", "argument.long.low": "Long-verde kan ikkje vera mindre enn %s. Fann %s", "argument.message.too_long": "Nettpratsmelding var for lang (%s > høgst %s teikn)", "argument.nbt.array.invalid": "«%s» er eit ugildt slag tabell", "argument.nbt.array.mixed": "Kan ikk<PERSON> setja inn %s i %s", "argument.nbt.expected.compound": "Forventa compound-tagg", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON> var venta", "argument.nbt.expected.value": "Verde var venta", "argument.nbt.list.mixed": "Kan ikkje setja inn %s i lista over %s", "argument.nbt.trailing": "Vidare data var ikkje venta", "argument.player.entities": "<PERSON><PERSON> s<PERSON>r kan verta påverka av denne kommandoen, men den oppgjevne veljaren femner òg e<PERSON>ar", "argument.player.toomany": "<PERSON><PERSON> éin spelar er tillaten, men den oppgjevne veljaren tillèt meir enn éin", "argument.player.unknown": "<PERSON><PERSON> spalaren finst ikkje", "argument.pos.missing.double": "Ein koordinat var venta", "argument.pos.missing.int": "Ein blokkstad var venta", "argument.pos.mixed": "Kan ikkje blanda verdskoordinatar og heimlege koordinatar (alt må anten bruka ^ eller ikkje noko)", "argument.pos.outofbounds": "Denne staden er utanføre dei tillatne grensene.", "argument.pos.outofworld": "Denne staden er utanføre verda!", "argument.pos.unloaded": "Denne staden er ikkje lasta inn", "argument.pos2d.incomplete": "Uheilsleg (2 koordinatar var venta)", "argument.pos3d.incomplete": "Uheilsleg (3 koordinatar var venta)", "argument.range.empty": "<PERSON>it verde eller verderekkje var venta", "argument.range.ints": "<PERSON><PERSON> heile tal er tillatne, ikk<PERSON> desima<PERSON>l", "argument.range.swapped": "Minst kan ikkje vera større enn høgst", "argument.resource.invalid_type": "Elementet «%s» har det range slaget «%s» (venta «%s»)", "argument.resource.not_found": "Kan ikkje finna elementet «%s» av slaget «%s»", "argument.resource_or_id.failed_to_parse": "Kunne ikkje tolka bygnad: %s", "argument.resource_or_id.invalid": "Ugild <PERSON> eller tagg", "argument.resource_or_id.no_such_element": "Kan ikke finne element '%s' i register '%s'", "argument.resource_selector.not_found": "Ingen funn for utveljar '%s' av type '%s'", "argument.resource_tag.invalid_type": "Taggen «%s» har det range slaget «%s» (venta «%s»)", "argument.resource_tag.not_found": "Kan ikkje finna taggen «%s» av slaget «%s»", "argument.rotation.incomplete": "Uheilsleg (2 koordinatar var venta)", "argument.scoreHolder.empty": "Ingen relevante skòretalshaldarar vart funne", "argument.scoreboardDisplaySlot.invalid": "«%s» er ein ukjend visingsstad", "argument.style.invalid": "Ugild stil: %s", "argument.time.invalid_tick_count": "Tikketal må vera ikkje-negativt", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON>", "argument.time.tick_count_too_low": "Tikketalet kan ikkje vera lægre enn %s; fann %s", "argument.uuid.invalid": "Ugild UUID", "argument.waypoint.invalid": "<PERSON>t eining er ikkje ein varde", "arguments.block.tag.unknown": "«%s» er ein ukjend blokktagg", "arguments.function.tag.unknown": "«%s» er ein ukjend funksjonstagg", "arguments.function.unknown": "«%s» er ein ukjend funk<PERSON>jon", "arguments.item.component.expected": "Venta gjenstandskomponent", "arguments.item.component.malformed": "Feilaktig '%s'-komponent: '%s'", "arguments.item.component.repeated": "Gjenstandskomponent «%s» ble gje<PERSON><PERSON>, men berre éin verdi kan bli oppgjeve", "arguments.item.component.unknown": "Ukjent gjenstandskomponent '%s'", "arguments.item.malformed": "Feilforma gjenstand '%s'", "arguments.item.overstacked": "%s kan berre verta lødd opp til %s", "arguments.item.predicate.malformed": "Feilaktig '%s'-predikat: '%s'", "arguments.item.predicate.unknown": "Ukjent gjenstandspredikat '%s'", "arguments.item.tag.unknown": "«%s» er ein ukjend gjenstandstagg", "arguments.nbtpath.node.invalid": "Ugild NBT-baneelement", "arguments.nbtpath.nothing_found": "Fann ingen samsvarande element %s", "arguments.nbtpath.too_deep": "Resulterande NBT for djupt nysta", "arguments.nbtpath.too_large": "Resulterande NBT for stor", "arguments.objective.notFound": "«%s» er eit ukjent mål på skòretavla", "arguments.objective.readonly": "Målet «%s» på skòretavla er skriveverna", "arguments.operation.div0": "Kan ikk<PERSON> dei<PERSON> null", "arguments.operation.invalid": "<PERSON><PERSON><PERSON>", "arguments.swizzle.invalid": "Ugild akselsamanstelling. Ei samanstelling av «x», «y» og «z» var venta", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "+%s%% %s", "attribute.name.armor": "Rustning", "attribute.name.armor_toughness": "Rustningsherdigheit", "attribute.name.attack_damage": "Angrepsskade", "attribute.name.attack_knockback": "Angrepstilbakeslag", "attribute.name.attack_speed": "Åtakssnøggleik", "attribute.name.block_break_speed": "Gravesnøggleik", "attribute.name.block_interaction_range": "Blokkrekkevidde", "attribute.name.burning_time": "Fyrtid", "attribute.name.camera_distance": "Kameraavstand", "attribute.name.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for einingssamhandling", "attribute.name.explosion_knockback_resistance": "Eksplosjonstilbakeslagsmotstand", "attribute.name.fall_damage_multiplier": "Fallskademultiplikator", "attribute.name.flying_speed": "Flygesnøggleik", "attribute.name.follow_range": "Skapningsforfølgelsesvidde", "attribute.name.generic.armor": "Rustning", "attribute.name.generic.armor_toughness": "Rustningsstyrke", "attribute.name.generic.attack_damage": "Skade", "attribute.name.generic.attack_knockback": "Atterslag på åtak", "attribute.name.generic.attack_speed": "Åtakssnøggleik", "attribute.name.generic.block_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for blokksamhandling", "attribute.name.generic.burning_time": "Fyrtid", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for einingssamhandling", "attribute.name.generic.explosion_knockback_resistance": "Eksplosjonstilbakeslagsmotstand", "attribute.name.generic.fall_damage_multiplier": "Multiplikator for fallskade", "attribute.name.generic.flying_speed": "Flygesnøggleik", "attribute.name.generic.follow_range": "Fråstand monster vil fylgja deg", "attribute.name.generic.gravity": "Tyngdekraft", "attribute.name.generic.jump_strength": "Hoppstyrke", "attribute.name.generic.knockback_resistance": "Motstand mot atterslag", "attribute.name.generic.luck": "Lukke", "attribute.name.generic.max_absorption": "Høgst oppsuging", "attribute.name.generic.max_health": "Høgst helse", "attribute.name.generic.movement_efficiency": "Fremkommelighet", "attribute.name.generic.movement_speed": "Snøggleik", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON> fallhøg<PERSON>", "attribute.name.generic.scale": "Storleik", "attribute.name.generic.step_height": "Steghøgd", "attribute.name.generic.water_movement_efficiency": "Vannfremkommelighet", "attribute.name.gravity": "Tyngdekraft", "attribute.name.horse.jump_strength": "<PERSON><PERSON> si hoppekraft", "attribute.name.jump_strength": "Hoppstyrke", "attribute.name.knockback_resistance": "Motstand mot atterslag", "attribute.name.luck": "<PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Høyeste absorbsjonsmengde", "attribute.name.max_health": "Høyest antall liv", "attribute.name.mining_efficiency": "Graveferdighet", "attribute.name.movement_efficiency": "Fremkommelighet", "attribute.name.movement_speed": "Snøggleik", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blokkgravarfart", "attribute.name.player.block_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for blokksamhandling", "attribute.name.player.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for einingssamhandling", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "Snikesnøggleik", "attribute.name.player.submerged_mining_speed": "Undervass<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sweeping_damage_ratio": "Sveipeskaderate", "attribute.name.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Snikesnøggleik", "attribute.name.spawn_reinforcements": "Zombieforsterkninger", "attribute.name.step_height": "Steghøgd", "attribute.name.submerged_mining_speed": "Undervass<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.sweeping_damage_ratio": "Sveipeskaderate", "attribute.name.tempt_range": "Skapningslokkevidde", "attribute.name.water_movement_efficiency": "Vannfremkommelighet", "attribute.name.waypoint_receive_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> for vardemottaking", "attribute.name.waypoint_transmit_range": "Rek<PERSON>vidd for vardekringkasting", "attribute.name.zombie.spawn_reinforcements": "Stønad av da<PERSON>ar", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bambusskog", "biome.minecraft.basalt_deltas": "Basaltosøyr", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Bjørkeskog", "biome.minecraft.cherry_grove": "Kissebærlund", "biome.minecraft.cold_ocean": "Kaldt hav", "biome.minecraft.crimson_forest": "Blodskog", "biome.minecraft.dark_forest": "Mørkskog", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON> kaldt hav", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON> frose hav", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON> lunke hav", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON> hav", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Dropsteinsholer", "biome.minecraft.end_barrens": "Enden - Ufræva", "biome.minecraft.end_highlands": "Enden - Høglanda", "biome.minecraft.end_midlands": "Enden - Midlanda", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "Blomsterskog", "biome.minecraft.forest": "Skog", "biome.minecraft.frozen_ocean": "Frose hav", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "Frosen elv", "biome.minecraft.grove": "<PERSON>", "biome.minecraft.ice_spikes": "<PERSON><PERSON><PERSON>", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Lunke hav", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "biome.minecraft.mangrove_swamp": "Mangrovemyr", "biome.minecraft.meadow": "Eng", "biome.minecraft.mushroom_fields": "Soppsletter", "biome.minecraft.nether_wastes": "Netherøyder", "biome.minecraft.ocean": "Hav", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON> bj<PERSON>rkes<PERSON>", "biome.minecraft.old_growth_pine_taiga": "Gamal fureskog", "biome.minecraft.old_growth_spruce_taiga": "Gamal granskog", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Sletter", "biome.minecraft.river": "Elv", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "Enden - <PERSON><PERSON><PERSON> øyar", "biome.minecraft.snowy_beach": "Snøtekt strand", "biome.minecraft.snowy_plains": "Snøtekte vidder", "biome.minecraft.snowy_slopes": "Snøtekte lier", "biome.minecraft.snowy_taiga": "Snøtekt barskog", "biome.minecraft.soul_sand_valley": "Sjelesandsdal", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON> jungel", "biome.minecraft.stony_peaks": "<PERSON><PERSON> tin<PERSON>", "biome.minecraft.stony_shore": "Steinstrand", "biome.minecraft.sunflower_plains": "Solvendeleng", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Barskog", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "Tomrome<PERSON>", "biome.minecraft.warm_ocean": "Varmt hav", "biome.minecraft.warped_forest": "Vrideskog", "biome.minecraft.windswept_forest": "Vindsliten skog", "biome.minecraft.windswept_gravelly_hills": "<PERSON>dslitne g<PERSON>", "biome.minecraft.windswept_hills": "Vindslitne åsar", "biome.minecraft.windswept_savanna": "Vindsliten savanne", "biome.minecraft.wooded_badlands": "Skogvakse stein<PERSON>", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Akasiedør", "block.minecraft.acacia_fence": "Gjerde av akasietre", "block.minecraft.acacia_fence_gate": "Grind av akasietre", "block.minecraft.acacia_hanging_sign": "Hengande akasieskilt", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Akasiestomn", "block.minecraft.acacia_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Akasietrykkplate", "block.minecraft.acacia_sapling": "A<PERSON><PERSON><PERSON>ning", "block.minecraft.acacia_sign": "Akasieskilt", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Akasietrapp", "block.minecraft.acacia_trapdoor": "Akasielem", "block.minecraft.acacia_wall_hanging_sign": "Hengande akasieskilt på vegg", "block.minecraft.acacia_wall_sign": "Akasieveggskilt", "block.minecraft.acacia_wood": "Akasietre", "block.minecraft.activator_rail": "Aktivatorskjene", "block.minecraft.air": "Luft", "block.minecraft.allium": "<PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametystblokk", "block.minecraft.amethyst_cluster": "Ametyskklyngje", "block.minecraft.ancient_debris": "<PERSON><PERSON> le<PERSON>", "block.minecraft.andesite": "<PERSON><PERSON>", "block.minecraft.andesite_slab": "Andesitthelle", "block.minecraft.andesite_stairs": "Andesittrapp", "block.minecraft.andesite_wall": "Andesittmur", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Festa melonstilk", "block.minecraft.attached_pumpkin_stem": "Festa graskarstilk", "block.minecraft.azalea": "Lyngrose", "block.minecraft.azalea_leaves": "Lauv av lyngrosetre", "block.minecraft.azure_bluet": "Houstonia", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblokk", "block.minecraft.bamboo_button": "Bambusknapp", "block.minecraft.bamboo_door": "Bambusdør", "block.minecraft.bamboo_fence": "Bambusgjerde", "block.minecraft.bamboo_fence_gate": "Bambusgrind", "block.minecraft.bamboo_hanging_sign": "Hengande bambusskilt", "block.minecraft.bamboo_mosaic": "Bambusmosaikk", "block.minecraft.bamboo_mosaic_slab": "Helle av bambusmosaikk", "block.minecraft.bamboo_mosaic_stairs": "Trapp av bambusmosaikk", "block.minecraft.bamboo_planks": "Bambusplankar", "block.minecraft.bamboo_pressure_plate": "Bambustrykkplate", "block.minecraft.bamboo_sapling": "Bambusskot", "block.minecraft.bamboo_sign": "Bambusskilt", "block.minecraft.bamboo_slab": "Helle av bambus", "block.minecraft.bamboo_stairs": "Trapp av bambus", "block.minecraft.bamboo_trapdoor": "Bambuslem", "block.minecraft.bamboo_wall_hanging_sign": "Hengande bambusskilt på vegg", "block.minecraft.bamboo_wall_sign": "Bambusveggskilt", "block.minecraft.banner.base.black": "<PERSON><PERSON>t botn", "block.minecraft.banner.base.blue": "Blå botn", "block.minecraft.banner.base.brown": "<PERSON><PERSON> botn", "block.minecraft.banner.base.cyan": "Blågrøn botn", "block.minecraft.banner.base.gray": "Grå botn", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.light_blue": "Ljosblå botn", "block.minecraft.banner.base.light_gray": "Ljosgrå botn", "block.minecraft.banner.base.lime": "Limegr<PERSON><PERSON> botn", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.orange": "Oransje botn", "block.minecraft.banner.base.pink": "<PERSON> botn", "block.minecraft.banner.base.purple": "<PERSON><PERSON> botn", "block.minecraft.banner.base.red": "<PERSON><PERSON> botn", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON> botn", "block.minecraft.banner.base.yellow": "<PERSON>ul botn", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.light_blue": "Ljosbl<PERSON><PERSON> bord", "block.minecraft.banner.border.light_gray": "L<PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.orange": "Or<PERSON><PERSON> bord", "block.minecraft.banner.border.pink": "<PERSON> bord", "block.minecraft.banner.border.purple": "<PERSON><PERSON> bord", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.yellow": "<PERSON><PERSON> bord", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.pink": "<PERSON>", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.blue": "Blå skive", "block.minecraft.banner.circle.brown": "<PERSON><PERSON> skive", "block.minecraft.banner.circle.cyan": "Blågrøn skive", "block.minecraft.banner.circle.gray": "Grå skive", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.light_blue": "Ljosblå skive", "block.minecraft.banner.circle.light_gray": "Ljosgrå skive", "block.minecraft.banner.circle.lime": "Limegrø<PERSON> skive", "block.minecraft.banner.circle.magenta": "L<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.orange": "Oransje skive", "block.minecraft.banner.circle.pink": "<PERSON> skive", "block.minecraft.banner.circle.purple": "<PERSON><PERSON> skive", "block.minecraft.banner.circle.red": "<PERSON><PERSON> skive", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.yellow": "<PERSON>ul skive", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.blue": "B<PERSON>å creeper", "block.minecraft.banner.creeper.brown": "<PERSON>run creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_blue": "Ljosblå creeper", "block.minecraft.banner.creeper.light_gray": "Ljosgrå creeper", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.pink": "<PERSON> creeper", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON> creeper", "block.minecraft.banner.creeper.red": "Raud creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.yellow": "Gul creeper", "block.minecraft.banner.cross.black": "Svart andreaskross", "block.minecraft.banner.cross.blue": "Blått kross", "block.minecraft.banner.cross.brown": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.cross.cyan": "Blågrøn kross", "block.minecraft.banner.cross.gray": "Grå kross", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.cross.light_blue": "Ljosblå kross", "block.minecraft.banner.cross.light_gray": "Ljosgrå kross", "block.minecraft.banner.cross.lime": "Limegrøn kross", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.cross.orange": "Oransje k<PERSON>", "block.minecraft.banner.cross.pink": "<PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "Gul kross", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON>t bord med snitt", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.light_blue": "Ljosblått bord med snitt", "block.minecraft.banner.curly_border.light_gray": "Ljosgr<PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.orange": "Oransje bord med snitt", "block.minecraft.banner.curly_border.pink": "<PERSON> bord med snitt", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> bord med snitt", "block.minecraft.banner.curly_border.yellow": "<PERSON>ult bord med snitt", "block.minecraft.banner.diagonal_left.black": "Svart skrådelt venstre", "block.minecraft.banner.diagonal_left.blue": "Blå skrådelt venstre", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON> skr<PERSON><PERSON>t venstre", "block.minecraft.banner.diagonal_left.cyan": "Blågrøn skrådelt venstre", "block.minecraft.banner.diagonal_left.gray": "Grå skrådelt venstre", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> skrå<PERSON>t venstre", "block.minecraft.banner.diagonal_left.light_blue": "Ljosblå skrådelt venstre", "block.minecraft.banner.diagonal_left.light_gray": "Ljosgrå skrådelt venstre", "block.minecraft.banner.diagonal_left.lime": "Limegrøn skrådelt venstre", "block.minecraft.banner.diagonal_left.magenta": "Ljoslilla skrådelt venstre", "block.minecraft.banner.diagonal_left.orange": "Oransje skrådelt venstre", "block.minecraft.banner.diagonal_left.pink": "<PERSON> skr<PERSON>t venstre", "block.minecraft.banner.diagonal_left.purple": "Lilla skrådelt venstre", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON> s<PERSON> venstre", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> skr<PERSON><PERSON>t venstre", "block.minecraft.banner.diagonal_left.yellow": "Gul skrådelt venstre", "block.minecraft.banner.diagonal_right.black": "Svart skrådelt høgre", "block.minecraft.banner.diagonal_right.blue": "Blå skrådelt høgre", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON> skr<PERSON><PERSON>t høgre", "block.minecraft.banner.diagonal_right.cyan": "Blågrøn skrådelt høgre", "block.minecraft.banner.diagonal_right.gray": "Grå skrådelt høgre", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> skr<PERSON>t høgre", "block.minecraft.banner.diagonal_right.light_blue": "Ljosblå skrådelt høgre", "block.minecraft.banner.diagonal_right.light_gray": "Ljosgrå skrådelt høgre", "block.minecraft.banner.diagonal_right.lime": "Limegrøn skrådelt høgre", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON><PERSON> skrådelt høgre", "block.minecraft.banner.diagonal_right.orange": "Oransje skrådelt høgre", "block.minecraft.banner.diagonal_right.pink": "<PERSON> s<PERSON>r<PERSON> høgre", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> skrå<PERSON>t høgre", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON> s<PERSON> høgre", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON>t høgre", "block.minecraft.banner.diagonal_right.yellow": "Gul skrådelt høgre", "block.minecraft.banner.diagonal_up_left.black": "Svart omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.blue": "Blå omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON> omsnudd skrå<PERSON>t venstre", "block.minecraft.banner.diagonal_up_left.cyan": "Blågrøn omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.gray": "Grå omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.light_blue": "Ljosblå omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.light_gray": "Ljosgrå omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.lime": "Limegrøn omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.magenta": "Ljoslilla omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.orange": "Oransje omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> omsnudd skrå<PERSON>t venstre", "block.minecraft.banner.diagonal_up_left.purple": "Lilla omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON> o<PERSON>dd skr<PERSON>t venstre", "block.minecraft.banner.diagonal_up_left.white": "K<PERSON>t omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_left.yellow": "Gul omsnudd skrådelt venstre", "block.minecraft.banner.diagonal_up_right.black": "Svart omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.blue": "Blå omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON> omsnudd skr<PERSON>t høgre", "block.minecraft.banner.diagonal_up_right.cyan": "Blågrøn omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.gray": "Grå omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON> omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.light_blue": "Ljosblå omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.light_gray": "Ljosgrå omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.lime": "Limegrøn omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON><PERSON> omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.orange": "Oransje omsnudd skrådelt høgre", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> omsnudd skr<PERSON><PERSON>t høgre", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON> o<PERSON>nudd skrå<PERSON>t høgre", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON> o<PERSON> skr<PERSON> høgre", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> oms<PERSON>dd skrå<PERSON>t høgre", "block.minecraft.banner.diagonal_up_right.yellow": "Gul omsnudd skrådelt høgre", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.blue": "Blå virvel", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> virvel", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.gray": "Gr<PERSON> virvel", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.light_blue": "Lyseblå virvel", "block.minecraft.banner.flow.light_gray": "Lysegrå virvel", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON>a virvel", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flow.pink": "<PERSON> virvel", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> virvel", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON> blome", "block.minecraft.banner.flower.blue": "Blå blome", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> blome", "block.minecraft.banner.flower.cyan": "B<PERSON><PERSON>gr<PERSON><PERSON> blome", "block.minecraft.banner.flower.gray": "Gr<PERSON> blome", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.flower.light_blue": "Ljosblå blome", "block.minecraft.banner.flower.light_gray": "Ljosgrå blome", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> blome", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON> blome", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.flower.pink": "<PERSON> blome", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> blome", "block.minecraft.banner.flower.red": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> blome", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.blue": "Blå klote", "block.minecraft.banner.globe.brown": "<PERSON><PERSON> k<PERSON>e", "block.minecraft.banner.globe.cyan": "Blågrøn klote", "block.minecraft.banner.globe.gray": "Grå klote", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.light_blue": "Ljosblå klote", "block.minecraft.banner.globe.light_gray": "Ljosgrå klote", "block.minecraft.banner.globe.lime": "Limegrøn", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON><PERSON> klote", "block.minecraft.banner.globe.orange": "Oransje k<PERSON>e", "block.minecraft.banner.globe.pink": "<PERSON>", "block.minecraft.banner.globe.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.globe.red": "<PERSON><PERSON>", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON> klote", "block.minecraft.banner.gradient.black": "Svart gradient", "block.minecraft.banner.gradient.blue": "Blå gradient", "block.minecraft.banner.gradient.brown": "Brun gradient", "block.minecraft.banner.gradient.cyan": "Blågrøn gradient", "block.minecraft.banner.gradient.gray": "Grå gradient", "block.minecraft.banner.gradient.green": "Grøn gradient", "block.minecraft.banner.gradient.light_blue": "Ljosblå gradient", "block.minecraft.banner.gradient.light_gray": "Ljosgrå gradient", "block.minecraft.banner.gradient.lime": "Limegrøn gradient", "block.minecraft.banner.gradient.magenta": "Ljoslilla gradient", "block.minecraft.banner.gradient.orange": "Oransje gradient", "block.minecraft.banner.gradient.pink": "Rosa gradient", "block.minecraft.banner.gradient.purple": "Lilla gradient", "block.minecraft.banner.gradient.red": "Raud gradient", "block.minecraft.banner.gradient.white": "Kvit gradient", "block.minecraft.banner.gradient.yellow": "Gul gradient", "block.minecraft.banner.gradient_up.black": "Svart omvendt gradient", "block.minecraft.banner.gradient_up.blue": "Blå omvendt gradient", "block.minecraft.banner.gradient_up.brown": "<PERSON>run om<PERSON>dt gradient", "block.minecraft.banner.gradient_up.cyan": "Blågrøn omvendt gradient", "block.minecraft.banner.gradient_up.gray": "Grå omvendt gradient", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> om<PERSON> gradient", "block.minecraft.banner.gradient_up.light_blue": "Ljosblå omvendt gradient", "block.minecraft.banner.gradient_up.light_gray": "Ljosgrå omvendt gradient", "block.minecraft.banner.gradient_up.lime": "Limegrøn omvendt gradient", "block.minecraft.banner.gradient_up.magenta": "L<PERSON><PERSON><PERSON> omvendt gradient", "block.minecraft.banner.gradient_up.orange": "Oransje omvendt gradient", "block.minecraft.banner.gradient_up.pink": "<PERSON> omvendt gradient", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> omvendt gradient", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> gradient", "block.minecraft.banner.gradient_up.white": "K<PERSON>t omvendt gradient", "block.minecraft.banner.gradient_up.yellow": "Gul omvendt gradient", "block.minecraft.banner.guster.black": "Svart vindkaster", "block.minecraft.banner.guster.blue": "Blå vindkaster", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "Grå vindkaster", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> vindkaster", "block.minecraft.banner.guster.light_blue": "Lyseblå vindkaster", "block.minecraft.banner.guster.light_gray": "Lysegrå vindkaster", "block.minecraft.banner.guster.lime": "Limegr<PERSON>nn vindkaster", "block.minecraft.banner.guster.magenta": "Magenta v<PERSON>", "block.minecraft.banner.guster.orange": "Oransje vindkaster", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.guster.yellow": "Gul vindkaster", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.blue": "B<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> over<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Ljosblå overdel", "block.minecraft.banner.half_horizontal.light_gray": "Ljosgrå overdel", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.pink": "<PERSON> overdel", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON> over<PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> over<PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal_bottom.black": "Svart underdel", "block.minecraft.banner.half_horizontal_bottom.blue": "Blå underdel", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON>run under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "Blågrøn underdel", "block.minecraft.banner.half_horizontal_bottom.gray": "Grå underdel", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Ljosblå underdel", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Ljosgrå underdel", "block.minecraft.banner.half_horizontal_bottom.lime": "Limegrøn underdel", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.orange": "Oransje underdel", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON> underdel", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON> venstredel", "block.minecraft.banner.half_vertical.blue": "Blå venstredel", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Blågr<PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.half_vertical.gray": "G<PERSON><PERSON> venstredel", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Ljosblå venstredel", "block.minecraft.banner.half_vertical.light_gray": "Ljosgrå venstredel", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.half_vertical_right.blue": "Blå høgredel", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Blågr<PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON> høgredel", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Ljosblå høgredel", "block.minecraft.banner.half_vertical_right.light_gray": "Ljosgrå høgredel", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.pink": "<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.light_blue": "Ljosblå ting", "block.minecraft.banner.mojang.light_gray": "Ljosgrå ting", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.pink": "<PERSON> ting", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.red": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON> ting", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> ting", "block.minecraft.banner.piglin.black": "<PERSON><PERSON>t snut", "block.minecraft.banner.piglin.blue": "Blå snut", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON> snut", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON> snut", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON> snut", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.piglin.light_blue": "Ljosblå snut", "block.minecraft.banner.piglin.light_gray": "Ljosgrå snut", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> snut", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON> snut", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON> snut", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON> snut", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> snut", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON>ul snut", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>t rute", "block.minecraft.banner.rhombus.blue": "Blå rute", "block.minecraft.banner.rhombus.brown": "Brun rute", "block.minecraft.banner.rhombus.cyan": "Blågrøn rute", "block.minecraft.banner.rhombus.gray": "Grå rute", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.light_blue": "Ljosblå rute", "block.minecraft.banner.rhombus.light_gray": "Ljosgrå rute", "block.minecraft.banner.rhombus.lime": "<PERSON>egr<PERSON><PERSON> rute", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.orange": "Oransje rute", "block.minecraft.banner.rhombus.pink": "Rosa rute", "block.minecraft.banner.rhombus.purple": "<PERSON>la rute", "block.minecraft.banner.rhombus.red": "Raud rute", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>t rute", "block.minecraft.banner.rhombus.yellow": "Gul rute", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.cyan": "Blågr<PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.light_blue": "Ljosblå<PERSON> stolpar", "block.minecraft.banner.small_stripes.light_gray": "Ljosgr<PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON> stolpar", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON> stol<PERSON>", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON> striper", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.yellow": "Gule stolpar", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON><PERSON> nedre venst<PERSON>on", "block.minecraft.banner.square_bottom_left.blue": "Blå nedre venstrekanton", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON> ne<PERSON> ve<PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "Blågrø<PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "Grå nedre venstrek<PERSON>on", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON><PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "Ljosblå nedre venstrekanton", "block.minecraft.banner.square_bottom_left.light_gray": "Ljosgrå nedre venstrekanton", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON><PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_left.orange": "Oransje nedre venstrek<PERSON>on", "block.minecraft.banner.square_bottom_left.pink": "<PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON><PERSON> ne<PERSON> ve<PERSON>", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON> nedre venst<PERSON>", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON><PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.blue": "Blå nedre høgrek<PERSON>on", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "Blågrø<PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.gray": "Gr<PERSON> nedre høg<PERSON>on", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "Ljosblå nedre høgrekanton", "block.minecraft.banner.square_bottom_right.light_gray": "Ljosgrå nedre høgrek<PERSON>on", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.orange": "Or<PERSON><PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON> nedre hø<PERSON>", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON><PERSON> ne<PERSON> hø<PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON> ne<PERSON> hø<PERSON>", "block.minecraft.banner.square_top_left.black": "<PERSON><PERSON><PERSON> øvre venstrek<PERSON>on", "block.minecraft.banner.square_top_left.blue": "Blå øvre venstrekanton", "block.minecraft.banner.square_top_left.brown": "<PERSON><PERSON> <PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.square_top_left.cyan": "Blågrøn øvre venstrek<PERSON>on", "block.minecraft.banner.square_top_left.gray": "G<PERSON><PERSON> øvre venstrek<PERSON>on", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON> venst<PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Ljosblå øvre venstrekanton", "block.minecraft.banner.square_top_left.light_gray": "Ljosgrå øvre venstrekanton", "block.minecraft.banner.square_top_left.lime": "Limegr<PERSON><PERSON> øvre venstrek<PERSON>on", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON><PERSON> øvre venst<PERSON>", "block.minecraft.banner.square_top_left.orange": "Oransje øvre venstrek<PERSON>on", "block.minecraft.banner.square_top_left.pink": "<PERSON> venst<PERSON>", "block.minecraft.banner.square_top_left.purple": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON> ø<PERSON> ve<PERSON>", "block.minecraft.banner.square_top_left.white": "<PERSON><PERSON><PERSON> ø<PERSON> venst<PERSON>", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON> <PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON> øvre høg<PERSON>", "block.minecraft.banner.square_top_right.blue": "Blå øvre høgrekanton", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.cyan": "Blågrø<PERSON> øvre høg<PERSON>on", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> øvre høg<PERSON>on", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.light_blue": "Ljosblå øvre høgrekanton", "block.minecraft.banner.square_top_right.light_gray": "Ljosgrå øvre høgrek<PERSON>on", "block.minecraft.banner.square_top_right.lime": "Limegr<PERSON><PERSON> øvre høg<PERSON>on", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON> øvre hø<PERSON>", "block.minecraft.banner.square_top_right.orange": "Or<PERSON><PERSON> ø<PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.pink": "<PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON> <PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.straight_cross.black": "<PERSON>vart kross", "block.minecraft.banner.straight_cross.blue": "Blå kross", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Blågrøn kross", "block.minecraft.banner.straight_cross.gray": "Grå kross", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Ljosblå kross", "block.minecraft.banner.straight_cross.light_gray": "Ljosgrå kross", "block.minecraft.banner.straight_cross.lime": "Limegrøn kross", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.orange": "Oransje k<PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "Gul kross", "block.minecraft.banner.stripe_bottom.black": "Svart skjoldfot", "block.minecraft.banner.stripe_bottom.blue": "Blått botnband", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.cyan": "Bl<PERSON><PERSON><PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON> botn<PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Ljosblått botnband", "block.minecraft.banner.stripe_bottom.light_gray": "Ljosgrått botnband", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.orange": "Oransje botnband", "block.minecraft.banner.stripe_bottom.pink": "<PERSON> botn<PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON> botn<PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON> botn<PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON> botnband", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON> botnband", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.blue": "Blå stolpe", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.cyan": "Blågr<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.gray": "Gr<PERSON> stolpe", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.light_blue": "Ljosblå stolpe", "block.minecraft.banner.stripe_center.light_gray": "Ljosgrå stolpe", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> s<PERSON>pe", "block.minecraft.banner.stripe_center.pink": "<PERSON> stolpe", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_downleft.black": "Svart band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.blue": "Blått band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.brown": "Brunt band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.cyan": "Blågrøn band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.gray": "Grått band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.green": "Grøn band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.light_blue": "Ljosblått band frå høgretopp til vestrebotn", "block.minecraft.banner.stripe_downleft.light_gray": "Ljosgrått band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.lime": "Limegrønt band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.magenta": "Ljoslilla band frå høgretopp til vestrebotn", "block.minecraft.banner.stripe_downleft.orange": "Oransje band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.pink": "Rosa band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.purple": "Lilla band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.red": "Raudt band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.white": "Kvitt band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downleft.yellow": "Gult band frå høgretopp til venstrebotn", "block.minecraft.banner.stripe_downright.black": "Svart skråbjelke", "block.minecraft.banner.stripe_downright.blue": "Blått band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.brown": "Brunt band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.cyan": "Blågrønt band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.gray": "Grått band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.green": "Grønt band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.light_blue": "Ljosblått band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.light_gray": "Ljosgrått band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.lime": "Limegrønt band frå venstretopp til høgrebotn", "block.minecraft.banner.stripe_downright.magenta": "Ljoslilla band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.orange": "Oransje band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.pink": "Rosa band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.purple": "Lilla band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.red": "Raudt band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.white": "Kvitt band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_downright.yellow": "Gult band frå ventretopp til høgrebotn", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON> høgrestol<PERSON>", "block.minecraft.banner.stripe_left.blue": "Blå høgrestolpe", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "Blågrøn hø<PERSON>ol<PERSON>", "block.minecraft.banner.stripe_left.gray": "Grå høgrestolpe", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Ljosblå høgrestolpe", "block.minecraft.banner.stripe_left.light_gray": "Ljosgrå høgrestolpe", "block.minecraft.banner.stripe_left.lime": "Limegr<PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> høgrestolpe", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "Blått vassrett midband", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.cyan": "Blågr<PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.light_blue": "Ljosblått vassrett midband", "block.minecraft.banner.stripe_middle.light_gray": "Ljosgrått vassrett midband", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.orange": "Oransje vassrett midband", "block.minecraft.banner.stripe_middle.pink": "<PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON> vass<PERSON>t midband", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON> vass<PERSON>t midband", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON> vassrett midband", "block.minecraft.banner.stripe_right.black": "Svart venstrestolpe", "block.minecraft.banner.stripe_right.blue": "Blå venstrestolpe", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "Blågrøn venstrestolpe", "block.minecraft.banner.stripe_right.gray": "Grå venstrestolpe", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Ljosblå venstrestolpe", "block.minecraft.banner.stripe_right.light_gray": "Ljosgrå venstrestolpe", "block.minecraft.banner.stripe_right.lime": "Limegr<PERSON><PERSON> venst<PERSON>ol<PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.stripe_right.orange": "Oransje venstrestolpe", "block.minecraft.banner.stripe_right.pink": "<PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> venstrestolpe", "block.minecraft.banner.stripe_right.yellow": "Gul venstrestolpe", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON> sk<PERSON>", "block.minecraft.banner.stripe_top.blue": "Blått toppband", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.cyan": "Blågr<PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON> topp<PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Ljosblått toppband", "block.minecraft.banner.stripe_top.light_gray": "Ljosgrått toppband", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.magenta": "Ljoslilla toppband", "block.minecraft.banner.stripe_top.orange": "Oransje toppband", "block.minecraft.banner.stripe_top.pink": "Rosa toppband", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> topp<PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON> toppband", "block.minecraft.banner.stripe_top.yellow": "Gult toppband", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.blue": "Blå sparre", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.cyan": "Blågr<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.light_blue": "Ljosblå sparre", "block.minecraft.banner.triangle_bottom.light_gray": "Ljosgrå sparre", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.pink": "<PERSON> sparre", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON> spa<PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.yellow": "G<PERSON> sparre", "block.minecraft.banner.triangle_top.black": "<PERSON>vart omvendt sparre", "block.minecraft.banner.triangle_top.blue": "Blå omvendt sparre", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON> o<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_top.cyan": "Blågrøn omvendt sparre", "block.minecraft.banner.triangle_top.gray": "Gr<PERSON> omvendt sparre", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON><PERSON> om<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_top.light_blue": "Ljosblå omvendt sparre", "block.minecraft.banner.triangle_top.light_gray": "Ljosgrå omvendt sparre", "block.minecraft.banner.triangle_top.lime": "<PERSON>egr<PERSON><PERSON> om<PERSON>dt sparre", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON> omvendt sparre", "block.minecraft.banner.triangle_top.orange": "Oransje omvendt sparre", "block.minecraft.banner.triangle_top.pink": "<PERSON> om<PERSON>dt sparre", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> o<PERSON> sparre", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON> <PERSON><PERSON> sparre", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_top.yellow": "Gul omvendt sparre", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON> tanns<PERSON>tt nede", "block.minecraft.banner.triangles_bottom.light_blue": "Ljosblått tannsnitt nede", "block.minecraft.banner.triangles_bottom.light_gray": "Ljosgrått tannsnitt nede", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> tannsnitt botn", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> tanns<PERSON>tt nede", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON><PERSON> tanns<PERSON> nede", "block.minecraft.banner.triangles_bottom.pink": "<PERSON> tan<PERSON> nede", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON> tan<PERSON> nede", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON> tanns<PERSON>tt nede", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> tanns<PERSON>tt nede", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> tannsnitt nede", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.blue": "B<PERSON><PERSON>tt tannsnitt oppe", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON> tanns<PERSON> oppe", "block.minecraft.banner.triangles_top.light_blue": "Ljosblått tannsnitt oppe", "block.minecraft.banner.triangles_top.light_gray": "Ljosgrått tannsnitt oppe", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> tannsnitt topp", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.orange": "Or<PERSON><PERSON> tan<PERSON>tt oppe", "block.minecraft.banner.triangles_top.pink": "<PERSON> tan<PERSON> oppe", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> oppe", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON> tanns<PERSON>tt oppe", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> tannsnitt oppe", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> tannsnitt oppe", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "Spelskjold", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Varde", "block.minecraft.beacon.primary": "Primærkraft", "block.minecraft.beacon.secondary": "Sekundærkraft", "block.minecraft.bed.no_sleep": "Du kan berre sova på natta eller ved torevêr", "block.minecraft.bed.not_safe": "Du kan ikkje sova no. Monster er i nærleiken", "block.minecraft.bed.obstructed": "Senga er sperra", "block.minecraft.bed.occupied": "Senga er oppteka", "block.minecraft.bed.too_far_away": "Senga er for langt undan", "block.minecraft.bedrock": "Berggrunn", "block.minecraft.bee_nest": "Biebol", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Stort droplauv", "block.minecraft.big_dripleaf_stem": "Stilk av stort droplauv", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON><PERSON>eknap<PERSON>", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON> av bjørk", "block.minecraft.birch_fence_gate": "Grind av bjørk", "block.minecraft.birch_hanging_sign": "Hengande bjørkeskilt", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Bjørkestomn", "block.minecraft.birch_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Bjørketrykkplate", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Bjørkeskilt", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "Bjørketrapp", "block.minecraft.birch_trapdoor": "Bjø<PERSON>elem", "block.minecraft.birch_wall_hanging_sign": "Hengande bjørkeskilt på vegg", "block.minecraft.birch_wall_sign": "Bjørkeveggskilt", "block.minecraft.birch_wood": "Bjørketre", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON> fane", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.black_candle": "Svart voksljos", "block.minecraft.black_candle_cake": "<PERSON>ke med svart voksljos", "block.minecraft.black_carpet": "<PERSON><PERSON>t golvteppe", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON>t glasert terrakotta", "block.minecraft.black_shulker_box": "Svart shulkerboks", "block.minecraft.black_stained_glass": "Svart glas", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON>t glasrute", "block.minecraft.black_terracotta": "Svart terrakotta", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_stairs": "Svartsteinstrapp", "block.minecraft.blackstone_wall": "<PERSON>vartsteinsm<PERSON>", "block.minecraft.blast_furnace": "Malmomn", "block.minecraft.blue_banner": "Blå fane", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.blue_candle": "Blått voksljos", "block.minecraft.blue_candle_cake": "<PERSON>ke med blått vok<PERSON>l<PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON><PERSON> golvteppe", "block.minecraft.blue_concrete": "Blå betong", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> betongpulver", "block.minecraft.blue_glazed_terracotta": "Blå glasert terrakotta", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "Blå orkidé", "block.minecraft.blue_shulker_box": "Blå shulkerboks", "block.minecraft.blue_stained_glass": "Blått glas", "block.minecraft.blue_stained_glass_pane": "Blå glasrute", "block.minecraft.blue_terracotta": "Blå terrakotta", "block.minecraft.blue_wool": "Blå ull", "block.minecraft.bone_block": "Beinblokk", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "Hjernekorall", "block.minecraft.brain_coral_block": "Hjernekorallblokk", "block.minecraft.brain_coral_fan": "Hjernekorallvifte", "block.minecraft.brain_coral_wall_fan": "Hjernekorallveggvifte", "block.minecraft.brewing_stand": "Bryggjereiskap", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "Teglmur", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON> <PERSON>e", "block.minecraft.brown_bed": "<PERSON><PERSON> seng", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON> vok<PERSON>ljos", "block.minecraft.brown_candle_cake": "Kake med brunt voksl<PERSON>", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> golv<PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON> glasert terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON> sopp", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON> glas<PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON> terra<PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>l", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Boblekorallblokk", "block.minecraft.bubble_coral_fan": "Boblekorallvifte", "block.minecraft.bubble_coral_wall_fan": "Boblekorallveggvifte", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON> ametyst", "block.minecraft.bush": "Busk", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Kalkspat", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> sculk<PERSON><PERSON><PERSON>", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Voksljos", "block.minecraft.candle_cake": "<PERSON>ke med vok<PERSON>ljos", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Kartteiknarbord", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.cauldron": "Gryte", "block.minecraft.cave_air": "Hòleluft", "block.minecraft.cave_vines": "Hengande hòlevokster", "block.minecraft.cave_vines_plant": "Hengande hòlevokster", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Kjedekommandoblokk", "block.minecraft.cherry_button": "Kissebærknapp", "block.minecraft.cherry_door": "Kisseb<PERSON>rdør", "block.minecraft.cherry_fence": "Kissebærgjerde", "block.minecraft.cherry_fence_gate": "Kissebærgrind", "block.minecraft.cherry_hanging_sign": "Hengande kissebærskilt", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "Kissebærstomn", "block.minecraft.cherry_planks": "Kisseb<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "Kissebærtrykkplate", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Kissebærskilt", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "Kissebærle<PERSON>", "block.minecraft.cherry_wall_hanging_sign": "Hengande kissebærskilt på vegg", "block.minecraft.cherry_wall_sign": "Kissebærveggskilt", "block.minecraft.cherry_wood": "Kisseb<PERSON>rt<PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.chiseled_bookshelf": "<PERSON>a bokhylle", "block.minecraft.chiseled_copper": "<PERSON>a kopar", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "Forma nethertegl", "block.minecraft.chiseled_polished_blackstone": "Forma finpus<PERSON>", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON>", "block.minecraft.chiseled_red_sandstone": "<PERSON>a raud sandstein", "block.minecraft.chiseled_resin_bricks": "Forma k<PERSON>etegl", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "<PERSON>a stein<PERSON>r", "block.minecraft.chiseled_tuff": "Forma tuff", "block.minecraft.chiseled_tuff_bricks": "Forma tuff<PERSON>l", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Korvokster", "block.minecraft.clay": "Le<PERSON>", "block.minecraft.closed_eyeblossom": "Attlaten augblom", "block.minecraft.coal_block": "Kolblokk", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Grov mold", "block.minecraft.cobbled_deepslate": "Djupskifer-brustein", "block.minecraft.cobbled_deepslate_slab": "Helle av djupskifer-brustein", "block.minecraft.cobbled_deepslate_stairs": "Trapp av djups<PERSON>fer-brustein", "block.minecraft.cobbled_deepslate_wall": "<PERSON>r av d<PERSON><PERSON><PERSON><PERSON>-brustein", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Kommandoblokk", "block.minecraft.comparator": "Redstone-samanliknar", "block.minecraft.composter": "Kompostbing", "block.minecraft.conduit": "Flødar", "block.minecraft.copper_block": "Koparblokk", "block.minecraft.copper_bulb": "Koparpære", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Koparmalm", "block.minecraft.copper_trapdoor": "Koparlem", "block.minecraft.cornflower": "Kornblom", "block.minecraft.cracked_deepslate_bricks": "Sprokken djupskifertegl", "block.minecraft.cracked_deepslate_tiles": "Sprokkne d<PERSON>ferfliser", "block.minecraft.cracked_nether_bricks": "Sprok<PERSON> nethertegl", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> fin<PERSON> s<PERSON>l", "block.minecraft.cracked_stone_bricks": "Sprokken steinmur", "block.minecraft.crafter": "<PERSON><PERSON>", "block.minecraft.crafting_table": "Emningsbord", "block.minecraft.creaking_heart": "Knirkehjar<PERSON>", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_button": "Blodknapp", "block.minecraft.crimson_door": "Bloddør", "block.minecraft.crimson_fence": "Blodgjerde", "block.minecraft.crimson_fence_gate": "Blodgrind", "block.minecraft.crimson_fungus": "Blodsopp", "block.minecraft.crimson_hanging_sign": "Hengande blodskilt", "block.minecraft.crimson_hyphae": "B<PERSON>dhyfar", "block.minecraft.crimson_nylium": "Blodnycel", "block.minecraft.crimson_planks": "Blodplankar", "block.minecraft.crimson_pressure_plate": "Blodtrykkplate", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Blodskilt", "block.minecraft.crimson_slab": "B<PERSON>dh<PERSON>", "block.minecraft.crimson_stairs": "Blodtrapp", "block.minecraft.crimson_stem": "Blodstomn", "block.minecraft.crimson_trapdoor": "Blodlem", "block.minecraft.crimson_wall_hanging_sign": "Hengande blodskilt på vegg", "block.minecraft.crimson_wall_sign": "Blodveggskilt", "block.minecraft.crying_obsidian": "Gråtande obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON> kopar", "block.minecraft.cut_copper_slab": "Heller av skoren kopar", "block.minecraft.cut_copper_stairs": "Trapp av skoren kopar", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON> raud sandstein", "block.minecraft.cut_red_sandstone_slab": "Helle av skoren raud sandstein", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON> sandstein", "block.minecraft.cut_sandstone_slab": "Helle av skoren sandstein", "block.minecraft.cyan_banner": "<PERSON><PERSON>ågr<PERSON><PERSON> fane", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.cyan_candle": "Blågrø<PERSON> vok<PERSON>ljos", "block.minecraft.cyan_candle_cake": "<PERSON>ke med blåg<PERSON><PERSON><PERSON> vok<PERSON>l<PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gol<PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "Blågrøn glasert terrakotta", "block.minecraft.cyan_shulker_box": "Blågrøn shulkerboks", "block.minecraft.cyan_stained_glass": "Blågr<PERSON><PERSON> glas", "block.minecraft.cyan_stained_glass_pane": "Blågrøn glasrute", "block.minecraft.cyan_terracotta": "Blågrøn terrakotta", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Mørkeikeknapp", "block.minecraft.dark_oak_door": "<PERSON>ørk<PERSON>ked<PERSON><PERSON>", "block.minecraft.dark_oak_fence": "Gjerde av mørkeik", "block.minecraft.dark_oak_fence_gate": "Grind av mørkeik", "block.minecraft.dark_oak_hanging_sign": "Hengande mørkeikeskilt", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "Mørkeikestomn", "block.minecraft.dark_oak_planks": "Mørkeikeplankar", "block.minecraft.dark_oak_pressure_plate": "Mørkeiketrykkplate", "block.minecraft.dark_oak_sapling": "Mørkeiker<PERSON>ning", "block.minecraft.dark_oak_sign": "Mørkeikeskilt", "block.minecraft.dark_oak_slab": "Mørkeikehelle", "block.minecraft.dark_oak_stairs": "Mørkeiketrapp", "block.minecraft.dark_oak_trapdoor": "Mørkeikelem", "block.minecraft.dark_oak_wall_hanging_sign": "Hengande mørkeikeskilt på vegg", "block.minecraft.dark_oak_wall_sign": "Mørkeikeveggskilt", "block.minecraft.dark_oak_wood": "Mørkeiketre", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON><PERSON> prismarintrap<PERSON>", "block.minecraft.daylight_detector": "Dagsljosmålar", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON>allblo<PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON> h<PERSON>korallveggvifte", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> b<PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON> b<PERSON><PERSON>all<PERSON>lo<PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON> b<PERSON>e", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON> bob<PERSON>korallveggvifte", "block.minecraft.dead_bush": "<PERSON><PERSON> busk", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON>ggvifte", "block.minecraft.dead_horn_coral": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON>e", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON>ggvifte", "block.minecraft.dead_tube_coral": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON>e", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON> org<PERSON>allveggvifte", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON> potte", "block.minecraft.deepslate": "D<PERSON>ps<PERSON>fer", "block.minecraft.deepslate_brick_slab": "Helle av djupskifertegl", "block.minecraft.deepslate_brick_stairs": "Trapp av djupskifertegl", "block.minecraft.deepslate_brick_wall": "Mur av djupskifertegl", "block.minecraft.deepslate_bricks": "Djupskifertegl", "block.minecraft.deepslate_coal_ore": "Kolmalm i djupskifer", "block.minecraft.deepslate_copper_ore": "Koparmalm i djupskifer", "block.minecraft.deepslate_diamond_ore": "Diamant i djupskifer", "block.minecraft.deepslate_emerald_ore": "Smaragdmalm i djupskifer", "block.minecraft.deepslate_gold_ore": "Gullmalm i djupskifer", "block.minecraft.deepslate_iron_ore": "Jarnmalm i djupskifer", "block.minecraft.deepslate_lapis_ore": "Lasursteinmalm i djupskifer", "block.minecraft.deepslate_redstone_ore": "Redstonemalm i djupskifer", "block.minecraft.deepslate_tile_slab": "Helle av djupskiferfliser", "block.minecraft.deepslate_tile_stairs": "Trapp av djupskiferfliser", "block.minecraft.deepslate_tile_wall": "<PERSON>r av d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_tiles": "Djups<PERSON>fer<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Detektorskjene", "block.minecraft.diamond_block": "Diamantblokk", "block.minecraft.diamond_ore": "Diamantmal<PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "Di<PERSON>ttrapp", "block.minecraft.diorite_wall": "Diorittmur", "block.minecraft.dirt": "Mold", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Utskytar", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "Uttørka ghast", "block.minecraft.dried_kelp_block": "Blokk av turka tare", "block.minecraft.dripstone_block": "Dropsteinsblokk", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragdblokk", "block.minecraft.emerald_ore": "Smaragdmalm", "block.minecraft.enchanting_table": "Galdrebord", "block.minecraft.end_gateway": "Endeport", "block.minecraft.end_portal": "Endeportal", "block.minecraft.end_portal_frame": "Endeportalramme", "block.minecraft.end_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Helle av endesteinstegl", "block.minecraft.end_stone_brick_stairs": "Trapp av endesteinstegl", "block.minecraft.end_stone_brick_wall": "Mur av endesteinstegl", "block.minecraft.end_stone_bricks": "Endesteinstegl", "block.minecraft.ender_chest": "Enderkis<PERSON>", "block.minecraft.exposed_chiseled_copper": "Utsett forma kopar", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON> kopar", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON>ett koparpære", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON> k<PERSON>ardør", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.exposed_copper_trapdoor": "Utsett koparlem", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON> skoren kopar", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON><PERSON> helle av skoren kopar", "block.minecraft.exposed_cut_copper_stairs": "Utsett trapp av skoren kopar", "block.minecraft.farmland": "<PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON>d", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Eldkorallblokk", "block.minecraft.fire_coral_fan": "Eldkorallvifte", "block.minecraft.fire_coral_wall_fan": "Eldkorallveggvifte", "block.minecraft.firefly_bush": "Eldflugebusk", "block.minecraft.fletching_table": "Pilmakarbord", "block.minecraft.flower_pot": "Blomsterpotte", "block.minecraft.flowering_azalea": "Blømande lyngrose", "block.minecraft.flowering_azalea_leaves": "Blømande lyngroselauv", "block.minecraft.frogspawn": "Fr<PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON> is", "block.minecraft.furnace": "Omn", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasrute", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Gullblokk", "block.minecraft.gold_ore": "Gullmalm", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Granitthelle", "block.minecraft.granite_stairs": "Granittrapp", "block.minecraft.granite_wall": "Granittmur", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblokk", "block.minecraft.gravel": "Grus", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON> fane", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.gray_candle": "Grått voksljos", "block.minecraft.gray_candle_cake": "<PERSON>ke med grått vok<PERSON>l<PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON><PERSON> gol<PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "Gr<PERSON> glasert terrakotta", "block.minecraft.gray_shulker_box": "Grå shulkerboks", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON> glas", "block.minecraft.gray_stained_glass_pane": "Grå glasrute", "block.minecraft.gray_terracotta": "Grå terrakotta", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>l", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON> fane", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON>ke med grønt vok<PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON> glas", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakot<PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "Hengande røter", "block.minecraft.hay_block": "Høystakk", "block.minecraft.heavy_core": "<PERSON>ng kjerne", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON>ng tyn<PERSON>plate", "block.minecraft.honey_block": "Honningblokk", "block.minecraft.honeycomb_block": "Vokskakeblokk", "block.minecraft.hopper": "<PERSON><PERSON>", "block.minecraft.horn_coral": "Hornkorall", "block.minecraft.horn_coral_block": "Hornkorallblokk", "block.minecraft.horn_coral_fan": "Hornkorallvifte", "block.minecraft.horn_coral_wall_fan": "Hornkorallveggvifte", "block.minecraft.ice": "Is", "block.minecraft.infested_chiseled_stone_bricks": "Fengd forma steinmur", "block.minecraft.infested_cobblestone": "<PERSON><PERSON> brustein", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON> sprokken steinmur", "block.minecraft.infested_deepslate": "<PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON> mose<PERSON> stein<PERSON>r", "block.minecraft.infested_stone": "Fengd stein", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON> stein<PERSON>", "block.minecraft.iron_bars": "Gitter", "block.minecraft.iron_block": "Jarnblokk", "block.minecraft.iron_door": "Jarndør", "block.minecraft.iron_ore": "Jarnmalm", "block.minecraft.iron_trapdoor": "Jarnlem", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jigsaw": "Pusleblokk", "block.minecraft.jukebox": "Platespelar", "block.minecraft.jungle_button": "Jungeltreknapp", "block.minecraft.jungle_door": "Jungeltredør", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON> av jung<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Grind av jungel<PERSON>", "block.minecraft.jungle_hanging_sign": "Hengande jungeltreskilt", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Jungeltrestomn", "block.minecraft.jungle_planks": "Jungeltreplankar", "block.minecraft.jungle_pressure_plate": "Jungeltretrykkplate", "block.minecraft.jungle_sapling": "Jungeltrerenning", "block.minecraft.jungle_sign": "Jungeltreskilt", "block.minecraft.jungle_slab": "Jungeltrehell<PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "Jungeltrelem", "block.minecraft.jungle_wall_hanging_sign": "Hengande jungeltreskilt på vegg", "block.minecraft.jungle_wall_sign": "Jungeltreveggskilt", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp": "<PERSON><PERSON>", "block.minecraft.kelp_plant": "Tarevokster", "block.minecraft.ladder": "Stige", "block.minecraft.lantern": "Lykt", "block.minecraft.lapis_block": "Blokk av lasurstein", "block.minecraft.lapis_ore": "<PERSON>m av <PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Stor ametystblokk", "block.minecraft.large_fern": "<PERSON><PERSON> burkne", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavagryte", "block.minecraft.leaf_litter": "Lauvstrøy", "block.minecraft.lectern": "Talarstol", "block.minecraft.lever": "Spak", "block.minecraft.light": "Ljos", "block.minecraft.light_blue_banner": "Ljosblå fane", "block.minecraft.light_blue_bed": "Ljosblå seng", "block.minecraft.light_blue_candle": "Ljosblått voksljos", "block.minecraft.light_blue_candle_cake": "Kake med ljosblått voksljos", "block.minecraft.light_blue_carpet": "Ljosblått golvteppe", "block.minecraft.light_blue_concrete": "Ljosblå betong", "block.minecraft.light_blue_concrete_powder": "Ljosblått betongpulver", "block.minecraft.light_blue_glazed_terracotta": "Ljosblå glasert terrakotta", "block.minecraft.light_blue_shulker_box": "Ljosblå shulkerboks", "block.minecraft.light_blue_stained_glass": "Ljosblått glas", "block.minecraft.light_blue_stained_glass_pane": "Ljosblå glasrute", "block.minecraft.light_blue_terracotta": "Ljosblå terrakotta", "block.minecraft.light_blue_wool": "Ljosblå ull", "block.minecraft.light_gray_banner": "Ljosgrå fane", "block.minecraft.light_gray_bed": "Ljosgrå seng", "block.minecraft.light_gray_candle": "Ljosgrått voksljos", "block.minecraft.light_gray_candle_cake": "Kake med ljosgrått voksljos", "block.minecraft.light_gray_carpet": "Ljosgr<PERSON>tt golvteppe", "block.minecraft.light_gray_concrete": "Ljosgrå betong", "block.minecraft.light_gray_concrete_powder": "Ljosgr<PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Ljosgrå glasert terrakotta", "block.minecraft.light_gray_shulker_box": "Ljosgrå shulkerboks", "block.minecraft.light_gray_stained_glass": "Ljosgrått glas", "block.minecraft.light_gray_stained_glass_pane": "Ljosgrå glasrute", "block.minecraft.light_gray_terracotta": "Ljosgrå terrakotta", "block.minecraft.light_gray_wool": "Ljosgrå ull", "block.minecraft.light_weighted_pressure_plate": "Lett tyngdet<PERSON>", "block.minecraft.lightning_rod": "Lynavleiar", "block.minecraft.lilac": "Syrin", "block.minecraft.lily_of_the_valley": "Rams", "block.minecraft.lily_pad": "Liljeblad", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> fane", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>l<PERSON>", "block.minecraft.lime_candle_cake": "<PERSON>ke med <PERSON>ønt vok<PERSON>l<PERSON>", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> gol<PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.lime_shulker_box": "Limegrøn shulkerboks", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> glas", "block.minecraft.lime_stained_glass_pane": "Limegrø<PERSON> g<PERSON>", "block.minecraft.lime_terracotta": "Limegrøn terrakotta", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Vevstol", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON> fane", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.magenta_candle": "Ljoslilla v<PERSON>ljos", "block.minecraft.magenta_candle_cake": "<PERSON>ke med ljoslilla vok<PERSON>ljos", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON> golv<PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shulker<PERSON>", "block.minecraft.magenta_stained_glass": "L<PERSON><PERSON><PERSON> glas", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> glas<PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.magma_block": "Magmablokk", "block.minecraft.mangrove_button": "Mangroveknapp", "block.minecraft.mangrove_door": "Mangrovedør", "block.minecraft.mangrove_fence": "Mangrovegjerde", "block.minecraft.mangrove_fence_gate": "Mangrovegrind", "block.minecraft.mangrove_hanging_sign": "Hengande mangroveskilt", "block.minecraft.mangrove_leaves": "Mangrovelauv", "block.minecraft.mangrove_log": "Mangrovestomn", "block.minecraft.mangrove_planks": "Mangroveplankar", "block.minecraft.mangrove_pressure_plate": "Mangrovetrykkplate", "block.minecraft.mangrove_propagule": "Mangrovefrø", "block.minecraft.mangrove_roots": "Man<PERSON>r<PERSON><PERSON>", "block.minecraft.mangrove_sign": "Mangroveskilt", "block.minecraft.mangrove_slab": "Mangrovehelle", "block.minecraft.mangrove_stairs": "Mangrovetrapp", "block.minecraft.mangrove_trapdoor": "Mangrovelem", "block.minecraft.mangrove_wall_hanging_sign": "Hengande mangroveskilt på vegg", "block.minecraft.mangrove_wall_sign": "Mangroveveggskilt", "block.minecraft.mangrove_wood": "Mangrovetre", "block.minecraft.medium_amethyst_bud": "Mellomstor ametystblokk", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "Melonstil<PERSON>", "block.minecraft.moss_block": "Moseblokk", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON>dd brustein", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON> brustein<PERSON>", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON> brustein<PERSON>pp", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON> brusteinsmur", "block.minecraft.mossy_stone_brick_slab": "Mosegrodd helle av steintegl", "block.minecraft.mossy_stone_brick_stairs": "Mosegrodd trapp av steintegl", "block.minecraft.mossy_stone_brick_wall": "Mosegrodd mur av steintegl", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON> stempel", "block.minecraft.mud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Helle av gjørmetegl", "block.minecraft.mud_brick_stairs": "Trapp av gjørmetegl", "block.minecraft.mud_brick_wall": "<PERSON>r av gjø<PERSON>etegl", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON>etegl", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> mangroverøter", "block.minecraft.mushroom_stem": "Soppstilk", "block.minecraft.mycelium": "Mycel", "block.minecraft.nether_brick_fence": "Gjerde av nethertegl", "block.minecraft.nether_brick_slab": "Helle av nethertegl", "block.minecraft.nether_brick_stairs": "Trapp av nethertegl", "block.minecraft.nether_brick_wall": "Mur av nethertegl", "block.minecraft.nether_bricks": "Nethertegl", "block.minecraft.nether_gold_ore": "Nethergullmalm", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "Malm av nether-kvarts", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "Nethervorte", "block.minecraft.nether_wart_block": "Vorteblokk", "block.minecraft.netherite_block": "Netherittblokk", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Noteblokk", "block.minecraft.oak_button": "<PERSON><PERSON>knap<PERSON>", "block.minecraft.oak_door": "<PERSON><PERSON>d<PERSON><PERSON>", "block.minecraft.oak_fence": "G<PERSON><PERSON> av eik", "block.minecraft.oak_fence_gate": "Grind av eik", "block.minecraft.oak_hanging_sign": "Hengande eikeskilt", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Eikestomn", "block.minecraft.oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Eiketrykkplate", "block.minecraft.oak_sapling": "Eikerenning", "block.minecraft.oak_sign": "Eikeskilt", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Eikelem", "block.minecraft.oak_wall_hanging_sign": "Hengande eikeskilt på vegg", "block.minecraft.oak_wall_sign": "Eikeveggskilt", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON><PERSON> fane", "block.minecraft.open_eyeblossom": "Open augblom", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON> fane", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.orange_candle": "Oransje voksljos", "block.minecraft.orange_candle_cake": "Kake med oransje vok<PERSON>ljos", "block.minecraft.orange_carpet": "Orans<PERSON> go<PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Oransje glasert terrakotta", "block.minecraft.orange_shulker_box": "Oransje shulkerboks", "block.minecraft.orange_stained_glass": "Oransje glas", "block.minecraft.orange_stained_glass_pane": "Oransje glasrute", "block.minecraft.orange_terracotta": "Branngul terrakotta", "block.minecraft.orange_tulip": "Oransje tulipan", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Prestekrage", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON> forma kopar", "block.minecraft.oxidized_copper": "<PERSON>ira koparblokk", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON> k<PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON> k<PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON>", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON> skoren kopar", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON> helle av skoren kopar", "block.minecraft.oxidized_cut_copper_stairs": "Eira trapp av skoren kopar", "block.minecraft.packed_ice": "<PERSON><PERSON>", "block.minecraft.packed_mud": "Pak<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "Hengande bleikmose", "block.minecraft.pale_moss_block": "Bleikmoseblokk", "block.minecraft.pale_moss_carpet": "Bleikmoseteppe", "block.minecraft.pale_oak_button": "Bleikeikeknapp", "block.minecraft.pale_oak_door": "Bleikeikedør", "block.minecraft.pale_oak_fence": "Bleikeikegard", "block.minecraft.pale_oak_fence_gate": "Bleikeikegrind", "block.minecraft.pale_oak_hanging_sign": "Hengande bleikeikeskilt", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON>lauv", "block.minecraft.pale_oak_log": "Bleikeikestomn", "block.minecraft.pale_oak_planks": "Bleikeikeplankar", "block.minecraft.pale_oak_pressure_plate": "Bleikeiketrykkplate", "block.minecraft.pale_oak_sapling": "Bleikeikerenning", "block.minecraft.pale_oak_sign": "Bleikeikeskilt", "block.minecraft.pale_oak_slab": "Bleikeikehelle", "block.minecraft.pale_oak_stairs": "Bleikeiketrapp", "block.minecraft.pale_oak_trapdoor": "Bleikeikelem", "block.minecraft.pale_oak_wall_hanging_sign": "Hengande bleikeikeskilt på vegg", "block.minecraft.pale_oak_wall_sign": "Bleikeikeveggskilt", "block.minecraft.pale_oak_wood": "Bleikeiketre", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.peony": "Pion", "block.minecraft.petrified_oak_slab": "Forsteina e<PERSON>e", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "Piglinvegg<PERSON>ud", "block.minecraft.pink_banner": "<PERSON> fane", "block.minecraft.pink_bed": "<PERSON> seng", "block.minecraft.pink_candle": "<PERSON> v<PERSON>", "block.minecraft.pink_candle_cake": "<PERSON>ke med rosa voksl<PERSON>", "block.minecraft.pink_carpet": "<PERSON> golv<PERSON>", "block.minecraft.pink_concrete": "<PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON> glasert terrakotta", "block.minecraft.pink_petals": "Ljosraude kronblad", "block.minecraft.pink_shulker_box": "<PERSON>", "block.minecraft.pink_stained_glass": "<PERSON> glas", "block.minecraft.pink_stained_glass_pane": "<PERSON> g<PERSON>", "block.minecraft.pink_terracotta": "<PERSON> terrakot<PERSON>", "block.minecraft.pink_tulip": "<PERSON> tulipan", "block.minecraft.pink_wool": "<PERSON>l", "block.minecraft.piston": "Stempel", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Kruk<PERSON><PERSON>ling", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Hovudet til %s", "block.minecraft.player_wall_head": "Spelar<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "Helle av finpus<PERSON> and<PERSON>tt", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_basalt": "Finpussa basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Helle av finpussa svartsteinstegl", "block.minecraft.polished_blackstone_brick_stairs": "Trapp av finpussa s<PERSON>teinstegl", "block.minecraft.polished_blackstone_brick_wall": "<PERSON>r av fin<PERSON><PERSON> s<PERSON>gl", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_button": "Knapp av fin<PERSON><PERSON> s<PERSON>tein", "block.minecraft.polished_blackstone_pressure_plate": "Trykkplate av finpussa svartstein", "block.minecraft.polished_blackstone_slab": "Helle av fin<PERSON><PERSON> s<PERSON>tein", "block.minecraft.polished_blackstone_stairs": "Trapp av fin<PERSON><PERSON> s<PERSON>tein", "block.minecraft.polished_blackstone_wall": "<PERSON>r av <PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Helle av finpussa d<PERSON>", "block.minecraft.polished_deepslate_stairs": "Trapp av fin<PERSON><PERSON> d<PERSON>", "block.minecraft.polished_deepslate_wall": "<PERSON>r av fin<PERSON><PERSON>", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON> grani<PERSON>", "block.minecraft.polished_granite_slab": "<PERSON><PERSON><PERSON> grani<PERSON>", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON> tuff", "block.minecraft.polished_tuff_slab": "Helle av finpussa tuff", "block.minecraft.polished_tuff_stairs": "Trapp av finpus<PERSON> tuff", "block.minecraft.polished_tuff_wall": "Mur av fin<PERSON><PERSON> tuff", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Poteter", "block.minecraft.potted_acacia_sapling": "Akasierenning i potte", "block.minecraft.potted_allium": "Villauk i potte", "block.minecraft.potted_azalea_bush": "Lyngrose i potte", "block.minecraft.potted_azure_bluet": "Houstonia i potte", "block.minecraft.potted_bamboo": "Bambus i potte", "block.minecraft.potted_birch_sapling": "Bjørkerenning i potte", "block.minecraft.potted_blue_orchid": "Blå orkidé i potte", "block.minecraft.potted_brown_mushroom": "<PERSON>run sopp i potte", "block.minecraft.potted_cactus": "<PERSON>kt<PERSON> i potte", "block.minecraft.potted_cherry_sapling": "Kissebærrenning i potte", "block.minecraft.potted_closed_eyeblossom": "Attlaten augblom i potte", "block.minecraft.potted_cornflower": "Kornblom i potte", "block.minecraft.potted_crimson_fungus": "Blodsopp i potte", "block.minecraft.potted_crimson_roots": "Blodrøter i potte", "block.minecraft.potted_dandelion": "Løvetann i potte", "block.minecraft.potted_dark_oak_sapling": "Mørkeikerenning i potte", "block.minecraft.potted_dead_bush": "<PERSON>ud busk i potte", "block.minecraft.potted_fern": "Burkne i potte", "block.minecraft.potted_flowering_azalea_bush": "Blømande lyngrose i potte", "block.minecraft.potted_jungle_sapling": "Jungeltrerenning i potte", "block.minecraft.potted_lily_of_the_valley": "Rams i potte", "block.minecraft.potted_mangrove_propagule": "Mangrovefrø i potte", "block.minecraft.potted_oak_sapling": "Eikerenning i potte", "block.minecraft.potted_open_eyeblossom": "Open augblom i potte", "block.minecraft.potted_orange_tulip": "Oransje tulipan i potte", "block.minecraft.potted_oxeye_daisy": "Prestekrage i potte", "block.minecraft.potted_pale_oak_sapling": "Bleikeikerenning i potte", "block.minecraft.potted_pink_tulip": "Rosa tulipan i potte", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON> i potte", "block.minecraft.potted_red_mushroom": "Flugesopp i potte", "block.minecraft.potted_red_tulip": "<PERSON><PERSON> tulipan i potte", "block.minecraft.potted_spruce_sapling": "Granrenning i potte", "block.minecraft.potted_torchflower": "Kyndelblom i potte", "block.minecraft.potted_warped_fungus": "Vridesopp i potte", "block.minecraft.potted_warped_roots": "Vriderøter i potte", "block.minecraft.potted_white_tulip": "Kvit tulipan i potte", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON> i potte", "block.minecraft.powder_snow": "Nysnø", "block.minecraft.powder_snow_cauldron": "Gryte med nysnø", "block.minecraft.powered_rail": "Straumskjene", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarin<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_bricks": "Prismarintegl", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "Prismarintrapp", "block.minecraft.prismarine_wall": "Prismarinmur", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Graskarstilk", "block.minecraft.purple_banner": "<PERSON><PERSON> fane", "block.minecraft.purple_bed": "<PERSON><PERSON> seng", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "Kake med lilla vok<PERSON>ljos", "block.minecraft.purple_carpet": "<PERSON><PERSON> gol<PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> glasert terrakotta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON> g<PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON> terrakotta", "block.minecraft.purple_wool": "<PERSON><PERSON> ull", "block.minecraft.purpur_block": "Purpurblokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.purpur_slab": "Purpurhell<PERSON>", "block.minecraft.purpur_stairs": "P<PERSON>pur<PERSON><PERSON>", "block.minecraft.quartz_block": "Blokk av kvarts", "block.minecraft.quartz_bricks": "Kvartstegl", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON>søyle", "block.minecraft.quartz_slab": "Helle av kvarts", "block.minecraft.quartz_stairs": "Kvartstrapp", "block.minecraft.rail": "Skjene", "block.minecraft.raw_copper_block": "Blokk av råkopar", "block.minecraft.raw_gold_block": "Blokk av rågull", "block.minecraft.raw_iron_block": "Blokk av råjarn", "block.minecraft.red_banner": "<PERSON><PERSON>e", "block.minecraft.red_bed": "<PERSON><PERSON> seng", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.red_candle_cake": "<PERSON>ke med raudt vok<PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> golv<PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON> glase<PERSON> terra<PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "Flugesoppblokk", "block.minecraft.red_nether_brick_slab": "Helle av raud nethertegl", "block.minecraft.red_nether_brick_stairs": "Trapp av raud nethertegl", "block.minecraft.red_nether_brick_wall": "<PERSON>r av raudt nethertegl", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "Raud sand", "block.minecraft.red_sandstone": "<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON>", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON> tulipan", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Blokk av redstone", "block.minecraft.redstone_lamp": "Redstone-lampe", "block.minecraft.redstone_ore": "Redstone-malm", "block.minecraft.redstone_torch": "Redstone-fakkel", "block.minecraft.redstone_wall_torch": "Redstone-veggfakkel", "block.minecraft.redstone_wire": "Redstoneleidning", "block.minecraft.reinforced_deepslate": "Styrkt djupskifer", "block.minecraft.repeater": "Redstone-aukar", "block.minecraft.repeating_command_block": "Gjentakande kommandoblokk", "block.minecraft.resin_block": "Kvaeblokk", "block.minecraft.resin_brick_slab": "Helle av kvaetegl", "block.minecraft.resin_brick_stairs": "Trapp av kvaetegl", "block.minecraft.resin_brick_wall": "Mur av kvaetegl", "block.minecraft.resin_bricks": "Kvaetegl", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Oppstodeanker", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "Rosebusk", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Sandsteinhelle", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_wall": "Sandsteinmur", "block.minecraft.scaffolding": "Stelling", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculkeskundar", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Sculkeskrikar", "block.minecraft.sculk_vein": "Sculkeår", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "Sjøagurk", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "<PERSON><PERSON>", "block.minecraft.short_dry_grass": "Stutt tørt gras", "block.minecraft.short_grass": "Stutt gras", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Shulkerboks", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_wall_skull": "Veggskalle", "block.minecraft.slime_block": "Slimblokk", "block.minecraft.small_amethyst_bud": "Lita ametystblokk", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "Smiebord", "block.minecraft.smoker": "Røykjaromn", "block.minecraft.smooth_basalt": "Glatt basalt", "block.minecraft.smooth_quartz": "Glatt kvartsblokk", "block.minecraft.smooth_quartz_slab": "Helle av glatt kvarts", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON> k<PERSON>tstrapp", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> raud <PERSON>stein", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON> r<PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON> r<PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "Helle av glatt sandstein", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON> stein", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON> stein<PERSON>e", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "Snø", "block.minecraft.snow_block": "Snøblokk", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "Sjelesand", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "Sjeleveggfakkel", "block.minecraft.spawn.not_valid": "Du har inga seng eller anker for oppsode, eller so vart ho/det stengd/t", "block.minecraft.spawner": "Skapningsframkallar", "block.minecraft.spawner.desc1": "<PERSON><PERSON><PERSON> f<PERSON>:", "block.minecraft.spawner.desc2": "Set s<PERSON>ningsslaget", "block.minecraft.sponge": "Svamp", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_button": "Granknapp", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence": "Gjerde av gran", "block.minecraft.spruce_fence_gate": "Grind av gran", "block.minecraft.spruce_hanging_sign": "Hengande granskilt", "block.minecraft.spruce_leaves": "Granbar", "block.minecraft.spruce_log": "Granstomn", "block.minecraft.spruce_planks": "Granp<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Grantrykkplate", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "Granskilt", "block.minecraft.spruce_slab": "Granhelle", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "Granlem", "block.minecraft.spruce_wall_hanging_sign": "Hengande granskilt på vegg", "block.minecraft.spruce_wall_sign": "Granveggskilt", "block.minecraft.spruce_wood": "<PERSON><PERSON>", "block.minecraft.sticky_piston": "Klisterstempel", "block.minecraft.stone": "<PERSON>", "block.minecraft.stone_brick_slab": "Stein<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_wall": "Mur av steintegl", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "Steintrykkplate", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Steinskjerar", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON>a mangrove<PERSON>n", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON>a mangrove<PERSON>", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON> gran<PERSON>", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.structure_block": "Bygnadsblokk", "block.minecraft.structure_void": "Bygnadstomrom", "block.minecraft.sugar_cane": "Sukkerrøyr", "block.minecraft.sunflower": "Solvendel", "block.minecraft.suspicious_gravel": "Mistenkjeleg grus", "block.minecraft.suspicious_sand": "Mistenkjeleg sand", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON> tørt gras", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON> gras", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.target": "Skotskive", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Testblokk", "block.minecraft.test_instance_block": "Testinstansblokk", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-eksplosjonar er avslege", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.trapped_chest": "Lokkekiste", "block.minecraft.trial_spawner": "Røyneframkallar", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Snubletrådfeste", "block.minecraft.tube_coral": "Orgelkorall", "block.minecraft.tube_coral_block": "Orgelkorallblokk", "block.minecraft.tube_coral_fan": "Orgelkorallvifte", "block.minecraft.tube_coral_wall_fan": "Orgelkorallveggvifte", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Helle av tufftegl", "block.minecraft.tuff_brick_stairs": "Trapp av tufftegl", "block.minecraft.tuff_brick_wall": "Tuffteglm<PERSON>", "block.minecraft.tuff_bricks": "Tufftegl", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "Tuffmur", "block.minecraft.turtle_egg": "Skjelpaddeegg", "block.minecraft.twisting_vines": "Krokande klivevokster", "block.minecraft.twisting_vines_plant": "Krokande klivevokster", "block.minecraft.vault": "Kvelv", "block.minecraft.verdant_froglight": "<PERSON>r<PERSON>ns<PERSON><PERSON> f<PERSON>", "block.minecraft.vine": "Klivevokster", "block.minecraft.void_air": "Tomromsluft", "block.minecraft.wall_torch": "Vegg<PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "Vrideknapp", "block.minecraft.warped_door": "Vridedør", "block.minecraft.warped_fence": "<PERSON>rideg<PERSON><PERSON>", "block.minecraft.warped_fence_gate": "Vridegrind", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "Hengande vrideskilt", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "Vridenycel", "block.minecraft.warped_planks": "Vrideplankar", "block.minecraft.warped_pressure_plate": "Vridetrykkplate", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "Vrideskilt", "block.minecraft.warped_slab": "Vridehell<PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "Vridestomn", "block.minecraft.warped_trapdoor": "Vridelem", "block.minecraft.warped_wall_hanging_sign": "Hengande vrideskilt på vegg", "block.minecraft.warped_wall_sign": "Vrideveggskilt", "block.minecraft.warped_wart_block": "Vridevorteblokk", "block.minecraft.water": "Vatn", "block.minecraft.water_cauldron": "Vassgryte", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON> forma kopar", "block.minecraft.waxed_copper_block": "Voksa koparblokk", "block.minecraft.waxed_copper_bulb": "<PERSON>oksa k<PERSON>", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON> k<PERSON>ø<PERSON>", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_trapdoor": "Voksa koparlem", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON>sa skoren kopar", "block.minecraft.waxed_cut_copper_slab": "Voksa helle av skoren kopar", "block.minecraft.waxed_cut_copper_stairs": "Voksa trapp av skoren kopar", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON> utsett forma kopar", "block.minecraft.waxed_exposed_copper": "Voksa utsett kopar", "block.minecraft.waxed_exposed_copper_bulb": "Voksa utsett koparpære", "block.minecraft.waxed_exposed_copper_door": "Voksa utsett kopardør", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON> uts<PERSON> kop<PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "Voksa utsett koparlem", "block.minecraft.waxed_exposed_cut_copper": "Voksa utsett skoren kopar", "block.minecraft.waxed_exposed_cut_copper_slab": "Voksa utsett helle av skoren kopar", "block.minecraft.waxed_exposed_cut_copper_stairs": "Voksa utsett trapp av skoren kopar", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON> eira forma kopar", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON> e<PERSON> kopar", "block.minecraft.waxed_oxidized_copper_bulb": "Voksa eira koparpære", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON> e<PERSON> kopardør", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> e<PERSON> k<PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Voksa eira koparlem", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON>sa eira skoren kopar", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON>oksa eira helle av skoren kopar", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Voksa eira trapp av skoren kopar", "block.minecraft.waxed_weathered_chiseled_copper": "Voksa vêrbiten forma kopar", "block.minecraft.waxed_weathered_copper": "Voksa vêrbiten kopar", "block.minecraft.waxed_weathered_copper_bulb": "Voksa vêrbita koparpære", "block.minecraft.waxed_weathered_copper_door": "Voksa vêrbita kopardør", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON> v<PERSON> k<PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "Voksa vêrbiten koparlem", "block.minecraft.waxed_weathered_cut_copper": "Voksa vêrbiten skoren kopar", "block.minecraft.waxed_weathered_cut_copper_slab": "Voksa vêrbita helle av skoren kopar", "block.minecraft.waxed_weathered_cut_copper_stairs": "Voksa vêrbita trapp av skoren kopar", "block.minecraft.weathered_chiseled_copper": "Vêrbiten forma kopar", "block.minecraft.weathered_copper": "Vêrbiten kopar", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>arpæ<PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_trapdoor": "Vêrbiten koparlem", "block.minecraft.weathered_cut_copper": "Vêrbiten skoren kopar", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> helle av skoren kopar", "block.minecraft.weathered_cut_copper_stairs": "Vêrbita trapp av skoren kopar", "block.minecraft.weeping_vines": "Gråtande klivevokster", "block.minecraft.weeping_vines_plant": "Gråtande klivevokster", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON> svamp", "block.minecraft.wheat": "Kveiteavlingar", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON> <PERSON>e", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.white_candle_cake": "<PERSON>ke med kvitt vok<PERSON>ljos", "block.minecraft.white_carpet": "Kvit rye", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON>hul<PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON>las", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> tulipan", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Skalle av witherbeinrangel", "block.minecraft.wither_skeleton_wall_skull": "Veggskalle frå witherbeinrangel", "block.minecraft.yellow_banner": "<PERSON><PERSON> fane", "block.minecraft.yellow_bed": "<PERSON><PERSON> seng", "block.minecraft.yellow_candle": "<PERSON><PERSON> v<PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON>ke med gult voksljos", "block.minecraft.yellow_carpet": "<PERSON><PERSON> golv<PERSON>pe", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Gul glasert terrakotta", "block.minecraft.yellow_shulker_box": "Gul shulkerboks", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.yellow_stained_glass_pane": "Gul glasrute", "block.minecraft.yellow_terracotta": "Gul terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON> ull", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "av %1$s", "book.edit.title": "Bokredigeringsskjerm", "book.editTitle": "Skriv inn tittelen på boka:", "book.finalizeButton": "Skriv under og lat att", "book.finalizeWarning": "Hugs! Skriv du under i boka, kan du ikkje lenger gjera om henne.", "book.generation.0": "Opphavleg", "book.generation.1": "Avrit av opphavleg", "book.generation.2": "Avrit av avrit", "book.generation.3": "Fillut", "book.invalid.tag": "* <PERSON><PERSON><PERSON> boktagg *", "book.pageIndicator": "Side %1$s av %2$s", "book.page_button.next": "Neste side", "book.page_button.previous": "Førre side", "book.sign.title": "Boksigneringsskjerm", "book.sign.titlebox": "<PERSON><PERSON><PERSON>", "book.signButton": "Skriv under", "book.view.title": "Bokvisningsskjerm", "build.tooHigh": "Høgdegrensa for byggjing er %s", "chat.cannotSend": "Kan ikkje senda melding", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klikk for å teleportera", "chat.copy": "Legg i utklippstavla", "chat.copy.click": "Klikk for å ta avrit", "chat.deleted_marker": "<PERSON>ne meldinga er sletta av tenaren.", "chat.disabled.chain_broken": "Nettprat avslege på grunn av ei øydelagd kjede. Freista å kopla på att.", "chat.disabled.expiredProfileKey": "Nettprat avslege av di den offentlege lykelen til profilen er utgått. Freista å kopla på att.", "chat.disabled.invalid_command_signature": "Kommandoen hadde uforventa eller manglande kommandoargument-signaturar.", "chat.disabled.invalid_signature": "Nettpraten hadde en ugyldig signatur. Vennligst prøv å koble til på nytt.", "chat.disabled.launcher": "Nettprat er avslege av oppstartarval. Kan ikkje senda melding.", "chat.disabled.missingProfileKey": "Nettpraten er avslegen på grunn av vantande offentleg lykel til profil. Freista å kopla på att.", "chat.disabled.options": "Nettprat er avslege i klientval.", "chat.disabled.out_of_order_chat": "Nettprat mottat i feil rekkefølge. Har systemtiden din endret seg?", "chat.disabled.profile": "Nettprat er sperra av kontovala. Klikk «%s» att for fleire opplysingar.", "chat.disabled.profile.moreInfo": "Nettprat er sperra av kontovala. Kan ikkje senda eller sjå meldingar.", "chat.editBox": "nett<PERSON>t", "chat.filtered": "Filtrert av tenaren.", "chat.filtered_full": "<PERSON><PERSON><PERSON> har gø<PERSON>t meldinga di for visse spelarar.", "chat.link.confirm": "<PERSON>r du trygg på at du vil opna fylgjande nettside?", "chat.link.confirmTrusted": "Ynskjer du å opna eller ta avrit av denne lenkja?", "chat.link.open": "Opna i nettlesaren", "chat.link.warning": "Opna aldri nettlenkjer frå folk du ikkje lit på!", "chat.queue": "[+%s <PERSON>ande rader]", "chat.square_brackets": "[%s]", "chat.tag.error": "Tenar sende ugild melding.", "chat.tag.modified": "Melding omgjord av tenaren: Opphavleg:", "chat.tag.not_secure": "Uverifisert melding. Kunne ikkje verta rapportert.", "chat.tag.system": "Tenarmelding. Kunne ikkje verta rapportert.", "chat.tag.system_single_player": "Tenarmelding.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s har fullført utfordringa %s", "chat.type.advancement.goal": "%s har n<PERSON>tt målet %s", "chat.type.advancement.task": "%s har gjort bragda %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Send melding til laget", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s seier %s", "chat.validation_error": "<PERSON>nne ikkje validera nettprat", "chat_screen.message": "Melding til sending: %s", "chat_screen.title": "Nettpratsskjerm", "chat_screen.usage": "Skriv melding og trykk Enter for å senda", "chunk.toast.checkLog": "Se loggen for flere detaljer", "chunk.toast.loadFailure": "Kunne ikke laste inn stykke ved %s", "chunk.toast.lowDiskSpace": "Lite diskplass!", "chunk.toast.lowDiskSpace.description": "Kan henda verda di ikkje vinn lagrast.", "chunk.toast.saveFailure": "Kunne ikke lagre stykke ved %s", "clear.failed.multiple": "Ingen gjenstandar var funne hjå %s spelarar", "clear.failed.single": "Ingen gjenstandar var funne hjå spelaren %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blå", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "Grå", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Ljosblå", "color.minecraft.light_gray": "Ljosgrå", "color.minecraft.lime": "Limegrøn", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "Oransje", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Gul", "command.context.here": "<--[HER]", "command.context.parse_error": "%s på stad %s: %s", "command.exception": "Kunne ikkje tolka kommando: %s", "command.expected.separator": "Mellomromsteikn var venta for å enda argumentet, men vidare data vart funne", "command.failed": "Eit uventa mistak hende medan du freista å utføra kommandoen", "command.forkLimit": "Høgste tal på samanhengen (%s) nått", "command.unknown.argument": "Urettug argument for kommando", "command.unknown.command": "<PERSON><PERSON><PERSON><PERSON> eller uheilsleg kommando. Sjå under for feil", "commands.advancement.criterionNotFound": "Bragda %1$s inneheld ikkje kravet '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Kunne ikkje gje kravet «%s» for bragda %s til %s spelarar sidan dei alt har henne", "commands.advancement.grant.criterion.to.many.success": "Innvilga kravet «%s» for bragda %s til %s spelarar", "commands.advancement.grant.criterion.to.one.failure": "<PERSON>nne ikkje gje kravet «%s» for bragda %s til %s, som alt har henne", "commands.advancement.grant.criterion.to.one.success": "Innvilga kravet «%s» for bragda %s til %s", "commands.advancement.grant.many.to.many.failure": "Kunne ikkje gje %s bragder til %s spelarar sidan dei alt har dei", "commands.advancement.grant.many.to.many.success": "Innvilga %s bragder til %s spelarar", "commands.advancement.grant.many.to.one.failure": "Kunne ikkje gje %s bragder til %s, som alt har dei", "commands.advancement.grant.many.to.one.success": "Innvilga %s bragder til %s", "commands.advancement.grant.one.to.many.failure": "Kunne ikkje gje bragda %s til %s spelarar sidan dei alt har det", "commands.advancement.grant.one.to.many.success": "Innvilga bragda %s til %s spelarar", "commands.advancement.grant.one.to.one.failure": "Kunne ikkje gje bragda %s til %s, som alt har henne", "commands.advancement.grant.one.to.one.success": "Innvilga bragda %s til %s", "commands.advancement.revoke.criterion.to.many.failure": "Kunne ikkje inndraga kravet «%s» for bragda %s frå %s spelarar sida dei ikkje har det", "commands.advancement.revoke.criterion.to.many.success": "Inndrog kravet «%s» for bragda %s frå %s spelarar", "commands.advancement.revoke.criterion.to.one.failure": "Kunne ikkje inndraga kravet «%s» for bragda %s frå %s, som ikkje har det", "commands.advancement.revoke.criterion.to.one.success": "Inndrog kravet «%s» for bragda %s frå %s", "commands.advancement.revoke.many.to.many.failure": "Kunne ikkje inndraga %s bragder frå %s spelarar sidan dei ikkje har dei", "commands.advancement.revoke.many.to.many.success": "Inndrog %s bragder frå %s spelarar", "commands.advancement.revoke.many.to.one.failure": "Kunne ikkje inndraga %s bragder frå %s, som ikkje har dei", "commands.advancement.revoke.many.to.one.success": "Inndrog %s bragder frå %s", "commands.advancement.revoke.one.to.many.failure": "Kunne ikkje inndraga bragda %s frå %s spelarar sidan dei ikkje har henne", "commands.advancement.revoke.one.to.many.success": "Inndrog bragda %s frå %s spelarar", "commands.advancement.revoke.one.to.one.failure": "Kunne ikkje inndraga bragda %s frå %s, som ikkje har henne", "commands.advancement.revoke.one.to.one.success": "Inndrog bragda %s frå %s", "commands.attribute.base_value.get.success": "Grunnverdet for eigenskapen %s for eininga %s er %s", "commands.attribute.base_value.reset.success": "Grunnverdet for eigenskapen %s for eininga %s er sett attende til førevalet %s", "commands.attribute.base_value.set.success": "Grunnverdet til eigenskapen %s til eininga %s er %s", "commands.attribute.failed.entity": "%s er ikkje ei gild eining for denne kommandoen", "commands.attribute.failed.modifier_already_present": "Modifiseringa %s er allereie til stades i eigenskapen %s til eininga %s", "commands.attribute.failed.no_attribute": "Eininga %s har ikkje eigenskapen %s", "commands.attribute.failed.no_modifier": "Eigenskapen %s til eininga %s har ikkje modifiseringa %s", "commands.attribute.modifier.add.success": "La til modifiseringa %s i eigenskapen %s til eininga %s", "commands.attribute.modifier.remove.success": "Tok bort modifiseringa %s fra eigenskapen %s til eininga %s", "commands.attribute.modifier.value.get.success": "Verdet til modifiseringa %s i eigenskapen %s til eininga %s er %s", "commands.attribute.value.get.success": "Verdet til eigenskapen %s til eininga %s er %s", "commands.ban.failed": "Ingenting gjord om. Spelaren er alt bannlyst", "commands.ban.success": "Bannlyste %s: %s", "commands.banip.failed": "Ingenting gjord om. Denne IPen er alt bannlyst", "commands.banip.info": "<PERSON>ne bannlysinga påverkar %s spelar(ar): %s", "commands.banip.invalid": "Ugild IP-adresse eller ukjend spelar", "commands.banip.success": "Bannlyste IP-adressa %s: %s", "commands.banlist.entry": "%s vart bannlyst av %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON><PERSON>)", "commands.banlist.list": "Det er %s bannlysing(ar):", "commands.banlist.none": "Det er ingen bannlysingar", "commands.bossbar.create.failed": "Ein fiendsmålar med IDen «%s» finst alt", "commands.bossbar.create.success": "Laga eigen fiendsmålar %s", "commands.bossbar.get.max": "Eigenlaga fiendsmålar %s har ein høgste på %s", "commands.bossbar.get.players.none": "Eigenlaga fiendsmålar %s har ingen spelarar pålogga for augneblinken", "commands.bossbar.get.players.some": "Eigenlaga fiendsmålar %s har %s spelar(ar) pålogga for augneblinken: %s", "commands.bossbar.get.value": "Eigenlaga fiendsmålar %s har eit verde av %s", "commands.bossbar.get.visible.hidden": "Eigenlaga fiendsmålar %s er for augneblinken gøymd", "commands.bossbar.get.visible.visible": "Eigenlaga fiendsmålar %s er for augneblinken vist", "commands.bossbar.list.bars.none": "Det er ingen eigne fiendsmålarar slegne på", "commands.bossbar.list.bars.some": "Det er %s eigenlaga fiendsmålar(ar) slegne på: %s", "commands.bossbar.remove.success": "Tok bort fiendsmålaren %s", "commands.bossbar.set.color.success": "Eigenlaga fiendsmålar %s har skift let", "commands.bossbar.set.color.unchanged": "Ingenting gjord om. Dette er alt leten av denne fiendsmålaren", "commands.bossbar.set.max.success": "Eigenlaga fiendsmålar %s har skift høgste til %s", "commands.bossbar.set.max.unchanged": "Ingenting gjord om. Dette er alt det høgste for denne fi<PERSON>å<PERSON>en", "commands.bossbar.set.name.success": "Eigenlaga fiendsmålar %s har fått nytt namn", "commands.bossbar.set.name.unchanged": "Ingenting gjord om. Dette er alt namnet på denne fiendsmå<PERSON>en", "commands.bossbar.set.players.success.none": "Eigenlaga fiendsmålar %s har ikkje nokon spelarar lenger", "commands.bossbar.set.players.success.some": "Eigenlaga fiendsmålar %s har no %s spelar(ar): %s", "commands.bossbar.set.players.unchanged": "Ingenting gjord om. Desse spelarane er alt på fiendsmålaren med ingen å leggja til eller ta bort", "commands.bossbar.set.style.success": "Eigenlaga fiendsmålar %s har skift stil", "commands.bossbar.set.style.unchanged": "Ingenting gjord om. Dette er alt stilen på denne fi<PERSON>å<PERSON>en", "commands.bossbar.set.value.success": "Eigenlaga fiendsmålar %s har skift verde til %s", "commands.bossbar.set.value.unchanged": "Ingenting gjord om. Dette er alt verdet av denne fiendsmålaren", "commands.bossbar.set.visibility.unchanged.hidden": "Ingenting gjord om. Fiendsmålaren er alt gøymd", "commands.bossbar.set.visibility.unchanged.visible": "Inegnting gjord om. Fiendsmålaren er alt synleg", "commands.bossbar.set.visible.success.hidden": "Eigenlaga fiendsmålar %s er no gøymd", "commands.bossbar.set.visible.success.visible": "Eigenlaga fiendsmålar %s er no synlege", "commands.bossbar.unknown": "Det finst ingen fiendsmålar med IDen «%s»", "commands.clear.success.multiple": "Tok bort %s gjenstand(ar) frå %s spelar(ar)", "commands.clear.success.single": "Tok bort %s gjenstand(ar) frå spelaren %s", "commands.clear.test.multiple": "Fann %s liknande gje<PERSON>(ar) hjå %s spelar(ar)", "commands.clear.test.single": "Fann %s liknande gjenstand(ar) hjå spelaren %s", "commands.clone.failed": "Inga blokk var klona", "commands.clone.overlap": "Kjelde- og destinasjonsområda kan ikkje overlappa kvarandre", "commands.clone.success": "Klona %s blokk(er)", "commands.clone.toobig": "For mange blokker i det oppgjevne området (%s er høgst. %s er oppgjeve)", "commands.damage.invulnerable": "Slik skade har ingen verknad på målet", "commands.damage.success": "Gjorde %s skade på %s", "commands.data.block.get": "%s ved %s, %s, %s etter skaleringsfaktor %s er %s", "commands.data.block.invalid": "Målblokka er ikkje ei blokkeining", "commands.data.block.modified": "Gjorde om blokkdata til %s, %s, %s", "commands.data.block.query": "%s, %s, %s har fylgjande blokkdata: %s", "commands.data.entity.get": "%s hjå %s etter skaleringsfaktor %s er %s", "commands.data.entity.invalid": "Kunne ikkje tilmåta spelardata", "commands.data.entity.modified": "Gjorde om einingsdata hjå %s", "commands.data.entity.query": "%s har fylg<PERSON>de einingsdata: %s", "commands.data.get.invalid": "Kan ikkje henta %s. <PERSON> talmessige taggar er tillatne", "commands.data.get.multiple": "Dette argumentet godtek eit einskilt NBT-verde", "commands.data.get.unknown": "Kan ikkje henta %s. <PERSON>gen finst ikkje", "commands.data.merge.failed": "Ingenting gjord om. Dei oppgjevne eigenskapane har alt desse verda", "commands.data.modify.expected_list": "Venta liste. Fann: %s", "commands.data.modify.expected_object": "Venta objekt. Fann: %s", "commands.data.modify.expected_value": "Venta verde; fekk: %s", "commands.data.modify.invalid_index": "<PERSON><PERSON><PERSON> listeindeks: %s", "commands.data.modify.invalid_substring": "Ugilde understrengindeksar: %s til %s", "commands.data.storage.get": "%s i lagringa %s etter skaleringsfaktoren av %s er %s", "commands.data.storage.modified": "Tilmåta lagringa %s", "commands.data.storage.query": "Lagringa %s har fylgjande innehald: %s", "commands.datapack.create.already_exists": "Pakke ved navn \"%s\" finnes allerede", "commands.datapack.create.invalid_full_name": "Ugyldig nytt pakkenavn '%s'", "commands.datapack.create.invalid_name": "Ugyldig tegn i det nye pakkenavnet \"%s\"", "commands.datapack.create.io_failure": "Kan ikke opprette pakke med navnet \"%s\". Sjekk logger", "commands.datapack.create.metadata_encode_failure": "Lyktes ikke med å kode metadata for pakke med navnet \"%s\": %s", "commands.datapack.create.success": "Skapte ny tom pakke med navnet \"%s\"", "commands.datapack.disable.failed": "Pakka «%s» er ikkje påslega!", "commands.datapack.disable.failed.feature": "Pakke '%s' kan ikke slås av fordi den betinges av et påsl<PERSON><PERSON> flagg!", "commands.datapack.enable.failed": "Pakka «%s» er alt påslega!", "commands.datapack.enable.failed.no_flags": "Pakka «%s» kan ikkje verta påslega, av di dei påkravde flagga ikkje er påslegne i denne verda: %s!", "commands.datapack.list.available.none": "Det er ingen fleire datapakkar tilgjengeleg", "commands.datapack.list.available.success": "Det er %s datapakke/ar tilg<PERSON>: %s", "commands.datapack.list.enabled.none": "Det er ingen datapakkar slegne på", "commands.datapack.list.enabled.success": "Det er %s datapakke/ar påslegne: %s", "commands.datapack.modify.disable": "Slår av datapakkar %s", "commands.datapack.modify.enable": "Slår på datapakka %s", "commands.datapack.unknown": "«%s» er ein ukjend datapakke", "commands.debug.alreadyRunning": "Tikkprofileraren er alt sett i gang", "commands.debug.function.noRecursion": "Kan ikkje spora innan funksjonen", "commands.debug.function.noReturnRun": "Kan ikkje bruka sporing med /return run", "commands.debug.function.success.multiple": "Spora %s kommando(ar) frå %s funksjonar til output-fil %s", "commands.debug.function.success.single": "Spora %s kommando(ar) frå funksjon «%s» til output-fil %s", "commands.debug.function.traceFailed": "Kunne ikkje spora funksjon", "commands.debug.notRunning": "Tikkprofileraren er ikkje sett i gang", "commands.debug.started": "Sette i gong tikkprofilering", "commands.debug.stopped": "Stansa tikkprofilering etter %s sekund og %s tikk (%s tikk i sekundet)", "commands.defaultgamemode.success": "Førevald spelemodus er no %s", "commands.deop.failed": "Ingenting gjord om. Spelaren er ikkje ein operatør", "commands.deop.success": "Tok bort %s som tenaroperatør", "commands.dialog.clear.multiple": "Fjernet dialog for %s spillere", "commands.dialog.clear.single": "Fjernet dialog for %s", "commands.dialog.show.multiple": "Viste dialog til %s spillere", "commands.dialog.show.single": "Viste dialog til %s", "commands.difficulty.failure": "Vandleiken vart ikkje gjord om. Han er alt sett til %s", "commands.difficulty.query": "Vandleiken er %s", "commands.difficulty.success": "Vandleiken er sett til %s", "commands.drop.no_held_items": "Eininga kan ikkje halda gjenstandar", "commands.drop.no_loot_table": "Eininga %s har ikk<PERSON> plyndringstabell", "commands.drop.no_loot_table.block": "Blokk %s har ingen utbyttetabell", "commands.drop.success.multiple": "Sleppte %s gjenstandar", "commands.drop.success.multiple_with_table": "Sleppte %s gjenstandar frå plyndringstabellen %s", "commands.drop.success.single": "Sleppte %s %s", "commands.drop.success.single_with_table": "Sleppte %s %s frå plyndringstabellen %s", "commands.effect.clear.everything.failed": "<PERSON><PERSON><PERSON> har ingen verknader å ta bort", "commands.effect.clear.everything.success.multiple": "Tok bort alle verknadene frå %s mål", "commands.effect.clear.everything.success.single": "Tok bort alle verknadene frå %s", "commands.effect.clear.specific.failed": "Målet har ikkje dei kravde verknadene", "commands.effect.clear.specific.success.multiple": "Tok bort verknaden %s frå %s mål", "commands.effect.clear.specific.success.single": "Tok bort verknaden %s frå %s", "commands.effect.give.failed": "<PERSON><PERSON> ikkje bruka denne verknaden (målet er anten immunt mot verknader, eller har noko sterkare)", "commands.effect.give.success.multiple": "Påførde verknaden %s på %s mål", "commands.effect.give.success.single": "Påførde verknaden %s på %s", "commands.enchant.failed": "Ingenting gjord om. Anten har måla ingen gjenstandar i hendene eller trolldomen kunne ikkje gjevast", "commands.enchant.failed.entity": "%s er ikkje ei gild eining for denne kommandoen", "commands.enchant.failed.incompatible": "%s kan ikkje stø den trolldomen", "commands.enchant.failed.itemless": "%s held ikkje nokon gjenstand", "commands.enchant.failed.level": "%s er høgre enn det høgste steget av %s stødd av trolldomen", "commands.enchant.success.multiple": "Gav trolldomen %s til %s gjenstandar", "commands.enchant.success.single": "Gav trolldomen %s til %ss gjenstand", "commands.execute.blocks.toobig": "For mange blokker i det oppgjevne området (%s er høgst. %s er oppgjeve)", "commands.execute.conditional.fail": "<PERSON>en var mislukka", "commands.execute.conditional.fail_count": "Testen var mislukka. Tal: %s", "commands.execute.conditional.pass": "<PERSON>en stod", "commands.execute.conditional.pass_count": "Testen stod. Tal: %s", "commands.execute.function.instantiationFailure": "Mislykkast med å instansiere funksjon %s: %s", "commands.experience.add.levels.success.multiple": "Gav %s røynslesteg til %s spelarar", "commands.experience.add.levels.success.single": "Gav %s røynslesteg til %s", "commands.experience.add.points.success.multiple": "Gav %s galdrepoeng til %s spelarar", "commands.experience.add.points.success.single": "Gav %s galdrepoeng til %s", "commands.experience.query.levels": "%s har %s røynslesteg", "commands.experience.query.points": "%s har %s galdrepoeng", "commands.experience.set.levels.success.multiple": "Sette %s røynslesteg hjå %s spelarar", "commands.experience.set.levels.success.single": "Sette %s røynslesteg hjå %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> over hø<PERSON><PERSON> meng<PERSON> poeng for det gjeldande steget hjå spelaren", "commands.experience.set.points.success.multiple": "Sette %s galdrepoeng hjå %s spelarar", "commands.experience.set.points.success.single": "Sette %s galdre<PERSON><PERSON> hjå %s", "commands.fill.failed": "Ingen blokker vart sette ned", "commands.fill.success": "Fylling av %s blokk(er) var vellukka", "commands.fill.toobig": "For mange blokker i det oppgjevne området (%s er høgst. %s er oppgjeve)", "commands.fillbiome.success": "Gjorde om landskap mellom %s, %s, %s og %s, %s, %s", "commands.fillbiome.success.count": "Gjorde om %s landskapsoppføring(ar) mellom %s, %s, %s og %s, %s, %s", "commands.fillbiome.toobig": "For mange blokker i det gjevne romet (høgst %s, gjeve %s)", "commands.forceload.added.failure": "Ingen verdsbitar vart merkte for tvangslasting", "commands.forceload.added.multiple": "Merkte %s verdsbitar i %s frå %s til %s til å verta tvangslasta", "commands.forceload.added.none": "Ingen tvangslasta verdsbitar vart funne i %s", "commands.forceload.added.single": "Merkte verdsbit %s i %s til å verta tvangslasta", "commands.forceload.list.multiple": "%s tvangslasta verdsbitar var funne i %s ved: %s", "commands.forceload.list.single": "Ein tvangslasta verdsbit var funnen i %s ved: %s", "commands.forceload.query.failure": "Verdsbiten ved %s i %s er ikkje merkt for tvangslasting", "commands.forceload.query.success": "Verdsbiten ved %s i %s er merkt for tvangslasting", "commands.forceload.removed.all": "Avmerkte alle tvangslasta verdsbitar i %s", "commands.forceload.removed.failure": "Ingen verdsbitar vart tekne bort frå tvangslasting", "commands.forceload.removed.multiple": "Avmerkte %s verdsbitar for tvangslasting i %s frå %s til %s", "commands.forceload.removed.single": "Avmerkte verdsbiten %s i %s for tvangslasting", "commands.forceload.toobig": "For mange verdsbitar i det gjevne området (%s er høgst, %s er gjeve)", "commands.function.error.argument_not_compound": "Ugildt argumentslag: %s; venta samansetjing", "commands.function.error.missing_argument": "Vantar argument %2$s til funksjonen %1$s", "commands.function.error.missing_arguments": "Vantar argument til funksjon %s", "commands.function.error.parse": "Under instansiering av makro %s: forårsaka kommandoen '%s' feil: %s", "commands.function.instantiationFailure": "Mislykka instansiering av funksjon %s: %s", "commands.function.result": "Funksjon %s returnerte %s", "commands.function.scheduled.multiple": "Køyrande funksjonar %s", "commands.function.scheduled.no_functions": "Kan ikkje finna nokon funksjonar for namn %s", "commands.function.scheduled.single": "Køyrer funksjon %s", "commands.function.success.multiple": "Utførde %s kommando(ar) frå %s funksjonar", "commands.function.success.multiple.result": "Utførde %s funksjonar", "commands.function.success.single": "Utførde %s kommando(ar) frå funksjon \"%s\"", "commands.function.success.single.result": "Funksjonen «%2$s» gav %1$s attende", "commands.gamemode.success.other": "%s sin spelmodus er sett til %s", "commands.gamemode.success.self": "Spelmodusen din er sett til %s", "commands.gamerule.query": "Speleregelen %s er for tida sett til: %s", "commands.gamerule.set": "Speleregelen %s er no sett til: %s", "commands.give.failed.toomanyitems": "Kan ikkje gjeva meir enn %s av %s", "commands.give.success.multiple": "Gav %s %s til %s spelarar", "commands.give.success.single": "Gav %s %s til %s", "commands.help.failed": "Ukjend kommando eller mangelfulle løyve", "commands.item.block.set.success": "Bytte ut ein bås hjå %s, %s, %s med %s", "commands.item.entity.set.success.multiple": "Bytte ut ein bås hjå %s einingar med %s", "commands.item.entity.set.success.single": "Bytte ut ein b<PERSON>s hjå %s med %s", "commands.item.source.no_such_slot": "<PERSON><PERSON>lda har ikk<PERSON> b<PERSON>sen %s", "commands.item.source.not_a_container": "Kjeldestad %s, %s, %s er ikkje eit ilåt", "commands.item.target.no_changed.known_item": "Ingen mål slepte inn gjenstanden %s til båsen %s", "commands.item.target.no_changes": "Ingen mål slepte inn gjenstanden i båsen %s", "commands.item.target.no_such_slot": "Målet har ikkje båsen %s", "commands.item.target.not_a_container": "Målstad %s, %s, %s er ikkje eit ilåt", "commands.jfr.dump.failed": "Kunne ikkje dumpa JFR-opptak: %s", "commands.jfr.start.failed": "<PERSON>nne ikkje setja i gang JFR-profilering", "commands.jfr.started": "Sette i gang JFR-profilering", "commands.jfr.stopped": "JFR-profilering stansa og dumpa i %s", "commands.kick.owner.failed": "Kan ikkje kasta ut eigaren av tenar i LAN-spel", "commands.kick.singleplayer.failed": "Kan ikkje kasta ut spelarar i eit fråkopla einspelarspel", "commands.kick.success": "Kasta ut %s: %s", "commands.kill.success.multiple": "Drap %s einingar", "commands.kill.success.single": "Drap %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Det er %s av høgst %s spelarar pålogga: %s", "commands.locate.biome.not_found": "Kunne ikkje finna lende av slaget «%s» innanføre ein rimeleg fråstand", "commands.locate.biome.success": "Næraste %s er ved %s (%s blokker undan)", "commands.locate.poi.not_found": "Kunne ikkje finna noko interessepunkt av slaget «%s» innan rimleg fråstand", "commands.locate.poi.success": "Næraste %s er ved %s (%s blokker undan)", "commands.locate.structure.invalid": "Det er ingen bygnad av slaget «%s»", "commands.locate.structure.not_found": "Kunne ikkje finna nokon bygnad av slaget «%s» i nærleiken", "commands.locate.structure.success": "Næraste %s er ved %s (%s blokker undan)", "commands.message.display.incoming": "%s kviskrar til deg: %s", "commands.message.display.outgoing": "Du kviskrar til %s: %s", "commands.op.failed": "Ingenting gjord om. Spelaren er alt ein operatør", "commands.op.success": "Gjorde %s til ein tenaroperatør", "commands.pardon.failed": "Ingenting gjord om. Spelaren er ikkje bannlyst", "commands.pardon.success": "Oppheva bannlysing av %s", "commands.pardonip.failed": "Ingenting gjord om. Denne IPen er alt bannlyst", "commands.pardonip.invalid": "Ugild IP-adresse", "commands.pardonip.success": "Oppheva bannlysing av IP-adressa %s", "commands.particle.failed": "Partikkelen var ikkje synleg for nokon", "commands.particle.success": "Viser partikkelen %s", "commands.perf.alreadyRunning": "Ytingsprofileraren har alt byrja", "commands.perf.notRunning": "Ytingsprofileraren har ikkje byrja", "commands.perf.reportFailed": "Kunne ikkje laga feilsøkingsrapport", "commands.perf.reportSaved": "Laga feilsøkingsrapport i %s", "commands.perf.started": "Sette i gang ei ytingsprofilering på 10 sekund (bruka ‘/perf stop’ for å stansa tidleg)", "commands.perf.stopped": "Stansa ytingsprofilering etter %s sekund og %s tikk (%s tikk per sekund)", "commands.place.feature.failed": "<PERSON>nne ikkje setja ned funksjonen", "commands.place.feature.invalid": "Det er ingen funksjon av slaget «%s»", "commands.place.feature.success": "Sette ned «%s» på %s, %s, %s", "commands.place.jigsaw.failed": "Kunne ikkje skapa puslebrikke", "commands.place.jigsaw.invalid": "Det er ingen mal med type «%s»", "commands.place.jigsaw.success": "Skapa puslebrikke ved %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON> ikkje setja ned bygnad", "commands.place.structure.invalid": "Det er ingen bygnad av slaget «%s»", "commands.place.structure.success": "Skapa bygnaden «%s» ved %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON> ikkje setja ned mal", "commands.place.template.invalid": "Det er ingen mal med IDen «%s»", "commands.place.template.success": "Lasta malen «%s» ved %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON> er for langt borte til å verta høyrd", "commands.playsound.success.multiple": "Spelte ljoden %s til %s spelarar", "commands.playsound.success.single": "Spelte ljoden %s til %s", "commands.publish.alreadyPublished": "Fleirspelarspel er alt halde oppe på port %s", "commands.publish.failed": "Kunne ikkje skipa lokalspel", "commands.publish.started": "Port %s er vert for lokalspelet", "commands.publish.success": "Fleirspelarspel er no oppe på port %s", "commands.random.error.range_too_large": "Rekkevidda til den tilfeldige verdien kan maks vere 2147483646", "commands.random.error.range_too_small": "Rekkevidda av den tilfeldige verdien må vere minst 2", "commands.random.reset.all.success": "Sette %s tilfell<PERSON>g sekvens(ar)", "commands.random.reset.success": "Sette tilfelleleg sekvens %s", "commands.random.roll": "%s rulla %s (frå %s til %s)", "commands.random.sample.success": "Tilfelleleg verde: %s", "commands.recipe.give.failed": "Ingen nye oppskrifter lærte", "commands.recipe.give.success.multiple": "Låste opp %s oppskrifter for %s spelarar", "commands.recipe.give.success.single": "Låste opp %s oppskrifter for %s", "commands.recipe.take.failed": "Ingen oppskrifter kunne gløymast", "commands.recipe.take.success.multiple": "Attra %s oppskrifter frå %s spelarar", "commands.recipe.take.success.single": "Attra %s oppskrifter frå %s", "commands.reload.failure": "<PERSON>nne ikkje lasta inn å nyo. Held på gamal data", "commands.reload.success": "Lastar inn å nyo!", "commands.ride.already_riding": "%s rid alt %s", "commands.ride.dismount.success": "%s stansa å rida %s", "commands.ride.mount.failure.cant_ride_players": "Du kan ikkje rida spelarar", "commands.ride.mount.failure.generic": "%s kunne ikkje byrja å rida %s", "commands.ride.mount.failure.loop": "Eininga kan ikkje rida seg sjølv eller passasjerane sine", "commands.ride.mount.failure.wrong_dimension": "Kan ikkje setja eining på ei eining i ein annan heim", "commands.ride.mount.success": "%s byr<PERSON> <PERSON> rida %s", "commands.ride.not_riding": "%s rid inkje farty", "commands.rotate.success": "Roterte %s", "commands.save.alreadyOff": "Sjølvverkande lagring er alt avslege", "commands.save.alreadyOn": "Sjølvverkande lagring er alt på", "commands.save.disabled": "Sjølvverkande lagring er no avslege", "commands.save.enabled": "Sjølvverkande lagring er no påslege", "commands.save.failed": "Kunne ikkje lagra spelet (er det nok rom på harddisken?)", "commands.save.saving": "La<PERSON>r spelet (dette kan ta ei stund!)", "commands.save.success": "Lagra spelet", "commands.schedule.cleared.failure": "Ingen tidsplanar med ID %s", "commands.schedule.cleared.success": "Tok bort %s plan(ar) med id %s", "commands.schedule.created.function": "Planla funksjon «%s» i %s tikk på speletid %s", "commands.schedule.created.tag": "La i timeplan taggen «%s» om %s tikkingar ved speltid %s", "commands.schedule.macro": "Kan ikke tidfeste en macro", "commands.schedule.same_tick": "Kan ikkje leggja til dette tikket", "commands.scoreboard.objectives.add.duplicate": "<PERSON>it mål med dette namnet finst alt", "commands.scoreboard.objectives.add.success": "Skipa ny målsetjing %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Ingenting gjord om. Denne visingsstaden er alt tom", "commands.scoreboard.objectives.display.alreadySet": "Ingenting gjord om. Denne visingsstaden viser alt dette målet", "commands.scoreboard.objectives.display.cleared": "Tok bort alle målsetjingar i visingsstad %s", "commands.scoreboard.objectives.display.set": "Sette visingsstad %s til å visa målsetjing %s", "commands.scoreboard.objectives.list.empty": "Det finst ingen mål", "commands.scoreboard.objectives.list.success": "Det er %s målsetjing(ar): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Deaktiverte vising av automatisk oppdatering for målsetjing %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Aktiverte vising av automatisk oppdatering for målsetjing %s", "commands.scoreboard.objectives.modify.displayname": "Endra visingsnamnet for %s til %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Reinska standardnummerformatet for målet %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Gjorde om standardnummerformatet for målet %s", "commands.scoreboard.objectives.modify.rendertype": "<PERSON><PERSON> for objektiv %s", "commands.scoreboard.objectives.remove.success": "Tok bort målsetjing %s", "commands.scoreboard.players.add.success.multiple": "La %s til %s for %s einingar", "commands.scoreboard.players.add.success.single": "La %s til %s for %s (no %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Reinska visningsnamnet til %s einingar i %s", "commands.scoreboard.players.display.name.clear.success.single": "Reinska visningsnamnet til %s i %s", "commands.scoreboard.players.display.name.set.success.multiple": "Gjorde visningsnamnet om til %s for %s einingar i %s", "commands.scoreboard.players.display.name.set.success.single": "Gjorde visningsnamnet om til %s for %s i %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Reinska nummerformatet til %s einingar i %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Reinska nummerformatet til %s i %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Gjorde om nummerformatet til %s einingar i %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Gjorde om nummerformatet til %s i %s", "commands.scoreboard.players.enable.failed": "Ingenting gjord om. Denne utlø<PERSON>n er alt påslegen", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON> berre på utl<PERSON>ysarm<PERSON>", "commands.scoreboard.players.enable.success.multiple": "Slo på utløysar %s for %s einingar", "commands.scoreboard.players.enable.success.single": "Slo på utløysar %s for %s", "commands.scoreboard.players.get.null": "Kan ikkje henta verdet %s for %s. Inkje er sett", "commands.scoreboard.players.get.success": "%s har %s %s", "commands.scoreboard.players.list.empty": "Det er ingen spora einingar", "commands.scoreboard.players.list.entity.empty": "%s har ingen skòretal å visa", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s har %s skòretal:", "commands.scoreboard.players.list.success": "Det er %s spora eining(ar): %s", "commands.scoreboard.players.operation.success.multiple": "Oppdaterte %s for %s einingar", "commands.scoreboard.players.operation.success.single": "Sette %s for %s til %s", "commands.scoreboard.players.remove.success.multiple": "Tok bort %s frå %s for %s einingar", "commands.scoreboard.players.remove.success.single": "Tok bort %s frå %s for %s (no %s)", "commands.scoreboard.players.reset.all.multiple": "Sette attende alle skòretal for %s einingar", "commands.scoreboard.players.reset.all.single": "Set<PERSON> attende alle skòretal for %s", "commands.scoreboard.players.reset.specific.multiple": "Sette attende %s for %s einingar", "commands.scoreboard.players.reset.specific.single": "Sette attende %s for %s", "commands.scoreboard.players.set.success.multiple": "Sette %s for %s einingar til %s", "commands.scoreboard.players.set.success.single": "Sette %s for %s til %s", "commands.seed.success": "Seed: %s", "commands.setblock.failed": "<PERSON><PERSON> ikkje setja blokka", "commands.setblock.success": "Skifte blokka ved %s, %s, %s", "commands.setidletimeout.success": "Tidavbrotet for gjerandsløyse er no %s minutt", "commands.setidletimeout.success.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for dankeri er nå slått av", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON> be<PERSON> set<PERSON> for oververda", "commands.setworldspawn.success": "Set byrjestaden i verda til %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Set byrjestaden til %s, %s, %s [%s] i %s for %s spelarar", "commands.spawnpoint.success.single": "Set byrjestaden til %s, %s, %s [%s] i %s for %s", "commands.spectate.not_spectator": "%s er ikkje i tilskodarmodus", "commands.spectate.self": "Du kan ikkje vera tilskodar av deg sjølv", "commands.spectate.success.started": "Ser på %s no", "commands.spectate.success.stopped": "Ser ikkje på noka eining meir", "commands.spreadplayers.failed.entities": "Kunne ikkje breia %s eining(ar) kring %s, %s (for mange einingar for romet – freista å breia med minst %s)", "commands.spreadplayers.failed.invalid.height": "Ugild maxHeight %s; venta høgre enn verdsminiimum %s", "commands.spreadplayers.failed.teams": "Kunne ikkje breia %s lag kring %s, %s (for mange einingar for romet - freista å breia med minst %s)", "commands.spreadplayers.success.entities": "Breidde %s spelar(ar) kring %s, %s med ein gjennomsnittleg fråstand på %s blokker ifrå kvarandre", "commands.spreadplayers.success.teams": "Breidde %s lag kring %s, %s med ein gjennomsnittleg fråstand på %s blokker ifr<PERSON> kvarandre", "commands.stop.stopping": "<PERSON><PERSON><PERSON> tenaren", "commands.stopsound.success.source.any": "<PERSON><PERSON> alle «%s»-l<PERSON><PERSON><PERSON>", "commands.stopsound.success.source.sound": "Stansa ljoden «%s» frå «%s»", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON> alle ljodane", "commands.stopsound.success.sourceless.sound": "<PERSON><PERSON> ljoden «%s»", "commands.summon.failed": "Kunne ikkje framkalla eining", "commands.summon.failed.uuid": "Kunne ikkje framkalla eining på grunn av identiske UUIDar", "commands.summon.invalidPosition": "Ugild stad for framkalling", "commands.summon.success": "Tilkalla ny %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> har anten alt taggen, eller for mange taggar", "commands.tag.add.success.multiple": "La taggen «%s» til %s einingar", "commands.tag.add.success.single": "La taggen «%s» til %s", "commands.tag.list.multiple.empty": "Det er ingen taggar hjå dei %s einingane", "commands.tag.list.multiple.success": "Dei %s e<PERSON>ane har i alt %s taggar: %s", "commands.tag.list.single.empty": "%s har ingen taggar", "commands.tag.list.single.success": "%s har %s taggar: %s", "commands.tag.remove.failed": "<PERSON><PERSON><PERSON> har ikk<PERSON> denne taggen", "commands.tag.remove.success.multiple": "Tok bort taggen «%s» frå %s einingar", "commands.tag.remove.success.single": "Tok bort taggen «%s» frå %s", "commands.team.add.duplicate": "Eit lag med dette namnet finst alt", "commands.team.add.success": "Skipa lag %s", "commands.team.empty.success": "Tok bort %s medlem(er) frå lag %s", "commands.team.empty.unchanged": "Ingenting gjord om. Dette laget er alt tomt", "commands.team.join.success.multiple": "La %s medlemer til lag %s", "commands.team.join.success.single": "La %s til lag %s", "commands.team.leave.success.multiple": "Tok bort %s medlemer frå alle lag", "commands.team.leave.success.single": "Tok bort %s frå alle lag", "commands.team.list.members.empty": "Det er ingen medlemer på lag %s", "commands.team.list.members.success": "Lag %s har %s medlem(er): %s", "commands.team.list.teams.empty": "Det er ingen lag", "commands.team.list.teams.success": "Det er %s lag: %s", "commands.team.option.collisionRule.success": "Kollisjonsregelen for lag %s er no «%s»", "commands.team.option.collisionRule.unchanged": "Ingenting gjort om. Kollisjonsregelen har alt dette verdet", "commands.team.option.color.success": "Oppdaterte leten for lag %s til %s", "commands.team.option.color.unchanged": "Ingenting gjord om. Dette laget har alt denne leten", "commands.team.option.deathMessageVisibility.success": "Daudemeldingssynleik for lag %s er no «%s»", "commands.team.option.deathMessageVisibility.unchanged": "Ingenting gjord om. Synleiken for daudemeldinga har alt dette verdet", "commands.team.option.friendlyfire.alreadyDisabled": "Ingenting gjord om. Veneskading er alt avslege for dette laget", "commands.team.option.friendlyfire.alreadyEnabled": "Ingenting gjord om. Veneskading er alt slagen på for dette laget", "commands.team.option.friendlyfire.disabled": "Slo av veneskading for lag %s", "commands.team.option.friendlyfire.enabled": "Slo på veneskading for lag %s", "commands.team.option.name.success": "Oppdaterte lagnamnet åt %s", "commands.team.option.name.unchanged": "Ingenting gjord om. Dette laget har alt det namnet", "commands.team.option.nametagVisibility.success": "Namneskiltssynleik for lag %s er no «%s»", "commands.team.option.nametagVisibility.unchanged": "Ingenting gjord om. Synleiken for namneskilt har alt dette verdet", "commands.team.option.prefix.success": "Førefeste for lag sett til %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Ingenting gjord om. Dette laget kan ikkje sjå usynlege lagslemer alt", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Ingenting gjord om. Dette laget kan alt sjå usynlege lagslemer", "commands.team.option.seeFriendlyInvisibles.disabled": "Lag %s kan ikkje lenger sjå usynlege lagslemer", "commands.team.option.seeFriendlyInvisibles.enabled": "Lag %s kan no sjå usynlege lagslemer", "commands.team.option.suffix.success": "Etterfeste for lag sett til %s", "commands.team.remove.success": "Tok bort laget %s", "commands.teammsg.failed.noteam": "Du må vera på eit lag for å senda melding til laget", "commands.teleport.invalidPosition": "<PERSON><PERSON><PERSON> stad for teleportering", "commands.teleport.success.entity.multiple": "Teleporterte %s einingar til %s", "commands.teleport.success.entity.single": "Teleporterte %s til %s", "commands.teleport.success.location.multiple": "Teleporterte %s einingar til %s, %s, %s", "commands.teleport.success.location.single": "Teleporterte %s til %s,%s,%s", "commands.test.batch.starting": "Starter miljø %s, batch %s", "commands.test.clear.error.no_tests": "<PERSON>nne ikke finne noen tester å fjerne", "commands.test.clear.success": "Fjernet %s struktur(er)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klikk for å kopiere til utklippstavle", "commands.test.create.success": "Laget testoppsett for test %s", "commands.test.error.no_test_containing_pos": "Kan ikke finne en testinstans som inneholder %s, %s, %s", "commands.test.error.no_test_instances": "Fant ingen testinstanser", "commands.test.error.non_existant_test": "Test %s kunne ikke finnes", "commands.test.error.structure_not_found": "Teststruktur %s kunne ikke finnes", "commands.test.error.test_instance_not_found": "Testinstans’ blokkenhet kunne ikke finnes", "commands.test.error.test_instance_not_found.position": "Testinstans’ blokkenhet kunne ikke finnes i test på %s, %s, %s", "commands.test.error.too_large": "Strukturst<PERSON><PERSON>sen må være mindre enn %s blokker langs hver akse", "commands.test.locate.done": "Leting fullført, fant %s struktur(er)", "commands.test.locate.found": "Fant struktur på: %s (avstand: %s)", "commands.test.locate.started": "Startet leting etter <PERSON>turer, dette kan ta en stund...", "commands.test.no_tests": "Ingen tester å kjøre", "commands.test.relative_position": "Posisjon i forhold til %s: %s", "commands.test.reset.error.no_tests": "<PERSON>nne ikke finne noen tester å tilbakestille", "commands.test.reset.success": "Tilbakestilte %s struktur(er)", "commands.test.run.no_tests": "Ingen tester funnet", "commands.test.run.running": "<PERSON><PERSON><PERSON><PERSON> %s test(er)...", "commands.test.summary": "Spilltest fullført! %s test(er) ble kjørt", "commands.test.summary.all_required_passed": "<PERSON>e påkrevde tester best<PERSON><PERSON> :)", "commands.test.summary.failed": "%s påkrevd(e) test(er) mislyktes :(", "commands.test.summary.optional_failed": "%s valgfri(e) test(er) mislyktes", "commands.tick.query.percentiles": "Prosentar: P50: %sms P95: %sms P99: %sms, prøver: %s", "commands.tick.query.rate.running": "Målverde for tikksnøggleik: %s per sekund.\nGjennomsnittleg tid per tikk: %sms (Mål: %sms)", "commands.tick.query.rate.sprinting": "Målverde for tikksnøggleik: %s per sekund (ignorert, einast til opplysing).\nGjennomsnittleg tid per tikk: %sms", "commands.tick.rate.success": "Sette målverdet for tikksnøggleiken til %s per sekund", "commands.tick.sprint.report": "Sprang fullført med %s tikk per sekund, eller %s ms per tikk", "commands.tick.sprint.stop.fail": "Inkje tikksprang er på gang", "commands.tick.sprint.stop.success": "<PERSON>it tikksprang vart avbrote", "commands.tick.status.frozen": "Spele er frose", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON>ø<PERSON>, men kan ikkje nå målverdet for tikksnøggleik", "commands.tick.status.running": "Spelet køyrer som normalt", "commands.tick.status.sprinting": "Spelet spring", "commands.tick.step.fail": "<PERSON>nne ikkje stega spelet – spelet må verta frose fyrst", "commands.tick.step.stop.fail": "Inkje tikksteg er på gang", "commands.tick.step.stop.success": "Eit tikksteg vart avbrote", "commands.tick.step.success": "Stig %s tikk", "commands.time.query": "Tida er %s", "commands.time.set": "Sette tida til %s", "commands.title.cleared.multiple": "Tok bort titlar frå %s spelarar", "commands.title.cleared.single": "Tok bort titlar frå %s", "commands.title.reset.multiple": "<PERSON><PERSON> attende tittelvala for %s spelarar", "commands.title.reset.single": "<PERSON><PERSON> attende tittelvala for %s", "commands.title.show.actionbar.multiple": "Viser ny tittel i åtgjerdsfeltet for %s spelarar", "commands.title.show.actionbar.single": "Viser ny tittel i åtgjerdsfeltet for %s", "commands.title.show.subtitle.multiple": "Viser ny undertekst for %s spelarar", "commands.title.show.subtitle.single": "Viser ny undertekst for %s", "commands.title.show.title.multiple": "Viser ny tittel for %s spelarar", "commands.title.show.title.single": "Viser ny tittel for %s", "commands.title.times.multiple": "Gjorde om tittelvisingstid for %s spelarar", "commands.title.times.single": "Gjorde om tittelvisingstid for %s", "commands.transfer.error.no_players": "Må peika ut minst éin spelar å overføra", "commands.transfer.success.multiple": "Overfører %s spelarar til %s:%s", "commands.transfer.success.single": "Overfører %s til %s:%s", "commands.trigger.add.success": "Utløyste %s (la til %s på verdet)", "commands.trigger.failed.invalid": "Du kan berre utløysa mål av slaget «trigger»", "commands.trigger.failed.unprimed": "Du kan ikkje utløysa dette målet enno", "commands.trigger.set.success": "Utløyste %s (sett verdet til %s)", "commands.trigger.simple.success": "Utløyste %s", "commands.version.build_time": "byggningstid = %s", "commands.version.data": "data = %s", "commands.version.header": "Info om serverversjon:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pakkedata = %s", "commands.version.pack.resource": "pakkeressurs = %s", "commands.version.protocol": "protokoll = %s (%s)", "commands.version.series": "serie = %s", "commands.version.stable.no": "stabil = nei", "commands.version.stable.yes": "stabil = ja", "commands.waypoint.list.empty": "Ingen vardar i %s", "commands.waypoint.list.success": "%s vard(e/ar) i %s: %s", "commands.waypoint.modify.color": "Vardefarge er no %s", "commands.waypoint.modify.color.reset": "<PERSON>t attende vardefarge", "commands.waypoint.modify.style": "Vardestil endra", "commands.weather.set.clear": "Sette vêret til klårt", "commands.weather.set.rain": "Sette vêret til regn", "commands.weather.set.thunder": "Sette vêret til regn og tore", "commands.whitelist.add.failed": "Spelaren er alt kvitelista", "commands.whitelist.add.success": "La til %s i kvitelista", "commands.whitelist.alreadyOff": "Kvitelista er alt avslega", "commands.whitelist.alreadyOn": "Kvitelista er alt påslega", "commands.whitelist.disabled": "Kvitelista er no avslega", "commands.whitelist.enabled": "Kvitelista er no påslega", "commands.whitelist.list": "Det er %s kvitelista spelar(ar): %s", "commands.whitelist.none": "Det er ingen kvitelista spelarar", "commands.whitelist.reloaded": "Kvitelista vart lasta å nyo", "commands.whitelist.remove.failed": "Spelaren er ikkje kvitelista", "commands.whitelist.remove.success": "Tok bort %s frå kvitelista", "commands.worldborder.center.failed": "Ingenting gjord om. Verdsgrensa har alt midpunkt der", "commands.worldborder.center.success": "Sette midpunkt av verdsgrensa til %s, %s", "commands.worldborder.damage.amount.failed": "Ingenting gjord om. Verdsgrenseskaden er alt denne mengda", "commands.worldborder.damage.amount.success": "Set skaden ved verdsgrensa til %s per blokk kvart sekund", "commands.worldborder.damage.buffer.failed": "Ingenting gjord om. Bufferen for verdsgrenseskade har alt denne fråstanden", "commands.worldborder.damage.buffer.success": "Sette skadeområdet åt verdsgrensa til %s blokk(er)", "commands.worldborder.get": "Verdsgrensa er for augneblinken %s blokk(ar) breid", "commands.worldborder.set.failed.big": "Verdsgrensa kan ikkje vera vidare enn %s blokker", "commands.worldborder.set.failed.far": "Verdsgrensa kan ikkje vera lengre ute enn %s blokker", "commands.worldborder.set.failed.nochange": "Ingenting gjord om. Verdsgrensa har alt denne storleiken", "commands.worldborder.set.failed.small": "Verdsgrensa kan ikkje vera mindre enn 1 blokk vid", "commands.worldborder.set.grow": "Forstørrar verdsgrensa til %s blokker vid over %s sekund", "commands.worldborder.set.immediate": "Sette verdsgrensa til %s blokk(er) vid", "commands.worldborder.set.shrink": "Minskar verdsgrensa til %s blokk(er) vid på %s sekund", "commands.worldborder.warning.distance.failed": "Ingenting gjord om. <PERSON><PERSON><PERSON> for verdsgrenseskade har alt denne fråstanden", "commands.worldborder.warning.distance.success": "Sette åtvaringsfråstanden for verdsgrensa til %s blokk(er)", "commands.worldborder.warning.time.failed": "Ingenting gjord om. <PERSON><PERSON><PERSON> for verdsgrenseskade har alt denne tidemengda", "commands.worldborder.warning.time.success": "Sette åtvaringstida åtverdsgrensa til %s sekund", "compliance.playtime.greaterThan24Hours": "Du har spela i meir enn 24 timar", "compliance.playtime.hours": "Du har spela i %s time/-ar", "compliance.playtime.message": "For mykje speling kan skipla k<PERSON>agen din", "connect.aborted": "Brote av", "connect.authorizing": "Loggar inn...", "connect.connecting": "Ko<PERSON><PERSON> til tenaren...", "connect.encrypting": "<PERSON><PERSON><PERSON><PERSON>...", "connect.failed": "Kunne ikkje kopla til tenaren", "connect.failed.transfer": "Til<PERSON>pling mislykkast under overføring til serveren", "connect.joining": "Går inn i verd...", "connect.negotiating": "Forhandlar...", "connect.reconfiging": "Omkonfigurerer...", "connect.reconfiguring": "Omkonfigurerer...", "connect.transferring": "<PERSON><PERSON><PERSON> over til ny tenar...", "container.barrel": "<PERSON><PERSON>", "container.beacon": "Varde", "container.beehive.bees": "Bier: %s / %s", "container.beehive.honey": "Honning: %s / %s", "container.blast_furnace": "Malmomn", "container.brewing": "Bryggjereiskap", "container.cartography_table": "Kartteiknarbord", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON> kiste", "container.crafter": "<PERSON><PERSON>", "container.crafting": "<PERSON><PERSON>", "container.creative": "<PERSON><PERSON>", "container.dispenser": "Utskytar", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "Galdra", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s <PERSON><PERSON><PERSON>", "container.enchant.lapis.one": "<PERSON><PERSON>", "container.enchant.level.many": "%s trolldomssteg", "container.enchant.level.one": "1 trolldomssteg", "container.enchant.level.requirement": "Røynslekrav: %s", "container.enderchest": "Enderkis<PERSON>", "container.furnace": "Omn", "container.grindstone_title": "Vøl og avgaldra", "container.hopper": "Gjenstandstrekt", "container.inventory": "Inventar", "container.isLocked": "%s er låst!", "container.lectern": "Talarstol", "container.loom": "Vevstol", "container.repair": "Vøl og gjev namn", "container.repair.cost": "Trolldomskostnad: %1$s", "container.repair.expensive": "For dyrt!", "container.shulkerBox": "Shulkerboks", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "og %s til...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Røykjaromn", "container.spectatorCantOpen": "Kunne ikkje opna. Tjuvgods er ikkje skapa enno.", "container.stonecutter": "Steinskjerar", "container.upgrade": "Betra utbunad", "container.upgrade.error_tooltip": "Kan ikkje betra utbunaden slik", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON> til smideskant", "controls.keybinds": "Tastebindingar...", "controls.keybinds.duplicateKeybinds": "<PERSON>ne tasten er òg bruka til:\n%s", "controls.keybinds.title": "Tastebindingar", "controls.reset": "Attra", "controls.resetAll": "Set attende knappar", "controls.title": "Ko<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Vel eit lende", "createWorld.customize.buffet.title": "Bufféverdstilmåting", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Nedste %s", "createWorld.customize.flat.layer.top": "Topp %s", "createWorld.customize.flat.removeLayer": "Tak bort lag", "createWorld.customize.flat.tile": "Materiale på lag", "createWorld.customize.flat.title": "Tilpass vassrettinnstillingar", "createWorld.customize.presets": "Førehandsinnstilling", "createWorld.customize.presets.list": "Til alternativ er her nokre me har laga tidlegare!", "createWorld.customize.presets.select": "Bruk førehandsinnstilling", "createWorld.customize.presets.share": "Ynskjer du å dela ei førehandsinnstilling med nokon? Bruk boksa under!", "createWorld.customize.presets.title": "Vel førehandsinnstilling", "createWorld.preparing": "<PERSON><PERSON><PERSON><PERSON>...", "createWorld.tab.game.title": "Spel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "Verd", "credits_and_attribution.button.attribution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.credits": "Medverkande", "credits_and_attribution.button.licenses": "Lisensar", "credits_and_attribution.screen.title": "Medverkande og tilleggjingar", "dataPack.bundle.description": "Slår på bundelgjenstand til utrøyning", "dataPack.bundle.name": "<PERSON><PERSON>", "dataPack.locator_bar.description": "Vis retningene av andre spillere i flerspillermodus", "dataPack.locator_bar.name": "Lokaliseringslinje", "dataPack.minecart_improvements.description": "Forbedrede gruvevognsbevegelser", "dataPack.minecart_improvements.name": "Gruvevognsforbedringer", "dataPack.redstone_experiments.description": "Eksperimentelle redstone-endringer", "dataPack.redstone_experiments.name": "Redstone-eksperiment", "dataPack.title": "Vel datapakkar", "dataPack.trade_rebalance.description": "Oppdaterte byte for bygdefolk", "dataPack.trade_rebalance.name": "Ombalanserte bygdebubyte", "dataPack.update_1_20.description": "Nye funksjonar og innehald til Minecraft 1.20", "dataPack.update_1_20.name": "Oppdatering 1.20", "dataPack.update_1_21.description": "Nye funksjonar og innehald til Minecraft 1.21", "dataPack.update_1_21.name": "Oppdatering 1.21", "dataPack.validation.back": "<PERSON><PERSON> attende", "dataPack.validation.failed": "Validering av datapakke var mislukka!", "dataPack.validation.reset": "Set attende til føreval", "dataPack.validation.working": "Validerer valde datapakkar...", "dataPack.vanilla.description": "Dei førevalde dataa for Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "Nye funksjonar og innehald i vettersleppet", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Trygg-modus", "datapackFailure.safeMode.failed.description": "Denne verda inneheld ugilde eller øydelagde lagringsdata.", "datapackFailure.safeMode.failed.title": "<PERSON><PERSON> ikkje lasta verda i trygg-modus.", "datapackFailure.title": "Feil i dei valde datapakkane hindra verda frå å lasta.\nDu kan anten freista å lasta med vanleg datapakke («trygg-modus») eller gå attende til hovudmenyen og retta det opp for hand.", "death.attack.anvil": "%1$s vart kverka av eit fallande smieste", "death.attack.anvil.player": "%1$s vart klemra av eit fallande smieste i strid med %2$s", "death.attack.arrow": "%1$s vart skoten av %2$s", "death.attack.arrow.item": "%1$s vart skoten av %2$s med %3$s", "death.attack.badRespawnPoint.link": "Tiltenkt speldesign", "death.attack.badRespawnPoint.message": "%1$s vart drepen av %2$s", "death.attack.cactus": "%1$s stakk seg i hel", "death.attack.cactus.player": "%1$s gjekk rett på ein kaktus i flukt frå %2$s", "death.attack.cramming": "%1$s vart klemra for mykje", "death.attack.cramming.player": "%1$s vart klemra av %2$s", "death.attack.dragonBreath": "%1$s vart sprøsteikt av drakepust", "death.attack.dragonBreath.player": "%1$s vart sprøsteikt av drakepust frå %2$s", "death.attack.drown": "%1$s drukna", "death.attack.drown.player": "%1$s drukna i flukt frå %2$s", "death.attack.dryout": "%1$s turka i hel", "death.attack.dryout.player": "%1$s turka i hel i flukt frå %2$s", "death.attack.even_more_magic": "%1$s vart drepen av endå meir trolldom", "death.attack.explosion": "%1$s vart sprengd i lufta", "death.attack.explosion.player": "%1$s vart sprengd i lufta av %2$s", "death.attack.explosion.player.item": "%1$s vart sprengd i filler av at %2$s bruka %3$s", "death.attack.fall": "%1$s møtte bakken ei smole for hardt", "death.attack.fall.player": "%1$s møtte bakken for hardt i freistnad på å fly %2$s", "death.attack.fallingBlock": "%1$s vart klemra av ei fallande blokk", "death.attack.fallingBlock.player": "%1$s vart klemra av ei fallande blokk i strid med %2$s", "death.attack.fallingStalactite": "%1$s vart spita av fallande stalaktitt", "death.attack.fallingStalactite.player": "%1$s vart spita av fallande stalaktitt i strid med %2$s", "death.attack.fireball": "%1$s vart klinka av ei eldkule frå %2$s", "death.attack.fireball.item": "%1$s vart klinka av ei eldkule frå %2$s med %3$s", "death.attack.fireworks": "%1$s gjekk av med eit pang", "death.attack.fireworks.item": "%1$s gjekk av med eit smell frå eit fyrverk skote frå %3$s av %2$s", "death.attack.fireworks.player": "%1$s gjekk av med eit pang i strid med %2$s", "death.attack.flyIntoWall": "%1$s røynde rørslekraft", "death.attack.flyIntoWall.player": "%1$s røynde rørslekraft i freistnad på å fly %2$s", "death.attack.freeze": "%1$s fraus i hel", "death.attack.freeze.player": "%1$s vart frøyst i hel av %2$s", "death.attack.generic": "%1$s døydde", "death.attack.generic.player": "%1$s døydde på grunn av %2$s", "death.attack.genericKill": "%1$s vart drepen", "death.attack.genericKill.player": "%1$s vart drepen i strid med %2$s", "death.attack.hotFloor": "%1$s fann ut at golvet var lava", "death.attack.hotFloor.player": "%1$s gjekk inn i faresona på grunn av %2$s", "death.attack.inFire": "%1$s gjekk opp i røyk", "death.attack.inFire.player": "%1$s vart ein smule for heit i kampen mot %2$s", "death.attack.inWall": "%1$s vart kvelt i ei blokk", "death.attack.inWall.player": "%1$s vart kvelt i ein vegg i strid med %2$s", "death.attack.indirectMagic": "%1$s vart drepen av %2$s med trolldom", "death.attack.indirectMagic.item": "%1$s vart drepen av %2$s med %3$s", "death.attack.lava": "%1$s freista symja i lava", "death.attack.lava.player": "%1$s freista symja i lava i flog frå %2$s", "death.attack.lightningBolt": "%1$s vart treft av lynet", "death.attack.lightningBolt.player": "%1$s vart slegen av lynet i strid med %2$s", "death.attack.mace_smash": "%1$s ble most av %2$s", "death.attack.mace_smash.item": "%1$s ble most av %2$s væpnet med %3$s", "death.attack.magic": "%1$s vart drepen av trolldom", "death.attack.magic.player": "%1$s vart drepen av trolldom i flukt frå %2$s", "death.attack.message_too_long": "Meldinga var diverre for lang til å verta send. Orsak! Her er ei korta ned utgåve: %s", "death.attack.mob": "%1$s vart kverka av %2$s", "death.attack.mob.item": "%1$s vart kverka av %2$s med %3$s", "death.attack.onFire": "%1$s brann i hel", "death.attack.onFire.item": "%1$s vart sprøsteikt i kampen mot %2$s med %3$s", "death.attack.onFire.player": "%1$s vart sprøsteikt i kampen mot %2$s", "death.attack.outOfWorld": "%1$s fall ut av verda", "death.attack.outOfWorld.player": "%1$s ynskte ikkje å leva i den same verda som %2$s", "death.attack.outsideBorder": "%1$s hamna utanføre verdsgrensa", "death.attack.outsideBorder.player": "%1$s hamna utanføre verdsgrensa i strid med %2$s", "death.attack.player": "%1$s vart drepen av %2$s", "death.attack.player.item": "%1$s vart kverka av %2$s med %3$s", "death.attack.sonic_boom": "%1$s vart utsletta av eit supersonisk skrik", "death.attack.sonic_boom.item": "%1$s vart utsletta av eit supersonisk skrik i flukt frå %2$s med %3$s", "death.attack.sonic_boom.player": "%1$s vart utsletta av eit supersonisk skrik i flukt frå %2$s", "death.attack.stalagmite": "%1$s vart spita på ein stalagmitt", "death.attack.stalagmite.player": "%1$s vart spita på ein stalagmitt i strid med %2$s", "death.attack.starve": "%1$s svalt i hel", "death.attack.starve.player": "%1$s svalt i hel i strid med %2$s", "death.attack.sting": "%1$s vart stungen i hel", "death.attack.sting.item": "%1$s vart stungen i hel av %2$s med %3$s", "death.attack.sting.player": "%1$s vart stungen i hel av %2$s", "death.attack.sweetBerryBush": "%1$s vart stungen i hel av ein søtbærbusk", "death.attack.sweetBerryBush.player": "%1$s vart stungen i hel av ein søtbærbusk i flukt frå %2$s", "death.attack.thorns": "%1$s vart drepen i freistnad på å skada %2$s", "death.attack.thorns.item": "%1$s vart drepen av %3$s i freistnad på å skada %2$s", "death.attack.thrown": "%1$s vart bombardert av %2$s", "death.attack.thrown.item": "%1$s vart bombardert av %2$s med %3$s", "death.attack.trident": "%1$s vart spita av %2$s", "death.attack.trident.item": "%1$s vart spita av %2$s med %3$s", "death.attack.wither": "%1$s visna", "death.attack.wither.player": "%1$s visna bort i strid med %2$s", "death.attack.witherSkull": "%1$s vart skoten av ein skalle frå %2$s", "death.attack.witherSkull.item": "%1$s vart skoten av ein skalle frå %2$s med %3$s", "death.fell.accident.generic": "%1$s fall frå ein høg plass", "death.fell.accident.ladder": "%1$s ramla frå ein stige", "death.fell.accident.other_climbable": "%1$s kleiv og fall", "death.fell.accident.scaffolding": "%1$s fall ned frå ei stelling", "death.fell.accident.twisting_vines": "%1$s fall av nokre krokande klivevokstrar", "death.fell.accident.vines": "%1$s fall av ein klivevokster", "death.fell.accident.weeping_vines": "%1$s fall av gråtande klatrevokster", "death.fell.assist": "%1$s vart tvinga til å falla av %2$s", "death.fell.assist.item": "%1$s vart tvinga til å falla av %2$s med %3$s", "death.fell.finish": "%1$s datt for langt og var drepen av %2$s", "death.fell.finish.item": "%1$s datt for langt og var drepen av %2$s som bruka %3$s", "death.fell.killer": "%1$s vart tvinga til å falla", "deathScreen.quit.confirm": "Er du trygg på at du vil enda spelet?", "deathScreen.respawn": "Stå opp att", "deathScreen.score": "Skòretal", "deathScreen.score.value": "Skòretal: %s", "deathScreen.spectate": "<PERSON><PERSON>", "deathScreen.title": "Du <PERSON>øyd<PERSON>!", "deathScreen.title.hardcore": "Du tapa!", "deathScreen.titleScreen": "Hovudmeny", "debug.advanced_tooltips.help": "F3 + H = Avanserte verktyråd", "debug.advanced_tooltips.off": "Avanserte verktyråd: g<PERSON><PERSON><PERSON>", "debug.advanced_tooltips.on": "Avanserte verktyråd: vist", "debug.chunk_boundaries.help": "F3 + G = Vis grenser mellom verdsbitane", "debug.chunk_boundaries.off": "Verdsbitgrenser: g<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "Verdsbitgrenser: vist", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON> nettpraten", "debug.copy_location.help": "F3 + C = Tak avrit av stadsetjing som /tp kommando. Haldt F3 + C for å krasja spelet", "debug.copy_location.message": "La stadsetjing i utklippstavla", "debug.crash.message": "F3 + C er halde nede. Dette vil krasja spelet om ikkje sleppt.", "debug.crash.warning": "Krasjar om %s...", "debug.creative_spectator.error": "<PERSON>nne ikkje byta spel<PERSON>, inkje løyve", "debug.creative_spectator.help": "F3 + N = Byt mellom førre spelmodus <-> tilskodar", "debug.dump_dynamic_textures": "Lagra dynamiske teksturar til %s", "debug.dump_dynamic_textures.help": "F3 + S = dumpa dynamiske teksturar", "debug.gamemodes.error": "Kunne ikkje opna spelmodusskiftar; manglar løyve", "debug.gamemodes.help": "F3 + F4 = Opna spelmodusskiftar", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s neste", "debug.help.help": "F3 + Q = Vis denne lista", "debug.help.message": "Tastebindingar:", "debug.inspect.client.block": "Kopierte blokkdata frå klienten til utklippstavla", "debug.inspect.client.entity": "Kopierte einingsdata frå klienten til utklippstavla", "debug.inspect.help": "F3 + I = Tak avrit av e<PERSON><PERSON>- el<PERSON> blokkdata", "debug.inspect.server.block": "Kopierte blokkdata frå tenaren til utklippstavla", "debug.inspect.server.entity": "Kopierte einingsdata frå tenaren til utklippstavla", "debug.pause.help": "F3 + Esc = Pausing utan pausemeny (om pausing er mogeleg)", "debug.pause_focus.help": "F3 + P = Avbrot av mist fokus", "debug.pause_focus.off": "A<PERSON><PERSON>t ved mist fokus: slege av", "debug.pause_focus.on": "A<PERSON>brot ved mist fokus: slege på", "debug.prefix": "[<PERSON><PERSON><PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Byrja/enda profilering", "debug.profiling.start": "Profilering for %s sekund byrja. Bruka F3 + L for å stansa tidleg", "debug.profiling.stop": "Profilering enda. Lagra utfall til %s", "debug.reload_chunks.help": "F3 + A = Lastar verdsbitar å nyo", "debug.reload_chunks.message": "Lastar alle verdsbitar å nyo", "debug.reload_resourcepacks.help": "F3 + T = Last tilfangspakkar å nyo", "debug.reload_resourcepacks.message": "<PERSON>a til<PERSON> å nyo", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON>", "debug.show_hitboxes.off": "Hittboksar: gø<PERSON><PERSON>", "debug.show_hitboxes.on": "Hittboksar: viste", "debug.version.header": "Info om klientversjon:", "debug.version.help": "F3 + V = Info om klientversjonen", "demo.day.1": "Denne prøveutgåva varer i fem speledagar. Gjer ditt beste!", "demo.day.2": "Dag to", "demo.day.3": "Dag tre", "demo.day.4": "Dag fire", "demo.day.5": "Dette er den siste dagen din!", "demo.day.6": "Den siste dagen din er over. Trykk på %s for å ta ein skjermdump av byggverket ditt.", "demo.day.warning": "Tida er snart ute!", "demo.demoExpired": "Prøvetida er ute!", "demo.help.buy": "<PERSON><PERSON><PERSON><PERSON> no!", "demo.help.fullWrapped": "<PERSON>ne prøveutgåva vil vara i 5 speledagar (kring 1 time og 40 minutt). <PERSON>jå bragdene for tips! Ha det kjekt!", "demo.help.inventory": "Trykk %1$s for å opna inventaret ditt", "demo.help.jump": "Trykk %%s for å hoppa", "demo.help.later": "Haldt fram med å spela!", "demo.help.movement": "Bruk %1$s, %2$s, %3$s, %4$s og datamusa for å røra deg", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> kring deg ved å bruka musa", "demo.help.movementShort": "<PERSON><PERSON><PERSON> deg ved å pressa %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft-prøveutgåve", "demo.remainingTime": "Tid att: %s", "demo.reminder": "Prøvetida er over. <PERSON><PERSON><PERSON><PERSON> spelet for å halda fram, eller lag ei ny verd!", "difficulty.lock.question": "Er du trygg på at du vil låsa vandleiken på denne verda? Dette vil setja vandleiken til %1$s, og du vil aldri kunna skifta honom att.", "difficulty.lock.title": "<PERSON><PERSON><PERSON> van<PERSON> til verda", "disconnect.endOfStream": "<PERSON>lutt på samband", "disconnect.exceeded_packet_rate": "<PERSON><PERSON> ut for å stiga over grensa for datapakkar", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorerer statusførespurnad", "disconnect.loginFailedInfo": "Innlogging var mislukka: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Fleirspelar er avslege. Sjå til innstillingane for Microsoft-kontoen din.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> (Freista å starta spelet og startsida å nyo)", "disconnect.loginFailedInfo.serversUnavailable": "Kunne ikkje nå stadfestartenarane for augneblinken. Freista å nyo.", "disconnect.loginFailedInfo.userBanned": "Du er bannlyst frå å spela på nett", "disconnect.lost": "Miste sambandet", "disconnect.packetError": "Netverksprotokollfeil", "disconnect.spam": "<PERSON>sta ut på grunn av spamming", "disconnect.timeout": "Tidavbrot", "disconnect.transfer": "<PERSON>ført til ein annan tenar", "disconnect.unknownHost": "Ukje<PERSON> vert", "download.pack.failed": "Kunne ikkje lasta ned %s av %s pakkar", "download.pack.progress.bytes": "Framgang: %s (total storleik ukjend)", "download.pack.progress.percent": "Framgang: %s%%", "download.pack.title": "Lastar ned tilfan<PERSON>ke %s/%s", "editGamerule.default": "Føreval: %s", "editGamerule.title": "Gjer om på spelereglane", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Oppsuging", "effect.minecraft.bad_omen": "<PERSON><PERSON> jar<PERSON>", "effect.minecraft.blindness": "Blindskap", "effect.minecraft.conduit_power": "Flødarkraft", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Delfinlukke", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Skunding", "effect.minecraft.health_boost": "Helseauke", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "Svolt", "effect.minecraft.infested": "<PERSON><PERSON>", "effect.minecraft.instant_damage": "Skade på augneblinken", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON> på augneblinken", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON><PERSON> hopp", "effect.minecraft.levitation": "Sviving", "effect.minecraft.luck": "Lukke", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Ty<PERSON>", "effect.minecraft.poison": "Gift", "effect.minecraft.raid_omen": "Herjingsjartegn", "effect.minecraft.regeneration": "Attskaping", "effect.minecraft.resistance": "Vern", "effect.minecraft.saturation": "Metting", "effect.minecraft.slow_falling": "Seint fall", "effect.minecraft.slowness": "Tråleik", "effect.minecraft.speed": "Snøggleik", "effect.minecraft.strength": "Styrke", "effect.minecraft.trial_omen": "Røynejartegn", "effect.minecraft.unluck": "<PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "V<PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "Veikskap", "effect.minecraft.weaving": "Veving", "effect.minecraft.wind_charged": "Vindlada", "effect.minecraft.wither": "V<PERSON><PERSON>", "effect.none": "Ingen verknader", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Vassvyrk", "enchantment.minecraft.bane_of_arthropods": "Leddyrsbane", "enchantment.minecraft.binding_curse": "Bindingstrolldom", "enchantment.minecraft.blast_protection": "Sprengnadsvern", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Tettleik", "enchantment.minecraft.depth_strider": "Dypt<PERSON><PERSON>", "enchantment.minecraft.efficiency": "Dugleik", "enchantment.minecraft.feather_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_aspect": "Eldverknad", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON>d", "enchantment.minecraft.fortune": "R<PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Frost<PERSON>gar", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Atterslag", "enchantment.minecraft.looting": "Plundring", "enchantment.minecraft.loyalty": "Trufast", "enchantment.minecraft.luck_of_the_sea": "Fiskelukke", "enchantment.minecraft.lure": "Agn", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Mangskot", "enchantment.minecraft.piercing": "Gjennomtrengjande", "enchantment.minecraft.power": "Kraft", "enchantment.minecraft.projectile_protection": "Skotvern", "enchantment.minecraft.protection": "Vern", "enchantment.minecraft.punch": "Slag", "enchantment.minecraft.quick_charge": "Snøgglading", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sharpness": "Kvassleik", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "Vandaudsbane", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "Sveipande kniv<PERSON>gg", "enchantment.minecraft.sweeping_edge": "Sveipande kniv<PERSON>gg", "enchantment.minecraft.swift_sneak": "Snøggsmyging", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Kvervingstrolldom", "enchantment.minecraft.wind_burst": "Vindstøyt", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "Akasiebåt med kiste", "entity.minecraft.allay": "H<PERSON>lpeånd", "entity.minecraft.area_effect_cloud": "Områdeverknadssky", "entity.minecraft.armadillo": "Beltedyr", "entity.minecraft.armor_stand": "Rustningstativ", "entity.minecraft.arrow": "<PERSON>l", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambusbåt med kiste", "entity.minecraft.bamboo_raft": "Bambusflòte", "entity.minecraft.bat": "Skinnvengje", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "Bjørkebåt med kiste", "entity.minecraft.blaze": "Eldskrømt", "entity.minecraft.block_display": "Blokkvising", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Vindskrømt", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.camel": "Dr<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "Kissebærb<PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "Kissebærbåt med kiste", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON> med kiste", "entity.minecraft.chest_minecart": "Gruvevogn med kiste", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Torsk", "entity.minecraft.command_block_minecart": "Gruvevogn med kommandoblokk", "entity.minecraft.cow": "Ku", "entity.minecraft.creaking": "Knirk", "entity.minecraft.creaking_transient": "Knirk", "entity.minecraft.creeper": "creeper", "entity.minecraft.dark_oak_boat": "Mørkeikeb<PERSON>t", "entity.minecraft.dark_oak_chest_boat": "Mørkeikebåt med kiste", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Eldkule frå drake", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Kasta egg", "entity.minecraft.elder_guardian": "<PERSON><PERSON>", "entity.minecraft.end_crystal": "Endekrystall", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Ender<PERSON>", "entity.minecraft.evoker": "Åndemanar", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "<PERSON><PERSON>", "entity.minecraft.experience_orb": "R<PERSON><PERSON>sleku<PERSON>", "entity.minecraft.eye_of_ender": "Enderauga", "entity.minecraft.falling_block": "<PERSON>ande blokk", "entity.minecraft.falling_block_type": "Fallande %s", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "Fyrverk", "entity.minecraft.fishing_bobber": "<PERSON><PERSON>", "entity.minecraft.fox": "Rev", "entity.minecraft.frog": "Frosk", "entity.minecraft.furnace_minecart": "Gruvevogn med omn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Gløderamme", "entity.minecraft.glow_squid": "Glødesprut", "entity.minecraft.goat": "Geit", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "Gladghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Gruvevogn med trekt", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Skaldauding", "entity.minecraft.illusioner": "Sjonkvervar", "entity.minecraft.interaction": "Samhandling", "entity.minecraft.iron_golem": "Jarnkjempe", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Gjenstandsvising", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON>elb<PERSON>t med kiste", "entity.minecraft.killer_bunny": "Mordarkaninen", "entity.minecraft.leash_knot": "Tygelknute", "entity.minecraft.lightning_bolt": "Lynnedslag", "entity.minecraft.lingering_potion": "Dveland<PERSON> brygg", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmakube", "entity.minecraft.mangrove_boat": "Mangrovebåt", "entity.minecraft.mangrove_chest_boat": "Mangrovebåt med kiste", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Gruvevogn", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON>ld<PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON>b<PERSON>t med kiste", "entity.minecraft.ocelot": "Ozelot", "entity.minecraft.ominous_item_spawner": "Illevarslende gjenstandsfremkaller", "entity.minecraft.painting": "Målarstykke", "entity.minecraft.pale_oak_boat": "Bleikeikebå<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Bleikeikebåt med kiste", "entity.minecraft.panda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.parrot": "Papegøye", "entity.minecraft.phantom": "<PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "Kulefisk", "entity.minecraft.rabbit": "<PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON>", "entity.minecraft.salmon": "Laks", "entity.minecraft.sheep": "Sau", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Sylvkrek", "entity.minecraft.skeleton": "be<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "Hestebeinrangel", "entity.minecraft.slime": "Sliming", "entity.minecraft.small_fireball": "<PERSON><PERSON>", "entity.minecraft.sniffer": "Moldnase", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON><PERSON>\n", "entity.minecraft.snowball": "Snøball", "entity.minecraft.spawner_minecart": "Gruvevogn med framkallar", "entity.minecraft.spectral_arrow": "Merkingspil", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "Granb<PERSON>t med kiste", "entity.minecraft.squid": "Blekksprut", "entity.minecraft.stray": "<PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "R<PERSON>et<PERSON>", "entity.minecraft.text_display": "Tekstvising", "entity.minecraft.tnt": "Tendra TNT", "entity.minecraft.tnt_minecart": "Gruvevogn med TNT", "entity.minecraft.trader_llama": "Kjøpmannslama", "entity.minecraft.trident": "Ljoster", "entity.minecraft.tropical_fish": "Sudhavsfisk", "entity.minecraft.tropical_fish.predefined.0": "Sjørosefisk", "entity.minecraft.tropical_fish.predefined.1": "Svart kirurg", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> cornutus", "entity.minecraft.tropical_fish.predefined.11": "Pryda skjelfinnefisk", "entity.minecraft.tropical_fish.predefined.12": "Papegøyefisk", "entity.minecraft.tropical_fish.predefined.13": "Dronningskalar", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "Raud snapper", "entity.minecraft.tropical_fish.predefined.17": "Polynemidae", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Avtrekkjarfisk", "entity.minecraft.tropical_fish.predefined.2": "Blå kirurg", "entity.minecraft.tropical_fish.predefined.20": "Gulhala papegøyefisk", "entity.minecraft.tropical_fish.predefined.21": "Gul kirurg", "entity.minecraft.tropical_fish.predefined.3": "Skjelfinnefisk", "entity.minecraft.tropical_fish.predefined.4": "Ciklid", "entity.minecraft.tropical_fish.predefined.5": "Klovnefisk", "entity.minecraft.tropical_fish.predefined.6": "Blårosa kampfisk", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "Keisarsnapper", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "Kampfisk", "entity.minecraft.tropical_fish.type.blockfish": "Blokkfisk", "entity.minecraft.tropical_fish.type.brinely": "Saltfisk", "entity.minecraft.tropical_fish.type.clayfish": "Leirfisk", "entity.minecraft.tropical_fish.type.dasher": "Strekfisk", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitterfisk", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snokefisk", "entity.minecraft.tropical_fish.type.spotty": "Flekkefisk", "entity.minecraft.tropical_fish.type.stripey": "Striping", "entity.minecraft.tropical_fish.type.sunstreak": "Solstripefisk", "entity.minecraft.turtle": "Skjelpadde", "entity.minecraft.vex": "Plageånd", "entity.minecraft.villager": "Bygdebu", "entity.minecraft.villager.armorer": "Rustningssmed", "entity.minecraft.villager.butcher": "Slaktar", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON>st", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "L<PERSON>rve<PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotekar", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Bygdetulling", "entity.minecraft.villager.none": "Bygdebu", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Ambo<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Vord", "entity.minecraft.wind_charge": "Vindlading", "entity.minecraft.witch": "Trollkjerring", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "With<PERSON>bein<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Varg", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "dauding", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Bygded<PERSON>ing", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Kunne ikkje framkalla eining av slaget %s", "event.minecraft.raid": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "<PERSON><PERSON>", "event.minecraft.raid.defeat.full": "<PERSON><PERSON><PERSON> - us<PERSON>", "event.minecraft.raid.raiders_remaining": "Attverande her<PERSON>: %s", "event.minecraft.raid.victory": "Si<PERSON>", "event.minecraft.raid.victory.full": "Herjing - siger", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over jung<PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over <PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.id": "Id #%s", "filled_map.level": "(Steg %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON>", "filled_map.mansion": "Skoggranskingskart", "filled_map.monument": "Sjøgransking<PERSON><PERSON>", "filled_map.scale": "Skalering på 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over <PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.unknown": "Ukjent kart", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_snowy": "Sn<PERSON><PERSON>g<PERSON><PERSON>", "filled_map.village_taiga": "Barskogbygdekart", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Klassisk jamn", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Oververda", "flat_world_preset.minecraft.redstone_ready": "<PERSON><PERSON><PERSON> for redstone", "flat_world_preset.minecraft.snowy_kingdom": "Snøriket", "flat_world_preset.minecraft.the_void": "Tomrommet", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.water_world": "Vassverd", "flat_world_preset.unknown": "???", "gameMode.adventure": "utforskarmodus", "gameMode.changed": "Spelmodusen din er vorten oppdatert til %s", "gameMode.creative": "s<PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "Hardhaus-modus!", "gameMode.spectator": "tilskodarmodus", "gameMode.survival": "overlevingsmodus", "gamerule.allowFireTicksAwayFromPlayer": "Tikk ild langt vekk fra spillere", "gamerule.allowFireTicksAwayFromPlayer.description": "Bestemmer om ild og lava skal kunne tikke lenger vekk enn 8 verdensstykker fra en spiller", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON><PERSON> bragder", "gamerule.blockExplosionDropDecay": "I sprengnader frå blokksamhandling slepper nok<PERSON> blokker ikkje alltid utbyte", "gamerule.blockExplosionDropDecay.description": "Noko av utbytet frå blokker øydelagde av sprengnader valda av blokkpåverknad kverv i sprengnaden.", "gamerule.category.chat": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON>", "gamerule.category.mobs": "Skapningar", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "Verdsoppdateringar", "gamerule.commandBlockOutput": "Kringkast utgjæv frå kommandoblokkar", "gamerule.commandModificationBlockLimit": "Blokkgrense for kommando-omgjerder", "gamerule.commandModificationBlockLimit.description": "Mengd av blokker som vert omgjorde samstundes av <PERSON>in kommando, t.d. fill eller clone.", "gamerule.disableElytraMovementCheck": "Slå av vengerørslesjekk", "gamerule.disablePlayerMovementCheck": "Slå av rørslesjekk for spelarar", "gamerule.disableRaids": "Slå av herjingar", "gamerule.doDaylightCycle": "Set tida fram", "gamerule.doEntityDrops": "Slepp utstyr frå e<PERSON>", "gamerule.doEntityDrops.description": "Kontrollerer slepp frå gru<PERSON> (inkludert inventar), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, båtar, osb.", "gamerule.doFireTick": "Oppdatering av eld", "gamerule.doImmediateRespawn": "Stå opp att på augneblinken", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doLimitedCrafting": "K<PERSON>v oppskrifter til emning", "gamerule.doLimitedCrafting.description": "Om påslege kan spelarar berre emna med opplåste oppskrifter.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON> gode frå skapning", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> til<PERSON> frå ska<PERSON>, som òg gjeld for galdrepoeng.", "gamerule.doMobSpawning": "Framkall s<PERSON>ar", "gamerule.doMobSpawning.description": "Somme einingar kan ha særskilde reglar.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON> blokker", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> frå blokker, som òg gjeld for galdrepoeng.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON><PERSON> farande kjø<PERSON>n", "gamerule.doVinesSpread": "Klivevokster breier seg", "gamerule.doVinesSpread.description": "Avgjer om klivevokstrar breier seg tilfeldig til granneblokker. Påverkar ikkje andre slag vokstrar som gråtande eller krokande klivevokstrar, osb.", "gamerule.doWardenSpawning": "Framkall vordar", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON> vê<PERSON>", "gamerule.drowningDamage": "Tak skade frå drukning", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON> <PERSON> kverv når ein døyr", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON> end<PERSON><PERSON><PERSON> kasta av ein spelar kverv når spelaren døyr.", "gamerule.entitiesWithPassengersCanUsePortals": "Enheter med passasjerer kan bruke portaler", "gamerule.entitiesWithPassengersCanUsePortals.description": "Tillat enheter med passasjerer å teleportere gjennom netherportaler, endeportaler, og endporter.", "gamerule.fallDamage": "Tak skade frå fall", "gamerule.fireDamage": "Tak skade frå eld", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON> daude spel<PERSON>r", "gamerule.forgiveDeadPlayers.description": "<PERSON>rge nø<PERSON>rale skapningar stansar å vera arge når spelaren dei er etter døyr i nærleiken.", "gamerule.freezeDamage": "Tak skade frå frost", "gamerule.globalSoundEvents": "<PERSON><PERSON>", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>, t.d. at ein boss vert skapa, er ljoden høyrt over alt.", "gamerule.keepInventory": "Haldt på inventar etter dauden", "gamerule.lavaSourceConversion": "Lava vert til lavak<PERSON>lde", "gamerule.lavaSourceConversion.description": "Når rennande lava er omringa av lavakjelder på to sider vert det òg ei lavakjelde.", "gamerule.locatorBar": "Aktiver spillerlokaliseringslinje", "gamerule.locatorBar.description": "Når aktivert vil en linje vises på skjermen for å indikere retninger spillere befinner seg i.", "gamerule.logAdminCommands": "Kringkast administrative kommandoar", "gamerule.maxCommandChainLength": "<PERSON><PERSON><PERSON> for lengda til kjeder av kommandoblokker", "gamerule.maxCommandChainLength.description": "<PERSON><PERSON>ld kjeder av kommandoblokker og verksemder.", "gamerule.maxCommandForkCount": "<PERSON><PERSON><PERSON> for kommando-samanheng", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON> antal kontekstar som kan brukast av kommandar som 'execute as'.", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON> for stappa einingar", "gamerule.minecartMaxSpeed": "Maksfart for gruvevogner", "gamerule.minecartMaxSpeed.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> snøggleik for gruvevogner i rørsle på land", "gamerule.mobExplosionDropDecay": "<PERSON> skapnings-sp<PERSON><PERSON>der slepper nokre blokker ikkje alltid utbyte", "gamerule.mobExplosionDropDecay.description": "Noko av utbytet frå blokker øydelagde av sprengnader valda av skapningar kverv i sprengnaden.", "gamerule.mobGriefing": "Tillat øydelegging frå skapningar", "gamerule.naturalRegeneration": "Få att liv", "gamerule.playersNetherPortalCreativeDelay": "Spelarens forseinking i netherportalar i kreativ modus", "gamerule.playersNetherPortalCreativeDelay.description": "Tid (i tikk) som ein spelar i kreativ modus må stå i ein netherportal før spelaren skiftar dimensjon.", "gamerule.playersNetherPortalDefaultDelay": "Spelarens forseinking i netherportalar utanom kreativ modus", "gamerule.playersNetherPortalDefaultDelay.description": "Tid (i tikk) ein spelar som ikkje er i kreativ modus må stå i ein netherportal før spelaren skiftar dimensjon.", "gamerule.playersSleepingPercentage": "Svevnprosent", "gamerule.playersSleepingPercentage.description": "Prosenten av spelarar som må sova for å hoppa over natta.", "gamerule.projectilesCanBreakBlocks": "Prosjektil kan øydeleggja blokkar", "gamerule.projectilesCanBreakBlocks.description": "Kontrollerar om prosjektilar vil øydeleggja blokkar som kan øydeleggjast av dei.", "gamerule.randomTickSpeed": "Fartsrate for tilfeldege tikk", "gamerule.reducedDebugInfo": "Reduser feilsøkingsinformasjon", "gamerule.reducedDebugInfo.description": "Grensar av innehaldet på feilsøkjingsskjermen.", "gamerule.sendCommandFeedback": "Vis utdata for kommandoen", "gamerule.showDeathMessages": "<PERSON><PERSON>", "gamerule.snowAccumulationHeight": "Høgste mengd snølag på marka", "gamerule.snowAccumulationHeight.description": "Den høgste mengda snølag som legg seg på marka når det snør.", "gamerule.spawnChunkRadius": "Startpunktstykkeradius", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON> stykker som forblir lastet inn rundt Oververdenens startpunkt.", "gamerule.spawnRadius": "Radius for oppstodestad", "gamerule.spawnRadius.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tø<PERSON> til området rundt startpunktet som spillere kan starte i.", "gamerule.spectatorsGenerateChunks": "Lat tilskodarar skapa landskap", "gamerule.tntExplodes": "La TNT antennes og eksplodere", "gamerule.tntExplosionDropDecay": "I TNT-sprengnader slepper nokre blokker ikkje alltid utbyte", "gamerule.tntExplosionDropDecay.description": "Noko av utbytetNoko av utbytet frå blokker øydelagde av sprengnader valda av TNT kverv i sprengnaden.", "gamerule.universalAnger": "Ålmenn argskap", "gamerule.universalAnger.description": "<PERSON>rge nø<PERSON>e skapningar går til åtak på alle spelarar i nærleiken, ikk<PERSON> berre spelaren som egde dei opp. <PERSON><PERSON><PERSON> best om forgiveDeadPlayers er slege av.", "gamerule.waterSourceConversion": "Lat vatn skapa kjeldeblokk", "gamerule.waterSourceConversion.description": "Når rennande vatn er omringa av vasskjelder på to sider vert det òg ei vasskjelde.", "generator.custom": "Tilmåta", "generator.customized": "<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "FORSTERKA", "generator.minecraft.amplified.info": "Merknad: <PERSON><PERSON> for moro skuld, krev ei kraftig datamaskin.", "generator.minecraft.debug_all_block_states": "Avlusarmodus", "generator.minecraft.flat": "<PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON> lende", "generator.minecraft.normal": "<PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Einskilt lende", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "Ved å sende inn denne rapporten bekrefter du at informasjonen du har oppgitt er riktig og fullstendig etter din beste evne.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Å dela detaljar hjelper oss med å gjera ei velgrunna avgjerd.", "gui.abuseReport.discard.content": "Om du fer misser du denne rapporten og kommentarane dine.\nEr du trygg på at du vil fara?", "gui.abuseReport.discard.discard": "Gå ut og kast rapporten", "gui.abuseReport.discard.draft": "Lagra som utkast", "gui.abuseReport.discard.return": "Haldt fram med å skriva", "gui.abuseReport.discard.title": "Kasta rapporten og kommentarar?", "gui.abuseReport.draft.content": "Vil du halda fram med å skriva på den gjeldande rapporten eller kasta honom og skriva ein ny?", "gui.abuseReport.draft.discard": "<PERSON><PERSON> bort", "gui.abuseReport.draft.edit": "Haldt fram med å skriva", "gui.abuseReport.draft.quittotitle.content": "Vil du halda fram med å skriva eller kasta honom?", "gui.abuseReport.draft.quittotitle.title": "Du har eit utkast som kverv om du fer", "gui.abuseReport.draft.title": "Skriva meir på utkastet?", "gui.abuseReport.error.title": "Kunne ikkje senda rapporten din", "gui.abuseReport.message": "<PERSON><PERSON> såg du den vonde framferda?\nDetta hjelper oss med å undersøkja saka di.", "gui.abuseReport.more_comments": "Skildra kva som hende:", "gui.abuseReport.name.comment_box_label": "Vennligst forklar hvorfor du vil rapportere dette navnet:", "gui.abuseReport.name.reporting": "Du rapporterer «%s».", "gui.abuseReport.name.title": "Rapporter spelarnamn", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON><PERSON> rapporterer du dette?", "gui.abuseReport.read_info": "<PERSON><PERSON>r meir om rapportering", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkotika eller alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Nokon oppmuntrar andre til å vera med på ulovleg narkotikaverksemd eller oppmuntrar mindreårige til å drikka alkohol.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON> ut<PERSON><PERSON> eller overgrep mot born", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> talar om, <PERSON><PERSON>, eller på anna vis fremjar usømeleg framferd som involverer born.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Ærekrenkjing", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Nokon skader omdømet ditt eller omdømet åt ein annan, lyg om å vera ein annan, eller breier usanningar med føremålet å villeia andre.", "gui.abuseReport.reason.description": "Utgreiing:", "gui.abuseReport.reason.false_reporting": "Misbruk av rapporteringsfunksjonen", "gui.abuseReport.reason.generic": "Eg vil rapportera honom/henne", "gui.abuseReport.reason.generic.description": "<PERSON>/ho plagar meg / har gjort noko eg ikkje likar.", "gui.abuseReport.reason.harassment_or_bullying": "T<PERSON><PERSON><PERSON> eller mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> eller mobbar deg eller andre. <PERSON><PERSON> gjeld òg om nokon gong på gong freistar å kontakta deg eller andre utan samtykke eller legg ut private opplysingar om deg eller andre utan samtykke («doxing»).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Nokon åtek deg eller ein annan spelar på grunn av religion, etnisitet, seksuell legning m.m.", "gui.abuseReport.reason.imminent_harm": "Trugar med å skada andre", "gui.abuseReport.reason.imminent_harm.description": "Nokon trugar med å skada deg eller ein annan i røynda.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intime bilete utan sam<PERSON>ke", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> talar om, <PERSON><PERSON>, eller på anna vis fremjar private og intime bilete.", "gui.abuseReport.reason.self_harm_or_suicide": "Sjølvskading eller sjølvmord", "gui.abuseReport.reason.self_harm_or_suicide.description": "Nokon trugar med å skada seg sjølv i røynda, eller talar om å skada seg sjølv i røynda.", "gui.abuseReport.reason.sexually_inappropriate": "Seksuelt upassande", "gui.abuseReport.reason.sexually_inappropriate.description": "Skall som avbilder seksuelle handlinger, kjønnsorganer og seksuell vold.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme eller valdleg ekstremisme", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Nokon talar om, <PERSON><PERSON><PERSON>, eller trugar med å utføra terrorisme eller valdleg ekstremisme av politiske, religiøse, ideologiske eller andre grun<PERSON>.", "gui.abuseReport.reason.title": "Vel rapportkategori", "gui.abuseReport.report_sent_msg": "Me har motteke rapporten din. Takk!\n\nLaget vårt ser på honom so fort som råd.", "gui.abuseReport.select_reason": "Vel rapportkategori", "gui.abuseReport.send": "Send rapport", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON> kommentaren stuttare", "gui.abuseReport.send.error_message": "Ein feil oppstod då du freista senda rapporten din:\n«%s»", "gui.abuseReport.send.generic_error": "<PERSON><PERSON><PERSON> på ein uventa feil medan du freista senda rapporten din.", "gui.abuseReport.send.http_error": "Ein uventa HTTP-feil oppstod då du sende rapporten din.", "gui.abuseReport.send.json_error": "Fann skadd nyttelast då du skulde senda rapporten din.", "gui.abuseReport.send.no_reason": "<PERSON>el ein rapportkategori", "gui.abuseReport.send.not_attested": "Vennligst les teksten over og kryss av boksen for å kunne sende rapporten", "gui.abuseReport.send.service_unavailable": "<PERSON><PERSON> ikkje nå tenesta for misbruksrapportar. Sjå om du er kopla til internett og freista å nyo.", "gui.abuseReport.sending.title": "Sender rapporten din...", "gui.abuseReport.sent.title": "Rap<PERSON><PERSON> vart sendt", "gui.abuseReport.skin.title": "Rapporter spelarham", "gui.abuseReport.title": "Rapporter spelar", "gui.abuseReport.type.chat": "Meldingar i nettprat", "gui.abuseReport.type.name": "Spelarnamn", "gui.abuseReport.type.skin": "Spelarham", "gui.acknowledge": "Stadfest", "gui.advancements": "<PERSON><PERSON><PERSON>", "gui.all": "Alle", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON> meir med den fylgjande lenkja: %s", "gui.banned.description.permanent": "<PERSON><PERSON><PERSON> din er permanent bannlyst, som tyder at du ikkje kan spela på nett eller vera med på Realmar.", "gui.banned.description.reason": "Me har Me har nyleg motteke ein rapport for d<PERSON>leg framferd av kontoen din. Moderatorane våre har no sett igjennom saka og identifisert det som %s, som bryt med samfunnsstandardane til Minecraft.", "gui.banned.description.reason_id": "Kode: %s", "gui.banned.description.reason_id_message": "Kode: %s - %s", "gui.banned.description.temporary": "%s Fram til då kan du ikkje spela på nett eller vera med på Realmar.", "gui.banned.description.temporary.duration": "Konto<PERSON> din er mellombels utestengd og får tilgjenge att om %s.", "gui.banned.description.unknownreason": "Me har nyleg motteke ein rapport for d<PERSON>leg framferd av kontoen din. Moderatorane våre har no sett igjennom saka og kome fram til at det bryt med samfunnsstandardane til Minecraft.", "gui.banned.name.description": "Det gjeldande namnet ditt - «%s» - strider imot samfunnsstandardane våre. Du kan spela på e<PERSON>pelar, men må gjera om på namnet ditt om du vil spela på nett.\n\n<PERSON><PERSON><PERSON> meir eller send saka til vurdering med den fylgjande lenkja: %s", "gui.banned.name.title": "Namnet er ikkje tillate i fleirspelar", "gui.banned.reason.defamation_impersonation_false_information": "Villeiing eller utnytting av andre ved å dela feilopplysingar, eller gje seg ut for å vera ein annan", "gui.banned.reason.drugs": "Tilvising til ulovlege rusmiddel", "gui.banned.reason.extreme_violence_or_gore": "Skildring av grovt vald eller gørr i røynda", "gui.banned.reason.false_reporting": "<PERSON>or mengd falske eller galne rapportar", "gui.banned.reason.fraud": "<PERSON><PERSON>rleg tileigning eller bruk av innehald", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON> samfu<PERSON>tandardan<PERSON>", "gui.banned.reason.harassment_or_bullying": "Krenkjande ordbruk i ein utpeikande og sårande måte", "gui.banned.reason.hate_speech": "Hatefulle ytringar eller mannemon/diskriminering", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON>iser til hatgrupper, <PERSON><PERSON><PERSON><PERSON>, eller <PERSON>lm<PERSON> personar", "gui.banned.reason.imminent_harm_to_person_or_property": "Har som føremål å skada folk eller eigedom i røynda", "gui.banned.reason.nudity_or_pornography": "Viser usømeleg eller pornografiskt materiale", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON> emne eller innehald", "gui.banned.reason.spam_or_advertising": "Bospost eller reklame", "gui.banned.skin.description": "Den gjeldande hamen din strider imot samfunnsstandardane våre. Du kan enno spela med ein standardham, eller velja ein ny.\n\n<PERSON><PERSON>r meir eller send saka til vurdering med den fylgjande lenkja: %s", "gui.banned.skin.title": "<PERSON>en er ikkje tillaten", "gui.banned.title.permanent": "<PERSON><PERSON><PERSON> er <PERSON> utestengd", "gui.banned.title.temporary": "Ko<PERSON><PERSON> er mellombels utestengd", "gui.cancel": "Bryt av", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Å dela detaljar hjelper oss med å ta ei velgrunna avgjerd.", "gui.chatReport.discard.content": "Om du fer misser du denne rapporten og kommentarane dine.\nEr du trygg på at du vil fara?", "gui.chatReport.discard.discard": "Forlat og kasta rapport", "gui.chatReport.discard.draft": "Lagra som utkast", "gui.chatReport.discard.return": "Haldt fram med å skriva", "gui.chatReport.discard.title": "Kasta rapport og kommentarar?", "gui.chatReport.draft.content": "Vil du halda fram med å skriva den gjeldande rapporten, eller kasta honom og skriva ein ny?", "gui.chatReport.draft.discard": "<PERSON><PERSON>", "gui.chatReport.draft.edit": "Haldt fram med å skriva", "gui.chatReport.draft.quittotitle.content": "Vil du halda fram med å skriva, eller kasta honom?", "gui.chatReport.draft.quittotitle.title": "Du har eit utkast av ein nettpratsrapport som kverv om du fer", "gui.chatReport.draft.title": "Gjera om på utkast av nettpratsrapport?", "gui.chatReport.more_comments": "Grei ut om kva som hende:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON><PERSON> rapporterer du dette?", "gui.chatReport.read_info": "<PERSON><PERSON><PERSON> om rapportering", "gui.chatReport.report_sent_msg": "Me har motteke rapporten din. Takk!\n\nArbeidsgruppa vår skal sjå på honom so snart som råd.", "gui.chatReport.select_chat": "Vel nettpratsmeldingar å rapportera", "gui.chatReport.select_reason": "Vel rapportkategori", "gui.chatReport.selected_chat": "%s nettpratsmelding(ar) valde til rapportering", "gui.chatReport.send": "Send rapport", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> kommentaren stuttare", "gui.chatReport.send.no_reason": "<PERSON>el ein rapportkategori", "gui.chatReport.send.no_reported_messages": "Vel minst éi nettpratsmelding å rapportera", "gui.chatReport.send.too_many_messages": "For mange meldingar er tekne med i rapporten", "gui.chatReport.title": "Rapportér spelar i nettpraten", "gui.chatSelection.context": "Meldingar før og etter dei merkte kjem med i rapporten for betre innsyn i samanhengen", "gui.chatSelection.fold": "%s meldingar gøymde", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s vart med i nettpraten", "gui.chatSelection.message.narrate": "%s skreiv: %s kl. %s", "gui.chatSelection.selected": "%s/%s melding(ar) vald(e)", "gui.chatSelection.title": "Vel meldingar i nettpraten å rapportera", "gui.continue": "Haldt fram", "gui.copy_link_to_clipboard": "<PERSON><PERSON><PERSON><PERSON> til utklippstavla", "gui.days": "%s dag(ar)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON>", "gui.entity_tooltip.type": "Slag: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Avviste %s filer", "gui.fileDropFailure.title": "Lyktes ikke med å legge til filer", "gui.hours": "%s time(-ar)", "gui.loadingMinecraft": "Lastar Minecraft", "gui.minutes": "%s minutt", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s-knapp", "gui.narrate.editBox": "Brigdefelt %s: %s", "gui.narrate.slider": "%s-glidebrytar", "gui.narrate.tab": "%s-fane", "gui.no": "<PERSON><PERSON>", "gui.none": "Ingen", "gui.ok": "<PERSON><PERSON><PERSON>", "gui.open_report_dir": "Opne rapport<PERSON>smappe", "gui.proceed": "Haldt fram", "gui.recipebook.moreRecipes": "Høgreklikk for meir", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Søk...", "gui.recipebook.toggleRecipes.all": "Viser alle", "gui.recipebook.toggleRecipes.blastable": "Viser smeltelege", "gui.recipebook.toggleRecipes.craftable": "Viser ting du kan emna", "gui.recipebook.toggleRecipes.smeltable": "Viser smeltelege", "gui.recipebook.toggleRecipes.smokable": "Viser røykjelege", "gui.report_to_server": "Rapporter til tenar", "gui.socialInteractions.blocking_hint": "Handsama med Microsoft-konto", "gui.socialInteractions.empty_blocked": "Ingen blokkerte spelarar i nettpraten", "gui.socialInteractions.empty_hidden": "Ingen spelarar i løynd nettprat", "gui.socialInteractions.hidden_in_chat": "Meldingar i nettpraten frå %s vert løynde", "gui.socialInteractions.hide": "G<PERSON><PERSON> i nettprat", "gui.socialInteractions.narration.hide": "<PERSON><PERSON><PERSON> me<PERSON>ar frå %s", "gui.socialInteractions.narration.report": "Rapportér spelaren %s", "gui.socialInteractions.narration.show": "Vis meldingar frå %s", "gui.socialInteractions.report": "Rapportér", "gui.socialInteractions.search_empty": "<PERSON><PERSON> ikkje finna spelarar med dette namnet", "gui.socialInteractions.search_hint": "Søk...", "gui.socialInteractions.server_label.multiple": "%s - %s spelarar", "gui.socialInteractions.server_label.single": "%s - %s spelar", "gui.socialInteractions.show": "<PERSON>is i nettprat", "gui.socialInteractions.shown_in_chat": "Meldingar i nettpraten frå %s vert viste", "gui.socialInteractions.status_blocked": "Blokkert", "gui.socialInteractions.status_blocked_offline": "Blokkert - Fråkopla", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Løynt - Fråkopla", "gui.socialInteractions.status_offline": "Fråkopla", "gui.socialInteractions.tab_all": "Alt", "gui.socialInteractions.tab_blocked": "Blokkert", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Samkvem", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "Rapport<PERSON><PERSON> spelar", "gui.socialInteractions.tooltip.report.disabled": "Rapporteringstenesta er utilgjengeleg", "gui.socialInteractions.tooltip.report.no_messages": "Ingen rapporterelege meldingar frå spelaren %s", "gui.socialInteractions.tooltip.report.not_reportable": "Denne spelaren kan ikkje verta rapportert, av di meldingane deira i nettpraten ikkje kan verta stadfeste av på denne tenaren", "gui.socialInteractions.tooltip.show": "Vis meldingar", "gui.stats": "Statistikk", "gui.toMenu": "Attende til tenarlista", "gui.toRealms": "Attende til Realms-lista", "gui.toTitle": "Attende til hovudmenyen", "gui.toWorld": "Attende til verdslista", "gui.togglable_slot": "Klikk for å slå av bås", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Tilbake (%ss)", "gui.waitingForResponse.title": "Venter på server", "gui.yes": "<PERSON>a", "hanging_sign.edit": "<PERSON><PERSON> meldinga på hengande skilt", "instrument.minecraft.admire_goat_horn": "Ovundring", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Undring", "instrument.minecraft.seek_goat_horn": "Leit", "instrument.minecraft.sing_goat_horn": "Song", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON>", "inventory.binSlot": "Øydelegg gjenstand", "inventory.hotbarInfo": "Lagra verktyline med %1$s+%2$s", "inventory.hotbarSaved": "Gjenstandsverkt<PERSON>ine lagra (henta opp att med %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON>:", "item.canPlace": "<PERSON>n set<PERSON>t ned på:", "item.canUse.unknown": "Ukjend", "item.color": "Let: %s", "item.components": "%s komponent(ar)", "item.disabled": "Avslegen gjenstand", "item.durability": "Varing: %s / %s", "item.dyed": "<PERSON>a", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "Akasiebåt med kiste", "item.minecraft.allay_spawn_egg": "Framkalleegg for hjelpeånd", "item.minecraft.amethyst_shard": "Ametystbrot", "item.minecraft.angler_pottery_shard": "Skålbrot med fiskestong", "item.minecraft.angler_pottery_sherd": "Skålbrot med fiskestong", "item.minecraft.apple": "Eple", "item.minecraft.archer_pottery_shard": "Skålbrot med bogeskyttar", "item.minecraft.archer_pottery_sherd": "Skålbrot med bogeskyttar", "item.minecraft.armadillo_scute": "Beltedyrskjel", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for beltedyr", "item.minecraft.armor_stand": "Rustningstativ", "item.minecraft.arms_up_pottery_shard": "Skålbrot med armane opp", "item.minecraft.arms_up_pottery_sherd": "Skålbrot med armane opp", "item.minecraft.arrow": "<PERSON>l", "item.minecraft.axolotl_bucket": "<PERSON>tte med axelotl", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for axelotl", "item.minecraft.baked_potato": "Bakt potet", "item.minecraft.bamboo_chest_raft": "Bambusflòte med kiste", "item.minecraft.bamboo_raft": "Bambusflòte", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for skinnvengje", "item.minecraft.bee_spawn_egg": "Framkalleegg for bie", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Raudbetefrø", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_chest_boat": "Bjørkebåt med kiste", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON> let", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> seletø<PERSON>", "item.minecraft.blade_pottery_shard": "Skålbrot med sverd", "item.minecraft.blade_pottery_sherd": "Skålbrot med sverd", "item.minecraft.blaze_powder": "Eldpulver", "item.minecraft.blaze_rod": "Eldstav", "item.minecraft.blaze_spawn_egg": "Framkalleegg for eldskrømt", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.blue_dye": "Blå let", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>tt egg", "item.minecraft.blue_harness": "B<PERSON><PERSON>tt seletøy", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>egg for myrd<PERSON>ug", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Lynaktig rustningspryd", "item.minecraft.bone": "Bein", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "Bok", "item.minecraft.bordure_indented_banner_pattern": "Bord med tannsnitt-bannermønster", "item.minecraft.bow": "Boge", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "Vindstav", "item.minecraft.breeze_spawn_egg": "Framkalleegg for vindskrømt", "item.minecraft.brewer_pottery_shard": "Skålbrot med brygg", "item.minecraft.brewer_pottery_sherd": "Skålbrot med brygg", "item.minecraft.brewing_stand": "Bryggjereiskap", "item.minecraft.brick": "Tegl", "item.minecraft.brown_bundle": "Brun bunt", "item.minecraft.brown_dye": "<PERSON><PERSON> let", "item.minecraft.brown_egg": "Brunt egg", "item.minecraft.brown_harness": "<PERSON><PERSON>le", "item.minecraft.brush": "<PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "Sekk", "item.minecraft.bundle.empty": "<PERSON>", "item.minecraft.bundle.empty.description": "Kan romme en blandet hop av gjenstander", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Skålbrot med eldtunge", "item.minecraft.burn_pottery_sherd": "Skålbrot med eldtunge", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for dromedar", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> på stong", "item.minecraft.cat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for katt", "item.minecraft.cauldron": "Gryte", "item.minecraft.cave_spider_spawn_egg": "Fram<PERSON>leegg for hòlevevkjerring", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "Ringbrynje", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "Brynje<PERSON><PERSON>", "item.minecraft.charcoal": "Trekol", "item.minecraft.cherry_boat": "Kissebærb<PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "Kissebærbåt med kiste", "item.minecraft.chest_minecart": "Gruvevogn med kiste", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Framkalleegg for høne", "item.minecraft.chorus_fruit": "Korfrukt", "item.minecraft.clay_ball": "Leireball", "item.minecraft.clock": "K<PERSON>kke", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Smideskant", "item.minecraft.coast_armor_trim_smithing_template.new": "Forlist rustning<PERSON>ryd", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON>rsk", "item.minecraft.cod_bucket": "Torskebytte", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for torsk", "item.minecraft.command_block_minecart": "Gruvevogn med kommandoblokk", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Oksesteik", "item.minecraft.cooked_chicken": "Steikt høne", "item.minecraft.cooked_cod": "Kokt to<PERSON>", "item.minecraft.cooked_mutton": "Steikt <PERSON>", "item.minecraft.cooked_porkchop": "Svinesteik", "item.minecraft.cooked_rabbit": "Stei<PERSON> hare", "item.minecraft.cooked_salmon": "Kokt laks", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Koparbarre", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for ku", "item.minecraft.creaking_spawn_egg": "Framkalleegg for knirkande", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-bannermønster", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for creeper", "item.minecraft.crossbow": "Låsboge", "item.minecraft.crossbow.projectile": "Skot:", "item.minecraft.crossbow.projectile.multiple": "Prosjektil: %s × %s", "item.minecraft.crossbow.projectile.single": "Prosjektil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON> bunt", "item.minecraft.cyan_dye": "Blågrønlet", "item.minecraft.cyan_harness": "Turkist seletøy", "item.minecraft.danger_pottery_shard": "Sk<PERSON><PERSON>brot med creeper", "item.minecraft.danger_pottery_sherd": "Sk<PERSON><PERSON>brot med creeper", "item.minecraft.dark_oak_boat": "Mørkeikeb<PERSON>t", "item.minecraft.dark_oak_chest_boat": "Mørkeikebåt med kiste", "item.minecraft.debug_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.debug_stick.empty": "%s har ingen eigenskapar", "item.minecraft.debug_stick.select": "valde «%s» (%s)", "item.minecraft.debug_stick.update": "«%s» til %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "Brystplate av diamant", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Diamantgrev", "item.minecraft.diamond_horse_armor": "Diaman<PERSON>ustning for hest", "item.minecraft.diamond_leggings": "Beinplater av diamant", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Diamantsverd", "item.minecraft.disc_fragment_5": "Musikkplateskòr", "item.minecraft.disc_fragment_5.desc": "Musikkplate – 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for delfin", "item.minecraft.donkey_spawn_egg": "Framkalleegg for asen", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "Turka tare", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for draug", "item.minecraft.dune_armor_trim_smithing_template": "Smideskant", "item.minecraft.dune_armor_trim_smithing_template.new": "Dynens rustningspryd", "item.minecraft.echo_shard": "Atterljomsbrot", "item.minecraft.egg": "Egg", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for eldre verjar", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Galdrebok", "item.minecraft.enchanted_golden_apple": "Trollbunde gulleple", "item.minecraft.end_crystal": "Endekrystall", "item.minecraft.ender_dragon_spawn_egg": "Fram<PERSON>lingsegg for enderdrake", "item.minecraft.ender_eye": "Enderauga", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for endermann", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for endermit", "item.minecraft.evoker_spawn_egg": "Framkalleegg for åndemanar", "item.minecraft.experience_bottle": "Galdreflaske", "item.minecraft.explorer_pottery_shard": "Skålbrot med kart", "item.minecraft.explorer_pottery_sherd": "Skålbrot med kart", "item.minecraft.eye_armor_trim_smithing_template": "Smideskant", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> rustningspryd", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.filled_map": "Kart", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Fyrverk", "item.minecraft.firework_rocket.flight": "Flygelengd:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Fyrverkstjerne", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blå", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Tilmåta", "item.minecraft.firework_star.cyan": "Blågrøn", "item.minecraft.firework_star.fade_to": "Ton inn til", "item.minecraft.firework_star.flicker": "Gnistring", "item.minecraft.firework_star.gray": "Grå", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Ljosblå", "item.minecraft.firework_star.light_gray": "Ljosgrå", "item.minecraft.firework_star.lime": "Limegrøn", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Oransje", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "Ukjend form", "item.minecraft.firework_star.shape.burst": "U<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "Creeperforma", "item.minecraft.firework_star.shape.large_ball": "Stor kule", "item.minecraft.firework_star.shape.small_ball": "Lita kule", "item.minecraft.firework_star.shape.star": "Stjerne-forma", "item.minecraft.firework_star.trail": "Spor", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Gul", "item.minecraft.fishing_rod": "Fiskestong", "item.minecraft.flint": "Flint", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Virvlende rustningspryd", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON>vel<PERSON><PERSON><PERSON><PERSON><PERSON>er", "item.minecraft.flow_pottery_sherd": "Skålbrot med kvervel", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Blomst-bannermønster", "item.minecraft.flower_pot": "Blomepotte", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for rev", "item.minecraft.friend_pottery_shard": "Skålbrot med venlegt andlet", "item.minecraft.friend_pottery_sherd": "Skålbrot med venlegt andlet", "item.minecraft.frog_spawn_egg": "Framkallingsegg for frosk", "item.minecraft.furnace_minecart": "Gruvevogn med omn", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for ghast", "item.minecraft.ghast_tear": "Ghasttåre", "item.minecraft.glass_bottle": "Glasflaske", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Globus-bannermønster", "item.minecraft.glow_berries": "Glødebær", "item.minecraft.glow_ink_sac": "Glødeblekkpose", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON> ramme", "item.minecraft.glow_squid_spawn_egg": "Framkallingsegg for glødesprut", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON>tø<PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Framkalleegg for geit", "item.minecraft.gold_ingot": "Gullbarre", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Brystplate av gull", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Gullhesterustning", "item.minecraft.golden_leggings": "Beinplater av gull", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "Gullspade", "item.minecraft.golden_sword": "Gullsverd", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.green_dye": "Grø<PERSON> let", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for verjar", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "Vindskrømt", "item.minecraft.guster_banner_pattern.new": "Vindkaster-bannermønster", "item.minecraft.guster_pottery_sherd": "Skålbrot med vindskrømt", "item.minecraft.happy_ghast_spawn_egg": "Fremkallingsegg for gladghast", "item.minecraft.harness": "<PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Skålbrot med hjarta", "item.minecraft.heart_pottery_sherd": "Skålbrot med hjarta", "item.minecraft.heartbreak_pottery_shard": "Skålbrot med brote hjarta", "item.minecraft.heartbreak_pottery_sherd": "Skålbrot med brote hjarta", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for hoglin", "item.minecraft.honey_bottle": "Honningflaske", "item.minecraft.honeycomb": "Vokskake", "item.minecraft.hopper_minecart": "Gruvevogn med trekt", "item.minecraft.horse_spawn_egg": "Framkalleegg for hest", "item.minecraft.host_armor_trim_smithing_template": "Smideskant", "item.minecraft.host_armor_trim_smithing_template.new": "Vertens rustningspryd", "item.minecraft.howl_pottery_shard": "Skålbrot med varg", "item.minecraft.howl_pottery_sherd": "Skålbrot med varg", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for skaldauding", "item.minecraft.ink_sac": "Blekkpose", "item.minecraft.iron_axe": "J<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "Brystplate av jarn", "item.minecraft.iron_golem_spawn_egg": "Framkallingsegg for jarnkjempe", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON> for hest", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "Beinplater av jarn", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Jarnsverd", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON>elb<PERSON>t med kiste", "item.minecraft.knowledge_book": "Kunnebok", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Lava<PERSON>tte", "item.minecraft.lead": "Band", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "Skinntrøye", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Lêrhesterustning", "item.minecraft.leather_leggings": "Skinnbrok", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.light_blue_dye": "Ljosblålet", "item.minecraft.light_blue_harness": "Lyseblått seletøy", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.light_gray_dye": "Ljosgrålet", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.lime_dye": "Limegrønlet", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "Dvelande trolldrykk", "item.minecraft.lingering_potion.effect.awkward": "Dveland<PERSON> uh<PERSON>t brygg", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON> ul<PERSON> brygg", "item.minecraft.lingering_potion.effect.fire_resistance": "Dvelande eld<PERSON>brygg", "item.minecraft.lingering_potion.effect.harming": "Dvelande s<PERSON>debrygg", "item.minecraft.lingering_potion.effect.healing": "Dvelande læ<PERSON>gg", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.invisibility": "Dvelande usynleiksbrygg", "item.minecraft.lingering_potion.effect.leaping": "Dvelande by<PERSON><PERSON><PERSON>gg", "item.minecraft.lingering_potion.effect.levitation": "Dvelande svi<PERSON>b<PERSON>gg", "item.minecraft.lingering_potion.effect.luck": "Dveland<PERSON> l<PERSON>", "item.minecraft.lingering_potion.effect.mundane": "Dvelande måteleg brygg", "item.minecraft.lingering_potion.effect.night_vision": "Dvelande nattsynsbrygg", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.poison": "Dvelande giftig brygg", "item.minecraft.lingering_potion.effect.regeneration": "Dvelande attskapingsbrygg", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "item.minecraft.lingering_potion.effect.slowness": "Dvelande tråleiksbrygg", "item.minecraft.lingering_potion.effect.strength": "<PERSON>vel<PERSON><PERSON> s<PERSON>rk<PERSON>gg", "item.minecraft.lingering_potion.effect.swiftness": "Dvelande snøggleiksbrygg", "item.minecraft.lingering_potion.effect.thick": "Dvelande tjuktflytande brygg", "item.minecraft.lingering_potion.effect.turtle_master": "Dvelande brygg frå skjelpaddemeisteren", "item.minecraft.lingering_potion.effect.water": "Dvelande vassflaske", "item.minecraft.lingering_potion.effect.water_breathing": "Dvelande vassandingsb<PERSON>gg", "item.minecraft.lingering_potion.effect.weakness": "Dveland<PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON>veland<PERSON> ve<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "Dvelande vindlad<PERSON>brygg", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for lama", "item.minecraft.lodestone_compass": "Kompass for leidarstein", "item.minecraft.mace": "Stridsklubbe", "item.minecraft.magenta_bundle": "Magenta bunt", "item.minecraft.magenta_dye": "L<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmakrem", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for magmakube", "item.minecraft.mangrove_boat": "Mangrovebåt", "item.minecraft.mangrove_chest_boat": "Mangrovemåt med kiste", "item.minecraft.map": "Tomt kart", "item.minecraft.melon_seeds": "Melonfrø", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "Mjølkebytte", "item.minecraft.minecart": "Gruvevogn", "item.minecraft.miner_pottery_shard": "Skålbrot med hakke", "item.minecraft.miner_pottery_sherd": "Skålbrot med hakke", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "<PERSON>g", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for mooshroom", "item.minecraft.mourner_pottery_shard": "Skålbrot med syrgjande", "item.minecraft.mourner_pottery_sherd": "Skålbrot med vord", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for muldyr", "item.minecraft.mushroom_stew": "Soppgryte", "item.minecraft.music_disc_11": "Musikkplate", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Musikkplate", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Musikkplate", "item.minecraft.music_disc_5.desc": "<PERSON> – 5", "item.minecraft.music_disc_blocks": "Musikkplate", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Musikkplate", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Musikkplate", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Musikkplate", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Musikkplate", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Musikkplate", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Musikkdisk", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Musikkplate", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Musikkplate", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Musikkplate", "item.minecraft.music_disc_otherside.desc": "<PERSON> – <PERSON>ide", "item.minecraft.music_disc_pigstep": "Musikkplate", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Musikkplate", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Musikkplate", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "Musikkplate", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Musikkplate", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Musikkplate", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Musikkplate", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Musikkplate", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Namneskilt", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nether_brick": "Nethertegl", "item.minecraft.nether_star": "Netherstjerne", "item.minecraft.nether_wart": "Nethervorte", "item.minecraft.netherite_axe": "Netherittøks", "item.minecraft.netherite_boots": "Netherittskor", "item.minecraft.netherite_chestplate": "Brystplate av netheritt", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Netherittljå", "item.minecraft.netherite_ingot": "Netherittbarre", "item.minecraft.netherite_leggings": "Beinplater av netheritt", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_scrap": "Netherittskrap", "item.minecraft.netherite_shovel": "Netherittspade", "item.minecraft.netherite_sword": "Netherittsverd", "item.minecraft.netherite_upgrade_smithing_template": "Smideskant", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherittoppgradering", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON>b<PERSON>t med kiste", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for ozelot", "item.minecraft.ominous_bottle": "Illevarslende flaske", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.orange_dye": "Oransjelet", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Målarstykke", "item.minecraft.pale_oak_boat": "Bleikeikebå<PERSON>", "item.minecraft.pale_oak_chest_boat": "Bleikeikebåt med kiste", "item.minecraft.panda_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for pandabjørn", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Fram<PERSON>leegg for papegøye", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for fantom", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for gris", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "Snut", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_brute_spawn_egg": "Framkalleegg for piglinråskinn", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for plyndrar", "item.minecraft.pink_bundle": "<PERSON> bunt", "item.minecraft.pink_dye": "<PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Krukkefrø", "item.minecraft.plenty_pottery_shard": "Skålbrot med kiste", "item.minecraft.plenty_pottery_sherd": "Skålbrot med kiste", "item.minecraft.poisonous_potato": "<PERSON><PERSON> potet", "item.minecraft.polar_bear_spawn_egg": "Framkalleegg for kvitebjørn", "item.minecraft.popped_chorus_fruit": "Sprungen korfrukt", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "Potet", "item.minecraft.potion": "Trolldrykk", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> brygg", "item.minecraft.potion.effect.empty": "<PERSON><PERSON><PERSON> brygg", "item.minecraft.potion.effect.fire_resistance": "Eldvernsbrygg", "item.minecraft.potion.effect.harming": "Ska<PERSON><PERSON><PERSON>gg", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "Usynleiksbrygg", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "Svivebrygg", "item.minecraft.potion.effect.luck": "Lu<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "Måteleg brygg", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "Giftig brygg", "item.minecraft.potion.effect.regeneration": "Attskapingsbrygg", "item.minecraft.potion.effect.slow_falling": "Fjørfallingsbrygg", "item.minecraft.potion.effect.slowness": "Tråleiksbrygg", "item.minecraft.potion.effect.strength": "Styrkjebrygg", "item.minecraft.potion.effect.swiftness": "Snøggleiksbrygg", "item.minecraft.potion.effect.thick": "Tjuktflytande brygg", "item.minecraft.potion.effect.turtle_master": "Brygg frå skjelpaddemeisteren", "item.minecraft.potion.effect.water": "Vassflaske", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "Vindladingsbrygg", "item.minecraft.pottery_shard_archer": "Skålbrot med bogeskyttar", "item.minecraft.pottery_shard_arms_up": "Skålbrot med armane opp", "item.minecraft.pottery_shard_prize": "Skålbrot med glimestein", "item.minecraft.pottery_shard_skull": "Skålbrot med skalle", "item.minecraft.powder_snow_bucket": "<PERSON>tte med nysnø", "item.minecraft.prismarine_crystals": "Prismarinkrystallar", "item.minecraft.prismarine_shard": "Prismarinbrot", "item.minecraft.prize_pottery_shard": "Skålbrot med glimestein", "item.minecraft.prize_pottery_sherd": "Skålbrot med glimestein", "item.minecraft.pufferfish": "Kulefisk", "item.minecraft.pufferfish_bucket": "Kulefiskebytte", "item.minecraft.pufferfish_spawn_egg": "Framkalleegg for kulefisk", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "Graskarfrø", "item.minecraft.purple_bundle": "<PERSON><PERSON> bunt", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON>", "item.minecraft.quartz": "Nether-kvarts", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "Harefell", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for hare", "item.minecraft.rabbit_stew": "Haregryte", "item.minecraft.raiser_armor_trim_smithing_template": "Smideskant", "item.minecraft.raiser_armor_trim_smithing_template.new": "Oppdretterens rustningspryd", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for herjar", "item.minecraft.raw_copper": "Råkopar", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Attskapingskompass", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.red_dye": "<PERSON><PERSON> let", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Redstone-pulver", "item.minecraft.resin_brick": "Kvaetegl", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Smideskant", "item.minecraft.rib_armor_trim_smithing_template.new": "Ribbeinsaktig rustningspryd", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON> k<PERSON>t", "item.minecraft.saddle": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon": "Rå laks", "item.minecraft.salmon_bucket": "Laksebytte", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for laks", "item.minecraft.scrape_pottery_sherd": "Skålbrot med øks", "item.minecraft.scute": "Sk<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Smideskant", "item.minecraft.sentry_armor_trim_smithing_template.new": "P<PERSON><PERSON>t rustningspryd", "item.minecraft.shaper_armor_trim_smithing_template": "Smideskant", "item.minecraft.shaper_armor_trim_smithing_template.new": "Formgiverens rustningspryd", "item.minecraft.sheaf_pottery_shard": "Skålbrot med avling", "item.minecraft.sheaf_pottery_sherd": "Skålbrot med avling", "item.minecraft.shears": "Saks", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for sau", "item.minecraft.shelter_pottery_shard": "Skålbrot med tre", "item.minecraft.shelter_pottery_sherd": "Skålbrot med tre", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.blue": "Blått skjold", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.light_blue": "Ljosblått skjold", "item.minecraft.shield.light_gray": "Ljosgrått skjold", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.orange": "Oransje s<PERSON>jold", "item.minecraft.shield.pink": "<PERSON> s<PERSON>jo<PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON> skjold", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>jold", "item.minecraft.shield.yellow": "<PERSON><PERSON> skjold", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for shulker", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Smideskant", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON>tens rustningspryd", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for sylvkrek", "item.minecraft.skeleton_horse_spawn_egg": "Framkalleegg for hestebeinrangel", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for beinrangel", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Hodeskalle-bannermønster", "item.minecraft.skull_pottery_shard": "Skålbrot med skalle", "item.minecraft.skull_pottery_sherd": "Skålbrot med skalle", "item.minecraft.slime_ball": "Slimball", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for sliming", "item.minecraft.smithing_template": "Smideskant", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON> bruka på:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Legg til barre eller krystall", "item.minecraft.smithing_template.armor_trim.applies_to": "Rustning", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Legg til stykke av rustning", "item.minecraft.smithing_template.armor_trim.ingredients": "Barrar og krystallar", "item.minecraft.smithing_template.ingredients": "Innehald:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Legg til netherittbarre", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantutstyr", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Legg til rustning, våpen eller reidskap av diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherittbarre", "item.minecraft.smithing_template.upgrade": "Betring: ", "item.minecraft.sniffer_spawn_egg": "Framkalleegg for moldnase", "item.minecraft.snort_pottery_shard": "Skålbrot med moldnase", "item.minecraft.snort_pottery_sherd": "Skålbrot med moldnase", "item.minecraft.snout_armor_trim_smithing_template": "Smideskant", "item.minecraft.snout_armor_trim_smithing_template.new": "Snuteaktig rustningspryd", "item.minecraft.snow_golem_spawn_egg": "Framkallingsegg for snømann", "item.minecraft.snowball": "Snøball", "item.minecraft.spectral_arrow": "Merkingspil", "item.minecraft.spider_eye": "<PERSON>ev<PERSON><PERSON><PERSON>auga", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for vevkjerring", "item.minecraft.spire_armor_trim_smithing_template": "Smideskant", "item.minecraft.spire_armor_trim_smithing_template.new": "Spirens rustningspryd", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Kasteleg eldvernsbrygg", "item.minecraft.splash_potion.effect.harming": "Kasteleg skadebrygg", "item.minecraft.splash_potion.effect.healing": "Kasteleg lækjebrygg", "item.minecraft.splash_potion.effect.infested": "Kasteleg fengjebrygg", "item.minecraft.splash_potion.effect.invisibility": "Kasteleg usynleiksbrygg", "item.minecraft.splash_potion.effect.leaping": "Kasteleg byksebrygg", "item.minecraft.splash_potion.effect.levitation": "Kasteleg svivebrygg", "item.minecraft.splash_potion.effect.luck": "Kasteleg lukkebrygg", "item.minecraft.splash_potion.effect.mundane": "Måteleg ka<PERSON>brygg", "item.minecraft.splash_potion.effect.night_vision": "Kasteleg nattsynsbrygg", "item.minecraft.splash_potion.effect.oozing": "Kaste<PERSON>gg", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.regeneration": "Kasteleg attskapingsbrygg", "item.minecraft.splash_potion.effect.slow_falling": "Kasteleg fjørfallingsbrygg", "item.minecraft.splash_potion.effect.slowness": "Kasteleg tråleiksbrygg", "item.minecraft.splash_potion.effect.strength": "Kasteleg styrkjebrygg", "item.minecraft.splash_potion.effect.swiftness": "Kasteleg snøggleiksbrygg", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Kastebrygg frå skjelpaddemeisteren", "item.minecraft.splash_potion.effect.water": "Kastbar vassflaske", "item.minecraft.splash_potion.effect.water_breathing": "Kasteleg vassandingsbrygg", "item.minecraft.splash_potion.effect.weakness": "Kasteleg veikjebrygg", "item.minecraft.splash_potion.effect.weaving": "Kasteleg vevebrygg", "item.minecraft.splash_potion.effect.wind_charged": "Kasteleg vindladingsbrygg", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "Granb<PERSON>t med kiste", "item.minecraft.spyglass": "Kikert", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for blekksprut", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "Steinspade", "item.minecraft.stone_sword": "Steinsverd", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for vandrar", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for stegar", "item.minecraft.string": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "Tvilsam gry<PERSON>ett", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON>b<PERSON><PERSON>", "item.minecraft.tadpole_bucket": "<PERSON>tte med rumpetroll", "item.minecraft.tadpole_spawn_egg": "Framkallingsegg for rumpetroll", "item.minecraft.tide_armor_trim_smithing_template": "Smideskant", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.tipped_arrow": "Dyppa pil", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON>va pil", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON> duppa pil", "item.minecraft.tipped_arrow.effect.fire_resistance": "Eldvernspil", "item.minecraft.tipped_arrow.effect.harming": "Skadepil", "item.minecraft.tipped_arrow.effect.healing": "Lækjepil", "item.minecraft.tipped_arrow.effect.infested": "Fengjepil", "item.minecraft.tipped_arrow.effect.invisibility": "Usynleikspil", "item.minecraft.tipped_arrow.effect.leaping": "Byksepil", "item.minecraft.tipped_arrow.effect.levitation": "Svivepil", "item.minecraft.tipped_arrow.effect.luck": "Lukkepil", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON>va pil", "item.minecraft.tipped_arrow.effect.night_vision": "Nattsynspil", "item.minecraft.tipped_arrow.effect.oozing": "Tytepil", "item.minecraft.tipped_arrow.effect.poison": "Giftpil", "item.minecraft.tipped_arrow.effect.regeneration": "Attskapingspil", "item.minecraft.tipped_arrow.effect.slow_falling": "Fjørfallingspil", "item.minecraft.tipped_arrow.effect.slowness": "Tråleikspil", "item.minecraft.tipped_arrow.effect.strength": "Styrkjepil", "item.minecraft.tipped_arrow.effect.swiftness": "Snøggleikspil", "item.minecraft.tipped_arrow.effect.thick": "<PERSON>va pil", "item.minecraft.tipped_arrow.effect.turtle_master": "Pil frå skjelpaddemeisteren", "item.minecraft.tipped_arrow.effect.water": "Pil med kastbar-verknad", "item.minecraft.tipped_arrow.effect.water_breathing": "Vassandingspil", "item.minecraft.tipped_arrow.effect.weakness": "Veikjepil", "item.minecraft.tipped_arrow.effect.weaving": "Vevepil", "item.minecraft.tipped_arrow.effect.wind_charged": "Vindladingspil", "item.minecraft.tnt_minecart": "Gruvevogn med TNT", "item.minecraft.torchflower_seeds": "Frø av kyndelblom", "item.minecraft.totem_of_undying": "Totem mot døying", "item.minecraft.trader_llama_spawn_egg": "Fram<PERSON>leegg for kjøpmannslama", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "Ljoster", "item.minecraft.tropical_fish": "Sudhavsfisk", "item.minecraft.tropical_fish_bucket": "Sudhavsfiskebytte", "item.minecraft.tropical_fish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for sudhavsfisk", "item.minecraft.turtle_helmet": "Skjelpaddeskal", "item.minecraft.turtle_scute": "Skjelpaddeskjel", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for skjelpadde", "item.minecraft.vex_armor_trim_smithing_template": "Smideskant", "item.minecraft.vex_armor_trim_smithing_template.new": "Plageåndens rustningspryd", "item.minecraft.vex_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>gg for plageånd", "item.minecraft.villager_spawn_egg": "Fram<PERSON>leegg for bygdebu", "item.minecraft.vindicator_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for forsvarar", "item.minecraft.wandering_trader_spawn_egg": "Fr<PERSON><PERSON><PERSON>gg for farande kjøpmann", "item.minecraft.ward_armor_trim_smithing_template": "Smideskant", "item.minecraft.ward_armor_trim_smithing_template.new": "Forvart rustningspryd", "item.minecraft.warden_spawn_egg": "Framkalleegg for vord", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON>n sopp på stong", "item.minecraft.water_bucket": "Vassbytte", "item.minecraft.wayfinder_armor_trim_smithing_template": "Smideskant", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Veiviserens rustningspryd", "item.minecraft.wheat": "Kveite", "item.minecraft.wheat_seeds": "Kveitefrø", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON> let", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Smideskant", "item.minecraft.wild_armor_trim_smithing_template.new": "Vill rustningspryd", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for trollkjerring", "item.minecraft.wither_skeleton_spawn_egg": "Framkalleegg for witherbeinrangel", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON>lingsegg for wither", "item.minecraft.wolf_armor": "Vargrustning", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for varg", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON>grev", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "Tresverd", "item.minecraft.writable_book": "Bok og fjørpenn", "item.minecraft.written_book": "<PERSON><PERSON><PERSON><PERSON> bok", "item.minecraft.yellow_bundle": "Gul bunt", "item.minecraft.yellow_dye": "Gul let", "item.minecraft.yellow_harness": "<PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "Fram<PERSON>leegg for zoglin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for hestedauding", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for dauding", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for bygdedauding", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> for piglindauding", "item.modifiers.any": "<PERSON><PERSON><PERSON> p<PERSON>:", "item.modifiers.armor": "<PERSON><PERSON><PERSON>(e):", "item.modifiers.body": "<PERSON><PERSON><PERSON> p<PERSON>:", "item.modifiers.chest": "Når på kroppen:", "item.modifiers.feet": "Når på føtene:", "item.modifiers.hand": "<PERSON><PERSON><PERSON> halde(n):", "item.modifiers.head": "Når på hovudet:", "item.modifiers.legs": "<PERSON><PERSON>r på beina:", "item.modifiers.mainhand": "Når i hovudhanda:", "item.modifiers.offhand": "<PERSON><PERSON><PERSON> i hi handa:", "item.modifiers.saddle": "<PERSON><PERSON><PERSON> salet:", "item.nbt_tags": "NBT: %s tagg(ar)", "item.op_block_warning.line1": "Å<PERSON>varing:", "item.op_block_warning.line2": "<PERSON> setja ned denne gjenstanden kann føra til at kommandoar vert sette i verk", "item.op_block_warning.line3": "Ikkje bruka om ikkje du kjenner innehaldet vel!", "item.unbreakable": "Uk<PERSON>eleg", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "<PERSON><PERSON> blo<PERSON>", "itemGroup.combat": "Strid", "itemGroup.consumables": "<PERSON> og drikke", "itemGroup.crafting": "<PERSON><PERSON>", "itemGroup.foodAndDrink": "<PERSON> og dry<PERSON>k", "itemGroup.functional": "Funksjonsblokker", "itemGroup.hotbar": "Lagra verktyliner", "itemGroup.ingredients": "<PERSON><PERSON><PERSON>", "itemGroup.inventory": "Overlevingsinventar", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Operatørverkty", "itemGroup.redstone": "Redstoneblokker", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "Fr<PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Verkty og reiskapar", "item_modifier.unknown": "<PERSON><PERSON><PERSON><PERSON> tilmåtar for gjenstandar: %s", "jigsaw_block.final_state": "Gjer om til:", "jigsaw_block.generate": "S<PERSON><PERSON>", "jigsaw_block.joint.aligned": "På line", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Skøytingsslag:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON> på pusleb.", "jigsaw_block.levels": "Steg: %s", "jigsaw_block.name": "Namn:", "jigsaw_block.placement_priority": "Plasseringsprioritet:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON><PERSON> denne pusleblokken kobler seg til en puslebit, er dette rekkefølgen puslebiten behandles for tilkoblinger i den bredere strukturen.\n\nPuslebiter behandles etter synkende prioritet der insettingsrekkefølgen overtar om uavgjort.", "jigsaw_block.pool": "Målområde:", "jigsaw_block.selection_priority": "Valprioritet:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON>r forelderbiten gjennomgå<PERSON> for koblinger, er dette rekkefølgen denne pusleblokken prøver å koble seg til dens mål.\n\nPusleblokker gjennomgås med synkende prioritet hvor tilfeldighet løser uavgjort rekkefølge.", "jigsaw_block.target": "Målnamn:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (spilledåse)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON>", "key.attack": "Åtak/øydelegg", "key.back": "<PERSON><PERSON> attleides", "key.categories.creative": "Skaparmodus", "key.categories.gameplay": "Speling", "key.categories.inventory": "Inventar", "key.categories.misc": "<PERSON><PERSON><PERSON>", "key.categories.movement": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Spelgrensesnitt", "key.chat": "<PERSON><PERSON> nett<PERSON>", "key.command": "Opna kommando", "key.drop": "<PERSON><PERSON><PERSON> vald gje<PERSON>and", "key.forward": "Gå framover", "key.fullscreen": "Fullskjerm", "key.hotbar.1": "For handa-bås 1", "key.hotbar.2": "For handa-bås 2", "key.hotbar.3": "For handa-bås 3", "key.hotbar.4": "For handa-bås 4", "key.hotbar.5": "For handa-bås 5", "key.hotbar.6": "For handa-<PERSON><PERSON><PERSON> 6", "key.hotbar.7": "For handa-bås 7", "key.hotbar.8": "For handa-b<PERSON><PERSON> 8", "key.hotbar.9": "For handa-<PERSON><PERSON><PERSON> 9", "key.inventory": "Opna eller lat att inventaret", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Sletta", "key.keyboard.down": "Pil ned", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Taltastatur 0", "key.keyboard.keypad.1": "Taltastatur 1", "key.keyboard.keypad.2": "Taltastatur 2", "key.keyboard.keypad.3": "Taltastatur 3", "key.keyboard.keypad.4": "Taltastatur 4", "key.keyboard.keypad.5": "Taltastatur 5", "key.keyboard.keypad.6": "Taltastatur 6", "key.keyboard.keypad.7": "Taltastatur 7", "key.keyboard.keypad.8": "Taltastatur 8", "key.keyboard.keypad.9": "Taltastatur 9", "key.keyboard.keypad.add": "Taltastatur +", "key.keyboard.keypad.decimal": "Taltastatur ,", "key.keyboard.keypad.divide": "Taltastatur /", "key.keyboard.keypad.enter": "Taltastatur Enter", "key.keyboard.keypad.equal": "Taltastatur =", "key.keyboard.keypad.multiply": "Taltastatur *", "key.keyboard.keypad.subtract": "Taltastatur -", "key.keyboard.left": "Venstrepil", "key.keyboard.left.alt": "Venstre alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Venstre ctrl", "key.keyboard.left.shift": "Venstre skift", "key.keyboard.left.win": "Venstre Windows-knapp", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "PrintScr", "key.keyboard.right": "<PERSON><PERSON>gre<PERSON><PERSON>", "key.keyboard.right.alt": "Høgre alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON><PERSON><PERSON><PERSON> ctrl", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON><PERSON> skift", "key.keyboard.right.win": "Høgre Windows-knapp", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Ikkje bunden", "key.keyboard.up": "Pil opp", "key.keyboard.world.1": "Verd 1", "key.keyboard.world.2": "Verd 2", "key.left": "Gå til venstre", "key.loadToolbarActivator": "Last inn verktylineaktivator", "key.mouse": "Knapp %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> knapp", "key.mouse.middle": "Midknapp", "key.mouse.right": "<PERSON><PERSON><PERSON><PERSON> knapp", "key.pickItem": "Vel blokk", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Raske handlinger", "key.right": "Gå til høgre", "key.saveToolbarActivator": "Lagra verktylineaktivator", "key.screenshot": "Tak skjermdump", "key.smoothCamera": "Filmatisk kamera", "key.sneak": "Smyg", "key.socialInteractions": "Samkvemsskjerm", "key.spectatorOutlines": "Merk av spelarar (tilskodarar)", "key.sprint": "Spring", "key.swapOffhand": "Byt gjenstanden med hi handa", "key.togglePerspective": "Skift perspektiv", "key.use": "Bruk gjenstand/set ned blokk", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Samfunn", "known_server_link.community_guidelines": "Samfunnsretningslinjer", "known_server_link.feedback": "Tilbakemelding", "known_server_link.forums": "Drøftestadar", "known_server_link.news": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.report_bug": "Rapporter serverfeil", "known_server_link.status": "Stode", "known_server_link.support": "Stønad", "known_server_link.website": "Nettside", "lanServer.otherPlayers": "Innstillingar for andre spelarar", "lanServer.port": "Portnummer", "lanServer.port.invalid": "Ikkje ein gild port.\nLat redigeringsfeltet vera tomt eller legg inn eit anna nummer mellom 1024 og 65535.", "lanServer.port.invalid.new": "Ikkje ein gild port.\nLat tekstfeltet vera tomt eller legg inn eit anna nummer mellom %s og %s.", "lanServer.port.unavailable": "Porten er ikkje tilgjengeleg.\nLat redigeringsfeltet vera tomt eller legg inn eit anna nummer mellom 1024 og 65535.", "lanServer.port.unavailable.new": "Port ikkje tilgjengeleg.\nLat tekstfeltet stå blankt eller oppgjev eit anna nummer mellom %s og %s.", "lanServer.scanning": "R<PERSON><PERSON>jer spel på heimenettverket ditt", "lanServer.start": "Start ei LAN-verd", "lanServer.title": "LAN-verd", "language.code": "nno_NO", "language.name": "Norsk nynorsk", "language.region": "<PERSON><PERSON>", "lectern.take_book": "<PERSON>k bok", "loading.progress": "%s%%", "mco.account.privacy.info": "Les meir om Mojang og personvernlover", "mco.account.privacy.info.button": "Les meir om GDPR", "mco.account.privacy.information": "Mojang set i verk visse framgangsmåtar for å hjelpa born og personvernet deira, inkludert fylgjing av den amerikanske lova Children’s Online Privacy Protection Act (COPPA) og personvernforordninga åt EU (GDPR).\n\nDet kan henda du må få løyve av ein forelder/verje før du får tilgang til Realms-kontoen din.", "mco.account.privacyinfo": "Mojang set i verk visse framgangsmåtar for å hjelpa born og personvernet deira, inkludert fylgjing av den amerikanske lova Children’s Online Privacy Protection Act (COPPA) og personvernforordninga åt EU (GDPR).\n\nDet kan henda du må få løyve av ein forelder/verje før du får tilgang til Realms-kontoen din.\n\nOm du har ein eldre Minecraft-konto (du loggar inn med brukarnamnet ditt), må du overføra kontoen din til ein Mojang-konto for å få tilgang til Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON> konto", "mco.activity.noactivity": "Inga verksemd dei siste %s dagen/dagane", "mco.activity.title": "Spelarverksemd", "mco.backup.button.download": "Last ned nyaste u<PERSON>", "mco.backup.button.reset": "Set attende verd", "mco.backup.button.restore": "<PERSON><PERSON> opp att", "mco.backup.button.upload": "Last opp verd", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry": "Tryggjingsavrit (%s)", "mco.backup.entry.description": "Skildring", "mco.backup.entry.enabledPack": "Slo på datapakken", "mco.backup.entry.gameDifficulty": "Spelvandleik", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Speltenarutgåve", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>", "mco.backup.entry.undefined": "Udefinert omgjerd", "mco.backup.entry.uploaded": "<PERSON><PERSON> opp", "mco.backup.entry.worldType": "Verdsslag", "mco.backup.generate.world": "Skap verda", "mco.backup.info.title": "Brigde frå siste avrit", "mco.backup.narration": "Reservekopi fra %s", "mco.backup.nobackups": "<PERSON>ne Realmen har ingen tryggingsavrit for augneblinken.", "mco.backup.restoring": "<PERSON><PERSON><PERSON> opp att Realmen din", "mco.backup.unknown": "UKJENT", "mco.brokenworld.download": "Last ned", "mco.brokenworld.downloaded": "<PERSON>a ned", "mco.brokenworld.message.line1": "Attend<PERSON>t eller vel ei anna verd.", "mco.brokenworld.message.line2": "Du kan òg velja å lasta ned verda til einspelar.", "mco.brokenworld.minigame.title": "Dette minispelet støst ikkje lenger", "mco.brokenworld.nonowner.error": "Vent til eigaren av Realmen set attende verda", "mco.brokenworld.nonowner.title": "Verda er utdatert", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Set attende", "mco.brokenworld.title": "Den noverande verda di støst ikkje lenger", "mco.client.incompatible.msg.line1": "Klienten din høver ikkje til Realms.", "mco.client.incompatible.msg.line2": "Bruk den siste utgåva av Minecraft.", "mco.client.incompatible.msg.line3": "Realms høver ikkje til utviklingsutgåver.", "mco.client.incompatible.title": "Klienten høver ikkje!", "mco.client.outdated.stable.version": "<PERSON> (%s) er ikke forenelig med Realms.\n\nVennligst bruk den seneste versjonen av Minecraft.", "mco.client.unsupported.snapshot.version": "<PERSON> (%s) er ikke forenelig med Realms.\n\nRealms er ikke tilgjengelig i denne utviklingsversjonen.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "Denne verda var sist spela i utgåva %s. Du brukar utgåva %s. Å nedgradera ei verd kan føra til øydelegging. Det er uvisst om ho vil lasta inn eller verka som ho skal.\n\nEit tryggjingsavrit av verda di vert lagra i «World backups». Du kan atterskapa verda di om det trengst.", "mco.compatibility.incompatible.popup.title": "Uforenelig versjon", "mco.compatibility.incompatible.releaseType.popup.message": "Verdenen du prøver å spille på er uforenelig med versjonen du er på.", "mco.compatibility.incompatible.series.popup.message": "Denne verdenen spiltes sist i versjon %s; du er på versjon %s.\n\nDisse grenene er ikke forenelige med hverandre. En ny verden trengs for å spille på denne versjonen.", "mco.compatibility.unverifiable.message": "Versjonen denne verdenen sist spiltes på kunne ikke fastsettes. Hvis verdenen oppgraderes eller nedgraderes, lagres en reservekopi automatisk i «Verdensreservekopier».", "mco.compatibility.unverifiable.title": "Kompatibiliteten kan ikkje verifiserast", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Denne verda var sist spela i utgåva %s. Du brukar utgåva %s.\n\nEit tryggjingsavrit av verda di vert lagra i «World backups». Du kan atterskapa verda di om det trengst.", "mco.compatibility.upgrade.friend.description": "Denne verdenen ble sist spilt på i versjon %s; du er nå i versjon %s.\n\nEn reservekopi av verdenen vil lagres i mappen \"World Backups\".\n\nEieren av Realmen kan gjenopprette verdenen om nødvendig.", "mco.compatibility.upgrade.title": "Vil du verkeleg oppgradere verda di?", "mco.configure.current.minigame": "Gjeldande", "mco.configure.world.activityfeed.disabled": "Spelarverksemd er mellombels avslege", "mco.configure.world.backup": "Verdstryggingsavrit", "mco.configure.world.buttons.activity": "Spelarverksemd", "mco.configure.world.buttons.close": "Steng Realm", "mco.configure.world.buttons.delete": "Sletta", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Innstillingar", "mco.configure.world.buttons.invite": "Byd inn spelar", "mco.configure.world.buttons.moreoptions": "Fleire val", "mco.configure.world.buttons.newworld": "<PERSON>y verd", "mco.configure.world.buttons.open": "Opna Realm", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "S<PERSON>ara<PERSON>", "mco.configure.world.buttons.region_preference": "Vel region...", "mco.configure.world.buttons.resetworld": "Set attende verd", "mco.configure.world.buttons.save": "Lagra", "mco.configure.world.buttons.settings": "Innstillingar", "mco.configure.world.buttons.subscription": "Tinging", "mco.configure.world.buttons.switchminigame": "Bytt minispel", "mco.configure.world.close.question.line1": "Realmen din vil verta utilgjengeleg.", "mco.configure.world.close.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.close.question.title": "Trenger du å gjøre endringer uten forstyrrelser?", "mco.configure.world.closing": "<PERSON><PERSON><PERSON> att Realmen...", "mco.configure.world.commandBlocks": "Kommandoblokker", "mco.configure.world.delete.button": "Slett Realm", "mco.configure.world.delete.question.line1": "Realmen din vil verta sletta i all æve", "mco.configure.world.delete.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.description": "Realmsskildring", "mco.configure.world.edit.slot.name": "<PERSON><PERSON> på verda", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON>me innstillingar er slegne av sidan den noverande verda di er eit eventyr", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON> innstillingar er slegne av sidan den noverande verda di er ei røynsle", "mco.configure.world.edit.subscreen.inspiration": "<PERSON>me innstillingar er slegne av fordi den noverande verda di er ein inngjevnad", "mco.configure.world.forceGameMode": "<PERSON><PERSON> spelmodus", "mco.configure.world.invite.narration": "Du har %s nye innbydingar", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Innboden", "mco.configure.world.invited.number": "Innbodne (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON><PERSON> brukar", "mco.configure.world.invites.ops.tooltip": "Operatør", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON> bort", "mco.configure.world.leave.question.line1": "Om du fer frå denne <PERSON>en kan du ikkje koma attende utan innbyding", "mco.configure.world.leave.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.loading": "Laster inn Realm", "mco.configure.world.location": "Stad", "mco.configure.world.minigame": "Gjeldande: %s", "mco.configure.world.name": "Realmsnamn", "mco.configure.world.opening": "<PERSON>nar <PERSON>en...", "mco.configure.world.players.error": "Ein spelar med det oppgjevne namnet finst ikkje", "mco.configure.world.players.inviting": "Byd inn spelar...", "mco.configure.world.players.title": "S<PERSON>ara<PERSON>", "mco.configure.world.pvp": "Spelar mot spelar", "mco.configure.world.region_preference": "Regionpreferanse", "mco.configure.world.region_preference.title": "Regionspreferansevalg", "mco.configure.world.reset.question.line1": "Verda di vil atterskapa seg og den noverande verda ditt vil gå tapt", "mco.configure.world.reset.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.resourcepack.question": "Du må ha en tilpasset ressurspakke for å spille på denne Realmen\n\nVil du laste den ned, så spille?", "mco.configure.world.resourcepack.question.line1": "Denne Realmen treng ei tilmåta tilfangspakke", "mco.configure.world.resourcepack.question.line2": "Vil du lasta ned og installere det til å spela?", "mco.configure.world.restore.download.question.line1": "Verda vert nedlasta og lagra i einspelarverdene dine.", "mco.configure.world.restore.download.question.line2": "Vil du halda fram?", "mco.configure.world.restore.question.line1": "Verda di vil atterskapa seg til standen ho hadde den «%s» (%s)", "mco.configure.world.restore.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.settings.expired": "Du kan ikke redigere innstillingene av en utgått Realm", "mco.configure.world.settings.title": "Innstillingar", "mco.configure.world.slot": "Verd %s", "mco.configure.world.slot.empty": "<PERSON>", "mco.configure.world.slot.switch.question.line1": "Realmen din vil verta bytt til ei onnor verd", "mco.configure.world.slot.switch.question.line2": "Er du trygg på at du vil halda fram?", "mco.configure.world.slot.tooltip": "Byt til verd", "mco.configure.world.slot.tooltip.active": "Vert med", "mco.configure.world.slot.tooltip.minigame": "Byt til minispel", "mco.configure.world.spawnAnimals": "Framkall dyr", "mco.configure.world.spawnMonsters": "Framkall monster", "mco.configure.world.spawnNPCs": "Framkall ikkje-spelarstyrte figurar", "mco.configure.world.spawnProtection": "Byrjingstadsvern", "mco.configure.world.spawn_toggle.message": "Å slå av dette kjem til å TAKA BORT ALLE einingar som finst av dette slaget", "mco.configure.world.spawn_toggle.message.npc": "Å slå av dette kjem til å TAKA BORT ALLE einingar som finst av dette slaget, m.a. bygdefolk", "mco.configure.world.spawn_toggle.title": "Åtvaring!", "mco.configure.world.status": "Stode", "mco.configure.world.subscription.day": "dag", "mco.configure.world.subscription.days": "dagar", "mco.configure.world.subscription.expired": "Utgjengen", "mco.configure.world.subscription.extend": "Leng tinging", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> enn <PERSON>in dag", "mco.configure.world.subscription.month": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "<PERSON>ert fornya av seg sjølv om", "mco.configure.world.subscription.recurring.info": "Omgjerder gjorde på Realms-tinginga di, m.a. å lengja tida eller slå av gjentakande fakturering, gjeld ikkje før neste faktureringsdato.", "mco.configure.world.subscription.remaining.days": "%1$s dag(ar)", "mco.configure.world.subscription.remaining.months": "%1$s månad(er)", "mco.configure.world.subscription.remaining.months.days": "%1$s månad(er), %2$s dag(ar)", "mco.configure.world.subscription.start": "Byrjingsdag", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "Tid att", "mco.configure.world.subscription.title": "Tinginga di", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "Laga verd", "mco.configure.world.switch.slot.subtitle": "Denne verda er tom. Vel korleis du vil laga henne", "mco.configure.world.title": "Set i stand Realm:", "mco.configure.world.uninvite.player": "<PERSON>r du trygg på at du vil oppheva innbydinga åt «%s»?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> <PERSON> trygg på at du vil oppheva innbydinga", "mco.configure.worlds.title": "<PERSON><PERSON>", "mco.connect.authorizing": "Loggar inn...", "mco.connect.connecting": "<PERSON><PERSON><PERSON> til Realmen...", "mco.connect.failed": "Kunne ikkje kopla til Realmen", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "Lag", "mco.create.world.error": "Du må gje eit namn!", "mco.create.world.failed": "Mislyktes i å skape verden!", "mco.create.world.reset.title": "Lagar verd...", "mco.create.world.skip": "<PERSON><PERSON> over", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON>, vel kven verd til å putta i Realmen din", "mco.create.world.wait": "Skipar ny Realm...", "mco.download.cancelled": "Nedlastninga er avbroten", "mco.download.confirmation.line1": "Verda du skal lasta ned er større enn %s", "mco.download.confirmation.line2": "Du vil ikkje kunne lasta opp denne verda til Realmen din att", "mco.download.confirmation.oversized": "Verdenen du skal laste ned er større enn %s\n\nDu vil ikke kunne laste opp denne verdenen til Realmen din igjen", "mco.download.done": "<PERSON>last<PERSON> <PERSON><PERSON><PERSON>rd", "mco.download.downloading": "<PERSON><PERSON> ned", "mco.download.extracting": "<PERSON><PERSON> ut", "mco.download.failed": "Nedlastninga var mislukka", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.resourcePack.fail": "Kunne ikkje lasta ned tilfangspak<PERSON>!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON>ar ned den nyaste verda", "mco.error.invalid.session.message": "Freista å opna Minecraft å nyo", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6001": "K<PERSON><PERSON> din er utdatert", "mco.errorMessage.6002": "Krava er ikkje god<PERSON>", "mco.errorMessage.6003": "Nedlastingsgrensa er nådd", "mco.errorMessage.6004": "Opplastingsgrensa er nådd", "mco.errorMessage.6005": "<PERSON><PERSON>", "mco.errorMessage.6006": "Verda er utdatert", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON> i for mange Realmar", "mco.errorMessage.6008": "Ugildt Realm-namn", "mco.errorMessage.6009": "Ugild Realm-skildring", "mco.errorMessage.connectionFailure": "Noko gjekk gale. Freista att seinare.", "mco.errorMessage.generic": "<PERSON><PERSON> gjekk gale: ", "mco.errorMessage.initialize.failed": "Mislyktes i å initialisere Realm", "mco.errorMessage.noDetails": "Ingen detaljar gjeve om feilen", "mco.errorMessage.realmsService": "Noko gjekk gale (%s):", "mco.errorMessage.realmsService.configurationError": "En uforventet feil oppsto under redigeringen av verdensinstillinger", "mco.errorMessage.realmsService.connectivity": "Kunne ikkje kopla til Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kunne ikkje sjå på kompatibel utgåve; fekk svar: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> nyo", "mco.errorMessage.serviceBusy": "Realms har mange påkopla nett no.\nFreista å kopla til Realmen din att om nokre minutt.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "<PERSON><PERSON><PERSON>", "mco.info": "Opplysingar!", "mco.invited.player.narration": "Baud inn spelar %s", "mco.invites.button.accept": "God<PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "Ingen ventande innbydingar!", "mco.invites.pending": "Nye innbydingar!", "mco.invites.title": "<PERSON>ent<PERSON>e innbydingar", "mco.minigame.world.changeButton": "Vel eit anna minispel", "mco.minigame.world.info.line1": "Dette vil mellombels byta ut verda di med eit minispel!", "mco.minigame.world.info.line2": "Du kan seinare gå attende til den opphavlege verda di utan å missa noko.", "mco.minigame.world.noSelection": "<PERSON><PERSON>ko", "mco.minigame.world.restore": "Stengjer minispel...", "mco.minigame.world.restore.question.line1": "Minispelet vil enda, og verda di vil verta gjeldande.", "mco.minigame.world.restore.question.line2": "Er du trygg på at du vil gjera dette?", "mco.minigame.world.selected": "Valt minispel:", "mco.minigame.world.slot.screen.title": "Byter verd...", "mco.minigame.world.startButton": "Byt", "mco.minigame.world.starting.screen.title": "Startar minispel...", "mco.minigame.world.stopButton": "Avslutt minispel", "mco.minigame.world.switch.new": "Vel eit anna minispel?", "mco.minigame.world.switch.title": "Byt minispel", "mco.minigame.world.title": "Gjer om Realm til speltypen minispel", "mco.news": "Realms-nyhende", "mco.notification.dismiss": "Lat att", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON> over no", "mco.notification.transferSubscription.message": "Java-Realms-abonnement<PERSON> flytter til Microsoft Store. La ikke abonnementet ditt løpe ut!\nOverfør det nå og få 30 dager med Realms gratis.\nGå til profilen din på minecraft.net for å overføre abonnementet ditt.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON> le<PERSON>", "mco.notification.visitUrl.message.default": "<PERSON><PERSON><PERSON> le<PERSON>", "mco.onlinePlayers": "Spelara<PERSON> på nett", "mco.play.button.realm.closed": "Realm er stengt", "mco.question": "Spurning", "mco.reset.world.adventure": "Eventyr", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "<PERSON>y verd", "mco.reset.world.inspiration": "Inngjevnad", "mco.reset.world.resetting.screen.title": "Set attende verd...", "mco.reset.world.seed": "Seed (valfritt)", "mco.reset.world.template": "Verdsmalar", "mco.reset.world.title": "Set attende verd", "mco.reset.world.upload": "Last opp verd", "mco.reset.world.warning": "Dette vil byta ut Realmverda di for all tid", "mco.selectServer.buy": "<PERSON><PERSON><PERSON><PERSON> ein Realm!", "mco.selectServer.close": "Lat att", "mco.selectServer.closed": "Stengd Realm", "mco.selectServer.closeserver": "Steng Realm", "mco.selectServer.configure": "<PERSON><PERSON>", "mco.selectServer.configureRealm": "Set opp Realm", "mco.selectServer.create": "Skipa Realm", "mco.selectServer.create.subtitle": "Velg hvilken verden som skal legges inn på din nye Realm", "mco.selectServer.expired": "Utgjengen Realm", "mco.selectServer.expiredList": "Tinginga di har gått ut", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON>g", "mco.selectServer.expiredTrial": "Prøveutgåva di er utgjengen", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON> ut om ein dag", "mco.selectServer.expires.days": "Går ut om %s dagar", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON> ut snart", "mco.selectServer.leave": "Far frå Realm", "mco.selectServer.loading": "Lastar inn Realm-liste", "mco.selectServer.mapOnlySupportedForVersion": "Dette kartet er ikkje stødd i %s", "mco.selectServer.minigame": "Minispel:", "mco.selectServer.minigameName": "Minispel: %s", "mco.selectServer.minigameNotSupportedInVersion": "Kan ikkje spela dette minispelet i %s", "mco.selectServer.noRealms": "Du ser ikkje ut til å ha ein Realm. Legg til ein Realm for å spela med venene dine.", "mco.selectServer.note": "Åthug:", "mco.selectServer.open": "Open Realm", "mco.selectServer.openserver": "Opna Realm", "mco.selectServer.play": "Spel", "mco.selectServer.popup": "Realms er ein trygg, enkel måte å nyta ei Minecraft-verd over nettet med opptil 10 vener på éin gong. Han stør mykje minispel og mange tilmåta verder! Berre eigaren av Realmen må betala.", "mco.selectServer.purchase": "<PERSON><PERSON> til <PERSON>", "mco.selectServer.trial": "Få ei prøveutgåve!", "mco.selectServer.uninitialized": "Klikk for å laga ein Realm!", "mco.snapshot.createSnapshotPopup.text": "Du oppretter nå en gratis Snapshot-Realm som kobles til ditt betalte abonnement. Denne nye Snapshot-Realmen er åpen så lenge det betalte abonnementet gjelder. Din betalte Realm påvirkes ikke.", "mco.snapshot.createSnapshotPopup.title": "Laga utviklingsutgåve-Realm?", "mco.snapshot.creating": "<PERSON>gar utviklingsutgåve-Realm...", "mco.snapshot.description": "Samankopla med «%s»", "mco.snapshot.friendsRealm.downgrade": "Du må vera på utgåva %s for å verta med i denne Realmen", "mco.snapshot.friendsRealm.upgrade": "%s må oppgradere Realmen sin før du kan spille på denne versjonen", "mco.snapshot.paired": "<PERSON><PERSON>-<PERSON>en er koblet med \"%s\"", "mco.snapshot.parent.tooltip": "Bruk den nyaste versjonen av Minecraft for å spele denne Realmen", "mco.snapshot.start": "Set opp kostnadsfri utviklingsutgåve-Realm", "mco.snapshot.subscription.info": "Dette er ein utviklingsversjons-Realm som er para med abonnementet '%s'. Den vil vere aktiv så lenge den para Realmen er aktiv.", "mco.snapshot.tooltip": "Bruk utviklginsversjons-Realmar for å få ein sniktitt på kommande versjonar av Minecraft, som kan innehalde nytt innhald eller andre endringar.\n\nDu kan finne dei normale Realmane dine i fullversjonen av spelet.", "mco.snapshotRealmsPopup.message": "Realms er no tilgjengeleg i utviklingsutgåver frå og med 23w41a. Kvar Realms-tinging kjem med ein kostnadsfri utviklingsutgåve-Realm som er skild frå den vanlege Java-Realmen!", "mco.snapshotRealmsPopup.title": "Realms er no tilgjengeleg i utviklingsutgåver", "mco.snapshotRealmsPopup.urlText": "<PERSON> ut meir", "mco.template.button.publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.select": "Vel", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON>el mal (valfritt)", "mco.template.info.tooltip": "Nettstaden åt utgjevaren", "mco.template.name": "Mal", "mco.template.select.failure": "Me kunne ikkje henta lista over innehald for denne kate<PERSON>.\nSjå på internettilkoplinga di eller freista att sidan.", "mco.template.select.narrate.authors": "Forfattarar: %s", "mco.template.select.narrate.version": "utgåve %s", "mco.template.select.none": "Oi sann! Det sér ut som om denne kategorien er tom for augneblinken.\nSjå att sidan for nytt innehald, eller %s om du er ein skapar.", "mco.template.select.none.linkTitle": "tenk på å senda inn noko sjølv", "mco.template.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.title.minigame": "Minispel", "mco.template.trailer.tooltip": "Verdstrailer", "mco.terms.buttons.agree": "Samd", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON> samd", "mco.terms.sentence.1": "Eg samtykkjer med Minecraft Realms", "mco.terms.sentence.2": "Nyttingsvilkår", "mco.terms.title": "Nyttingsvilkår for Realms", "mco.time.daysAgo": "%1$s dag(ar) sidan", "mco.time.hoursAgo": "%1$s time/timar sidan", "mco.time.minutesAgo": "%1$s minutt sidan", "mco.time.now": "nett no", "mco.time.secondsAgo": "%1$s sekund sidan", "mco.trial.message.line1": "Vil du ha din eigen realm?", "mco.trial.message.line2": "<PERSON>likk her for meir opplysing!", "mco.upload.button.name": "Last opp", "mco.upload.cancelled": "Opplasting a<PERSON><PERSON><PERSON>", "mco.upload.close.failure": "Kunne ikkje stengja Realmen din. Freista att seinare", "mco.upload.done": "Opplasting ferdig", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Opplasting var mislukka (%s)", "mco.upload.failed.too_big.description": "Den valgte verdenen er for stor. Høyeste tillatte størrelse er %s.", "mco.upload.failed.too_big.title": "Verd for stor", "mco.upload.hardcore": "Hardhausverder kan ikkje verta lasta opp!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Førebur verda di", "mco.upload.select.world.none": "Ingen einspelarverder funne!", "mco.upload.select.world.subtitle": "Vel ei einspelarverd å lasta opp", "mco.upload.select.world.title": "Last opp verd", "mco.upload.size.failure.line1": "«%s» er for stor!", "mco.upload.size.failure.line2": "Det er %s. Høgste tillatne storleik er %s.", "mco.upload.uploading": "Lastar opp «%s»", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON> kartet ditt", "mco.version": "Utgåve: %s", "mco.warning": "Åtvaring!", "mco.worldSlot.minigame": "Minispel", "menu.custom_options": "Egendefinerte innstillinger...", "menu.custom_options.title": "Egendefinerte innstillinger", "menu.custom_options.tooltip": "Husk: Egendefinerte innstillinger er sørget for av tredjepartsservere og/eller -innhold.\nHåndter forsiktig!", "menu.custom_screen_info.button_narration": "Dette er en egendefinert skjerm. Lær mer.", "menu.custom_screen_info.contents": "Innholdet av denne skjermen er kontrollert av tredjepartsservere og -kart som enten ikke er eid, styrt, eller overvåket av Mojang Studios eller Microsoft.\n\nHåndter forsiktig! Vær alltid forsiktig når du følger lenker og gi aldri bort din personlige informasjon, inkludert innloggingsdetaljer.\n\nHvis denne skjermen forhindrer deg fra å spille, kan du også koble fra serveren ved å bruke knappen under.", "menu.custom_screen_info.disconnect": "Egendefinert skjerm avvist", "menu.custom_screen_info.title": "Påminnelse om egendefinerte skjermer", "menu.custom_screen_info.tooltip": "<PERSON>te er en egendefinert skjerm. Klikk her for å lære mer.", "menu.disconnect": "Kopla frå", "menu.feedback": "Tilbakemelding…", "menu.feedback.title": "Tilbakemelding", "menu.game": "Spelmeny", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Val...", "menu.paused": "Spel pausa", "menu.playdemo": "Spel prøveutgåveverd", "menu.playerReporting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Førebur byrjestad: %s%%", "menu.quick_actions": "Raske handlinger...", "menu.quick_actions.title": "Raske handlinger", "menu.quit": "Enda spel", "menu.reportBugs": "<PERSON>d frå om feil", "menu.resetdemo": "Set attende prøveutgåveverd", "menu.returnToGame": "Attende til spelet", "menu.returnToMenu": "Lagra og gå til hovudmenyen", "menu.savingChunks": "<PERSON><PERSON><PERSON> ve<PERSON>", "menu.savingLevel": "<PERSON><PERSON><PERSON> verda", "menu.sendFeedback": "<PERSON><PERSON><PERSON>terbo<PERSON>", "menu.server_links": "<PERSON><PERSON><PERSON>…", "menu.server_links.title": "<PERSON><PERSON><PERSON>", "menu.shareToLan": "Opna til LAN", "menu.singleplayer": "Einspelar", "menu.working": "<PERSON><PERSON><PERSON>...", "merchant.deprecated": "Bygdefolk fyller på i lageret to gonger kvar dag.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "Svein", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s — %s", "merchant.trades": "Byter", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Trykk %1$s for å stiga av", "multiplayer.applyingPack": "Legg inn tilfangspakke", "multiplayer.confirm_command.parse_errors": "Du prøver å utføre en ukjent eller ugyldig kommando.\nEr du sikker?\nKommando: %s", "multiplayer.confirm_command.permissions_required": "Du prøver å utføre en kommando som krever utvidete tillatelser.\nDette kan muligens negativt påvirke spillet ditt.\nEr du sikker?\nKommando: %s", "multiplayer.confirm_command.title": "Bekreft kommandoutførelse", "multiplayer.disconnect.authservers_down": "Stadfestingstenarane er nede. Freista att seinare, lei for det!", "multiplayer.disconnect.bad_chat_index": "Oppdaga ei overhoppa eller omstokka nettpratsmelding frå serveren", "multiplayer.disconnect.banned": "Du har vorte bannlyst frå denne tenaren", "multiplayer.disconnect.banned.expiration": "\nBannlysinga di vert teka bort %s", "multiplayer.disconnect.banned.reason": "Du er bannlyst frå denne tenaren.\nGrunn: %s", "multiplayer.disconnect.banned_ip.expiration": "\nBannlysinga di vert teka bort %s", "multiplayer.disconnect.banned_ip.reason": "IP-adressa di er bannlyst frå denne tenaren.\nGrunn: %s", "multiplayer.disconnect.chat_validation_failed": "Kunne ikkje validera nettpratsmelding", "multiplayer.disconnect.duplicate_login": "Du logga inn frå ein annan stad", "multiplayer.disconnect.expired_public_key": "Den offentlege lykelen til profilen er utgjengen. Sjå til at systemet ditt er synkronisert, og freista opna spelet å nyo.", "multiplayer.disconnect.flying": "Flyging er ikkje slege på på denne tenaren", "multiplayer.disconnect.generic": "Fråkopla", "multiplayer.disconnect.idling": "Du har vore gjerand<PERSON>us for lenge!", "multiplayer.disconnect.illegal_characters": "Ugilde teikn i nettpraten", "multiplayer.disconnect.incompatible": "Uhøvande klient! Bruk %s", "multiplayer.disconnect.invalid_entity_attacked": "Freistar å åtaka ei ugild eining", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON><PERSON> sende ein ugild pakke", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON> s<PERSON>", "multiplayer.disconnect.invalid_player_movement": "Ugild spelarflyttingspakke teken imot", "multiplayer.disconnect.invalid_public_key_signature": "Ugild underskrift for den offentlege lykelen til profilen. Freista å opna spelet å nyo.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ugild underskrift for den offentlege lykelen til profilen.\nFreista å opna spelet å nyo.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON><PERSON>ld kø<PERSON>tysflyttingspakke teken imot", "multiplayer.disconnect.ip_banned": "Du er vorten IP-bannlyst frå denne tenaren", "multiplayer.disconnect.kicked": "Kasta ut av ein operatør", "multiplayer.disconnect.missing_tags": "Eit ufullstendig oppsett av taggar vart henta frå tenaren.\nKontakt tenaroperatøren.", "multiplayer.disconnect.name_taken": "<PERSON>te namnet er teke alt", "multiplayer.disconnect.not_whitelisted": "Du er ikkje kvitelista på denne tenaren!", "multiplayer.disconnect.out_of_order_chat": "Nettpratspakke vart motteken i gala rekkjefylgje. Er systemtida di omgjord?", "multiplayer.disconnect.outdated_client": "Klienten høver ikkje! Bruka %s", "multiplayer.disconnect.outdated_server": "Klienten høver ikkje! Bruka %s", "multiplayer.disconnect.server_full": "Tenaren er full!", "multiplayer.disconnect.server_shutdown": "Tenar laten att", "multiplayer.disconnect.slow_login": "Tok for lang tid å logga inn", "multiplayer.disconnect.too_many_pending_chats": "For mange usannkjende meldingar i nettprat", "multiplayer.disconnect.transfers_disabled": "Tenaren godtek ikkje overføringar", "multiplayer.disconnect.unexpected_query_response": "Uventa eige data frå klient", "multiplayer.disconnect.unsigned_chat": "Mottok nettpratspakke med manglande eller ugild underskrift.", "multiplayer.disconnect.unverified_username": "Kunne ikkje sannkjenna brukarnamn!", "multiplayer.downloadingStats": "Hentar statistikk...", "multiplayer.downloadingTerrain": "Lastar terreng...", "multiplayer.lan.server_found": "<PERSON>y tenar funnen: %s", "multiplayer.message_not_delivered": "Kan ikkje senda melding. Sjå tenarloggen: %s", "multiplayer.player.joined": "%s logga på", "multiplayer.player.joined.renamed": "%s (tidlegare kjend som %s) vart med i spelet", "multiplayer.player.left": "%s gjekk or spelet", "multiplayer.player.list.hp": "%sliv", "multiplayer.player.list.narration": "Spelarar på nett: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON><PERSON> krev ein tilmåta tilfangspakke", "multiplayer.requiredTexturePrompt.line1": "<PERSON>ne tenaren krev bruk av ein tilmåta tilfangspakke.", "multiplayer.requiredTexturePrompt.line2": "Om du avviser denne tilm<PERSON> tilfangspakken vert du kopla frå tenaren.", "multiplayer.socialInteractions.not_available": "Samkvem er berre mogeleg i fleirspelar-verder", "multiplayer.status.and_more": "... og %s fleire ...", "multiplayer.status.cancelled": "Avbrote", "multiplayer.status.cannot_connect": "Kunne ikkje kopla til tenaren", "multiplayer.status.cannot_resolve": "<PERSON><PERSON> ikkje løysa vertsnamn", "multiplayer.status.finished": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Uhøvande utgåve!", "multiplayer.status.motd.narration": "Bodskap for dagen: %s", "multiplayer.status.no_connection": "(inga til<PERSON>)", "multiplayer.status.old": "G<PERSON><PERSON>", "multiplayer.status.online": "<PERSON><PERSON> nett", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisekundar", "multiplayer.status.pinging": "Pingar...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s av %s spelarar på nett", "multiplayer.status.quitting": "<PERSON><PERSON>", "multiplayer.status.request_handled": "Førespurnaden om stode er vorten handsama", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Tok imot uynskt stode", "multiplayer.status.version.narration": "Tenarutgåve: %s", "multiplayer.stopSleeping": "Gå ifrå senga", "multiplayer.texturePrompt.failure.line1": "<PERSON><PERSON> ikkje slå på tilfangspakke for tenaren", "multiplayer.texturePrompt.failure.line2": "Funksjonar som krev tilmåta tilfang verkar kan henda som venta", "multiplayer.texturePrompt.line1": "<PERSON>ne tenaren tilrår bruk av ei viss tilfangspakke.", "multiplayer.texturePrompt.line2": "Ynskjer du å lasta ned og installera det automatisk?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMelding frå tenaren:\n%s", "multiplayer.title": "Spel flei<PERSON>ar", "multiplayer.unsecureserver.toast": "Meldingar sende på denne tenaren kan ha vore omgjorde og avspeglar kan henda ikkje den opphavlege meldinga", "multiplayer.unsecureserver.toast.title": "Kunne ikkje verifisera nettpratsmeldingar", "multiplayerWarning.check": "Ikkje vis denne skjermen att", "multiplayerWarning.header": "Åtvaring: Tredjeparts-nettspel", "multiplayerWarning.message": "Åtvaring: Nettspel er tilbode av tredjeparts-tenarar som Mojang Studios eller Microsoft korkje eig, styrer, eller tilser. Når du spelar på nett kan du møta på umoderert nettprat eller andre former for brukarskapa innehald som ikkje alltid høver for alle.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Knapp: %s", "narration.button.usage.focused": "Trykk Enter for å slå på", "narration.button.usage.hovered": "Venstreklikk for å slå på", "narration.checkbox": "Avmerkingsboks: %s", "narration.checkbox.usage.focused": "Trykk Enter for å slå på", "narration.checkbox.usage.hovered": "Venstreklikk for å skifta", "narration.component_list.usage": "Trykk Tab for å koma deg til neste element", "narration.cycle_button.usage.focused": "Trykk Enter for å byta til %s", "narration.cycle_button.usage.hovered": "Venstreklikk for å byta til %s", "narration.edit_box": "Brigdefelt: %s", "narration.item": "Gjenstand: %s", "narration.recipe": "Oppskrift for %s", "narration.recipe.usage": "Venstreklikk for å velja", "narration.recipe.usage.more": "Høgreklikk for å sjå fleire oppskrifter", "narration.selection.usage": "Trykk op<PERSON>- <PERSON><PERSON> ned-knappane for å flytta deg til ei anna <PERSON>pfø<PERSON>", "narration.slider.usage.focused": "Trykk på tastaturknappane for venstre eller høgre for å gjera om på verde", "narration.slider.usage.hovered": "Drag glidebrytaren for å gjera om på verde", "narration.suggestion": "Valde framlegg %s av %s: %s", "narration.suggestion.tooltip": "Valde framlegg %s av %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Trykk Tab for å koma deg til neste framlegg", "narration.suggestion.usage.cycle.hidable": "Trykk Tab for å koma deg til neste framlegg, el<PERSON> for å gå bort frå framlegga", "narration.suggestion.usage.fill.fixed": "Trykk Tab for å bruka framlegg", "narration.suggestion.usage.fill.hidable": "Trykk Tab for å bruka framlegg, el<PERSON> for å gå bort frå framlegga", "narration.tab_navigation.usage": "Trykk Ctrl og Tab for å skifta mellom faner", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Vandleikslås", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON> opp", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s er bunde til %s", "narrator.controls.reset": "Set attende %s-knapp", "narrator.controls.unbound": "%s er ikkje bunde", "narrator.joining": "Vert med", "narrator.loading": "Lastar: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Valde listerad %s utav %s", "narrator.position.object_list": "Valde radelement %s utav %s", "narrator.position.screen": "Skjermelement %s utav %s", "narrator.position.tab": "Valde fane %s utav %s", "narrator.ready_to_play": "Klår til å spela", "narrator.screen.title": "Hovudmeny", "narrator.screen.usage": "Bruk musepeikaren eller Tab til å velja element", "narrator.select": "Merkt: %s", "narrator.select.world": "Valt: %s, sist spela: %s, %s, %s, utgåve: %s", "narrator.select.world_info": "Valde %s, sist spela %s,%s", "narrator.toast.disabled": "Forteljar slegen av", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> slegen på", "optimizeWorld.confirm.description": "Dette vil freista å optimalisera verda di ved å sjå til at all data er lagra i det nyaste spelformatet. Dette kan taka ei lang stund, avhengig av verda di. Når dette er klårt vil verda di køyra snøggare, men vil ikkje lenger stø eldre utgåver av spelet. Er du trygg på at du vil halda fram?", "optimizeWorld.confirm.proceed": "Lag reservekopi og optimer", "optimizeWorld.confirm.title": "Optimalis<PERSON><PERSON> verd", "optimizeWorld.info.converted": "Oppgraderte verdsbitar: %s", "optimizeWorld.info.skipped": "Verdsbitar hoppa over: %s", "optimizeWorld.info.total": "Verdsbitar i alt: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Tel verdsbitar...", "optimizeWorld.stage.failed": "Mislukka! :(", "optimizeWorld.stage.finished": "Fullfører...", "optimizeWorld.stage.finished.chunks": "Avsluttar oppgradering av verdsbitar...", "optimizeWorld.stage.finished.entities": "Avsluttar oppgradering av einingar...", "optimizeWorld.stage.finished.poi": "Avsluttar oppgradering av interessepunkt...", "optimizeWorld.stage.upgrading": "Oppgraderer alle verdsbitar...", "optimizeWorld.stage.upgrading.chunks": "Oppgraderer alle verdsbitar...", "optimizeWorld.stage.upgrading.entities": "Oppgraderer alle einingane...", "optimizeWorld.stage.upgrading.poi": "Oppgraderer alle interessepunktar...", "optimizeWorld.title": "Optimaliserer verda «%s»", "options.accessibility": "Innstillingar for tilgjenge...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON> k<PERSON>", "options.accessibility.high_contrast.error.tooltip": "Høgkontrast-tilfangspakke er ikkje tilgjengeleg", "options.accessibility.high_contrast.tooltip": "Styrkjer kontrasten til element i brukarflata", "options.accessibility.high_contrast_block_outline": "Høgkontrast på blokk-kant", "options.accessibility.high_contrast_block_outline.tooltip": "Styrkjer blokk-kantkontrasten på målblokki.", "options.accessibility.link": "Vegvisar for tilgjenge", "options.accessibility.menu_background_blurriness": "Bakgrunnsklårleik i menyen", "options.accessibility.menu_background_blurriness.tooltip": "Gjer om på klårleiken av menybakgrunnar", "options.accessibility.narrator_hotkey": "Snartast for forteljar", "options.accessibility.narrator_hotkey.mac.tooltip": "Till<PERSON>t at forteljaren vert påslegen og avslegen med «Cmd+B»", "options.accessibility.narrator_hotkey.tooltip": "Tillèt at forteljaren vert påslegen og avslegen med «Ctrl+B»", "options.accessibility.panorama_speed": "Panoramasnøggleik", "options.accessibility.text_background": "Tekstbakgrunn", "options.accessibility.text_background.chat": "<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background.everywhere": "Allstad", "options.accessibility.text_background_opacity": "Bakgrunnsgjennomsyn", "options.accessibility.title": "Innstillingar for tilgjenge...", "options.allowServerListing": "Tillat tenarlistingar", "options.allowServerListing.tooltip": "Tenarar kan lista spelarar som er på nett i den offentlege statusen sin.\nMed dette valet avslege vil ikkje namnet ditt koma opp på slike lister.", "options.ao": "Jamt ljos", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minst", "options.ao.off": "AV", "options.attack.crosshair": "Trådkross", "options.attack.hotbar": "For handa", "options.attackIndicator": "Åtaksmålar", "options.audioDevice": "Eining", "options.audioDevice.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.autoJump": "Autohopp", "options.autoSuggestCommands": "Kommandoframlegg", "options.autosaveIndicator": "<PERSON>is når spelet vert lagra", "options.biomeBlendRadius": "Lendeblanding", "options.biomeBlendRadius.1": "AV (snøggast)", "options.biomeBlendRadius.11": "11x11 (ekstrem)", "options.biomeBlendRadius.13": "13x13 (s<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (høgst)", "options.biomeBlendRadius.3": "3x3 (sn<PERSON>gg)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (høg)", "options.biomeBlendRadius.9": "9x9 (s<PERSON><PERSON> høg)", "options.chat": "Prateinstillingar...", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Samtaledrygjing: %s sekund", "options.chat.delay_none": "Samtaledrygjing: inga", "options.chat.height.focused": "Fokusert høgd", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON><PERSON> høgd", "options.chat.line_spacing": "Radfråstand", "options.chat.links": "Internettlenkjer", "options.chat.links.prompt": "Be om lenkjer", "options.chat.opacity": "Gjennomsyn på prat", "options.chat.scale": "Skriftstorleik i praten", "options.chat.title": "Prateinstillingar...", "options.chat.visibility": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Vist", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "Breidd", "options.chunks": "%s verdsbitar", "options.clouds.fancy": "Stase<PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Kontrollar...", "options.credits_and_attribution": "Medverkande...", "options.damageTiltStrength": "Skadeskaking", "options.damageTiltStrength.tooltip": "<PERSON><PERSON><PERSON> kameraristing årsaka av å verta skadd.", "options.darkMojangStudiosBackgroundColor": "Einleta logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON> bakgrunnen på Mojang Studios-lasteskjermen til svart.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "A<PERSON><PERSON><PERSON> kor mykje mørkerverknaden pulserar når ein vord eller sculkeskrikar gjev deg det.", "options.difficulty": "<PERSON><PERSON><PERSON>", "options.difficulty.easy": "<PERSON>t", "options.difficulty.easy.info": "Fiendslege skapningar kjem fram men gjer mindre skade. Svoltmålaren vert tømd og dreg helse ned til 5 hjarto.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "Fiendslege skapningar kjem fram og gjer meir skade. Svoltmålaren vert tømd og all helsa fer.", "options.difficulty.hardcore": "<PERSON><PERSON>", "options.difficulty.normal": "<PERSON><PERSON>", "options.difficulty.normal.info": "Fiendslege skapningar kjem fram og gjer vanleg skade. Svoltmålaren vert tømd og dreg helse ned til eit halvt hjarta.", "options.difficulty.online": "Tenarvandleik", "options.difficulty.peaceful": "<PERSON> j<PERSON>", "options.difficulty.peaceful.info": "Ingen fiendslege skapningar. Berre somme nøytrale skapningar kjem fram. Ein vert ikkje svolten og liv vert fylt opp over tid.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klassisk stereoljod", "options.directionalAudio.on.tooltip": "Brukar HRTF-basert ljoddusj til å betra simuleringa av 3D-ljod. Krev maskinvare for ljod som tillet HRTF. HRTF er best note med øyretelefonar.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON> rulling", "options.entityDistanceScaling": "Einingsfråstand", "options.entityShadows": "Einingsskuggar", "options.font": "Skriftval...", "options.font.title": "Skriftval", "options.forceUnicodeFont": "Tving Unicode-skrifttype", "options.fov": "Synsfelt", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON>", "options.fovEffectScale": "Synsviddverknader", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON> kor mykje synsvidd kan skifta med spelverknader.", "options.framerate": "%s fps", "options.framerateLimit": "Høgst FPS", "options.framerateLimit.max": "Uavgrensa", "options.fullscreen": "Fullskjerm", "options.fullscreen.current": "Gjeldande", "options.fullscreen.entry": "%s×%s@%s (%sbit)", "options.fullscreen.resolution": "Fullskjermsoppløysing", "options.fullscreen.unavailable": "Innstilinga er ikkje tilgjengeleg", "options.gamma": "Ljosstyrke", "options.gamma.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.max": "Ljost", "options.gamma.min": "Dimt", "options.generic_value": "%s: %s", "options.glintSpeed": "Glinsesnøggleik", "options.glintSpeed.tooltip": "Avg<PERSON> kor fort den visuelle glimten skrimrar over galdra gjenstandar.", "options.glintStrength": "Glimtstyrke", "options.glintStrength.tooltip": "Avgjer kor gjennomsynleg den visuelle glimten på galdra gjenstandar er.", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Framifrå!", "options.graphics.fabulous.tooltip": "%s grafikk brukar shaders til å teikna vêr, skyer og partiklar bak gjennomskinlege blokker og vatn.\nDette kan påverka ytinga i stor grad for berelege einingar og 4K-skjermar.", "options.graphics.fancy": "Stase<PERSON>", "options.graphics.fancy.tooltip": "Staseleg grafikk balanserer yting og kvalitet for flestalle maskiner.\nDet er mogeleg at vêr, skyer og partiklar ikkje ovrar seg bak gjennomskinlege blokker eller vatn.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Snøgg grafikk minkar mengda synleg regn og snø.\nGjennomsynseffektar er slegne av for ymse bokker, som t.d. trelauv.", "options.graphics.warning.accept": "Haldt fram utan stønad", "options.graphics.warning.cancel": "Tak meg attende", "options.graphics.warning.message": "Grafikkeininga di vantar stønad for grafikkvalet «%s».\n\nDu kan ignorera dette og halda fram, men eininga di vert ikkje stødd om du vel å bruka grafikkvalet «%s».", "options.graphics.warning.renderer": "<PERSON><PERSON><PERSON> funnen: [%s]", "options.graphics.warning.title": "Grafikkeininga er ustødd", "options.graphics.warning.vendor": "Lev<PERSON><PERSON><PERSON><PERSON>: [%s]", "options.graphics.warning.version": "Utgåve av OpenGL funna: [%s]", "options.guiScale": "Grensesnittsstorleik", "options.guiScale.auto": "Automatisk", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "Hindrar lynnedslag frå å lysa opp himmelen. Sjølve nedslaget vil framleis vera synleg.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON>namn", "options.hideMatchedNames.tooltip": "3.-parts-tenarar kan senda meldingar som ikkje er i standardformat.\nMed denne innstillinga på; gøymde meldingar vert samsvara etter kva sendaren heiter.", "options.hideSplashTexts": "<PERSON><PERSON><PERSON>", "options.hideSplashTexts.tooltip": "Gøymer den gule hovudmenyteksta.", "options.inactivityFpsLimit": "Senk FPS når", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Begrenser bildefrekvens til 30 når spillet ikke får instruksjoner fra spilleren på over ett minutt. Begrenser videre til 10 etter 9 minutter til.", "options.inactivityFpsLimit.minimized": "Minimert", "options.inactivityFpsLimit.minimized.tooltip": "Begrenser bilderate kun når spillvinduet er minimert.", "options.invertMouse": "Spegelvend museretning", "options.japaneseGlyphVariants": "Japanske teiknvariantar", "options.japaneseGlyphVariants.tooltip": "Brukar japanske variantar av CJK-teikn i standardskrifttypen", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Skift", "options.language": "Mål...", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(Det kan hende omsetjingane ikkje er 100%% rette)", "options.languageWarning": "Det hender at omsetjingane ikkje er 100%% rette", "options.mainHand": "Hovudhand", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap-nivå", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "Trøye", "options.modelPart.left_pants_leg": "Venstre br<PERSON>n", "options.modelPart.left_sleeve": "Venstre erm", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON><PERSON> erm", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.mouse_settings": "Museinnstillingar...", "options.mouse_settings.title": "Museinnstillingar", "options.multiplayer.title": "Fleirspelinnstillingar...", "options.multiplier": "%sx", "options.music_frequency": "Musikkhyppighet", "options.music_frequency.constant": "<PERSON><PERSON> tiden", "options.music_frequency.default": "Standard", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "<PERSON><PERSON> hvor ofte musikk spilles mens man er i en spillverden.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Les opp alt", "options.narrator.chat": "<PERSON> op<PERSON> nett<PERSON>ten", "options.narrator.notavailable": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.off": "AV", "options.narrator.system": "Fortel om systemet", "options.notifications.display_time": "Visingstid for varslar", "options.notifications.display_time.tooltip": "Påverkar kor lenge varslingar er synlege på skjermen.", "options.off": "AV", "options.off.composed": "%s: AV", "options.on": "PÅ", "options.on.composed": "%s: P<PERSON>", "options.online": "På nett...", "options.online.title": "Innstillingar for speling på nett", "options.onlyShowSecureChat": "<PERSON>is berre trygg nettprat", "options.onlyShowSecureChat.tooltip": "Vis berre meldingar frå andre spel<PERSON>r når det er stadfest at sende dei, og utan å vera modifiserte.", "options.operatorItemsTab": "OP-reiskapsfane", "options.particles": "<PERSON><PERSON><PERSON>", "options.particles.all": "Alle", "options.particles.decreased": "<PERSON><PERSON><PERSON>", "options.particles.minimal": "Minimalt", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Verdsbitbyggjar", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Nokre gjerningar inni ein verdsbit oppdaterer blokka på augneblinken. Dette gjeld setjing og øydelegging av blokker.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Verdsbitar i nærleiken vert alltid oppdaterte på augneblinken. Dette kan påverka spelytinga når blokker vert sette ned eller øydelagde.", "options.prioritizeChunkUpdates.none": "Tråda", "options.prioritizeChunkUpdates.none.tooltip": "Verdsbitar i nærleiken vert oppdaterte i parallelle trådar. Dette kan føra til stutte visuelle hol når blokker vert øydelagde.", "options.rawMouseInput": "<PERSON><PERSON>æv", "options.realmsNotifications": "Realms-nyhende og innbydingar", "options.realmsNotifications.tooltip": "Henter Realms-nyheter og invitasjoner til hovedmenyen og viser deres passende ikon på Realms-knappen.", "options.reducedDebugInfo": "<PERSON><PERSON> feil<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "Skyfråstand", "options.renderDistance": "Synsvidd", "options.resourcepack": "Tilfangspakkar...", "options.rotateWithMinecart": "Roter med gruvevogner", "options.rotateWithMinecart.tooltip": "Om spillerens synsfelt skal vende seg med en svingende gruvevogn. Kun tilgjengelig i verdener med den eksperimentelle innstillingen «Forbedringer av gruvevogner» slått på.", "options.screenEffectScale": "Rengjingsverknader", "options.screenEffectScale.tooltip": "Styrken av skjermrengjing frå ørske og Nether-portal.\nPå lægre steg er svimringsverknaden bytt ut med grønt overlegg.", "options.sensitivity": "Musevarleik", "options.sensitivity.max": "OVSNØGGLEIK!!!", "options.sensitivity.min": "*geisp*", "options.showNowPlayingToast": "<PERSON><PERSON>", "options.showNowPlayingToast.tooltip": "Viser et kort varsel når en sang begynner å spille. Det samme varselet vises konstant i pausemenyen mens en sang spiller.", "options.showSubtitles": "<PERSON><PERSON>", "options.simulationDistance": "Simuleringsfråstand", "options.skinCustomisation": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "Musikk og ljod...", "options.sounds.title": "Musikk- og ljodval", "options.telemetry": "Telemetridata...", "options.telemetry.button": "Datainn<PERSON><PERSON>ling", "options.telemetry.button.tooltip": "«%s» inneheld berre naudsynt data.\n«%s» inneheld både valfrie og naudsynte data.", "options.telemetry.disabled": "Telemetri er slått av.", "options.telemetry.state.all": "Alt", "options.telemetry.state.minimal": "<PERSON><PERSON> mogeleg", "options.telemetry.state.none": "Inkje", "options.title": "Innstillingar", "options.touchscreen": "Peikeskjerm-modus", "options.video": "Grafikkinnstillingar...", "options.videoTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.viewBobbing": "<PERSON><PERSON> gongelag", "options.visible": "Vist", "options.vsync": "VSync", "outOfMemory.message": "Minecraft er tom for minne.\n\n<PERSON>rsa<PERSON> kan vera ei lus i spelet, el<PERSON> av at Java Virtual Machine ikkje er løyvt nok minne.\n\nFor å hindra at spelfiler vert skadde vart spelet late att. Me har freista å frigjera nok minne til at du skal kunna gå attende til hovudmenyen og halda fram med spelinga, men det er uvisst om det verkar.\nOpna spelet å nyo om du ser denne meldinga att.", "outOfMemory.title": "Tom for minne!", "pack.available.title": "Tilgjengeleg", "pack.copyFailure": "Kunne ikkje kopiera pakkar", "pack.dropConfirm": "Vil du leggja til fylgjande pakkar i Minecraft?", "pack.dropInfo": "Drag og slepp filer i dette vindauga for å leggja til pakkar", "pack.dropRejected.message": "Fylgjande oppføringar var ugilde pakkar og vart ikkje kopierte:\n %s", "pack.dropRejected.title": "Oppføringar som ikkje er pakkar", "pack.folderInfo": "(<PERSON><PERSON> pak<PERSON><PERSON><PERSON> her)", "pack.incompatible": "<PERSON><PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "<PERSON><PERSON> pakka var laga for ei nyare utgåve av Minecraft og vil kan henda ikkje verka.", "pack.incompatible.confirm.old": "<PERSON><PERSON> pakka var laga for ei eldre utgåve av Minecraft og vil kan henda ikkje verka.", "pack.incompatible.confirm.title": "<PERSON>r du trygg på at du vil lasta denne pakka?", "pack.incompatible.new": "(<PERSON>ga for ei nyare utgåve av Minecraft)", "pack.incompatible.old": "(<PERSON>ga for ei eldre utgåve av Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON>na mappa for pakker", "pack.selected.title": "Valde", "pack.source.builtin": "innbygt", "pack.source.feature": "funksjon", "pack.source.local": "lokal", "pack.source.server": "tenar", "pack.source.world": "verd", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanar", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON> bomba med suksess", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON>le i brann", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON> om på", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Stridsfolk", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Leitar", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON>d", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Audmjuk", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Dis", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Fyrstik<PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Dam", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Bassenget", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Havstranda", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Døyeleg kveil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skalle og roser", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON>a er sett", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Solsikker", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Pakka ut", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Tomrome<PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Ferdar", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Øydemark", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Vatn", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Slumpvald variant", "parsing.bool.expected": "Boolsk verde var venta", "parsing.bool.invalid": "Ugildt boolsk verde. «True» eller «false» var venta, men «%s» var funnen", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> var venta", "parsing.double.invalid": "Ugildt dobbeltal «%s»", "parsing.expected": "«%s» var venta", "parsing.float.expected": "Flyttal var venta", "parsing.float.invalid": "Ugildt flyttal «%s»", "parsing.int.expected": "<PERSON><PERSON><PERSON> var venta", "parsing.int.invalid": "Ugildt heiltal «%s»", "parsing.long.expected": "Lang<PERSON>verde var venta", "parsing.long.invalid": "Ugildt lang-verde «%s»", "parsing.quote.escape": "«\\%s» er ein ugild skiftesekvens i den attgjevne strengen", "parsing.quote.expected.end": "Strengen har inkje attlatande gåsauga", "parsing.quote.expected.start": "Gåsauga var venta for å byrja ein streng", "particle.invalidOptions": "Kan ikke tolke partikkelvalg: %s", "particle.notFound": "«%s» er ein ukjend partikkel", "permissions.requires.entity": "Ei eining er kravd for å køyra denne kommandoen", "permissions.requires.player": "Ein spelar er kravd for å køyra denne kommandoen", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "<PERSON><PERSON><PERSON> d<PERSON>:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "<PERSON>k<PERSON><PERSON> predikat: %s", "quickplay.error.invalid_identifier": "Kunne ikkje finna verda med den gjevne identifikatoren", "quickplay.error.realm_connect": "Kunne ikkje kopla til Realms", "quickplay.error.realm_permission": "Manglar løyve til å kopla til denne Realmen", "quickplay.error.title": "Kunne ikkje snarspela", "realms.configuration.region.australia_east": "Ny-Sud-Wales, Australia", "realms.configuration.region.australia_southeast": "Viktoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "Nord-Karolina, USA", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Aust-Japan", "realms.configuration.region.japan_west": "Vest-Japan", "realms.configuration.region.korea_central": "Sud-Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sverige", "realms.configuration.region.uae_north": "<PERSON>i sameinte arabiske emirata (UAE)", "realms.configuration.region.uk_south": "Sud-England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Nederland", "realms.configuration.region.west_us": "Kalifornia, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatisk (eigaren av Realmen)", "realms.configuration.region_preference.automatic_player": "Automatisk (fyrste som vert med i øykt)", "realms.missing.snapshot.error.text": "Realms er for tida ikkje stødd i utviklingsutgåver", "recipe.notFound": "«%s» er ei ukjend oppskrift", "recipe.toast.description": "Sjå i oppskriftsboka di", "recipe.toast.title": "Nye oppskrifter låste opp!", "record.nowPlaying": "Spelar nett no: %s", "recover_world.bug_tracker": "<PERSON><PERSON> om feil", "recover_world.button": "Freista å få attende", "recover_world.done.failed": "<PERSON>nne ikkje atterskape frå førre tilstand.", "recover_world.done.success": "Gjenoppretting lykkast!", "recover_world.done.title": "<PERSON><PERSON> ferdig", "recover_world.issue.missing_file": "Manglande fil", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "Fylgjande problem hende medan spelet prøvde lesa verdsmappa «%s».\nDet kan vera mogeleg å skipa verda opp att frå ein eldre tilstand, eller so kan du melda ifrå om problemet i feilsporaren.", "recover_world.no_fallback": "Ingen tilstand å retta opp frå er tilgjengeleg", "recover_world.restore": "Freista å retta opp att", "recover_world.restoring": "Freistar å retta opp att verda...", "recover_world.state_entry": "Tilstand frå %s: ", "recover_world.state_entry.unknown": "ukjent", "recover_world.title": "<PERSON><PERSON> ikkje lasta verd", "recover_world.warning": "<PERSON><PERSON> ikkje lasta ve<PERSON>", "resourcePack.broken_assets": "FANN BROTNE TILFANG", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON> k<PERSON>", "resourcePack.load_fail": "<PERSON><PERSON> ikkje lasta ressurs å nyo", "resourcePack.programmer_art.name": "Programmeringskunst", "resourcePack.runtime_failure": "Ressurspakkefeil oppdaget", "resourcePack.server.name": "Verdsstyrte ressursar", "resourcePack.title": "<PERSON><PERSON>", "resourcePack.vanilla.description": "Den førevalde utsjånaden og kjensla av Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcepack.downloading": "<PERSON>ar ned <PERSON>", "resourcepack.progress": "Lastar ned fil (%s MB)...", "resourcepack.requesting": "Sender førespurnad...", "screenshot.failure": "Kunne ikkje lagra skjermbilete: %s", "screenshot.success": "Lagra skjermbilete som %s", "selectServer.add": "Legg til tenar", "selectServer.defaultName": "Minecraft-tenar", "selectServer.delete": "Sletta", "selectServer.deleteButton": "Sletta", "selectServer.deleteQuestion": "Er du trygg på at du vil taka bort denne tenaren?", "selectServer.deleteWarning": "«%s» vil vera tapt i all æve! (<PERSON><PERSON><PERSON> lenge!)", "selectServer.direct": "Beinve<PERSON> tilko<PERSON>", "selectServer.edit": "<PERSON><PERSON> om", "selectServer.hiddenAddress": "(G<PERSON><PERSON>d)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "<PERSON><PERSON> på tenar", "selectWorld.access_failure": "Fekk ikkje tilgjenge til verd", "selectWorld.allowCommands": "Tillat juksekodar", "selectWorld.allowCommands.info": "Kommandoar som /gamemode, /xp", "selectWorld.allowCommands.new": "Tillat fusk", "selectWorld.backupEraseCache": "Slett data i snøggminnet", "selectWorld.backupJoinConfirmButton": "Tak tryggingsavrit og last inn", "selectWorld.backupJoinSkipButton": "Eg veit kva eg gjer!", "selectWorld.backupQuestion.customized": "Tilmåta verder er ikkje lenger stødde", "selectWorld.backupQuestion.downgrade": "Å nedgradera verda er ikkje støtt", "selectWorld.backupQuestion.experimental": "<PERSON>r som brukar innstillingar til utrøyning er ikkje stødde", "selectWorld.backupQuestion.snapshot": "Vil du verkeleg lasta denne verda?", "selectWorld.backupWarning.customized": "Me stør diverre ikkje tilmåta verder i denne utgåva av Minecraft. Me kan enno lasta denne verda, og halda på alt som det var, men alle nye tilemna landskap vil ikkje lenger vera tilmåta. Me orsakar bryet!", "selectWorld.backupWarning.downgrade": "Denne verda var sist spela i utgåva %s. Du brukar utgåva %s. Å nedgradera ei verd kan føra til øydelegging. Det er uvisst om ho vil lasta inn eller verka som ho skal. Om du, trass i dette, ynskjer å halda fram gjer du klokt i å laga eit avrit fyrst!", "selectWorld.backupWarning.experimental": "Denne verda brukar innstillingar til utrøyning som kan stansa å verka når som helst. Der er ikkje visst om ho vil lasta eller verka. Ver varsam!", "selectWorld.backupWarning.snapshot": "Denne verda var sist spela i utgåva %s og du brukar utgåva %s. Tak eit tryggingsavrit i tilfelle verda vert øydelagd!", "selectWorld.bonusItems": "Bonuskiste", "selectWorld.cheats": "Juksekodar", "selectWorld.commands": "Kommandoar", "selectWorld.conversion": "<PERSON>å verta omskapa!", "selectWorld.conversion.tooltip": "Du må opna denne verda i ei eldre utgåve (som 1.6.4) for å kunna konvertera henne trygt", "selectWorld.create": "Laga ny verd", "selectWorld.customizeType": "Tilpass", "selectWorld.dataPacks": "Datapakkar", "selectWorld.data_read": "Les verdsdata...", "selectWorld.delete": "<PERSON><PERSON>", "selectWorld.deleteButton": "Sletta", "selectWorld.deleteQuestion": "<PERSON>r du trygg på at du vil sletta denne verda?", "selectWorld.deleteWarning": "«%s» vil vera tapt i all æve! (<PERSON><PERSON><PERSON> lenge!)", "selectWorld.delete_failure": "Kunne ikkje sletta verd", "selectWorld.edit": "<PERSON><PERSON> om", "selectWorld.edit.backup": "Tak tryggingsa<PERSON>rit", "selectWorld.edit.backupCreated": "Tryggingsavrita: %s", "selectWorld.edit.backupFailed": "Tryggingsavrit var mislukka", "selectWorld.edit.backupFolder": "Opna mappa med tryggingsavrit", "selectWorld.edit.backupSize": "storleik: %s MB", "selectWorld.edit.export_worldgen_settings": "Tak ut innstillingar for verdskaping", "selectWorld.edit.export_worldgen_settings.failure": "Eksporten var mislukka", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON> ut", "selectWorld.edit.openFolder": "<PERSON><PERSON>", "selectWorld.edit.optimize": "Optimalis<PERSON><PERSON> verd", "selectWorld.edit.resetIcon": "Set attende bilete", "selectWorld.edit.save": "Lagra", "selectWorld.edit.title": "<PERSON><PERSON> om verd", "selectWorld.enterName": "<PERSON><PERSON> på verda", "selectWorld.enterSeed": "Seed til verdsskaparen", "selectWorld.experimental": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Påkravde utrøynings-funksjonar: %s", "selectWorld.experimental.details.title": "Krav for utrøynings-funksjonar", "selectWorld.experimental.message": "Ver varsam!\nDenne konfigurasjonen krev funksjonar som enno vert utvikla. Verda di kjem kan henda til å kræsja, verta ø<PERSON>, eller stansa å verka med seinare utgåver.", "selectWorld.experimental.title": "Åtvaring om utrøynings-funksjonar", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Utrøyningar (eksperiment) er mogelege nye funksjonar. <PERSON><PERSON> varsam, for ting kan gå i sund. Du kan ikkje slå av utrøyningar etter at du har laga verda.", "selectWorld.futureworld.error.text": "Noko gjekk gale medan du freista å lasta ei verd frå ei framtidig utgåve. Dette var ei risikabel åtgjerd til å byrja med. Orsak at det ikkje verka.", "selectWorld.futureworld.error.title": "Noko gjekk gale!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Same som overlevingsmodus, men ein kan ikkje leggja ned eller taka bort blokker.", "selectWorld.gameMode.adventure.line1": "Same som overlevingsmodus, men blokker kan ikkje", "selectWorld.gameMode.adventure.line2": "<PERSON>g<PERSON>t til eller takast bort", "selectWorld.gameMode.creative": "Skapande", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bygg og utforska utan grenser. <PERSON> kan fly, ha uendeleg med bygg<PERSON><PERSON><PERSON>, og monster kan ikkje skada deg.", "selectWorld.gameMode.creative.line1": "Uavgrensa ressursar, fri flyging og", "selectWorld.gameMode.creative.line2": "øydelegg blokker på augneblinken", "selectWorld.gameMode.hardcore": "<PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Overlevingsmodus låst til vanskegraden «vandt». Du kan ikkje stå opp att om du døyr.", "selectWorld.gameMode.hardcore.line1": "Same som overlevingsmodus, lå<PERSON> p<PERSON> van<PERSON>", "selectWorld.gameMode.hardcore.line2": "<PERSON><PERSON><PERSON>, og når du døyr er spelet over", "selectWorld.gameMode.spectator": "Tilskodar", "selectWorld.gameMode.spectator.info": "<PERSON> kan sjå, men ikk<PERSON> røra.", "selectWorld.gameMode.spectator.line1": "<PERSON> kan sjå, men ikk<PERSON> røra", "selectWorld.gameMode.survival": "Overleving", "selectWorld.gameMode.survival.info": "Granska ei løyndomsfull verd der du byggjer, sam<PERSON>, emnar, og slåst mot monster.", "selectWorld.gameMode.survival.line1": "<PERSON>it etter til<PERSON>g, emna, auk i", "selectWorld.gameMode.survival.line2": "v<PERSON><PERSON>, auk i steg, helse og svolt", "selectWorld.gameRules": "Spelereglar", "selectWorld.import_worldgen_settings": "Importer innstillingar", "selectWorld.import_worldgen_settings.failure": "Feil ved importering av innstillingar", "selectWorld.import_worldgen_settings.select_file": "Vel innstillingsfil (.json)", "selectWorld.incompatible.description": "Kan ikkje opna denne verda i denne utgåva.\nHo var sist spela i utgåve %s.", "selectWorld.incompatible.info": "Uhøvande utgåve: %s", "selectWorld.incompatible.title": "<PERSON><PERSON><PERSON><PERSON> utgå<PERSON>", "selectWorld.incompatible.tooltip": "Kan ikkje opna denne verda fordi ho var laga av ei uhøvande utgåve.", "selectWorld.incompatible_series": "Laga av ei uhøvande utgåve", "selectWorld.load_folder_access": "Kunne ikkje lesa eller opna mappa der spelverdene er lagra!", "selectWorld.loading_list": "<PERSON><PERSON> ve<PERSON>", "selectWorld.locked": "Låst av ein annan køyrande førekomst av Minecraft", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, skips<PERSON><PERSON> o. s. fr.", "selectWorld.mapType": "Verdsslag", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "Fleire verdsval...", "selectWorld.newWorld": "<PERSON>y verd", "selectWorld.recreate": "Gjenskap", "selectWorld.recreate.customized.text": "Tilmåta verder er ikkje lenger stødde i denne utgåva av Minecraft. Me kan freista å attlaga henne med den same seeden og eigenskapar, men alle landskapstilmåtingar vil vera tapte. Me orsakar denne vanden!", "selectWorld.recreate.customized.title": "Tilmåta verder er ikkje lenger stødde", "selectWorld.recreate.error.text": "Noko gjekk gale medan du freista å attskapa ei verd.", "selectWorld.recreate.error.title": "Noko gjekk gale!", "selectWorld.resource_load": "<PERSON><PERSON><PERSON><PERSON>...", "selectWorld.resultFolder": "Vert lagra i:", "selectWorld.search": "søk etter verder", "selectWorld.seedInfo": "<PERSON>t st<PERSON> blankt om du vil ha ein tilfeldig seed", "selectWorld.select": "Spel vald verd", "selectWorld.targetFolder": "Lagringsmappe: %s", "selectWorld.title": "Vel verd", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON>a vart lagra i ei nyare utgåve", "selectWorld.tooltip.fromNewerVersion2": "ved å opna denne verda kan det oppstå problem!", "selectWorld.tooltip.snapshot1": "Ikkje gløym å ta tryggingsavrit av denne verda", "selectWorld.tooltip.snapshot2": "før du lastar inn i denne utviklingsutgåva.", "selectWorld.unable_to_load": "<PERSON>nne ikkje lasta inn verder", "selectWorld.version": "Utgåve:", "selectWorld.versionJoinButton": "Last same kva", "selectWorld.versionQuestion": "Vil du verkeleg lasta denne verda?", "selectWorld.versionUnknown": "ukjend", "selectWorld.versionWarning": "Denne verda vart sist spela i utgåva %s og å lasta henne inn i denne utgåva kan valda øydelegging!", "selectWorld.warning.deprecated.question": "Nokre funskjonar er gamle og kjem ikkje til å verka i framtida. Vil du halda fram?", "selectWorld.warning.deprecated.title": "Åtvaring! Desse innstillingane brukar gamle funksjonar", "selectWorld.warning.experimental.question": "Desse innstillingane er til utrøyning og kan stansa å verka ein dag. Vil du halda fram?", "selectWorld.warning.experimental.title": "Åtvaring! Desse innstillingane brukar utrøynings-funksjonar", "selectWorld.warning.lowDiskSpace.description": "Det er ikkje mykje plass att på eininga di. Å gå tom for diskplass medan du spelar kan føra til at verda di vert skada.", "selectWorld.warning.lowDiskSpace.title": "Åtvaring! Lite diskplass!", "selectWorld.world": "Verd", "sign.edit": "Gjer om skiltmelding", "sleep.not_possible": "Inga kvild kan få deg gjennom natta", "sleep.players_sleeping": "%s/%s spelarar søv", "sleep.skipping_night": "<PERSON><PERSON><PERSON> gjennom denne natta", "slot.only_single_allowed": "Kun enkelte ruter tillatt, fikk '%s'", "slot.unknown": "«%s» er ein ukjend b<PERSON>s", "snbt.parser.empty_key": "<PERSON><PERSON>k<PERSON> kan ikke være tom", "snbt.parser.expected_binary_numeral": "Forventet et binærtall", "snbt.parser.expected_decimal_numeral": "Forventet et desimaltall", "snbt.parser.expected_float_type": "Forventet et flyttall", "snbt.parser.expected_hex_escape": "Forventet et tegnliteral med lengde %s", "snbt.parser.expected_hex_numeral": "Forventet et heksadesimaltall", "snbt.parser.expected_integer_type": "Forventet et heltall", "snbt.parser.expected_non_negative_number": "Forventet et ikke-negativt tall", "snbt.parser.expected_number_or_boolean": "Forventet et tall eller en boolsk verdi", "snbt.parser.expected_string_uuid": "Forventet en streng som representerer en gyldig UUID", "snbt.parser.expected_unquoted_string": "Forventet en gyldig usitert streng", "snbt.parser.infinity_not_allowed": "<PERSON>k<PERSON>-end<PERSON>ge tall er ikke tillatt", "snbt.parser.invalid_array_element_type": "Ugyldig tabellelementtype", "snbt.parser.invalid_character_name": "Ugyldig Unicodetegn-navn", "snbt.parser.invalid_codepoint": "Ugyldig Unicodetegn-verdi: %s", "snbt.parser.invalid_string_contents": "Ugyldig strenginnhold", "snbt.parser.invalid_unquoted_start": "<PERSON><PERSON><PERSON> strenger kan ikke starte med sifrene 0-9, + el<PERSON> -", "snbt.parser.leading_zero_not_allowed": "Desimaltall kan ikke starte med 0", "snbt.parser.no_such_operation": "Ingen slik operasjon: %s", "snbt.parser.number_parse_failure": "Lyktes ikke med å tolke tall: %s", "snbt.parser.undescore_not_allowed": "Understrekstegn er ikke tillatt på starten eller slutten av et tall", "soundCategory.ambient": "Omgjevnad", "soundCategory.block": "B<PERSON>kker", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "soundCategory.master": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.music": "Musikk", "soundCategory.neutral": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "soundCategory.player": "S<PERSON>ara<PERSON>", "soundCategory.record": "Platespelar/musikkblokker", "soundCategory.ui": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.voice": "Røyst/tale", "soundCategory.weather": "<PERSON><PERSON><PERSON>", "spectatorMenu.close": "Lat att meny", "spectatorMenu.next_page": "Neste side", "spectatorMenu.previous_page": "Førre side", "spectatorMenu.root.prompt": "Trykk ein tast for å velja ein kommando, og ein gong til for å bruka honom.", "spectatorMenu.team_teleport": "Teleporter til lagslem", "spectatorMenu.team_teleport.prompt": "Vel eit lag å teleportera til", "spectatorMenu.teleport": "Teleporter til spelar", "spectatorMenu.teleport.prompt": "Vel ein spelar å teleportera til", "stat.generalButton": "Generelt", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Dyr avla", "stat.minecraft.aviate_one_cm": "Veglengd floge med venger", "stat.minecraft.bell_ring": "<PERSON><PERSON> bjøller", "stat.minecraft.boat_one_cm": "Veglengd i båt", "stat.minecraft.clean_armor": "Rustningar vaska", "stat.minecraft.clean_banner": "<PERSON><PERSON> v<PERSON>a", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vart lauga", "stat.minecraft.climb_one_cm": "Veglengd kliven", "stat.minecraft.crouch_one_cm": "Veglengd krope", "stat.minecraft.damage_absorbed": "Skade teken opp", "stat.minecraft.damage_blocked_by_shield": "S<PERSON><PERSON> hindra med skjold", "stat.minecraft.damage_dealt": "Ska<PERSON> p<PERSON>", "stat.minecraft.damage_dealt_absorbed": "S<PERSON><PERSON> gjeven (oppteken)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> gjeven (motstått)", "stat.minecraft.damage_resisted": "Ska<PERSON> stått imot", "stat.minecraft.damage_taken": "Skade teken", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "stat.minecraft.eat_cake_slice": "Kakestykke etne", "stat.minecraft.enchant_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> galdra", "stat.minecraft.fall_one_cm": "Falle", "stat.minecraft.fill_cauldron": "Gryter fylte", "stat.minecraft.fish_caught": "Fisk fanga", "stat.minecraft.fly_one_cm": "Veglengd flogen", "stat.minecraft.happy_ghast_one_cm": "Veglengd på gladghast", "stat.minecraft.horse_one_cm": "Veglengd på hest", "stat.minecraft.inspect_dispenser": "Utskytarar glytta", "stat.minecraft.inspect_dropper": "Slepparar glytta", "stat.minecraft.inspect_hopper": "Trekter glytta", "stat.minecraft.interact_with_anvil": "Verksemd med smieste", "stat.minecraft.interact_with_beacon": "Verksemd med vardar", "stat.minecraft.interact_with_blast_furnace": "Verksemd med smelteomn", "stat.minecraft.interact_with_brewingstand": "Verksemd med bryggjereiskap", "stat.minecraft.interact_with_campfire": "Verksemd med bål", "stat.minecraft.interact_with_cartography_table": "Verksemd med kartteiknarbord", "stat.minecraft.interact_with_crafting_table": "Verksemd med emningsbord", "stat.minecraft.interact_with_furnace": "Verksemd med smelteomn", "stat.minecraft.interact_with_grindstone": "Verksemd med slipestein", "stat.minecraft.interact_with_lectern": "Verksemd med talarstol", "stat.minecraft.interact_with_loom": "Verksemd med vevstol", "stat.minecraft.interact_with_smithing_table": "Verksemd med smiebord", "stat.minecraft.interact_with_smoker": "Verksemd med røykjaromn", "stat.minecraft.interact_with_stonecutter": "Verksemd med steinskjerar", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "Spel enda", "stat.minecraft.minecart_one_cm": "Veglengd med gruvevogn", "stat.minecraft.mob_kills": "Skapningar drepne", "stat.minecraft.open_barrel": "Tunner opna", "stat.minecraft.open_chest": "<PERSON><PERSON> opna", "stat.minecraft.open_enderchest": "Enderkister opna", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON>boks<PERSON>na", "stat.minecraft.pig_one_cm": "Veglengd på gris", "stat.minecraft.play_noteblock": "Musikkblokker spelt", "stat.minecraft.play_record": "Musikkplatar spela", "stat.minecraft.play_time": "Tid spela", "stat.minecraft.player_kills": "Samspelarar drepne", "stat.minecraft.pot_flower": "Vokstrar sette i potte", "stat.minecraft.raid_trigger": "<PERSON><PERSON><PERSON> utløys<PERSON>", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON> vunne", "stat.minecraft.sleep_in_bed": "<PERSON>er sove i ei seng", "stat.minecraft.sneak_time": "Tid smoge", "stat.minecraft.sprint_one_cm": "Veglengd sprungen", "stat.minecraft.strider_one_cm": "Veglengd på stegar", "stat.minecraft.swim_one_cm": "Veglengd sumd", "stat.minecraft.talked_to_villager": "Tala med bygdefolk", "stat.minecraft.target_hit": "Skotskiver råka", "stat.minecraft.time_since_death": "<PERSON><PERSON> siste dø<PERSON>", "stat.minecraft.time_since_rest": "<PERSON><PERSON> siste k<PERSON>d", "stat.minecraft.total_world_time": "Tid med verda open", "stat.minecraft.traded_with_villager": "Byt med bygdefolk", "stat.minecraft.trigger_trapped_chest": "Lokkekister utløyste", "stat.minecraft.tune_noteblock": "Musikkblokker stilt", "stat.minecraft.use_cauldron": "Vatn henta frå gryter", "stat.minecraft.walk_on_water_one_cm": "Veglengd gjenge på vatn", "stat.minecraft.walk_one_cm": "Veglengd gådd", "stat.minecraft.walk_under_water_one_cm": "Veglengd gjenge under vatn", "stat.mobsButton": "Skapningar", "stat_type.minecraft.broken": "<PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON>", "stat_type.minecraft.dropped": "Sleppt", "stat_type.minecraft.killed": "Du drap %s %s", "stat_type.minecraft.killed.none": "Du har aldri drepe %s", "stat_type.minecraft.killed_by": "%s drap deg %s gong(er)", "stat_type.minecraft.killed_by.none": "Du er aldri vorten drepen av %s", "stat_type.minecraft.mined": "<PERSON><PERSON> vunne ut", "stat_type.minecraft.picked_up": "Plukka opp", "stat_type.minecraft.used": "<PERSON><PERSON> bruka", "stats.none": "-", "structure_block.button.detect_size": "OPPDAG", "structure_block.button.load": "LAST", "structure_block.button.save": "LAGRA", "structure_block.custom_data": "Tilmåta datatagg-namn", "structure_block.detect_size": "Oppdag bygnadstorleik og stad:", "structure_block.hover.corner": "Hyrne: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Last: %s", "structure_block.hover.save": "Lagra: %s", "structure_block.include_entities": "Ha med einingar:", "structure_block.integrity": "Bygnadsheilskap og frø", "structure_block.integrity.integrity": "Bygnadsheilskap", "structure_block.integrity.seed": "Bygnadsseed", "structure_block.invalid_structure_name": "Ugildt bygnadsnamn «%s»", "structure_block.load_not_found": "Bygnaden «%s» er ikkje tilgjengeleg", "structure_block.load_prepare": "Staden til bygnad «%s» gjord klår", "structure_block.load_success": "Bygnad lasta frå «%s»", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "Last inn", "structure_block.mode.save": "Lagra", "structure_block.mode_info.corner": "Hyrnemodus - stode- og storleiksmerke", "structure_block.mode_info.data": "Datamodus - <PERSON><PERSON><PERSON> for spellogikk", "structure_block.mode_info.load": "Lasta modus - lasta frå fil", "structure_block.mode_info.save": "Lagra modus - skriv til fil", "structure_block.position": "Relativ stad", "structure_block.position.x": "relativ stad x", "structure_block.position.y": "relativ stad y", "structure_block.position.z": "relativ stad z", "structure_block.save_failure": "Kunne ikkje lagra bygnaden «%s»", "structure_block.save_success": "Bygnad lagra som «%s»", "structure_block.show_air": "<PERSON><PERSON> blokker:", "structure_block.show_boundingbox": "Vis markeringsramme:", "structure_block.size": "Bygnadsstorleik", "structure_block.size.x": "bygnadsstorleik x", "structure_block.size.y": "bygnadsstorleik y", "structure_block.size.z": "bygnadsstorleik z", "structure_block.size_failure": "<PERSON>nne ikkje finna storleiken åt bygnaden. Legg til hyrne med samsvarande bygnadsnamn", "structure_block.size_success": "Storleik er oppdaga for «%s»", "structure_block.strict": "<PERSON><PERSON><PERSON> plassering:", "structure_block.structure_name": "Bygnadsnamn", "subtitles.ambient.cave": "<PERSON><PERSON> ljod", "subtitles.ambient.sound": "<PERSON><PERSON> ljod", "subtitles.block.amethyst_block.chime": "Ametyst kling", "subtitles.block.amethyst_block.resonate": "Ametysk resonnerer", "subtitles.block.anvil.destroy": "Smieste vart øydelagt", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> landa", "subtitles.block.anvil.use": "Smieste vart nytta", "subtitles.block.barrel.close": "Tunne vert laten att", "subtitles.block.barrel.open": "Tunne vert opna", "subtitles.block.beacon.activate": "Varde vert slegen på", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON> n<PERSON>ar", "subtitles.block.beacon.deactivate": "Varde vert slegen av", "subtitles.block.beacon.power_select": "Vardekraft vald", "subtitles.block.beehive.drip": "Honning dryp", "subtitles.block.beehive.enter": "Bier går inn i biekube", "subtitles.block.beehive.exit": "Bier går ut or biekube", "subtitles.block.beehive.shear": "Saks skrapar", "subtitles.block.beehive.work": "<PERSON><PERSON> arbeider", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON><PERSON> gjev <PERSON>", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Droplauv kvelv nedover", "subtitles.block.big_dripleaf.tilt_up": "Droplauv kvelv oppover", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.brewing_stand.brew": "B<PERSON>gg<PERSON>eiska<PERSON> boblar", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON> sprekk", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON> flyt", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON> s<PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> k<PERSON>v<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON> fyk", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "<PERSON><PERSON>", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.candle.extinguish": "Voksljos sløkk", "subtitles.block.chest.close": "Kiste vert laten att", "subtitles.block.chest.locked": "<PERSON><PERSON> er låst", "subtitles.block.chest.open": "Kiste vert opna", "subtitles.block.chorus_flower.death": "Ko<PERSON><PERSON>me visnar", "subtitles.block.chorus_flower.grow": "Korblome veks", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.empty": "Kompostbing vert tømd", "subtitles.block.composter.fill": "Kompostbing vert fylt", "subtitles.block.composter.ready": "Kompostbing lagar kompost", "subtitles.block.conduit.activate": "Flødar vert slegen på", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON>l<PERSON><PERSON>", "subtitles.block.conduit.deactivate": "Flødar vert slegen av", "subtitles.block.copper_bulb.turn_off": "Koparpære vert slega av", "subtitles.block.copper_bulb.turn_on": "Koparpære vert slega på", "subtitles.block.copper_trapdoor.close": "Lem vert laten att", "subtitles.block.copper_trapdoor.open": "Lem vert opna", "subtitles.block.crafter.craft": "<PERSON><PERSON> emnar", "subtitles.block.crafter.fail": "<PERSON><PERSON> var mislukka", "subtitles.block.creaking_heart.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brakar", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON> ljod", "subtitles.block.creaking_heart.spawn": "Knirkeh<PERSON><PERSON> v<PERSON>nar", "subtitles.block.deadbush.idle": "Turrleiksljodar", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON>dd potte vert fylt", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON>dd potte vinglar", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON> knys", "subtitles.block.dispenser.dispense": "Gjenstand vart skoten ut", "subtitles.block.dispenser.fail": "Utskytar var mislukka", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> ghast rehydre<PERSON>t", "subtitles.block.dried_ghast.place_in_water": "Uttørka ghast vert blø<PERSON>t", "subtitles.block.dried_ghast.transition": "Uttørka ghast kjenner seg betre", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON><PERSON> lyder", "subtitles.block.enchantment_table.use": "Galdrebord bruka", "subtitles.block.end_portal.spawn": "Endeportal vert opna", "subtitles.block.end_portal_frame.fill": "Enderauga fester seg", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON><PERSON> lèt seg att", "subtitles.block.eyeblossom.idle": "Aug<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.eyeblossom.open": "Augblom opnar seg", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON>d knirkar", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "Eld vart slokna", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON> surrer", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON><PERSON> kle<PERSON>", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.generic.break": "Blokk vart øydelagd", "subtitles.block.generic.fall": "<PERSON><PERSON> fell på ei blokk", "subtitles.block.generic.footsteps": "Steg", "subtitles.block.generic.hit": "Blokk vert øydelagd", "subtitles.block.generic.place": "Blokk vert sett ned", "subtitles.block.grindstone.use": "Slipestein nytta", "subtitles.block.growing_plant.crop": "Vokster vert skoren", "subtitles.block.hanging_sign.waxed_interact_fail": "Skilt flakrar", "subtitles.block.honey_block.slide": "<PERSON><PERSON> ned ei ho<PERSON>lo<PERSON>k", "subtitles.block.iron_trapdoor.close": "Lem vert laten att", "subtitles.block.iron_trapdoor.open": "Lem vert opna", "subtitles.block.lava.ambient": "<PERSON><PERSON> boblar", "subtitles.block.lava.extinguish": "Lava fresar", "subtitles.block.lever.click": "Spak klikkar", "subtitles.block.note_block.note": "Toneblokk spelar", "subtitles.block.pale_hanging_moss.idle": "<PERSON>fs ljos", "subtitles.block.piston.move": "Stempel rører seg", "subtitles.block.pointed_dripstone.drip_lava": "Lava dryp", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava dryp ned i gryte", "subtitles.block.pointed_dripstone.drip_water": "Vatn dryp", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Vatn dryp ned i gryte", "subtitles.block.pointed_dripstone.land": "Stalaktitt fyk ned", "subtitles.block.portal.ambient": "Portal svirrar", "subtitles.block.portal.travel": "Portalstøy veiknar", "subtitles.block.portal.trigger": "Portalstøy vert sterkare", "subtitles.block.pressure_plate.click": "Trykkplate k<PERSON>", "subtitles.block.pumpkin.carve": "Saks skjer", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> freser", "subtitles.block.respawn_anchor.ambient": "Oppstodeanker svirrar", "subtitles.block.respawn_anchor.charge": "Oppstodeanker er ladd", "subtitles.block.respawn_anchor.deplete": "Oppstodeanker vert tømt", "subtitles.block.respawn_anchor.set_spawn": "Oppstodeanker sette by<PERSON><PERSON>", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sand.wind": "Vindljodar", "subtitles.block.sculk.charge": "Sculk boblar", "subtitles.block.sculk.spread": "Sculk breier seg", "subtitles.block.sculk_catalyst.bloom": "Sculkeskundar blømer", "subtitles.block.sculk_sensor.clicking": "Sculkemerkar by<PERSON><PERSON> klikka", "subtitles.block.sculk_sensor.clicking_stop": "Sculkemerkar stansar å klikka", "subtitles.block.sculk_shrieker.shriek": "Sculkeskrikar skrik", "subtitles.block.shulker_box.close": "Shulkerboks vert laten att", "subtitles.block.shulker_box.open": "Shulkerboks vert opna", "subtitles.block.sign.waxed_interact_fail": "Skilt flakrar", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON><PERSON> bruka", "subtitles.block.smoker.smoke": "Røykjaromn ryk", "subtitles.block.sniffer_egg.crack": "Moldnaseegg sprekk", "subtitles.block.sniffer_egg.hatch": "Moldnaseegg kle<PERSON>kjer", "subtitles.block.sniffer_egg.plop": "Moldnase verp", "subtitles.block.sponge.absorb": "Svamp syg", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON> vert plukka", "subtitles.block.trapdoor.close": "<PERSON><PERSON> luk<PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "<PERSON><PERSON><PERSON><PERSON> gje<PERSON>and vert budd", "subtitles.block.trial_spawner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sprakar", "subtitles.block.trial_spawner.ambient_charged": "<PERSON><PERSON><PERSON>de spraking", "subtitles.block.trial_spawner.ambient_ominous": "<PERSON><PERSON><PERSON>de spraking", "subtitles.block.trial_spawner.charge_activate": "Jartegn kringset røyneframkallar", "subtitles.block.trial_spawner.close_shutter": "Røyneframkallar stengjer", "subtitles.block.trial_spawner.detect_player": "Røyneframkallar lader opp", "subtitles.block.trial_spawner.eject_item": "Røyneframkallar slepp gjenstandar", "subtitles.block.trial_spawner.ominous_activate": "Jærtegn omfavner prøvelsesfremkaller", "subtitles.block.trial_spawner.open_shutter": "Røyneframkallar opnar", "subtitles.block.trial_spawner.spawn_item": "<PERSON>lev<PERSON><PERSON> gjenstand vert sloppen", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON>levaran<PERSON> gje<PERSON>and ovrar seg", "subtitles.block.trial_spawner.spawn_mob": "Skapning vert framkalla", "subtitles.block.tripwire.attach": "S<PERSON><PERSON><PERSON><PERSON>d vert festa", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert løysa", "subtitles.block.vault.activate": "Kvelv kveikjer seg", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON> sprakar", "subtitles.block.vault.close_shutter": "Kvelv lèt seg att", "subtitles.block.vault.deactivate": "Kvelv sløkk", "subtitles.block.vault.eject_item": "Kvelv slepp gjenstand", "subtitles.block.vault.insert_item": "Kvelv vert låst opp", "subtitles.block.vault.insert_item_fail": "Kvelv vert ikkje låst opp", "subtitles.block.vault.open_shutter": "Kvelv opnar seg", "subtitles.block.vault.reject_rewarded_player": "Kvelv avviser spelar", "subtitles.block.water.ambient": "Vatn sildrar", "subtitles.block.wet_sponge.dries": "Svamp turkar", "subtitles.chiseled_bookshelf.insert": "Bok sett inn", "subtitles.chiseled_bookshelf.insert_enchanted": "Galdrebok sett inn", "subtitles.chiseled_bookshelf.take": "Bok teka", "subtitles.chiseled_bookshelf.take_enchanted": "Galdrebok teka", "subtitles.enchant.thorns.hit": "Tornar sting", "subtitles.entity.allay.ambient_with_item": "H<PERSON>l<PERSON><PERSON><PERSON> leitar", "subtitles.entity.allay.ambient_without_item": "H<PERSON>l<PERSON><PERSON><PERSON> lengtar", "subtitles.entity.allay.death": "Hjelpeånd døyr", "subtitles.entity.allay.hurt": "Hjelpeånd verkjer", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> knisar", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hjelper", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kastar", "subtitles.entity.armadillo.ambient": "Beltedyr gryntar", "subtitles.entity.armadillo.brush": "Beltedyr vert kosta", "subtitles.entity.armadillo.death": "Beltedyr døyr", "subtitles.entity.armadillo.eat": "Beltedyr et", "subtitles.entity.armadillo.hurt": "Beltedyr verkjer", "subtitles.entity.armadillo.hurt_reduced": "Beltedyr vernar seg", "subtitles.entity.armadillo.land": "Beltedyr landar", "subtitles.entity.armadillo.peek": "Beltedyr glytter", "subtitles.entity.armadillo.roll": "Beltedyr rullar i hop", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON><PERSON> fell av", "subtitles.entity.armadillo.unroll_finish": "Beltedyr rullar ut", "subtitles.entity.armadillo.unroll_start": "Beltedyr glytter", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON> fall", "subtitles.entity.arrow.hit": "<PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> høvd", "subtitles.entity.arrow.shoot": "<PERSON><PERSON> skoten", "subtitles.entity.axolotl.attack": "Axolotl åtek", "subtitles.entity.axolotl.death": "Axolo<PERSON> dø<PERSON>", "subtitles.entity.axolotl.hurt": "Axolotl verkjer", "subtitles.entity.axolotl.idle_air": "Axolotl kvitrar", "subtitles.entity.axolotl.idle_water": "Axolotl kvitrar", "subtitles.entity.axolotl.splash": "Axolotl plaskar", "subtitles.entity.axolotl.swim": "Axolotl sym", "subtitles.entity.bat.ambient": "Skinnvengje skrik", "subtitles.entity.bat.death": "Skinnvengje døyr", "subtitles.entity.bat.hurt": "Skinnvengje verkjer", "subtitles.entity.bat.takeoff": "Skinnvengje flyg av garde", "subtitles.entity.bee.ambient": "<PERSON><PERSON> sur<PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.bee.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON> sur<PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> surrar argt", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> sur<PERSON> glad", "subtitles.entity.bee.sting": "Bie sting", "subtitles.entity.blaze.ambient": "Eldskrømt andar", "subtitles.entity.blaze.burn": "Eldskrømt knitrar", "subtitles.entity.blaze.death": "Eldskrømt døyr", "subtitles.entity.blaze.hurt": "Eldskrømt verkjer", "subtitles.entity.blaze.shoot": "Eldskrømt skyt", "subtitles.entity.boat.paddle_land": "Ror", "subtitles.entity.boat.paddle_water": "Ror", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON><PERSON> ranglar", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.breeze.charge": "Vindskrømt lader opp", "subtitles.entity.breeze.death": "Vindskrømt døyr", "subtitles.entity.breeze.deflect": "Vindskrømt bægjer", "subtitles.entity.breeze.hurt": "Vindskrømt verkjer", "subtitles.entity.breeze.idle_air": "Vindskrømt flyg", "subtitles.entity.breeze.idle_ground": "Vindskrømt svirrar", "subtitles.entity.breeze.inhale": "Vindskrømt andar inn", "subtitles.entity.breeze.jump": "Vindskrømt hoppar", "subtitles.entity.breeze.land": "Vindskrømt landar", "subtitles.entity.breeze.shoot": "Vindskrømt skyt", "subtitles.entity.breeze.slide": "Vindskrømt glid", "subtitles.entity.breeze.whirl": "Vindskrømt kvervlar", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON><PERSON> brestar", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "Dromedar hoppar", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON> kjem seg", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "Dromedar et", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON> vert ikledd sâl", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON> set seg", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> opp", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON> trør", "subtitles.entity.camel.step_sand": "Dromedar trør i sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> tigg", "subtitles.entity.cat.death": "<PERSON><PERSON>", "subtitles.entity.cat.eat": "<PERSON><PERSON> et", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON>", "subtitles.entity.cat.purr": "<PERSON>t mel", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> verp", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.cod.death": "Torsk døyr", "subtitles.entity.cod.flop": "Torsk spralar", "subtitles.entity.cod.hurt": "Torsk verkjer", "subtitles.entity.cow.ambient": "<PERSON> r<PERSON>ar", "subtitles.entity.cow.death": "<PERSON>", "subtitles.entity.cow.hurt": "<PERSON>", "subtitles.entity.cow.milk": "<PERSON> vert mjølka", "subtitles.entity.creaking.activate": "Knirk ser", "subtitles.entity.creaking.ambient": "Knirk knirkar", "subtitles.entity.creaking.attack": "Knirk åtek", "subtitles.entity.creaking.deactivate": "Knirk roar seg", "subtitles.entity.creaking.death": "Knirk dett i hop", "subtitles.entity.creaking.freeze": "Knirk stansar", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON> syner seg", "subtitles.entity.creaking.sway": "Knirk vert slegen", "subtitles.entity.creaking.twitch": "Knirk kipper", "subtitles.entity.creaking.unfreeze": "Knirk røyver seg", "subtitles.entity.creeper.death": "Creeper døyr", "subtitles.entity.creeper.hurt": "Creeper verkjer", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.dolphin.ambient_water": "Delfin plystrar", "subtitles.entity.dolphin.attack": "<PERSON><PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.dolphin.eat": "Delfin et", "subtitles.entity.dolphin.hurt": "Delfin verkjer", "subtitles.entity.dolphin.jump": "Delfin hoppar", "subtitles.entity.dolphin.play": "<PERSON><PERSON> leikar", "subtitles.entity.dolphin.splash": "Delfin sprutar", "subtitles.entity.dolphin.swim": "Delfin sym", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON>", "subtitles.entity.donkey.chest": "<PERSON>en vert i<PERSON>t kister", "subtitles.entity.donkey.death": "<PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON> et", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> hoppar", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> kastar l<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON> trør", "subtitles.entity.drowned.swim": "Draug sym", "subtitles.entity.egg.throw": "Egg flyg", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON> verjar styn", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON> verjar spreller", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON> verjar bannar", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON> verjar dø<PERSON>", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON> verjar spreller", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON> verjar verk<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON> d<PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON> flaksar", "subtitles.entity.ender_dragon.growl": "<PERSON> knurrar", "subtitles.entity.ender_dragon.hurt": "<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON> skyt", "subtitles.entity.ender_eye.death": "Enderauga fell", "subtitles.entity.ender_eye.launch": "Enderauga vert kasta", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> flyg", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.enderman.teleport": "Endermann teleporterar", "subtitles.entity.endermite.ambient": "Endermit spring", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON>d", "subtitles.entity.evoker.celebrate": "Åndemanar ropar av glede", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.evoker.hurt": "Åndemanar verkjer", "subtitles.entity.evoker.prepare_attack": "Åndemanar førebur <PERSON>", "subtitles.entity.evoker.prepare_summon": "<PERSON>nde<PERSON><PERSON> fø<PERSON>", "subtitles.entity.evoker.prepare_wololo": "Åndemanar førebur ynde", "subtitles.entity.evoker_fangs.attack": "Hoggtenner smell", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.blast": "Fyrverk smell", "subtitles.entity.firework_rocket.launch": "Fyrverk vert fyrt av", "subtitles.entity.firework_rocket.twinkle": "Fyrverk sprakar", "subtitles.entity.fish.swim": "Plask", "subtitles.entity.fishing_bobber.retrieve": "Dupp dregen inn", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON> p<PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> kasta", "subtitles.entity.fox.aggro": "<PERSON>", "subtitles.entity.fox.ambient": "Rev pip", "subtitles.entity.fox.bite": "Rev bit", "subtitles.entity.fox.death": "<PERSON>", "subtitles.entity.fox.eat": "Rev et", "subtitles.entity.fox.hurt": "<PERSON>", "subtitles.entity.fox.screech": "Rev kvin", "subtitles.entity.fox.sleep": "<PERSON> snorkar", "subtitles.entity.fox.sniff": "Rev snusar", "subtitles.entity.fox.spit": "<PERSON>", "subtitles.entity.fox.teleport": "<PERSON> teleporterer", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.frog.death": "Frosk døyr", "subtitles.entity.frog.eat": "Fros<PERSON> et", "subtitles.entity.frog.hurt": "Frosk verkjer", "subtitles.entity.frog.lay_spawn": "Frosk gyt", "subtitles.entity.frog.long_jump": "Frosk hoppar", "subtitles.entity.generic.big_fall": "<PERSON><PERSON> fall", "subtitles.entity.generic.burn": "Brenn", "subtitles.entity.generic.death": "D<PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Et", "subtitles.entity.generic.explode": "Sprengnad", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.generic.splash": "Plask", "subtitles.entity.generic.swim": "Sym", "subtitles.entity.generic.wind_burst": "Vindlading sprengjer", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> skrik", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "G<PERSON><PERSON> skyt", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> kurrar", "subtitles.entity.ghastling.death": "<PERSON><PERSON>leg<PERSON><PERSON> døyr", "subtitles.entity.ghastling.hurt": "Vesleghast verkjer", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>r opp", "subtitles.entity.glow_item_frame.add_item": "Gløderamme vert fylt", "subtitles.entity.glow_item_frame.break": "Gløderamme vert øydelagd", "subtitles.entity.glow_item_frame.place": "Gløderamme vert sett ned", "subtitles.entity.glow_item_frame.remove_item": "Gløderamme vert tømd", "subtitles.entity.glow_item_frame.rotate_item": "Gløderam<PERSON>", "subtitles.entity.glow_squid.ambient": "Glødesprut sym", "subtitles.entity.glow_squid.death": "Glødesprut døyr", "subtitles.entity.glow_squid.hurt": "Glødesprut verkjer", "subtitles.entity.glow_squid.squirt": "Glødesprut skyt blekk", "subtitles.entity.goat.ambient": "<PERSON><PERSON> mekrar", "subtitles.entity.goat.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.goat.eat": "Geit et", "subtitles.entity.goat.horn_break": "Bukkehorn bryt av", "subtitles.entity.goat.hurt": "Geit verkjer", "subtitles.entity.goat.long_jump": "Geit spring", "subtitles.entity.goat.milk": "Geit vert mjølka", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> trappar", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON> stangar", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON> trør", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> styn", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> flaksar", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON> skyt", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "Gladghast kurrar", "subtitles.entity.happy_ghast.death": "Gladghast døyr", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON> ta<PERSON>t på", "subtitles.entity.happy_ghast.harness_goggles_down": "Gladghast er klar", "subtitles.entity.happy_ghast.harness_goggles_up": "Gladghast stoppar", "subtitles.entity.happy_ghast.hurt": "Gladghast verkjer", "subtitles.entity.happy_ghast.unequip": "<PERSON>le takast av", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> murrar", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> murrar argt", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> vert skapa om til zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON>n dreg seg attende", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> trør", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "Hest vert ikledd rustning", "subtitles.entity.horse.breathe": "<PERSON><PERSON> andar", "subtitles.entity.horse.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON> et", "subtitles.entity.horse.gallop": "<PERSON><PERSON> galopperer", "subtitles.entity.horse.hurt": "<PERSON><PERSON> verk<PERSON>", "subtitles.entity.horse.jump": "Hest hoppar", "subtitles.entity.horse.saddle": "Hest vert ikledd sâl", "subtitles.entity.husk.ambient": "Skaldauding styn", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON><PERSON> vert skapa om til dauding", "subtitles.entity.husk.death": "Skaldauding døyr", "subtitles.entity.husk.hurt": "Skaldauding verkjer", "subtitles.entity.illusioner.ambient": "Sjonkvervar kviskrar", "subtitles.entity.illusioner.cast_spell": "Sjonkvervar kastar trolldom", "subtitles.entity.illusioner.death": "Sjonkvervar døyr", "subtitles.entity.illusioner.hurt": "Sjonkvervar verkjer", "subtitles.entity.illusioner.mirror_move": "Sjonkvervar fortrengjer", "subtitles.entity.illusioner.prepare_blindness": "Sjonkvervar førebur <PERSON>", "subtitles.entity.illusioner.prepare_mirror": "Sjonkvervar førebur spegelbilete", "subtitles.entity.iron_golem.attack": "Jarnkjempe åtek", "subtitles.entity.iron_golem.damage": "Jarnkjempe går i sund", "subtitles.entity.iron_golem.death": "Jarnkjempe døyr", "subtitles.entity.iron_golem.hurt": "Jarnkjempe verkjer", "subtitles.entity.iron_golem.repair": "Jarnkjempe vert sett i stand", "subtitles.entity.item.break": "G<PERSON>nstand vert øydelagd", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON> vert teken opp", "subtitles.entity.item_frame.add_item": "Gjenstandsramme vert fyllt", "subtitles.entity.item_frame.break": "Gjenstandsramme vert øydelagd", "subtitles.entity.item_frame.place": "Gjenstandsramme vert sett ned", "subtitles.entity.item_frame.remove_item": "Gjenstandsramme vert tømd", "subtitles.entity.item_frame.rotate_item": "Gjenstands<PERSON><PERSON> k<PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON> vert ø<PERSON>", "subtitles.entity.leash_knot.place": "Leietau vert knytt", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON> s<PERSON> ned", "subtitles.entity.lightning_bolt.thunder": "Toredøn", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON> brekar a<PERSON>t", "subtitles.entity.llama.chest": "Lama vert ikledd kister", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> et", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON> trør", "subtitles.entity.llama.swag": "<PERSON> vert ikledd ty", "subtitles.entity.magma_cube.death": "Magmakube døyr", "subtitles.entity.magma_cube.hurt": "Magmakube verkjer", "subtitles.entity.magma_cube.squish": "Magmaterning klaskar", "subtitles.entity.minecart.inside": "Gruvevogn ranglar", "subtitles.entity.minecart.inside_underwater": "Gruvevogn vaggar under vatn", "subtitles.entity.minecart.riding": "Gruvevogn trillar", "subtitles.entity.mooshroom.convert": "Mooshroom skapar seg om", "subtitles.entity.mooshroom.eat": "Mooshroom et", "subtitles.entity.mooshroom.milk": "Mooshroom vert mjølka", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom vert mjølka tvilsamt", "subtitles.entity.mule.ambient": "Muldyr skryter", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> kneggjar", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> vert ikledt kister", "subtitles.entity.mule.death": "Muldyr døyr", "subtitles.entity.mule.eat": "Muldyr et", "subtitles.entity.mule.hurt": "Muldyr verkjer", "subtitles.entity.mule.jump": "Muldyr hoppar", "subtitles.entity.painting.break": "Målarstykke vert ø<PERSON>elagt", "subtitles.entity.painting.place": "Målarstykke vert hengt opp", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ar", "subtitles.entity.panda.bite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bit", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et", "subtitles.entity.panda.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "Pandanase vert kitla", "subtitles.entity.panda.sneeze": "Pandabjørn nys", "subtitles.entity.panda.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trør", "subtitles.entity.panda.worried_ambient": "Pandabjørn <PERSON>", "subtitles.entity.parrot.ambient": "Papegøye talar", "subtitles.entity.parrot.death": "Papegøye døyr", "subtitles.entity.parrot.eats": "Papegøye et", "subtitles.entity.parrot.fly": "Papegøye flagrar", "subtitles.entity.parrot.hurts": "Papegøye verkjer", "subtitles.entity.parrot.imitate.blaze": "Papegøye pustar", "subtitles.entity.parrot.imitate.bogged": "Papeg<PERSON><PERSON> rang<PERSON>", "subtitles.entity.parrot.imitate.breeze": "Papegøye svirrar", "subtitles.entity.parrot.imitate.creaking": "Papegøye knirkar", "subtitles.entity.parrot.imitate.creeper": "Papegøye kveser", "subtitles.entity.parrot.imitate.drowned": "Papegøye surklar", "subtitles.entity.parrot.imitate.elder_guardian": "Papegøye styn", "subtitles.entity.parrot.imitate.ender_dragon": "Papegøye brølar", "subtitles.entity.parrot.imitate.endermite": "Papegøye spring", "subtitles.entity.parrot.imitate.evoker": "Papeg<PERSON><PERSON> mumlar", "subtitles.entity.parrot.imitate.ghast": "Papegøye skrik", "subtitles.entity.parrot.imitate.guardian": "Papegøye styn", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON>g<PERSON><PERSON> murrar", "subtitles.entity.parrot.imitate.husk": "Papegøye styn", "subtitles.entity.parrot.imitate.illusioner": "Papeg<PERSON><PERSON> mumlar", "subtitles.entity.parrot.imitate.magma_cube": "Papegøye klaskar", "subtitles.entity.parrot.imitate.phantom": "Papegøye skrik", "subtitles.entity.parrot.imitate.piglin": "Papegøye gryntar", "subtitles.entity.parrot.imitate.piglin_brute": "Papegøye gryntar", "subtitles.entity.parrot.imitate.pillager": "Papeg<PERSON><PERSON> mumlar", "subtitles.entity.parrot.imitate.ravager": "Papegøye gryntar", "subtitles.entity.parrot.imitate.shulker": "Papegøye lurer", "subtitles.entity.parrot.imitate.silverfish": "Papegøye kveser", "subtitles.entity.parrot.imitate.skeleton": "Papeg<PERSON><PERSON> rang<PERSON>", "subtitles.entity.parrot.imitate.slime": "Papegøye klaskar", "subtitles.entity.parrot.imitate.spider": "Papegøye kveser", "subtitles.entity.parrot.imitate.stray": "Papeg<PERSON><PERSON> rang<PERSON>", "subtitles.entity.parrot.imitate.vex": "Papegøye plagar", "subtitles.entity.parrot.imitate.vindicator": "Papeg<PERSON><PERSON> mumlar", "subtitles.entity.parrot.imitate.warden": "Papegøye kvin", "subtitles.entity.parrot.imitate.witch": "Papegøye fniser", "subtitles.entity.parrot.imitate.wither": "Papegøye argast", "subtitles.entity.parrot.imitate.wither_skeleton": "Papeg<PERSON><PERSON> rang<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON>g<PERSON><PERSON> murrar", "subtitles.entity.parrot.imitate.zombie": "Papegøye styn", "subtitles.entity.parrot.imitate.zombie_villager": "Papegøye styn", "subtitles.entity.phantom.ambient": "Fantom skrik", "subtitles.entity.phantom.bite": "Fantom bit", "subtitles.entity.phantom.death": "<PERSON><PERSON>", "subtitles.entity.phantom.flap": "<PERSON><PERSON> flaksar", "subtitles.entity.phantom.hurt": "Fantom verk<PERSON>", "subtitles.entity.phantom.swoop": "Fantom svipar", "subtitles.entity.pig.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON>ris vert ikledd sâl", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> ser på gjenstand med ovundring", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> styn", "subtitles.entity.piglin.angry": "<PERSON><PERSON> styn argt", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feirar", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin vert skapa om til piglindauding", "subtitles.entity.piglin.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON>lin styn av avund", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> dreg seg attende", "subtitles.entity.piglin.step": "<PERSON><PERSON> trør", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> styn", "subtitles.entity.piglin_brute.angry": "Piglinr<PERSON><PERSON>n s<PERSON> a<PERSON>t", "subtitles.entity.piglin_brute.converted_to_zombified": "Pig<PERSON>r<PERSON><PERSON>n vert skapa om til piglindauding", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.piglin_brute.hurt": "Piglinråskinn verk<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trør", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON>r ropar av glede", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.player.attack.crit": "Kritisk åtak", "subtitles.entity.player.attack.knockback": "Attendeslagsåtak", "subtitles.entity.player.attack.strong": "Sterkt åtak", "subtitles.entity.player.attack.sweep": "Sveipande åtak", "subtitles.entity.player.attack.weak": "Veikt åtak", "subtitles.entity.player.burp": "Rap", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> frys", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> brenn", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> plingar", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON> teleporterer", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> knuser", "subtitles.entity.potion.throw": "Flaske vert kasta", "subtitles.entity.puffer_fish.blow_out": "Kulefisk krepp", "subtitles.entity.puffer_fish.blow_up": "Kulefisk blæs seg opp", "subtitles.entity.puffer_fish.death": "Kulefisk døyr", "subtitles.entity.puffer_fish.flop": "Kulefisk spralar", "subtitles.entity.puffer_fish.hurt": "Kulefisk verkjer", "subtitles.entity.puffer_fish.sting": "Kulefisk stikk", "subtitles.entity.rabbit.ambient": "Hare pip", "subtitles.entity.rabbit.attack": "Hare å<PERSON>", "subtitles.entity.rabbit.death": "<PERSON> døyr", "subtitles.entity.rabbit.hurt": "<PERSON> verkjer", "subtitles.entity.rabbit.jump": "Hare hoppar", "subtitles.entity.ravager.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.ravager.attack": "Herjar bit", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON> ropar av glede", "subtitles.entity.ravager.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON> trør", "subtitles.entity.ravager.stunned": "<PERSON><PERSON> vert dø<PERSON>", "subtitles.entity.salmon.death": "Laks døyr", "subtitles.entity.salmon.flop": "<PERSON><PERSON> spralar", "subtitles.entity.salmon.hurt": "Laks verkjer", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> breka<PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> ve<PERSON>", "subtitles.entity.shulker.ambient": "Shulker lurer", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> lèt seg att", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opnar seg", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> skyt", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleporterar", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> knys", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON>v<PERSON><PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.silverfish.hurt": "Sylvkrek verkjer", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON><PERSON> vert skapa om til vandrar", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.shoot": "Bein<PERSON><PERSON> skyt", "subtitles.entity.skeleton_horse.ambient": "Hestebeinrangel skrik", "subtitles.entity.skeleton_horse.death": "Hestebein<PERSON>el dø<PERSON>", "subtitles.entity.skeleton_horse.hurt": "Hestebeinrangel verkjer", "subtitles.entity.skeleton_horse.jump_water": "Hestebeinrangel hoppar", "subtitles.entity.skeleton_horse.swim": "Hestebeinrangel sym", "subtitles.entity.slime.attack": "Sliming åtek", "subtitles.entity.slime.death": "Sliming døyr", "subtitles.entity.slime.hurt": "Sliming verkjer", "subtitles.entity.slime.squish": "Sliming klaskar", "subtitles.entity.sniffer.death": "Moldnase døyr", "subtitles.entity.sniffer.digging": "Moldnase grev", "subtitles.entity.sniffer.digging_stop": "<PERSON>ld<PERSON><PERSON> står opp", "subtitles.entity.sniffer.drop_seed": "Moldnase slepper frø", "subtitles.entity.sniffer.eat": "Moldnase et", "subtitles.entity.sniffer.egg_crack": "Moldnaseegg sprekk", "subtitles.entity.sniffer.egg_hatch": "Moldnaseegg kle<PERSON>kjer", "subtitles.entity.sniffer.happy": "Moldnase er glad", "subtitles.entity.sniffer.hurt": "Moldnase verkjer", "subtitles.entity.sniffer.idle": "Moldnase gryntar", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> tevar", "subtitles.entity.sniffer.searching": "Moldnase leitar", "subtitles.entity.sniffer.sniffing": "Moldnase nasar", "subtitles.entity.sniffer.step": "Moldnase trør", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.snowball.throw": "Snøball flyg", "subtitles.entity.spider.ambient": "Vevkjerring kveser", "subtitles.entity.spider.death": "Vevkjerring døyr", "subtitles.entity.spider.hurt": "Vevkjerring verkjer", "subtitles.entity.squid.ambient": "Blekksprut sym", "subtitles.entity.squid.death": "Blekksprut døyr", "subtitles.entity.squid.hurt": "Blekksprut verkjer", "subtitles.entity.squid.squirt": "Blekksprut sprutar blekk", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.eat": "Steg<PERSON> et", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON> trallar", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON> dreg seg attende", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON> spralar", "subtitles.entity.tadpole.grow_up": "Rumpetroll veks opp", "subtitles.entity.tadpole.hurt": "Rumpetroll verkjer", "subtitles.entity.tnt.primed": "TNT freser", "subtitles.entity.tropical_fish.death": "Sudhavsfisk døyr", "subtitles.entity.tropical_fish.flop": "Sudhavsfisk spralar", "subtitles.entity.tropical_fish.hurt": "Sudhavsfisk verkjer", "subtitles.entity.turtle.ambient_land": "Skjelpadde kvitrar", "subtitles.entity.turtle.death": "Skjelpadde døyr", "subtitles.entity.turtle.death_baby": "Skjelpaddeunge døyr", "subtitles.entity.turtle.egg_break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> knuser", "subtitles.entity.turtle.egg_crack": "Skjelpaddeegg sprekk", "subtitles.entity.turtle.egg_hatch": "Skjelpaddeegg klekkjer", "subtitles.entity.turtle.hurt": "Skjelpadde verkjer", "subtitles.entity.turtle.hurt_baby": "Skjelpaddeunge verkjer", "subtitles.entity.turtle.lay_egg": "Skjelpadde legg egg", "subtitles.entity.turtle.shamble": "Skjelpadde kravlar", "subtitles.entity.turtle.shamble_baby": "Skjelpaddeunge kravlar", "subtitles.entity.turtle.swim": "Skjelpadde sym", "subtitles.entity.vex.ambient": "P<PERSON>ånd plagar", "subtitles.entity.vex.charge": "Plageånd skrik", "subtitles.entity.vex.death": "Plageånd døyr", "subtitles.entity.vex.hurt": "Plageånd verkjer", "subtitles.entity.villager.ambient": "Byg<PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Bygdebu ropar av glede", "subtitles.entity.villager.death": "Bygdebu døyr", "subtitles.entity.villager.hurt": "Bygdebu verkjer", "subtitles.entity.villager.no": "Bygdebu er usamd", "subtitles.entity.villager.trade": "Bygdebu byter", "subtitles.entity.villager.work_armorer": "Rustningssmed verkar", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_cartographer": "Ka<PERSON><PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_cleric": "Prest verkar", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> verkar", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.work_librarian": "Bibliotekar verkar", "subtitles.entity.villager.work_mason": "<PERSON><PERSON> verkar", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.villager.work_toolsmith": "Ambo<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON> verkar", "subtitles.entity.villager.yes": "Bygdebu er samd", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.celebrate": "Forsvarar ropar av glede", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> mumlar", "subtitles.entity.wandering_trader.death": "Farande kjøpmann døyr", "subtitles.entity.wandering_trader.disappeared": "Farande kjøpmann kverv", "subtitles.entity.wandering_trader.drink_milk": "Farande kjøpmann drikk mjølk", "subtitles.entity.wandering_trader.drink_potion": "Farande kjøpmann drikk trolldrykk", "subtitles.entity.wandering_trader.hurt": "Farande kjøpmann verkjer", "subtitles.entity.wandering_trader.no": "Farande kjøpmann er usamd", "subtitles.entity.wandering_trader.reappeared": "Farande kjøpm<PERSON> ovrar seg", "subtitles.entity.wandering_trader.trade": "Farande kjøpmann byter", "subtitles.entity.wandering_trader.yes": "Farande kjøpmann er samd", "subtitles.entity.warden.agitated": "Vord styn sint", "subtitles.entity.warden.ambient": "Vord kvin", "subtitles.entity.warden.angry": "Vord er sint", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON> s<PERSON>år", "subtitles.entity.warden.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.warden.dig": "Vord grev", "subtitles.entity.warden.emerge": "Vord grev seg opp", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> vord slår", "subtitles.entity.warden.hurt": "Vord verkjer", "subtitles.entity.warden.listening": "Vord merkar", "subtitles.entity.warden.listening_angry": "Vord merkar sint", "subtitles.entity.warden.nearby_close": "<PERSON>ord kjem", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON> kjem nærare", "subtitles.entity.warden.nearby_closest": "<PERSON>ord er nær", "subtitles.entity.warden.roar": "<PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON> s<PERSON>ar", "subtitles.entity.warden.sonic_boom": "Vord fyrer av", "subtitles.entity.warden.sonic_charge": "Vord lader opp", "subtitles.entity.warden.step": "<PERSON><PERSON> trør", "subtitles.entity.warden.tendril_clicks": "Veidehorna åt vord klikkar", "subtitles.entity.wind_charge.throw": "Vindlading fyk", "subtitles.entity.wind_charge.wind_burst": "Vindlading brest", "subtitles.entity.witch.ambient": "Trollkjerring fniser", "subtitles.entity.witch.celebrate": "Trollkjerring ropar av glede", "subtitles.entity.witch.death": "Trollkjerring døyr", "subtitles.entity.witch.drink": "Trollkjerring drikk", "subtitles.entity.witch.hurt": "Trollkjerring verkjer", "subtitles.entity.witch.throw": "Trollkjerring kastar", "subtitles.entity.wither.ambient": "<PERSON><PERSON> a<PERSON>st", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON>", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON>bein<PERSON><PERSON> dø<PERSON>", "subtitles.entity.wither_skeleton.hurt": "Witherbeinrangel verkjer", "subtitles.entity.wolf.ambient": "Varg pesar", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> gø<PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON> knurrar", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> verk<PERSON>", "subtitles.entity.wolf.pant": "Varg pesar", "subtitles.entity.wolf.shake": "Varg ristar", "subtitles.entity.wolf.whine": "Varg kvin", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> murrar", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> murrar argt", "subtitles.entity.zoglin.attack": "Zoglin åtek", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> verkjer", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> trør", "subtitles.entity.zombie.ambient": "Dauding styn", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> vert ø<PERSON>d", "subtitles.entity.zombie.converted_to_drowned": "Dauding vert skapa om til draug", "subtitles.entity.zombie.death": "Dauding dø<PERSON>", "subtitles.entity.zombie.destroy_egg": "Skjelpaddeegg vert trakka på", "subtitles.entity.zombie.hurt": "Dauding verkjer", "subtitles.entity.zombie.infect": "Dauding forgiftar", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON>aud<PERSON> skrik", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON>aud<PERSON> verkjer", "subtitles.entity.zombie_villager.ambient": "Bygded<PERSON>ing styn", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON><PERSON><PERSON> klagar", "subtitles.entity.zombie_villager.cure": "<PERSON>g<PERSON><PERSON><PERSON> snøftar", "subtitles.entity.zombie_villager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.zombie_villager.hurt": "<PERSON>g<PERSON><PERSON><PERSON> verkjer", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON><PERSON> gryntar", "subtitles.entity.zombified_piglin.angry": "<PERSON>lindauding gryntar argt", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON><PERSON><PERSON> verkjer", "subtitles.event.mob_effect.bad_omen": "Jartegn slår rot", "subtitles.event.mob_effect.raid_omen": "Herjing ruvar i nærleiken", "subtitles.event.mob_effect.trial_omen": "Illevarande røyning ruvar i nærleiken", "subtitles.event.raid.horn": "Illevarande horn ljomar", "subtitles.item.armor.equip": "Utstyr vert teke på", "subtitles.item.armor.equip_chain": "<PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "Diamantrustning kling", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_gold": "Gullrustning kling", "subtitles.item.armor.equip_iron": "Jar<PERSON><PERSON><PERSON> kling", "subtitles.item.armor.equip_leather": "L<PERSON><PERSON><PERSON><PERSON> vert kledd på", "subtitles.item.armor.equip_netherite": "Rustning av netheritt klinkar", "subtitles.item.armor.equip_turtle": "Skjelpaddeskal støyter", "subtitles.item.armor.equip_wolf": "Vargrustning vert teken på", "subtitles.item.armor.unequip_wolf": "Vargrustning glid av", "subtitles.item.axe.scrape": "Øks skrapar", "subtitles.item.axe.strip": "<PERSON><PERSON> s<PERSON>", "subtitles.item.axe.wax_off": "Voks av", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.book.page_turn": "Side kraslar", "subtitles.item.book.put": "Bok støyter", "subtitles.item.bottle.empty": "<PERSON><PERSON> vert tømd", "subtitles.item.bottle.fill": "Flaske vert fylt", "subtitles.item.brush.brushing.generic": "Kostar", "subtitles.item.brush.brushing.gravel": "Kostar grus", "subtitles.item.brush.brushing.gravel.complete": "Kosting av grus fullført", "subtitles.item.brush.brushing.sand": "Kostar sand", "subtitles.item.brush.brushing.sand.complete": "Kosting av sand fullført", "subtitles.item.bucket.empty": "<PERSON><PERSON> vert tømd", "subtitles.item.bucket.fill": "<PERSON><PERSON> vert fylt", "subtitles.item.bucket.fill_axolotl": "Axolotl vert teken opp", "subtitles.item.bucket.fill_fish": "Fisk fanga", "subtitles.item.bucket.fill_tadpole": "Rumpetroll fanga", "subtitles.item.bundle.drop_contents": "Sekk vert tømd", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON><PERSON> vert pakka ned", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON>ele<PERSON> er full", "subtitles.item.bundle.remove_one": "Gjenstand vert pakka ut", "subtitles.item.chorus_fruit.teleport": "Spelar teleporterar", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.crossbow.charge": "Låsboge vert spend", "subtitles.item.crossbow.hit": "<PERSON><PERSON>", "subtitles.item.crossbow.load": "Låsboge vert ladd", "subtitles.item.crossbow.shoot": "Låsboge skyt", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "Susing", "subtitles.item.firecharge.use": "Eldladning blæs", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Glødeblekk<PERSON> klattar", "subtitles.item.goat_horn.play": "Bukkehorn spelar", "subtitles.item.hoe.till": "Grev snur molda", "subtitles.item.honey_bottle.drink": "Belgjar", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>s på", "subtitles.item.horse_armor.unequip": "Hesterustning klippes av", "subtitles.item.ink_sac.use": "Blekk<PERSON> klattar", "subtitles.item.lead.break": "<PERSON> ryker", "subtitles.item.lead.tied": "<PERSON> knyttes", "subtitles.item.lead.untied": "<PERSON> løses opp", "subtitles.item.llama_carpet.unequip": "Teppe klippes av", "subtitles.item.lodestone_compass.lock": "Kompass for leidarstein rettar seg inn på leidarstein", "subtitles.item.mace.smash_air": "Stridsklubbe vert slengd", "subtitles.item.mace.smash_ground": "Stridsklubbe slår ned", "subtitles.item.nether_wart.plant": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> knuser", "subtitles.item.saddle.unequip": "Sal klippes av", "subtitles.item.shears.shear": "Saks klikker", "subtitles.item.shears.snip": "Saks klipper", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON> vernar", "subtitles.item.shovel.flatten": "Spaden flatar ut", "subtitles.item.spyglass.stop_using": "Kikert vert stuttare", "subtitles.item.spyglass.use": "Kikert vert lengre", "subtitles.item.totem.use": "Totem vert sett i gang", "subtitles.item.trident.hit": "Ljoster stikk", "subtitles.item.trident.hit_ground": "Ljoster bivrar", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON> kjem attende", "subtitles.item.trident.riptide": "Ljoster fyk", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON> kling", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON><PERSON> brakar", "subtitles.item.wolf_armor.break": "Vargrustning går sund", "subtitles.item.wolf_armor.crack": "Vargrustning slår sprekkar", "subtitles.item.wolf_armor.damage": "Vargrustning tek skade", "subtitles.item.wolf_armor.repair": "Vargrustning er vølt", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> flyr", "subtitles.ui.cartography_table.take_result": "Kart vert teikna", "subtitles.ui.hud.bubble_pop": "<PERSON><PERSON><PERSON><PERSON> søkk", "subtitles.ui.loom.take_result": "Vevstol vert nytta", "subtitles.ui.stonecutter.take_result": "Steinskjerar vert nytta", "subtitles.weather.rain": "<PERSON>n fell", "symlink_warning.message": "Å lasta verder frå mapper med symbolske lenkjer kan vera utrygt om ein ikkje veit kva ein gjer. Vitja %s for å læra meir.", "symlink_warning.message.pack": "Å lasta inn pakker frå mapper med symbolske lenkjer kan vera utrygt om du ikkje veit kva du gjer. Gjer vel og vitja %s for å læra meir.", "symlink_warning.message.world": "Å lasta inn verder frå mapper med symbolske lenkjer kan vera utrygt om du ikkje veit kva du gjer. Gjer vel og vitja %s for å læra meir.", "symlink_warning.more_info": "Fleire opplysingar", "symlink_warning.title": "Mappa med verder inneheld <PERSON>ke lenkjer", "symlink_warning.title.pack": "Pakke/-ane som er lagd(e) til inneheld symbolske lenkjer", "symlink_warning.title.world": "Mappa med verder inneheld <PERSON>ke lenkjer", "team.collision.always": "Alltid", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "D<PERSON>t andre lag", "team.collision.pushOwnTeam": "Dytt eige lag", "team.notFound": "«%s» er eit ukjent lag", "team.visibility.always": "Alltid", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON> for andre lag", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON> for eige lag", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "<PERSON><PERSON> å skjøna samanhengen bak å vinna seg bragder kan ein lettare skjøna og betra framgangen i spelet.", "telemetry.event.advancement_made.title": "Bragd gjord", "telemetry.event.game_load_times.description": "<PERSON>ne hendinga kan hjelpa med å finna ut kvar ein skal betra oppstartsytinga ved å måla tida på utføringa under oppstart.", "telemetry.event.game_load_times.title": "Spellastingstider", "telemetry.event.optional": "%s (valfri)", "telemetry.event.optional.disabled": "%s (<PERSON><PERSON><PERSON><PERSON>) - Avslege", "telemetry.event.performance_metrics.description": "Å kjenna til den overordna ytingsprofilen åt Minecraft hjelper oss med å tilmåta og optimalisera spelet for eit breitt utval maskinspesifikasjonar og operativsystem. Spelutgåve fylgjer med for å hjelpa oss å jamføra ytingsprofilane åt nyare utgåver av Minecraft.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON>sm<PERSON><PERSON>", "telemetry.event.required": "%s (påkravd)", "telemetry.event.world_load_times.description": "Det er viktig for oss å skjøna kor lang tid det tek å verta med i ei verd, og korleis det skifter over tid. Til dømes, når me legg inn nye funksjonar eller gjer store tekniske omgjerder treng me sjå kva påverknad det har på lastingstida.", "telemetry.event.world_load_times.title": "Verdlastingstider", "telemetry.event.world_loaded.description": "Å kjenna til korleis spelarar spelar Minecraft (slik som spelmodus, klient eller modda tenar, og spelutgåve) lèt oss fokusera på speloppdateringar som legg vekt på å betra ting spelarar bryr seg mest om. World Loaded-hendinga er kopla saman med World Unloaded-hendinga for å rekna ut kor lang tid speleøkta har vart.", "telemetry.event.world_loaded.title": "<PERSON><PERSON>", "telemetry.event.world_unloaded.description": "<PERSON>ne hendinga er kopla saman med World Loaded-hendinga for å rekna ut kor lenge økta åt verda har vart. Varigna (i sekund eller tikk) er målt når økta åt verda har enda (går attende til hovudmenyen, koplar frå ein tenar).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Speltid (tikk)", "telemetry.property.advancement_id.title": "Bragd-ID", "telemetry.property.client_id.title": "Klient-ID", "telemetry.property.client_modded.title": "Modda klient", "telemetry.property.dedicated_memory_kb.title": "Dedikert minne (kB)", "telemetry.property.event_timestamp_utc.title": "Tidstempel for hending (UTC)", "telemetry.property.frame_rate_samples.title": "Biletfrekvensprøver (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Spelutgåve", "telemetry.property.launcher_name.title": "Oppstartarnamn", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap-tid (millisekund)", "telemetry.property.load_time_loading_overlay_ms.title": "Tid på last<PERSON>m (millisekund)", "telemetry.property.load_time_pre_window_ms.title": "Tid før vindauga opnar (millisekund)", "telemetry.property.load_time_total_time_ms.title": "Total lastetid (millisekund)", "telemetry.property.minecraft_session_id.title": "Minecraft-økt-ID", "telemetry.property.new_world.title": "<PERSON>y verd", "telemetry.property.number_of_samples.title": "<PERSON><PERSON> på prøver", "telemetry.property.operating_system.title": "Operativsystem", "telemetry.property.opt_in.title": "Samtykke", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realmskart-innehald (minispelnamn)", "telemetry.property.render_distance.title": "Synsvidd", "telemetry.property.render_time_samples.title": "Skildringstidprøver", "telemetry.property.seconds_since_load.title": "Tid sidan innlasting (sekund)", "telemetry.property.server_modded.title": "<PERSON><PERSON>a tenar", "telemetry.property.server_type.title": "Tenarslag", "telemetry.property.ticks_since_load.title": "Tid sidan innlasting (tikk)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON> (RAM)", "telemetry.property.user_id.title": "Brukar-ID", "telemetry.property.world_load_time_ms.title": "Innlastingstid for verd (millisekund)", "telemetry.property.world_session_id.title": "Økt-ID for verd", "telemetry_info.button.give_feedback": "<PERSON><PERSON><PERSON>terbo<PERSON>", "telemetry_info.button.privacy_statement": "Personvernsfråsegn", "telemetry_info.button.show_data": "Vis dataa mine", "telemetry_info.opt_in.description": "Eg samtykkjer til at valfrie telemetridata vert sende", "telemetry_info.property_title": "Inneheldt data", "telemetry_info.screen.description": "Å samla desse dataa hjelper oss med å betra Minecraft ved å føra oss i dei leiene som er relevante for spelarane våre.\nDu kan òg senda inn særskilt atterbod for å hjelpa oss i arbeidet.", "telemetry_info.screen.title": "Innsamling av telemetridata", "test.error.block_property_mismatch": "Forventet egenskap %s å være %s, var %s", "test.error.block_property_missing": "Blokkegenskap mangler, forventet egenskap %s å være %s", "test.error.entity_property": "Enhet %s besto ikke test: %s", "test.error.entity_property_details": "Enhet %s besto ikke test: %s, forventet: %s, var: %s", "test.error.expected_block": "Forventet blokk %s, fikk %s", "test.error.expected_block_tag": "Forventet blokk i #%s, fikk %s", "test.error.expected_container_contents": "Beholder forventes å inneholde: %s", "test.error.expected_container_contents_single": "Beholder forventes å ha en enkelt: %s", "test.error.expected_empty_container": "Beholder forventes å være tom", "test.error.expected_entity": "Forventet %s", "test.error.expected_entity_around": "Forventet %s å finnes omkring %s, %s, %s", "test.error.expected_entity_count": "Forventet %s enhet(er) av type %s, fant %s", "test.error.expected_entity_data": "Forventet enhetsdata å være: %s, var %s", "test.error.expected_entity_data_predicate": "Enhetsdata passer ikke med %s", "test.error.expected_entity_effect": "Forventet at %s skulle ha effekten %s %s", "test.error.expected_entity_having": "Enhets inventar forventes å inneholde %s", "test.error.expected_entity_holding": "Enhet forventes å holde %s", "test.error.expected_entity_in_test": "Forventet at %s fantes i testen", "test.error.expected_entity_not_touching": "Forventet ikke at %s skulle røre %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_entity_touching": "Forventet at %s skulle røre %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_item": "Forventet gjenstand av type %s", "test.error.expected_items_count": "Forventet %s gjenstander av type %s, fant %s", "test.error.fail": "Feilbetingelser møtte", "test.error.invalid_block_type": "Uforventet blokktype funnet: %s", "test.error.missing_block_entity": "<PERSON><PERSON> blokken<PERSON>t", "test.error.position": "%s på %s, %s, %s (relativt: %s, %s, %s) på tikk %s", "test.error.sequence.condition_already_triggered": "Betingelse allerede utløst på %s", "test.error.sequence.condition_not_triggered": "Betingelse ikke utløst", "test.error.sequence.invalid_tick": "Lyktes på feil tikk: forventet %s", "test.error.sequence.not_completed": "Test gikk tom for tid før se<PERSON><PERSON> fullførte", "test.error.set_biome": "Lyktes ikke med å sette markslag for testen", "test.error.spawn_failure": "Lyktes ikke med å skape enhet %s", "test.error.state_not_equal": "Feil tilstand. Forventet %s, var %s", "test.error.structure.failure": "Lyktes ikke med å plassere teststruktur for %s", "test.error.tick": "%s på tikk %s", "test.error.ticking_without_structure": "Tikking av test før plassering av struktur", "test.error.timeout.no_result": "Intet resultat innen %s tikk", "test.error.timeout.no_sequences_finished": "Ingen sekvenser fullført innen %s tikk", "test.error.too_many_entities": "Forventet at kun én %s fantes omkring %s, %s, %s, men fant %s", "test.error.unexpected_block": "Forventet ikke at blokk var %s", "test.error.unexpected_entity": "Forventet ikke at %s fantes", "test.error.unexpected_item": "Forventet ikke gjenstand av type %s", "test.error.unknown": "Ukjent indre feil: %s", "test.error.value_not_equal": "Forventet at %s skulle være %s, var %s", "test.error.wrong_block_entity": "Feil blokkenhetstype: %s", "test_block.error.missing": "Teststruktur mangler %s-blokk", "test_block.error.too_many": "For mange %s-blokker", "test_block.invalid_timeout": "Ugyldig tidsavbrudd (%s) - må være et positivt antall tikk", "test_block.message": "Melding:", "test_block.mode.accept": "Bestå", "test_block.mode.fail": "Feilslå", "test_block.mode.log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Beståmodus - <PERSON> for å bestå (delvis) en test", "test_block.mode_info.fail": "Feilmodus - Feilslå testen", "test_block.mode_info.log": "Loggmodus - Loggfør en melding", "test_block.mode_info.start": "Startmodus - Startpunktet til en test", "test_instance.action.reset": "Tilbakestill og last inn", "test_instance.action.run": "Last inn og kjør", "test_instance.action.save": "<PERSON><PERSON>r struktur", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Mislyktes: %s", "test_instance.description.function": "Funksjon: %s", "test_instance.description.invalid_id": "Ugyldig test-ID", "test_instance.description.no_test": "Ingen slik test", "test_instance.description.structure": "Struktur: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Blokkbasert test", "test_instance.type.function": "Innebygd funksjonstest", "test_instance_block.entities": "Enheter:", "test_instance_block.error.no_test": "Kan ikke kjøre testinstans på %s, %s, %s fordi den har en udefinert test", "test_instance_block.error.no_test_structure": "Kan ikke kjøre testinstans på %s, %s, %s fordi den ikke har en teststruktur", "test_instance_block.error.unable_to_save": "Kan ikke lagre teststrukturmal til testinstand på %s, %s, %s", "test_instance_block.invalid": "[u<PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Tilbakestilling lyktes for test: %s", "test_instance_block.rotation": "Rotasjon:", "test_instance_block.size": "Teststrukturstørrelse", "test_instance_block.starting": "Starter test %s", "test_instance_block.test_id": "Testinstans-ID", "title.32bit.deprecation": "Fann 32-bitssystem. <PERSON>te kan hindra deg ifrå å spela i framtida av di spelet vil krevja 64-bitssystem!", "title.32bit.deprecation.realms": "Minecraft kjem snart til å krevja 64-bitssystem, som vil hindra deg frå å spela eller bruka Realms på denne eininga. Du må stansa Realms-tingingar på eiga hand.", "title.32bit.deprecation.realms.check": "Ikkje vis denne skjermen att", "title.32bit.deprecation.realms.header": "Fann 32-bitssystem", "title.credits": "Opphavsrett Mojang AB. Ikkje distribuer!", "title.multiplayer.disabled": "Fleirspelar er avslege. Sjå til innstillingane for Microsoft-kontoen din.", "title.multiplayer.disabled.banned.name": "Du må gjera om på namnet ditt før du kan spela på nett", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON><PERSON> din er permanent utestengd frå nettspel", "title.multiplayer.disabled.banned.temporary": "Kontoen din er mellombels utestengd frå nettspel", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "Fleirspelar (tredjepartstenar)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Einspelar", "translation.test.args": "%s %s", "translation.test.complex": "Prefiks, %s%2$s igjen %s og %1$s til sist %s og so %1$s att!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hei %", "translation.test.invalid2": "hei %s", "translation.test.none": "Hallo, verd!", "translation.test.world": "verd", "trim_material.minecraft.amethyst": "Ametystemne", "trim_material.minecraft.copper": "Koparemne", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragdemne", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "Jarnemne", "trim_material.minecraft.lapis": "Lasursteinemne", "trim_material.minecraft.netherite": "Netherittemne", "trim_material.minecraft.quartz": "Kvar<PERSON>emne", "trim_material.minecraft.redstone": "Redstoneemne", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Tore-rustningspryd", "trim_pattern.minecraft.coast": "<PERSON><PERSON> rustningpryd", "trim_pattern.minecraft.dune": "Ørkenvoren rustningpryd", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON> rust<PERSON><PERSON>d", "trim_pattern.minecraft.flow": "Kvervlande rustningspryd", "trim_pattern.minecraft.host": "Hysar-rustningp<PERSON>d", "trim_pattern.minecraft.raiser": "Lyftar-rustningpryd", "trim_pattern.minecraft.rib": "Ribbeinsvoren rustningpryd", "trim_pattern.minecraft.sentry": "Vaktarvoren rustningpryd", "trim_pattern.minecraft.shaper": "Formar-rustningpryd", "trim_pattern.minecraft.silence": "Togn-rustningpryd", "trim_pattern.minecraft.snout": "Snutvoren rustningpryd", "trim_pattern.minecraft.spire": "S<PERSON>r<PERSON><PERSON> rustningpryd", "trim_pattern.minecraft.tide": "Tidvassvoren rustningpryd", "trim_pattern.minecraft.vex": "Plageåndsvoren rustningpryd", "trim_pattern.minecraft.ward": "Vordvoren rustningpryd", "trim_pattern.minecraft.wayfinder": "Vegfinnar-rustningpryd", "trim_pattern.minecraft.wild": "Vill rustningpryd", "tutorial.bundleInsert.description": "Høgreklikk for å leggja til ting", "tutorial.bundleInsert.title": "<PERSON><PERSON>a ein sekk", "tutorial.craft_planks.description": "Oppskriftsboka kan hjelpa", "tutorial.craft_planks.title": "<PERSON><PERSON> tre<PERSON>", "tutorial.find_tree.description": "Slå det for å samla ved", "tutorial.find_tree.title": "Finn eit tre", "tutorial.look.description": "Bruk datamusa di til å snu deg", "tutorial.look.title": "Sj<PERSON> deg omkring", "tutorial.move.description": "Hopp med %s", "tutorial.move.title": "<PERSON><PERSON><PERSON> deg med %s, %s, %s, og %s", "tutorial.open_inventory.description": "Trykk på %s", "tutorial.open_inventory.title": "Opna inventaret ditt", "tutorial.punch_tree.description": "Haldt nede %s", "tutorial.punch_tree.title": "Ø<PERSON><PERSON><PERSON> treet", "tutorial.socialInteractions.description": "Trykk %s for å opna", "tutorial.socialInteractions.title": "Samkvem", "upgrade.minecraft.netherite_upgrade": "Netherittbetring"}